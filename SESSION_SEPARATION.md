# Session Separation Between Apps

This document explains how the authentication sessions have been separated between the `apps/web` and `apps/portal` applications.

## Problem

Previously, both applications shared the same NextAuth.js session cookies, which meant that logging into one application would automatically log the user into the other application as well.

## Solution

The portal app's authentication configuration has been updated to use different cookie names, ensuring complete session isolation while leaving the main web app unchanged.

## Changes Made

### Portal App Only (`apps/portal/lib/auth.ts`)
- Added custom cookie configuration with `portal.` prefix
- Cookie names:
  - `portal.next-auth.session-token`
  - `portal.next-auth.callback-url`
  - `portal.next-auth.csrf-token`
- Uses `NEXTAUTH_SECRET_PORTAL` environment variable (falls back to `NEXTAUTH_SECRET`)

### Web App (`apps/web/lib/auth.ts`)
- **No changes made** - continues to use default NextAuth cookie names
- Uses existing `NEXTAUTH_SECRET` environment variable

## Environment Variables (Optional)

For additional security isolation, you can set a different NextAuth secret for the portal app:

```bash
# For the portal app (optional)
NEXTAUTH_SECRET_PORTAL="your-portal-app-secret-here"

# Existing (used by web app and portal fallback)
NEXTAUTH_SECRET="your-default-secret-here"
```

If you don't set `NEXTAUTH_SECRET_PORTAL`, the portal app will fall back to using the existing `NEXTAUTH_SECRET`.

## How It Works

1. **Different Cookie Names**: Each app now uses its own set of cookie names, preventing cookie conflicts
2. **Same Domain Support**: Both apps can run on the same domain (localhost) without interfering with each other
3. **Independent Sessions**: Users must log in separately to each application
4. **Secure Configuration**: Cookies are properly configured with security flags for production

## Testing

To verify the separation works:

1. Start both applications:
   ```bash
   pnpm dev
   ```

2. Log into the web app (localhost:3000 or rvhelp.test)
3. Navigate to the portal app (localhost:4000)
4. Verify that you are NOT automatically logged in
5. Log into the portal app separately
6. Verify both sessions work independently

## Development Ports

- **Web App**: `localhost:3000` (or `rvhelp.test`)
- **Portal App**: `localhost:4000`

The different cookie names ensure that sessions remain separate even when both apps are running on localhost.

touch