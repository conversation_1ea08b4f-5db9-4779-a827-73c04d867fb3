// Simple test script to verify analytics API endpoint
const testAnalyticsAPI = async () => {
	try {
		// Test the analytics endpoint
		const response = await fetch(
			"http://localhost:3001/api/dashboard/analytics",
			{
				method: "GET",
				headers: {
					"Content-Type": "application/json"
				}
			}
		);

		console.log("Response status:", response.status);

		if (response.ok) {
			const data = await response.json();
			console.log("Analytics data structure:", Object.keys(data));
			console.log("Performance metrics:", data.performanceMetrics);
			console.log("Pipeline stats:", data.pipeline?.stats);
		} else {
			const error = await response.text();
			console.log("Error response:", error);
		}
	} catch (error) {
		console.error("Test failed:", error.message);
	}
};

// Run the test
testAnalyticsAPI();
