export class AppError extends <PERSON>rror {
    constructor(
        message: string,
        public readonly code: string,
        public readonly statusCode: number = 500,
        public readonly context?: Record<string, any>
    ) {
        super(message);
        this.name = 'AppError';
    }
}

// Specific error types
export class PrismaError extends AppError {
    constructor(message: string, context?: Record<string, any>) {
        super(message, 'DATABASE_ERROR', 500, context);
    }
}

export class ValidationError extends AppError {
    constructor(message: string, context?: Record<string, any>) {
        super(message, 'VALIDATION_ERROR', 400, context);
    }
}

export class AuthError extends AppError {
    constructor(message: string, context?: Record<string, any>) {
        super(message, 'AUTH_ERROR', 401, context);
    }
}
