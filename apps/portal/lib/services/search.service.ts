import config from "@/config";
import prisma from "@/lib/prisma";
import { calculateDistance, getDrivingDistance } from "@/lib/utils/distance";
import { Prisma, RVHelpVerificationLevel } from "@rvhelp/database";

export const safeFields = {
	id: true,
	slug: true,
	rvtaa_member_id: true,
	first_name: true,
	last_name: true,
	business_name: true,
	short_description: true,
	rvtaa_technician_level: true,
	nrvia_inspector_level: true,
	profile_image: true,
	phone: true,
	rating: true,
	logo: true,
	num_reviews: true,
	discount_hourly_rate: true,
	discount_dispatch_fee: true,
	rv_help_verification_level: true,
	settings_virtual_diagnosis: true,
	locations: {
		select: {
			city: true,
			latitude: true,
			longitude: true,
			state: true,
			inspection_radius: true,
			radius: true,
			default: true,
			start_date: true,
			end_date: true,
			description: true,
			emergency_radius: true
		}
	},
	vacation_mode: true,
	categories: true,
	year_established: true
};

interface SearchParams {
	listingIds?: string[];
	category: string;
	subcategory?: string;
	city?: string;
	state?: string;
	lat?: string;
	lng?: string;
	sortBy?: string;
	radius?: string;
	useDrivingDistance?: boolean;
	filters: {
		certificationLevels?: number[];
		verified?: boolean;
		certification?: string;
		showDiscountProviders?: boolean;
		showTroubleshootingProviders?: boolean;
	};
}

interface PrivateLocation {
	city: string;
	state: string;
	latitude: number;
	longitude: number;
	radius?: number;
	emergency_radius?: number;
	inspection_radius?: number;
}

export class SearchService {
	// Helper function to calculate distance using appropriate method
	private static async calculateDistanceWithMethod(
		lat1: number,
		lng1: number,
		lat2: number,
		lng2: number,
		useDrivingDistance: boolean = false
	): Promise<number> {
		if (useDrivingDistance) {
			try {
				return await getDrivingDistance(
					{ lat: lat1, lng: lng1 },
					{ lat: lat2, lng: lng2 }
				);
			} catch (error) {
				console.warn("Driving distance calculation failed, falling back to Haversine:", error);
				return calculateDistance(lat1, lng1, lat2, lng2);
			}
		}
		return calculateDistance(lat1, lng1, lat2, lng2);
	}

	// Private helper method to build common where clause
	public static buildWhereClause(
		params: SearchParams
	): Prisma.ListingWhereInput {
		const { filters } = params;

		const getCertificationLevelClause = (category: string) => {
			if (category === "rv-inspection") {
				if (filters.certificationLevels) {
					return {
						nrvia_inspector_level: {
							in: filters.certificationLevels
						}
					};
				}
				return {
					nrvia_inspector_level: {
						gt: 0
					}
				};
			}
			if (category === "rv-repair") {
				if (filters.certificationLevels) {
					return {
						rvtaa_technician_level: {
							in: filters.certificationLevels
						}
					};
				}
				return {
					rvtaa_technician_level: {
						gt: 0
					}
				};
			}
		};

		// Add certification clause
		const getCertificationClause = (certification: string) => {
			if (certification && certification.trim() !== "") {
				return {
					partner_certifications: {
						has: certification
					}
				};
			}
			return {};
		};

		// Add pro filter clauses
		const getProFilterClauses = () => {
			const clauses: any[] = [];

			if (filters.showDiscountProviders) {
				clauses.push({
					OR: [{ discount_hourly_rate: true }, { discount_dispatch_fee: true }]
				});
			}

			if (filters.showTroubleshootingProviders) {
				clauses.push({
					settings_virtual_diagnosis: true
				});
			}

			return clauses;
		};

		// Build verification clause - simplified boolean logic
		const getVerificationClause = () => {
			if (filters.verified === true) {
				// Show only verified providers (NOT NONE)
				return {
					rv_help_verification_level: {
						not: RVHelpVerificationLevel.NONE
					}
				};
			} else if (filters.verified === false) {
				// Show only unverified providers (NONE only)
				return {
					rv_help_verification_level: RVHelpVerificationLevel.NONE
				};
			}
			// If undefined, show all (no filter)
			return {};
		};

		const whereClause = {
			is_active: true,
			status: "ACTIVE",
			...(params.listingIds && {
				id: { in: params.listingIds }
			}),
			...(params.category && {
				categories: {
					path: [params.category, "selected"],
					equals: true
				}
			}),
			...getVerificationClause(),
			...getCertificationLevelClause(params.category),
			...getCertificationClause(filters.certification || ""),
			...(params.city || params.state
				? {
					location: {
						...(params.city && {
							city: { contains: params.city, mode: "insensitive" }
						}),
						...(params.state && {
							state: { contains: params.state, mode: "insensitive" }
						})
					}
				}
				: {}),
			// Add pro filter clauses if any are active
			...(filters.showDiscountProviders || filters.showTroubleshootingProviders
				? {
					AND: getProFilterClauses()
				}
				: {})
		};

		return whereClause as Prisma.ListingWhereInput;
	}

	// New helper to select the active location from an array of locations
	private static getActiveLocation(locations: any[]): any {
		const now = new Date();
		let activeLocation = null;
		if (locations && locations.length > 0) {
			if (locations.length === 1) {
				activeLocation = locations[0];
			}
			else {
				const result = locations.find(loc =>
					loc.start_date && loc.end_date &&
					loc.start_date <= now && loc.end_date >= now
				);
				if (result) {
					activeLocation = result;
				}
				if (!result) {
					activeLocation = locations.find(loc => loc.default === true);
				}
				if (!result && locations.length > 0) {
					activeLocation = locations[0];
				}
			}
			return activeLocation
		}
		return null;
	}

	// Add the fuzzLocation utility (can be moved to a shared utils file)
	private static fuzzLocation(
		lat: number,
		lng: number
	): { latitude: number; longitude: number } {
		const milesOffset = 0.5 + Math.random();
		const latOffset = (milesOffset / 69) * (Math.random() > 0.5 ? 1 : -1);
		const lngOffset =
			(milesOffset / (69 * Math.cos((lat * Math.PI) / 180))) *
			(Math.random() > 0.5 ? 1 : -1);

		return {
			latitude: lat + latOffset,
			longitude: lng + lngOffset
		};
	}

	private static sanitizeLocation(location: any): PrivateLocation | undefined {
		if (!location) return undefined;

		// Only return allowed fields
		return {
			city: location.city,
			state: location.state,
			radius: location.radius,
			emergency_radius: location.emergency_radius,
			inspection_radius: location.inspection_radius,
			// Fuzz the coordinates
			...this.fuzzLocation(location.latitude, location.longitude)
		};
	}

	private static sanitizeListings(listings: any[]) {
		return listings.map((listing) => ({
			...listing,
			location: this.sanitizeLocation(listing.location)
		}));
	}

	private static sortListings(listings: any[], params: SearchParams) {
		// For distance sorting, just sort by distance
		if (params.sortBy === "distance") {
			return this.sortByVerificationAndDistance(listings);
		}

		// For reviews sorting
		if (params.sortBy === "reviews") {
			return listings.sort((a, b) => {
				if (a.rating !== b.rating) {
					return (b.rating || 0) - (a.rating || 0);
				}
				return (b.num_reviews || 0) - (a.num_reviews || 0);
			});
		}

		// best match
		if (params.category === "rv-repair") {
			return this.sortTechnicians(listings);
		} else {
			return this.sortInspectors(listings, params);
		}
	}

	private static sortTechnicians(listings: any[]) {
		// Separate verified (NOT NONE and not null) from unverified (NONE or null)
		const verifiedListings = listings.filter(
			(l) =>
				l.rv_help_verification_level &&
				l.rv_help_verification_level !== RVHelpVerificationLevel.NONE
		);
		const nonVerifiedListings = listings.filter(
			(l) =>
				!l.rv_help_verification_level ||
				l.rv_help_verification_level === RVHelpVerificationLevel.NONE
		);

		const sortBandedListings = (listings: any[]) => {
			// Group into bands
			const bands = new Map<number, any[]>();
			listings.forEach((listing) => {
				const band = Math.floor((listing.distance || 0) / 50); // 50-mile bands for techs
				if (!bands.has(band)) bands.set(band, []);
				bands.get(band)!.push(listing);
			});

			// Sort within each band and maintain band order
			return Array.from(bands.entries())
				.sort(([bandA], [bandB]) => bandA - bandB) // Sort bands by distance
				.map(([_, bandListings]) =>
					// Sort listings within each band
					bandListings.sort((a, b) => {
						// Sort by certification level within band
						const certDiff =
							(b.rvtaa_technician_level || 0) - (a.rvtaa_technician_level || 0);
						if (certDiff !== 0) return certDiff;

						// If same certification, sort by exact distance
						return (a.distance || 0) - (b.distance || 0);
					})
				)
				.flat(); // Flatten the sorted bands into a single array
		};

		return [
			...sortBandedListings(verifiedListings),
			...sortBandedListings(nonVerifiedListings)
		];
	}

	private static sortByVerificationAndDistance(listings: any[]) {
		// Default sort: Split into verified and non-verified groups
		const verifiedListings = listings.filter(
			(l) =>
				l.rv_help_verification_level &&
				l.rv_help_verification_level !== RVHelpVerificationLevel.NONE
		);
		const nonVerifiedListings = listings.filter(
			(l) =>
				!l.rv_help_verification_level ||
				l.rv_help_verification_level === RVHelpVerificationLevel.NONE
		);

		return [
			...verifiedListings.sort(sortByDistance),
			...nonVerifiedListings.sort(sortByDistance)
		];
	}

	private static groupByDistanceBand(listings: any[], category: string) {
		const distanceBands = listings.reduce((acc, listing) => {
			const distance = listing.distance || 0;
			const band = Math.floor(distance / (category === "rv-repair" ? 50 : 100));
			if (!acc[band]) {
				acc[band] = [];
			}
			acc[band].push(listing);
			return acc;
		}, {});

		return Object.values(distanceBands);
	}

	private static sortByBandAndCertification(a: any, b: any, category: string) {
		const getDistanceBand = (distance: number): number => {
			return Math.floor(distance / (category === "rv-repair" ? 50 : 100));
		};

		// For technicians, keep the original band-based sorting
		if (category === "rv-repair") {
			const aDistanceBand = getDistanceBand(a.distance || 0);
			const bDistanceBand = getDistanceBand(b.distance || 0);
			if (aDistanceBand !== bDistanceBand) {
				return aDistanceBand - bDistanceBand;
			}
			return (b.rvtaa_technician_level || 0) - (a.rvtaa_technician_level || 0);
		}

		// For inspectors, sort by certification level first, then use bands as tiebreaker
		const certificationDiff =
			(b.nrvia_inspector_level || 0) - (a.nrvia_inspector_level || 0);
		if (certificationDiff !== 0) {
			return certificationDiff;
		}

		const reviewsDiff = (b.num_reviews || 0) - (a.num_reviews || 0);
		if (reviewsDiff !== 0) {
			return reviewsDiff;
		}

		const certifiedTechDiff =
			(b.rvtaa_technician_level || 0) - (a.rvtaa_technician_level || 0);
		if (certifiedTechDiff !== 0) {
			return certifiedTechDiff;
		}

		// Use distance bands as final tiebreaker
		const aDistanceBand = getDistanceBand(a.distance || 0);
		const bDistanceBand = getDistanceBand(b.distance || 0);
		if (aDistanceBand !== bDistanceBand) {
			return aDistanceBand - bDistanceBand;
		}

		// If in same band, sort by exact distance
		return (a.distance || 0) - (b.distance || 0);
	}

	private static sortInspectors(listings: any[], _params: SearchParams) {
		const verifiedListings = listings.filter(
			(l) =>
				l.rv_help_verification_level &&
				l.rv_help_verification_level !== RVHelpVerificationLevel.NONE
		);
		const nonVerifiedListings = listings.filter(
			(l) =>
				!l.rv_help_verification_level ||
				l.rv_help_verification_level === RVHelpVerificationLevel.NONE
		);

		const sortBandedListings = (listings: any[]) => {
			// Group into bands
			const bands = new Map<number, any[]>();
			listings.forEach((listing) => {
				const band = Math.floor((listing.distance || 0) / 100); // 100-mile bands for inspectors
				if (!bands.has(band)) bands.set(band, []);
				bands.get(band)!.push(listing);
			});

			// Sort within each band and maintain band order
			return Array.from(bands.entries())
				.sort(([bandA], [bandB]) => bandA - bandB)
				.map(([_, bandListings]) =>
					bandListings.sort((a, b) => {
						// Sort by certification level first
						const certDiff =
							(b.nrvia_inspector_level || 0) - (a.nrvia_inspector_level || 0);
						if (certDiff !== 0) return certDiff;

						const aRating = a.rating || 0;
						const bRating = b.rating || 0;
						const aReviews = a.num_reviews || 0;
						const bReviews = b.num_reviews || 0;

						// If both have at least 10 reviews
						if (aReviews >= 10 && bReviews >= 10) {
							const aIsHighRated = aRating >= 4.8;
							const bIsHighRated = bRating >= 4.8;

							// if one has more than 20 reviews
							const aHasMoreReviews = aReviews > 20;
							const bHasMoreReviews = bReviews > 20;

							// If one is high-rated and the other isn't
							if (aIsHighRated !== bIsHighRated) {
								return bIsHighRated ? 1 : -1;
							}

							// If both are high-rated
							if (aIsHighRated && bIsHighRated) {
								// if one has more than 20 reviews, it wins
								if (aHasMoreReviews !== bHasMoreReviews) {
									return aHasMoreReviews ? -1 : 1;
								}
								return (a.distance || 0) - (b.distance || 0);
							}
						}

						// Traditional sort if either has less than 10 reviews
						const reviewsDiff = bReviews - aReviews;
						if (reviewsDiff !== 0) return reviewsDiff;

						if (aRating !== bRating) return bRating - aRating;

						// Then by tech certification
						const techDiff =
							(b.rvtaa_technician_level || 0) - (a.rvtaa_technician_level || 0);
						if (techDiff !== 0) return techDiff;

						// Finally by exact distance within band
						return (a.distance || 0) - (b.distance || 0);
					})
				)
				.flat();
		};

		return [
			...sortBandedListings(verifiedListings),
			...sortBandedListings(nonVerifiedListings)
		];
	}

	static async getListingsByLatLong(
		params: SearchParams,
		page: number = 1,
		pageSize: number = 10
	): Promise<any> {
		try {
			const lat = Number(params.lat);
			const lng = Number(params.lng);
			const BOUNDING_BOX_RADIUS = 250; // Miles
			const milesPerLat = 69;
			const milesPerLng = 69 * Math.cos((lat * Math.PI) / 180);
			const latDelta = BOUNDING_BOX_RADIUS / milesPerLat;
			const lngDelta = BOUNDING_BOX_RADIUS / milesPerLng;

			if (isNaN(lat) || isNaN(lng)) {
				throw new Error("Invalid latitude or longitude");
			}

			// Get all listings within the bounding box using the "locations" array
			const listings = await prisma.listing.findMany({
				where: {
					AND: [
						this.buildWhereClause(params),
						{
							locations: {
								some: {
									AND: [
										{
											latitude: {
												gte: lat - latDelta,
												lte: lat + latDelta
											},
											longitude: {
												gte: lng - lngDelta,
												lte: lng + lngDelta
											}
										},
										{
											OR: [
												{
													start_date: null,
													end_date: null
												},
												{
													start_date: { lte: new Date() },
													end_date: { gte: new Date() },
												}
											]
										}
									],
								}
							}
						}
					]
				} as any,
				// Update selection to include all needed location fields
				select: {
					...safeFields,
					locations: {
						select: {
							city: true,
							latitude: true,
							longitude: true,
							state: true,
							inspection_radius: true,
							radius: true,
							default: true,
							start_date: true,
							end_date: true,
							description: true,
							emergency_radius: true
						}
					}
				} as any
			});

			if (!listings?.length) {
				return {
					listings: [],
					total: 0,
					page,
					pageSize
				};
			}

			// Process listings: pick active location and calculate distance from that location
			const processedListings = await Promise.all(
				listings.map(async (listing) => {
					// Determine the active location based on dates or fallback to default
					const activeLocation = SearchService.getActiveLocation(listing.locations);
					if (!activeLocation) return listing;

					// Calculate distance using the active location coordinates
					const distance = await this.calculateDistanceWithMethod(
						lat,
						lng,
						Number(activeLocation.latitude) || 0,
						Number(activeLocation.longitude) || 0,
						params.useDrivingDistance || false
					);
					return {
						...listing,
						// Replace "location" with the active location for further processing
						location: activeLocation,
						distance: Number.isFinite(distance) ? distance : Infinity
					};
				})
			);

			const filteredListings = processedListings
				.filter((listing) => {
					(listing as any).categories = (listing as any).categories || {};
					if (!params.subcategory) {
						return true;
					}
					if ((listing as any).categories[params.category]?.subcategories) {
						return (listing as any).categories[params.category].subcategories[
							params.subcategory
						];
					}
					return false;
				})
				.filter((listing) => {
					// Filter out providers with vacation mode enabled
					if ((listing as any).vacation_mode &&
						typeof (listing as any).vacation_mode === 'object' &&
						(listing as any).vacation_mode !== null &&
						'enabled' in (listing as any).vacation_mode &&
						((listing as any).vacation_mode as any).enabled === true) {
						return false;
					}
					return true;
				})
				.filter((listing) => {
					// First filter by radius parameter if provided
					if (params.radius) {
						const withinRadius =
							(listing as any).distance <= Number(params.radius);
						if (!withinRadius) {
							return false;
						}
					}
					return this.filterByServiceRadius(listing, params);
				});

			// Sort all filtered listings
			const sortedListings = this.sortListings(filteredListings, params);

			// Apply pagination after all processing
			const startIndex = (page - 1) * pageSize;
			const paginatedListings = sortedListings.slice(
				startIndex,
				startIndex + pageSize
			);

			return {
				listings: this.sanitizeListings(paginatedListings),
				total: sortedListings.length,
				page,
				pageSize
			};
		} catch (error) {
			console.error("Error in getListingsByLatLong:", error);
			throw error;
		}
	}

	static async getListingsByCityState(
		params: SearchParams,
		page?: number,
		limit: number = 10
	) {
		if (params.city) {
			// reverse geocode the city and state to get the lat and lng
			const { lat, lng } = await this.reverseGeocode(
				params.city,
				params.state!
			);
			// delete the city and state from the params
			if (!lat || !lng) {
				// Return empty results if geocoding fails
				return {
					listings: [],
					total: 0,
					page,
					pageSize: limit
				};
			}

			const { city, state, ...rest } = params;

			return this.getListingsByLatLong(
				{
					...rest,
					lat: lat.toString(),
					lng: lng.toString()
				},
				page,
				limit
			);
		} else {
			return {
				listings: [],
				total: 0,
				page,
				pageSize: limit
			};
		}
	}

	private static async reverseGeocode(city: string, state: string) {
		const response = await fetch(
			`https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
				`${city}, ${state}`
			)}.json?access_token=${config.MAPBOX_PUBLIC_TOKEN}&types=place,locality,postcode&country=us,ca`
		);
		const data = await response.json();

		if (data.features && data.features.length > 0) {
			const [lng, lat] = data.features[0].center;
			return { lat, lng };
		}

		// Return null instead of throwing error to maintain backward compatibility
		return { lat: null, lng: null };
	}

	static async searchByBusinessName(query: string, limit: number = 10) {
		try {
			const listings = await prisma.listing.findMany({
				where: {
					OR: [
						{ business_name: { contains: query, mode: "insensitive" } },
						{ first_name: { contains: query, mode: "insensitive" } },
						{ last_name: { contains: query, mode: "insensitive" } }
					],
					is_active: true,
					status: "ACTIVE"
				},
				select: {
					...safeFields,
					locations: {
						select: {
							city: true,
							latitude: true,
							longitude: true,
							state: true,
							inspection_radius: true,
							radius: true,
							default: true,
							start_date: true,
							end_date: true,
						}
					}
				} as any,
				take: limit,
				orderBy: [{ rv_help_verification_level: "desc" }, { rating: "desc" }]
			});

			// Set active location for each listing
			const processed = listings.map((listing) => {
				const activeLoc = SearchService.getActiveLocation(listing.locations);
				return { ...listing, location: activeLoc };
			});

			return this.sanitizeListings(processed);
		} catch (error) {
			console.error("Error in searchByBusinessName:", error);
			throw error;
		}
	}

	private static filterByServiceRadius(listing: any, params: SearchParams) {
		// For rv-inspection category, first try inspection_radius, then fall back to regular radius
		let serviceRadius = 0;

		if (params.category === "rv-inspection") {
			if (listing.location?.inspection_radius) {
				serviceRadius = Number(listing.location.inspection_radius);
			} else if (listing.location?.radius) {
				serviceRadius = Number(listing.location.radius);
			}
		} else {
			serviceRadius = Number(listing.location?.radius || 0);
		}

		const distance = Number((listing as any).distance);

		// If radius parameter is provided, check both search radius and service radius
		if (params.radius) {
			const searchRadius = Number(params.radius);
			return distance <= searchRadius && distance <= serviceRadius;
		}

		// Otherwise, use the provider's service radius
		return distance <= serviceRadius;
	}

	static async findNearestCitiesWithProviders(
		lat: number,
		lng: number,
		category: string,
		limit: number = 3,
		useDrivingDistance: boolean = false
	) {
		try {
			// Step 1: Find nearest cities with at least one provider
			const INITIAL_RADIUS = 250; // Miles
			const milesPerLat = 69;
			const milesPerLng = 69 * Math.cos((lat * Math.PI) / 180);
			const latDelta = INITIAL_RADIUS / milesPerLat;
			const lngDelta = INITIAL_RADIUS / milesPerLng;

			// Query using the locations array
			const nearestCities = await prisma.listing.findMany({
				where: {
					AND: [
						{
							is_active: true,
							categories: {
								path: [category, "selected"],
								equals: true
							}
						},
						{
							locations: {
								some: {
									latitude: { gte: lat - latDelta, lte: lat + latDelta },
									longitude: { gte: lng - lngDelta, lte: lng + lngDelta }
								}
							}
						}
					]
				} as any,
				select: {
					id: true,
					locations: {
						select: {
							city: true,
							state: true,
							latitude: true,
							longitude: true,
							radius: true,
							default: true,
							start_date: true,
							end_date: true
						}
					}
				} as any
			});

			// Use active location for each listing to get unique city identifiers
			const uniqueCities = Array.from(
				new Set(
					nearestCities
						.map((l) => {
							const activeLoc = SearchService.getActiveLocation(l.locations);
							return activeLoc ? `${activeLoc.city},${activeLoc.state}` : null;
						})
						.filter(Boolean)
				)
			)
				.map((cityKey) => {
					const listing = nearestCities.find((l) => {
						const activeLoc = SearchService.getActiveLocation(l.locations);
						return activeLoc && `${activeLoc.city},${activeLoc.state}` === cityKey;
					});
					return listing ? SearchService.getActiveLocation(listing.locations) : null;
				})
				.filter(Boolean);

			// Calculate distances and sort cities
			const citiesWithDistances = await Promise.all(
				uniqueCities.map(async (city) => ({
					city: city!.city,
					state: city!.state,
					latitude: city!.latitude,
					longitude: city!.longitude,
					distance: await this.calculateDistanceWithMethod(
						lat,
						lng,
						city!.latitude,
						city!.longitude,
						useDrivingDistance
					)
				}))
			);

			const sortedCities = citiesWithDistances
				.sort((a, b) => a.distance - b.distance)
				.slice(0, limit);

			// For each city, count provider listings whose active location is within its service radius
			const citiesWithProviders = await Promise.all(
				sortedCities.map(async (city) => {
					const providers = await prisma.listing.findMany({
						where: {
							AND: [
								{
									is_active: true,
									categories: {
										path: [category, "selected"],
										equals: true
									}
								},
								{
									locations: {
										some: {
											latitude: { gte: city.latitude - latDelta, lte: city.latitude + latDelta },
											longitude: { gte: city.longitude - lngDelta, lte: city.longitude + lngDelta }
										}
									}
								}
							]
						} as any,
						select: {
							id: true,
							locations: {
								select: {
									radius: true,
									latitude: true,
									longitude: true,
									default: true,
									start_date: true,
									end_date: true
								}
							}
						} as any
					});

					const techCount = providers.filter((provider) => {
						const activeLoc = SearchService.getActiveLocation(provider.locations);
						if (!activeLoc || !activeLoc.radius) return false;
						const distanceToCity = calculateDistance(
							city.latitude,
							city.longitude,
							Number(activeLoc.latitude),
							Number(activeLoc.longitude)
						);
						return distanceToCity <= activeLoc.radius;
					}).length;

					return {
						city: city.city,
						state: city.state,
						distance: Math.round(city.distance),
						techCount
					};
				})
			);

			return citiesWithProviders.filter((city) => city.techCount > 0);
		} catch (error) {
			console.error("Error in findNearestCitiesWithProviders:", error);
			throw error;
		}
	}

	/**
	 * Specialized method for dispatch emails to find nearest verified providers
	 * Uses progressive radius expansion to find the requested number of providers
	 */
	static async findNearestVerifiedProviders(
		latitude: number,
		longitude: number,
		category: string,
		count: number = 20,
		maxRadiusMiles: number = 500,
		useDrivingDistance: boolean = false
	) {
		try {
			console.log("🔍 FindNearestVerifiedProviders params:", {
				latitude,
				longitude,
				category,
				count,
				maxRadiusMiles
			});

			if (isNaN(latitude) || isNaN(longitude)) {
				throw new Error("Invalid latitude or longitude");
			}

			// Progressive search radiuses: start small and expand until we find enough providers
			const searchRadiuses = [50, 100, 150, 250, 350, 500];
			const targetRadius =
				searchRadiuses.find((r) => r >= maxRadiusMiles) || maxRadiusMiles;
			const radiusesToTry = searchRadiuses.filter((r) => r <= targetRadius);
			if (radiusesToTry[radiusesToTry.length - 1] !== targetRadius) {
				radiusesToTry.push(targetRadius);
			}

			console.log("🎯 Will try radiuses:", radiusesToTry);

			let allProviders: any[] = [];
			let actualRadius = 0;

			// Try each radius until we have enough providers
			for (const radiusMiles of radiusesToTry) {
				console.log(`🔍 Searching within ${radiusMiles} miles...`);

				// Calculate bounding box for this radius
				const milesPerLat = 69;
				const milesPerLng = 69 * Math.cos((latitude * Math.PI) / 180);
				const latDelta = radiusMiles / milesPerLat;
				const lngDelta = radiusMiles / milesPerLng;

				// Query for VERIFIED providers within bounding box
				const listings = await prisma.listing.findMany({
					where: {
						is_active: true,
						status: "ACTIVE",
						rv_help_verification_level: "VERIFIED", // Direct field match for VERIFIED only
						...(category && {
							categories: {
								path: [category, "selected"],
								equals: true
							}
						}),
						locations: {
							some: {
								latitude: {
									gte: latitude - latDelta,
									lte: latitude + latDelta
								},
								longitude: {
									gte: longitude - lngDelta,
									lte: longitude + lngDelta
								}
							}
						}
					} as any,
					select: {
						...safeFields,
						email: true,
						settings_dispatch_emails_opt_out: true,
						is_active: true,
						status: true
					}
				});

				console.log(
					`📊 Found ${listings.length} verified providers in ${radiusMiles} mile bounding box`
				);

				// Calculate distances and filter by exact radius
				const providersWithDistance = await Promise.all(
					listings.map(async (listing) => {
						const activeLocation = SearchService.getActiveLocation((listing as any).locations);
						if (!activeLocation) return null;

						const distance = await this.calculateDistanceWithMethod(
							latitude,
							longitude,
							Number(activeLocation?.latitude) || 0,
							Number(activeLocation?.longitude) || 0,
							useDrivingDistance
						);

						return {
							...listing,
							distance: Math.round(distance * 100) / 100 // Round to 2 decimal places
						};
					})
				);

				const filteredProviders = providersWithDistance.filter(
					(provider): provider is NonNullable<typeof provider> =>
						provider !== null && provider.distance <= radiusMiles
				);

				console.log(
					`📈 After distance filtering: ${filteredProviders.length} providers within ${radiusMiles} miles`
				);

				// If this radius gives us enough providers, we're done
				if (filteredProviders.length >= count) {
					allProviders = filteredProviders
						.sort((a, b) => a.distance - b.distance) // Simple distance sort
						.slice(0, count); // Take only the requested count
					actualRadius = radiusMiles;
					console.log(
						`✅ Found ${count} providers within ${radiusMiles} miles`
					);
					break;
				} else {
					// Keep all providers found so far for the next iteration
					allProviders = filteredProviders;
					actualRadius = radiusMiles;
					console.log(
						`⏭️ Only found ${filteredProviders.length} providers, trying larger radius...`
					);
				}
			}

			// Sort final results by distance
			allProviders.sort((a, b) => a.distance - b.distance);

			console.log(
				"🎯 Final results:",
				allProviders.slice(0, 5).map((p) => ({
					id: p.id,
					name: `${p.first_name} ${p.last_name}`,
					distance: p.distance,
					hasEmail: !!p.email,
					optedOut: p.settings_dispatch_emails_opt_out
				}))
			);

			console.log(
				`🎉 Returning ${allProviders.length} providers within ${actualRadius} miles`
			);

			return {
				listings: allProviders,
				total: allProviders.length,
				searchRadius: actualRadius,
				searchCenter: { latitude, longitude }
			};
		} catch (error) {
			console.error("❌ Error in findNearestVerifiedProviders:", error);
			throw error;
		}
	}
}

// Sort each group by distance
const sortByDistance = (a: any, b: any) => {
	const distA = Number(a.distance) || 0;
	const distB = Number(b.distance) || 0;
	return distA - distB;
};
