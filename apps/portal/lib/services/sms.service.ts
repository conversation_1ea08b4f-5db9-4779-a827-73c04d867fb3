// Portal SMS service with shared infrastructure
import config from '@/config';
import prisma from '@/lib/prisma';
import { formatToE164 } from '@/lib/utils';
import { SharedSmsService, SmsServiceConfig } from '@rvhelp/services';

export interface PortalSmsService {
    send(to: string, message: string): Promise<any>;
    sendToProvider(to: string, message: string): Promise<any>;
    sendToUser(to: string, message: string): Promise<any>;
}

export class PortalSmsServiceImpl extends SharedSmsService implements PortalSmsService {
    constructor() {
        const smsConfig: SmsServiceConfig = {
            twilio: {
                accountSid: config.twilio.accountSid,
                authToken: config.twilio.authToken,
                providerFromNumber: config.twilio.providerFromNumber,
                userFromNumber: config.twilio.userFromNumber
            },
            isDevelopment: config.isDevelopment,
            formatToE164: formatToE164
        };

        super(prisma, smsConfig);
    }
}

export function getSmsService(): PortalSmsService {
    return new PortalSmsServiceImpl();
}

export const smsService = getSmsService();
