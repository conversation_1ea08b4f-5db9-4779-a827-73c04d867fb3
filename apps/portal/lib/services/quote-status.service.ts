import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { ProviderStatsService } from "@/lib/services/provider-stats.service";
import {
	Quote,
	QuoteStatus,
	RejectionReasonType,
	WarrantyRequestStatus
} from "@rvhelp/database";

import { ExtendedWarrantyRequest } from "@/types/warranty";


export interface QuoteStatusChangeParams {
	quoteId: string;
	listingId: string;
	newStatus: QuoteStatus;
	message?: string;
	schedulingTimeframe?: string;
	rejectionReason?: string;
	rejectionReasonDetails?: string;
	userId?: string; // For tracking who made the change
}

export class QuoteStatusService {
	/**
	 * Change quote status and trigger appropriate side effects
	 */
	static async changeQuoteStatus(params: QuoteStatusChangeParams) {
		const {
			quoteId,
			listingId,
			newStatus,
			message,
			rejectionReason,
			rejectionReasonDetails
		} = params;

		const now = new Date();
		const updateData: Partial<Quote> = {
			status: newStatus
		};

		// Set appropriate timestamps based on status change
		switch (newStatus) {
			case QuoteStatus.ACCEPTED:
			case QuoteStatus.WITHDRAWN:
				updateData.responded_at = now;
				if (message) updateData.provider_notes = message;
				if (rejectionReason) updateData.rejection_reason = rejectionReason as RejectionReasonType;
				if (rejectionReasonDetails)
					updateData.rejection_reason_details = rejectionReasonDetails;
				break;

			case QuoteStatus.ACCEPTED:
				updateData.accepted_at = now;
				break;

			case QuoteStatus.REJECTED:
				// For rejections, don't change responded_at if it's already set
				if (message) updateData.provider_notes = message;
				if (rejectionReason) updateData.rejection_reason = rejectionReason as RejectionReasonType;
				if (rejectionReasonDetails)
					updateData.rejection_reason_details = rejectionReasonDetails;
				break;
		}

		// Update the quote

		const updatedQuote = await prisma.quote.update({
			where: { id: quoteId },
			data: updateData,
			include: {
				job: {
					include: {
						user: true
					}
				},
				listing: {
					include: {
						locations: true
					} as any
				},
				messages: {
					orderBy: {
						created_at: "desc"
					}
				}
			}
		});

		// Job updates are now only handled by customerAcceptQuote method
		// This ensures job status only changes when customers explicitly accept quotes

		// Trigger appropriate stats updates
		await this.triggerStatsUpdate(newStatus, listingId);

		return updatedQuote;
	}


	/**
	 * Trigger appropriate stats updates based on status change
	 */
	private static async triggerStatsUpdate(
		newStatus: QuoteStatus,
		listingId: string
	) {
		switch (newStatus) {
			case QuoteStatus.ACCEPTED:
			case QuoteStatus.WITHDRAWN:
				// Provider responded (either with quote or decline)
				await ProviderStatsService.onProviderResponse(listingId);
				break;

			case QuoteStatus.ACCEPTED:
				// Job was accepted by customer
				await ProviderStatsService.onJobAccepted(listingId);
				break;

			case QuoteStatus.REJECTED:
				// Provider rejected/declined - might affect completion rates
				await ProviderStatsService.updateProviderStats(listingId);
				break;

			case QuoteStatus.CUSTOMER_REJECTED:
				// Customer rejected provider's proposal - might affect completion rates
				await ProviderStatsService.updateProviderStats(listingId);
				break;

			case QuoteStatus.PENDING:
				// No stats update needed for pending (initial invites or info requests)
				break;

			case QuoteStatus.EXPIRED:
				// Expired quotes might affect response rates
				await ProviderStatsService.updateProviderStats(listingId);
				break;
		}
	}

	/**
	 * Customer cancels a job - handles all related quote logic
	 */
	static async oemCancelJob(params: {
		jobId: string;
		userId: string;
		reason: string;
	}) {
		const { jobId, userId, reason } = params;

		// Get the job with all quotes
		const job = await prisma.job.findFirst({
			where: {
				id: jobId,
				user_id: userId,
			},
			include: {
				quotes: {
					include: {
						listing: {
							select: {
								id: true,
								business_name: true,
								first_name: true,
								last_name: true,
								email: true,
							},
						},
					},
				},
				warranty_request: {
					include: {
						company: true
					}
				},
				user: true,
			},
		});

		if (!job) {
			throw new Error("Job not found or access denied");
		}

		// Expire all quotes regardless of job stage
		await this.expireQuotes(job.quotes, userId);

		await this.notifyCustomerJobCancelled(job.warranty_request as ExtendedWarrantyRequest, reason);

		// Notify all providers about cancellation
		await this.notifyProvidersJobCancelled(
			job.quotes,
			reason,
			`${job.user.first_name} ${job.user.last_name}`
		);

		return job;
	}

	/**
	 * Helper method to expire multiple quotes
	 */
	private static async expireQuotes(quotes: any[], userId: string) {
		for (const quote of quotes) {
			// Only expire quotes that are still pending or accepted (not already expired/rejected)
			if (quote.status === QuoteStatus.PENDING || quote.status === QuoteStatus.ACCEPTED || quote.status === QuoteStatus.IN_PROGRESS) {
				await this.changeQuoteStatus({
					quoteId: quote.id,
					listingId: quote.listing_id,
					newStatus: QuoteStatus.EXPIRED,
					userId,
				});
			}
		}
	}

	/**
 * Send notification to providers that job is no longer available
 */
	private static async notifyCustomerJobCancelled(
		warrantyRequest: ExtendedWarrantyRequest,
		reason: string,
	) {
		if (warrantyRequest.status === WarrantyRequestStatus.JOB_CANCELLED) {
			// Use professional email template for cancellations
			const { JobCancelledEmail } = await import(
				"@/components/email-templates/customer/CustomerJobCancelledEmail"
			);

			await emailService.send({
				to: warrantyRequest.email,
				subject: "Warranty Service Cancelled",
				react: JobCancelledEmail({
					companyName: warrantyRequest.company.name,
					supportPhone: warrantyRequest.company.support_phone,
					customerName: `${warrantyRequest.first_name} ${warrantyRequest.last_name}`,
					reason,
				}),
			});
		}
	};

	/**
	 * Send notification to providers that job is no longer available
	 */
	private static async notifyProvidersJobCancelled(
		quotes: any[],
		reason: string,
		customerName?: string
	) {
		for (const quote of quotes) {
			// Only notify providers who had pending or accepted quotes
			if (quote.status === QuoteStatus.PENDING || quote.status === QuoteStatus.ACCEPTED || quote.status === QuoteStatus.IN_PROGRESS) {
				const providerEmail = quote.listing.notification_email || quote.listing.email;
				const providerName = quote.listing.business_name ||
					`${quote.listing.first_name} ${quote.listing.last_name}`;

				try {
					// Use professional email template for cancellations
					const { JobCancelledEmail } = await import(
						"@/components/email-templates/provider/JobCancelledEmail"
					);

					await emailService.send({
						to: providerEmail,
						subject: "Warranty Service Cancelled",
						react: JobCancelledEmail({
							companyName: quote.listing.company_name,
							customerName: customerName || "The customer",
							providerName,
							reason,
						}),
					});
				} catch (error) {
					console.error(`❌ Failed to send notification to provider ${providerEmail}:`, error);
					// Don't fail the entire operation if email fails
				}
			} else {
				console.log(`🟡 Skipping notification for provider ${quote.listing.business_name || quote.listing.first_name} (quote status: ${quote.status})`);
			}
		}
	}
}
