/**
 * Service for handling Stripe-related operations
 * @module StripeService
 */

import config from "@/config";
import prisma from "@/lib/prisma";
import { stripe } from "@/lib/stripe";
import Stripe from "stripe";
import { UserService } from "./user.service";

type AccountLinkType = Stripe.AccountLinkCreateParams.Type;

export class StripeService {
	/**
	 * Creates a new Stripe Connect Express account
	 * @param {string} userId - The ID of the user to create the account for
	 * @returns {Promise<string>} The ID of the created Stripe account
	 */
	static async createConnectAccount(userId: string): Promise<string> {
		const user = await UserService.findById(userId);
		if (!user) throw new Error("User not found");
		if (user?.role !== "PROVIDER") throw new Error("User is not a provider");

		// Check if user already has a stripe connection
		const existingConnection = await prisma.stripeConnection.findUnique({
			where: { user_id: userId }
		});

		if (existingConnection) {
			return existingConnection.stripe_account_id;
		}

		// Get the user's listing to get the website and slug
		const listing = await prisma.listing.findFirst({
			where: { owner_id: userId }
		});

		const account = await stripe.accounts.create({
			type: "express",
			email: user.email,
			metadata: { userId },
			business_profile: {
				url: listing
					? `${process.env.NEXT_PUBLIC_APP_URL}/providers/${listing.slug}`
					: undefined
			}
		});

		// Create StripeConnection record
		await prisma.stripeConnection.create({
			data: {
				user_id: userId,
				stripe_account_id: account.id,
				stripe_account_status: "pending",
				payments_enabled: false,
				details_submitted: false,
				payouts_enabled: false,
				is_verified: false
			}
		});

		return account.id;
	}

	/**
	 * Creates a new account link for onboarding or updates
	 */
	static async createAccountLink(
		accountId: string,
		type: AccountLinkType = "account_onboarding",
		_listingId: string
	): Promise<string> {
		const connection = await prisma.stripeConnection.findUnique({
			where: { stripe_account_id: accountId }
		});

		if (!connection) {
			throw new Error("No Stripe connection found");
		}

		const accountLink = await stripe.accountLinks.create({
			account: connection.stripe_account_id,
			refresh_url: `${process.env.NEXT_PUBLIC_APP_URL}/provider/billing/settings/refresh`,
			return_url: `${process.env.NEXT_PUBLIC_APP_URL}/provider/billing/settings/return`,
			type: type
		});

		return accountLink.url;
	}

	/**
	 * Gets the current balance for a Stripe account
	 */
	static async getAccountBalance(userId: string) {
		const connection = await prisma.stripeConnection.findUnique({
			where: { user_id: userId }
		});

		if (!connection) {
			throw new Error("No Stripe connection found");
		}

		const balance = await stripe.balance.retrieve({
			stripeAccount: connection.stripe_account_id
		});

		return {
			available: balance.available.reduce((sum, bal) => sum + bal.amount, 0),
			pending: balance.pending.reduce((sum, bal) => sum + bal.amount, 0)
		};
	}

	/**
	 * Gets the stripe connection for a user
	 */
	static async getStripeConnection(listingId: string) {
		// get all listing users
		const users = await prisma.userListing.findMany({
			where: { listing_id: listingId }
		});

		const listing = await prisma.listing.findUnique({
			where: { id: listingId }
		});

		// get any stripe connection for the listing.owner user or any of the users in the users array
		const stripeConnection = await prisma.stripeConnection.findFirst({
			where: {
				OR: [
					...(listing.owner_id ? [{ user_id: listing.owner_id }] : []),
					{ user_id: { in: users.map((user) => user.user_id) } }
				]
			}
		});

		return stripeConnection;
	}

	/**
	 * Updates stripe connection status based on Stripe account status
	 */
	static async syncConnectionStatus(accountId: string) {
		const account = await stripe.accounts.retrieve(accountId);

		return prisma.stripeConnection.update({
			where: { stripe_account_id: accountId },
			data: {
				stripe_account_status: account.details_submitted ? "active" : "pending",
				payments_enabled: account.charges_enabled,
				details_submitted: account.details_submitted,
				payouts_enabled: account.payouts_enabled,
				is_verified: account.payouts_enabled && account.charges_enabled
			}
		});
	}

	/**
	 * Deletes a Stripe connection for a user
	 */
	static async deleteStripeConnection(userId: string) {
		return prisma.stripeConnection.delete({
			where: { user_id: userId }
		});
	}

	/**
	 * Creates a Stripe customer for a user
	 */
	static async createCustomer(
		email: string,
		name: string,
		phone?: string,
		metadata?: Record<string, string>
	): Promise<string> {
		const customer = await stripe.customers.create({
			email,
			name,
			phone,
			metadata
		});

		return customer.id;
	}

	// Calculate total amount for checkout
	static async calculateCheckoutAmount(params: {
		productType: "rv_help_premium" | "rv_help_guarantee";
		dispatchFee: number;
		isFirstYear?: boolean;
	}) {
		const { productType: _productType, dispatchFee: _dispatchFee, isFirstYear: _isFirstYear = true } = params;

		// if (productType === "rv_help_premium") {
		// 	// Premium membership: Regular price - first year discount + discounted dispatch fee
		// 	const membershipFee = isFirstYear ? MEMBERSHIP_FIRST_YEAR_PRICE : MEMBERSHIP_REGULAR_PRICE;
		// 	const discountedDispatchFee = dispatchFee * (1 - LABOR_DISCOUNT);
		// 	return Math.round((membershipFee + discountedDispatchFee) * 100); // Convert to cents
		// } else {
		// 	// Regular booking: Dispatch fee + platform fee
		// 	return Math.round((dispatchFee + PLATFORM_FEE) * 100); // Convert to cents
		// }
		return 0;
	}

	/**
	 * Creates a payment intent on the platform account with transfer to the connected account
	 */
	static async createPlatformPaymentIntent({
		ownerId,
		amount,
		currency,
		_providerId,
		metadata,
		payment_method_types
	}: {
		ownerId: string;
		amount: number;
		currency: string;
		_providerId: string;
		metadata?: Record<string, string>;
		payment_method_types?: string[];
	}) {
		// Get the provider's Stripe account
		const connection = await this.getStripeConnection(ownerId);
		if (!connection) {
			throw new Error("No Stripe connection found");
		}

		console.log("amount", amount);

		// Create the payment intent on the platform account
		const paymentIntent = await stripe.paymentIntents.create({
			amount: amount,
			currency,
			metadata,
			payment_method_types: payment_method_types || ["card"],
			// Set up transfer to the connected account
			transfer_data: {
				destination: connection.stripe_account_id,
				// Platform fee (0%)
				amount: amount // 100% goes to the provider
			}
		});

		console.log("paymentIntent", paymentIntent);

		return paymentIntent;
	}

	/**
	 * Retrieves a payment intent by ID
	 */
	static async retrievePaymentIntent(paymentIntentId: string) {
		return stripe.paymentIntents.retrieve(paymentIntentId);
	}

	/**
	 * Creates a login link for a connected account to access their Express dashboard
	 */
	static async createLoginLink(userId: string): Promise<string> {
		const connection = await this.getStripeConnection(userId);

		if (!connection) {
			throw new Error("No Stripe connection found");
		}

		const loginLink = await stripe.accounts.createLoginLink(
			connection.stripe_account_id
		);

		return loginLink.url;
	}

	/**
	 * Gets the publishable key for Stripe
	 */
	static async getPublishableKey(providerId: string): Promise<string> {
		// Get the provider's Stripe account
		const connection = await this.getStripeConnection(providerId);
		if (!connection) {
			throw new Error("No Stripe connection found");
		}

		// For connected accounts, we need to use the account's publishable key
		// This is different from the platform's publishable key
		// We'll use the platform's publishable key for now, as it should work with the client secret
		// In a production environment, you would need to implement a more secure way to handle this
		return config.stripe.publishableKey;
	}

	/**
	 * Creates a direct transfer from platform account to connected account
	 * This allows the platform to pay providers directly
	 */
	static async createDirectPlatformPayment({
		ownerId,
		amount,
		currency,
		metadata,
	}: {
		ownerId: string;
		amount: number;
		currency: string;
		metadata?: Record<string, string>;
	}) {
		// Get the provider's Stripe account
		const connection = await this.getStripeConnection(ownerId);
		if (!connection) {
			throw new Error("No Stripe connection found");
		}

		// Verify the connected account can receive transfers
		const account = await stripe.accounts.retrieve(connection.stripe_account_id);
		if (!account.payouts_enabled) {
			throw new Error("Provider account is not enabled for payouts");
		}

		// Create a transfer from platform account to connected account
		const transfer = await stripe.transfers.create({
			amount: amount,
			currency: currency,
			destination: connection.stripe_account_id,
			metadata,
		});

		return transfer;
	}

	/**
	 * Finds or creates a Stripe Connect Express account for the user
	 * @param {string} userId - The ID of the user to create the account for
	 * @returns {Promise<string>} The ID of the Stripe account
	 */
	static async findOrCreateConnectAccount(userId: string): Promise<string> {
		const user = await UserService.findById(userId);
		if (!user) throw new Error("User not found");
		if (user?.role !== "PROVIDER") throw new Error("User is not a provider");

		// Check if user already has a stripe connection
		const existingConnection = await prisma.stripeConnection.findUnique({
			where: { user_id: userId }
		});

		if (existingConnection) {
			return existingConnection.stripe_account_id;
		}

		// Get the user's listing to get the website and slug
		const listing = await prisma.listing.findFirst({
			where: { owner_id: userId }
		});

		const account = await stripe.accounts.create({
			type: "express",
			email: user.email,
			metadata: { userId },
			business_profile: {
				url: listing
					? `${process.env.NEXT_PUBLIC_APP_URL}/providers/${listing.slug}`
					: undefined
			}
		});

		// Create StripeConnection record
		await prisma.stripeConnection.create({
			data: {
				user_id: userId,
				stripe_account_id: account.id,
				stripe_account_status: "pending",
				payments_enabled: false,
				details_submitted: false,
				payouts_enabled: false,
				is_verified: false
			}
		});

		return account.id;
	}

	/**
	 * Gets the provider's listing ID or falls back to a default value
	 * @param userId - The provider's user ID
	 * @returns Promise<string> - The listing ID or fallback value
	 */
	static async getProviderListingId(userId: string): Promise<string> {
		const listing = await prisma.listing.findFirst({
			where: { owner_id: userId }
		});

		return listing?.id || userId;
	}
}

// Format amount for display
export function formatAmount(amount: number) {
	return new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: "USD"
	}).format(amount);
}
