import config from '@/config';
import prisma from '@/lib/prisma';
import axios from 'axios';

export class EmailNewsletterService {
    private static baseApiUrl = "https://rv-help.mailcoach.app/api";
    private static apiToken = config.mailcoach?.apiToken;

    // Mailcoach list UUIDs
    private static RV_OWNER_LIST_UUID = "1ec1df62-5e76-43d7-9840-3a897d097e5d";
    private static SERVICE_PROVIDER_LIST_UUID = "e0ff2e80-e881-4644-a38e-2e94bd88ccf8";

    static async syncNewsletterSubscriber({
        email,
        first_name,
        last_name,
        user,
        tags
    }: {
        email: string;
        first_name: string;
        last_name: string;
        user?: any;
        tags?: string[];
    }) {
        // Skip in development to avoid API calls
        if (config.isDevelopment) {
            console.log("Skipping newsletter sync in development");
            return true;
        }

        // Check if API token is available
        if (!this.apiToken) {
            console.error('MAILCOACH_TOKEN environment variable is not set. Newsletter sync will fail.');
            throw new Error('Newsletter service not configured - missing MAILCOACH_TOKEN');
        }

        // Normalize email to lowercase to avoid newsletter service validation errors
        const normalizedEmail = email.toLowerCase().trim();

        try {
            let existingTags: string[] = [];

            // First, try to get existing subscriber to preserve their tags
            try {
                const searchResponse = await axios.get(
                    `${this.baseApiUrl}/email-lists/${this.RV_OWNER_LIST_UUID}/subscribers?filter[search]=${encodeURIComponent(normalizedEmail)}`,
                    {
                        headers: {
                            accept: "application/json",
                            Authorization: `Bearer ${this.apiToken}`
                        }
                    }
                );

                if (searchResponse.data?.data?.[0]?.tags) {
                    existingTags = searchResponse.data.data[0].tags;
                    console.log(
                        `Found existing subscriber ${normalizedEmail} with ${existingTags.length} tags`
                    );
                }
            } catch (searchError) {
                // If search fails, continue with empty existing tags
                console.log(
                    `Could not fetch existing tags for ${normalizedEmail}, proceeding with new tags only`
                );
            }

            // Merge existing tags with new tags (avoid duplicates)
            const allTags = Array.from(new Set([...existingTags, ...(tags || [])]));

            const payload: any = {
                email: normalizedEmail,
                first_name,
                last_name,
                extra_attributes: user?.id ? [{ key: "user_id", value: user.id }] : [],
                append_tags: false // Use false and send complete tag list to ensure reliability
            };

            if (allTags.length > 0) {
                payload.tags = allTags;
            }

            // Send data to the Mailcoach API
            const response = await axios.post(
                `${this.baseApiUrl}/email-lists/${this.RV_OWNER_LIST_UUID}/subscribers`,
                payload,
                {
                    headers: {
                        accept: "application/json",
                        "content-type": "application/json",
                        Authorization: `Bearer ${this.apiToken}`
                    }
                }
            );

            // Update the user's newsletter_subscribed in the database if they exist
            if (user) {
                await prisma.user.update({
                    where: { id: user.id },
                    data: { newsletter_subscribed: true }
                });
            }

            console.log(
                `User ${normalizedEmail} synced successfully with ${allTags.length} tags.`,
                response.data
            );
            return true;
        } catch (error) {
            console.error(`Error syncing user ${normalizedEmail}:`, error);
            throw error;
        }
    }
}