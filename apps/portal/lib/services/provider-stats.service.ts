import { prisma } from "@/lib/prisma";
import { QuoteStatus } from "@rvhelp/database";
import { calculateDistance } from "@/lib/utils/distance";

export class ProviderStatsService {
  /**
   * Calculate and update stats for a specific provider
   */
  static async updateProviderStats(listingId: string) {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
    const seventyTwoHoursAgo = new Date(now.getTime() - 72 * 60 * 60 * 1000);

    // Get all quotes for this provider
    const quotes = await prisma.quote.findMany({
      where: { listing_id: listingId },
      select: {
        id: true,
        status: true,
        invited_at: true,
        responded_at: true,
        accepted_at: true,
        completed_at: true,
        reviewed_at: true,
        created_at: true,
        provider_notes: true,
        resolution_status: true,
      },
      orderBy: { created_at: "desc" },
    });

    // Calculate stats for each time period
    const stats30d = this.calculateStatsForPeriod(quotes, thirtyDaysAgo, seventyTwoHoursAgo);
    const stats90d = this.calculateStatsForPeriod(quotes, ninetyDaysAgo, seventyTwoHoursAgo);
    const statsAllTime = this.calculateStatsForPeriod(quotes, new Date(0), seventyTwoHoursAgo);

    // Update or create provider stats
    await prisma.providerStats.upsert({
      where: { listing_id: listingId },
      update: {
        // 30-day stats
        total_leads_30d: stats30d.totalLeads,
        responded_leads_30d: stats30d.respondedLeads,
        response_rate_30d: stats30d.responseRate,
        avg_response_time_30d: stats30d.avgResponseTime,
        accepted_jobs_30d: stats30d.acceptedJobs,
        completed_jobs_30d: stats30d.completedJobs,
        completion_rate_30d: stats30d.completionRate,
        avg_completion_time_30d: stats30d.avgCompletionTime,
        reviewed_jobs_30d: stats30d.reviewedJobs,
        review_completion_rate_30d: stats30d.reviewCompletionRate,
        non_response_count_30d: stats30d.nonResponseCount,
        non_response_rate_30d: stats30d.nonResponseRate,
        abandonment_count_30d: stats30d.abandonmentCount,
        abandonment_rate_30d: stats30d.abandonmentRate,

        // 90-day stats
        total_leads_90d: stats90d.totalLeads,
        responded_leads_90d: stats90d.respondedLeads,
        response_rate_90d: stats90d.responseRate,
        avg_response_time_90d: stats90d.avgResponseTime,
        accepted_jobs_90d: stats90d.acceptedJobs,
        completed_jobs_90d: stats90d.completedJobs,
        completion_rate_90d: stats90d.completionRate,
        avg_completion_time_90d: stats90d.avgCompletionTime,
        reviewed_jobs_90d: stats90d.reviewedJobs,
        review_completion_rate_90d: stats90d.reviewCompletionRate,
        non_response_count_90d: stats90d.nonResponseCount,
        non_response_rate_90d: stats90d.nonResponseRate,
        abandonment_count_90d: stats90d.abandonmentCount,
        abandonment_rate_90d: stats90d.abandonmentRate,

        // All-time stats
        total_leads_all_time: statsAllTime.totalLeads,
        responded_leads_all_time: statsAllTime.respondedLeads,
        response_rate_all_time: statsAllTime.responseRate,
        avg_response_time_all_time: statsAllTime.avgResponseTime,
        accepted_jobs_all_time: statsAllTime.acceptedJobs,
        completed_jobs_all_time: statsAllTime.completedJobs,
        completion_rate_all_time: statsAllTime.completionRate,
        avg_completion_time_all_time: statsAllTime.avgCompletionTime,
        reviewed_jobs_all_time: statsAllTime.reviewedJobs,
        review_completion_rate_all_time: statsAllTime.reviewCompletionRate,
        non_response_count_all_time: statsAllTime.nonResponseCount,
        non_response_rate_all_time: statsAllTime.nonResponseRate,
        abandonment_count_all_time: statsAllTime.abandonmentCount,
        abandonment_rate_all_time: statsAllTime.abandonmentRate,

        last_calculated_at: now,
      },
      create: {
        listing_id: listingId,
        // 30-day stats
        total_leads_30d: stats30d.totalLeads,
        responded_leads_30d: stats30d.respondedLeads,
        response_rate_30d: stats30d.responseRate,
        avg_response_time_30d: stats30d.avgResponseTime,
        accepted_jobs_30d: stats30d.acceptedJobs,
        completed_jobs_30d: stats30d.completedJobs,
        completion_rate_30d: stats30d.completionRate,
        avg_completion_time_30d: stats30d.avgCompletionTime,
        reviewed_jobs_30d: stats30d.reviewedJobs,
        review_completion_rate_30d: stats30d.reviewCompletionRate,
        non_response_count_30d: stats30d.nonResponseCount,
        non_response_rate_30d: stats30d.nonResponseRate,
        abandonment_count_30d: stats30d.abandonmentCount,
        abandonment_rate_30d: stats30d.abandonmentRate,

        // 90-day stats
        total_leads_90d: stats90d.totalLeads,
        responded_leads_90d: stats90d.respondedLeads,
        response_rate_90d: stats90d.responseRate,
        avg_response_time_90d: stats90d.avgResponseTime,
        accepted_jobs_90d: stats90d.acceptedJobs,
        completed_jobs_90d: stats90d.completedJobs,
        completion_rate_90d: stats90d.completionRate,
        avg_completion_time_90d: stats90d.avgCompletionTime,
        reviewed_jobs_90d: stats90d.reviewedJobs,
        review_completion_rate_90d: stats90d.reviewCompletionRate,
        non_response_count_90d: stats90d.nonResponseCount,
        non_response_rate_90d: stats90d.nonResponseRate,
        abandonment_count_90d: stats90d.abandonmentCount,
        abandonment_rate_90d: stats90d.abandonmentRate,

        // All-time stats
        total_leads_all_time: statsAllTime.totalLeads,
        responded_leads_all_time: statsAllTime.respondedLeads,
        response_rate_all_time: statsAllTime.responseRate,
        avg_response_time_all_time: statsAllTime.avgResponseTime,
        accepted_jobs_all_time: statsAllTime.acceptedJobs,
        completed_jobs_all_time: statsAllTime.completedJobs,
        completion_rate_all_time: statsAllTime.completionRate,
        avg_completion_time_all_time: statsAllTime.avgCompletionTime,
        reviewed_jobs_all_time: statsAllTime.reviewedJobs,
        review_completion_rate_all_time: statsAllTime.reviewCompletionRate,
        non_response_count_all_time: statsAllTime.nonResponseCount,
        non_response_rate_all_time: statsAllTime.nonResponseRate,
        abandonment_count_all_time: statsAllTime.abandonmentCount,
        abandonment_rate_all_time: statsAllTime.abandonmentRate,
      },
    });

    // Update is_highly_responsive flag based on 30-day metrics with minimum volume
    const isHighlyResponsive =
      stats30d.responseRate >= 80 &&
      stats30d.avgResponseTime <= 24 &&
      stats30d.totalLeads >= 2; // Minimum 2 leads for marketplace growth

    await prisma.listing.update({
      where: { id: listingId },
      data: { is_highly_responsive: isHighlyResponsive },
    });
  }

  /**
   * Calculate average distance for warranty jobs with assigned techs
   * This is a portal-level metric for warranty job analytics
   */
  static async calculateAverageWarrantyJobDistance(companyId?: string): Promise<{
    averageDistance: number;
    totalJobs: number;
    jobsWithDistance: number;
  }> {
    // Get all warranty jobs that have been assigned to a tech (have a listing_id)
    const warrantyJobs = await prisma.warrantyRequest.findMany({
      where: {
        listing_id: { not: null }, // Has assigned tech
        company_id: companyId, // Optional filter by company
      },
      include: {
        listing: {
          include: {
            locations: true, // Get provider locations
          },
        },
      },
    });

    if (warrantyJobs.length === 0) {
      return {
        averageDistance: 0,
        totalJobs: 0,
        jobsWithDistance: 0,
      };
    }

    let totalDistance = 0;
    let jobsWithDistance = 0;

    for (const job of warrantyJobs) {
      // Get job location from warranty request
      const jobLocation = job.location as any;
      if (!jobLocation?.latitude || !jobLocation?.longitude) {
        continue;
      }

      // Get provider's default location or first available location
      const providerLocation = job.listing?.locations?.find(loc => loc.default) || job.listing?.locations?.[0];
      if (!providerLocation?.latitude || !providerLocation?.longitude) {
        continue;
      }

      // Calculate distance using Haversine formula
      const distance = calculateDistance(
        jobLocation.latitude,
        jobLocation.longitude,
        providerLocation.latitude,
        providerLocation.longitude
      );

      totalDistance += distance;
      jobsWithDistance++;
    }

    const averageDistance = jobsWithDistance > 0 ? totalDistance / jobsWithDistance : 0;

    return {
      averageDistance: Math.round(averageDistance * 10) / 10, // Round to 1 decimal place
      totalJobs: warrantyJobs.length,
      jobsWithDistance,
    };
  }

  /**
   * Calculate all metrics for a specific time period
   */
  private static calculateStatsForPeriod(
    quotes: any[],
    periodStart: Date,
    seventyTwoHoursAgo: Date
  ) {
    // Filter quotes for this time period
    const quotesInPeriod = quotes.filter(q => q.invited_at >= periodStart);

    // Response metrics - consider a quote responded if status is not PENDING or EXPIRED
    const respondedQuotes = quotesInPeriod.filter(q =>
      q.status !== QuoteStatus.PENDING && q.status !== QuoteStatus.EXPIRED
    );

    const responseTimes = respondedQuotes
      .filter(q => q.invited_at && q.responded_at)
      .map(q => {
        const invitedAt = new Date(q.invited_at);
        const respondedAt = new Date(q.responded_at);
        return (respondedAt.getTime() - invitedAt.getTime()) / (1000 * 60 * 60); // hours
      });

    // Calculate accepted quotes and completion rates
    // Consider quotes as "accepted" if they have ACCEPTED status OR if they have provider_notes (indicating they responded)
    const acceptedQuotes = quotesInPeriod.filter(q =>
      q.status === QuoteStatus.ACCEPTED ||
      (q.provider_notes && q.provider_notes.trim().length > 0)
    );

    // Consider quotes as "completed" if they have completed_at timestamp
    const completedQuotes = quotesInPeriod.filter(q => q.completed_at !== null);

    const avgResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;

    // Completion rate should be: completed quotes / responded quotes
    const completionRate = respondedQuotes.length > 0
      ? (completedQuotes.length / respondedQuotes.length) * 100
      : 0;

    // Count quotes that have been accepted but not yet completed (active jobs)
    const activeJobs = quotesInPeriod.filter(q =>
      (q.status === QuoteStatus.ACCEPTED || (q.provider_notes && q.provider_notes.trim().length > 0)) &&
      q.completed_at === null
    ).length;

    // Review metrics
    const reviewedQuotes = quotesInPeriod.filter(q => q.reviewed_at);

    // Failure metrics
    const nonResponses = quotesInPeriod.filter(q =>
      q.status === QuoteStatus.PENDING && q.invited_at < seventyTwoHoursAgo
    );

    const abandonedJobs = quotesInPeriod.filter(q =>
      (q.status === QuoteStatus.ACCEPTED || (q.provider_notes && q.provider_notes.trim().length > 0)) &&
      q.completed_at === null
    );

    // Calculate completion times for completed quotes
    const completionTimes = completedQuotes
      .filter(q => q.accepted_at && q.completed_at)
      .map(q => {
        const acceptedAt = new Date(q.accepted_at);
        const completedAt = new Date(q.completed_at);
        return (completedAt.getTime() - acceptedAt.getTime()) / (1000 * 60 * 60); // hours
      });

    const avgCompletionTime = completionTimes.length > 0
      ? completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length
      : 0;

    const responseRate = quotesInPeriod.length > 0
      ? (respondedQuotes.length / quotesInPeriod.length) * 100
      : 0;

    const reviewCompletionRate = completedQuotes.length > 0
      ? (reviewedQuotes.length / completedQuotes.length) * 100
      : 0;

    const nonResponseRate = quotesInPeriod.length > 0
      ? (nonResponses.length / quotesInPeriod.length) * 100
      : 0;

    const abandonmentRate = acceptedQuotes.length > 0
      ? (abandonedJobs.length / acceptedQuotes.length) * 100
      : 0;

    return {
      totalLeads: quotesInPeriod.length,
      respondedLeads: respondedQuotes.length,
      responseRate,
      avgResponseTime,
      acceptedJobs: acceptedQuotes.length,
      completedJobs: completedQuotes.length,
      completionRate,
      avgCompletionTime,
      reviewedJobs: reviewedQuotes.length,
      reviewCompletionRate,
      nonResponseCount: nonResponses.length,
      nonResponseRate,
      abandonmentCount: abandonedJobs.length,
      abandonmentRate,
    };
  }

  /**
   * Update stats when a provider responds to a lead
   */
  static async onProviderResponse(listingId: string) {
    await this.updateProviderStats(listingId);
  }

  /**
   * Update stats when a new lead is received
   */
  static async onNewLead(listingId: string) {
    await this.updateProviderStats(listingId);
  }

  /**
   * Update stats when a job is accepted by customer
   */
  static async onJobAccepted(listingId: string) {
    await this.updateProviderStats(listingId);
  }

  /**
   * Update stats when a job is completed
   */
  static async onJobCompleted(listingId: string) {
    await this.updateProviderStats(listingId);
  }

  /**
   * Update stats when a review is submitted
   */
  static async onReviewSubmitted(listingId: string) {
    await this.updateProviderStats(listingId);
  }

  /**
   * Get provider stats for a listing
   */
  static async getProviderStats(listingId: string) {
    const stats = await prisma.providerStats.findUnique({
      where: { listing_id: listingId },
    });

    if (!stats) {
      // Create stats if they don't exist
      await this.updateProviderStats(listingId);
      return await prisma.providerStats.findUnique({
        where: { listing_id: listingId },
      });
    }

    return stats;
  }

  /**
   * Update all provider stats (for cron job)
   */
  static async updateAllProviderStats() {
    const listings = await prisma.listing.findMany({
      where: { is_active: true },
      select: { id: true },
    });

    for (const listing of listings) {
      try {
        await this.updateProviderStats(listing.id);
      } catch (error) {
        console.error(`Failed to update stats for listing ${listing.id}:`, error);
      }
    }
  }
} 