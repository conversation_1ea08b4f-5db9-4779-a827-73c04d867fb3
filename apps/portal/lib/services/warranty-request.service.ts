import prisma from '@/lib/prisma';
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { JobStatus, TimelineEventType, User, WarrantyRequestStatus } from '@rvhelp/database';
import { v4 as uuidv4 } from 'uuid';
import { slackService } from '../services';
import { emailService } from './email.service';
import { EmailNewsletterService } from './emailNewsletter.service';


export interface WarrantyRequestFilters {
    representativeId?: string | null;
    status?: string | null;
    component?: string | null;
    search?: string | null;
    rvVin?: string | null;
    rvModel?: string | null;
    page?: number;
    pageSize?: number;
}

export interface WarrantyRequestsResult {
    requests: any[];
    totalCount: number;
}

export interface CreateWarrantyRequestData {
    id: string;
    complaint: string;
    cause?: string;
    correction?: string;
    component_id?: string | null;
    notes_to_provider?: string | null;
    requires_return?: boolean;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    contact_preference?: 'sms' | 'phone';
    location?: {
        address: string;
        longitude?: number;
        latitude?: number;
    };
    attachments?: Array<{
        id?: string | null;
        title: string;
        type: string;
        url: string;
        required: boolean;
        component_id?: string | null;
        completed?: boolean;
    }>;
    rv_vin: string;
    rv_make: string;
    rv_model: string;
    rv_year: string;
    rv_type: string;
    approved_hours?: number;
}

export class WarrantyRequestService {
    /**
     * Helper function to get the next member number
     */
    private static async getNextMemberNumber(): Promise<number> {
        const count = await prisma.membership.count({
            where: { is_active: true }
        });
        return count + 1;
    }

    /**
     * Get warranty requests with filtering and pagination
     */
    static async getWarrantyRequests(
        companyId: string,
        userId: string,
        filters: WarrantyRequestFilters = {}
    ): Promise<WarrantyRequestsResult> {
        const {
            representativeId,
            status: statusParam,
            component: componentParam,
            search,
            rvVin: rvVinParam,
            rvModel: rvModelParam,
            page = 1,
            pageSize = 8
        } = filters;

        const skip = (page - 1) * pageSize;

        // Build the where clause for filtering
        const whereClause: any = { company_id: companyId };

        // Filter by representative if specified
        if (representativeId === 'current') {
            whereClause.oem_user_id = userId;
        } else if (representativeId && representativeId !== 'all') {
            whereClause.oem_user_id = representativeId;
        }

        // Filter by status if specified - handle multiple statuses
        if (statusParam) {
            try {
                const statusArray = JSON.parse(statusParam);
                if (Array.isArray(statusArray) && statusArray.length > 0) {
                    whereClause.status = { in: statusArray };
                }
            } catch (error) {
                // Fallback to single status for backward compatibility
                console.log('Error parsing status:', error);
                if (statusParam !== 'all') {
                    whereClause.status = statusParam;
                }
            }
        } else {
            // If no status specified, exclude JOB_CANCELLED requests
            whereClause.status = {
                not: 'JOB_CANCELLED'
            };
        }

        // Filter by component if specified - handle multiple components
        if (componentParam) {
            try {
                const componentArray = JSON.parse(componentParam);
                if (Array.isArray(componentArray) && componentArray.length > 0) {
                    whereClause.component_id = { in: componentArray };
                }
            } catch (error) {
                console.log('Error parsing component:', error);
                // Fallback to single component for backward compatibility
                if (componentParam !== 'all') {
                    whereClause.component_id = componentParam;
                }
            }
        }

        // Filter by last name if search term is provided
        if (search && search.trim()) {
            whereClause.last_name = {
                contains: search.trim(),
                mode: 'insensitive',
            };
        }

        // Filter by RV VIN if search term is provided
        if (rvVinParam && rvVinParam.trim()) {
            whereClause.rv_vin = {
                contains: rvVinParam.trim(),
                mode: 'insensitive',
            };
        }

        // Filter by RV model if search term is provided
        if (rvModelParam && rvModelParam.trim()) {
            whereClause.rv_model = {
                contains: rvModelParam.trim(),
                mode: 'insensitive',
            };
        }

        const [requests, totalCount] = await prisma.$transaction([
            prisma.warrantyRequest.findMany({
                where: whereClause,
                orderBy: { created_at: 'desc' },
                skip: skip,
                take: pageSize,
                include: {
                    oem_user: true,
                    listing: true,
                    component: true,
                    timeline_updates: {
                        orderBy: { date: 'desc' },
                        include: {
                            updated_by: {
                                select: {
                                    first_name: true,
                                    last_name: true,
                                },
                            },
                        },
                    },
                },
            }),
            prisma.warrantyRequest.count({
                where: whereClause,
            }),
        ]);

        return { requests, totalCount };
    }

    /**
     * Create or upgrade user for warranty request with membership handling
     */
    static async createOrUpgradeUser(data: CreateWarrantyRequestData, companyName?: string): Promise<string> {
        let existingCustomer = await prisma.user.findUnique({
            where: { email: data.email },
            include: { membership: true }
        });

        if (!existingCustomer) {
            // Create user for warranty request
            const newUser = await prisma.user.create({
                data: {
                    email: data.email,
                    first_name: data.first_name,
                    last_name: data.last_name,
                    rv_details: {
                        rv_make: data.rv_make,
                        rv_model: data.rv_model,
                        rv_year: data.rv_year,
                    },
                    role: "USER",
                    membership_level: "STANDARD", // Grant STANDARD membership immediately
                    newsletter_subscribed: true
                }
            });

            // Create membership record
            await prisma.membership.create({
                data: {
                    user_id: newUser.id,
                    level: "STANDARD",
                    member_number: await this.getNextMemberNumber(),
                    amount_paid: 0, // Free for warranty users
                    currency: "usd"
                }
            });

            // Refetch user with membership included
            existingCustomer = await prisma.user.findUnique({
                where: { id: newUser.id },
                include: { membership: true }
            });

            // Subscribe to newsletter with appropriate tags
            try {
                const tags = [
                    "consumer has account",
                    "source: added by oem"
                ];

                if (companyName) {
                    tags.push(`source: added by ${companyName}`);
                }

                await EmailNewsletterService.syncNewsletterSubscriber({
                    email: data.email,
                    first_name: data.first_name,
                    last_name: data.last_name,
                    user: newUser,
                    tags
                });

                console.log(`Created user, granted STANDARD membership, and subscribed to newsletter for warranty request: ${existingCustomer!.email}`);
            } catch (error) {
                console.error("Failed to sync new warranty user with newsletter:", error);
                // Don't fail user creation if newsletter fails
                console.log(`Created user and granted STANDARD membership for warranty request: ${existingCustomer!.email}`);
            }
        } else {
            // User exists - check if they need membership upgrade
            if (!existingCustomer.membership || existingCustomer.membership.level === "FREE") {
                // Update user's membership level
                const rvDetails = existingCustomer.rv_details as any;
                await prisma.user.update({
                    where: { id: existingCustomer.id },
                    data: {
                        membership_level: "STANDARD",
                        member_number: await this.getNextMemberNumber(),
                        rv_details: {
                            class: rvDetails?.class || data.rv_type,
                            make: rvDetails?.make || data.rv_make,
                            model: rvDetails?.model || data.rv_model,
                            year: rvDetails?.year || data.rv_year
                        }
                    }
                });

                if (!existingCustomer.membership) {
                    // Create new membership record
                    await prisma.membership.create({
                        data: {
                            user_id: existingCustomer.id,
                            level: "STANDARD",
                            member_number: await this.getNextMemberNumber(),
                            amount_paid: 0, // Free for warranty users
                            currency: "usd"
                        }
                    });
                } else {
                    // Update existing membership
                    await prisma.membership.update({
                        where: { user_id: existingCustomer.id },
                        data: {
                            level: "STANDARD",
                            is_active: true,
                            cancelled_at: null
                        }
                    });
                }

            }
        }

        return existingCustomer.id;
    }

    /**
     * Create a warranty request with all associated logic
     */
    static async createWarrantyRequest(
        data: CreateWarrantyRequestData,
        companyId: string,
        oemUserId: string,
        companyName?: string
    ): Promise<any> {
        const { id, component_id, attachments, ...otherFields } = data;

        // Create or upgrade user with membership handling
        const customerId = await this.createOrUpgradeUser(
            data,
            companyName
        );

        // Set RV make to company name if not provided
        if (!otherFields.rv_make && companyName) {
            otherFields.rv_make = companyName;
        }

        // Create the warranty request with the standard fields and metadata
        const request = await prisma.warrantyRequest.create({
            data: {
                ...otherFields,
                id: id,
                uuid: uuidv4(),
                status: 'REQUEST_APPROVED',
                company_id: companyId,
                oem_user_id: oemUserId,
                created_at: new Date(),
                customer_id: customerId,
                component_id: component_id,
                attachments: attachments,
            },
            include: {
                component: true,
                company: true,
            },
        });

        // Create initial timeline events for analytics tracking
        try {
            // Create PREAUTHORIZATION_APPROVED event (since status is REQUEST_APPROVED)
            await prisma.timelineUpdate.create({
                data: {
                    warranty_request_id: request.id,
                    updated_by_id: oemUserId,
                    event_type: 'PREAUTHORIZATION_APPROVED' as TimelineEventType,
                    details: {
                        notes: `Preauthorization approved for warranty request`,
                        approved_by: oemUserId
                    },
                    date: new Date(),
                },
            });

        } catch (timelineError) {
            console.error('Error creating timeline events:', timelineError);
            // Don't fail the warranty request creation if timeline events fail
        }


        // Send all notifications (email, SMS, Slack)
        await WarrantyRequestService.sendWarrantyRequestNotifications(
            request,
            request.company?.name
        );



        return request;
    }

    /**
     * Send notifications for warranty request (email, SMS, Slack)
     */
    static async sendWarrantyRequestNotifications(warrantyRequest: any, companyName?: string): Promise<void> {
        try {
            // Send email notification
            await emailService.sendWarrantyRequestEmail(warrantyRequest);

            // Send SMS notification if phone number is available
            if (warrantyRequest.phone) {
                console.log("[Disabled] Sending SMS notification to", warrantyRequest.phone);
                // const message = `Hi ${warrantyRequest.first_name}! Your ${companyName || 'RV'} warranty service request has been pre-authorized.\n\nClick here to schedule via RV Help: ${config.rvhelpUrl}/oem/${warrantyRequest.uuid}`;

                // await smsService.sendToUser(warrantyRequest.phone, message);
            }

            // Note: Slack notifications are handled in the API route for now

            // Update email_sent_at timestamp
            await prisma.warrantyRequest.update({
                where: { id: warrantyRequest.id },
                data: {
                    email_sent_at: new Date(),
                    ...(warrantyRequest.phone && { sms_sent_at: new Date() })
                },
            });

            try {
                await slackService.notifyNewWarrantyRequest(
                    warrantyRequest,
                    warrantyRequest.company?.name
                );
            } catch (slackError) {
                console.error('Failed to send Slack notification for warranty request:', slackError);
                // Don't fail the entire request if Slack notification fails
            }

        } catch (error) {
            console.error('Error sending warranty request notifications:', error);
            // Don't fail the warranty request creation if notifications fail
        }
    }

    static async cancelWarrantyRequest(requestId: string, user: User, notes: string): Promise<any> {
        const request = await prisma.warrantyRequest.findUnique({
            where: { id: requestId, company_id: user.company_id },
        });

        if (!request) {
            throw new Error('Warranty request not found');
        }

        // Update the warranty request status to JOB_CANCELLED
        const updatedWarrantyRequest = await prisma.warrantyRequest.update({
            where: { id: request.id, company_id: user.company_id },
            data: {
                status: WarrantyRequestStatus.JOB_CANCELLED
            },
        });

        if (updatedWarrantyRequest.job_id) {
            await prisma.job.update({
                where: { id: updatedWarrantyRequest.job_id },
                data: {
                    status: JobStatus.CANCELLED
                }
            });

            await QuoteStatusService.oemCancelJob({
                jobId: updatedWarrantyRequest.job_id,
                userId: updatedWarrantyRequest.customer_id,
                reason: notes
            });
        }

        // Create a timeline update record
        await prisma.timelineUpdate.create({
            data: {
                job_id: request.job_id,
                warranty_request_id: request.id,
                updated_by_id: user.id,
                event_type: 'JOB_CANCELLED' as TimelineEventType,
                details: { notes: notes },
                date: new Date(),
            },
        });

    }
}