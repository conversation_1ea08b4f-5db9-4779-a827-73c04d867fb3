import config from "../../config";

/**
 * Calculate the distance between two points on Earth using the Haversine formula
 * @param lat1 Latitude of first point in degrees
 * @param lon1 Longitude of first point in degrees
 * @param lat2 Latitude of second point in degrees
 * @param lon2 Longitude of second point in degrees
 * @returns Distance in miles
 */
export function calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
): number {
    const R = 6371; // Earth's radius in km
    const dLat = toRad(lat2 - lat1);
    const dLon = toRad(lon2 - lon1);
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(toRad(lat1)) *
        Math.cos(toRad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c; // Distance in km
    const miles = d * 0.621371; // Convert to miles
    return miles;
}

// Simple in-memory cache for distance calculations
const distanceCache = new Map<string, { distance: number; timestamp: number }>();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

function getCacheKey(origin: { lat: number; lng: number }, destination: { lat: number; lng: number }): string {
    // Round coordinates to 4 decimal places to group nearby locations
    const lat1 = Math.round(origin.lat * 10000) / 10000;
    const lng1 = Math.round(origin.lng * 10000) / 10000;
    const lat2 = Math.round(destination.lat * 10000) / 10000;
    const lng2 = Math.round(destination.lng * 10000) / 10000;
    return `${lat1},${lng1}-${lat2},${lng2}`;
}

/**
 * Get driving distance between two points using Google Maps API with caching
 * @param origin Origin coordinates
 * @param destination Destination coordinates
 * @returns Distance in miles
 */
export async function getDrivingDistance(
    origin: { lat: number; lng: number },
    destination: { lat: number; lng: number }
): Promise<number> {
    const cacheKey = getCacheKey(origin, destination);
    const now = Date.now();

    // Check cache first
    const cached = distanceCache.get(cacheKey);
    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
        return cached.distance;
    }

    try {
        // Check if we're on the server-side (has config.google.apiKey)
        if (typeof window === 'undefined' && config.google.apiKey) {
            // Server-side: call Google Maps API directly
            const response = await fetch(
                `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${origin.lat},${origin.lng}&destinations=${destination.lat},${destination.lng}&mode=driving&key=${config.google.apiKey}`
            );

            if (!response.ok) {
                throw new Error(`Google Maps API responded with status: ${response.status}`);
            }

            const data = await response.json();

            if (data.status !== "OK") {
                throw new Error(`Google Maps API error: ${data.status} - ${data.error_message || "Unknown error"}`);
            }

            if (!data.rows?.[0]?.elements?.[0]) {
                throw new Error("No distance data returned from Google Maps API");
            }

            const element = data.rows[0].elements[0];

            if (element.status !== "OK") {
                throw new Error(`Distance calculation failed: ${element.status}`);
            }

            const distanceInMeters = element.distance.value;
            const distanceInMiles = distanceInMeters * 0.000621371;

            // Cache the result
            distanceCache.set(cacheKey, {
                distance: distanceInMiles,
                timestamp: now
            });

            return distanceInMiles;
        } else {
            // Client-side: use our API endpoint
            const baseUrl = typeof window !== 'undefined' ? '' : (process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000');
            const response = await fetch(`${baseUrl}/api/distance`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    origin,
                    destination
                })
            });

            if (!response.ok) {
                throw new Error(`API responded with status: ${response.status}`);
            }

            const data = await response.json();

            if (data.error) {
                throw new Error(data.message || "Failed to calculate distance");
            }

            // Cache the result
            distanceCache.set(cacheKey, {
                distance: data.distance,
                timestamp: now
            });

            return data.distance;
        }
    } catch (error) {
        console.error("Error fetching driving distance:", error);
        // Fallback to Haversine calculation
        return calculateDistance(origin.lat, origin.lng, destination.lat, destination.lng);
    }
}

/**
 * Get distance with fallback - tries driving distance first, falls back to Haversine
 * This is useful for components that need to handle both sync and async scenarios
 * @param origin Origin coordinates
 * @param destination Destination coordinates
 * @param useDrivingDistance Whether to attempt driving distance calculation
 * @returns Promise<number> Distance in miles
 */
export async function getDistanceWithFallback(
    origin: { lat: number; lng: number },
    destination: { lat: number; lng: number },
    useDrivingDistance: boolean = true
): Promise<number> {
    if (useDrivingDistance) {
        try {
            return await getDrivingDistance(origin, destination);
        } catch (error) {
            console.warn("Driving distance failed, using Haversine fallback:", error);
            return calculateDistance(origin.lat, origin.lng, destination.lat, destination.lng);
        }
    }
    return calculateDistance(origin.lat, origin.lng, destination.lat, destination.lng);
}

/**
 * Convert degrees to radians
 * @param degrees Angle in degrees
 * @returns Angle in radians
 */
function toRad(degrees: number): number {
    return degrees * (Math.PI / 180);
}
