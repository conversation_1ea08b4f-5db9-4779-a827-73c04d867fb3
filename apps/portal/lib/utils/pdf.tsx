/// <reference types="node" />
import { WarrantyHistoryDocument } from "@/components/pdf/WarrantyHistoryDocument";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { pdf } from "@react-pdf/renderer";
import { Readable } from "stream";

export async function generateWarrantyHistoryPDF(
	request: ExtendedWarrantyRequest,
	company?: { name: string; brand_color?: string }
): Promise<Buffer> {
	const pdfDoc = (
		<WarrantyHistoryDocument request={request} company={company} />
	);
	const stream = await pdf(pdfDoc).toBuffer();

	// Convert ReadableStream to Node.js Readable stream
	const nodeStream = Readable.from(stream);

	// Convert stream to buffer
	return new Promise((resolve, reject) => {
		const chunks: Buffer[] = [];
		nodeStream.on("data", (chunk) => chunks.push(Buffer.from(chunk)));
		nodeStream.on("end", () => resolve(Buffer.concat(chunks)));
		nodeStream.on("error", reject);
	});
}
