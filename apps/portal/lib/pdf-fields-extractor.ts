import config from '@/config';
import { GetObjectCommand } from '@aws-sdk/client-s3';
import { PDFDocument } from 'pdf-lib';
import { s3 } from './s3';

export interface PDFFieldData {
    index: number;
    name: string;
    type: string;
    ref: string;
}

export async function extractPDFFields(s3Key: string): Promise<PDFFieldData[]> {
    try {
        // Download PDF from S3
        const command = new GetObjectCommand({
            Bucket: config.aws.bucket,
            Key: s3Key,
        });

        const response = await s3.send(command);

        if (!response.Body) {
            throw new Error('No file content received from S3');
        }

        // Convert stream to buffer
        const pdfBytes = await streamToBuffer(response.Body as any);

        // Load the PDF document
        const pdfDoc = await PDFDocument.load(pdfBytes);

        // Get the form from the document
        const form = pdfDoc.getForm();

        if (!form) {
            console.log('No form fields found in PDF');
            return [];
        }

        // Get all form fields
        const fields = form.getFields();

        if (fields.length === 0) {
            console.log('No form fields found in PDF');
            return [];
        }

        console.log(`Found ${fields.length} form field(s) in PDF: ${s3Key}`);

        // Extract field metadata
        const fieldData: PDFFieldData[] = [];

        for (let i = 0; i < fields.length; i++) {
            const field = fields[i];
            const fieldName = field.getName();
            const fieldType = field.constructor.name;
            const fieldRef = field.ref;

            fieldData.push({
                index: i,
                name: fieldName,
                type: fieldType,
                ref: fieldRef.toString(),
            });
        }

        // Sort fields numerically by the number at the beginning of field names
        fieldData.sort((a, b) => {
            const getLeadingNumber = (name: string): number => {
                const match = name.match(/^(\d+)/);
                return match ? parseInt(match[1], 10) : Infinity;
            };

            const numA = getLeadingNumber(a.name);
            const numB = getLeadingNumber(b.name);

            // If both have numbers, sort by number
            if (numA !== Infinity && numB !== Infinity) {
                return numA - numB;
            }

            // If only one has a number, number comes first
            if (numA !== Infinity) return -1;
            if (numB !== Infinity) return 1;

            // If neither has a number, sort alphabetically
            return a.name.localeCompare(b.name);
        });

        return fieldData;
    } catch (error) {
        console.error('Error extracting PDF fields:', error);
        throw new Error(`Failed to extract PDF fields: ${error.message}`);
    }
}

async function streamToBuffer(stream: any): Promise<Uint8Array> {
    const chunks: Uint8Array[] = [];

    for await (const chunk of stream) {
        chunks.push(chunk);
    }

    return new Uint8Array(Buffer.concat(chunks));
}
