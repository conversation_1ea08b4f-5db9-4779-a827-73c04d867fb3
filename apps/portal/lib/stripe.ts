import config from "@/config";
import Stripe from "stripe";

if (!config.stripe.secretKey) {
	throw new Error("STRIPE_SECRET_KEY is not defined in config");
}

export const stripe = new Stripe(config.stripe.secretKey, {
	apiVersion: "2025-02-24.acacia",
	typescript: true
});

export const formatStripeAmount = (amount: number) => {
	return Math.round(amount * 100); // Convert to cents
};

export const formatStripeError = (error: any) => {
	if (error instanceof Stripe.errors.StripeError) {
		return error.message;
	}
	return "An unexpected error occurred";
};
