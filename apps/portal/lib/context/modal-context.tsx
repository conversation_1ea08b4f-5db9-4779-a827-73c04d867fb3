import { ListingWithLocation } from '@/types/global';
import { createContext, useContext, useState } from 'react';

interface ModalContextType {
    openUnlockModal: () => void;
    closeUnlockModal: () => void;
    openUnlockFullModal: () => void;
    closeUnlockFullModal: () => void;
    isUnlockModalOpen: boolean;
    isUnlockFullModalOpen: boolean;
    openSendMessageModal: (
        listing: ListingWithLocation,
        options?: {
            title?: string;
            description?: string;
            forceInitialStep?: boolean;
            leadType?: 'message' | 'phone';
        }
    ) => void;
    closeSendMessageModal: () => void;
    isSendMessageModalOpen: boolean;
    selectedListing: ListingWithLocation | null;
    setSelectedListing: (listing: ListingWithLocation | null) => void;
    modalOptions: {
        title?: string;
        description?: string;
        forceInitialStep?: boolean;
        leadType?: 'message' | 'phone';
    } | null;
    setModalOptions: (
        options: {
            title?: string;
            description?: string;
            forceInitialStep?: boolean;
            leadType?: 'message' | 'phone';
        } | null
    ) => void;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

export function ModalProvider({ children }: { children: React.ReactNode }) {
    const [isUnlockModalOpen, setIsUnlockModalOpen] = useState(false);
    const [isUnlockFullModalOpen, setIsUnlockFullModalOpen] = useState(false);
    const [isSendMessageModalOpen, setIsSendMessageModalOpen] = useState(false);
    const [selectedListing, setSelectedListing] = useState<ListingWithLocation | null>(null);
    const [modalOptions, setModalOptions] = useState<{
        title?: string;
        description?: string;
        forceInitialStep?: boolean;
        leadType?: 'message' | 'phone';
    } | null>(null);
    const openUnlockModal = () => setIsUnlockModalOpen(true);
    const closeUnlockModal = () => setIsUnlockModalOpen(false);
    const openUnlockFullModal = () => setIsUnlockFullModalOpen(true);
    const closeUnlockFullModal = () => setIsUnlockFullModalOpen(false);
    const openSendMessageModal = (
        listing: ListingWithLocation,
        options?: {
            title?: string;
            description?: string;
            forceInitialStep?: boolean;
            leadType?: 'message' | 'phone';
        }
    ) => {
        setIsSendMessageModalOpen(true);
        setSelectedListing(listing);
        setModalOptions(options || null);
    };
    const closeSendMessageModal = () => {
        setIsSendMessageModalOpen(false);
        setModalOptions(null);
    };
    return (
        <ModalContext.Provider
            value={{
                openUnlockModal,
                closeUnlockModal,
                openUnlockFullModal,
                closeUnlockFullModal,
                isUnlockModalOpen,
                isUnlockFullModalOpen,
                openSendMessageModal,
                closeSendMessageModal,
                isSendMessageModalOpen,
                selectedListing,
                setSelectedListing,
                modalOptions,
                setModalOptions,
            }}
        >
            {children}
        </ModalContext.Provider>
    );
}

export const useModal = () => {
    const context = useContext(ModalContext);
    if (context === undefined) {
        throw new Error('useModal must be used within a ModalProvider');
    }
    return context;
};
