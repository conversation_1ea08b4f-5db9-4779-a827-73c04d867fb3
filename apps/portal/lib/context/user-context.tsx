'use client';

import { User } from '@/types/global';
import { useSession } from 'next-auth/react';
import { createContext, useCallback, useEffect, useState } from 'react';

interface UserContextType {
    user: User | null;
    loading: boolean;
    setUser: (user: User | null) => void;
    refreshUser: () => Promise<void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: React.ReactNode }) {
    const { data: session, status } = useSession();
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState(true);

    const fetchUser = useCallback(async () => {
        try {
            if (!session?.user?.email) {
                setUser(null);
                return;
            }

            const response = await fetch('/api/user');
            const userData = await response.json();
            setUser(userData);
        } catch (error) {
            console.error('Error fetching user:', error);
            setUser(null);
        } finally {
            setLoading(false);
        }
    }, [session?.user?.email]);

    useEffect(() => {
        if (status === 'loading') return;

        if (status === 'unauthenticated') {
            setLoading(false);
            setUser(null);
            return;
        }

        fetchUser();
    }, [status, fetchUser]);

    return (
        <UserContext.Provider
            value={{
                user,
                loading: status === 'loading' || loading,
                setUser: (newUser) => setUser(newUser),
                refreshUser: fetchUser,
            }}
        >
            {children}
        </UserContext.Provider>
    );
}

export default UserContext;
