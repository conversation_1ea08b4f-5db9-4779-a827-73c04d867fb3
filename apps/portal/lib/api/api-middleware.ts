import { User } from '@rvhelp/database';
import { NextRequest, NextResponse } from 'next/server';

type RouteHandler = (request: Request, user: User) => Promise<NextResponse>;

export function withAuthorization(handler: RouteHandler, requiredRole?: string) {
    return async (request: NextRequest, user: User) => {
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const userRole = user.role; // Assuming the role is stored in the token

        if (requiredRole && userRole !== requiredRole && userRole !== 'ADMIN') {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
        }

        return handler(request, user);
    };
}
