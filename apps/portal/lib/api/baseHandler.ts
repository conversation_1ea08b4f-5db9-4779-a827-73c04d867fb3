import prisma from '@/lib/prisma';
import { UserService } from '@/lib/services/user.service';
import { verify } from 'jsonwebtoken';
import { NextRequest, NextResponse } from 'next/server';
import { ZodSchema } from 'zod';

type HandlerContext = {
    user?: any;
    params?: any;
    query?: Record<string, string>;
    validatedData?: any;
    isAdmin?: boolean;
    req: NextRequest;
};

type HandlerFunction = (
    req: NextRequest,
    context: {
        validatedData?: any;
        session?: any;
        params?: any;
        query?: Record<string, string>;
    }
) => Promise<any>;

interface HandlerConfig {
    handler: HandlerFunction;
    requireAuth?: boolean;
    requiredRole?: "ADMIN" | "OEM" | "OEM_MANAGER";
    validateBody?: ZodSchema;
    validateQuery?: ZodSchema;
}

export interface HandlerOptions {
    requireAuth?: boolean;
    requiredRole?: string;
    validateBody?: ZodSchema;
    validateQuery?: ZodSchema;
    validateParams?: ZodSchema;
}

function formatZodErrors(errors: any[]): Record<string, string> {
    const fieldErrors: Record<string, string> = {};
    errors.forEach((err) => {
        const fieldName = err.path.join('.');
        fieldErrors[fieldName] = err.message;
    });
    return fieldErrors;
}

async function verifyJWTToken(token: string) {
    try {
        const tokenValue = token.replace('Bearer ', '');

        // Check if this is a cron secret (not a JWT)
        if (process.env.CRON_SECRET && tokenValue === process.env.CRON_SECRET) {
            // Return null for cron requests - they don't represent a user
            return null;
        }

        const decoded = verify(tokenValue, process.env.JWT_SECRET!);
        const userId = typeof decoded === 'object' ? decoded.userId : null;

        if (!userId) {
            return null;
        }

        const user = await prisma.user.findUnique({
            where: { id: userId },
        });

        return user;
    } catch (error) {
        // Only log JWT verification errors if it's not a cron secret
        const tokenValue = token.replace('Bearer ', '');
        if (!process.env.CRON_SECRET || tokenValue !== process.env.CRON_SECRET) {
            console.error('Token verification failed:', error);
        }
        return null;
    }
}

export function createHandler(config: HandlerConfig): HandlerFunction;
export function createHandler(
    handler: HandlerFunction,
    options?: Omit<HandlerConfig, 'handler'>
): HandlerFunction;
export function createHandler(
    configOrHandler: HandlerConfig | HandlerFunction,
    options: Omit<HandlerConfig, 'handler'> = {}
): HandlerFunction {
    const config =
        typeof configOrHandler === 'function'
            ? { handler: configOrHandler, ...options }
            : configOrHandler;

    return async function (req: NextRequest, routeContext: { params?: any } = {}) {
        try {
            const query = Object.fromEntries(req.nextUrl.searchParams);
            const context: HandlerContext = {
                query,
                params: routeContext.params,
                req,
            };

            const respond = (data: any, status: number = 200) => {
                return NextResponse.json(data, { status });
            };

            // Check for JWT token in Authorization header first
            const authHeader = req.headers.get('authorization');
            let user: any = null;

            if (authHeader?.startsWith('Bearer ')) {
                user = await verifyJWTToken(authHeader);
            } else {
                // Fall back to web session auth
                user = await UserService.user(req);
            }

            context.user = user;
            context.isAdmin = user?.role === 'ADMIN';

            if (config.requireAuth && !user) {
                return respond({ error: 'Unauthorized' }, 401);
            }

            // Add role check
            if (config.requiredRole && !user?.role?.startsWith(config.requiredRole) && user?.role !== "ADMIN") {
                return respond({ error: "Forbidden: Insufficient permissions" }, 403);
            }
            if (config.validateBody && req.method !== 'GET') {
                const body = await req.json().catch(() => ({}));
                try {
                    context.validatedData = config.validateBody.parse(body);
                } catch (error) {
                    return respond(
                        {
                            error: error.errors[0].message,
                            code: 'VALIDATION_ERROR',
                            details: {
                                message: error.errors[0].message,
                                errors: formatZodErrors(error.errors),
                            },
                        },
                        422
                    );
                }
            }

            if (config.validateQuery) {
                try {
                    context.validatedData = config.validateQuery.parse(query);
                } catch (error) {
                    return respond(
                        {
                            error: 'Validation failed',
                            code: 'VALIDATION_ERROR',
                            details: {
                                message: 'Please check your query parameters',
                                errors: formatZodErrors(error.errors),
                            },
                        },
                        422
                    );
                }
            }

            const handlerContext = {
                ...context,
                session: { user },
                respond,
            };

            // Call handler with both this context and passed parameters
            const result = await config.handler.call(handlerContext, req, {
                validatedData: context.validatedData,
                session: { user },
                params: routeContext.params,
                query,
            });

            return result;
        } catch (error) {
            console.error(error);
            return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
        }
    };
}
