const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const bcrypt = require("bcryptjs");

// dont do this in production
if (process.env.NODE_ENV === "production") {
	console.log("In production environment, skipping...", process.env.NODE_ENV);
	process.exit(0);
}

const users = [
	{
		email: "<EMAIL>",
		first_name: "<PERSON>",
		last_name: "<PERSON>",
		role: "ADMIN",
		password: "Css0urc3"
	},
	{
		email: "<EMAIL>",
		first_name: "<PERSON>",
		last_name: "<PERSON><PERSON><PERSON>",
		role: "ADMIN",
		password: "Css0urc3"
	},
	{
		email: "<EMAIL>",
		first_name: "<PERSON>",
		last_name: "<PERSON><PERSON><PERSON>",
		role: "ADMIN",
		password: "Css0urc3"
	}
];

async function main() {
	for (const user of users) {
		const hashedPassword = await bcrypt.hash(user.password, 10);
		await prisma.user.upsert({
			where: { email: user.email },
			update: {
				email: user.email,
				first_name: user.first_name,
				last_name: user.last_name,
				role: user.role,
				password: hashedPassword,
				email_verified_at: new Date(),
				notification_preferences: {
					create: {
						messages: { email: true, sms: false },
						requests: { email: true, sms: false },
						quotes: { email: true, sms: false }
					}
				}
			},
			create: {
				email: user.email,
				first_name: user.first_name,
				last_name: user.last_name,
				role: user.role,
				password: hashedPassword,
				email_verified_at: new Date(),
				notification_preferences: {
					create: {
						messages: { email: true, sms: false },
						requests: { email: true, sms: false },
						quotes: { email: true, sms: false }
					}
				}
			}
		});
	}

	console.log("Users created successfully");
}
main()
	.catch((e) => {
		console.error(e);
		process.exit(1);
	})
	.finally(async () => {
		await prisma.$disconnect();
	});
