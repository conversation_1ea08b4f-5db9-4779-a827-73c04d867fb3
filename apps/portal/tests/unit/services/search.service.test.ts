import { RVHelpVerificationLevel } from "@rvhelp/database";
import prisma from "../../../lib/prisma";
import { SearchService } from "../../../lib/services/search.service";

// Mock Prisma
jest.mock('../../../lib/prisma', () => ({
    listing: {
        findMany: jest.fn(),
        count: jest.fn(),
    },
    location: {
        findMany: jest.fn(),
    },
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe("SearchService", () => {
    const baseParams = {
        lat: "35.0",
        lng: "-85.0",
        filters: {}
    };
    beforeEach(() => {
        jest.clearAllMocks();
        (mockPrisma.listing.findMany as jest.Mock).mockResolvedValue([]);
        (mockPrisma.listing.count as jest.Mock).mockResolvedValue(0);
        (mockPrisma.location.findMany as jest.Mock).mockResolvedValue([]);
    });

    describe("buildWhereClause", () => {
        it("should include basic filters", () => {
            const params = {
                category: "rv-repair",
                filters: {}
            };

            const whereClause = SearchService.buildWhereClause(params);

            expect(whereClause.is_active).toBe(true);
            expect(whereClause.status).toBe("ACTIVE");
        });

        it("should handle verification filters", () => {
            const params = {
                category: "rv-repair",
                filters: {
                    verified: true
                }
            };

            const whereClause = SearchService.buildWhereClause(params);

            expect(whereClause.rv_help_verification_level).toEqual({
                not: RVHelpVerificationLevel.NONE
            });
        });

        it("should handle certification level filters for rv-repair", () => {
            const params = {
                category: "rv-repair",
                filters: {
                    certificationLevels: [1, 2, 3]
                }
            };

            const whereClause = SearchService.buildWhereClause(params);

            expect(whereClause.rvtaa_technician_level).toEqual({
                in: [1, 2, 3]
            });
        });

        it("should handle certification level filters for rv-inspection", () => {
            const params = {
                category: "rv-inspection",
                filters: {
                    certificationLevels: [1, 2, 3]
                }
            };

            const whereClause = SearchService.buildWhereClause(params);

            expect(whereClause.nrvia_inspector_level).toEqual({
                in: [1, 2, 3]
            });
        });

        it("should handle pro filter clauses", () => {
            const params = {
                category: "rv-repair",
                filters: {
                    showDiscountProviders: true,
                    showTroubleshootingProviders: true
                }
            };

            const whereClause = SearchService.buildWhereClause(params);

            expect(whereClause).toHaveProperty("AND");
            expect(whereClause.AND).toBeDefined();
            if (whereClause.AND) {
                expect(whereClause.AND).toHaveLength(2);
                expect(whereClause.AND[0]).toEqual({
                    OR: [{ discount_hourly_rate: true }, { discount_dispatch_fee: true }]
                });
                expect(whereClause.AND[1]).toEqual({
                    settings_virtual_diagnosis: true
                });
            }
        });
    });

});
