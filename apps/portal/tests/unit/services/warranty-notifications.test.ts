import { describe, expect, it } from '@jest/globals';
import { WarrantyRequestService } from '../../../lib/services/warranty-request.service';

describe('WarrantyRequestService - Notifications', () => {
    describe('sendWarrantyRequestNotifications', () => {
        it('should be a function that exists', () => {
            expect(typeof WarrantyRequestService.sendWarrantyRequestNotifications).toBe('function');
        });

        it('should be a static method on WarrantyRequestService', () => {
            expect(WarrantyRequestService.sendWarrantyRequestNotifications).toBeDefined();
            expect(typeof WarrantyRequestService.sendWarrantyRequestNotifications).toBe('function');
        });

        it('should accept warranty request and company name parameters', () => {
            const mockWarrantyRequest = {
                id: 'test-request-123',
                uuid: 'test-uuid-123',
                first_name: '<PERSON>',
                phone: '************',
                email: '<EMAIL>',
                company: {
                    name: 'Test Company'
                }
            };

            // Test that the function signature is correct
            expect(WarrantyRequestService.sendWarrantyRequestNotifications).toHaveLength(2);
        });
    });
});
