import { CustomerAuthorizationApprovedEmail } from '@/components/email-templates/customer/CustomerAuthorizationApprovedEmail';
import { describe, expect, it } from '@jest/globals';
import { renderAsync } from '@react-email/render';

describe('Warranty Authorization Email Templates', () => {
    const mockWarrantyRequest = {
        id: 'warranty-123',
        uuid: 'uuid-123',
        status: 'AUTHORIZATION_APPROVED',
        company_id: 'company-123',
        email: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        complaint: 'AC not working properly',
        cause: 'Compressor failure',
        correction: 'Replace compressor unit',
        rv_vin: '12345678901234567',
        rv_model: 'Test Model 2023',
        approved_hours: 2.5,
        estimated_hours: 3.0,
        job_id: 'job-123',
    };

    const mockCompany = {
        id: 'company-123',
        name: 'Test RV Company',
        brand_color: '#437F6B',
    };

    describe('CustomerAuthorizationApprovedEmail', () => {
        it('should render approval email with all required fields', async () => {
            const email = CustomerAuthorizationApprovedEmail({
                company: mockCompany,
                warrantyRequest: mockWarrantyRequest,
                rvhelpUrl: 'https://rvhelp.com',
                approvedHours: 3.0,
                updateNotes: 'Approved for additional diagnostic work',
            });

            const html = await renderAsync(email);

            expect(html).toContain('Your Warranty Authorization Has Been Approved');
            expect(html).toContain('John');
            expect(html).toContain('Test RV Company');
            expect(html).toContain('AC not working properly');
            expect(html).toContain('Approved for additional diagnostic work');
            expect(html).toContain('uuid-123');
            expect(html).toContain('https://rvhelp.com/oem/uuid-123');
        });

        it('should render approval email without optional fields', async () => {
            const email = CustomerAuthorizationApprovedEmail({
                company: mockCompany,
                warrantyRequest: {
                    ...mockWarrantyRequest,
                    cause: undefined,
                    correction: undefined,
                },
                rvhelpUrl: 'https://rvhelp.com',
            });

            const html = await renderAsync(email);

            expect(html).toContain('Your Warranty Authorization Has Been Approved');
            expect(html).toContain('John');
        });
    });
}); 