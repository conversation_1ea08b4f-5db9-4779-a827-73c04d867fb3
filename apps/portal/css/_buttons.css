.btn {
    @apply px-5 py-2 inline-flex items-center  rounded-lg text-white font-semibold;
}

.btn-primary {
    background-color: var(--primary-color);
}

.btn-primary-outline {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-sm {
    @apply px-3 py-1.5 text-sm;
}

.btn-lg {
    @apply px-6 py-4 text-lg;
}

.data-\[state\=checked\]\:bg-primary[data-state='checked'] {
    background-color: var(--primary-color);
}
