.carousel {
    max-width: 80rem;
    margin: 0 auto;
    padding: 0 40px 32px;
    position: relative;
}

.carousel__btn {
    position: absolute;
    top: 100%;
    transform: translateY(-120%);
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
    z-index: 10;
    transition: all;
}

.carousel__btn--left {
    right: 50px;
}

.carousel__btn--right {
    right: 0;
}

.carousel__btn--light {
    background: white;
    color: #888;
    opacity: 1;
}

.carousel__btn--light:hover {
    opacity: 0.8;
}

.carousel__btn--dark {
    right: 0;
}

.carousel__btn__icon {
    width: 24px;
    height: 24px;
}

.carousel__card {
    padding: 8px;
}

.slick-dots > li > button::before {
    font-size: 45px !important;
    font-family:
        ui-sans-serif,
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        <PERSON><PERSON>,
        'Helvetica Neue',
        <PERSON><PERSON>,
        'Noto Sans',
        sans-serif,
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji' !important;
}
.slick-dots > li.slick-active > button::before {
    font-size: 45px !important;
    color: hsl(0, 0%, 20%);
    font-family:
        ui-sans-serif,
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        'Helvetica Neue',
        Arial,
        'Noto Sans',
        sans-serif,
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji' !important;
}

.slick-track {
    display: flex;
    align-items: start;
}
