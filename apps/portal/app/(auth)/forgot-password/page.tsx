'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Link from 'next/link';
import { useState } from 'react';
import { toast } from 'react-hot-toast';

export default function ForgotPassword() {
    const [email, setEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [emailError, setEmailError] = useState('');

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setEmailError('');

        if (!email || !email.trim() || !email.includes('@')) {
            setEmailError('You must enter an email address to continue.');
            return;
        }

        setLoading(true);

        try {
            const response = await fetch('/api/auth/forgot-password', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email }),
            });

            const data = await response.json();

            if (response.ok) {
                toast.success(data.message);
                setEmail('');
            } else {
                toast.error(data.message || 'Failed to send reset instructions');
            }
        } catch {
            toast.error('An error occurred');
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="bg-white p-8 rounded-lg shadow-md w-96">
            <h1 className="text-2xl font-bold mb-6 text-center">Forgot Password</h1>
            <p className="text-gray-600 mb-6 text-center">
                Enter your email address and we&apos;ll send you instructions to reset your
                password.
            </p>
            <div className="mb-6">
                <Input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Email"
                    required
                    disabled={loading}
                    className={emailError ? 'border-red-500' : ''}
                />
                {emailError && <p className="text-red-500 text-sm mt-1">{emailError}</p>}
            </div>
            <Button type="submit" className="w-full mb-4" disabled={loading}>
                {loading ? 'Sending...' : 'Send Reset Link'}
            </Button>
            <p className="text-center text-sm">
                Remember your password?{' '}
                <Link href="/login" className="text-primary hover:underline">
                    Sign In
                </Link>
            </p>
        </form>
    );
}
