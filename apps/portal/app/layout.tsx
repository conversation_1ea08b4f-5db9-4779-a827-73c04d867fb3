import Header from "@/components/layout/Header";

import { CenteredPageLoader } from "@/components/Loader";
import LoadingBar from "@/components/LoadingBar";
import { Providers } from "@/components/Providers";
import "@/css/app.css";
import { Suspense } from "react";
import { Toaster } from "react-hot-toast";

export const viewport = {
	width: "device-width",
	initialScale: 1,
	maximumScale: 1,
	userScalable: false
};

export default function RootLayout({
	children
}: {
	children: React.ReactNode;
}) {
	return (
		<html lang="en" suppressHydrationWarning className="h-full">
			<body className="antialiased min-h-screen bg-gray-50">
				<Providers>
					<Suspense fallback={<CenteredPageLoader />}>
						<LoadingBar />
						<Header>{children}</Header>
						<Toaster />
					</Suspense>
				</Providers>
			</body>
		</html>
	);
}
