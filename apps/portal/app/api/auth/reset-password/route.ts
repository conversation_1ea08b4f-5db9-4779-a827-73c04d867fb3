import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { authService } from "@/lib/services/auth.service";
import { NextRequest, NextResponse } from "next/server";

export const POST = createHandler(async (request: NextRequest) => {
    try {
        const { token, password } = await request.json();

        if (!token || !password) {
            throw new Error("Invalid request");
        }

        await authService.resetPassword(token, password);
        console.log("Reset password route - completed successfully");

        return NextResponse.json(
            { message: "Password reset successfully" },
            { status: 200 }
        );
    } catch (error) {
        console.error("Reset password error:", error);
        return NextResponse.json(
            {
                message:
                    error instanceof Error ? error.message : "Error resetting password"
            },
            { status: 400 }
        );
    }
});
