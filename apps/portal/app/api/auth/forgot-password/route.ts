import { createHandler } from '@/lib/api/baseHandler';
import prisma from '@/lib/prisma';
import { authService } from '@/lib/services/auth.service';
import { NextResponse } from 'next/server';
import { z } from 'zod';

const validateBody = z.object({
    email: z.string().email('Please enter a valid email address'),
});

export const POST = createHandler(
    async function () {
        const { email } = this.validatedData;

        // Check if user exists / look for case insensitive match
        const user = await prisma.user.findFirst({
            where: { email: { equals: email, mode: 'insensitive' } },
        });

        if (!user) {
            return NextResponse.json({
                success: false,
                message: 'No account found with this email address.',
            });
        }

        await authService.forgotPassword(user, false);

        return NextResponse.json({
            success: true,
            message: 'Password reset instructions have been sent to your email address.',
        });
    },
    {
        validateBody,
        requireAuth: false,
    }
);
