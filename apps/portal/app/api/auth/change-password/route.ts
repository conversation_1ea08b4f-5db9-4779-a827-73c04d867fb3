import { createHandler } from '@/lib/api/baseHandler';
import { authService } from '@/lib/services/auth.service';
import { z } from 'zod';

const validateBody = z.object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z.string().min(8, 'Password must be at least 8 characters'),
});

export const PUT = createHandler(
    async function () {
        const { currentPassword, newPassword } = this.validatedData;

        try {
            await authService.changePassword(this.user.id, currentPassword, newPassword);

            return this.respond({ message: 'Password changed successfully' });
        } catch (error) {
            return this.respond(
                { error: 'Failed to change password', message: error.message },
                400
            );
        }
    },
    {
        requireAuth: true,
        validateBody,
    }
);
