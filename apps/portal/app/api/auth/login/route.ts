import { createHandler } from '@/lib/api/baseHandler';
import prisma from '@/lib/prisma';
import { NextResponse } from 'next/server';

export const POST = createHandler(
    async function () {
        if (!this.user) {
            return new NextResponse('Unauthorized', { status: 401 });
        }
        await prisma.user.update({
            where: { id: this.user.id },
            data: { last_login: new Date() },
        });

        return NextResponse.json({ success: true });
    },
    { requireAuth: true }
);
