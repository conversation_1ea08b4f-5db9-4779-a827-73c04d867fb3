import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { endOfDay, startOfDay, subWeeks } from "date-fns";

export const POST = createHandler(
    async function () {
        try {
            console.log("🔧 [Platform Fee Reports Generate API] Generating current period report...");

            // Calculate current 2-week period
            const now = new Date();
            const twoWeeksAgo = subWeeks(now, 2);
            const periodStart = startOfDay(twoWeeksAgo);
            const periodEnd = endOfDay(now);

            console.log(`🔧 [Platform Fee Reports Generate API] Period: ${periodStart.toISOString()} to ${periodEnd.toISOString()}`);

            // Fetch provider invoices for this period
            const providerInvoices = await prisma.invoice.findMany({
                where: {
                    status: "PAID",
                    paid_at: {
                        gte: periodStart,
                        lte: periodEnd
                    }
                },
                include: {
                    provider: {
                        select: {
                            business_name: true
                        }
                    },
                    warranty_provider_request: {
                        select: {
                            rv_vin: true
                        }
                    }
                },
                orderBy: {
                    paid_at: "desc"
                }
            });

            console.log(`🔧 [Platform Fee Reports Generate API] Found ${providerInvoices.length} provider invoices`);

            const totalAmount = providerInvoices.reduce((sum, invoice) => sum + invoice.amount, 0);
            const platformFeeTotal = providerInvoices.length * 5000; // $50 per invoice

            const report = {
                id: `${periodStart.getTime()}-${periodEnd.getTime()}`,
                period_start: periodStart.toISOString(),
                period_end: periodEnd.toISOString(),
                provider_invoices: providerInvoices,
                total_amount: totalAmount,
                platform_fee_total: platformFeeTotal,
                created_at: new Date().toISOString()
            };

            console.log(`🔧 [Platform Fee Reports Generate API] Generated report with ${providerInvoices.length} invoices, total platform fees: $${platformFeeTotal / 100}`);

            return this.respond({
                success: true,
                report
            });
        } catch (error) {
            console.error("Error generating platform fee report:", error);
            return this.respond({
                success: false,
                error: error instanceof Error ? error.message : "Failed to generate platform fee report"
            }, 500);
        }
    },
    {
        requiredRole: "OEM"
    }
);
