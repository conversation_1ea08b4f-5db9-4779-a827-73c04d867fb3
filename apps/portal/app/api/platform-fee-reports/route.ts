import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { endOfDay, startOfDay } from "date-fns";

export const GET = createHandler(
    async function () {
        try {
            console.log("🔧 [Platform Fee Reports API] Fetching platform fee reports...");

            // Calculate 2-week periods starting from Aug 4, 2025
            const startDate = new Date("2025-08-04");
            const now = new Date();
            const reports = [];

            // Generate reports for each 2-week period
            let currentStart = startDate;
            while (currentStart < now) {
                const currentEnd = new Date(currentStart);
                currentEnd.setDate(currentEnd.getDate() + 13); // 2 weeks - 1 day

                if (currentEnd > now) {
                    currentEnd.setTime(now.getTime());
                }

                // Fetch provider invoices for this period
                const providerInvoices = await prisma.invoice.findMany({
                    where: {
                        status: "PAID",
                        paid_at: {
                            gte: startOfDay(currentStart),
                            lte: endOfDay(currentEnd)
                        }
                    },
                    include: {
                        provider: {
                            select: {
                                business_name: true
                            }
                        },
                        warranty_provider_request: {
                            select: {
                                rv_vin: true
                            }
                        }
                    },
                    orderBy: {
                        paid_at: "desc"
                    }
                });

                if (providerInvoices.length > 0) {
                    const totalAmount = providerInvoices.reduce((sum, invoice) => sum + invoice.amount, 0);
                    const platformFeeTotal = providerInvoices.length * 5000; // $50 per invoice

                    reports.push({
                        id: `${currentStart.getTime()}-${currentEnd.getTime()}`,
                        period_start: currentStart.toISOString(),
                        period_end: currentEnd.toISOString(),
                        provider_invoices: providerInvoices,
                        total_amount: totalAmount,
                        platform_fee_total: platformFeeTotal,
                        created_at: new Date().toISOString()
                    });
                }

                // Move to next 2-week period
                currentStart = new Date(currentEnd);
                currentStart.setDate(currentStart.getDate() + 1);
            }

            console.log(`🔧 [Platform Fee Reports API] Found ${reports.length} reports`);

            return this.respond({
                success: true,
                reports: reports.reverse() // Most recent first
            });
        } catch (error) {
            console.error("Error fetching platform fee reports:", error);
            return this.respond({
                success: false,
                error: error instanceof Error ? error.message : "Failed to fetch platform fee reports"
            }, 500);
        }
    },
    {
        requiredRole: "OEM"
    }
);
