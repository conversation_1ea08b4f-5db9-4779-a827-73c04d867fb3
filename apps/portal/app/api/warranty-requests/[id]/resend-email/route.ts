import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { WarrantyRequestService } from "@/lib/services/warranty-request.service";
import { ExtendedWarrantyRequest } from "@/types/warranty";

export const POST = createHandler(
    async function () {
        const { id } = this.params;

        try {
            // Get the warranty request with company and component info
            const warrantyRequest = await prisma.warrantyRequest.findUnique({
                where: { id },
                include: {
                    company: true,
                    component: true,
                },
            });

            if (!warrantyRequest) {
                return this.respond({
                    success: false,
                    error: "Warranty request not found"
                }, 404);
            }

            // Check if user has permission to resend emails for this company
            if (this.user?.company_id !== warrantyRequest.company_id) {
                return this.respond({
                    success: false,
                    error: "Unauthorized to resend emails for this warranty request"
                }, 403);
            }

            // Send all notifications (email, SMS)
            await WarrantyRequestService.sendWarrantyRequestNotifications(
                warrantyRequest as ExtendedWarrantyRequest,
                warrantyRequest.company?.name
            );

            return this.respond({
                success: true,
                message: "Warranty request notifications resent successfully"
            });

        } catch (error) {
            console.error('Error resending warranty request notifications:', error);
            return this.respond({
                success: false,
                error: "Failed to resend warranty request notifications"
            }, 500);
        }
    },
    {
        requiredRole: "OEM",
        requireAuth: true
    }
);
