import { createHandler } from '@/lib/api/baseHandler';
import prisma from '@/lib/prisma';
import { generateWarrantyHistoryPDF } from '@/lib/utils/pdf';
import { NextResponse } from 'next/server';

export const GET = createHandler(
    async function (req, { params, session }) {
        const { id } = params;

        try {
            // Get the warranty request with all related data
            const request = await prisma.warrantyRequest.findFirst({
                where: { id, company_id: session.user.company_id },
                include: {
                    company: {
                        select: { name: true, brand_color: true }
                    },
                    timeline_updates: {
                        orderBy: { date: 'asc' },
                        include: {
                            updated_by: {
                                select: { first_name: true, last_name: true }
                            }
                        }
                    }
                }
            });
            if (!request) {
                return new NextResponse("Warranty request not found", { status: 404 });
            }

            // Check if user has permission to access this request
            // This depends on your authorization logic - adjust as needed
            const userCompanyId = session.user.company_id;
            if (request.company_id !== userCompanyId && session.user.role !== 'ADMIN') {
                return new NextResponse("Unauthorized", { status: 403 });
            }

            // Get company information for the PDF
            const company = request.company ? {
                name: request.company.name || 'Unknown Company',
                brand_color: request.company.brand_color
            } : undefined;

            // Generate the PDF
            const pdfBuffer = await generateWarrantyHistoryPDF(request as any, company);

            // Create a filename
            const filename = `warranty-history-${request.id}.pdf`;

            // Return the PDF with appropriate headers
            return new NextResponse(new Uint8Array(pdfBuffer), {
                status: 200,
                headers: {
                    "Content-Type": "application/pdf",
                    "Content-Disposition": `attachment; filename="${filename}"`,
                    "Cache-Control": "public, max-age=3600" // Cache for 1 hour
                }
            });
        } catch (error) {
            console.error("Error generating warranty history PDF:", error);
            return new NextResponse("Failed to generate PDF", { status: 500 });
        }
    },
    {
        requireAuth: true
    }
);