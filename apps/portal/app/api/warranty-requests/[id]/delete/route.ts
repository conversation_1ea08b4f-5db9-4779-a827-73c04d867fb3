import { createH<PERSON><PERSON> } from '@/lib/api/baseHandler';
import prisma from '@/lib/prisma';

export const DELETE = createHandler(
    async function (req, { params }) {
        const { id } = params;

        // Check if user is admin
        if (this.user?.role !== 'ADMIN') {
            return this.respond({ error: 'Unauthorized - Admin access required' }, 403);
        }

        // Find the warranty request
        const warrantyRequest = await prisma.warrantyRequest.findUnique({
            where: { id },
            include: {
                oem_user: true,
                timeline_updates: true,
                job: {
                    include: {
                        quotes: true
                    }
                },
                provider_invoice: true,
                platform_invoice: true,
            }
        });

        if (!warrantyRequest) {
            return this.respond({ error: 'Warranty request not found' }, 404);
        }

        // Check if the user who created the request is an admin
        if (warrantyRequest.oem_user?.role !== 'ADMIN') {
            return this.respond({ error: 'Can only delete warranty requests created by admin users' }, 403);
        }

        try {
            // Delete in a transaction to ensure all related data is removed
            await prisma.$transaction(async (tx) => {
                // Delete timeline updates
                await tx.timelineUpdate.deleteMany({
                    where: { warranty_request_id: id }
                });

                // Delete the job if it exists
                if (warrantyRequest.job) {
                    // First delete all quote messages for all quotes of this job
                    for (const quote of warrantyRequest.job.quotes) {
                        await tx.quoteMessage.deleteMany({
                            where: { quote_id: quote.id }
                        });
                    }

                    // Then delete all quotes for this job
                    await tx.quote.deleteMany({
                        where: { job_id: warrantyRequest.job.id }
                    });

                    // Finally delete the job
                    await tx.job.delete({
                        where: { id: warrantyRequest.job.id }
                    });
                }

                // Delete the warranty request (this will cascade to related records)
                await tx.warrantyRequest.delete({
                    where: { id }
                });
            });

            return this.respond({
                message: 'Warranty request and all related data deleted successfully',
                deletedId: id
            }, 200);

        } catch (error) {
            console.error('Error deleting warranty request:', error);
            return this.respond({
                error: 'Failed to delete warranty request. Please try again.'
            }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: 'ADMIN'
    }
);
