import { createHandler } from '@/lib/api/baseHandler';
import { WarrantyRequestService } from '@/lib/services/warranty-request.service';
import { z } from 'zod';

const cancelJobSchema = z.object({
    update_notes: z.string().min(1, 'Reason is required'),
});

export const POST = createHandler(
    async function () {
        if (!this?.user?.email || !this?.user?.company_id) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract ID from URL: /api/warranty-requests/[id]/cancel-job
        const urlParts = this.req.url.split('/');
        const id = urlParts[urlParts.length - 2]; // Get the ID from the second-to-last part

        const { update_notes } = this.validatedData;

        try {
            const updatedWarrantyRequest = await WarrantyRequestService.cancelWarrantyRequest(id, this.user, update_notes);

            return Response.json({
                success: true,
                warrantyRequest: updatedWarrantyRequest,
                message: 'Warranty request marked as resolved successfully'
            });
        } catch (error) {
            console.error('Error marking warranty request as resolved:', error);
            return Response.json({ error: 'Failed to mark warranty request as resolved' }, { status: 500 });
        }
    },
    {
        validateBody: cancelJobSchema,
        requiredRole: "OEM"
    }
);
