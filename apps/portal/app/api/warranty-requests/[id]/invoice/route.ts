import { createHandler } from "@/lib/api/baseHandler";
import { invoiceService } from "@/lib/services";

export const GET = createHandler(
	async function () {
		const { id } = this.params;
		console.log("🔧 [Warranty Invoice API] Looking for invoice for warranty request:", id);

		const invoice = await invoiceService.getProviderInvoiceByWarrantyRequestId(id);

		if (!invoice) {
			console.log("🔧 [Warranty Invoice API] No invoice found for warranty request:", id);
			return Response.json({ error: "Invoice not found" }, { status: 404 });
		}

		console.log("🔧 [Warranty Invoice API] Found invoice:", invoice.id, invoice.invoice_number);
		return Response.json({ invoice });
	},
	{
		requiredRole: "OEM"
	}
);