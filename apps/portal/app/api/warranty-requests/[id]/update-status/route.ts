import { createHandler } from '@/lib/api/baseHandler';
import prisma from '@/lib/prisma';
import { ExtendedWarrantyRequest } from '@/types/warranty'; // Import the extended type
import { TimelineEventType, WarrantyRequestStatus } from '@rvhelp/database'; // Import enum
import { z } from 'zod';

// Match the expanded schema from the form
const putWarrantyRequestSchema = z.object({
    event_type: z
        .enum([
            'PREAUTHORIZATION_APPROVED',
            'PREAUTHORIZATION_REJECTED',
            'AUTHORIZATION_APPROVED',
            'AUTHORIZATION_REJECTED',
            'PARTS_ORDERED',
            'INVOICE_PAID',
        ])
        .optional(),
    update_notes: z.string().optional(), // Optional field for update notes
});

export const PUT = createHandler(
    async function () {
        if (!this?.user?.email || !this?.user?.company_id) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract ID from URL: /api/warranty-requests/[id]
        const urlParts = this.req.url.split('/');
        const id = urlParts[urlParts.length - 2];

        const { event_type, update_notes } = this.validatedData;

        let status: WarrantyRequestStatus;
        if (event_type) {
            switch (event_type) {
                case 'PREAUTHORIZATION_APPROVED':
                    status = 'REQUEST_APPROVED';
                    break;
                case 'PREAUTHORIZATION_REJECTED':
                    status = 'REQUEST_REJECTED';
                    break;
                case 'AUTHORIZATION_APPROVED':
                    status = 'AUTHORIZATION_APPROVED';
                    break;
                case 'AUTHORIZATION_REJECTED':
                    status = 'AUTHORIZATION_REJECTED';
                    break;
                case 'PARTS_ORDERED':
                    status = 'PARTS_ORDERED';
                    break;
                case 'INVOICE_PAID':
                    status = 'INVOICE_PAID';
                    break;
            }
        }

        try {
            const existing = (await prisma.warrantyRequest.findFirst({
                where: { id },
            })) as ExtendedWarrantyRequest;
            if (!existing) {
                return Response.json({ error: 'Warranty request not found' }, { status: 404 });
            }


            const updatedWarrantyRequest = await prisma.warrantyRequest.update({
                where: { id: existing.id },
                data: {
                    ...(status && { status: status as WarrantyRequestStatus }), // Cast status to enum
                },
            });

            // Create a WarrantyRequestUpdate record if status was updated
            if (status && event_type) {
                await prisma.timelineUpdate.create({
                    data: {
                        job_id: existing.job_id,
                        warranty_request_id: existing.id,
                        updated_by_id: this.user.id, // ID of the user making the update
                        event_type: event_type as TimelineEventType, // The new status
                        details: update_notes ? { notes: update_notes } : undefined, // Store notes if provided
                        date: new Date(),
                    },
                });
            }

            return Response.json(updatedWarrantyRequest);
        } catch (error) {
            console.error('Error updating warranty request status:', error);
            return Response.json({ error: 'Failed to update warranty request status' }, { status: 500 });
        }
    },
    {
        validateBody: putWarrantyRequestSchema,
        requiredRole: "OEM"
    }
);
