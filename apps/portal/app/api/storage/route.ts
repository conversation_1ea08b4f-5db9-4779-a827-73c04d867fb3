import config from '@/config';
import { createHandler } from '@/lib/api/baseHandler';
import { s3 } from '@/lib/s3';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { NextRequest, NextResponse } from 'next/server';

export const POST = createHandler(
    async function (req: NextRequest) {
        try {
            const formData = await req.formData();
            const file = formData.get('file') as File;
            const fileName = formData.get('fileName') as string;
            const path = formData.get('path') as string;

            if (!file || !fileName || !path) {
                return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
            }

            const buffer = Buffer.from(await file.arrayBuffer());
            await s3.send(
                new PutObjectCommand({
                    Bucket: config.aws.bucket,
                    Key: `${path}/${fileName}`,
                    Body: buffer,
                    ContentType: file.type,
                    ACL: 'public-read',
                })
            );

            return NextResponse.json({
                url: `https://${config.aws.bucket}.s3.${config.aws.region}.amazonaws.com/${path}/${fileName}`,
                key: `${path}/${fileName}`,
            });
        } catch (error) {
            console.error('Error uploading file:', error);
            return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 });
        }
    }, {
    requiredRole: "OEM"
});
