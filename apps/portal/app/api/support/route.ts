import { SupportRequestEmail } from "@/components/email-templates/SupportRequestEmail";
import { createHandler } from "@/lib/api/baseHandler";
import { emailService } from "@/lib/services";
import { z } from "zod";

const schema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    email: z.string().email("Please enter a valid email address"),
    description: z.string().min(10, "Please provide a detailed description"),
    type: z.enum(["bug", "feature"]),
    companyName: z.string().min(1, "Company name is required")
});

export const POST = createHandler(
    async function () {
        const { firstName, lastName, email, description, type, companyName } = this.validatedData;

        try {
            const subject = type === "bug"
                ? `${companyName} portal bug report`
                : `${companyName} portal feature request`;

            await emailService.send({
                from: "RVHelp Portal <<EMAIL>>",
                to: "<EMAIL>",
                cc: email,
                subject,
                text: `
New ${type === "bug" ? "bug report" : "feature request"} from ${companyName} Portal

From: ${firstName} ${lastName}
Email: ${email}
Company: ${companyName}
Type: ${type === "bug" ? "Bug Report" : "Feature Request"}

Description:
${description}
				`,
                react: SupportRequestEmail({
                    firstName,
                    lastName,
                    email,
                    description,
                    type,
                    companyName
                })
            });

            return this.respond({ success: true });
        } catch (error) {
            console.error("Failed to send support request:", error);
            return this.respond({ success: false, error: error.message }, 500);
        }
    },
    {
        validateBody: schema
    }
);