export const dynamic = 'force-dynamic';

import { createHandler } from '@/lib/api/baseHandler';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { NextResponse } from 'next/server';

export const GET = createHandler(
    async function () {
        try {
            const session = await getServerSession(authOptions);

            if (!session?.user?.email) {
                return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            // Get the current user with their company_id
            const user = await prisma.user.findFirst({
                where: { email: session.user.email },
                select: { id: true, company_id: true, role: true },
            });

            if (!user?.company_id) {
                return NextResponse.json(
                    { error: 'User not associated with a company' },
                    { status: 400 }
                );
            }

            // Get company data
            const company = await prisma.company.findUnique({
                where: { id: user.company_id },
                include: {
                    components: true,
                },
            });

            if (!company) {
                return NextResponse.json({ error: 'Company not found' }, { status: 404 });
            }

            // Get warranty requests for the company
            const warrantyRequests = await prisma.warrantyRequest.findMany({
                where: {
                    company_id: user.company_id,
                },
                include: {
                    component: {
                        select: {
                            type: true,
                            manufacturer: true,
                        },
                    },
                    timeline_updates: {
                        include: {
                            updated_by: {
                                select: {
                                    id: true,
                                    first_name: true,
                                    last_name: true,
                                },
                            },
                        },
                    },
                    oem_user: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            role: true,
                        },
                    },
                    listing: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            business_name: true,
                            phone: true,
                            email: true,
                            city: true,
                            pricing_settings: true,
                        }
                    }
                },
                orderBy: {
                    created_at: 'desc',
                },
            });

            // Calculate statistics
            const now = new Date();
            const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

            const pendingStatuses = ["AUTHORIZATION_REQUESTED"];
            if (user.role === "ADMIN") {
                pendingStatuses.push("INVOICE_CREATED");
            }

            const stats = {
                totalRequests: warrantyRequests.length,
                pendingRequests: warrantyRequests.filter((req) =>
                    pendingStatuses.includes(req.status)
                ).length,
                last30Days: warrantyRequests.filter(
                    (req) => new Date(req.created_at) >= thirtyDaysAgo
                ).length,
                completedRequests: warrantyRequests.filter((req) =>
                    ['INVOICE_PAID'].includes(req.status)
                ).length,
                averageResolutionTime: 0, // Could be calculated if needed
                requestsByStatus: warrantyRequests.reduce(
                    (acc, req) => {
                        acc[req.status] = (acc[req.status] || 0) + 1;
                        return acc;
                    },
                    {} as { [key: string]: number }
                ),
            };

            // Get pending requests (limit to 5 for dashboard)
            const pendingRequests = warrantyRequests
                .filter((req) =>
                    pendingStatuses.includes(
                        req.status
                    )
                )
                .slice(0, 5);

            // Get component breakdown
            const componentBreakdown = await prisma.warrantyRequest.groupBy({
                by: ['component_id'],
                where: {
                    company_id: user.company_id,
                    component_id: { not: null },
                },
                _count: {
                    id: true,
                },
                orderBy: {
                    _count: {
                        id: 'desc',
                    },
                },
            });

            // Get component details for the breakdown
            const componentIds = componentBreakdown.map((cb) => cb.component_id).filter(Boolean);
            const components = await prisma.component.findMany({
                where: {
                    id: { in: componentIds },
                },
                select: {
                    id: true,
                    type: true,
                    manufacturer: true,
                },
            });

            // Combine component data with counts
            const componentStats = componentBreakdown.map((cb) => {
                const component = components.find((c) => c.id === cb.component_id);
                return {
                    component_id: cb.component_id,
                    type: component?.type || 'Unknown',
                    manufacturer: component?.manufacturer || 'Unknown',
                    count: cb._count.id,
                };
            });

            return NextResponse.json({
                stats,
                pendingRequests,
                company,
                componentBreakdown: componentStats,
            });
        } catch (error) {
            console.error('Dashboard API error:', error);
            return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
        }
    },
    {
        requiredRole: 'OEM',
    }
);
