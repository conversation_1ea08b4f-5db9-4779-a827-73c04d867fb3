export const dynamic = 'force-dynamic';

import { createHandler } from '@/lib/api/baseHandler';
import { UserService } from '@/lib/services/user.service';
import { NextRequest, NextResponse } from 'next/server';

export const GET = createHandler(
    async function (req: NextRequest) {
        try {
            const user = await UserService.user(req);
            return NextResponse.json(user);
        } catch (error) {
            console.error('Error fetching user:', error);
            return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
        }
    },
    {
        requiredRole: "OEM"
    });

export const PUT = createHandler(
    async function (req: NextRequest) {
        try {
            const user = await UserService.user(req);
            if (!user) {
                return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            const data = await req.json();
            const updatedUser = await UserService.update(user.id, data);
            return NextResponse.json(updatedUser);
        } catch (error) {
            console.error('Error updating user:', error);
            return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
        }
    }, {
    requiredRole: "OEM"
});
