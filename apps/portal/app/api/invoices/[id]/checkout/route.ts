import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { invoiceService } from "@/lib/services";
import { StripeService } from "@/lib/services/stripe.service";
import { stripe } from "@/lib/stripe";

export const POST = createHandler(
    async function (_, { params }) {
        const { id } = params;

        try {
            // Get the invoice
            const invoice = await invoiceService.getInvoiceById(id);
            if (!invoice) {
                return this.respond({ error: "Invoice not found" }, 404);
            }

            // Check if invoice is already paid
            if (invoice.status === "PAID") {
                return this.respond({ error: "Invoice is already paid" }, 400);
            }

            // Check if a payment intent already exists for this invoice
            if (invoice.payment_intent_id) {
                return this.respond({ error: "Payment intent already exists for this invoice" }, 400);
            }

            const listing = await prisma.listing.findUnique({
                where: {
                    id: invoice.provider_id
                }
            });

            if (!listing) {
                return this.respond({ error: "Provider listing not found" }, 404);
            }

            // Get the provider's Stripe connection
            const connection = await StripeService.getStripeConnection(
                listing.id
            );

            if (!connection) {
                return this.respond(
                    { error: "Provider has not connected their Stripe account" },
                    400
                );
            }

            // Create Stripe Checkout session with total amount
            // We handle line item details in our UI, Stripe just processes the total
            const session = await stripe.checkout.sessions.create({
                payment_method_types: ["card"],
                line_items: [{
                    price_data: {
                        currency: invoice.currency,
                        product_data: {
                            name: `Invoice #${invoice.invoice_number}`,
                            description: `Payment for ${listing.business_name}`,
                        },
                        unit_amount: invoice.amount, // Total invoice amount in cents
                    },
                    quantity: 1,
                }],
                mode: "payment",
                success_url: `${process.env.NEXT_PUBLIC_APP_URL}/invoices/${invoice.id}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
                cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/invoices/${invoice.id}?canceled=true`,
                customer_email: "<EMAIL>",
                custom_text: {
                    submit: {
                        message: `You are paying ${listing.business_name}${listing.email ? ` (${listing.email})` : ''}${listing.phone ? ` • ${listing.phone}` : ''}`
                    }
                },
                metadata: {
                    invoice_id: invoice.id,
                    provider_id: invoice.provider_id,
                    provider_invoice_id: invoice.id,
                    provider_business_name: listing.business_name,
                    provider_email: listing.email || "",
                    customer_email: invoice.customer_email,
                    type: "invoice_payment"
                },
                // Transfer to connected account (provider)
                payment_intent_data: {
                    transfer_data: {
                        destination: connection.stripe_account_id,
                        amount: invoice.amount // 100% goes to provider
                    },
                    metadata: {
                        invoice_id: invoice.id,
                        provider_invoice_id: invoice.id,
                        invoice_number: invoice.invoice_number,
                        provider_id: invoice.provider_id,
                        provider_business_name: listing.business_name,
                        customer_email: invoice.customer_email,
                        type: "invoice_payment",
                        description: `Payment for Invoice #${invoice.invoice_number}`
                    }
                },
            });

            // Store the session ID in the invoice to prevent duplicate payments
            // Note: payment_intent will be null until payment is completed
            await prisma.invoice.update({
                where: { id: invoice.id },
                data: {
                    payment_intent_id: session.payment_intent as string || null
                }
            });

            return this.respond({
                success: true,
                checkoutUrl: session.url
            }, 200);
        } catch (error) {
            console.error("Error creating checkout session:", error);
            let errorMessage = "Failed to create checkout session";
            if (error.message) {
                errorMessage = error.message;
            }
            return this.respond({
                error: errorMessage,
                details: error.message
            }, 500);
        }
    }, {
    requiredRole: "OEM"
}
); 