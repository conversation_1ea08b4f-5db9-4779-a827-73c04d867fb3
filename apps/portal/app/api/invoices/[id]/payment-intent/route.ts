import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { invoiceService } from "@/lib/services";
import { StripeService } from "@/lib/services/stripe.service";
import { stripe } from "@/lib/stripe";
import { z } from "zod";

const createPaymentIntentSchema = z.object({
	payment_method_types: z.array(z.string()).optional()
});

export const POST = createHandler(
	async function (req, { params }) {
		const { id } = params;
		const { payment_method_types } = this.validatedData;

		try {
			// Get the invoice
			const invoice = await invoiceService.getInvoiceById(id);
			if (!invoice) {
				return this.respond({ error: "Invoice not found" }, 404);
			}

			const listing = await prisma.listing.findUnique({
				where: {
					id: invoice.provider_id
				}
			});

			// Get the provider's Stripe connection
			const connection = await StripeService.getStripeConnection(
				listing.id
			);

			if (!connection) {
				return this.respond(
					{ error: "Provider has not connected their Stripe account" },
					400
				);
			}

			// Create the payment intent
			const paymentIntent = await StripeService.createPlatformPaymentIntent({
				ownerId: listing.owner_id,
				amount: invoice.amount,
				currency: invoice.currency,
				_providerId: invoice.provider_id,
				metadata: {
					provider_invoice_id: invoice.id,
					provider_id: invoice.provider_id,
					customer_email: invoice.customer_email,
					type: "invoice_payment"
				},
				payment_method_types
			});

			return this.respond({ clientSecret: paymentIntent.client_secret }, 200);
		} catch (error) {
			console.error("Error creating payment intent:", error);
			return this.respond({ error: "Failed to create payment intent" }, 500);
		}
	},
	{
		validateBody: createPaymentIntentSchema
	}
);

export const GET = createHandler(
	async function (_, { params, query }) {
		const { id } = params;
		const { session_id } = query;

		if (!session_id) {
			return this.respond({ error: "Session ID is required" }, 400);
		}

		try {
			// Retrieve the checkout session to get the payment intent
			const session = await stripe.checkout.sessions.retrieve(session_id as string);

			if (!session) {
				return this.respond({ error: "Checkout session not found" }, 404);
			}

			// Check if the session is for the correct invoice
			if (session.metadata?.invoice_id !== id) {
				return this.respond({ error: "Session does not match invoice" }, 400);
			}

			// The payment_intent will be available after the payment is completed
			if (!session.payment_intent) {
				return this.respond({
					error: "Payment intent not available yet",
					status: session.payment_status,
					session_status: session.status
				}, 400);
			}

			// Optionally retrieve the full payment intent details
			const paymentIntent = await stripe.paymentIntents.retrieve(session.payment_intent as string);

			return this.respond({
				success: true,
				payment_intent: session.payment_intent,
				payment_status: session.payment_status,
				session_status: session.status,
				payment_intent_details: {
					id: paymentIntent.id,
					amount: paymentIntent.amount,
					currency: paymentIntent.currency,
					status: paymentIntent.status,
					created: paymentIntent.created,
					metadata: paymentIntent.metadata
				}
			}, 200);

		} catch (error) {
			console.error("Error retrieving payment intent:", error);
			return this.respond({
				error: "Failed to retrieve payment intent",
				details: error.message
			}, 500);
		}
	}, {
	requiredRole: "OEM"
}
);
