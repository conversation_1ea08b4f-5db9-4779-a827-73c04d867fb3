import { z } from "zod";

import { createHandler } from "@/lib/api/baseHandler";
import { invoiceService } from "@/lib/services";
import { stripe } from "@/lib/stripe";
import { InvoiceStatus } from "@rvhelp/database";

const verifyPaymentSchema = z.object({
	payment_intent: z.string(),
	redirect_status: z.string()
});

export const POST = createHandler(
	async function POST(_req, { params }) {
		try {
			const { payment_intent, redirect_status } = this.validatedData;

			// Get the invoice
			const invoice = await invoiceService.getInvoiceById(params.id);
			if (!invoice) {
				return Response.json(
					{ success: false, message: "Invoice not found" },
					{ status: 404 }
				);
			}

			// Verify the payment intent
			const paymentIntent =
				await stripe.paymentIntents.retrieve(payment_intent);

			// Check if the payment was successful
			if (
				redirect_status === "succeeded" &&
				paymentIntent.status === "succeeded" &&
				paymentIntent.amount === invoice.amount
			) {
				// Update invoice status to paid
				await invoiceService.updateInvoiceStatus(invoice.id, InvoiceStatus.PAID);
				return Response.json({ success: true });
			}

			return Response.json(
				{ success: false, message: "Payment verification failed" },
				{ status: 400 }
			);
		} catch (error) {
			console.error("Error verifying payment:", error);
			return Response.json(
				{
					success: false,
					message:
						error instanceof Error ? error.message : "Failed to verify payment"
				},
				{ status: 500 }
			);
		}
	},
	{
		validateBody: verifyPaymentSchema,
		requiredRole: "OEM"
	}
);
