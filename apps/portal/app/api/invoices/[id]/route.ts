import { createHandler } from "@/lib/api/baseHandler";
import { invoiceService } from "@/lib/services";

export const GET = createHandler(
	async function () {
		const { id } = this.params;
		const invoice = await invoiceService.getInvoiceById(id);

		if (!invoice) {
			return Response.json({ error: "Invoice not found" }, { status: 404 });
		}

		return Response.json({ invoice });
	},
	{
		requiredRole: "OEM"
	}
);