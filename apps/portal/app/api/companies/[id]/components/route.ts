import { createHand<PERSON> } from '@/lib/api/baseHandler';
import prisma from '@/lib/prisma';
import { extractPDFFields } from '@/lib/pdf-fields-extractor';
import { WarrantyAttachment } from '@/types/warranty';

async function processDocumentAttachments(
    attachments: WarrantyAttachment[]
): Promise<WarrantyAttachment[]> {
    if (!attachments || !Array.isArray(attachments)) {
        return;
    }

    for (const attachment of attachments) {
        try {
            // Extract S3 key from URL (assuming format like https://bucket.s3.region.amazonaws.com/key)
            const url = new URL(attachment.url);
            const s3Key = url.pathname.substring(1); // Remove leading slash

            const fields = attachment.type === 'form' ? await extractPDFFields(s3Key) : null;

            const document = await prisma.document.create({
                data: {
                    url: attachment.url,
                    original_id: null,
                    form_fields: JSON.stringify(fields),
                    type: attachment.type,
                    title: attachment.title,
                },
            });
            attachment.id = document.id;
        } catch (error) {
            console.error(`Failed to process PDF attachment ${attachment.url}:`, error);
            // Continue processing other attachments even if one fails
        }
    }

    return attachments;
}

export const POST = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract company ID from URL: /api/companies/[id]/components
        const urlParts = this.req.url.split('/');
        const companyId = urlParts[urlParts.length - 2];

        try {
            // Verify user has access to this company
            const user = await prisma.user.findFirst({
                where: {
                    email: this.user.email,
                    company_id: companyId,
                },
                select: { id: true, role: true },
            });

            if (!user) {
                return Response.json(
                    { error: 'Company not found or access denied' },
                    { status: 404 }
                );
            }

            // Parse request body
            const body = await this.req.json();

            // Accept either a single object or an array
            const components = Array.isArray(body) ? body : [body];

            // Validate all components
            const invalid = components.find((comp) => !comp.type || !comp.manufacturer);
            if (invalid) {
                return Response.json(
                    { error: 'Each component must have required fields: type and manufacturer' },
                    { status: 400 }
                );
            }

            // Process required PDF attachments
            for (const comp of components) {
                if (comp.attachments) {
                    try {
                        await processDocumentAttachments(comp.attachments);
                    } catch (error) {
                        console.error(
                            `Failed to process PDF attachments for component ${comp.id}:`,
                            error
                        );
                    }
                }
            }

            // Create all components
            const created = await Promise.all(
                components.map(async (comp) => {
                    try {
                        const newComponent = await prisma.component.create({
                            data: {
                                company_id: companyId,
                                type: comp.type,
                                manufacturer: comp.manufacturer,
                                notes: comp.notes || null,
                                attachments: comp.attachments || null,
                            },
                            include: {
                                company: true,
                            },
                        });

                        return { success: true, component: newComponent };
                    } catch (err) {
                        return {
                            success: false,
                            error: err?.message || 'Create failed',
                            data: comp,
                        };
                    }
                })
            );

            // If only one component was sent, return a single object for compatibility
            if (!Array.isArray(body)) {
                return Response.json(created[0], { status: created[0].success ? 201 : 400 });
            }
            return Response.json({ results: created }, { status: 201 });
        } catch (error) {
            console.error('Error creating component:', error);
            return Response.json({ error: 'Failed to create component' }, { status: 500 });
        }
    },
    {
        requireAuth: true,
        requiredRole: 'OEM', // Only OEM users can create components
    }
);

export const PUT = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract company ID from URL: /api/companies/[id]/components
        const urlParts = this.req.url.split('/');
        const companyId = urlParts[urlParts.length - 2];

        try {
            // Verify user has access to this company
            const user = await prisma.user.findFirst({
                where: {
                    email: this.user.email,
                    company_id: companyId,
                },
                select: { id: true, role: true },
            });

            if (!user) {
                return Response.json(
                    { error: 'Company not found or access denied' },
                    { status: 404 }
                );
            }

            // Parse request body
            const body = await this.req.json();

            // Accept either a single object or an array
            const components = Array.isArray(body) ? body : [body];

            for (const comp of components) {
                if (comp.attachments) {
                    try {
                        const currentAttachments = (comp.attachments as WarrantyAttachment[]) || [];
                        const newAttachments = (comp.attachments as WarrantyAttachment[]) || [];

                        // Find newly added attachments by comparing URLs
                        const currentUrls = new Set(currentAttachments.map((att) => att.url));
                        const addedAttachments = newAttachments.filter(
                            (att) => !currentUrls.has(att.id)
                        );

                        if (addedAttachments.length > 0) {
                            await processDocumentAttachments(addedAttachments);
                        }
                    } catch (error) {
                        console.error(
                            `Failed to process PDF attachments for component ${comp.id}:`,
                            error
                        );
                    }
                }
            }

            // Validate all components
            const invalid = components.find((comp) => !comp.id || typeof comp.id !== 'string');
            if (invalid) {
                return Response.json(
                    { error: 'Each component must have a valid id' },
                    { status: 400 }
                );
            }

            // Update components in parallel
            const results = await Promise.all(
                components.map(async (comp) => {
                    try {
                        // Get current component to compare attachments
                        const currentComponent = await prisma.component.findUnique({
                            where: { id: comp.id, company_id: companyId },
                            select: { attachments: true },
                        });

                        if (!currentComponent) {
                            return { id: comp.id, success: false, error: 'Component not found' };
                        }

                        const updated = await prisma.component.update({
                            where: { id: comp.id, company_id: companyId },
                            data: {
                                type: comp.type,
                                manufacturer: comp.manufacturer,
                                notes: comp.notes,
                                attachments: comp.attachments,
                            },
                        });

                        return { id: comp.id, success: true, updated };
                    } catch (err) {
                        return {
                            id: comp.id,
                            success: false,
                            error: err?.message || 'Update failed',
                        };
                    }
                })
            );

            // If only one component was sent, return a single object for compatibility
            if (!Array.isArray(body)) {
                return Response.json(results[0], { status: results[0].success ? 200 : 400 });
            }
            return Response.json({ results }, { status: 200 });
        } catch (error) {
            console.error('Error updating components:', error);
            return Response.json({ error: 'Failed to update components' }, { status: 500 });
        }
    },
    {
        requireAuth: true,
        requiredRole: 'OEM', // Only OEM users can update components
    }
);
