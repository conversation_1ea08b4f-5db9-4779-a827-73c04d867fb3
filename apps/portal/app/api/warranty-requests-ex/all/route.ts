import { createHandler } from '@/lib/api/baseHandler';
import { WarrantyRequestService } from '@/lib/services/warranty-request.service';
import { NextRequest } from 'next/server';

export const GET = createHandler(
    async function (req: NextRequest) {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        if (!this.user?.company_id) {
            return Response.json({ error: 'User not associated with a company' }, { status: 400 });
        }

        const searchParams = req.nextUrl.searchParams;
        const hideCompleted = searchParams.get('hideCompleted') === 'true';

        const filters = {
            page: parseInt(searchParams.get('page') || '1'),
            pageSize: parseInt(searchParams.get('pageSize') || '50'),
            representativeId: "all",
            // Include all statuses in enum order, except completed/cancelled if hideCompleted is true
            status: hideCompleted
                ? "[\"REQUEST_CREATED\",\"REQUEST_APPROVED\",\"REQUEST_REJECTED\",\"JOB_REGISTERED\",\"JOB_REQUESTED\",\"JOB_ACCEPTED\",\"JOB_STARTED\",\"AUTHORIZATION_REQUESTED\",\"AUTHORIZATION_APPROVED\",\"AUTHORIZATION_REJECTED\",\"AUTHORIZATION_FEEDBACK\",\"PARTS_ORDERED\",\"INVOICE_CREATED\"]"
                : "[\"REQUEST_CREATED\",\"REQUEST_APPROVED\",\"REQUEST_REJECTED\",\"JOB_REGISTERED\",\"JOB_REQUESTED\",\"JOB_ACCEPTED\",\"JOB_STARTED\",\"JOB_COMPLETED\",\"JOB_CANCELLED\",\"AUTHORIZATION_REQUESTED\",\"AUTHORIZATION_APPROVED\",\"AUTHORIZATION_REJECTED\",\"AUTHORIZATION_FEEDBACK\",\"PARTS_ORDERED\",\"INVOICE_CREATED\",\"INVOICE_PAID\"]",
            component: searchParams.get('component'),
            search: searchParams.get('search'),
            rvVin: searchParams.get('rvVin'),
            rvModel: searchParams.get('rvModel'),
        };

        const result = await WarrantyRequestService.getWarrantyRequests(
            this.user.company_id,
            this.user.id,
            filters
        );

        return Response.json(result);
    },
    {
        requiredRole: "OEM"
    }
);
