import { createHand<PERSON> } from '@/lib/api/baseHandler';
import { WarrantyRequestService } from '@/lib/services/warranty-request.service';
import { NextRequest } from 'next/server';

export const GET = createHandler(
    async function (req: NextRequest) {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        if (!this.user?.company_id) {
            return Response.json({ error: 'User not associated with a company' }, { status: 400 });
        }

        // Get search params from the request URL
        const searchParams = req.nextUrl.searchParams;

        const filters = {
            page: parseInt(searchParams.get('page') || '1'),
            pageSize: parseInt(searchParams.get('pageSize') || '10'),
            representativeId: "all",
            status: "[\"JOB_STARTED\",\"AUTHORIZATION_REQUESTED\",\"AUTHORIZATION_APPROVED\",\"AUTHORIZATION_REJECTED\",\"AUTHORIZATION_FEEDBACK\",\"PARTS_ORDERED\"]",
            component: searchParams.get('component'),
            search: searchParams.get('search'),
            rvVin: searchParams.get('rvVin'),
            rvModel: searchParams.get('rvModel'),
        };

        const result = await WarrantyRequestService.getWarrantyRequests(
            this.user.company_id,
            this.user.id,
            filters
        );

        return Response.json(result);
    },
    {
        requiredRole: "OEM"
    }
);
