import prisma from '@/lib/prisma';

export async function GET() {
    try {
        const schemaInfo = {
            workingDirectory: process.cwd(),
            nodeModulesPath: require.resolve('@prisma/client'),
            prismaClientConstructor: prisma.constructor.name,
            availableModels: Object.keys(prisma),
        };

        // Try a simple query to see if Prisma is working
        const testResult = await prisma.warrantyRequest.findFirst({
            select: {
                id: true,
                complaint: true,
            }
        });

        return Response.json({
            schemaInfo,
            testResult,
            message: 'Prisma client debug info'
        });
    } catch (error) {
        console.error('Debug endpoint error:', error);
        return Response.json({
            error: error.message,
            stack: error.stack
        }, { status: 500 });
    }
} 