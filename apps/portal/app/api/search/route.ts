export const dynamic = 'force-dynamic';
import { createHandler } from "@/lib/api/baseHandler";
import { SearchService } from "@/lib/services/search.service";
import { RVHelpVerificationLevel } from "@rvhelp/database";
import { z } from "zod";

const searchParamsSchema = z.object({
    lat: z.string().optional(),
    lng: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    category: z.string().optional(),
    subcategory: z.string().optional(),
    certification: z.string().optional(),
    verificationLevel: z.string().optional(),
    certificationLevels: z.string().optional(),
    sortBy: z.enum(["default", "distance", "reviews"]).optional(),
    radius: z.string().optional(),
    page: z.string().default("1"),
    limit: z.string().default("10"),
    showDiscountProviders: z.string().optional(),
    showTroubleshootingProviders: z.string().optional()
});

export const GET = createHandler(
    async function (req: Request) {
        const url = new URL(req.url);
        const params = Object.fromEntries(url.searchParams);

        const {
            lat,
            lng,
            city,
            state,
            category,
            subcategory,
            certification,
            verificationLevel,
            certificationLevels,
            sortBy,
            radius,
            page,
            limit,
            showDiscountProviders,
            showTroubleshootingProviders
        } = params;

        // Convert certification levels from string to array if present
        const certLevels = certificationLevels
            ? certificationLevels.split(",").map(Number)
            : undefined;

        const searchParams = {
            lat,
            lng,
            city,
            state,
            category,
            subcategory,
            sortBy,
            radius,
            filters: {
                verificationLevel:
                    verificationLevel === "undefined"
                        ? undefined
                        : (verificationLevel as RVHelpVerificationLevel),
                certificationLevels: certLevels,
                certification: certification,
                showDiscountProviders: showDiscountProviders === "true",
                showTroubleshootingProviders: showTroubleshootingProviders === "true"
            }
        };

        try {
            let results;
            if (lat && lng) {
                results = await SearchService.getListingsByLatLong(
                    searchParams,
                    parseInt(page),
                    parseInt(limit)
                );
            } else if (city && state) {
                results = await SearchService.getListingsByCityState(
                    searchParams,
                    parseInt(page),
                    parseInt(limit)
                );
            } else {
                return Response.json(
                    { error: "Invalid search parameters" },
                    { status: 400 }
                );
            }

            return Response.json(results);
        } catch (error) {
            console.error("Search error:", error);
            return Response.json({ error: "Search failed" }, { status: 500 });
        }
    }, {
    validateQuery: searchParamsSchema,
    requiredRole: "OEM"
});
