import WarrantyRequestList from "@/components/WarrantyRequestList";
import prisma from "@/lib/prisma";

import <PERSON>EM<PERSON><PERSON> from "@/components/oem-logo";
import { authOptions } from "@/lib/auth";
import { User } from "@/types/global";
import { getServerSession } from "next-auth";

/**
 * Service Requests Page - Supports URL query parameters for filtering
 *
 * Query Parameters:
 * - representative: 'current' (default) or 'all'
 * - status: JSON array of status strings, e.g., ["REQUEST_CREATED","JOB_STARTED"]
 * - component: JSON array of component IDs, e.g., ["comp1","comp2"]
 * - search: Search term for customer names
 * - rvManufacturer: Search term for RV manufacturer names, e.g., "Jayco"
 * - rvModel: Search term for RV model names
 * - page: Page number (1-based)
 *
 * Example URLs:
 * - /service-requests?representative=all&page=2
 * - /service-requests?status=["REQUEST_CREATED","JOB_STARTED"]&search=smith
 * - /service-requests?component=["comp1"]&representative=current
 * - /service-requests?rvManufacturer=Jayco&rvModel=eagle
 */

interface SearchParams {
	representative?: string; // 'current' or 'all'
	status?: string; // JSON array of status strings
	component?: string; // JSON array of component IDs
	search?: string; // Search term for customer names
	rvVin?: string; // Search term for RV manufacturer names
	rvModel?: string; // Search term for RV model names
	page?: string; // Page number (1-based)
}

interface ServiceRequestsPageProps {
	searchParams: SearchParams;
}

export default async function ServiceRequestsPage({
	searchParams
}: ServiceRequestsPageProps) {
	const session = await getServerSession(authOptions);

	if (!session?.user?.email) {
		return Response.json({ error: "Unauthorized" }, { status: 401 });
	}

	// Get the current user with their company_id
	const dbUser = await prisma.user.findFirst({
		where: { email: session.user.email }
	});

	if (!dbUser?.company_id) {
		return <div>User not associated with a company</div>;
	}

	// Transform the user data to match the expected User type
	const user: User = {
		...dbUser,
		rv_details:
			dbUser.rv_details &&
			typeof dbUser.rv_details === "object" &&
			dbUser.rv_details !== null
				? {
						type: (dbUser.rv_details as any).type || "",
						year: Number((dbUser.rv_details as any).year) || 0,
						make: (dbUser.rv_details as any).make || "",
						model: (dbUser.rv_details as any).model || ""
					}
				: {
						type: "",
						year: 0,
						make: "",
						model: ""
					}
	};

	// Fetch the company with its components and recent warranty requests
	const company = await prisma.company.findUnique({
		where: { id: user.company_id },
		include: {
			components: true
		}
	});

	if (!company) {
		return <div>Company not found</div>;
	}

	// Format the company data to match ExtendedCompany type
	const extendedCompany = {
		...company,
		components: company.components.map((component) => ({
			...component,
			attachments: component.attachments
				? Array.isArray(component.attachments)
					? component.attachments
					: [component.attachments]
				: []
		}))
	};

	// Parse initial filter values from query parameters with error handling
	const parseArrayParam = (param: string | undefined): string[] => {
		if (!param) return [];
		try {
			const parsed = JSON.parse(param);
			return Array.isArray(parsed) ? parsed : [];
		} catch {
			return [];
		}
	};

	const parseNumberParam = (
		param: string | undefined,
		defaultValue: number
	): number => {
		if (!param) return defaultValue;
		const parsed = parseInt(param);
		return isNaN(parsed) ? defaultValue : Math.max(1, parsed);
	};

	const parseRepresentativeParam = (param: string | undefined): string => {
		const validValues = ["current", "all"];
		return validValues.includes(param || "") ? param! : "current";
	};

	const initialFilters = {
		representative: parseRepresentativeParam(searchParams.representative),
		status: parseArrayParam(searchParams.status),
		component: parseArrayParam(searchParams.component),
		search: searchParams.search || "",
		rvVin: searchParams.rvVin || "",
		rvModel: searchParams.rvModel || "",
		page: parseNumberParam(searchParams.page, 1)
	};

	return (
		<div className="space-y-6">
			<div className="flex justify-center gap-4">
				<OEMLogo company={extendedCompany} />
				<h1 className="pt-8 text-2xl font-semibold text-gray-900">
					Warranty Service Requests
				</h1>
			</div>
			<WarrantyRequestList
				company={extendedCompany}
				initialFilters={initialFilters}
				user={user}
			/>
		</div>
	);
}
