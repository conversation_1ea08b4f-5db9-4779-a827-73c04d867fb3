import OEMLogo from "@/components/oem-logo";
import GeneralStatusView from "@/components/warranty/status-cards/general-status-view";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth";

export default async function GeneralStatusPage() {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
        return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const dbUser = await prisma.user.findFirst({
        where: { email: session.user.email }
    });
    if (!dbUser?.company_id) {
        return <div>User not associated with a company</div>;
    }

    const company = await prisma.company.findUnique({
        where: { id: dbUser.company_id }
    });
    if (!company) {
        return <div>Company not found</div>;
    }

    return (
        <div className="space-y-6">
            <div className="flex justify-center gap-4">
                <OEMLogo company={company} />
                <h1 className="pt-8 text-2xl font-semibold text-gray-900">
                    Service Request Status Overview
                </h1>
            </div>
            <GeneralStatusView
                company={company}
                user={dbUser as any}
            />
        </div>
    );
}
