"use client";

import PaymentConfirmation from "@/components/payments/PaymentConfirmation";
import { useEffect, useState } from "react";

export default function PaymentSuccess({
	params,
	searchParams
}: {
	params: {
		id: string;
	};
	searchParams: {
		session_id?: string;
	};
}) {
	const [invoice, setInvoice] = useState<any>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const sessionId = searchParams.session_id;

	useEffect(() => {
		const fetchInvoice = async () => {
			try {
				const response = await fetch(`/api/invoices/${params.id}`);
				if (!response.ok) {
					setError("Invoice not found");
					setLoading(false);
					return;
				}
				const invoiceData = await response.json();
				if (!invoiceData) {
					setError("Invoice not found");
					setLoading(false);
					return;
				}

				setInvoice(invoiceData);

				// Only update warranty request status if:
				// 1. This is a warranty invoice
				// 2. The invoice is not already paid
				// 3. The payment intent matches the session ID
				if (
					invoiceData.warranty_request_id &&
					invoiceData.status !== "PAID" &&
					invoiceData.payment_intent_id === sessionId
				) {
					try {
						const response = await fetch(
							`/api/warranty-requests/${invoiceData.warranty_request_id}/update-status`,
							{
								method: "PUT",
								headers: {
									"Content-Type": "application/json"
								},
								body: JSON.stringify({
									event_type: "INVOICE_PAID",
									update_notes: "Payment completed via Stripe Checkout"
								})
							}
						);

						if (!response.ok) {
							console.error(
								"Failed to update warranty status:",
								response.status,
								response.statusText
							);
						} else {
							console.log("Warranty status updated successfully");

							// Generate platform fee invoice for warranty claims
							try {
								const platformResponse = await fetch(
									`/api/admin/generate-platform-invoice`,
									{
										method: "POST",
										headers: {
											"Content-Type": "application/json"
										},
										body: JSON.stringify({
											originalInvoiceId: invoiceData.id
										})
									}
								);

								if (!platformResponse.ok) {
									console.error(
										"Failed to generate platform invoice:",
										platformResponse.status,
										platformResponse.statusText
									);
								} else {
									const platformResult = await platformResponse.json();
									console.log(
										"Platform invoice generated successfully:",
										platformResult
									);
								}
							} catch (platformError) {
								console.error(
									"Failed to generate platform invoice:",
									platformError
								);
							}
						}
					} catch (error) {
						console.error("Failed to update warranty status:", error);
					}
				} else if (invoiceData.status === "PAID") {
					console.log(
						"Invoice is already paid, skipping warranty status update"
					);
				} else if (invoiceData.payment_intent_id !== sessionId) {
					console.log(
						"Payment intent mismatch, skipping warranty status update"
					);
				}

				setLoading(false);
			} catch (error) {
				console.error("Failed to fetch invoice:", error);
				setError("Failed to load invoice");
				setLoading(false);
			}
		};

		fetchInvoice();
	}, [params.id, sessionId]);

	if (loading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
					<p className="text-gray-600">Loading payment confirmation...</p>
				</div>
			</div>
		);
	}

	if (error || !invoice) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<h1 className="text-xl font-semibold text-gray-900 mb-2">
						Invoice Not Found
					</h1>
					<p className="text-gray-600">
						The invoice you're looking for doesn't exist.
					</p>
				</div>
			</div>
		);
	}

	return (
		<PaymentConfirmation
			invoice={invoice.invoice}
			paymentId={sessionId || "Unknown"}
		/>
	);
}
