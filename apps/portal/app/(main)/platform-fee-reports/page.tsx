"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON>,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle
} from "@/components/ui/card";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@/components/ui/table";
import { formatCurrency } from "@/lib/utils";
import { Download, RefreshCw } from "lucide-react";
import { useEffect, useState } from "react";

interface PlatformFeeReport {
    id: string;
    period_start: string;
    period_end: string;
    provider_invoices: {
        id: string;
        invoice_number: string;
        amount: number;
        provider: {
            business_name: string;
        };
        warranty_provider_request: {
            rv_vin: string;
        };
    }[];
    total_amount: number;
    platform_fee_total: number;
    created_at: string;
}

export default function PlatformFeeReportsPage() {
    const [reports, setReports] = useState<PlatformFeeReport[]>([]);
    const [loading, setLoading] = useState(true);
    const [generating, setGenerating] = useState(false);

    const fetchReports = async () => {
        try {
            setLoading(true);
            const response = await fetch("/api/platform-fee-reports");
            if (!response.ok) throw new Error("Failed to fetch reports");
            const data = await response.json();
            setReports(data.reports || []);
        } catch (error) {
            console.error("Error fetching platform fee reports:", error);
        } finally {
            setLoading(false);
        }
    };

    const generateReport = async () => {
        try {
            setGenerating(true);
            const response = await fetch("/api/platform-fee-reports/generate", {
                method: "POST"
            });
            if (!response.ok) throw new Error("Failed to generate report");

            // Refresh the reports list
            await fetchReports();
        } catch (error) {
            console.error("Error generating platform fee report:", error);
        } finally {
            setGenerating(false);
        }
    };

    const exportToCSV = async (reportId: string) => {
        try {
            const response = await fetch(`/api/platform-fee-reports/${reportId}/export`);
            if (!response.ok) throw new Error("Failed to export report");

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `platform-fee-report-${reportId}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error("Error exporting report:", error);
        }
    };

    useEffect(() => {
        fetchReports();
    }, []);

    return (
        <div className="container mx-auto py-8">
            <div className="flex items-center justify-between mb-8">
                <div>
                    <h1 className="text-3xl font-bold">Platform Fee Reports</h1>
                    <p className="text-gray-600 mt-2">
                        Track platform fees from provider invoices for QuickBooks export
                    </p>
                </div>
                <div className="flex gap-3">
                    <Button
                        variant="outline"
                        onClick={fetchReports}
                        disabled={loading}
                    >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Refresh
                    </Button>
                    <Button
                        onClick={generateReport}
                        disabled={generating}
                    >
                        {generating ? "Generating..." : "Generate Report"}
                    </Button>
                </div>
            </div>

            {loading ? (
                <div className="grid gap-6">
                    {[...Array(3)].map((_, i) => (
                        <Card key={i}>
                            <CardHeader>
                                <div className="h-6 bg-gray-200 rounded animate-pulse" />
                                <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                            </CardHeader>
                            <CardContent>
                                <div className="h-32 bg-gray-200 rounded animate-pulse" />
                            </CardContent>
                        </Card>
                    ))}
                </div>
            ) : reports.length === 0 ? (
                <Card>
                    <CardContent className="py-12 text-center">
                        <h3 className="text-lg font-semibold mb-2">No Reports Found</h3>
                        <p className="text-gray-600 mb-4">
                            Generate your first platform fee report to get started.
                        </p>
                        <Button onClick={generateReport} disabled={generating}>
                            {generating ? "Generating..." : "Generate First Report"}
                        </Button>
                    </CardContent>
                </Card>
            ) : (
                <div className="grid gap-6">
                    {reports.map((report) => (
                        <Card key={report.id}>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle>
                                            Platform Fee Report
                                        </CardTitle>
                                        <CardDescription>
                                            {new Date(report.period_start).toLocaleDateString()} - {new Date(report.period_end).toLocaleDateString()}
                                        </CardDescription>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <div className="text-right">
                                            <div className="text-2xl font-bold">
                                                {formatCurrency(report.platform_fee_total)}
                                            </div>
                                            <div className="text-sm text-gray-600">
                                                Platform Fee Total
                                            </div>
                                        </div>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => exportToCSV(report.id)}
                                        >
                                            <Download className="w-4 h-4 mr-2" />
                                            Export CSV
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Invoice #</TableHead>
                                            <TableHead>Provider</TableHead>
                                            <TableHead>RV VIN</TableHead>
                                            <TableHead>Invoice Amount</TableHead>
                                            <TableHead>Platform Fee</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {report.provider_invoices.map((invoice) => (
                                            <TableRow key={invoice.id}>
                                                <TableCell className="font-medium">
                                                    {invoice.invoice_number}
                                                </TableCell>
                                                <TableCell>
                                                    {invoice.provider.business_name}
                                                </TableCell>
                                                <TableCell>
                                                    {invoice.warranty_provider_request?.rv_vin || "Unknown VIN"}
                                                </TableCell>
                                                <TableCell>
                                                    {formatCurrency(invoice.amount)}
                                                </TableCell>
                                                <TableCell>
                                                    {formatCurrency(5000)} {/* $50.00 */}
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>

                                <div className="mt-4 pt-4 border-t">
                                    <div className="flex justify-between text-sm text-gray-600">
                                        <span>Total Warranty Requests: {report.provider_invoices.length}</span>
                                        <span>Generated: {new Date(report.created_at).toLocaleDateString()}</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    );
}
