import { ProfileSettingsContent } from '@/components/profile/profile-settings';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';

export default async function ProfilePage() {
    const session = await getServerSession(authOptions);
    let user = null;

    if (session?.user?.email) {
        user = await prisma.user.findFirst({
            where: { email: session.user.email },
            include: { company: true },
        });
    }

    return (
        <div className="space-y-6">
            <h1 className="text-2xl font-semibold text-center text-gray-900">
                Warranty Service Requests
            </h1>
            <ProfileSettingsContent user={user} />;
        </div>
    );
}
