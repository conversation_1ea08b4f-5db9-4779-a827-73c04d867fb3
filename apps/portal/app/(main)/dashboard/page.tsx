import prisma from "@/lib/prisma";

import { Dashboard } from "@/components/dashboard/dashboard";
import { authOptions } from "@/lib/auth";
import { User } from "@/types/global";
import { getServerSession } from "next-auth";

export default async function DashboardPage() {
	const session = await getServerSession(authOptions);

	if (!session?.user?.email) {
		return Response.json({ error: "Unauthorized" }, { status: 401 });
	}

	// Get the current user with their company_id
	const dbUser = await prisma.user.findFirst({
		where: { email: session.user.email }
	});

	if (!dbUser?.company_id) {
		return <div>User not associated with a company</div>;
	}

	// Transform the user data to match the expected User type
	const user: User = {
		...dbUser,
		rv_details:
			dbUser.rv_details &&
			typeof dbUser.rv_details === "object" &&
			dbUser.rv_details !== null
				? {
						type: (dbUser.rv_details as any).type || "",
						year: Number((dbUser.rv_details as any).year) || 0,
						make: (dbUser.rv_details as any).make || "",
						model: (dbUser.rv_details as any).model || ""
					}
				: {
						type: "",
						year: 0,
						make: "",
						model: ""
					}
	};

	return <Dashboard user={user} />;
}
