"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	<PERSON><PERSON>Header,
	DialogTitle
} from "@/components/ui/dialog";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { CheckCircle, FileText, Loader2, XCircle } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";

interface PlatformInvoiceGenerationModalProps {
	open: boolean;
	onClose: () => void;
	request: ExtendedWarrantyRequest | null;
}

type GenerationState = "idle" | "generating" | "success" | "error";

export function PlatformInvoiceGenerationModal({
	open,
	onClose,
	request
}: PlatformInvoiceGenerationModalProps) {
	const [state, setState] = useState<GenerationState>("idle");
	const [progress, setProgress] = useState(0);
	const [currentStep, setCurrentStep] = useState("");
	const [platformInvoice, setPlatformInvoice] = useState<any>(null);
	const [error, setError] = useState<string | null>(null);

	// Reset state when modal opens/closes
	useEffect(() => {
		if (open) {
			setState("idle");
			setProgress(0);
			setCurrentStep("");
			setPlatformInvoice(null);
			setError(null);
		}
	}, [open]);

	const steps = [
		"Finding associated invoice...",
		"Validating invoice data...",
		"Creating platform invoice...",
		"Sending email notifications...",
		"Sending Slack notifications...",
		"Finalizing..."
	];

	const generatePlatformInvoice = useCallback(async () => {
		if (!request) return;

		setState("generating");
		setProgress(0);
		setError(null);

		try {
			// Step 1: Find associated invoice
			setCurrentStep(steps[0]);
			setProgress(16);

			const response = await fetch(
				`/api/warranty-requests/${request.id}/invoice`
			);
			if (!response.ok) {
				throw new Error("Could not find associated invoice");
			}

			const invoiceData = await response.json();
			if (!invoiceData.invoice) {
				throw new Error("No invoice found for this warranty request");
			}

			// Step 2: Validate invoice data
			setCurrentStep(steps[1]);
			setProgress(33);

			// Add a small delay to show the progress
			await new Promise((resolve) => setTimeout(resolve, 500));

			// Step 3: Generate platform invoice
			setCurrentStep(steps[2]);
			setProgress(50);

			const platformResponse = await fetch(
				"/api/admin/generate-platform-invoice",
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						originalInvoiceId: invoiceData.invoice.id
					})
				}
			);

			const platformData = await platformResponse.json();

			if (!platformData.success) {
				throw new Error(
					platformData.message || "Failed to generate platform invoice"
				);
			}

			// Step 4: Email notifications
			setCurrentStep(steps[3]);
			setProgress(66);
			await new Promise((resolve) => setTimeout(resolve, 500));

			// Step 5: Slack notifications
			setCurrentStep(steps[4]);
			setProgress(83);
			await new Promise((resolve) => setTimeout(resolve, 500));

			// Step 6: Finalize
			setCurrentStep(steps[5]);
			setProgress(100);
			await new Promise((resolve) => setTimeout(resolve, 300));

			setPlatformInvoice(platformData.platformInvoice);
			setState("success");

			// Show success toast
			toast.success(
				`Platform invoice #${platformData.platformInvoice.invoice_number} generated successfully!`
			);
		} catch (error) {
			console.error("Error generating platform invoice:", error);
			setError(
				error instanceof Error
					? error.message
					: "Failed to generate platform invoice"
			);
			setState("error");

			// Show error toast
			toast.error("Failed to generate platform invoice");
		}
	}, [request]);

	const handleClose = () => {
		if (state === "generating") {
			// Don't allow closing while generating
			return;
		}
		onClose();
	};

	const getIcon = () => {
		switch (state) {
			case "generating":
				return <Loader2 className="h-8 w-8 animate-spin text-blue-600" />;
			case "success":
				return <CheckCircle className="h-8 w-8 text-green-600" />;
			case "error":
				return <XCircle className="h-8 w-8 text-red-600" />;
			default:
				return <FileText className="h-8 w-8 text-gray-600" />;
		}
	};

	const getTitle = () => {
		switch (state) {
			case "generating":
				return "Generating Platform Invoice";
			case "success":
				return "Platform Invoice Generated";
			case "error":
				return "Generation Failed";
			default:
				return "Generate Platform Invoice";
		}
	};

	const getDescription = () => {
		switch (state) {
			case "generating":
				return "Please wait while we generate the platform invoice and send notifications...";
			case "success":
				return "The platform invoice has been successfully generated and notifications have been sent.";
			case "error":
				return "There was an error generating the platform invoice. Please try again.";
			default:
				return `Generate a $50 platform fee invoice for warranty request ${request?.uuid?.slice(0, 8) || "N/A"}.`;
		}
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-md">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-3">
						{getIcon()}
						{getTitle()}
					</DialogTitle>
					<DialogDescription>{getDescription()}</DialogDescription>
				</DialogHeader>

				<div className="py-4">
					{state === "generating" && (
						<div className="space-y-4">
							{/* Progress bar */}
							<div className="w-full bg-gray-200 rounded-full h-2">
								<div
									className="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
									style={{ width: `${progress}%` }}
								/>
							</div>

							{/* Current step */}
							<div className="text-sm text-gray-600 text-center">
								{currentStep}
							</div>
						</div>
					)}

					{state === "success" && platformInvoice && (
						<div className="bg-green-50 border border-green-200 rounded-lg p-4">
							<div className="flex items-center gap-2 text-green-800 font-medium mb-2">
								<CheckCircle className="h-4 w-4" />
								Invoice Generated Successfully
							</div>
							<div className="text-sm text-green-700 space-y-1">
								<div>Invoice #{platformInvoice.invoice_number}</div>
								<div>Amount: ${(platformInvoice.amount / 100).toFixed(2)}</div>
								<div>Notifications sent to team and Keystone</div>
							</div>
						</div>
					)}

					{state === "error" && error && (
						<div className="bg-red-50 border border-red-200 rounded-lg p-4">
							<div className="flex items-center gap-2 text-red-800 font-medium mb-2">
								<XCircle className="h-4 w-4" />
								Generation Failed
							</div>
							<div className="text-sm text-red-700">{error}</div>
						</div>
					)}

					{state === "idle" && request && (
						<div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
							<div className="text-sm text-gray-700 space-y-2">
								<div>
									<strong>Warranty Request:</strong> {request.uuid?.slice(0, 8)}
								</div>
								<div>
									<strong>Customer:</strong> {request.first_name}{" "}
									{request.last_name}
								</div>
								<div>
									<strong>RV:</strong> {request.rv_year} {request.rv_make}{" "}
									{request.rv_model}
								</div>
								<div className="text-xs text-gray-500 mt-3">
									This will generate a $50 platform fee invoice and send
									notifications to your team and Keystone.
								</div>
							</div>
						</div>
					)}
				</div>

				<DialogFooter>
					{state === "idle" && (
						<>
							<Button variant="outline" onClick={handleClose}>
								Cancel
							</Button>
							<Button
								onClick={generatePlatformInvoice}
								className="bg-blue-600 hover:bg-blue-700"
							>
								<FileText className="h-4 w-4 mr-2" />
								Generate Invoice
							</Button>
						</>
					)}

					{state === "generating" && (
						<Button disabled className="bg-blue-600">
							<Loader2 className="h-4 w-4 mr-2 animate-spin" />
							Generating...
						</Button>
					)}

					{(state === "success" || state === "error") && (
						<Button onClick={handleClose}>
							{state === "success" ? "Done" : "Close"}
						</Button>
					)}
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
