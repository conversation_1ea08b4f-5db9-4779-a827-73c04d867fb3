"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON>eader,
	<PERSON>alogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ExtendedCompany } from "@/types/warranty";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const supportSchema = z.object({
	firstName: z.string().min(1, "First name is required"),
	lastName: z.string().min(1, "Last name is required"),
	email: z.string().email("Please enter a valid email address"),
	description: z
		.string()
		.min(10, "Please provide a detailed description (minimum 10 characters)")
});

type SupportFormData = z.infer<typeof supportSchema>;

interface SupportModalProps {
	open: boolean;
	onClose: () => void;
	type: "bug" | "feature";
	company?: ExtendedCompany;
}

export function SupportModal({
	open,
	onClose,
	type,
	company
}: SupportModalProps) {
	const [loading, setLoading] = useState(false);
	const [submitted, setSubmitted] = useState(false);

	const form = useForm<SupportFormData>({
		resolver: zodResolver(supportSchema),
		defaultValues: {
			firstName: "",
			lastName: "",
			email: "",
			description: ""
		}
	});

	const onSubmit = async (data: SupportFormData) => {
		setLoading(true);
		try {
			const response = await fetch("/api/support", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					...data,
					type,
					companyName: company?.name || "Unknown Company"
				})
			});

			if (!response.ok) {
				throw new Error("Failed to submit support request");
			}

			setSubmitted(true);
			setTimeout(() => {
				setSubmitted(false);
				form.reset();
				onClose();
			}, 2000);
		} catch (error) {
			console.error("Failed to submit support request:", error);
			// You could add error handling here (toast notification, etc.)
		} finally {
			setLoading(false);
		}
	};

	const handleClose = () => {
		if (!loading) {
			form.reset();
			setSubmitted(false);
			onClose();
		}
	};

	const title = type === "bug" ? "Report a Bug" : "Request a Feature";
	const description =
		type === "bug"
			? "Help us improve by reporting any bugs you've encountered."
			: "Tell us about a feature you'd like to see added.";

	return (
		<Dialog open={open} onOpenChange={(open) => !open && handleClose()}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
					<p className="text-sm text-gray-600 mt-1">{description}</p>
				</DialogHeader>

				{submitted ? (
					<div className="py-8 text-center">
						<div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
							<svg
								className="w-8 h-8 text-green-500"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M5 13l4 4L19 7"
								/>
							</svg>
						</div>
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							Thank you!
						</h3>
						<p className="text-gray-600">
							Your {type === "bug" ? "bug report" : "feature request"} has been
							submitted successfully.
						</p>
					</div>
				) : (
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<div className="grid grid-cols-2 gap-4">
							<div>
								<Label htmlFor="firstName">First Name</Label>
								<Input
									id="firstName"
									{...form.register("firstName")}
									disabled={loading}
								/>
								{form.formState.errors.firstName && (
									<p className="text-sm text-red-600 mt-1">
										{form.formState.errors.firstName.message}
									</p>
								)}
							</div>
							<div>
								<Label htmlFor="lastName">Last Name</Label>
								<Input
									id="lastName"
									{...form.register("lastName")}
									disabled={loading}
								/>
								{form.formState.errors.lastName && (
									<p className="text-sm text-red-600 mt-1">
										{form.formState.errors.lastName.message}
									</p>
								)}
							</div>
						</div>

						<div>
							<Label htmlFor="email">Email</Label>
							<Input
								id="email"
								type="email"
								{...form.register("email")}
								disabled={loading}
							/>
							{form.formState.errors.email && (
								<p className="text-sm text-red-600 mt-1">
									{form.formState.errors.email.message}
								</p>
							)}
						</div>

						<div>
							<Label htmlFor="description">
								{type === "bug" ? "Bug Description" : "Feature Description"}
							</Label>
							<Textarea
								id="description"
								rows={4}
								placeholder={
									type === "bug"
										? "Please describe the bug you encountered, including steps to reproduce it..."
										: "Please describe the feature you'd like to see added..."
								}
								{...form.register("description")}
								disabled={loading}
							/>
							{form.formState.errors.description && (
								<p className="text-sm text-red-600 mt-1">
									{form.formState.errors.description.message}
								</p>
							)}
						</div>

						<DialogFooter className="pt-4">
							<Button
								type="button"
								variant="outline"
								onClick={handleClose}
								disabled={loading}
								className="px-6"
								style={{
									color: company?.brand_color,
									borderColor: company?.brand_color
								}}
							>
								Cancel
							</Button>
							<Button
								type="submit"
								disabled={loading}
								className="px-6"
								style={{
									backgroundColor: company?.brand_color ?? "#2563eb",
									color: "#fff"
								}}
							>
								{loading ? (
									<div className="flex items-center gap-2">
										<div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
										Submitting...
									</div>
								) : (
									"Submit"
								)}
							</Button>
						</DialogFooter>
					</form>
				)}
			</DialogContent>
		</Dialog>
	);
}
