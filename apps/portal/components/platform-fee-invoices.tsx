"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";
import { Check, DollarSign } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";

interface PlatformFeeInvoice {
	id: string;
	invoice_number: number;
	amount: number;
	notes: string;
	created_at: string;
	items: Array<{
		description: string;
		quantity: number;
		unit_price: number;
		amount: number;
	}>;
	warranty_request?: {
		job?: {
			first_name: string;
			last_name: string;
		};
	};
}

export function PlatformFeeInvoices() {
	const [invoices, setInvoices] = useState<PlatformFeeInvoice[]>([]);
	const [loading, setLoading] = useState(true);
	const [approving, setApproving] = useState<string | null>(null);

	const fetchInvoices = useCallback(async () => {
		try {
			const response = await fetch("/api/platform-fee-invoices");
			if (!response.ok)
				throw new Error("Failed to fetch platform fee invoices");

			const data = await response.json();
			setInvoices(data.invoices || []);
		} catch (error) {
			console.error("Error fetching platform fee invoices:", error);
		} finally {
			setLoading(false);
		}
	}, []);

	const approveInvoice = async (invoiceId: string) => {
		setApproving(invoiceId);
		try {
			const response = await fetch(
				`/api/platform-fee-invoices/${invoiceId}/approve`,
				{
					method: "POST"
				}
			);

			if (!response.ok) throw new Error("Failed to approve invoice");

			const data = await response.json();
			if (data.success) {
				toast.success("Platform fee invoice approved and sent!");
				// Remove the approved invoice from the list
				setInvoices((prev) => prev.filter((inv) => inv.id !== invoiceId));
			} else {
				throw new Error(data.error || "Failed to approve invoice");
			}
		} catch (error) {
			console.error("Error approving invoice:", error);
			toast.error("Failed to approve invoice");
		} finally {
			setApproving(null);
		}
	};

	useEffect(() => {
		fetchInvoices();
	}, [fetchInvoices]);

	if (loading) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<DollarSign className="h-5 w-5" />
						RV Help Invoices
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex items-center justify-center py-6">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (invoices.length === 0) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<DollarSign className="h-5 w-5" />
						RV Help Invoices
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-gray-500 text-center py-6">
						No pending RV Help invoices
					</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<DollarSign className="h-5 w-5" />
					RV Help Invoices ({invoices.length} pending)
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					{invoices.map((invoice) => (
						<div key={invoice.id} className="border rounded-lg p-4 bg-white">
							<div className="flex items-center justify-between mb-3">
								<div>
									<h4 className="font-medium text-gray-900">
										Invoice #{invoice.invoice_number}
									</h4>
									<p className="text-sm text-gray-500">RV Help Invoice</p>
								</div>
								<div className="text-right">
									<p className="font-semibold text-lg">
										{formatCurrency(invoice.amount / 100)}
									</p>
									<p className="text-sm text-gray-500">
										{new Date(invoice.created_at).toLocaleDateString()}
									</p>
								</div>
							</div>

							{invoice.notes && (
								<p className="text-sm text-gray-600 mb-3 italic">
									{invoice.notes}
								</p>
							)}

							<div className="mb-4">
								<h5 className="font-medium text-sm text-gray-700 mb-2">
									Line Items:
								</h5>
								<div className="space-y-1">
									{invoice.items.map((item, index) => (
										<div key={index} className="flex justify-between text-sm">
											<span className="text-gray-600">
												{item.description} (x{item.quantity})
											</span>
											<span className="font-medium">
												{formatCurrency(item.amount / 100)}
											</span>
										</div>
									))}
								</div>
							</div>

							<div className="flex justify-end">
								<Button
									onClick={() => approveInvoice(invoice.id)}
									disabled={approving === invoice.id}
									className="bg-green-600 hover:bg-green-700"
								>
									{approving === invoice.id ? (
										<>
											<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
											Approving...
										</>
									) : (
										<>
											<Check className="h-4 w-4 mr-2" />
											Approve & Send
										</>
									)}
								</Button>
							</div>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}
