"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { signIn } from "next-auth/react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";

interface LoginFormData {
	email: string;
	password: string;
}

function LoginForm() {
	const searchParams = useSearchParams();
	const emailVerified = searchParams?.get("emailVerified");
	const verifiedEmail = searchParams?.get("email");

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting }
	} = useForm<LoginFormData>({
		defaultValues: {
			email: verifiedEmail || "",
			password: ""
		}
	});

	const onSubmit = async (data: LoginFormData) => {
		try {
			const result = await signIn("credentials", {
				email: data.email.toLowerCase(),
				password: data.password,
				redirect: false
			});

			if (result?.error) {
				toast.error("Invalid credentials");
				return;
			}

			const loginResult = await fetch("/api/auth/login", {
				method: "POST"
			});

			if (!loginResult.ok) {
				toast.error("Login failed. Please try again.");
				return;
			}

			const redirectUrl = searchParams?.get("redirectUrl");
			window.location.href = redirectUrl || "/dashboard";
		} catch (error) {
			console.error(error);
			toast.error("An error occurred. Please try again.");
		}
	};

	return (
		<div className="relative flex items-center justify-center bg-gray-50">
			<form
				onSubmit={handleSubmit(onSubmit)}
				className={cn(
					"bg-white w-full max-w-lg p-8 rounded-lg shadow-md",
					isSubmitting ? "opacity-50 pointer-events-none" : ""
				)}
			>
				<h1 className="text-2xl font-bold mb-6 text-center">Sign In</h1>
				{emailVerified && verifiedEmail && (
					<p className="text-green-500 text-sm mb-4">
						Email verified successfully! Please login to continue.
					</p>
				)}
				<div className="space-y-4">
					<Input
						{...register("email", {
							required: "Email is required",
							pattern: {
								value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
								message: "Invalid email address"
							}
						})}
						type="email"
						label="Email"
						error={errors.email?.message}
						disabled={isSubmitting}
					/>
					<Input
						{...register("password", {
							required: "Password is required"
						})}
						type="password"
						label="Password"
						error={errors.password?.message}
						disabled={isSubmitting}
					/>
					<Button
						type="submit"
						className="w-full"
						disabled={isSubmitting}
						data-testid="login-button"
					>
						{isSubmitting ? "Signing in..." : "Sign In"}
					</Button>
					<div className="text-center mt-2">
						<Link
							href="/forgot-password"
							className="text-sm text-blue-600 hover:text-blue-800"
						>
							Forgot Password?
						</Link>
					</div>
				</div>
			</form>
		</div>
	);
}

export default function Login() {
	return (
		<Suspense fallback={<div>Loading...</div>}>
			<LoginForm />
		</Suspense>
	);
}
