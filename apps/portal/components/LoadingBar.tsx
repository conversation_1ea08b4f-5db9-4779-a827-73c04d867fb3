'use client';

import { Progress } from '@/components/ui/progress';
import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function LoadingBar() {
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [loading, setLoading] = useState(false);
    const [progress, setProgress] = useState(0);

    useEffect(() => {
        let progressInterval: NodeJS.Timeout;

        const startLoading = () => {
            setLoading(true);
            setProgress(0);
            progressInterval = setInterval(() => {
                setProgress((prev) => {
                    if (prev >= 90) {
                        clearInterval(progressInterval);
                        return 90;
                    }
                    return prev + 10;
                });
            }, 100);
        };

        const stopLoading = () => {
            setProgress(100);
            setTimeout(() => {
                setLoading(false);
                setProgress(0);
            }, 200);
            clearInterval(progressInterval);
        };

        startLoading();
        stopLoading();

        return () => {
            clearInterval(progressInterval);
        };
    }, [pathname, searchParams]);

    if (!loading) return null;

    return (
        <Progress
            value={progress}
            className="fixed top-0 left-0 right-0 z-50 h-1 rounded-none bg-gray-100"
        />
    );
}
