import { ExtendedWarrantyRequest } from "@/types/warranty";
import { Document, Page, StyleSheet, Text, View } from "@react-pdf/renderer";
import type { Style } from "@react-pdf/types";

// Create styles
const styles = StyleSheet.create({
	page: {
		flexDirection: "column",
		backgroundColor: "#ffffff",
		padding: 40,
		fontFamily: "Helvetica",
		color: "#374151"
	} as Style,
	header: {
		marginBottom: 30,
		borderBottom: "1pt solid #e5e7eb",
		paddingBottom: 20
	} as Style,
	title: {
		fontSize: 24,
		fontWeight: "bold",
		marginBottom: 8,
		color: "#111827"
	} as Style,
	subtitle: {
		fontSize: 14,
		color: "#6b7280",
		marginBottom: 4
	} as Style,
	requestInfo: {
		marginBottom: 20,
		padding: 15,
		backgroundColor: "#f8fafc",
		borderRadius: 8
	} as Style,
	infoRow: {
		flexDirection: "row",
		marginBottom: 6
	} as Style,
	infoLabel: {
		fontSize: 12,
		fontWeight: "bold",
		color: "#374151",
		width: "30%"
	} as Style,
	infoValue: {
		fontSize: 12,
		color: "#6b7280",
		width: "70%"
	} as Style,
	historySection: {
		marginTop: 20
	} as Style,
	historyTitle: {
		fontSize: 18,
		fontWeight: "bold",
		marginBottom: 15,
		color: "#111827"
	} as Style,
	timelineItem: {
		marginBottom: 15,
		paddingBottom: 15,
		borderBottom: "1pt solid #f3f4f6"
	} as Style,
	timelineHeader: {
		flexDirection: "row",
		justifyContent: "space-between",
		marginBottom: 8
	} as Style,
	timelineDate: {
		fontSize: 12,
		fontWeight: "bold",
		color: "#374151"
	} as Style,
	timelineStatus: {
		fontSize: 11,
		backgroundColor: "#dbeafe",
		color: "#1e40af",
		padding: "4 8",
		borderRadius: 12,
		textAlign: "center"
	} as Style,
	timelineUpdatedBy: {
		fontSize: 11,
		color: "#6b7280",
		marginBottom: 6
	} as Style,
	timelineNotes: {
		fontSize: 11,
		color: "#374151",
		lineHeight: 1.4,
		fontStyle: "italic"
	} as Style,
	noHistory: {
		textAlign: "center",
		fontSize: 14,
		color: "#9ca3af",
		fontStyle: "italic",
		marginTop: 40
	} as Style,
	footer: {
		position: "absolute",
		bottom: 30,
		left: 40,
		right: 40,
		textAlign: "center",
		fontSize: 10,
		color: "#9ca3af"
	} as Style
});

interface WarrantyHistoryDocumentProps {
	request: ExtendedWarrantyRequest;
	company?: {
		name: string;
		brand_color?: string;
	};
}

export function WarrantyHistoryDocument({
	request,
	company
}: WarrantyHistoryDocumentProps) {
	const formatDate = (dateString: string | Date) => {
		const date = new Date(dateString);
		return date.toLocaleDateString("en-US", {
			year: "numeric",
			month: "long",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		});
	};

	const formatEventType = (eventType: string) => {
		return eventType
			.replace(/_/g, " ")
			.replace(/\b\w/g, (l) => l.toUpperCase());
	};

	return (
		<Document>
			<Page size="A4" style={styles.page}>
				{/* Header */}
				<View style={styles.header}>
					<Text style={styles.title}>Warranty Request History</Text>
					<Text style={styles.subtitle}>Request ID: {request.id}</Text>
					<Text style={styles.subtitle}>
						Generated: {formatDate(new Date())}
					</Text>
				</View>

				{/* Request Information */}
				<View style={styles.requestInfo}>
					<View style={styles.infoRow}>
						<Text style={styles.infoLabel}>Customer:</Text>
						<Text style={styles.infoValue}>
							{request.first_name} {request.last_name}
						</Text>
					</View>
					<View style={styles.infoRow}>
						<Text style={styles.infoLabel}>Email:</Text>
						<Text style={styles.infoValue}>{request.email}</Text>
					</View>
					<View style={styles.infoRow}>
						<Text style={styles.infoLabel}>Phone:</Text>
						<Text style={styles.infoValue}>{request.phone || "N/A"}</Text>
					</View>
					<View style={styles.infoRow}>
						<Text style={styles.infoLabel}>RV Make/Model:</Text>
						<Text style={styles.infoValue}>
							{request.rv_make} {request.rv_model}
						</Text>
					</View>
					<View style={styles.infoRow}>
						<Text style={styles.infoLabel}>Year:</Text>
						<Text style={styles.infoValue}>{request.rv_year}</Text>
					</View>
					<View style={styles.infoRow}>
						<Text style={styles.infoLabel}>VIN:</Text>
						<Text style={styles.infoValue}>{request.rv_vin || "N/A"}</Text>
					</View>
					<View style={styles.infoRow}>
						<Text style={styles.infoLabel}>Current Status:</Text>
						<Text style={styles.infoValue}>
							{formatEventType(request.status)}
						</Text>
					</View>
					{company && (
						<View style={styles.infoRow}>
							<Text style={styles.infoLabel}>Company:</Text>
							<Text style={styles.infoValue}>{company.name}</Text>
						</View>
					)}
				</View>

				{/* History Section */}
				<View style={styles.historySection}>
					<Text style={styles.historyTitle}>Status History</Text>

					{request.timeline_updates && request.timeline_updates.length > 0 ? (
						request.timeline_updates.map((update, index) => (
							<View key={update.id} style={styles.timelineItem}>
								<View style={styles.timelineHeader}>
									<Text style={styles.timelineDate}>
										{formatDate(update.date)}
									</Text>
									<Text style={styles.timelineStatus}>
										{formatEventType(update.event_type)}
									</Text>
								</View>
								<Text style={styles.timelineUpdatedBy}>
									Updated by: {update.updated_by.first_name}{" "}
									{update.updated_by.last_name}
								</Text>
								{update.details?.notes && (
									<Text style={styles.timelineNotes}>
										&quot;{update.details.notes}&quot;
									</Text>
								)}
							</View>
						))
					) : (
						<Text style={styles.noHistory}>
							No status updates available for this warranty request.
						</Text>
					)}
				</View>

				{/* Footer */}
				<Text style={styles.footer}>
					RVHelp Warranty Request History - Page 1
				</Text>
			</Page>
		</Document>
	);
}
