"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
    <PERSON><PERSON><PERSON><PERSON>gle,
    BarChart3,
    CheckCircle,
    Clock,
    CreditCard,
    DollarSign,
    FileText,
    MapPin,
    Package,
    Target,
    Wrench,
    XCircle,
    Zap
} from "lucide-react";
import { useEffect, useState } from "react";

const getStatusInfo = (status: string) => {
    const statusMap: { [key: string]: { label: string; color: string; icon: any; bgColor: string; description: string } } = {
        REQUEST_CREATED: {
            label: "New Requests",
            color: "text-blue-600",
            icon: FileText,
            bgColor: "bg-blue-50",
            description: "New warranty requests awaiting review"
        },
        REQUEST_APPROVED: {
            label: "Pre-approved",
            color: "text-green-600",
            icon: CheckCircle,
            bgColor: "bg-green-50",
            description: "Requests that have been pre-approved"
        },
        REQUEST_REJECTED: {
            label: "Rejected",
            color: "text-red-600",
            icon: XCircle,
            bgColor: "bg-red-50",
            description: "Requests that have been rejected"
        },
        JOB_REGISTERED: {
            label: "Job Registered",
            color: "text-blue-600",
            icon: FileText,
            bgColor: "bg-blue-50",
            description: "Jobs that have been registered"
        },
        JOB_REQUESTED: {
            label: "Provider Invited",
            color: "text-amber-600",
            icon: AlertTriangle,
            bgColor: "bg-amber-50",
            description: "Jobs where providers have been invited"
        },
        JOB_ACCEPTED: {
            label: "Job Accepted",
            color: "text-green-600",
            icon: CheckCircle,
            bgColor: "bg-green-50",
            description: "Jobs that have been accepted"
        },
        JOB_STARTED: {
            label: "In Progress",
            color: "text-blue-600",
            icon: Wrench,
            bgColor: "bg-blue-50",
            description: "Jobs currently in progress"
        },
        JOB_COMPLETED: {
            label: "Completed",
            color: "text-green-600",
            icon: CheckCircle,
            bgColor: "bg-green-50",
            description: "Jobs that have been completed"
        },
        AUTHORIZATION_REQUESTED: {
            label: "Auth Required",
            color: "text-amber-600",
            icon: AlertTriangle,
            bgColor: "bg-amber-50",
            description: "Requests requiring authorization"
        },
        AUTHORIZATION_APPROVED: {
            label: "Auth Approved",
            color: "text-green-600",
            icon: CheckCircle,
            bgColor: "bg-green-50",
            description: "Authorizations that have been approved"
        },
        AUTHORIZATION_REJECTED: {
            label: "Auth Rejected",
            color: "text-red-600",
            icon: XCircle,
            bgColor: "bg-red-50",
            description: "Authorizations that have been rejected"
        },
        AUTHORIZATION_FEEDBACK: {
            label: "Auth Feedback",
            color: "text-amber-600",
            icon: AlertTriangle,
            bgColor: "bg-amber-50",
            description: "Authorizations requiring feedback"
        },
        PARTS_ORDERED: {
            label: "Parts Ordered",
            color: "text-blue-600",
            icon: Package,
            bgColor: "bg-blue-50",
            description: "Parts that have been ordered"
        },
        INVOICE_CREATED: {
            label: "Invoice Created",
            color: "text-amber-600",
            icon: CreditCard,
            bgColor: "bg-amber-50",
            description: "Invoices that have been created"
        },
        INVOICE_PAID: {
            label: "Invoice Paid",
            color: "text-green-600",
            icon: CheckCircle,
            bgColor: "bg-green-50",
            description: "Invoices that have been paid"
        },
        JOB_CANCELLED: {
            label: "Cancelled",
            color: "text-red-600",
            icon: XCircle,
            bgColor: "bg-red-50",
            description: "Jobs that have been cancelled"
        },
    };

    return statusMap[status] || {
        label: status,
        color: "text-gray-600",
        icon: FileText,
        bgColor: "bg-gray-50",
        description: "Requests in this status"
    };
};

interface AnalyticsData {
    performanceMetrics: {
        totalJobsThisMonth: number;
        platformRevenueThisMonth: number;
        allTimePlatformRevenue: number;
        avgTimeToAcceptance: number;
        avgRepairCycleTime: number;
        matchSuccessRate: number;
        avgRegistrationTime: number;
        noMatchCasesThisWeek: number;
        avgDistance: number;
        totalJobsWithDistance: number;
        jobsWithDistance: number;
    };
    oemHealthMetrics: {
        avgServiceCallFee: number;
        avgHourlyRate: number;
        avgHoursPerJob: number;
        avgTotalPerJob: number;
    };
    financialSummary: {
        techPayoutsThisMonth: number;
    };
    pipeline: {
        stats: { [key: string]: number };
        times: { [key: string]: string };
    };
    noMatchAnalysis: Array<{
        id: string;
        customer: string;
        location: string;
        timeInSystem: string;
        reason: string;
        date: string;
        status: string;
    }>;
    topParts: Array<{
        name: string;
        count: number;
    }>;
    trends: {
        monthlyGrowth: string;
        revenueGrowth: string;
        noMatchTrend: string;
    };
}

export function AnalyticsSection({ user }: { user?: { role?: string } }) {
    const [data, setData] = useState<AnalyticsData | null>(null);
    const [showPlatformMetrics, setShowPlatformMetrics] = useState(false);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Set initial state based on user role
        const isAdmin = user?.role === "ADMIN";
        setShowPlatformMetrics(isAdmin);

        // If admin, check localStorage for saved preference
        if (isAdmin && typeof window !== 'undefined') {
            const savedPreference = localStorage.getItem("showPlatformMetrics");
            if (savedPreference !== null) {
                setShowPlatformMetrics(savedPreference === "true");
            }
        }
    }, [user]);

    useEffect(() => {
        const fetchAnalytics = async () => {
            try {
                const response = await fetch("/api/dashboard/analytics");
                if (!response.ok) throw new Error("Failed to fetch analytics");
                const analyticsData = await response.json();
                setData(analyticsData);
            } catch (error) {
                console.error("Error fetching analytics:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchAnalytics();
    }, []);

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {[...Array(8)].map((_, i) => (
                        <Card key={i} className="animate-pulse">
                            <CardContent className="p-6">
                                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        );
    }

    if (!data || !data.performanceMetrics || !data.oemHealthMetrics) {
        return (
            <Card>
                <CardContent className="p-6">
                    <p className="text-center text-gray-500">Failed to load analytics data</p>
                </CardContent>
            </Card>
        );
    }

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    };

    const formatTime = (hours: number) => {
        if (hours < 1) return `${Math.round(hours * 60)}m`;
        if (hours < 24) return `${hours.toFixed(1)}h`;
        return `${(hours / 24).toFixed(1)}d`;
    };

    const togglePlatformMetrics = () => {
        const newValue = !showPlatformMetrics;
        setShowPlatformMetrics(newValue);

        // Only save to localStorage if we're in the browser
        if (typeof window !== 'undefined') {
            localStorage.setItem("showPlatformMetrics", newValue.toString());
        }
    };


    return (
        <TooltipProvider>
            <div className="space-y-6">
                {/* Performance Metrics */}
                <div>
                    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <div className="flex items-center gap-2 w-full">
                            <Target className="h-5 w-5" />
                            <span>RV Help Performance Metrics & Revenue
                            </span>

                        </div>
                        {/* hide platform metrics if user is not admin */}
                        <Button variant="outline" size="sm" onClick={togglePlatformMetrics}>
                            {showPlatformMetrics ? "Hide Platform Metrics" : "Show Platform Metrics"}
                        </Button>
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Card className="bg-gradient-to-br from-blue-600 to-blue-700 text-white">
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <Target className="h-8 w-8 opacity-80 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {data.performanceMetrics?.totalJobsThisMonth || 0}
                                        </div>
                                        <div className="text-sm opacity-90">Total Jobs This Month</div>
                                        <div className="text-xs opacity-75 mt-2 flex items-center gap-1">
                                            📈 {Math.round(((data.performanceMetrics?.totalJobsThisMonth || 0) / 1000) * 100)}% of 1,000 goal
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {showPlatformMetrics && (
                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center gap-4">
                                        <DollarSign className="h-8 w-8 text-green-600 flex-shrink-0" />
                                        <div className="flex-1">
                                            <div className="text-3xl font-bold mb-1">
                                                {formatCurrency(data.performanceMetrics?.platformRevenueThisMonth || 0)}
                                            </div>
                                            <div className="text-sm text-gray-600">Platform Revenue This Month</div>
                                            <div className="text-xs text-gray-500 mt-2">
                                                {data.performanceMetrics?.totalJobsThisMonth || 0} jobs × $50
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {showPlatformMetrics && (
                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center gap-4">
                                        <BarChart3 className="h-8 w-8 text-blue-600 flex-shrink-0" />
                                        <div className="flex-1">
                                            <div className="text-3xl font-bold mb-1">
                                                {formatCurrency(data.performanceMetrics?.allTimePlatformRevenue || 0)}
                                            </div>
                                            <div className="text-sm text-gray-600">All-Time Platform Revenue</div>
                                            <div className="text-xs text-gray-500 mt-2">
                                                {Math.round((data.performanceMetrics?.allTimePlatformRevenue || 0) / 50)} total jobs
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <Clock className="h-8 w-8 text-orange-600 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {formatTime(data.performanceMetrics?.avgTimeToAcceptance || 0)}
                                        </div>
                                        <div className="text-sm text-gray-600">Avg Time to Acceptance</div>
                                        <div className="text-xs text-gray-500 mt-1">Technician invited → accepted</div>
                                        <div className="text-xs text-green-600 mt-2 flex items-center gap-1">
                                            ↓ 12% from last week
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <Package className="h-8 w-8 text-purple-600 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {(data.performanceMetrics?.avgRepairCycleTime || 0).toFixed(1)}d
                                        </div>
                                        <div className="text-sm text-gray-600">RECT (Repair Event Cycle Time)</div>
                                        <div className="text-xs text-gray-500 mt-1">Preauth approved → invoice paid</div>
                                        <div className="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                            Based on timeline data
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <CheckCircle className="h-8 w-8 text-green-600 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {data.performanceMetrics?.matchSuccessRate || 0}%
                                        </div>
                                        <div className="text-sm text-gray-600">Match Success Rate</div>
                                        <div className="text-xs text-red-600 mt-2 flex items-center gap-1">
                                            ↓ 2% from last week
                                        </div>
                                        <div className="text-xs text-gray-500">Goal: 90%+</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <Zap className="h-8 w-8 text-indigo-600 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {formatTime(data.performanceMetrics?.avgRegistrationTime || 0)}
                                        </div>
                                        <div className="text-sm text-gray-600">Avg Registration Time</div>
                                        <div className="text-xs text-gray-500 mt-1">Preauth approved → customer registered</div>
                                        <div className="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                            Based on timeline data
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <XCircle className="h-8 w-8 text-red-600 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {data.performanceMetrics?.noMatchCasesThisWeek || 0}
                                        </div>
                                        <div className="text-sm text-gray-600">No Match Cases This Week</div>
                                        <div className="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                            Based on actual data
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <MapPin className="h-8 w-8 text-blue-600 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {data.performanceMetrics?.avgDistance > 0 ? `${data.performanceMetrics.avgDistance} mi` : "N/A"}
                                        </div>
                                        <div className="text-sm text-gray-600">Avg Tech Travel Distance</div>
                                        <div className="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                            {data.performanceMetrics?.jobsWithDistance || 0} warranty jobs with assigned techs
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* OEM Health Metrics */}
                <div>
                    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <DollarSign className="h-5 w-5" />
                        OEM Partner Health Metrics
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <DollarSign className="h-8 w-8 text-purple-600 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {formatCurrency(data.oemHealthMetrics?.avgServiceCallFee || 0)}
                                        </div>
                                        <div className="text-sm text-gray-600">Avg Service Call Fee</div>
                                        <div className="text-xs text-gray-500">Example data</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <Clock className="h-8 w-8 text-red-600 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {formatCurrency(data.oemHealthMetrics?.avgHourlyRate || 0)}/hr
                                        </div>
                                        <div className="text-sm text-gray-600">Avg Hourly Rate</div>
                                        <div className="text-xs text-gray-500">Example data</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <Package className="h-8 w-8 text-teal-600 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {data.oemHealthMetrics?.avgHoursPerJob || 0}h
                                        </div>
                                        <div className="text-sm text-gray-600">Avg Hours Per Job</div>
                                        <div className="text-xs text-gray-500">Example data</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <DollarSign className="h-8 w-8 text-green-600 flex-shrink-0" />
                                    <div className="flex-1">
                                        <div className="text-3xl font-bold mb-1">
                                            {formatCurrency(data.oemHealthMetrics?.avgTotalPerJob || 0)}
                                        </div>
                                        <div className="text-sm text-gray-600">Avg Total Per Job</div>
                                        <div className="text-xs text-gray-500">Example data</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>


                {/* Job Pipeline */}
                <div>
                    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Job Pipeline Status
                    </h3>
                    <div className="overflow-x-auto pb-4">
                        <div className="flex gap-4 min-w-max">
                            {Object.entries(data.pipeline?.stats || {})
                                .sort(([, a], [, b]) => b - a) // Sort by count in descending order
                                .map(([status, count], index) => {
                                    const statusInfo = getStatusInfo(status);
                                    const StatusIcon = statusInfo.icon;

                                    return (
                                        <div key={status} className="w-[200px] flex-shrink-0">
                                            <Card className="h-full">
                                                <CardContent className="p-4">
                                                    <div className="text-center">
                                                        <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full ${statusInfo.bgColor} mb-3`}>
                                                            <StatusIcon className={`w-6 h-6 ${statusInfo.color}`} />
                                                        </div>
                                                        <div className="text-2xl font-bold text-gray-900 mb-1">
                                                            {count}
                                                        </div>
                                                        <div className="text-sm text-gray-600 mb-2">
                                                            {statusInfo.label}
                                                        </div>
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full inline-flex items-center gap-1 cursor-help">
                                                                    {data.pipeline?.times?.[status] || 'N/A'} ⓘ
                                                                </div>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>{statusInfo.description}</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </div>
                                    );
                                })}
                        </div>
                    </div>
                </div>

                {/* No Match Analysis */}
                <div>
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5" />
                            No Match Analysis
                        </h3>
                        <Button variant="outline" size="sm">View All</Button>
                    </div>
                    <Card>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="bg-blue-600 text-white">
                                            <th className="px-4 py-3 text-left font-medium">Job ID</th>
                                            <th className="px-4 py-3 text-left font-medium">Customer</th>
                                            <th className="px-4 py-3 text-left font-medium">Location</th>
                                            <th className="px-4 py-3 text-left font-medium">Time in System</th>
                                            <th className="px-4 py-3 text-left font-medium">Reason</th>
                                            <th className="px-4 py-3 text-left font-medium">Date</th>
                                            <th className="px-4 py-3 text-left font-medium">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {(data.noMatchAnalysis || []).slice(0, 5).map((item) => (
                                            <tr key={item.id} className="border-b hover:bg-gray-50">
                                                <td className="px-4 py-3 text-blue-600 font-semibold">
                                                    {item.id.slice(-4).toUpperCase()}
                                                </td>
                                                <td className="px-4 py-3">{item.customer}</td>
                                                <td className="px-4 py-3">{item.location}</td>
                                                <td className="px-4 py-3">{item.timeInSystem}</td>
                                                <td className="px-4 py-3">
                                                    <select className="border border-gray-300 rounded px-2 py-1 text-sm">
                                                        <option>No technicians in 100-mile radius</option>
                                                        <option>All techs declined</option>
                                                        <option>Specialized equipment required</option>
                                                        <option>Customer cancelled</option>
                                                        <option>Parts unavailable</option>
                                                    </select>
                                                </td>
                                                <td className="px-4 py-3">
                                                    {new Date(item.date).toLocaleDateString()}
                                                </td>
                                                <td className="px-4 py-3">
                                                    <Badge variant="destructive">No Match</Badge>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </TooltipProvider>
    );
}
