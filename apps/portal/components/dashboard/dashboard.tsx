"use client";


import OEM<PERSON><PERSON> from "@/components/oem-logo";

import { SupportModal } from "@/components/support/support-modal";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ApprovalModal } from "@/components/warranty/approval-modal";
import { ComponentManagerDialog } from "@/components/warranty/component-manager/component-manager-dialog";
import { InvoicePaymentModal } from "@/components/warranty/invoice-payment-modal";
import WarrantyRequestCardDialog from "@/components/warranty/warranty-request-card/warranty-request-card-dialog";
import WarrantyRequestWizardDialog from "@/components/warranty/warranty-request-wizard/warranty-request-wizard-dialog";
import { isPendingApproval } from "@/components/warranty/warranty-utils";
import WarrantyRequestTableRow from "@/components/warranty/WarrantyRequestTableRow";
import type { User } from "@/types/global";
import {
	ExtendedCompany,
	ExtendedComponent,
	ExtendedWarrantyRequest
} from "@/types/warranty";
import {
	Activity,
	AlertCircle,
	Bug,
	CheckCircle,
	DollarSign,
	FileText,
	Lightbulb,
	Package,
	Plus,
	Settings,
	TrendingUp
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface DashboardStats {
	totalRequests: number;
	pendingRequests: number;
	last30Days: number;
	completedRequests: number;
	averageResolutionTime: number;
	requestsByStatus: { [key: string]: number };
}

interface ComponentBreakdown {
	component_id: string;
	type: string;
	manufacturer: string;
	count: number;
}

interface DashboardData {
	stats: DashboardStats;
	pendingRequests: ExtendedWarrantyRequest[];
	company: ExtendedCompany;
	componentBreakdown: ComponentBreakdown[];
}

function DashboardSkeleton() {
	return (
		<div className="space-y-6">
			{/* Header Skeleton */}
			<div className="flex justify-center gap-4 mb-8">
				<div className="animate-pulse">
					<div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
				</div>
				<div className="pt-8">
					<div className="animate-pulse">
						<div className="h-8 bg-gray-200 rounded w-32 mb-2"></div>
						<div className="h-5 bg-gray-200 rounded w-64"></div>
					</div>
				</div>
			</div>

			{/* Quick Actions Skeleton */}
			<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
				{[...Array(4)].map((_, i) => (
					<div key={i} className="animate-pulse">
						<div className="h-20 md:h-24 w-full bg-gray-200 rounded-lg"></div>
					</div>
				))}
			</div>

			{/* Statistics Cards Skeleton */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				{[...Array(4)].map((_, i) => (
					<Card key={i} className="animate-pulse">
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<div className="h-4 bg-gray-200 rounded w-20"></div>
							<div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
						</CardHeader>
						<CardContent>
							<div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
							<div className="h-3 bg-gray-200 rounded w-24"></div>
						</CardContent>
					</Card>
				))}
			</div>

			{/* Pending Requests Skeleton */}
			<Card>
				<CardHeader>
					<div className="animate-pulse">
						<div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
						<div className="h-4 bg-gray-200 rounded w-64"></div>
					</div>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{[...Array(3)].map((_, i) => (
							<div key={i} className="animate-pulse">
								<div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
									<div className="flex-1">
										<div className="flex items-center gap-3 mb-2">
											<div className="w-16 h-6 bg-gray-200 rounded"></div>
											<div>
												<div className="h-5 bg-gray-200 rounded w-32 mb-1"></div>
												<div className="h-4 bg-gray-200 rounded w-48"></div>
											</div>
										</div>
										<div className="flex items-center gap-4">
											<div className="h-5 bg-gray-200 rounded w-20"></div>
											<div className="h-4 bg-gray-200 rounded w-24"></div>
										</div>
									</div>
									<div className="w-16 h-8 bg-gray-200 rounded"></div>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Status Breakdown Skeleton */}
			<Card>
				<CardHeader>
					<div className="animate-pulse">
						<div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
						<div className="h-4 bg-gray-200 rounded w-64"></div>
					</div>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						{[...Array(6)].map((_, i) => (
							<div key={i} className="animate-pulse">
								<div className="p-3 bg-gray-50 rounded-lg">
									<div className="flex items-center justify-between">
										<div className="h-4 bg-gray-200 rounded w-20"></div>
										<div className="h-6 bg-gray-200 rounded w-8"></div>
									</div>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Component Breakdown Skeleton */}
			<Card>
				<CardHeader>
					<div className="animate-pulse">
						<div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
						<div className="h-4 bg-gray-200 rounded w-64"></div>
					</div>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="animate-pulse">
								<div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
									<div className="flex items-center gap-3">
										<div className="w-8 h-8 bg-gray-200 rounded-full"></div>
										<div>
											<div className="h-5 bg-gray-200 rounded w-24 mb-1"></div>
											<div className="h-4 bg-gray-200 rounded w-20"></div>
										</div>
									</div>
									<div className="text-right">
										<div className="h-6 bg-gray-200 rounded w-8 mb-1"></div>
										<div className="h-3 bg-gray-200 rounded w-12"></div>
									</div>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}

export function Dashboard({ user }: { user: User }) {
	const router = useRouter();
	const [data, setData] = useState<DashboardData | null>(null);
	const [loading, setLoading] = useState(true);
	const [componentManagerOpen, setComponentManagerOpen] = useState(false);
	const [extendedCompany, setExtendedCompany] =
		useState<ExtendedCompany | null>(null);
	const [components, setComponents] = useState<ExtendedComponent[]>([]);
	const [newRequestModalOpen, setNewRequestModalOpen] = useState(false);

	// Add state for status update and view modals
	const [viewModalOpen, setViewModalOpen] = useState(false);
	const [authorizationModalOpen, setAuthorizationModalOpen] = useState(false);
	const [invoicePaymentModalOpen, setInvoicePaymentModalOpen] = useState(false);
	const [selectedRequest, setSelectedRequest] =
		useState<ExtendedWarrantyRequest | null>(null);
	const [deletingRequestId, setDeletingRequestId] = useState<string | null>(null);

	// Support modal states
	const [bugReportModalOpen, setBugReportModalOpen] = useState(false);
	const [featureRequestModalOpen, setFeatureRequestModalOpen] = useState(false);

	useEffect(() => {
		const fetchDashboardData = async () => {
			try {
				const response = await fetch("/api/dashboard");
				if (!response.ok) throw new Error("Failed to fetch dashboard data");
				const dashboardData = await response.json();
				setData(dashboardData);
			} catch (error) {
				console.error("Error fetching dashboard data:", error);
			} finally {
				setLoading(false);
			}
		};

		fetchDashboardData();
	}, []);

	// Fetch company data with components when needed
	const fetchCompanyData = async () => {
		if (!data?.company?.id) return;

		try {
			const response = await fetch(`/api/companies/${data.company.id}`);
			if (response.ok) {
				const companyData = await response.json();
				setExtendedCompany(companyData);
				setComponents(companyData.components || []);
			}
		} catch (error) {
			console.error("Failed to fetch company data:", error);
		}
	};

	const handleComponentManagerClick = async () => {
		await fetchCompanyData();
		setComponentManagerOpen(true);
	};

	const handleComponentsUpdate = async (
		updatedComponents: ExtendedComponent[]
	) => {
		if (!data?.company?.id) return;

		const newComponents = updatedComponents.filter((c) => !c.id);
		const existingComponents = updatedComponents.filter((c) => c.id);

		const requests: Promise<Response>[] = [];
		const requestTypes: ("POST" | "PUT")[] = [];

		if (newComponents.length > 0) {
			requests.push(
				fetch(`/api/companies/${data.company.id}/components`, {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify(
						newComponents.length === 1 ? newComponents[0] : newComponents
					)
				})
			);
			requestTypes.push("POST");
		}

		if (existingComponents.length > 0) {
			requests.push(
				fetch(`/api/companies/${data.company.id}/components`, {
					method: "PUT",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify(
						existingComponents.length === 1
							? existingComponents[0]
							: existingComponents
					)
				})
			);
			requestTypes.push("PUT");
		}

		try {
			const responses = await Promise.all(requests);
			let allUpdatedComponents = [...components];

			for (let i = 0; i < responses.length; i++) {
				const res = responses[i];
				if (!res.ok) {
					const error = await res.json();
					throw new Error(error.error || "Failed to update components");
				}
				const responseData = await res.json();

				if (requestTypes[i] === "POST") {
					const created = Array.isArray(responseData.results)
						? responseData.results
							.filter((r) => r.success)
							.map((r) => r.component)
						: responseData.success
							? [responseData.component]
							: [];

					allUpdatedComponents = [
						...allUpdatedComponents.filter(
							(c) =>
								!newComponents.some(
									(nc) =>
										!nc.id &&
										c.type === nc.type &&
										c.manufacturer === nc.manufacturer
								)
						),
						...created
					];
				} else if (requestTypes[i] === "PUT") {
					const updated = Array.isArray(responseData.results)
						? responseData.results
							.filter((r) => r.success)
							.map((r) => r.updated)
						: responseData.success
							? [responseData.updated]
							: [];

					updated.forEach((upd) => {
						const idx = allUpdatedComponents.findIndex((c) => c.id === upd.id);
						if (idx !== -1) {
							allUpdatedComponents[idx] = upd;
						}
					});
				}
			}

			setComponents(allUpdatedComponents);
			if (extendedCompany) {
				setExtendedCompany({
					...extendedCompany,
					components: allUpdatedComponents
				});
			}
		} catch (error) {
			console.error("Failed to update components:", error);
			throw error;
		}
	};

	const handleNewRequestSuccess = () => {
		setNewRequestModalOpen(false);
		// Refresh the dashboard data
		const fetchDashboardData = async () => {
			try {
				const response = await fetch("/api/dashboard");
				if (!response.ok) throw new Error("Failed to fetch dashboard data");
				const dashboardData = await response.json();
				setData(dashboardData);
			} catch (error) {
				console.error("Error fetching dashboard data:", error);
			}
		};
		fetchDashboardData();
	};

	// Handle view action
	const handleView = (request: ExtendedWarrantyRequest) => {
		setSelectedRequest(request);
		setViewModalOpen(true);
	};

	// Handle update status action
	const handleUpdateStatus = (request: ExtendedWarrantyRequest) => {
		if (!isPendingApproval(request.status, user?.role)) {
			return;
		}
		setSelectedRequest(request);
		if (request.status === "AUTHORIZATION_REQUESTED") {
			setAuthorizationModalOpen(true);
		} else if (request.status === "INVOICE_CREATED") {
			setInvoicePaymentModalOpen(true);
		}
	};

	// Handle invoice payment
	const handlePayInvoice = (request: ExtendedWarrantyRequest) => {
		setSelectedRequest(request);
		setInvoicePaymentModalOpen(true);
	};

	// Handle delete warranty request
	const handleDelete = async (requestId: string) => {
		setDeletingRequestId(requestId);
		try {
			const response = await fetch(`/api/warranty-requests/${requestId}/delete`, {
				method: 'DELETE',
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to delete warranty request');
			}

			// Refresh the dashboard data after deletion
			handleStatusUpdated();

		} catch (error) {
			console.error('Error deleting warranty request:', error);
			throw error; // Re-throw to let the component handle the toast
		} finally {
			setDeletingRequestId(null);
		}
	};

	// Handle status updated callback
	const handleStatusUpdated = () => {
		// Refresh the dashboard data
		const fetchDashboardData = async () => {
			try {
				const response = await fetch("/api/dashboard");
				if (!response.ok) throw new Error("Failed to fetch dashboard data");
				const dashboardData = await response.json();
				setData(dashboardData);
			} catch (error) {
				console.error("Error fetching dashboard data:", error);
			}
		};
		fetchDashboardData();
	};

	if (loading) {
		return <DashboardSkeleton />;
	}

	if (!data) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-center">
					<h2 className="text-xl font-semibold mb-2">
						Unable to load dashboard
					</h2>
					<p className="text-gray-600">Please try refreshing the page</p>
				</div>
			</div>
		);
	}

	const { stats, pendingRequests, company, componentBreakdown } = data;

	const quickActions = [
		{
			title: "View Requests",
			description: "View all warranty requests",
			icon: FileText,
			href: "/service-requests",
			color: company?.brand_color || "#2563eb",
			disabled: false
		},
		{
			title: "New Request",
			description: "Create a new warranty request",
			icon: Plus,
			onClick: () => setNewRequestModalOpen(true),
			color: company?.brand_color || "#2563eb",
			disabled: false
		},
		{
			title: "Status Overview",
			description: "View all requests by status",
			icon: FileText,
			href: "/status",
			color: company?.brand_color || "#2563eb",
			disabled: false,
			hidden: user?.role !== "ADMIN"
		},
		{
			title: "Manage Components",
			description: "Manage warranty components",
			icon: Settings,
			onClick: handleComponentManagerClick,
			color: company?.brand_color || "#2563eb",
			disabled: false
		},
		{
			title: "Reports",
			description: "View warranty reports",
			icon: Activity,
			href: "/reports",
			color: company?.brand_color || "#2563eb",
			disabled: false,
			hidden: user?.role !== "ADMIN"
		},
		{
			title: "Platform Fee Reports",
			description: "View platform fee reports for QuickBooks",
			icon: DollarSign,
			href: "/platform-fee-reports",
			color: company?.brand_color || "#2563eb",
			disabled: false,
			hidden: user?.role !== "ADMIN"
		}
	];

	const supportActions = [
		{
			title: "Report Bug",
			description: "Report a bug or issue",
			icon: Bug,
			onClick: () => setBugReportModalOpen(true),
			color: "#ef4444",
			disabled: false
		},
		{
			title: "Request Feature",
			description: "Request a new feature",
			icon: Lightbulb,
			onClick: () => setFeatureRequestModalOpen(true),
			color: "#f59e0b",
			disabled: false
		}
	];

	const statCards = [
		{
			title: "Total Requests",
			value: stats.totalRequests.toLocaleString(),
			description: "All warranty requests",
			icon: FileText,
			color: "blue"
		},
		{
			title: "Pending Action",
			value: stats.pendingRequests.toLocaleString(),
			description: "Requests requiring attention",
			icon: AlertCircle,
			color: "amber"
		},
		{
			title: "Last 30 Days",
			value: stats.last30Days.toLocaleString(),
			description: "New requests this month",
			icon: TrendingUp,
			color: "emerald"
		},
		{
			title: "Completed",
			value: stats.completedRequests.toLocaleString(),
			description: "Successfully resolved",
			icon: CheckCircle,
			color: "green"
		}
	];

	const getColorClasses = (color: string) => {
		const colorMap = {
			blue: "bg-blue-100 text-blue-600",
			amber: "bg-amber-100 text-amber-600",
			emerald: "bg-emerald-100 text-emerald-600",
			green: "bg-green-100 text-green-600",
			purple: "bg-purple-100 text-purple-600",
			red: "bg-red-100 text-red-600"
		};
		return colorMap[color] || "bg-gray-100 text-gray-600";
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-center gap-4 mb-8">
				<OEMLogo company={company} />
				<div className="pt-8">
					<h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
					<p className="text-gray-600">Warranty request management overview</p>
				</div>
			</div>





			{/* Quick Actions */}
			<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
				{quickActions.filter(action => !action.hidden).map((action) => (
					<div key={action.title} className="relative">
						{action.disabled ? (
							<div className="relative">
								<Button
									variant="outline"
									className="h-20 md:h-24 w-full bg-gray-100 border-gray-300 cursor-not-allowed opacity-60 transition-all duration-200"
									disabled
								>
									<div className="flex flex-col items-center">
										<action.icon className="h-5 w-5 md:h-6 md:w-6 mb-1 md:mb-2 text-gray-400" />
										<span className="text-sm md:text-base font-medium text-center text-gray-400">
											{action.title}
										</span>
										<span className="text-xs text-gray-500 text-center mt-1">
											{action.description}
										</span>
									</div>
								</Button>
								{/* Coming Soon Banner */}
								<div className="absolute top-2 left-1/2 transform -translate-x-1/2 pointer-events-none z-5">
									<div className="bg-gradient-to-r from-orange-400/80 to-red-500/80 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg backdrop-blur-sm">
										Coming Soon
									</div>
								</div>
							</div>
						) : action.href ? (
							<Link href={action.href} className="block">
								<Button
									variant="outline"
									className="h-20 md:h-24 w-full bg-white hover:bg-gray-50 border-gray-200 hover:border-gray-300 transition-all duration-200"
								>
									<div className="flex flex-col items-center">
										<action.icon
											className="h-5 w-5 md:h-6 md:w-6 mb-1 md:mb-2"
											style={{ color: action.color }}
										/>
										<span className="text-sm md:text-base font-medium text-center">
											{action.title}
										</span>
										<span className="text-xs text-gray-500 text-center mt-1">
											{action.description}
										</span>
									</div>
								</Button>
							</Link>
						) : (
							<Button
								variant="outline"
								className="h-20 md:h-24 w-full bg-white hover:bg-gray-50 border-gray-200 hover:border-gray-300 transition-all duration-200"
								onClick={action.onClick}
							>
								<div className="flex flex-col items-center">
									<action.icon
										className="h-5 w-5 md:h-6 md:w-6 mb-1 md:mb-2"
										style={{ color: action.color }}
									/>
									<span className="text-sm md:text-base font-medium text-center">
										{action.title}
									</span>
									<span className="text-xs text-gray-500 text-center mt-1">
										{action.description}
									</span>
								</div>
							</Button>
						)}
					</div>
				))}
			</div>

			{/* Support Actions */}
			<div className="grid grid-cols-2 gap-4">
				{supportActions.map((action) => (
					<div key={action.title} className="relative">
						<Button
							variant="outline"
							className="h-16 w-full bg-white hover:bg-gray-50 border-gray-200 hover:border-gray-300 transition-all duration-200"
							onClick={action.onClick}
						>
							<div className="flex items-center gap-3">
								<action.icon
									className="h-5 w-5"
									style={{ color: action.color }}
								/>
								<div className="text-left">
									<span className="text-sm font-medium block">
										{action.title}
									</span>
									<span className="text-xs text-gray-500">
										{action.description}
									</span>
								</div>
							</div>
						</Button>
					</div>
				))}
			</div>

			{/* Statistics Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				{statCards.map((stat) => (
					<Card
						key={stat.title}
						className="hover:shadow-md transition-shadow duration-200"
					>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								{stat.title}
							</CardTitle>
							<div className={`p-2 rounded-lg ${getColorClasses(stat.color)}`}>
								<stat.icon className="h-4 w-4" />
							</div>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-gray-900">
								{stat.value}
							</div>
							<p className="text-xs text-gray-500 mt-1">{stat.description}</p>
						</CardContent>
					</Card>
				))}
			</div>

			{/* Pending Requests Section */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<AlertCircle className="h-5 w-5 text-amber-600" />
						Service Requests Requiring Action
					</CardTitle>
					<CardDescription>
						Warranty requests that need your immediate attention
					</CardDescription>
				</CardHeader>
				<CardContent>
					{pendingRequests.length === 0 ? (
						<div className="text-center py-8">
							<CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
							<h3 className="text-lg font-medium text-gray-900 mb-2">
								All caught up!
							</h3>
							<p className="text-gray-600">
								No warranty requests require immediate action.
							</p>
						</div>
					) : (
						<div className="space-y-4">
							<div className="rounded-lg border border-gray-200 shadow-sm bg-white overflow-hidden">
								<TooltipProvider>
									<Table>
										<TableHeader
											style={{
												backgroundColor: company?.brand_color ?? "#2563eb"
											}}
										>
											<TableRow>
												<TableHead className="text-white font-semibold">
													ID/Status
												</TableHead>
												<TableHead className="text-white font-semibold">
													Customer
												</TableHead>
												<TableHead className="text-white font-semibold">
													Issue
												</TableHead>
												<TableHead className="text-white font-semibold">
													RV Details
												</TableHead>
												<TableHead className="text-white font-semibold">
													Hours
												</TableHead>
												<TableHead className="text-white font-semibold">
													Submitted
												</TableHead>
												<TableHead className="text-white font-semibold">
													Actions
												</TableHead>
											</TableRow>
										</TableHeader>
										<TableBody>
											{pendingRequests.slice(0, 3).map((request, index) => (
												<WarrantyRequestTableRow
													key={request.id}
													request={request}
													company={company}
													index={index}
													onView={handleView}
													onUpdateStatus={handleUpdateStatus}
													onPayInvoice={handlePayInvoice}
													onDelete={handleDelete}
													user={user}
													isDeleting={deletingRequestId === request.id}
												/>
											))}
											{/* Fill remaining rows to maintain consistent height */}
											{Array.from(
												{ length: Math.max(0, 3 - pendingRequests.length) },
												(_, index) => (
													<TableRow
														key={`empty-${index}`}
														className={`h-16 ${index % 2 === 0 ? "bg-gray-50" : "bg-gray-100"}`}
													>
														<TableCell
															colSpan={7}
															className="text-center text-muted-foreground"
														>
															{/* Empty row */}
														</TableCell>
													</TableRow>
												)
											)}
										</TableBody>
									</Table>
								</TooltipProvider>
							</div>

							{pendingRequests.length > 3 && (
								<div className="flex justify-center">
									<Button
										type="button"
										variant="outline"
										className="px-6"
										style={{
											color: company?.brand_color,
											borderColor: company?.brand_color
										}}
										onClick={() => {
											// Navigate to service-requests page with pending status filters
											const pendingStatuses = [
												"AUTHORIZATION_REQUESTED",
												"INVOICE_CREATED"
											];
											const statusFilter = JSON.stringify(pendingStatuses);
											router.push(
												`/service-requests?status=${encodeURIComponent(statusFilter)}`
											);
										}}
									>
										View All Pending Requests ({pendingRequests.length})
									</Button>
								</div>
							)}
						</div>
					)}
				</CardContent>
			</Card>

			{/* Status Breakdown and Component Breakdown - Side by Side */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Status Breakdown */}
				<Card>
					<CardHeader>
						<div className="flex items-center justify-between">
							<div>
								<CardTitle className="flex items-center gap-2">
									<Activity className="h-5 w-5 text-green-600" />
									Request Status Breakdown
								</CardTitle>
								<CardDescription>
									Overview of warranty requests by current status
								</CardDescription>
							</div>
							<Button
								variant="outline"
								size="sm"
								onClick={() => router.push('/status')}
								style={{
									borderColor: company?.brand_color || "#2563eb",
									color: company?.brand_color || "#2563eb"
								}}
							>
								View All
							</Button>
						</div>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{Object.entries(stats.requestsByStatus).map(([status, count]) => (
								<div key={status} className="p-3 bg-gray-50 rounded-lg">
									<div className="flex items-center justify-between">
										<span className="text-sm text-gray-600">
											{status
												.replace(/_/g, " ")
												.toLowerCase()
												.replace(/\b\w/g, (l) => l.toUpperCase())}
										</span>
										<span className="text-lg font-semibold text-gray-900">
											{count}
										</span>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>

				{/* Component Breakdown */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Package className="h-5 w-5 text-blue-600" />
							Component Breakdown
						</CardTitle>
						<CardDescription>
							Warranty requests by component type
						</CardDescription>
					</CardHeader>
					<CardContent>
						{componentBreakdown && componentBreakdown.length > 0 ? (
							<div className="space-y-3">
								{componentBreakdown.slice(0, 10).map((component, index) => (
									<div
										key={component.component_id}
										className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
									>
										<div className="flex items-center gap-3">
											<div
												className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium text-white"
												style={{
													backgroundColor: company?.brand_color || "#2563eb"
												}}
											>
												{index + 1}
											</div>
											<div>
												<h4 className="font-medium text-gray-900">
													{component.type}
												</h4>
												<p className="text-sm text-gray-600">
													{component.manufacturer}
												</p>
											</div>
										</div>
										<div className="text-right">
											<div className="text-lg font-semibold text-gray-900">
												{component.count}
											</div>
											<div className="text-xs text-gray-500">
												{component.count === 1 ? "request" : "requests"}
											</div>
										</div>
									</div>
								))}

								{componentBreakdown.length > 10 && (
									<div className="text-center pt-4">
										<span className="text-sm text-gray-500">
											Showing top 10 of {componentBreakdown.length} components
										</span>
									</div>
								)}
							</div>
						) : (
							<div className="text-center py-8">
								<Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
								<h3 className="text-lg font-medium text-gray-900 mb-2">
									No Component Data
								</h3>
								<p className="text-gray-600">
									Component breakdown will appear here once warranty requests
									are submitted with component information.
								</p>
							</div>
						)}
					</CardContent>
				</Card>
			</div>



			{/* Component Manager Modal */}
			{extendedCompany && (
				<ComponentManagerDialog
					open={componentManagerOpen}
					onClose={() => setComponentManagerOpen(false)}
					components={components}
					onUpdate={handleComponentsUpdate}
					company={extendedCompany}
				/>
			)}

			{/* New Request Dialog */}
			{data?.company && (
				<WarrantyRequestWizardDialog
					open={newRequestModalOpen}
					onClose={() => setNewRequestModalOpen(false)}
					company={data.company}
					onSuccess={handleNewRequestSuccess}
				/>
			)}

			{/* View Request Dialog */}
			{selectedRequest && data?.company && (
				<WarrantyRequestCardDialog
					open={viewModalOpen}
					onClose={() => setViewModalOpen(false)}
					company={data.company}
					request={selectedRequest}
					onStatusUpdated={handleStatusUpdated}
					user={user}
				/>
			)}

			{/* Status Update Modals */}
			{selectedRequest && data?.company && (
				<>
					<ApprovalModal
						open={authorizationModalOpen}
						onClose={() => setAuthorizationModalOpen(false)}
						request={selectedRequest}
						onStatusUpdated={() => {
							setAuthorizationModalOpen(false);
							handleStatusUpdated();
						}}
						company={data.company}
					/>
					<InvoicePaymentModal
						open={invoicePaymentModalOpen}
						onClose={() => setInvoicePaymentModalOpen(false)}
						request={selectedRequest}
						onStatusUpdated={() => {
							setInvoicePaymentModalOpen(false);
							handleStatusUpdated();
						}}
						company={data.company}
					/>
				</>
			)}

			{/* Support Modals */}
			{data?.company && (
				<>
					<SupportModal
						open={bugReportModalOpen}
						onClose={() => setBugReportModalOpen(false)}
						type="bug"
						company={data.company}
					/>

					<SupportModal
						open={featureRequestModalOpen}
						onClose={() => setFeatureRequestModalOpen(false)}
						type="feature"
						company={data.company}
					/>
				</>
			)}
		</div>
	);
}
