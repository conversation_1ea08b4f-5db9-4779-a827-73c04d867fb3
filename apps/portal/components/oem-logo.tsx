import { cn } from "@/lib/utils";
import { Company } from "@rvhelp/database";
import Image from "next/image";

export default function OEMLogo({
	className = "",
	company,
	alt_logo = false
}: {
	className?: string;
	company: Company;
	alt_logo?: boolean;
}) {
	if (!company) {
		return null;
	}

	const logoUrl = alt_logo
		? company.logo_url_alt || company.logo_url
		: company.logo_url;
	const isSvg = logoUrl?.toLowerCase().endsWith(".svg");
	const finalClassName = className
		? className
		: company.logo_class_name
			? company.logo_class_name
			: "h-12 w-auto";

	return (
		<div
			className={cn(
				"flex flex-col items-center justify-center h-20 w-auto pt-6"
			)}
		>
			{isSvg ? (
				<img
					src={logoUrl}
					alt={`${company.name} Logo`}
					className={finalClassName}
				/>
			) : (
				<Image
					src={logoUrl}
					alt={`${company.name} Logo`}
					width={160}
					height={60}
					className={finalClassName}
					priority
				/>
			)}
		</div>
	);
}
