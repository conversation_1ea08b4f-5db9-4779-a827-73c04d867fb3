import config from '@/config';
import { cn } from '@/lib/utils';
import Image from 'next/image';

export default function ApplicationLogo({ className = '', textWhite = false }) {
    return (
        <span
            className={cn(
                'h-8 text-2xl w-auto font-bold uppercase',
                textWhite ? 'text-white' : 'text-black',
                className
            )}
        >
            {/* RV Help */}
            <Image
                src={`${config.assetsUrl}/public/logo.webp`}
                alt="RV Help Logo"
                className="object-contain"
                priority
                width={160}
                height={60}
            />
        </span>
    );
}
