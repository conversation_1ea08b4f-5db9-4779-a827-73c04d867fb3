import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import * as React from 'react';

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
    error?: string | null;
    label?: string | null;
    required?: boolean;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
    ({ error = null, label = null, required = false, className, ...props }, ref) => {
        return (
            <div className="flex flex-col gap-2">
                {label && <Label required={required}>{label}</Label>}
                {/* {label && <label className="text-sm font-medium">{label}</label>} */}
                <textarea
                    className={cn(
                        'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50',
                        className,
                        error
                            ? 'border-red-500'
                            : 'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
                    )}
                    ref={ref}
                    {...props}
                />
                {error && (
                    <p className="text-sm text-red-500" data-testid={`error-${props.name}`}>
                        {error}
                    </p>
                )}
            </div>
        );
    }
);

Textarea.displayName = 'Textarea';

export { Textarea };
