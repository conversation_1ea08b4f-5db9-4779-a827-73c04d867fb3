import { Slot } from '@radix-ui/react-slot';
import { cva, VariantProps } from 'class-variance-authority';
import React from 'react';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
    'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
    {
        variants: {
            ghost: {
                true: 'bg-transparent',
            },
            variant: {
                default: 'bg-primary text-primary-foreground hover:bg-primary/90 font-semibold',
                destructive:
                    'bg-destructive text-destructive-foreground hover:bg-destructive/90 font-semibold',
                outline:
                    'border border-primary text-primary bg-background hover:bg-accent hover:text-accent-foreground',
                secondary: 'bg-secondary text-white hover:bg-secondary/80 font-semibold',
                ghost: 'hover:bg-accent hover:text-accent-foreground font-semibold',
                link: 'text-primary underline-offset-4 hover:underline font-semibold',
            },
            size: {
                default: 'h-10 px-4 py-2',
                sm: 'h-7 rounded-md px-3 text-xs',
                lg: 'h-11 rounded-md px-8 text-base',
                icon: 'h-10 w-10',
            },
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
        },
    }
);

interface ButtonProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement>,
        VariantProps<typeof buttonVariants> {
    asChild?: boolean;
    loading?: boolean;
}

const Button = React.forwardRef(
    (
        { className, variant, size, asChild = false, loading = false, ...props }: ButtonProps,
        ref
    ) => {
        const Comp = asChild ? Slot : 'button';
        return (
            <Comp
                className={cn(buttonVariants({ variant, size, className }))}
                ref={ref as React.LegacyRef<HTMLButtonElement>}
                {...props}
                disabled={loading || props.disabled}
            />
        );
    }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
