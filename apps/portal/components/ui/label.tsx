'use client';

import * as LabelPrimitive from '@radix-ui/react-label';
import { cva } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

const labelVariants = cva(
    'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
);

const Label = React.forwardRef(
    (
        {
            className,
            required = false,
            ...props
        }: React.ComponentPropsWithoutRef<'label'> & { required?: boolean },
        ref
    ) => (
        <LabelPrimitive.Root
            ref={ref as React.LegacyRef<HTMLLabelElement>}
            className={cn(required && 'flex items-center space-x-1', labelVariants(), className)}
            {...props}
        >
            <span>{props.children}</span>
            {required && <span className="text-red-500 text-xs">*</span>}
        </LabelPrimitive.Root>
    )
);
Label.displayName = LabelPrimitive.Root.displayName;

export { Label };
