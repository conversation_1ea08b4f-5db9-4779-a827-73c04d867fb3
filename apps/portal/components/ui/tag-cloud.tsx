import React from 'react';

interface TagCloudProps {
    tags: string[];
}

export const TagCloud: React.FC<TagCloudProps> = ({ tags }) => {
    // cursor-pointer hover:bg-secondary/90
    return (
        <div className="flex flex-wrap gap-2">
            {tags.map((tag, index) => (
                <span
                    key={index}
                    className="px-3 py-1 bg-secondary text-white rounded-full text-sm"
                >
                    {tag}
                </span>
            ))}
        </div>
    );
};
