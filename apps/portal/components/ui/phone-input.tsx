// react-phone-number-input
import "react-phone-number-input/style.css";

import { cn } from "@/lib/utils";
import ReactPhoneNumberInput from "react-phone-number-input";
import { Label } from "./label";

export default function PhoneInput({
    label,
    value,
    onChange,
    error,
    warning,
    className,
    name,
    required = false,
    defaultCountry = "US",
    disabled = false
}: {
    label?: string;
    value: string;
    onChange: (value: string) => void;
    error: string | undefined;
    warning?: string | undefined;
    className: string;
    required?: boolean;
    name: string;
    defaultCountry?: string;
    disabled?: boolean;
}) {
    return (
        <div className="flex flex-col gap-2">
            <Label required={required}>{label}</Label>

            <ReactPhoneNumberInput
                value={value}
                name={name}
                onChange={(value) => onChange(value || "")}
                className={cn(
                    className,
                    "w-full rounded-md h-10 border border-input bg-background text-sm pl-2",
                    disabled && "opacity-50 cursor-not-allowed",
                    warning && "border-amber-500",
                )}
                defaultCountry={defaultCountry as any}
                style={{
                    ".PhoneInputInput": {
                        padding: "0.5rem"
                    }
                }}
                placeholder={"Enter phone number"}
                disabled={disabled}
            />
            {error && <p className="text-sm text-red-500">{error}</p>}
        </div>
    );
}
