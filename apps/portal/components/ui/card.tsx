import * as React from 'react';

import { cn } from '@/lib/utils';
import { AbsoluteLoader } from '../Loader';

type CardProps = React.ComponentPropsWithoutRef<'div'> & {
    noBorder?: boolean;
    loading?: boolean;
};

const Card = React.forwardRef(
    ({ noBorder = false, className, loading = false, children, ...props }: CardProps, ref) => (
        <div
            ref={ref as React.LegacyRef<HTMLDivElement>}
            className={cn(
                'rounded-lg bg-card text-card-foreground shadow-sm',
                className,
                {
                    border: !noBorder,
                },
                loading && 'opacity-50 pointer-events-none relative'
            )}
            {...props}
        >
            {loading && <AbsoluteLoader />}
            {children}
        </div>
    )
);
Card.displayName = 'Card';

type CardHeaderProps = React.ComponentPropsWithoutRef<'div'> & {
    padding?: 'sm' | 'md';
};

const CardHeader = React.forwardRef(
    ({ className, padding = 'md', ...props }: CardHeaderProps, ref) => (
        <div
            ref={ref as React.LegacyRef<HTMLDivElement>}
            className={cn('flex flex-col space-y-1.5', className, padding === 'sm' ? 'p-4' : 'p-6')}
            {...props}
        />
    )
);
CardHeader.displayName = 'CardHeader';

const CardTitle = React.forwardRef(
    ({ className, ...props }: React.ComponentPropsWithoutRef<'h3'>, ref) => (
        <h3
            ref={ref as React.LegacyRef<HTMLHeadingElement>}
            className={cn('text-xl font-semibold leading-none tracking-tight', className)}
            {...props}
        />
    )
);
CardTitle.displayName = 'CardTitle';

const CardDescription = React.forwardRef(
    ({ className, ...props }: React.ComponentPropsWithoutRef<'p'>, ref) => (
        <p
            ref={ref as React.LegacyRef<HTMLParagraphElement>}
            className={cn('text-sm text-muted-foreground', className)}
            {...props}
        />
    )
);
CardDescription.displayName = 'CardDescription';

const CardContent = React.forwardRef(
    ({ className, ...props }: React.ComponentPropsWithoutRef<'div'>, ref) => (
        <div
            ref={ref as React.LegacyRef<HTMLDivElement>}
            className={cn('p-6 pt-0', className)}
            {...props}
        />
    )
);
CardContent.displayName = 'CardContent';

const CardFooter = React.forwardRef(
    ({ className, ...props }: React.ComponentPropsWithoutRef<'div'>, ref) => (
        <div
            ref={ref as React.LegacyRef<HTMLDivElement>}
            className={cn('flex items-center p-6 pt-0', className)}
            {...props}
        />
    )
);
CardFooter.displayName = 'CardFooter';

export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle };
