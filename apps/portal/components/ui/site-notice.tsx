import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import Link from 'next/link';

export interface SiteNoticeAction {
    label: string;
    href?: string;
    onClick?: () => void;
}

export interface SiteNoticeProps {
    fixed?: boolean;
    title?: string;
    content: string;
    action?: SiteNoticeAction;
    variant?: 'default' | 'info' | 'destructive';
    size?: 'small' | 'large';
    className?: string;
    dismissible?: boolean;
    onDismiss?: () => void;
}

export function SiteNotice({
    fixed,
    title,
    content,
    action,
    variant = 'default',
    className,
    size = 'large',
}: SiteNoticeProps) {
    return (
        <Alert
            variant={variant}
            id="site-notice"
            className={cn(
                'rounded-none border-x-0 border-t-0',
                fixed ? 'fixed border-none z-20 top-0 left-0 right-0' : '',
                className,
                variant === 'default' ? 'bg-primary' : '',
                size === 'large' ? 'py-8' : ''
            )}
        >
            <div className="px-4 text-white">
                {title && <AlertTitle>{title}</AlertTitle>}
                <AlertDescription
                    className={cn(
                        'flex items-center',
                        action ? 'justify-between' : 'justify-center'
                    )}
                >
                    <span>{content}</span>
                    {action &&
                        (action.href ? (
                            <Link href={action.href}>
                                <Button
                                    variant="link"
                                    className={cn(
                                        'px-0 py-0 h-auto',
                                        variant === 'default'
                                            ? 'text-white hover:text-white/90'
                                            : ''
                                    )}
                                >
                                    {action.label}
                                </Button>
                            </Link>
                        ) : (
                            <Button
                                variant="link"
                                className={cn(
                                    'px-0 py-0 h-auto',
                                    variant === 'default' ? 'text-white hover:text-white/90' : ''
                                )}
                                onClick={action.onClick}
                            >
                                {action.label}
                            </Button>
                        ))}
                </AlertDescription>
            </div>
        </Alert>
    );
}
