import { cn } from '@/lib/utils';

interface TimelineProps {
    children: React.ReactNode;
    className?: string;
}

interface TimelineItemProps {
    title: string;
    timestamp: string;
    description?: string;
    status?: 'completed' | 'current' | 'upcoming';
    className?: string;
}

export function Timeline({ children, className }: TimelineProps) {
    return <div className={cn('space-y-4', className)}>{children}</div>;
}

Timeline.Item = function TimelineItem({
    title,
    timestamp,
    description,
    status = 'completed',
    className,
}: TimelineItemProps) {
    return (
        <div className={cn('flex gap-4', className)}>
            <div className="flex flex-col items-center">
                <div
                    className={cn(
                        'w-3 h-3 rounded-full border-2',
                        status === 'completed' && 'bg-primary border-primary',
                        status === 'current' && 'bg-white border-primary',
                        status === 'upcoming' && 'bg-white border-gray-300'
                    )}
                />
                {status !== 'upcoming' && <div className="w-0.5 h-full bg-gray-200" />}
            </div>
            <div className="flex-1 pb-8">
                <div className="flex items-center justify-between">
                    <h4 className="font-medium">{title}</h4>
                    <time className="text-sm text-gray-500">{timestamp}</time>
                </div>
                {description && <p className="mt-1 text-sm text-gray-600">{description}</p>}
            </div>
        </div>
    );
};
