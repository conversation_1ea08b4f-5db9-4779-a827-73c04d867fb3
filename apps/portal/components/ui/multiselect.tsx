'use client';

import * as React from 'react';
import { ChevronDown, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface MultiSelectOption {
    value: string;
    label: string;
}

interface MultiSelectProps {
    options: MultiSelectOption[];
    value: string[];
    onChange: (value: string[]) => void;
    placeholder?: string;
    className?: string;
    maxDisplay?: number;
}

export function MultiSelect({
    options,
    value,
    onChange,
    placeholder = 'Select items...',
    className,
    maxDisplay = 3,
}: MultiSelectProps) {
    const handleSelect = (optionValue: string) => {
        if (value.includes(optionValue)) {
            onChange(value.filter((item) => item !== optionValue));
        } else {
            onChange([...value, optionValue]);
        }
    };

    const handleClear = () => {
        onChange([]);
    };

    const displayText = React.useMemo(() => {
        if (value.length === 0) {
            return placeholder;
        }

        if (value.length <= maxDisplay) {
            return value
                .map((val) => options.find((opt) => opt.value === val)?.label || val)
                .join(', ');
        }

        return `${value.length} selected`;
    }, [value, options, placeholder, maxDisplay]);

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="outline"
                    className={cn(
                        'h-8 w-full justify-between text-left font-normal border-gray-300 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1',
                        value.length === 0 && 'text-muted-foreground',
                        className
                    )}
                >
                    <span className="truncate text-popover-foreground">{displayText}</span>
                    <div className="flex items-center gap-1">
                        {value.length > 0 && (
                            <X
                                className="h-3 w-3 text-gray-500 hover:text-gray-700"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleClear();
                                }}
                            />
                        )}
                        <ChevronDown className="h-3 w-3 text-gray-500" />
                    </div>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full min-w-[200px]" align="start">
                {options.map((option) => (
                    <DropdownMenuCheckboxItem
                        key={option.value}
                        checked={value.includes(option.value)}
                        onCheckedChange={() => handleSelect(option.value)}
                        className="cursor-pointer"
                    >
                        {option.label}
                    </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
