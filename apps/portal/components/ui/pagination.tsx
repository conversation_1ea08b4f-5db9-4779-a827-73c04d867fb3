import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from './button';

interface PaginationProps {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    maxVisible?: number;
}

export function Pagination({
    currentPage,
    totalPages,
    onPageChange,
    maxVisible = 5,
}: PaginationProps) {
    const handlePageChange = (page: number) => {
        onPageChange(page);
    };

    const getPageNumbers = () => {
        const pages: (number | string)[] = [];

        if (totalPages <= maxVisible) {
            return Array.from({ length: totalPages }, (_, i) => i + 1);
        }

        // Always show first page
        pages.push(1);

        // Calculate range around current page
        let start = Math.max(2, currentPage - Math.floor(maxVisible / 2));
        const end = Math.min(totalPages - 1, start + maxVisible - 3);

        // Adjust start if end is too close to totalPages
        start = Math.max(2, end - maxVisible + 3);

        // Add ellipsis after first page if needed
        if (start > 2) {
            pages.push('...');
        }

        // Add pages around current page
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }

        // Add ellipsis before last page if needed
        if (end < totalPages - 1) {
            pages.push('...');
        }

        // Always show last page
        pages.push(totalPages);

        return pages;
    };

    return (
        <div className="flex items-center justify-center space-x-2 mt-4">
            <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                disabled={currentPage <= 1}
            >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
            </Button>

            <div className="flex items-center space-x-1">
                {getPageNumbers().map((page, index) =>
                    typeof page === 'number' ? (
                        <Button
                            key={index}
                            variant={currentPage === page ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => handlePageChange(page)}
                            className={
                                currentPage === page ? 'bg-primary text-primary-foreground' : ''
                            }
                        >
                            {page}
                        </Button>
                    ) : (
                        <span key={index} className="px-2">
                            {page}
                        </span>
                    )
                )}
            </div>

            <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage >= totalPages}
            >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
        </div>
    );
}
