'use client';

import React from 'react';
import { Control, Controller } from 'react-hook-form';

interface Option {
    value: string;
    label: string;
}

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
    name: string;
    label?: string;
    control?: Control<any>;
    options: Option[];
    rules?: any;
    error?: string;
    children?: React.ReactNode;
}

export const BaseSelect: React.FC<SelectProps> = ({
    name,
    label,
    control,
    options,
    rules,
    error,
    children,
    ...props
}) => {
    return (
        <Controller
            name={name}
            control={control}
            rules={rules}
            render={({ field }) => (
                <div className={label ? 'space-y-2' : ''}>
                    {label && (
                        <label className="block text-sm font-medium text-gray-700">{label}</label>
                    )}
                    <select
                        {...field}
                        {...props}
                        className={`w-full px-3 py-2 border rounded-md ${
                            error
                                ? 'border-red-500'
                                : 'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
                        }`}
                    >
                        {children}
                        <option value="">Select an option</option>
                        {options.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                    {error && <p className="text-sm text-red-500">{error}</p>}
                </div>
            )}
        />
    );
};
