import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

const badgeVariants = cva(
    'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
    {
        variants: {
            variant: {
                default: 'border-transparent bg-primary text-primary-foreground',
                secondary: 'border-transparent bg-secondary text-white',
                destructive: 'border-transparent bg-destructive text-destructive-foreground',
                warning: 'border-transparent bg-warning text-warning-foreground',
                success: 'border-transparent bg-green-500 text-white',
                outline: 'text-foreground',
            },
        },
        defaultVariants: {
            variant: 'default',
        },
    }
);

export interface BadgeProps
    extends React.HTMLAttributes<HTMLDivElement>,
        VariantProps<typeof badgeVariants> {
    hoverable?: boolean;
}

function Badge({ className, variant, hoverable = true, ...props }: BadgeProps) {
    return (
        <div
            className={cn(
                hoverable ? 'hover:opacity-50' : '',
                badgeVariants({ variant }),
                className
            )}
            {...props}
        />
    );
}

export { Badge, badgeVariants };
