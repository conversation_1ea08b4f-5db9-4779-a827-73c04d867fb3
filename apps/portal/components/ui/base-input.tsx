import * as React from 'react';

import { cn } from '@/lib/utils';

const BaseInput = React.forwardRef<
    HTMLInputElement,
    React.InputHTMLAttributes<HTMLInputElement> & {
        error?: string | null;
        warning?: string | null;
        hasIcon?: boolean;
    }
>(({ className, hasIcon = false, error, warning, ...props }, ref: React.Ref<HTMLInputElement>) => {
    return (
        <input
            className={cn(
                hasIcon ? 'pr-3 pl-8' : 'px-3',
                'flex h-10 w-full rounded-md border border-input bg-background py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50',
                className,
                error
                    ? 'border-red-500'
                    : 'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                warning
                    ? 'border-amber-500'
                    : 'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
            )}
            ref={ref}
            {...props}
        />
    );
});
BaseInput.displayName = 'Input';

export { BaseInput };
