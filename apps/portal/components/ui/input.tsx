import { Label } from '@/components/ui/label';
import React from 'react';
import { BaseInput } from './base-input';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
    error?: string | null;
    warning?: string | null;
    label?: string | null;
    icon?: React.ReactNode | null;
    ref?: React.Ref<HTMLInputElement>;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
    (
        { error = null, warning = null, label = null, icon = null, required = false, ...props },
        ref
    ) => {
        return (
            <div className="flex flex-col gap-2 w-full">
                {label && <Label required={required}>{label}</Label>}
                <div className="relative">
                    {icon && <div className="absolute left-2 top-1/2 -translate-y-1/2">{icon}</div>}
                    <BaseInput
                        hasIcon={!!icon}
                        ref={ref}
                        {...props}
                        error={error}
                        warning={warning}
                    />
                </div>
                {error && (
                    <p
                        role="alert"
                        className="text-sm text-red-500"
                        data-testid={`error-${props.name}`}
                    >
                        {error}
                    </p>
                )}
            </div>
        );
    }
);

Input.displayName = 'Input';
