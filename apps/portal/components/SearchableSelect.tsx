import { FieldError } from 'react-hook-form';
import ReactSelect from 'react-select';

type OptionType = { value: string; label: string };

type SearchableSelectProps = {
    name: string;
    options: any[];
    value: OptionType | null;
    onChange: (option: OptionType | null) => void;
    placeholder: string;
    error?: FieldError;
};

export const SearchableSelect = ({
    name,
    options,
    value,
    onChange,
    placeholder,
    error,
}: SearchableSelectProps) => (
    <ReactSelect
        id={`react-select-${name}`}
        inputId={`${name}-input`}
        options={options}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        menuPortalTarget={document.body}
        menuPosition="fixed"
        styles={{
            control: (base, state) => ({
                ...base,
                borderRadius: '0.5rem',
                minHeight: '40px',
                borderColor: error
                    ? 'hsl(var(--destructive))'
                    : state.isFocused
                      ? 'hsl(var(--ring))'
                      : 'hsl(var(--input))',
                boxShadow: error
                    ? '0 0 0 1px hsl(var(--destructive))'
                    : state.isFocused
                      ? '0 0 0 2px hsl(var(--ring))'
                      : 'none',
                '&:hover': {
                    borderColor: state.isFocused ? 'hsl(var(--ring))' : 'hsl(var(--input))',
                },
            }),
            menu: (base) => ({
                ...base,
                borderRadius: '0.5rem',
                overflow: 'hidden',
                zIndex: 99999,
                pointerEvents: 'auto',
            }),
            menuPortal: (base) => ({
                ...base,
                zIndex: 99999,
                pointerEvents: 'auto',
            }),
            menuList: (base) => ({
                ...base,
                padding: 0,
            }),
            option: (base, state) => ({
                ...base,
                padding: '8px 12px',
                backgroundColor: state.isSelected
                    ? 'hsl(var(--primary))'
                    : state.isFocused
                      ? 'hsl(var(--accent))'
                      : 'transparent',
                color: state.isSelected ? 'hsl(var(--primary-foreground))' : 'inherit',
                '&:active': {
                    backgroundColor: 'hsl(var(--accent))',
                },
            }),
        }}
    />
);
