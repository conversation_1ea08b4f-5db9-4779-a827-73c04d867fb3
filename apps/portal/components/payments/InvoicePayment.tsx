"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import {
	DialogDescription,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { formatCurrency } from "@/lib/utils";
import { Listing } from "@rvhelp/database";
import { format } from "date-fns";
import { CreditCard, LoaderIcon } from "lucide-react";
import { useState } from "react";

interface InvoicePaymentProps {
	invoice: {
		id: string;
		invoice_number: number;
		customer_name: string;
		customer_email: string;
		customer_phone?: string;
		amount: number;
		due_date?: Date;
		description?: string;
		items: Array<{
			description: string;
			quantity: number;
			unit_price: number;
			amount: number;
		}>;
		provider_id: string;
	};
	listing: ListingWithOwner;
}

type ListingWithOwner = Listing & {
	owner: {
		first_name: string;
		last_name: string;
		email: string;
		phone: string;
	};
};

export function InvoicePaymentComponent({
	invoice,
	listing
}: InvoicePaymentProps) {
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleCardPayment = async () => {
		setLoading(true);
		setError(null);

		try {
			// Create Stripe Checkout session
			const response = await fetch(`/api/invoices/${invoice.id}/checkout`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				}
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Failed to create checkout session");
			}

			// Redirect to Stripe Checkout
			window.location.href = data.checkoutUrl;
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : "An error occurred";
			setError(errorMessage);
		} finally {
			setLoading(false);
		}
	};

	return (
		<>
			<DialogHeader>
				<DialogTitle className="text-2xl font-semibold text-gray-900">
					Invoice #{invoice.invoice_number}
				</DialogTitle>
				<DialogDescription className="text-gray-600">
					Invoice #{invoice.invoice_number} for {formatCurrency(invoice.amount)}
				</DialogDescription>
			</DialogHeader>

			<div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
				<div className="divide-y md:divide-y-0 md:divide-x divide-gray-200">
					{/* Invoice details - Left column */}
					<div className="p-6">
						<div className="space-y-6">
							<div>
								<h2 className="text-lg font-medium text-gray-900">
									Invoice Summary
								</h2>
								<div className="mt-4 flex justify-between text-sm">
									<div>
										<p className="text-gray-500">Provider</p>
										<p className="font-medium text-gray-900">
											{listing.business_name}
										</p>
										<p className="text-gray-500">
											{listing?.owner?.first_name} {listing?.owner?.last_name}
										</p>
										<p className="text-gray-500">{listing.email}</p>
										{listing.phone && (
											<p className="text-gray-500">{listing.phone}</p>
										)}
									</div>
									<div className="text-right">
										<p className="text-gray-500">Customer</p>
										<p className="font-medium text-gray-900">
											{invoice.customer_name}
										</p>
										<p className="text-gray-500">{invoice.customer_email}</p>
										{invoice.customer_phone && (
											<p className="text-gray-500">{invoice.customer_phone}</p>
										)}
									</div>
								</div>
							</div>

							<div className="border-t border-gray-200 pt-4">
								<h3 className="text-sm font-medium text-gray-900 mb-3">
									Invoice Items
								</h3>
								<div className="space-y-2">
									{invoice.items.map((item, index) => (
										<div key={index} className="flex justify-between text-sm">
											<div className="flex-1">
												<p className="text-gray-700">{item.description}</p>
												<p className="text-gray-500 text-xs">
													{item.quantity} × {formatCurrency(item.unit_price)}
												</p>
											</div>
											<p className="text-gray-900 font-medium ml-4">
												{formatCurrency(item.amount)}
											</p>
										</div>
									))}
								</div>
							</div>

							{invoice.description && (
								<div className="border-t border-gray-200 pt-4">
									<h3 className="text-sm font-medium text-gray-900 mb-2">
										Notes
									</h3>
									<p className="text-sm text-gray-600">{invoice.description}</p>
								</div>
							)}

							<div className="border-t border-gray-200 pt-4">
								<div className="flex justify-between">
									<p className="text-base font-medium text-gray-900">Total</p>
									<p className="text-base font-medium text-gray-900">
										{formatCurrency(invoice.amount)}
									</p>
								</div>
								{invoice.due_date && (
									<p className="mt-1 text-sm text-gray-500">
										Due by {format(new Date(invoice.due_date), "MMMM d, yyyy")}
									</p>
								)}
							</div>
						</div>
						<div className="mt-4 space-y-4">
							{/* Payment Card */}
							<Card>
								<CardHeader>
									<CardTitle className="flex items-center">
										<CreditCard className="mr-2 h-5 w-5" />
										Credit/Debit Card Payment
									</CardTitle>
									<CardDescription>
										Secure payment processed by Stripe
									</CardDescription>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="text-center">
										<div className="text-2xl font-bold">
											{formatCurrency(invoice.amount)}
										</div>
									</div>

									{error && (
										<div className="text-sm text-red-600 bg-red-50 p-3 rounded">
											{error}
										</div>
									)}

									<div className="space-y-4">
										<div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
											<p className="text-sm text-gray-600">
												You will be redirected to Stripe's secure checkout page
												to complete your payment.
											</p>
										</div>
										<Button
											onClick={handleCardPayment}
											disabled={loading}
											className="w-full"
											size="lg"
										>
											{loading ? (
												<>
													<LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
													Creating Checkout Session...
												</>
											) : (
												"Proceed to Checkout"
											)}
										</Button>
									</div>
								</CardContent>
							</Card>
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
