"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { Invoice } from "@rvhelp/database";
import { CheckCircle, Home, Receipt } from "lucide-react";
import { useRouter } from "next/navigation";

interface PaymentConfirmationProps {
	invoice: Invoice;
	paymentId: string;
}

// Format amount from cents to dollars
const formatAmount = (amount: number) => {
	return new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: "USD"
	}).format(amount / 100);
};

export default function PaymentConfirmation({
	invoice,
	paymentId
}: PaymentConfirmationProps) {
	const router = useRouter();

	return (
		<div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
			<Card className="w-full max-w-md">
				<CardHeader className="text-center pb-4">
					<div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
						<CheckCircle className="h-8 w-8 text-green-600" />
					</div>
					<CardTitle className="text-2xl font-semibold text-gray-900">
						Payment Successful!
					</CardTitle>
					<CardDescription className="text-gray-600">
						Your invoice payment has been processed successfully
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Payment Details */}
					<div className="bg-gray-50 rounded-lg p-4 space-y-3">
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">Invoice Number:</span>
							<span className="font-medium text-gray-900">
								#{invoice.invoice_number}
							</span>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">Amount Paid:</span>
							<span className="text-lg font-bold text-green-600">
								{formatAmount(invoice.amount)}
							</span>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">Payment ID:</span>
							<span className="text-xs text-gray-500 font-mono truncate max-w-32">
								{paymentId}
							</span>
						</div>
					</div>

					{/* Success Message */}
					<div className="text-center">
						<p className="text-sm text-gray-600">
							You will receive a confirmation email shortly with your receipt.
						</p>
					</div>

					{/* Action Buttons */}
					<div className="space-y-3">
						<Button
							onClick={() => router.push("/invoices")}
							className="w-full"
							size="lg"
						>
							<Receipt className="mr-2 h-4 w-4" />
							View All Invoices
						</Button>

						<Button
							onClick={() => router.push("/dashboard")}
							variant="outline"
							className="w-full"
							size="lg"
						>
							<Home className="mr-2 h-4 w-4" />
							Back to Portal
						</Button>
					</div>

					{/* Additional Info */}
					<div className="text-center pt-4 border-t border-gray-200">
						<p className="text-xs text-gray-500">
							Thank you for your payment. Your service request will be processed
							shortly.
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
