"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MultiSelect } from "@/components/ui/multiselect";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ApprovalModal } from "@/components/warranty/approval-modal";
import { InvoicePaymentModal } from "@/components/warranty/invoice-payment-modal";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { User } from "@rvhelp/database";
import {
	Clock,
	Filter,
	Search,
	Settings,
	Truck,
	User as UserIcon,
	X
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import WarrantyRequestCardDialog from "./warranty/warranty-request-card/warranty-request-card-dialog";
import WarrantyRequestWizardDialog from "./warranty/warranty-request-wizard/warranty-request-wizard-dialog";
import { isPendingApproval } from "./warranty/warranty-utils";
import WarrantyRequestTableRow from "./warranty/WarrantyRequestTableRow";
// WarrantyRequestListSkeleton component
const WarrantyRequestListSkeleton = ({ company }: { company: any }) => {
	return (
		<div className="space-y-4">
			<div className="flex justify-between items-center">
				<div className="space-y-1">
					<Skeleton className="h-6 w-[250px]" />
				</div>
				<Skeleton className="h-9 w-[120px]" />
			</div>

			<div className="flex flex-col sm:flex-row gap-4">
				<div className="w-full sm:w-1/2">
					<Skeleton className="h-4 w-[100px] mb-2" />
					<Skeleton className="h-10 w-full" />
				</div>
				<div className="w-full sm:w-1/2">
					<Skeleton className="h-4 w-[100px] mb-2" />
					<Skeleton className="h-10 w-full" />
				</div>
			</div>

			<div className="w-full">
				<Skeleton className="h-4 w-[100px] mb-2" />
				<Skeleton className="h-10 w-[300px]" />
			</div>

			<div className="rounded-md border">
				<Table>
					<TableHeader
						style={{ backgroundColor: company?.brand_color ?? "#2563eb" }}
					>
						<TableRow>
							<TableHead>
								<Skeleton className="h-4 w-[80px] bg-white/20" />
							</TableHead>
							<TableHead>
								<Skeleton className="h-4 w-[120px] bg-white/20" />
							</TableHead>
							<TableHead>
								<Skeleton className="h-4 w-[200px] bg-white/20" />
							</TableHead>
							<TableHead>
								<Skeleton className="h-4 w-[100px] bg-white/20" />
							</TableHead>
							<TableHead>
								<Skeleton className="h-4 w-[80px] bg-white/20" />
							</TableHead>
							<TableHead>
								<Skeleton className="h-4 w-[100px] bg-white/20" />
							</TableHead>
							<TableHead>
								<Skeleton className="h-4 w-[80px] bg-white/20" />
							</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{[...Array(5)].map((_, i) => (
							<TableRow key={i}>
								<TableCell>
									<Skeleton className="h-4 w-[60px]" />
								</TableCell>
								<TableCell>
									<Skeleton className="h-4 w-[120px]" />
								</TableCell>
								<TableCell>
									<Skeleton className="h-4 w-[200px]" />
								</TableCell>
								<TableCell>
									<Skeleton className="h-4 w-[100px]" />
								</TableCell>
								<TableCell>
									<Skeleton className="h-4 w-[80px]" />
								</TableCell>
								<TableCell>
									<Skeleton className="h-4 w-[100px]" />
								</TableCell>
								<TableCell>
									<Skeleton className="h-4 w-[40px]" />
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>

			<div className="flex items-center justify-between pt-4">
				<Skeleton className="h-9 w-[100px]" />
				<Skeleton className="h-4 w-[120px]" />
				<Skeleton className="h-9 w-[100px]" />
			</div>
		</div>
	);
};

interface InitialFilters {
	representative: string;
	status: string[];
	component: string[];
	search: string;
	rvVin: string;
	rvModel: string;
	page: number;
}

interface WarrantyRequestListProps {
	company?: any; // Replace 'any' with the correct type if available
	initialFilters?: InitialFilters;
	user: User;
}

export default function WarrantyRequestList({
	company,
	initialFilters,
	user
}: WarrantyRequestListProps) {
	const router = useRouter();

	const [requests, setRequests] = useState<ExtendedWarrantyRequest[]>([]);
	const [loading, setLoading] = useState(true);
	const [currentPage, setCurrentPage] = useState(initialFilters?.page || 1);
	const [pageSize] = useState(10); // Increased for table view
	const [totalCount, setTotalCount] = useState(0);
	const [components, setComponents] = useState<
		Array<{ id: string; type: string; manufacturer: string }>
	>([]);

	// Add filter state with initial values from props
	const [representativeFilter, setRepresentativeFilter] = useState<string>(
		initialFilters?.representative || "current"
	);
	const [statusFilter, setStatusFilter] = useState<string[]>(
		initialFilters?.status || []
	);
	const [componentFilter, setComponentFilter] = useState<string[]>(
		initialFilters?.component || []
	);
	const [searchTerm, setSearchTerm] = useState<string>(
		initialFilters?.search || ""
	);
	const [rvVinFilter, setRvVinFilter] = useState<string>(
		initialFilters?.rvVin || ""
	);
	const [rvModelFilter, setRvModelFilter] = useState<string>(
		initialFilters?.rvModel || ""
	);

	// Add new request modal state
	const [newRequestModalOpen, setNewRequestModalOpen] = useState(false);

	// Add state for status update and view modals
	const [viewModalOpen, setViewModalOpen] = useState(false);
	const [authorizationModalOpen, setApprovalModalOpen] = useState(false);
	const [invoicePaymentModalOpen, setInvoicePaymentModalOpen] = useState(false);
	const [selectedRequest, setSelectedRequest] =
		useState<ExtendedWarrantyRequest | null>(null);
	const [deletingRequestId, setDeletingRequestId] = useState<string | null>(null);

	// Debounced search state
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>(
		initialFilters?.search || ""
	);
	const [debouncedRvVinFilter, setDebouncedRvVinFilter] = useState<string>(
		initialFilters?.rvVin || ""
	);
	const [debouncedRvModelFilter, setDebouncedRvModelFilter] = useState<string>(
		initialFilters?.rvModel || ""
	);

	// Function to update URL with current filter state
	const updateURL = useCallback(
		(filters: {
			representative?: string;
			status?: string[];
			component?: string[];
			search?: string;
			rvVin?: string;
			rvModel?: string;
			page?: number;
		}) => {
			const params = new URLSearchParams();

			if (filters.representative && filters.representative !== "current") {
				params.set("representative", filters.representative);
			}

			if (filters.status && filters.status.length > 0) {
				params.set("status", JSON.stringify(filters.status));
			}

			if (filters.component && filters.component.length > 0) {
				params.set("component", JSON.stringify(filters.component));
			}

			if (filters.search && filters.search.trim()) {
				params.set("search", filters.search.trim());
			}

			if (filters.rvVin && filters.rvVin.trim()) {
				params.set("rvVin", filters.rvVin.trim());
			}

			if (filters.rvModel && filters.rvModel.trim()) {
				params.set("rvModel", filters.rvModel.trim());
			}

			if (filters.page && filters.page > 1) {
				params.set("page", filters.page.toString());
			}

			const newURL = params.toString()
				? `?${params.toString()}`
				: window.location.pathname;
			router.replace(newURL, { scroll: false });
		},
		[router]
	);

	const fetchRequests = useCallback(
		async (page: number, size = pageSize) => {
			try {
				setLoading(true);

				// Build the query parameters including filters
				const params = new URLSearchParams({
					page: page.toString(),
					pageSize: size.toString()
				});

				// Add representative filter
				if (representativeFilter === "current") {
					params.append("representativeId", "current");
				}

				// Add status filter
				if (statusFilter.length > 0) {
					params.append("status", JSON.stringify(statusFilter));
				}

				// Add component filter
				if (componentFilter.length > 0) {
					params.append("component", JSON.stringify(componentFilter));
				}

				// Add search filter
				if (debouncedSearchTerm.trim()) {
					params.append("search", debouncedSearchTerm.trim());
				}

				// Add RV VIN filter
				if (debouncedRvVinFilter.trim()) {
					params.append("rvVin", debouncedRvVinFilter.trim());
				}

				// Add RV model filter
				if (debouncedRvModelFilter.trim()) {
					params.append("rvModel", debouncedRvModelFilter.trim());
				}

				const res = await fetch(`/api/warranty-requests?${params.toString()}`);
				if (!res.ok) throw new Error("Failed to fetch");
				const data = await res.json();
				// Sort requests so amber statuses appear first
				const sortedRequests = [...data.requests].sort((a, b) => {
					const aIsAmber = isPendingApproval(a.status, user?.role);
					const bIsAmber = isPendingApproval(b.status, user?.role);
					if (aIsAmber && !bIsAmber) return -1;
					if (!aIsAmber && bIsAmber) return 1;
					return 0;
				});
				setRequests(sortedRequests);
				setTotalCount(data.totalCount);
				// Only update currentPage if it's different to avoid triggering effects
				if (page !== currentPage) {
					setCurrentPage(page);
				}
			} catch {
				setRequests([]);
				setTotalCount(0);
			} finally {
				setLoading(false);
			}
		},
		[
			representativeFilter,
			statusFilter,
			componentFilter,
			debouncedSearchTerm,
			debouncedRvVinFilter,
			debouncedRvModelFilter,
			pageSize,
			user?.role,
			currentPage,
		]
	);

	useEffect(() => {
		const fetchComponents = async () => {
			if (!company?.id) return;

			try {
				const res = await fetch(`/api/companies/${company.id}`);
				if (!res.ok) throw new Error("Failed to fetch components");
				const data = await res.json();
				setComponents(data.components || []);
			} catch (error) {
				console.error("Error fetching components:", error);
				setComponents([]);
			}
		};
		fetchComponents();
	}, [company?.id]);

	// Debounced search effect
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedRvVinFilter(rvVinFilter);
		}, 300);

		return () => clearTimeout(timer);
	}, [rvVinFilter]);

	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedRvModelFilter(rvModelFilter);
		}, 300);

		return () => clearTimeout(timer);
	}, [rvModelFilter]);

	useEffect(() => {
		// When filters change, reset to page 1 and update URL
		setCurrentPage(1);
		updateURL({
			representative: representativeFilter,
			status: statusFilter,
			component: componentFilter,
			search: debouncedSearchTerm,
			rvVin: debouncedRvVinFilter,
			rvModel: debouncedRvModelFilter,
			page: 1
		});
	}, [
		representativeFilter,
		statusFilter,
		componentFilter,
		debouncedSearchTerm,
		debouncedRvVinFilter,
		debouncedRvModelFilter
	]);

	useEffect(() => {
		// Always fetch when currentPage changes (this fixes the pagination issue)
		fetchRequests(currentPage);
	}, [currentPage, fetchRequests]);

	// Handle filter changes with URL updates
	const handleRepresentativeFilterChange = (value: string) => {
		setRepresentativeFilter(value);
		setCurrentPage(1);
		updateURL({
			representative: value,
			status: statusFilter,
			component: componentFilter,
			search: searchTerm,
			rvVin: rvVinFilter,
			rvModel: rvModelFilter,
			page: 1
		});
	};

	const handleStatusFilterChange = (value: string[]) => {
		setStatusFilter(value);
		setCurrentPage(1);
		updateURL({
			representative: representativeFilter,
			status: value,
			component: componentFilter,
			search: searchTerm,
			rvVin: rvVinFilter,
			rvModel: rvModelFilter,
			page: 1
		});
	};

	const handleComponentFilterChange = (value: string[]) => {
		setComponentFilter(value);
		setCurrentPage(1);
		updateURL({
			representative: representativeFilter,
			status: statusFilter,
			component: value,
			search: searchTerm,
			rvVin: rvVinFilter,
			rvModel: rvModelFilter,
			page: 1
		});
	};

	const handleSearchChange = (value: string) => {
		setSearchTerm(value);
	};

	const handleRvVinFilterChange = (value: string) => {
		setRvVinFilter(value);
	};

	const handleRvModelFilterChange = (value: string) => {
		setRvModelFilter(value);
	};

	const handleClearFilters = () => {
		setRepresentativeFilter("current");
		setStatusFilter([]);
		setComponentFilter([]);
		setSearchTerm("");
		setRvVinFilter("");
		setRvModelFilter("");
		setCurrentPage(1);
		updateURL({
			representative: "current",
			status: [],
			component: [],
			search: "",
			rvVin: "",
			rvModel: "",
			page: 1
		});
	};

	const handleNewRequestSuccess = () => {
		setNewRequestModalOpen(false);
		fetchRequests(1); // Refresh the list and go to first page
	};

	// Handle view action
	const handleView = (request: ExtendedWarrantyRequest) => {
		setSelectedRequest(request);
		setViewModalOpen(true);
	};

	// Handle update status action
	const handleUpdateStatus = (request: ExtendedWarrantyRequest) => {
		if (!isPendingApproval(request.status, user?.role)) {
			return;
		}
		setSelectedRequest(request);
		if (request.status === "AUTHORIZATION_REQUESTED") {
			setApprovalModalOpen(true);
		} else if (request.status === "INVOICE_CREATED") {
			setInvoicePaymentModalOpen(true);
		}
	};

	// Handle invoice payment
	const handlePayInvoice = (request: ExtendedWarrantyRequest) => {
		setSelectedRequest(request);
		setInvoicePaymentModalOpen(true);
	};

	// Handle platform invoice generation
	const handleGeneratePlatformInvoice = async (
		request: ExtendedWarrantyRequest
	) => {
		// Find the associated invoice for this warranty request
		try {
			const response = await fetch(
				`/api/warranty-requests/${request.id}/invoice`
			);

			if (!response.ok) {
				toast.error("Could not find associated invoice");
				return;
			}

			const invoiceData = await response.json();

			if (!invoiceData.invoice) {
				toast.error("No invoice found for this warranty request");
				return;
			}

			// Generate platform invoice using the invoice ID
			const platformResponse = await fetch(
				"/api/admin/generate-platform-invoice",
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						originalInvoiceId: invoiceData.invoice.id
					})
				}
			);

			const platformData = await platformResponse.json();

			if (platformData.success) {
				toast.success(
					`Platform invoice #${platformData.platformInvoice.invoice_number} generated successfully! Email and Slack notifications sent.`
				);
			} else {
				console.error("🔧 [Platform Invoice] Platform invoice generation failed:", platformData.message);
				toast.error(
					platformData.message || "Failed to generate platform invoice"
				);
			}
		} catch (error) {
			console.error("🔧 [Platform Invoice] Error generating platform invoice:", error);
			toast.error("Failed to generate platform invoice");
		}
	};

	// Handle delete warranty request
	const handleDelete = async (requestId: string) => {
		setDeletingRequestId(requestId);
		try {
			const response = await fetch(`/api/warranty-requests/${requestId}/delete`, {
				method: 'DELETE',
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to delete warranty request');
			}

			// Remove the deleted request from the local state
			setRequests(prevRequests => prevRequests.filter(req => req.id !== requestId));
			setTotalCount(prev => prev - 1);

		} catch (error) {
			console.error('Error deleting warranty request:', error);
			throw error; // Re-throw to let the component handle the toast
		} finally {
			setDeletingRequestId(null);
		}
	};

	// Handle resend email action
	const handleResendEmail = async (request: ExtendedWarrantyRequest) => {
		try {
			const response = await fetch(`/api/warranty-requests/${request.id}/resend-email`, {
				method: "POST",
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to resend email");
			}

			toast.success("Warranty request notifications resent successfully");
			fetchRequests(currentPage); // Refresh to update email_sent_at timestamp
		} catch (error) {
			console.error("Error resending warranty request email:", error);
			toast.error(error instanceof Error ? error.message : "Failed to resend email");
		}
	};

	// Update page navigation to sync with URL
	const handlePageChange = (newPage: number) => {
		if (newPage === currentPage) return; // Prevent unnecessary updates

		setCurrentPage(newPage);
		updateURL({
			representative: representativeFilter,
			status: statusFilter,
			component: componentFilter,
			search: searchTerm,
			rvVin: rvVinFilter,
			rvModel: rvModelFilter,
			page: newPage
		});
	};

	if (loading && requests.length === 0) {
		return <WarrantyRequestListSkeleton company={company} />;
	}

	const totalPages = Math.ceil(totalCount / pageSize);

	return (
		<TooltipProvider>
			<div className="space-y-4">
				<div className="flex justify-between items-center">
					<div className="space-y-1">
						<h2 className="text-md">
							View and manage warranty service requests
						</h2>
					</div>
					<Button
						onClick={() => setNewRequestModalOpen(true)}
						style={{
							backgroundColor: company?.brand_color ?? "#2563eb",
							color: "#fff"
						}}
					>
						New Request
					</Button>
				</div>

				{/* Filter Controls */}
				<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
					<div className="px-6 py-1">
						<div className="flex items-center justify-between">
							<div className="text-base font-semibold text-gray-900 flex items-center gap-1.5">
								<Filter className="h-4 w-4 text-gray-600" />
								Filters
							</div>
							<Button
								variant="ghost"
								size="sm"
								onClick={handleClearFilters}
								className="text-gray-500 hover:text-gray-700 flex items-center gap-1 h-8 px-2"
							>
								<X className="h-3.5 w-3.5" />
								Clear
							</Button>
						</div>
					</div>
					<div className="p-6 pt-4">
						<div className="grid grid-cols-1 md:grid-cols-3 gap-3">
							{/* Representative Filter */}
							<div className="space-y-1">
								<label className="text-xs font-medium text-gray-700 flex items-center gap-1.5">
									<UserIcon className="h-3.5 w-3.5 text-gray-500" />
									Representative
								</label>
								<Select
									value={representativeFilter}
									onValueChange={handleRepresentativeFilterChange}
								>
									<SelectTrigger className="h-8 border-gray-300 text-sm">
										<SelectValue placeholder="Filter by representative" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="current">My Requests</SelectItem>
										<SelectItem value="all">All Requests</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{/* Search Filter */}
							<div className="space-y-1">
								<label className="text-xs font-medium text-gray-700 flex items-center gap-1.5">
									<Search className="h-3.5 w-3.5 text-gray-500" />
									Search Customer
								</label>
								<div className="relative">
									<Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
									<Input
										placeholder="Enter customer last name..."
										value={searchTerm}
										onChange={(e) => handleSearchChange(e.target.value)}
										className="pl-8 h-8 border-gray-300 text-sm"
									/>
								</div>
							</div>

							{/* RV VIN Filter */}
							<div className="space-y-1">
								<label className="text-xs font-medium text-gray-700 flex items-center gap-1.5">
									<Truck className="h-3.5 w-3.5 text-gray-500" />
									RV VIN
								</label>
								<div className="relative">
									<Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
									<Input
										placeholder="Enter RV VIN..."
										value={rvVinFilter}
										onChange={(e) => handleRvVinFilterChange(e.target.value)}
										className="pl-8 h-8 border-gray-300 text-sm"
									/>
								</div>
							</div>

							{/* Status Filter */}
							<div className="space-y-1">
								<label className="text-xs font-medium text-gray-700 flex items-center gap-1.5">
									<Clock className="h-3.5 w-3.5 text-gray-500" />
									Status
								</label>
								<MultiSelect
									options={[
										{ value: "REQUEST_CREATED", label: "Request Created" },
										{
											value: "REQUEST_APPROVED",
											label: "Request Preauthorized"
										},
										{ value: "REQUEST_REJECTED", label: "Request Rejected" },
										{
											value: "REQUEST_REGISTERED",
											label: "Customer Registered"
										},
										{
											value: "TECHNICIANS_INVITED",
											label: "Technicians Invited"
										},
										{ value: "JOB_ACCEPTED", label: "Job Accepted" },
										{ value: "JOB_STARTED", label: "Job Started" },
										{
											value: "AUTHORIZATION_REQUESTED",
											label: "Authorization Requested"
										},
										{
											value: "AUTHORIZATION_APPROVED",
											label: "Authorization Approved"
										},
										{
											value: "AUTHORIZATION_FEEDBACK",
											label: "Authorization Feedback"
										},
										{
											value: "PARTS_ORDERED",
											label: "Parts Ordered"
										},
										{ value: "JOB_COMPLETED", label: "Job Completed" },
										{ value: "JOB_CANCELLED", label: "Job Cancelled" },
										{ value: "INVOICE_CREATED", label: "Invoice Received" },
										{ value: "INVOICE_PAID", label: "Invoice Paid" }
									]}
									value={statusFilter}
									onChange={handleStatusFilterChange}
									placeholder="Filter by status"
									className="text-sm"
								/>
							</div>

							{/* Component Filter */}
							<div className="space-y-1">
								<label className="text-xs font-medium text-gray-700 flex items-center gap-1.5">
									<Settings className="h-3.5 w-3.5 text-gray-500" />
									Component
								</label>
								<MultiSelect
									options={components.map((component) => ({
										value: component.id,
										label: `${component.type} - ${component.manufacturer}`
									}))}
									value={componentFilter}
									onChange={handleComponentFilterChange}
									placeholder="Filter by component"
									className="text-sm"
								/>
							</div>

							{/* RV Model Filter */}
							<div className="space-y-1">
								<label className="text-xs font-medium text-gray-700 flex items-center gap-1.5">
									<Truck className="h-3.5 w-3.5 text-gray-500" />
									RV Model
								</label>
								<div className="relative">
									<Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
									<Input
										placeholder="Enter RV model..."
										value={rvModelFilter}
										onChange={(e) => handleRvModelFilterChange(e.target.value)}
										className="pl-8 h-8 border-gray-300 text-sm"
									/>
								</div>
							</div>
						</div>

						{/* Active Filters Summary */}
						{(representativeFilter !== "current" ||
							statusFilter.length > 0 ||
							componentFilter.length > 0 ||
							searchTerm ||
							rvVinFilter ||
							rvModelFilter) && (
								<div className="mt-3 pt-2 border-t border-gray-200">
									<div className="flex flex-wrap items-center gap-1.5">
										<span className="text-xs font-medium text-gray-600">
											Active filters:
										</span>
										{representativeFilter !== "current" && (
											<span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
												All Requests
											</span>
										)}
										{statusFilter.length > 0 && (
											<span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
												{statusFilter.length === 1
													? statusFilter[0].replace(/_/g, " ")
													: `${statusFilter.length} statuses`}
											</span>
										)}
										{componentFilter.length > 0 && (
											<span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
												{componentFilter.length === 1
													? components.find((c) => c.id === componentFilter[0])
														?.type +
													" - " +
													components.find((c) => c.id === componentFilter[0])
														?.manufacturer || "Component"
													: `${componentFilter.length} components`}
											</span>
										)}
										{searchTerm && (
											<span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
												&quot;{searchTerm}&quot;
											</span>
										)}
										{rvVinFilter && (
											<span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
												&quot;{rvVinFilter}&quot;
											</span>
										)}
										{rvModelFilter && (
											<span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
												&quot;{rvModelFilter}&quot;
											</span>
										)}
									</div>
								</div>
							)}
					</div>
				</div>

				<div className="rounded-lg border border-gray-200 shadow-sm bg-white overflow-hidden text-sm">
					<Table>
						<TableHeader
							style={{ backgroundColor: company?.brand_color ?? "#2563eb" }}
						>
							<TableRow>
								<TableHead className="text-white font-semibold">
									ID/Status
								</TableHead>
								<TableHead className="text-white font-semibold">
									Customer
								</TableHead>
								<TableHead className="text-white font-semibold">
									Issue
								</TableHead>
								<TableHead className="text-white font-semibold">
									RV Details
								</TableHead>
								<TableHead className="text-white font-semibold">
									Hours
								</TableHead>
								<TableHead className="text-white font-semibold">
									Submitted
								</TableHead>
								<TableHead className="text-white font-semibold">
									Actions
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{/* Always render exactly 10 rows */}
							{Array.from({ length: 10 }, (_, index) => {
								const request = requests[index];
								if (!request) {
									// Empty row to maintain consistent height
									return (
										<TableRow
											key={`empty-${index}`}
											className={`h-16 ${index % 2 === 0 ? "bg-gray-50" : "bg-gray-100"}`}
										>
											<TableCell
												colSpan={7}
												className="text-center text-muted-foreground"
											>
												{requests.length === 0 && index === 0 ? (
													<div className="py-8">
														<div className="text-gray-400 text-sm mb-2">📋</div>
														<div className="font-medium">
															No warranty requests found
														</div>
														<div className="text-xs">
															Try adjusting your filters or create a new request
														</div>
													</div>
												) : (
													""
												)}
											</TableCell>
										</TableRow>
									);
								}
								return (
									<WarrantyRequestTableRow
										key={request.id}
										request={request}
										company={company}
										index={index}
										onView={handleView}
										onUpdateStatus={handleUpdateStatus}
										onPayInvoice={handlePayInvoice}
										onGeneratePlatformInvoice={handleGeneratePlatformInvoice}
										onDelete={handleDelete}
										onResendEmail={handleResendEmail}
										user={user}
										isDeleting={deletingRequestId === request.id}
									/>
								);
							})}
						</TableBody>
					</Table>
				</div>

				{/* Pagination Controls - Always visible */}
				<div className="flex items-center justify-between pt-4">
					<Button
						variant="outline"
						onClick={() => {
							const newPage = Math.max(1, currentPage - 1);
							handlePageChange(newPage);
						}}
						disabled={currentPage === 1 || loading || totalCount === 0}
						style={{
							backgroundColor: company?.brand_color ?? "#2563eb",
							color: "#fff"
						}}
					>
						Previous
					</Button>
					<span className="text-sm text-muted-foreground">
						{totalCount > 0
							? `Page ${currentPage} of ${totalPages}`
							: "No records"}
					</span>
					<Button
						variant="outline"
						style={{
							backgroundColor: company?.brand_color ?? "#2563eb",
							color: "#fff"
						}}
						onClick={() => {
							const newPage = Math.min(totalPages, currentPage + 1);
							handlePageChange(newPage);
						}}
						disabled={currentPage === totalPages || loading || totalCount === 0}
					>
						Next
					</Button>
				</div>
			</div>

			{/* New Request Dialog */}
			<WarrantyRequestWizardDialog
				open={newRequestModalOpen}
				onClose={() => setNewRequestModalOpen(false)}
				company={company}
				onSuccess={handleNewRequestSuccess}
			/>

			{/* View Request Dialog */}
			{selectedRequest && (
				<WarrantyRequestCardDialog
					open={viewModalOpen}
					onClose={() => setViewModalOpen(false)}
					company={company}
					request={selectedRequest}
					onStatusUpdated={() => fetchRequests(currentPage)}
					user={user}
				/>
			)}

			{/* Update Status Modal */}
			{selectedRequest && (
				<>
					<ApprovalModal
						open={authorizationModalOpen}
						onClose={() => setApprovalModalOpen(false)}
						request={selectedRequest}
						onStatusUpdated={() => {
							setApprovalModalOpen(false);
							fetchRequests(currentPage);
						}}
						company={company}
					/>
					<InvoicePaymentModal
						open={invoicePaymentModalOpen}
						onClose={() => setInvoicePaymentModalOpen(false)}
						request={selectedRequest}
						onStatusUpdated={() => {
							setInvoicePaymentModalOpen(false);
							fetchRequests(currentPage);
						}}
						company={company}
					/>
				</>
			)}
		</TooltipProvider>
	);
}
