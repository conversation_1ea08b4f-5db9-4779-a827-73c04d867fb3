import config from '@/config';
import { Link, Section, Text } from '@react-email/components';
import { BaseEmail } from './BaseEmail';

interface PasswordChangeEmailProps {
    firstName: string;
    timestamp: string;
}

export default function PasswordChangeEmail({ firstName, timestamp }: PasswordChangeEmailProps) {
    return (
        <BaseEmail previewText="Your password has been changed">
            <Section className="flex flex-col gap-4">
                <Text>Hi {firstName},</Text>
                <Text>
                    Your password was successfully changed on {timestamp}. If you did not make this
                    change, please log in to your account and change your password to prevent
                    unauthorized access.
                </Text>
                <Text>If you did not make this change, please:</Text>
                <Text className="ml-4">
                    Reset your password again using the &quot;Forgot Password&quot; feature or by
                    clicking the link below:
                </Text>
                <Text className="ml-4">
                    <Link href={`${config.appUrl}/forgot-password`}>Reset Password</Link>
                </Text>
                <Text className="ml-4">
                    Best regards,
                    <br />
                    The RV Help Team
                </Text>
            </Section>
        </BaseEmail>
    );
}
