import { Container, Heading, Section, Text } from "@react-email/components";
import { BaseEmail } from "../BaseEmail";
import { emailStyles } from "../shared-styles";

interface JobCancelledEmailProps {
	companyName: string;
	customerName: string;
	providerName: string;
	reason: string;
}

export const JobCancelledEmail = ({
	companyName,
	customerName,
	providerName,
	reason
}: JobCancelledEmailProps) => {
	return (
		<BaseEmail previewText="Warranty Service has been cancelled">
			<Container style={emailStyles.container}>
				<Heading style={emailStyles.heading}>
					Warranty Service Cancelled
				</Heading>

				<Text style={emailStyles.text}>Hello {providerName},</Text>

				<Section style={emailStyles.section}>
					<Text style={emailStyles.text}>
						We wanted to let you know that {companyName} has cancelled the
						warranty service request for {customerName}.
					</Text>

					<Text style={emailStyles.text}>
						<strong>Reason for cancellation:</strong>
					</Text>
					<Text
						style={{
							...emailStyles.text,
							marginTop: "8px",
							fontStyle: "italic"
						}}
					>
						&quot;{reason}&quot;
					</Text>
				</Section>

				<Text style={emailStyles.text}>
					We apologize for any inconvenience this may have caused. We appreciate
					your interest in this opportunity and look forward to connecting you
					with other customers who need your services.
				</Text>

				<Text style={emailStyles.footer}>
					Keep checking your dashboard for new service opportunities in your
					area.
				</Text>

				<Text style={emailStyles.footer}>
					Best regards,
					<br />
					The RV Help Team
				</Text>
			</Container>
		</BaseEmail>
	);
};
