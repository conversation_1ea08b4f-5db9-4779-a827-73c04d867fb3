import {
	Body,
	Container,
	Head,
	Hr,
	Html,
	Section,
	Text
} from "@react-email/components";

interface SupportRequestEmailProps {
	firstName: string;
	lastName: string;
	email: string;
	description: string;
	type: "bug" | "feature";
	companyName: string;
}

export const SupportRequestEmail = ({
	firstName,
	lastName,
	email,
	description,
	type,
	companyName
}: SupportRequestEmailProps) => (
	<Html>
		<Head />
		<Body style={main}>
			<Container style={container}>
				<Section style={section}>
					<Text style={title}>
						{type === "bug" ? "Bug Report" : "Feature Request"} from{" "}
						{companyName} Portal
					</Text>

					<Text style={text}>
						<strong>From:</strong> {firstName} {lastName}
					</Text>

					<Text style={text}>
						<strong>Email:</strong> {email}
					</Text>

					<Text style={text}>
						<strong>Company:</strong> {companyName}
					</Text>

					<Text style={text}>
						<strong>Type:</strong>{" "}
						{type === "bug" ? "Bug Report" : "Feature Request"}
					</Text>

					<Hr style={hr} />

					<Text style={text}>
						<strong>Description:</strong>
					</Text>

					<Text style={descriptionStyle}>{description}</Text>
				</Section>
			</Container>
		</Body>
	</Html>
);

export default SupportRequestEmail;

const main = {
	backgroundColor: "#ffffff",
	fontFamily:
		'-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif'
};

const container = {
	margin: "0 auto",
	padding: "20px 0 48px",
	maxWidth: "560px"
};

const section = {
	padding: "24px",
	border: "solid 1px #dedede",
	borderRadius: "5px",
	textAlign: "left" as const
};

const title = {
	fontSize: "24px",
	fontWeight: "bold",
	color: "#333333",
	marginBottom: "20px"
};

const text = {
	fontSize: "16px",
	lineHeight: "26px",
	color: "#333333",
	marginBottom: "10px"
};

const descriptionStyle = {
	fontSize: "16px",
	lineHeight: "26px",
	color: "#333333",
	backgroundColor: "#f6f6f6",
	padding: "16px",
	borderRadius: "4px",
	marginTop: "10px",
	whiteSpace: "pre-wrap" as const
};

const hr = {
	borderColor: "#cccccc",
	margin: "20px 0"
};
