import { Container, Heading, Section, Text } from "@react-email/components";
import { BaseEmail } from "../BaseEmail";
import { emailStyles } from "../shared-styles";

interface JobCancelledEmailProps {
	companyName: string;
	supportPhone: string;
	customerName: string;
	reason: string;
}

export const JobCancelledEmail = ({
	companyName,
	supportPhone,
	customerName,
	reason
}: JobCancelledEmailProps) => {
	return (
		<BaseEmail previewText="Warranty Service has been cancelled">
			<Container style={emailStyles.container}>
				<Heading style={emailStyles.heading}>
					Warranty Service Cancelled
				</Heading>

				<Text style={emailStyles.text}>Hello {customerName},</Text>

				<Section style={emailStyles.section}>
					<Text style={emailStyles.text}>
						We wanted to let you know that {companyName} has cancelled your
						warranty service request.
					</Text>

					<Text style={emailStyles.text}>
						<strong>Reason for cancellation:</strong>
					</Text>
					<Text
						style={{
							...emailStyles.text,
							marginTop: "8px",
							fontStyle: "italic"
						}}
					>
						&quot;{reason}&quot;
					</Text>
				</Section>

				<Text style={emailStyles.text}>
					If this was done in error, please call {companyName} at {supportPhone}
				</Text>

				<Text style={emailStyles.footer}>
					Best regards,
					<br />
					The RV Help Team
				</Text>
			</Container>
		</BaseEmail>
	);
};
