import { BaseEmail } from "@/components/email-templates/BaseEmail";
import type { ExtendedWarrantyRequest } from "@/types/warranty";
import { Button, Section, Text } from "@react-email/components";
import type { Company } from "@rvhelp/database";
import { emailStyles, emailUtils } from "../shared-styles";

interface WarrantyRequestEmailProps {
	company: Company;
	warrantyRequest: ExtendedWarrantyRequest;
	rvhelpUrl: string;
}

export function WarrantyRequestEmail({
	company,
	warrantyRequest,
	rvhelpUrl
}: WarrantyRequestEmailProps) {
	const previewText = "Your warranty service request has been pre-authorized!";
	// Extract last 6 digits of VIN
	const lastSixDigits = warrantyRequest.rv_vin.slice(-6);

	return (
		<BaseEmail previewText={previewText}>
			<Section>
				<Text style={emailStyles.heading}>
					Action Required: Schedule Your Pre-Authorized RV Service
				</Text>

				<Text style={emailUtils.greetingText}>
					Dear {warrantyRequest.first_name},
				</Text>

				<Text style={emailStyles.text}>
					<span style={{ fontWeight: "bold" }}>Great news!</span> Your warranty
					service request for your {company.name} {warrantyRequest.rv_model} has
					been pre-authorized.
				</Text>

				<Text style={emailStyles.subheading}>
					{company.name} + RV Help = A Better Warranty Service Experience
				</Text>

				<Text style={emailStyles.text}>
					{company.name} has partnered with RV Help, a nationwide network of
					certified mobile RV technicians who can come directly to you.
				</Text>

				<Text style={emailStyles.text}>
					<span style={{ fontWeight: "bold" }}>To complete the process:</span>{" "}
					Click the button below to create your RV Help account and invite
					nearby technicians to your job.
				</Text>

				<Section style={emailStyles.centered}>
					<Button
						href={`${rvhelpUrl}/oem/${warrantyRequest.uuid}`}
						style={emailStyles.button}
					>
						Find Mobile Techs Near You
					</Button>
				</Section>

				<Text style={emailStyles.subheading}>How it works</Text>

				<Text style={emailStyles.text}>
					As a valued {company.name} customer, you&apos;ll receive{" "}
					<span style={{ fontWeight: "bold" }}>
						one full year of RV Help Pro (Standard) membership
					</span>{" "}
					completely free! This gives you access to exclusive benefits that will
					enhance your RV experience.
				</Text>

				<Text style={emailStyles.text}>
					Your Pro membership will be automatically activated when you create
					your RV Help account. You&apos;ll have full access to all these
					benefits for one year from your service date.
				</Text>

				{/* Why You'll Love RV Help section */}
				<Text style={emailStyles.subheading}>
					Why You&apos;ll Love RV Help:
				</Text>

				<Section style={emailStyles.list}>
					<Text style={emailStyles.listItem}>
						<span
							style={{
								color: "#437F6B",
								fontWeight: "bold",
								marginRight: "8px"
							}}
						>
							✓
						</span>
						Certified mobile technicians come to your location
					</Text>
					<Text style={emailStyles.listItem}>
						<span
							style={{
								color: "#437F6B",
								fontWeight: "bold",
								marginRight: "8px"
							}}
						>
							✓
						</span>
						No need to tow your RV to a service center
					</Text>
					<Text style={emailStyles.listItem}>
						<span
							style={{
								color: "#437F6B",
								fontWeight: "bold",
								marginRight: "8px"
							}}
						>
							✓
						</span>
						Real-time updates on your appointment
					</Text>
					<Text style={emailStyles.listItem}>
						<span
							style={{
								color: "#437F6B",
								fontWeight: "bold",
								marginRight: "8px"
							}}
						>
							✓
						</span>
						Direct communication with your technician
					</Text>
					<Text style={emailStyles.listItem}>
						<span
							style={{
								color: "#437F6B",
								fontWeight: "bold",
								marginRight: "8px"
							}}
						>
							✓
						</span>
						{`${company.name}-covered dispatch fee`}
					</Text>
					<Text style={emailStyles.listItem}>
						<span
							style={{
								color: "#437F6B",
								fontWeight: "bold",
								marginRight: "8px"
							}}
						>
							✓
						</span>
						Convenient scheduling options
					</Text>
				</Section>

				{/* How It Works section */}
				<Text style={emailStyles.subheading}>How It Works:</Text>

				<Section style={emailStyles.list}>
					<Text style={emailStyles.listItem}>
						<span
							style={{
								backgroundColor: "#437F6B",
								color: "#ffffff",
								borderRadius: "50%",
								width: "24px",
								height: "24px",
								display: "inline-block",
								textAlign: "center",
								lineHeight: "24px",
								fontWeight: "bold",
								marginRight: "8px",
								fontSize: "14px"
							}}
						>
							1
						</span>
						Click the button to create your RV Help account
					</Text>
					<Text style={emailStyles.listItem}>
						<span
							style={{
								backgroundColor: "#437F6B",
								color: "#ffffff",
								borderRadius: "50%",
								width: "24px",
								height: "24px",
								display: "inline-block",
								textAlign: "center",
								lineHeight: "24px",
								fontWeight: "bold",
								marginRight: "8px",
								fontSize: "14px"
							}}
						>
							2
						</span>
						Invite nearby technicians to your job
					</Text>
					<Text style={emailStyles.listItem}>
						<span
							style={{
								backgroundColor: "#437F6B",
								color: "#ffffff",
								borderRadius: "50%",
								width: "24px",
								height: "24px",
								display: "inline-block",
								textAlign: "center",
								lineHeight: "24px",
								fontWeight: "bold",
								marginRight: "8px",
								fontSize: "14px"
							}}
						>
							3
						</span>
						Message technicians to schedule your appointment
					</Text>
					<Text style={emailStyles.listItem}>
						<span
							style={{
								backgroundColor: "#437F6B",
								color: "#ffffff",
								borderRadius: "50%",
								width: "24px",
								height: "24px",
								display: "inline-block",
								textAlign: "center",
								lineHeight: "24px",
								fontWeight: "bold",
								marginRight: "8px",
								fontSize: "14px"
							}}
						>
							4
						</span>
						Keep track of your job status
					</Text>
				</Section>

				<Text style={emailStyles.text}>
					Your RV Help account gives you full visibility into the service
					process, from scheduling to completion. You&apos;ll be able to
					communicate directly with your technician and receive updates every
					step of the way.
				</Text>

				<Section style={emailStyles.centered}>
					<Button
						href={`${rvhelpUrl}/oem/${warrantyRequest.uuid}`}
						style={emailStyles.button}
					>
						SCHEDULE YOUR APPOINTMENT NOW
					</Button>
				</Section>

				{/* Service details section */}
				<Text style={emailStyles.subheading}>
					Your Pre-Authorized Service Details:
				</Text>

				<Section style={emailStyles.messageBox}>
					<Text style={emailStyles.messageTitle}>Service Information</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>Issue:</span>{" "}
						{warrantyRequest.complaint}
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>Pre-authorized hours:</span>{" "}
						{warrantyRequest.approved_hours} hours
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>{company.name} model:</span>{" "}
						{warrantyRequest.rv_model}
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>VIN:</span> {lastSixDigits}
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>Request ID:</span>{" "}
						{warrantyRequest.uuid}
					</Text>
				</Section>

				<Section style={emailStyles.centered}>
					<Button
						href={`${rvhelpUrl}/oem/${warrantyRequest.uuid}`}
						style={emailStyles.button}
					>
						SCHEDULE YOUR APPOINTMENT NOW
					</Button>
				</Section>

				<Text style={emailUtils.signatureText}>Happy Camping,</Text>

				<Text style={{ ...emailStyles.text, fontWeight: "bold", margin: "0" }}>
					The RV Help Team
				</Text>
			</Section>
		</BaseEmail>
	);
}
