"use client";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import config from "@/config";
import { useAuth } from "@/lib/hooks/useAuth";
import { AlertCircle } from "lucide-react";
import { useEffect, useState } from "react";

export function DevelopmentNotice() {
	const { user } = useAuth();
	const isAdmin = user?.role === "ADMIN";
	const [isCollapsed, setIsCollapsed] = useState(true);

	useEffect(() => {
		const hidden = localStorage.getItem("dev-notice-hidden");
		if (hidden === "false") {
			setIsCollapsed(false);
		}
	}, []);

	if (
		!config.isDevelopment ||
		config.appUrl.includes("localhost") ||
		config.appUrl.includes("rvhelp.test")
	) {
		return null;
	}

	const handleToggle = () => {
		setIsCollapsed(!isCollapsed);
		localStorage.setItem("dev-notice-hidden", (!isCollapsed).toString());
	};

	if (isCollapsed) {
		return (
			<Alert
				variant="info"
				className="cursor-pointer z-50"
				onClick={handleToggle}
			>
				<AlertCircle className="h-4 w-4" />
				<AlertDescription className="ml-2">
					Development Environment Notice (click to expand)
				</AlertDescription>
			</Alert>
		);
	}

	return (
		<Alert variant="info" className="relative">
			<AlertCircle className="h-5 w-5" />
			<AlertTitle>Development Environment Notice</AlertTitle>
			<AlertDescription>
				In development, emails and text notifications are only sent to addresses
				on the safelist.
				{isAdmin ? (
					<span className="block mt-2 text-sm text-muted-foreground">
						Contact a system administrator to manage safelist settings.
					</span>
				) : (
					<p className="mt-2 text-sm">
						Please contact an administrator to add your contact information to
						the safelist.
					</p>
				)}
			</AlertDescription>
			<button
				onClick={handleToggle}
				className="text-sm absolute top-2 right-2 text-muted-foreground hover:text-foreground"
			>
				Collapse
			</button>
		</Alert>
	);
}
