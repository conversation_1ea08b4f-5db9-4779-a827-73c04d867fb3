"use client";

import { Input } from "@/components/ui/input";
import { useGoogleMapsScript } from "@/hooks/useGoogleMapsScript";
import { cn } from "@/lib/utils";
import { useCallback, useEffect, useRef, useState } from "react";

type LocationType = "precise" | "city";

interface PlacesAutocompleteProps
	extends React.InputHTMLAttributes<HTMLInputElement> {
	onPlaceSelect?: (
		address: string,
		details: google.maps.places.PlaceResult | null
	) => void;
	className?: string;
	locationType?: LocationType;
}

export function PlacesAutocomplete({
	onPlaceSelect,
	className,
	value = "",
	onChange,
	locationType = "precise",
	placeholder,
	...props
}: PlacesAutocompleteProps) {
	const { isLoaded, loadError } = useGoogleMapsScript();
	const [inputValue, setInputValue] = useState(value);
	const [predictions, setPredictions] = useState<
		google.maps.places.AutocompletePrediction[]
	>([]);
	const [showPredictions, setShowPredictions] = useState(false);
	const autocompleteService =
		useRef<google.maps.places.AutocompleteService | null>(null);
	const placesService = useRef<google.maps.places.PlacesService | null>(null);
	const [focusedIndex, setFocusedIndex] = useState(-1);
	const inputRef = useRef<HTMLInputElement>(null);

	useEffect(() => {
		if (isLoaded && !autocompleteService.current) {
			autocompleteService.current =
				new google.maps.places.AutocompleteService();
			placesService.current = new google.maps.places.PlacesService(
				document.createElement("div")
			);
		}
	}, [isLoaded]);

	useEffect(() => {
		setInputValue(value);
	}, [value]);

	const getAutocompleteOptions = useCallback(
		(input: string) => {
			const baseOptions = {
				input,
				componentRestrictions: { country: ["us", "ca"] }
			};

			if (locationType === "precise") {
				return {
					...baseOptions,
					types: ["street_address", "premise", "postal_code"]
				};
			}

			return {
				...baseOptions,
				types: ["(cities)"]
			};
		},
		[locationType]
	);

	const handleInput = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const input = e.target.value;
			setInputValue(input);
			onChange?.(e);

			if (!input || input.length < 3) {
				setPredictions([]);
				setShowPredictions(false);
				return;
			}

			if (!autocompleteService.current) return;

			autocompleteService.current.getPlacePredictions(
				getAutocompleteOptions(input),
				(predictions, status) => {
					if (
						status === google.maps.places.PlacesServiceStatus.OK &&
						predictions
					) {
						setPredictions(predictions);
						setShowPredictions(true);
					} else {
						setPredictions([]);
						setShowPredictions(false);
					}
				}
			);
		},
		[onChange, getAutocompleteOptions]
	);

	const handlePredictionSelect = (
		prediction: google.maps.places.AutocompletePrediction
	) => {
		if (!placesService.current) return;

		const fields =
			locationType === "precise"
				? ["formatted_address", "geometry"]
				: ["geometry", "address_components"];

		placesService.current.getDetails(
			{
				placeId: prediction.place_id,
				fields
			},
			(place, status) => {
				if (status === google.maps.places.PlacesServiceStatus.OK && place) {
					if (locationType === "city") {
						const cityComponent = place.address_components?.find((component) =>
							component.types.includes("locality")
						);
						const stateComponent = place.address_components?.find((component) =>
							component.types.includes("administrative_area_level_1")
						);

						if (cityComponent && stateComponent) {
							const formattedAddress = `${cityComponent.long_name}, ${stateComponent.short_name}`;
							onPlaceSelect?.(formattedAddress, place);
						}
					} else {
						onPlaceSelect?.(prediction.description, place);
					}
				}
			}
		);
		setShowPredictions(false);
	};

	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (!predictions.length) return;

		if (e.key === "ArrowDown") {
			e.preventDefault();
			setFocusedIndex((prev) =>
				prev < predictions.length - 1 ? prev + 1 : prev
			);
		} else if (e.key === "ArrowUp") {
			e.preventDefault();
			setFocusedIndex((prev) => (prev > 0 ? prev - 1 : prev));
		} else if (e.key === "Enter" && focusedIndex >= 0) {
			e.preventDefault();
			handlePredictionSelect(predictions[focusedIndex]);
		} else if (e.key === "Escape") {
			setShowPredictions(false);
		}
	};

	useEffect(() => {
		setFocusedIndex(-1);
	}, [predictions]);

	if (loadError) {
		return <Input type="text" {...props} className={className} />;
	}

	return (
		<div className={cn("relative", className)}>
			<Input
				{...props}
				type="text"
				value={inputValue}
				ref={inputRef}
				onChange={handleInput}
				onKeyDown={handleKeyDown}
				placeholder={placeholder}
				autoComplete="off"
				className={cn("w-full", className)}
			/>
			{showPredictions && predictions.length > 0 && (
				<ul className="absolute z-[9999] w-full bg-white border rounded-md shadow-lg mt-1 max-h-60 overflow-auto">
					{predictions.map((prediction, index) => (
						<li
							key={prediction.place_id}
							className={cn(
								"px-4 py-2 hover:bg-gray-100 cursor-pointer",
								focusedIndex === index && "bg-gray-100"
							)}
							onClick={() => handlePredictionSelect(prediction)}
						>
							{prediction.description}
						</li>
					))}
				</ul>
			)}
		</div>
	);
}
