"use client";
import dynamic from "next/dynamic";
import { useState } from "react";
import { Dialog, DialogContent } from "../ui/dialog";

const ProfilePasswordForm = dynamic(() => import("./profile-password-form"), {
	ssr: false
});

function Modal({
	open,
	onClose,
	children
}: {
	open: boolean;
	onClose: () => void;
	children: React.ReactNode;
}) {
	if (!open) return null;
	return (
		<div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
			<div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
				<button
					className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
					onClick={onClose}
					aria-label="Close"
				>
					×
				</button>
				{children}
			</div>
		</div>
	);
}

export function ProfileSettingsContent({ user }: { user: any }) {
	const [modalOpen, setModalOpen] = useState(false);
	const [formK<PERSON>, setFormKey] = useState(0); // to reset form on open

	const handlePasswordChangeSuccess = () => {
		setModalOpen(false);
		setFormKey((k) => k + 1); // reset form for next open
	};

	return (
		<div className="space-y-6">
			<h1 className="text-2xl font-semibold text-gray-900">Profile Settings</h1>

			<div className="bg-white shadow overflow-hidden sm:rounded-lg">
				<div className="px-4 py-5 sm:px-6">
					<h3 className="text-lg leading-6 font-medium text-gray-900">
						User Information
					</h3>
					<p className="mt-1 max-w-2xl text-sm text-gray-500">
						Personal details and company information.
					</p>
				</div>
				<div className="border-t border-gray-200">
					<dl>
						<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="text-sm font-medium text-gray-500">Full name</dt>
							<dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
								{user.first_name} {user.last_name}
							</dd>
						</div>
						<div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="text-sm font-medium text-gray-500">
								Email address
							</dt>
							<dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
								{user.email}
							</dd>
						</div>
						<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="text-sm font-medium text-gray-500">Role</dt>
							<dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
								{user.role.toUpperCase()}
							</dd>
						</div>
						<div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="text-sm font-medium text-gray-500">Company</dt>
							<dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
								{user.company?.name || "Not associated with a company"}
							</dd>
						</div>
						<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="text-sm font-medium text-gray-500">Last login</dt>
							<dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
								{user.last_login
									? new Date(user.last_login).toLocaleString()
									: "Not available"}
							</dd>
						</div>
					</dl>
				</div>
			</div>
			<div className="flex justify-end">
				<button
					className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
					onClick={() => setModalOpen(true)}
					data-testid="change-password-btn"
				>
					Change Password
				</button>
			</div>
			<Dialog open={modalOpen} onOpenChange={() => setModalOpen(false)}>
				<DialogContent>
					<h2 className="text-lg font-semibold mb-4">Change Password</h2>
					<ProfilePasswordForm
						key={formKey}
						onSuccess={handlePasswordChangeSuccess}
					/>
				</DialogContent>
			</Dialog>
		</div>
	);
}
