import prisma from "@/lib/prisma";
import { Company, User } from "@rvhelp/database";
import { getServerSession } from "next-auth";
import { authOptions } from "../../lib/auth";
import { DevelopmentNotice } from "../DevelopmentNotice";
import Navigation from "./Navigation";

interface UserWithCompany extends User {
	company: Company;
}

export default async function Header({
	children
}: {
	children: React.ReactNode;
}) {
	const session = await getServerSession(authOptions);
	let company = null;

	if (session?.user?.email) {
		const user = (await prisma.user.findFirst({
			where: { email: session.user.email },
			include: { company: true }
		})) as UserWithCompany;
		company = user?.company;
	}

	return (
		<div className="flex min-h-screen flex-col">
			<DevelopmentNotice />
			<Navigation company={company} />
			<main className="flex-1">
				<div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">{children}</div>
			</main>
		</div>
	);
}
