"use client";

import { ComponentManagerDialog } from "@/components/warranty/component-manager/component-manager-dialog";
import { ExtendedCompany, ExtendedComponent } from "@/types/warranty";
import { Company } from "@rvhelp/database";
import { signOut, useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

interface NavigationProps {
	company?: Company;
}

export default function Navigation({ company }: NavigationProps) {
	const pathname = usePathname();
	const router = useRouter();
	const { data: session } = useSession();
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [componentManagerOpen, setComponentManagerOpen] = useState(false);
	const [extendedCompany, setExtendedCompany] =
		useState<ExtendedCompany | null>(null);
	const [components, setComponents] = useState<ExtendedComponent[]>([]);
	const dropdownRef = useRef<HTMLDivElement>(null);
	const showBackButton = pathname !== "/dashboard";

	// Close dropdown when clicking outside
	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setDropdownOpen(false);
			}
		}

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	// Fetch company data with components when needed
	const fetchCompanyData = async () => {
		if (!company?.id) return;

		try {
			const response = await fetch(`/api/companies/${company.id}`);
			if (response.ok) {
				const data = await response.json();
				setExtendedCompany(data);
				setComponents(data.components || []);
			}
		} catch (error) {
			console.error("Failed to fetch company data:", error);
		}
	};

	const handleComponentManagerClick = async () => {
		setDropdownOpen(false);
		await fetchCompanyData();
		setComponentManagerOpen(true);
	};

	const handleComponentsUpdate = async (
		updatedComponents: ExtendedComponent[]
	) => {
		if (!company?.id) return;

		const newComponents = updatedComponents.filter((c) => !c.id);
		const existingComponents = updatedComponents.filter((c) => c.id);

		const requests: Promise<Response>[] = [];
		const requestTypes: ("POST" | "PUT")[] = [];

		if (newComponents.length > 0) {
			requests.push(
				fetch(`/api/companies/${company.id}/components`, {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify(
						newComponents.length === 1 ? newComponents[0] : newComponents
					)
				})
			);
			requestTypes.push("POST");
		}

		if (existingComponents.length > 0) {
			requests.push(
				fetch(`/api/companies/${company.id}/components`, {
					method: "PUT",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify(
						existingComponents.length === 1
							? existingComponents[0]
							: existingComponents
					)
				})
			);
			requestTypes.push("PUT");
		}

		try {
			const responses = await Promise.all(requests);
			let allUpdatedComponents = [...components];

			for (let i = 0; i < responses.length; i++) {
				const res = responses[i];
				if (!res.ok) {
					const error = await res.json();
					throw new Error(error.error || "Failed to update components");
				}
				const data = await res.json();

				if (requestTypes[i] === "POST") {
					const created = Array.isArray(data.results)
						? data.results.filter((r) => r.success).map((r) => r.component)
						: data.success
							? [data.component]
							: [];

					allUpdatedComponents = [
						...allUpdatedComponents.filter(
							(c) =>
								!newComponents.some(
									(nc) =>
										!nc.id &&
										c.type === nc.type &&
										c.manufacturer === nc.manufacturer
								)
						),
						...created
					];
				} else if (requestTypes[i] === "PUT") {
					const updated = Array.isArray(data.results)
						? data.results.filter((r) => r.success).map((r) => r.updated)
						: data.success
							? [data.updated]
							: [];

					updated.forEach((upd) => {
						const idx = allUpdatedComponents.findIndex((c) => c.id === upd.id);
						if (idx !== -1) {
							allUpdatedComponents[idx] = upd;
						}
					});
				}
			}

			setComponents(allUpdatedComponents);
			if (extendedCompany) {
				setExtendedCompany({
					...extendedCompany,
					components: allUpdatedComponents
				});
			}
		} catch (error) {
			console.error("Failed to update components:", error);
			throw error;
		}
	};

	const handleSignOut = async () => {
		await signOut({ redirect: false });
		router.push("/login");
	};

	return (
		<>
			<nav className="bg-white shadow-sm">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between items-center h-16">
						<div className="flex items-center space-x-8">
							{showBackButton && (
								<button
									onClick={() => router.back()}
									className="text-gray-500 hover:text-gray-700"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										strokeWidth={1.5}
										stroke="currentColor"
										className="w-6 h-6"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18"
										/>
									</svg>
								</button>
							)}
							<div className="flex items-center space-x-6">
								<Link href="/dashboard" className="flex items-center">
									<Image
										src={"/logos/logo.png"}
										alt={`RV Help Logo`}
										width={150}
										height={40}
										className="h-10 w-auto"
									/>
									<span className="pt-1 ml-4 text-lg font-semibold text-gray-900">
										RV Help OEM Partner Portal
									</span>
								</Link>
							</div>
						</div>

						{/* User Profile Dropdown */}
						<div className="relative" ref={dropdownRef}>
							<button
								onClick={() => setDropdownOpen(!dropdownOpen)}
								className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none"
							>
								<div className="flex items-center space-x-2">
									<div className="bg-gray-200 rounded-full p-1">
										<svg
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 24 24"
											strokeWidth={1.5}
											stroke="currentColor"
											className="w-6 h-6"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z"
											/>
										</svg>
									</div>
									<span className="text-sm font-medium hidden sm:block">
										{session?.user?.name || session?.user?.email || "Account"}
									</span>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										strokeWidth={1.5}
										stroke="currentColor"
										className="w-4 h-4"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											d="M19.5 8.25l-7.5 7.5-7.5-7.5"
										/>
									</svg>
								</div>
							</button>

							{dropdownOpen && (
								<div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 ring-1 ring-black ring-opacity-5">
									{company?.name && (
										<div className="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
											<p className="font-medium">{company?.name}</p>
											<p className="text-xs text-gray-500">Partner Portal</p>
										</div>
									)}
									<Link
										href="/dashboard"
										className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
									>
										Dashboard
									</Link>
									<button
										onClick={handleComponentManagerClick}
										disabled={!session?.user}
										className={`block w-full text-left px-4 py-2 text-sm ${
											session?.user
												? "text-gray-700 hover:bg-gray-100"
												: "text-gray-400 cursor-not-allowed"
										}`}
									>
										Component Manager
									</button>
									<Link
										href="/profile"
										className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
									>
										Profile Settings
									</Link>
									<button
										onClick={handleSignOut}
										className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
									>
										Sign Out
									</button>
								</div>
							)}
						</div>
					</div>
				</div>
			</nav>

			{/* Component Manager Modal */}
			{extendedCompany && (
				<ComponentManagerDialog
					open={componentManagerOpen}
					onClose={() => setComponentManagerOpen(false)}
					components={components}
					onUpdate={handleComponentsUpdate}
					company={extendedCompany}
				/>
			)}
		</>
	);
}
