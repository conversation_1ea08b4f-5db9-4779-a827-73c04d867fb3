"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import config from "@/config";
import { ExtendedCompany } from "@/types/warranty";
import { useSession } from "next-auth/react";
import { useWarrantyRequestWizard } from "./use-request-wizard";

interface WarrantyRequestWizardProps {
	company: ExtendedCompany;
	initialRequest?: any; // Should match ExtendedWarrantyRequest
	onCancel?: () => void;
	onSuccess?: (updatedRequest?: any) => void;
}

const WarrantyRequestWizardSkeleton = () => (
	<Card>
		<CardHeader>
			<Skeleton className="h-8 w-48" />
			<Skeleton className="h-4 w-64 mt-2" />
		</CardHeader>
		<CardContent>
			<div className="space-y-6">
				<Skeleton className="h-6 w-32" />
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Skeleton className="h-10 w-full" />
					<Skeleton className="h-10 w-full" />
				</div>
				<Skeleton className="h-6 w-32" />
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Skeleton className="h-10 w-full" />
					<Skeleton className="h-10 w-full" />
				</div>
			</div>
		</CardContent>
	</Card>
);

export default function WarrantyRequestWizard({
	company,
	initialRequest,
	onCancel,
	onSuccess
}: WarrantyRequestWizardProps) {
	const { data: session } = useSession();

	const {
		// State
		isSubmitting,
		isSuccess,
		currentStep,
		currentRequest,
		form,
		// Render
		renderCurrentStep,
		// Actions
		handleNext,
		handlePrevious,
		handleSubmit,
		handleCancel,
		handleSuccess,
		// Configuration
		STEPS,
		// Development
		fillDummyRVValues
	} = useWarrantyRequestWizard({
		company,
		initialRequest,
		onCancel,
		onSuccess
	});

	if (!session?.user) {
		return <WarrantyRequestWizardSkeleton />;
	}

	// Success state
	if (isSuccess) {
		return (
			<div className="space-y-4">
				<Card className="max-h-[80vh] overflow-y-auto">
					<CardHeader>
						<CardTitle className="text-center text-green-600">
							✓ Request Submitted Successfully!
						</CardTitle>
						<CardDescription className="text-center">
							Your warranty service request has been submitted and is being
							processed.
						</CardDescription>
					</CardHeader>
					<CardContent className="text-center">
						<div className="py-8">
							<div
								className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-white text-2xl"
								style={{ backgroundColor: company.brand_color || "#2563eb" }}
							>
								✓
							</div>
							<h3 className="text-lg font-semibold mb-2">
								{currentRequest?.id ? "Request Updated" : "Request Created"}
							</h3>
							<p className="text-muted-foreground mb-6">
								{currentRequest?.id
									? "Your warranty service request has been updated successfully."
									: "Your warranty service request has been created and submitted for review."}
							</p>
							<Button
								size="lg"
								onClick={handleSuccess}
								style={{
									backgroundColor: company.brand_color || "#2563eb",
									color: "#fff"
								}}
								className="px-8 py-3 text-lg"
							>
								Close
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<form className="space-y-4" onSubmit={form.handleSubmit(() => {})}>
			<Card className="max-h-[80vh] overflow-y-auto">
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle>
								{currentRequest?.id
									? "Edit Warranty Service Request"
									: "New Warranty Service Request"}
							</CardTitle>
							<CardDescription>
								Step {currentStep} of {STEPS.length}:{" "}
								{STEPS[currentStep - 1].title}
							</CardDescription>
						</div>
						<div className="text-sm text-muted-foreground">
							{STEPS[currentStep - 1].description}
						</div>
					</div>

					{/* Progress Indicator */}
					<div className="flex items-center space-x-2 mt-4">
						{STEPS.map((step, index) => (
							<div key={step.id} className="flex items-center">
								<div
									className={`w-4 h-4 rounded-full flex items-center justify-center text-sm font-medium ${
										currentStep >= step.id
											? "text-primary-foreground"
											: "bg-muted text-muted-foreground"
									}`}
									style={
										currentStep >= step.id
											? { backgroundColor: company.brand_color || "#2563eb" }
											: undefined
									}
								>
									{step.id}
								</div>
								{index < STEPS.length - 1 && (
									<div
										className={`w-4 h-0.5 mx-2 ${currentStep > step.id ? "" : "bg-muted"}`}
										style={
											currentStep > step.id
												? {
														backgroundColor: company.brand_color || "#2563eb"
													}
												: undefined
										}
									/>
								)}
							</div>
						))}
					</div>
				</CardHeader>

				<CardContent>
					{/* Development helper button for RV information step */}
					{config.isDevelopment && currentStep === 3 && (
						<div className="mb-4 pt-4 border-t border-dashed border-gray-300">
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={fillDummyRVValues}
								className="text-xs text-gray-600 border-gray-300 hover:bg-gray-50"
							>
								🔧 Use Dummy Values (Dev)
							</Button>
						</div>
					)}
					{renderCurrentStep()}

					{/* Navigation */}
					<div className="flex justify-between mt-6 pt-4 border-t">
						<div className="flex gap-2">
							<Button
								type="button"
								variant="outline"
								onClick={handleCancel}
								disabled={isSubmitting}
								style={{
									color: company.brand_color || "#2563eb",
									borderColor: company.brand_color || "#2563eb"
								}}
							>
								Cancel
							</Button>
							{currentStep > 1 && (
								<Button
									type="button"
									variant="outline"
									onClick={handlePrevious}
									disabled={isSubmitting}
									style={{
										color: company.brand_color || "#2563eb",
										borderColor: company.brand_color || "#2563eb"
									}}
								>
									Previous
								</Button>
							)}
						</div>

						<div>
							{currentStep < STEPS.length ? (
								<Button
									type="button"
									onClick={handleNext}
									disabled={isSubmitting}
									style={{
										backgroundColor: company.brand_color || "#2563eb",
										color: "#fff"
									}}
								>
									Next
								</Button>
							) : (
								<Button
									type="button"
									onClick={handleSubmit}
									disabled={isSubmitting}
									style={{
										backgroundColor: company.brand_color || "#2563eb",
										color: "#fff"
									}}
								>
									{isSubmitting
										? currentRequest
											? "Saving..."
											: "Submitting..."
										: currentRequest
											? "Save Changes"
											: "Submit Request"}
								</Button>
							)}
						</div>
					</div>
				</CardContent>
			</Card>
		</form>
	);
}
