import { z } from 'zod';

export const warrantyRequestSchema = z.object({
    // Service Request Details
    id: z.string().min(1, 'ID is required'),
    complaint: z.string().min(20, 'Please provide a description of the issue (at least 20 characters)'),
    cause: z.string().optional(),
    correction: z.string().optional(),
    authorization_type: z.enum(['SPECIFIC', 'GENERAL']),
    component_id: z.string().min(1, 'Component is required'),
    notes_to_provider: z.string().optional().nullable(),
    // Customer Information
    first_name: z.string().min(1, 'First name is required'),
    last_name: z.string().min(1, 'Last name is required'),
    email: z.string().email('Valid email is required'),
    phone: z.string().min(10, 'Valid phone number is required'),
    contact_preference: z.enum(['phone', 'sms', 'email']).optional(),
    requires_return: z.boolean().optional(),
    // Location Information
    location: z
        .object({
            address: z.string().min(1, 'Address is required'),
            latitude: z.number(),
            longitude: z.number(),
        })
        .optional(),

    attachments: z
        .array(
            z.object({
                id: z.string().optional().nullable(),
                title: z.string(),
                type: z.string(),
                url: z.string(),
                required: z.boolean(),
                component_id: z.string().optional().nullable(),
                completed: z.boolean().optional(),
            })
        )
        .optional()
        .nullable(),

    // RV Information
    rv_vin: z
        .string()
        .min(17, 'VIN must be exactly 17 characters')
        .max(17, 'VIN must be exactly 17 characters'),
    rv_make: z.string().min(1, 'Make is required'),
    rv_model: z.string().min(1, 'Model is required'),
    rv_year: z.string().min(1, 'Year is required'),
    rv_type: z.string().min(1, 'Type is required'),

    // Additional Information
    // estimated_hours: z.number().min(0).optional(),
    approved_hours: z
        .number({
            required_error: 'Approved hours is required',
            invalid_type_error: 'Please enter a valid number',
        })
        .min(0.1, 'Approved hours must be at least 0.1')
        .max(1000, 'Approved hours cannot exceed 1000')
        .optional(),
});
