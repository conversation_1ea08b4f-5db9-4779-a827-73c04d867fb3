"use client";

import { Input } from "@/components/ui/input";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import PhoneInput from "../../../ui/phone-input";

// Define the schema for RV information
export const customerInfoSchema = z.object({
	// RV Information
	// Customer Information
	first_name: z.string().min(1, "First name is required"),
	last_name: z.string().min(1, "Last name is required"),
	email: z.string().email("Valid email is required"),
	phone: z.string().min(10, "Valid phone number is required")
});

export type CustomerInformationStepData = z.infer<typeof customerInfoSchema>;

interface CustomerInfoFormProps {
	form: UseFormReturn<CustomerInformationStepData>;
	company: ExtendedCompany;
	request?: ExtendedWarrantyRequest;
}

export function CustomerInfoForm({
	form,
	company,
	request
}: CustomerInfoFormProps) {
	const { register, formState } = form;

	useEffect(() => {
		if (request) {
			form.setValue("first_name", request.first_name);
			form.setValue("last_name", request.last_name);
			form.setValue("email", request.email);
			form.setValue("phone", request.phone);
		}
	}, [request, form]);
	return (
		<div className="space-y-6">
			{/* Customer Information */}
			<div>
				<div className="flex items-center gap-2 font-bold text-sm text-slate-700 mb-4">
					<div
						className="rounded-full p-1"
						style={{ backgroundColor: company.brand_color || "#2563eb" }}
					>
						<svg
							className="w-4 h-4 text-white"
							fill="currentColor"
							viewBox="0 0 20 20"
						>
							<path
								fillRule="evenodd"
								d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
								clipRule="evenodd"
							/>
						</svg>
					</div>
					Customer Information
				</div>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Input
						{...register("first_name")}
						error={String(formState.errors.first_name?.message || "")}
						placeholder="Enter first name"
						label="First Name"
						required
					/>
					<Input
						{...register("last_name")}
						error={String(formState.errors.last_name?.message || "")}
						placeholder="Enter last name"
						label="Last Name"
						required
					/>
					<Input
						type="email"
						{...register("email")}
						error={String(formState.errors.email?.message || "")}
						placeholder="Enter email"
						label="Email"
						required
					/>
					<PhoneInput
						{...register("phone")}
						value={form.watch("phone")}
						onChange={(value) => {
							form.setValue("phone", value);
						}}
						className="w-full"
						error={String(formState.errors.phone?.message || "")}
						label="Phone"
						required
					/>
				</div>
			</div>
		</div>
	);
}
