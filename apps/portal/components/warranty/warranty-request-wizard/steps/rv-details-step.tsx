"use client";

import { SearchableSelect } from "@/components/SearchableSelect";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { allRvMakes, popularRvMakes } from "@/data/rv_makes";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import { useEffect, useMemo } from "react";
import { Controller, UseFormReturn } from "react-hook-form";
import { z } from "zod";

// Define the schema for RV information
export const RVDetailsSchema = z.object({
	// RV Information
	rv_vin: z
		.string()
		.min(17, "VIN must be exactly 17 characters")
		.max(17, "VIN must be exactly 17 characters"),
	rv_make: z.string().nonempty("Make is required").default(""),
	rv_model: z.string().min(1, "Model is required"),
	rv_year: z.string().min(1, "Year is required"),
	rv_type: z.string().min(1, "Type is required")
});

interface RVDetailsFormProps {
	form: UseFormReturn<any>;
	request?: ExtendedWarrantyRequest;
	company: ExtendedCompany;
}

export function RVDetailsForm({ form, company, request }: RVDetailsFormProps) {
	const { register, formState } = form;

	useEffect(() => {
		if (request) {
			form.setValue("rv_vin", request.rv_vin);
			form.setValue("rv_make", request.rv_make);
			form.setValue("rv_model", request.rv_model);
			form.setValue("rv_year", request.rv_year);
			form.setValue("rv_type", request.rv_type);
		} else {
			form.setValue("rv_make", company.name);
		}
	}, [company, request, form]);

	// Create the make options in the same format as the profile form
	const makeOptions = useMemo(
		() => [
			{
				label: "Popular Makes",
				options: popularRvMakes.map((make) => ({
					value: make,
					label: make
				}))
			},
			{
				label: "All Makes",
				options: allRvMakes.map((make) => ({
					value: make,
					label: make
				}))
			}
		],
		[]
	);

	return (
		<div className="space-y-6">
			<div>
				<div className="flex items-center gap-2 font-bold text-sm text-slate-700 mb-4">
					<div
						className="rounded-full p-1"
						style={{ backgroundColor: company.brand_color || "#2563eb" }}
					>
						<svg
							className="w-4 h-4 text-white"
							fill="currentColor"
							viewBox="0 0 20 20"
						>
							<path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zm7 0a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
							<path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H17a1 1 0 001-1v-1h-1.5a.5.5 0 01-.5-.5v-3a.5.5 0 01.5-.5H18a1 1 0 00.95-1.316l-1.5-4A1 1 0 0017 4H3zm11.5 5h2.17l-1.25-3.5h-1.84l.92 3.5z" />
						</svg>
					</div>
					RV Information
				</div>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="md:col-span-2">
						<Input
							{...register("rv_vin")}
							error={String(formState.errors.rv_vin?.message || "")}
							placeholder="Enter VIN"
							label="VIN"
							required
						/>
					</div>
					<div>
						<Label required={true} className="block text-sm font-medium mb-2">
							Manufacturer
						</Label>
						<Controller
							name="rv_make"
							control={form.control}
							rules={{ required: "Make is required" }}
							render={({ field }) => (
								<>
									<SearchableSelect
										name={field.name}
										options={makeOptions}
										value={
											makeOptions
												.flatMap((group) => group.options)
												.find((option) => option.value === field.value) || null
										}
										onChange={(option) =>
											field.onChange(option ? option.value : "")
										}
										placeholder="Select RV make"
										error={form.formState.errors.rv_make as any}
									/>
									{formState.errors.rv_make && (
										<p className="text-red-500 text-sm mt-1">
											{String(formState.errors.rv_make.message || "")}
										</p>
									)}
								</>
							)}
						/>
					</div>
					<Input
						{...register("rv_model")}
						error={String(formState.errors.rv_model?.message || "")}
						placeholder="Enter model"
						label="Model"
						required
					/>
					<Input
						{...register("rv_year")}
						error={String(formState.errors.rv_year?.message || "")}
						placeholder="Enter year"
						label="Year"
						required
					/>
					<Controller
						name="rv_type"
						control={form.control}
						render={({ field }) => (
							<div>
								<label className="block text-sm font-medium mb-2">
									Type <span className="text-red-500">*</span>
								</label>
								<Select
									key={field.value}
									onValueChange={(val) => {
										field.onChange(val);
										form.clearErrors("rv_type");
									}}
									value={field.value || ""}
								>
									<SelectTrigger type="button">
										<SelectValue placeholder="Select RV type" />
									</SelectTrigger>
									<SelectContent>
										{/* TODO:  This will need to configurable in the DB per company in the future
										<SelectItem value="Class A">Class A</SelectItem>
										<SelectItem value="Class B">Class B</SelectItem>
										<SelectItem value="Class C">Class C</SelectItem> 
										*/}
										<SelectItem value="Travel Trailer">
											Travel Trailer
										</SelectItem>
										<SelectItem value="Fifth Wheel">Fifth Wheel</SelectItem>
										<SelectItem value="Truck Camper">Truck Camper</SelectItem>
										<SelectItem value="Toy Hauler">Toy Hauler</SelectItem>
									</SelectContent>
								</Select>
								{formState.errors.rv_type && (
									<p className="text-red-500 text-sm mt-1">
										{String(formState.errors.rv_type.message || "")}
									</p>
								)}
							</div>
						)}
					/>
				</div>
			</div>
		</div>
	);
}
