"use client";

import { PlacesAutocomplete } from "@/components/places/PlacesAutocomplete";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import { useCallback, useEffect, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

// Define the schema for service location
export const LocationSchema = z.object({
	location: z.object({
		address: z.string().min(1, "Address is required"),
		latitude: z.number().min(0.1, "Latitude is required"),
		longitude: z.number().min(-300, "Longitude is required")
	})
});

type LocationFormData = z.infer<typeof LocationSchema>;

interface LocationFormProps {
	form: UseFormReturn<LocationFormData>;
	company: ExtendedCompany;
	request?: ExtendedWarrantyRequest;
}

export function LocationForm({ form, company, request }: LocationFormProps) {
	const {
		setValue,
		watch,
		formState: { errors }
	} = form;

	const [providerCount, setProviderCount] = useState(0);
	const [certifiedMasterProviderCount, setCertifiedMasterProviderCount] =
		useState(0);
	const [registeredProviderCount, setRegisteredProviderCount] = useState(0);
	const [isSearchingProviders, setIsSearchingProviders] = useState(false);

	const fetchProviders = useCallback(async () => {
		setIsSearchingProviders(true);

		const params = new URLSearchParams({
			radius: "70",
			category: "rv-repair",
			subcategory: "",
			page: "1",
			limit: "25",
			sortBy: "default"
		});

		params.set("lat", watch("location.latitude").toString());
		params.set("lng", watch("location.longitude").toString());

		try {
			const response = await fetch(`/api/search?${params}`);
			if (response.ok) {
				const data = await response.json();

				setProviderCount(data.total || 0);
				setCertifiedMasterProviderCount(
					data.listings.filter(
						(listing: any) =>
							listing.rvtaa_technician_level === 2 ||
							listing.rvtaa_technician_level === 3
					).length
				);
				setRegisteredProviderCount(
					data.listings.filter(
						(listing: any) => listing.rvtaa_technician_level === 1
					).length
				);
			} else {
				console.error("Failed to fetch providers");
			}
		} catch (error) {
			console.error("Error fetching providers:", error);
		} finally {
			setIsSearchingProviders(false);
		}
	}, [watch]);

	const handleLocationSelect = (address: string, details: any) => {
		if (details?.geometry?.location) {
			const locationData = {
				address,
				latitude: details.geometry.location.lat(),
				longitude: details.geometry.location.lng()
			};

			setValue("location", locationData);

			fetchProviders();
		}
	};

	useEffect(() => {
		if (request) {
			setValue("location", request.location);
		}
	}, [request, setValue]);

	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			// Clear any validation errors on location as the user begins typing
			form.clearErrors("location");
			if (!e.target.value) {
				setValue("location", { address: "", latitude: 0, longitude: 0 });
			}
		},
		[setValue, form]
	);

	return (
		<div className="space-y-6">
			{/* Location Information */}
			<div>
				<div className="flex items-center gap-2 font-bold text-sm text-slate-700 mb-4">
					<div
						className="rounded-full p-1"
						style={{ backgroundColor: company.brand_color || "#2563eb" }}
					>
						<svg
							className="w-4 h-4 text-white"
							fill="currentColor"
							viewBox="0 0 20 20"
						>
							<path
								fillRule="evenodd"
								d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
								clipRule="evenodd"
							/>
						</svg>
					</div>
					Service Location
				</div>
				<div className="space-y-4">
					<div>
						<label className="block text-sm font-medium mb-1">
							Address for Service <span className="text-red-500">*</span>
						</label>
						<PlacesAutocomplete
							placeholder="Enter the address where service is needed"
							value={watch("location")?.address || ""}
							onPlaceSelect={handleLocationSelect}
							onChange={handleInputChange}
							locationType="precise"
							className="focus:ring-primary focus:border-primary transition-all duration-200"
						/>
						{errors.location?.address && (
							<p className="text-sm text-red-500 mt-1">
								{String(errors.location.address.message || "")}
							</p>
						)}
						<div className="min-h-[100px]">
							{/* Provider Counts Display */}
							{watch("location")?.address && (
								<div className="mt-4 p-3 bg-slate-50 border border-slate-200 rounded">
									<div className="font-semibold text-sm mb-1 text-slate-700">
										Nearby Providers (within 100 mile radius)
									</div>
									<div className="text-sm text-slate-600">
										Total Providers:{" "}
										<span className="font-bold">{providerCount}</span>
									</div>
									<div className="text-sm text-green-700">
										Certified/Master Providers:{" "}
										<span className="font-bold">
											{certifiedMasterProviderCount}
										</span>
									</div>
									<div className="text-sm text-yellow-700">
										Registered Providers:{" "}
										<span className="font-bold">{registeredProviderCount}</span>
									</div>
									{isSearchingProviders && (
										<div className="text-sm text-blue-600 mt-2 flex items-center gap-2">
											<svg
												className="animate-spin h-4 w-4"
												fill="none"
												viewBox="0 0 24 24"
											>
												<circle
													className="opacity-25"
													cx="12"
													cy="12"
													r="10"
													stroke="currentColor"
													strokeWidth="4"
												></circle>
												<path
													className="opacity-75"
													fill="currentColor"
													d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
												></path>
											</svg>
											Searching for providers...
										</div>
									)}
									{!isSearchingProviders && providerCount === 0 && (
										<div className="text-sm text-red-500 mt-2">
											No providers found in this area. Please verify the address
											or try a different location.
										</div>
									)}
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
