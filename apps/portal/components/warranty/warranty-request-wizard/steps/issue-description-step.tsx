"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ComponentManagerDialog } from "@/components/warranty/component-manager/component-manager-dialog";
import {
	ExtendedCompany,
	ExtendedComponent,
	ExtendedWarrantyRequest,
	WarrantyAttachment
} from "@/types/warranty";
import { Loader2, Paperclip, X } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

export const IssueDescriptionSchema = z.object({
	// Service Request Details
	id: z.string().min(1, "ID is required"),
	complaint: z
		.string()
		.min(
			20,
			"Please provide a description of the issue (at least 20 characters)"
		)
		.max(1000, "Description cannot exceed 1000 characters")
		.optional(),
	notes_to_provider: z.string().optional().nullable(),
	component_id: z.string().optional(),
	requires_return: z.boolean().optional(),
	attachments: z
		.array(
			z.object({
				id: z.string().optional().nullable(),
				title: z.string(),
				type: z.string(),
				url: z.string(),
				required: z.boolean(),
				component_id: z.string().optional(),
				completed: z.boolean().optional()
			})
		)
		.optional()
		.nullable()
});

type IssueDescriptionStepData = z.infer<typeof IssueDescriptionSchema>;

interface IssueDescriptionStepProps {
	form: UseFormReturn<IssueDescriptionStepData>;
	company: ExtendedCompany;
	request?: ExtendedWarrantyRequest;

	componentManager?: {
		handleComponentsUpdate: (
			components: ExtendedComponent[]
		) => Promise<ExtendedComponent[]>;
		deleteComponent: (componentId: string) => Promise<void>;
		isUpdating: boolean;
	};
}

export function IssueDescriptionStep({
	company,
	form,
	request,
	componentManager
}: IssueDescriptionStepProps) {
	const { register, formState, setValue, watch, clearErrors } = form;
	const [ComponentManagerModalOpen, setComponentManagerModalOpen] =
		useState(false);
	const [isUploading, setIsUploading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const [componentList, setComponentList] = useState<ExtendedComponent[]>([]);

	// Watch the component_id field to sync with our internal dropdown state
	const selectedComponentId = watch("component_id");

	useEffect(() => {
		if (request) {
			form.setValue("id", request.id);
			form.setValue("complaint", request.complaint);
			form.setValue("component_id", request.component?.id);
			form.setValue("notes_to_provider", request.notes_to_provider);
			form.setValue("requires_return", request.requires_return);
			form.setValue("attachments", request.attachments);
		}
	}, [request, form]);

	// Memoize component options to prevent recalculation on every render
	useEffect(() => {
		setComponentList(company.components);
	}, [company]);

	const componentOptions = useMemo(() => {
		return componentList
			.slice()
			.sort((a, b) => {
				const typeCompare = a.type.localeCompare(b.type);
				if (typeCompare !== 0) return typeCompare;
				return a.manufacturer.localeCompare(b.manufacturer);
			})
			.map((component) => ({
				id: component.id,
				value: `${component.type} - ${component.manufacturer}`,
				label: `${component.type} - ${component.manufacturer}`
			}));
	}, [componentList]);

	// Memoize handlers to prevent recreation on every render
	const handleComponentChange = useCallback(
		(componentId: string, component?: ExtendedComponent) => {
			setValue("component_id", componentId);
			clearErrors("component_id");

			// Get current attachments
			const currentAttachments = watch("attachments") || [];

			// Filter out attachments that have a component_id different from the new component
			// Keep attachments without component_id and those matching the new component
			const filteredAttachments = currentAttachments.filter(
				(attachment: WarrantyAttachment) =>
					attachment.completed ||
					!attachment.component_id ||
					attachment.component_id === componentId
			);

			// When changing components. keep completed forms, but mark them not as required
			const completed = currentAttachments.find(
				(attachment: WarrantyAttachment) => attachment.completed
			);
			if (completed && completed.component_id !== componentId) {
				completed.required = false;
			}
			// When changing components. keep completed forms, but mark them as required if we are changing back
			else if (completed && completed.component_id === componentId) {
				completed.required = true;
			}

			// Find the selected component and add its attachments if any
			let updatedAttachments = [...filteredAttachments];
			if (componentId) {
				const selectedComponent =
					component || company.components.find((c) => c.id === componentId);
				if (selectedComponent && selectedComponent.attachments) {
					// Add component attachments that aren't already in the list
					const componentAttachments = selectedComponent.attachments.filter(
						(compAttachment) =>
							compAttachment.title &&
							!updatedAttachments.some(
								(existing) => existing.title === compAttachment.title
							)
					);
					updatedAttachments = [...updatedAttachments, ...componentAttachments];
				}
			}

			// Update the form with the new attachments
			setValue("attachments", updatedAttachments);
		},
		[setValue, watch, company.components, clearErrors]
	);

	const handleComponentsUpdate = useCallback(
		async (updatedComponents: ExtendedComponent[]) => {
			const returned: ExtendedComponent[] = updatedComponents;

			// Find a new component (not in company.components)
			if (
				returned.length === 1 &&
				!company.components.some((c) => c.id === returned[0].id)
			) {
				// This is a new component, set it as selected
				setValue("component_id", returned[0].id);
			}

			// If the currently selected component_id is in the updated components, update its attachments
			if (
				selectedComponentId &&
				updatedComponents.some((c) => c.id === selectedComponentId)
			) {
				handleComponentChange(
					selectedComponentId,
					returned.find((c) => c.id === selectedComponentId)
				);
			}

			// Create a new component list by replacing existing components and adding new ones
			const updatedComponentList = [
				// Keep components that weren't changed
				...componentList.filter((c) => !returned.some((r) => r.id === c.id)),
				// Add all returned components (new + changed)
				...returned
			];

			setComponentList(updatedComponentList);

			setComponentManagerModalOpen(false);
		},
		[
			company.components,
			setValue,
			selectedComponentId,
			handleComponentChange,
			componentList
		]
	);
	const attachments = watch("attachments") || [];

	const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const files = e.target.files;
		if (!files || files.length === 0) return;

		setIsUploading(true);
		try {
			for (let i = 0; i < files.length; i++) {
				const file = files[i];
				const fName = file.name;
				const newFilename = file.name.toLowerCase().replace(/\s+/g, "_");
				let title = file.name.split(".").shift() || "";
				title = title.replace(/_/g, " ");
				// capitalize first letter of each work
				title = title
					.split(" ")
					.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
					.join(" ");
				// Determine attachment type based on file extension
				const extension = fName.split(".").pop()?.toLowerCase();
				let type = "document";

				if (["jpg", "jpeg", "png", "gif", "webp"].includes(extension || "")) {
					type = "image";
				} else if (["pdf"].includes(extension || "")) {
					type = "document";
				} else if (["doc", "docx"].includes(extension || "")) {
					type = "document";
				}

				const formData = new FormData();
				formData.append("file", file);
				formData.append("fileName", newFilename);
				formData.append(
					"path",
					`/${company.id}/warranty-requests/${form.getValues("id")}`
				);

				const response = await fetch("/api/storage", {
					method: "POST",
					body: formData
				});

				if (!response.ok) {
					throw new Error("Failed to upload file");
				}

				const data = await response.json();
				const newAttachment: WarrantyAttachment = {
					id: undefined,
					type,
					title: title,
					url: data.url,
					required: false,
					completed: false,
					component_id: null
				};

				setValue("attachments", [...attachments, newAttachment]);
			}
		} catch (error) {
			console.error("Error uploading file:", error);
			toast.error("Failed to upload file");
		} finally {
			setIsUploading(false);
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}
		}
	};

	const removeAttachment = (title: string) => {
		const updatedAttachments = attachments.filter(
			(attachment: WarrantyAttachment) => attachment.title !== title
		);
		setValue("attachments", updatedAttachments);
	};

	const toggleAttachmentRequired = (title: string, required: boolean) => {
		const updatedAttachments = attachments.map(
			(attachment: WarrantyAttachment) =>
				attachment.title === title ? { ...attachment, required } : attachment
		);
		setValue("attachments", updatedAttachments);
	};

	return (
		<div className="space-y-6">
			{/* Service Request Details */}
			<div>
				<div className="font-semibold mb-4">Service Request Details</div>
				<div className="space-y-4">
					<Textarea
						{...register("complaint")}
						error={String(formState.errors.complaint?.message || "")}
						placeholder="Please describe the issue in detail"
						rows={3}
						label={"Description of Issue"}
						required
					/>
					<p className="text-sm text-red-600 mt-1">
						⚠️ Warning: The contents of this field will be visible to the
						customer
					</p>
					<Textarea
						{...register("notes_to_provider")}
						error={String(formState.errors.notes_to_provider?.message || "")}
						placeholder="Add any internal notes for the provider (not visible to customer)"
						rows={3}
						label={"Notes to Provider"}
					/>
					{company.components.length > 0 && (
						<div className="space-y-2">
							<Label
								required
								className="block text-sm font-medium text-gray-700"
							>
								Component
							</Label>
							<div className="flex gap-2">
								<select
									value={selectedComponentId || ""}
									onChange={(e) => handleComponentChange(e.target.value)}
									aria-label="Component"
									className={`flex-1 px-3 py-2 border rounded-md ${
										formState.errors.component_id
											? "border-red-500"
											: "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
									}`}
								>
									<option value="">Select a component</option>
									{componentOptions.map((option) => (
										<option key={option.id} value={option.id}>
											{option.label}
										</option>
									))}
								</select>
								<Button
									type="button"
									variant="outline"
									onClick={() => setComponentManagerModalOpen(true)}
									className="px-4 py-2 whitespace-nowrap"
									style={{
										color: company.brand_color,
										borderColor: company.brand_color
									}}
								>
									Manage
								</Button>
							</div>
							{formState.errors.component_id && (
								<p className="text-sm text-red-500">
									{formState.errors.component_id.message}
								</p>
							)}
						</div>
					)}

					{/* Requires Return Checkbox */}
					<div className="flex items-center space-x-2">
						<Checkbox
							id="requires-return"
							{...register("requires_return")}
							checked={watch("requires_return") || false}
							onCheckedChange={(checked) =>
								setValue("requires_return", checked as boolean)
							}
						/>
						<Label
							htmlFor="requires-return"
							className="text-sm font-medium text-gray-700"
						>
							Requires return of original component
						</Label>
					</div>

					<div className="space-y-4">
						<div>
							<Label>
								Attachments (Attach any photos or documents from customer)
							</Label>
							<div className="mt-2">
								<Input
									type="file"
									ref={fileInputRef}
									onChange={handleFileChange}
									multiple
									className="hidden"
									accept="*"
								/>
								<Button
									type="button"
									variant="outline"
									onClick={() => fileInputRef.current?.click()}
									disabled={isUploading}
									className="w-full"
									style={{
										color: company.brand_color,
										borderColor: company.brand_color
									}}
								>
									{isUploading ? (
										<>
											<Loader2 className="mr-2 h-4 w-4 animate-spin" />
											Uploading...
										</>
									) : (
										<>
											<Paperclip className="mr-2 h-4 w-4" />
											Attach Files and Photos
										</>
									)}
								</Button>
							</div>

							{attachments.length > 0 && (
								<div className="mt-4 space-y-2">
									{attachments.map((attachment: WarrantyAttachment) => (
										<div
											key={attachment.id}
											className="flex items-center justify-between p-2 bg-gray-50 rounded-md"
										>
											<div className="flex items-center space-x-2">
												<Paperclip className="h-4 w-4 text-gray-500" />
												<div className="flex flex-col space-y-1 min-w-0 flex-1">
													<div className="flex items-center space-x-2">
														<span className="text-sm font-medium truncate max-w-[200px]">
															{attachment.title}
														</span>
														<span
															className={`px-2 py-0.5 text-xs rounded-full font-medium ${
																attachment.type === "image"
																	? "bg-blue-100 text-blue-800"
																	: "bg-gray-100 text-gray-800"
															}`}
														>
															{attachment.type}
														</span>
														{attachment.required && (
															<span className="px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-800 font-medium">
																Required
															</span>
														)}
													</div>
												</div>
											</div>
											<div className="flex items-center space-x-2">
												{attachment.type === "form" && (
													<div className="flex items-center space-x-1">
														<Checkbox
															id={`required-${attachment.title}`}
															checked={attachment.required}
															onCheckedChange={(checked) =>
																toggleAttachmentRequired(
																	attachment.title,
																	checked as boolean
																)
															}
														/>
														<Label
															htmlFor={`required-${attachment.title}`}
															className="text-xs text-gray-600 whitespace-nowrap"
														>
															Required
														</Label>
													</div>
												)}
												<Button
													type="button"
													variant="ghost"
													size="sm"
													onClick={() => removeAttachment(attachment.title)}
													className="h-8 w-8 p-0"
												>
													<X className="h-4 w-4" />
												</Button>
											</div>
										</div>
									))}
								</div>
							)}
						</div>
					</div>
				</div>
			</div>

			{/* Component Editor Modal */}
			<ComponentManagerDialog
				open={ComponentManagerModalOpen}
				onClose={() => setComponentManagerModalOpen(false)}
				components={company.components}
				onUpdate={handleComponentsUpdate}
				componentManager={componentManager}
				company={company}
			/>
		</div>
	);
}
