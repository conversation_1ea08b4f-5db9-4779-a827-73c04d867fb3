"use client";

import { Input } from "@/components/ui/input";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

export const AuthorizationSchema = z.object({
	// Cost Estimates
	authorization_type: z.enum(["SPECIFIC", "GENERAL"]),
	approved_hours: z
		.number({
			required_error: "Approved hours is required",
			invalid_type_error: "Please enter a valid number"
		})
		.min(0.1, "Approved hours must be at least 0.1")
		.max(1000, "Approved hours cannot exceed 1000")
});

type AuthorizationStepData = z.infer<typeof AuthorizationSchema>;

interface AuthorizationStepProps {
	form: UseFormReturn<AuthorizationStepData>;
	company: ExtendedCompany;
	request?: ExtendedWarrantyRequest;
}

export function AuthorizationStep({
	form,
	company,
	request
}: AuthorizationStepProps) {
	const { register, formState, setValue, watch } = form;
	const estimateType = watch("authorization_type");

	useEffect(() => {
		if (estimateType === "GENERAL") {
			setValue("authorization_type", "GENERAL");
			setValue("approved_hours", 1);
		}
	}, [estimateType, setValue]);

	useEffect(() => {
		if (request) {
			setValue("authorization_type", request.authorization_type);
			setValue("approved_hours", request.approved_hours);
		}
	}, [request, setValue]);

	return (
		<div className="space-y-6">
			{/* Authorization Type */}
			<div className="space-y-2">
				<div className="flex items-center gap-1 mb-4">
					<span className="font-semibold">Authorization Type</span>
					<span className="text-red-500">*</span>
				</div>

				{/* General Service Authorization Option */}
				<div
					className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${estimateType === "GENERAL"
							? "border-blue-500 bg-blue-50"
							: "border-gray-200 hover:border-gray-300"
						}`}
					onClick={() => {
						setValue("authorization_type", "GENERAL");
						setValue("approved_hours", 3);
					}}
				>
					<div className="flex items-start gap-3">
						<div className="mt-1">
							<input
								type="radio"
								{...register("authorization_type")}
								value="GENERAL"
								className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
							/>
						</div>
						<div className="flex-1">
							<div className="font-medium text-gray-900 mb-1">
								General Service Authorization
							</div>
							<div className="text-sm text-gray-600">
								Issue needs diagnosis - authorize baseline hours to resolve
							</div>
						</div>
					</div>
				</div>

				<div className="space-y-4">
					{/* Specific Hour Estimate Option */}
					<div
						className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${estimateType === "SPECIFIC"
								? "border-blue-500 bg-blue-50"
								: "border-gray-200 hover:border-gray-300"
							}`}
						onClick={() => {
							setValue("authorization_type", "SPECIFIC");
							setValue("approved_hours", undefined);
						}}
					>
						<div className="flex items-start gap-3">
							<div className="mt-1">
								<input
									type="radio"
									{...register("authorization_type")}
									value="SPECIFIC"
									className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
								/>
							</div>
							<div className="flex-1">
								<div className="font-medium text-gray-900 mb-1">
									Specific Hour Estimate
								</div>
								<div className="text-sm text-gray-600">
									I know approximately how long this job should take
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Conditional Content Based on Authorization Type */}
			{estimateType === "SPECIFIC" && (
				<div>
					<div className="flex items-center gap-1 mb-4">
						<span className="font-semibold">Approved Hours</span>
						<span className="text-red-500">*</span>
					</div>
					<div className="text-sm text-gray-600 mb-3">
						Expected job completion time that will be shared with the assigned
						technician
					</div>
					<div className="max-w-sm">
						<Input
							type="number"
							step="0.1"
							{...register("approved_hours", { valueAsNumber: true })}
							error={String(formState.errors.approved_hours?.message || "")}
							placeholder="e.g. 2.5"
						/>
					</div>

					{/* Reminder for Specific Hours */}
					<div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
						<div className="flex items-start gap-2">
							<div className="text-blue-500 mt-0.5">💡</div>
							<div className="text-sm text-blue-900">
								<span className="font-medium">Reminder:</span> Once submitted,
								Keystone agrees to pay the service fee plus these estimated
								hours. This estimate will be visible to the technician who
								accepts the job. Provide realistic estimates to ensure smooth
								job completion and avoid rework approvals.
							</div>
						</div>
					</div>
				</div>
			)}

			{estimateType === "GENERAL" && (
				<div className="space-y-6">
					{/* Baseline Authorization Info */}
					<div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
						<div className="font-medium text-blue-600 mb-2">
							Baseline Authorization: Service fee + up to 1 hours
						</div>
						<div className="space-y-2">
							<div className="text-sm">
								<span className="font-medium">Technician Instructions:</span>{" "}
								'Diagnose and resolve the customer's issue. If no parts need to
								be ordered and the issue can be resolved within the baseline
								hours, proceed to complete the job. Contact Keystone for
								additional authorization if parts or extended time are needed.'
							</div>
						</div>
					</div>

					{/* Reminder for General Service */}
					<div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
						<div className="flex items-start gap-2">
							<div className="text-blue-500 mt-0.5">💡</div>
							<div className="text-sm text-blue-900">
								<span className="font-medium">Reminder:</span> General
								authorization gives technicians flexibility to diagnose and
								resolve customer issues efficiently. Keystone agrees to pay the
								service fee plus baseline hours, with additional authorization
								available if needed.
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
