"use client";

import { Dialog, DialogContent } from "@/components/ui/dialog";
import { ExtendedCompany } from "@/types/warranty";
import WarrantyRequestWizard from "./warranty-request-wizard";

interface WarrantyRequestWizardDialogProps {
	open: boolean;
	onClose: () => void;
	company: ExtendedCompany;
	initialRequest?: any;
	onSuccess?: (updatedRequest?: any) => void;
}

export default function WarrantyRequestWizardDialog({
	open,
	onClose,
	company,
	initialRequest,
	onSuccess
}: WarrantyRequestWizardDialogProps) {
	const handleSuccess = (updatedRequest?: any) => {
		onClose();
		if (onSuccess) {
			onSuccess(updatedRequest);
		}
	};

	const handleCancel = () => {
		onClose();
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-visible">
				<div className="h-full overflow-y-auto">
					<WarrantyRequestWizard
						company={company}
						initialRequest={initialRequest}
						onCancel={handleCancel}
						onSuccess={handleSuccess}
					/>
				</div>
			</DialogContent>
		</Dialog>
	);
}
