"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	ExtendedCompany,
	ExtendedComponent,
	WarrantyAttachment
} from "@/types/warranty";
import { Loader2, Paperclip, X } from "lucide-react";
import { useRef, useState } from "react";
import { toast } from "react-hot-toast";

interface ComponentManagerModalProps {
	open: boolean;
	onClose: () => void;
	components: ExtendedComponent[];
	onUpdate?: (
		components: ExtendedComponent[]
	) => Promise<void | ExtendedComponent[]>;
	company?: ExtendedCompany;
	componentManager?: {
		handleComponentsUpdate: (
			components: ExtendedComponent[]
		) => Promise<ExtendedComponent[]>;
		deleteComponent: (componentId: string) => Promise<void>;
		isUpdating: boolean;
	};
}

interface ComponentFormData {
	id?: string;
	type: string;
	manufacturer: string;
	notes: string;
	attachments: WarrantyAttachment[];
}

export function ComponentManagerDialog({
	open,
	onClose,
	components,
	onUpdate,
	company,
	componentManager
}: ComponentManagerModalProps) {
	const [loading, setLoading] = useState(false);

	// Use hook's loading state if available, otherwise use local state
	const isLoading = componentManager?.isUpdating || loading;
	const [editingComponent, setEditingComponent] =
		useState<ComponentFormData | null>(null);
	const [showForm, setShowForm] = useState(false);
	const [isUploading, setIsUploading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const handleAddComponent = () => {
		setEditingComponent({
			type: "",
			manufacturer: "",
			notes: "",
			attachments: []
		});
		setShowForm(true);
	};

	const handleEditComponent = (component: ExtendedComponent) => {
		setEditingComponent({
			id: component.id,
			type: component.type,
			manufacturer: component.manufacturer,
			notes: component.notes || "",
			attachments: component.attachments || []
		});
		setShowForm(true);
	};

	const handleDeleteComponent = async (componentId: string) => {
		if (!confirm("Are you sure you want to delete this component?")) return;

		// Only set local loading if not using the hook (hook manages its own loading state)
		if (!componentManager) {
			setLoading(true);
		}
		try {
			// Use the new component manager hook if available, otherwise fall back to the old method
			if (componentManager) {
				await componentManager.deleteComponent(componentId);
			} else if (onUpdate) {
				const updatedComponents = components.filter(
					(c) => c.id !== componentId
				);
				await onUpdate(updatedComponents);
			}
		} catch (error) {
			console.error("Failed to delete component:", error);
		} finally {
			if (!componentManager) {
				setLoading(false);
			}
		}
	};

	const handleSaveComponent = async () => {
		if (!editingComponent) return;

		// Only set local loading if not using the hook (hook manages its own loading state)
		if (!componentManager) {
			setLoading(true);
		}
		try {
			let changedComponents: ExtendedComponent[] = [];
			if (!editingComponent.id) {
				// New component
				const newComponent: ExtendedComponent = {
					id: undefined,
					company_id: "", // Will be set by API
					type: editingComponent.type,
					manufacturer: editingComponent.manufacturer,
					notes: editingComponent.notes,
					attachments: editingComponent.attachments as any // Type assertion for API compatibility
				};
				changedComponents = [newComponent];
			} else {
				// Existing component: compare to original
				const original = components.find((c) => c.id === editingComponent.id);
				if (original) {
					// Compare relevant fields
					const fieldsToCompare = [
						"type",
						"manufacturer",
						"notes",
						"attachments"
					];
					const isChanged = fieldsToCompare.some(
						(field) =>
							JSON.stringify(original[field]) !==
							JSON.stringify(editingComponent[field])
					);
					if (isChanged) {
						changedComponents = [
							{
								...original,
								type: editingComponent.type,
								manufacturer: editingComponent.manufacturer,
								notes: editingComponent.notes,
								attachments: editingComponent.attachments as any // Type assertion for API compatibility
							}
						];
					}
				}
			}
			if (changedComponents.length > 0) {
				// Use the new component manager hook if available, otherwise fall back to the old onUpdate
				if (componentManager) {
					await componentManager.handleComponentsUpdate(changedComponents);
					// Also call onUpdate to ensure parent components are notified
					// if (onUpdate) {
					// 	await onUpdate(updatedComponents);
					// }
				} else if (onUpdate) {
					await onUpdate(changedComponents);
				}
			}
			setShowForm(false);
			setEditingComponent(null);
		} catch (error) {
			console.error("Failed to save component:", error);
		} finally {
			if (!componentManager) {
				setLoading(false);
			}
		}
	};

	const handleUpdateAttachment = (
		index: number,
		field: keyof WarrantyAttachment,
		value: string | boolean
	) => {
		if (!editingComponent) return;

		const updatedAttachments = editingComponent.attachments.map(
			(attachment, i) =>
				i === index ? { ...attachment, [field]: value } : attachment
		);

		setEditingComponent({
			...editingComponent,
			attachments: updatedAttachments
		});
	};

	const handleRemoveAttachment = (index: number) => {
		if (!editingComponent) return;

		const updatedAttachments = editingComponent.attachments.filter(
			(_, i) => i !== index
		);

		setEditingComponent({
			...editingComponent,
			attachments: updatedAttachments
		});
	};

	// New file upload handler
	const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		if (!editingComponent) return;
		const files = e.target.files;
		if (!files || files.length === 0) return;
		setIsUploading(true);
		try {
			for (let i = 0; i < files.length; i++) {
				const file = files[i];
				const fName = file.name;
				const newFilename = file.name.toLowerCase().replace(/\s+/g, "_");
				let title = file.name.split(".").shift() || "";
				title = title.replace(/_/g, " ");
				// capitalize first letter of each work
				title = title
					.split(" ")
					.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
					.join(" ");

				// Determine attachment type based on file extension
				const extension = fName.split(".").pop()?.toLowerCase();
				let type = "document";
				if (["jpg", "jpeg", "png", "gif", "webp"].includes(extension || "")) {
					type = "image";
				} else if (["pdf"].includes(extension || "")) {
					type = "form";
				} else if (["doc", "docx"].includes(extension || "")) {
					type = "document";
				}
				const formData = new FormData();
				formData.append("file", file);
				formData.append("fileName", newFilename);
				formData.append(
					"path",
					`/${company.id}/components/${editingComponent.id || "new"}`
				);
				const response = await fetch("/api/storage", {
					method: "POST",
					body: formData
				});
				if (!response.ok) {
					throw new Error("Failed to upload file");
				}
				const data = await response.json();
				const newAttachment: WarrantyAttachment = {
					type,
					title: title,
					url: data.url,
					required: false,
					component_id: editingComponent.id
				};
				setEditingComponent((prev) =>
					prev
						? {
								...prev,
								attachments: [...prev.attachments, newAttachment]
							}
						: null
				);
			}
		} catch (error) {
			console.error("Error uploading file:", error);
			toast.error("Failed to upload file");
		} finally {
			setIsUploading(false);
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}
		}
	};

	if (!open) return null;

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl max-h-[85vh] flex flex-col">
				{/* Header */}
				<div className="p-6 flex-shrink-0">
					<div className="flex items-center justify-between">
						<div>
							<h2 className="text-xl font-bold">Component Manager</h2>
							<p className="text-sm opacity-90 mt-1">
								Manage company components and attachments
							</p>
						</div>
						{!showForm && (
							<Button
								onClick={handleAddComponent}
								style={{
									backgroundColor: company?.brand_color || "#2563eb",
									borderColor: company?.brand_color || "#2563eb"
								}}
								className="hover:bg-gray-100"
							>
								Add Component
							</Button>
						)}
					</div>
				</div>

				{/* Content */}
				<div className="flex-1 min-h-0">
					{showForm ? (
						/* Component Form */
						<div className="h-full flex flex-col">
							<div className="flex-1 overflow-y-auto p-6 space-y-6">
								<div className="flex items-center justify-between">
									<h3 className="text-lg font-semibold">
										{editingComponent?.id
											? "Edit Component"
											: "Add New Component"}
									</h3>
									<Button
										variant="outline"
										className="px-4 py-2 whitespace-nowrap"
										style={{
											color: company.brand_color,
											borderColor: company.brand_color
										}}
										onClick={() => {
											setShowForm(false);
											setEditingComponent(null);
										}}
									>
										Back to List
									</Button>
								</div>

								{/* Component Details */}
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div>
										<Input
											label="Component Type"
											value={editingComponent?.type || ""}
											onChange={(e) =>
												setEditingComponent((prev) =>
													prev ? { ...prev, type: e.target.value } : null
												)
											}
											placeholder="e.g., Air Conditioner, Refrigerator"
											required
											disabled={!!editingComponent?.id}
										/>
									</div>
									<div>
										<Input
											label="Manufacturer"
											value={editingComponent?.manufacturer || ""}
											onChange={(e) =>
												setEditingComponent((prev) =>
													prev
														? { ...prev, manufacturer: e.target.value }
														: null
												)
											}
											placeholder="e.g., Dometic, Coleman"
											required
											disabled={!!editingComponent?.id}
										/>
									</div>
								</div>

								<div>
									<Textarea
										label="Notes"
										value={editingComponent?.notes || ""}
										onChange={(e) =>
											setEditingComponent((prev) =>
												prev ? { ...prev, notes: e.target.value } : null
											)
										}
										placeholder="Additional notes about this component..."
										rows={3}
									/>
								</div>

								{/* Attachments Section */}
								<div>
									<div className="flex items-center justify-between mb-4">
										<h4 className="text-md font-semibold">Attachments</h4>
										<>
											<input
												type="file"
												ref={fileInputRef}
												onChange={handleFileChange}
												multiple
												className="hidden"
												accept="image/*,.pdf,.doc,.docx"
												aria-label="Upload attachment files"
											/>
											<Button
												type="button"
												variant="outline"
												size="sm"
												className="px-4 py-2 whitespace-nowrap"
												style={{
													color: company.brand_color,
													borderColor: company.brand_color
												}}
												onClick={() => fileInputRef.current?.click()}
												disabled={isUploading}
											>
												{isUploading ? (
													<>
														<Loader2 className="mr-2 h-4 w-4 animate-spin" />
														Uploading...
													</>
												) : (
													<>
														<Paperclip className="mr-2 h-4 w-4" />
														Attach Files and Photos
													</>
												)}
											</Button>
										</>
									</div>
									{/* Remove Drop Zone */}
									{editingComponent?.attachments.map((attachment, index) => (
										<div
											key={index}
											className="border rounded-lg p-4 mb-3 bg-gray-50"
										>
											{/* Row 1: Title, URL */}
											<div className="flex flex-col md:flex-row gap-3 mb-3">
												<div className="flex-1 min-w-0">
													<label className="block text-sm font-medium text-gray-700 mb-1">
														Title
													</label>
													<Input
														value={attachment.title}
														onChange={(e) =>
															handleUpdateAttachment(
																index,
																"title",
																e.target.value
															)
														}
														placeholder="Attachment title"
													/>
												</div>
												<div>
													<label className="block text-sm font-medium text-gray-700 mb-1">
														Type
													</label>
													<select
														value={attachment.type}
														onChange={(e) =>
															handleUpdateAttachment(
																index,
																"type",
																e.target.value
															)
														}
														className="text-sm h-8 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary max-w-[120px] w-28"
														aria-label="Attachment type"
													>
														<option value="form">Form</option>
														<option value="document">Document</option>
														<option value="image">Image</option>
														<option value="manual">Manual</option>
														<option value="specification">Specification</option>
													</select>
												</div>
											</div>
											{/* Row 2: Type, Required, Remove */}
											<div className="flex flex-col md:flex-row md:items-end gap-3">
												<div className="flex flex-row items-end gap-3">
													<div className="flex items-center ml-3 mt-5 md:mt-0">
														<input
															id={`attachment-${index}-required`}
															type="checkbox"
															checked={attachment.required || false}
															onChange={(e) =>
																handleUpdateAttachment(
																	index,
																	"required",
																	e.target.checked
																)
															}
															className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
														/>
														<label
															htmlFor={`attachment-${index}-required`}
															className="ml-2 block text-sm text-gray-700"
														>
															Required
														</label>
													</div>
													{/* filename */}
													<span className="pl-4 text-sm text-gray-700">
														{attachment.url
															? attachment.url.split("/").pop()
															: "No file"}
													</span>
												</div>
												<div className="flex items-end md:ml-auto">
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleRemoveAttachment(index)}
														className="text-red-600 hover:text-red-700"
													>
														<X className="h-4 w-4" />
													</Button>
												</div>
											</div>
										</div>
									))}
									{editingComponent?.attachments.length === 0 && (
										<div className="text-center py-8 text-gray-500">
											{
												"No attachments added yet. Click 'Attach Files and Photos' to get started."
											}
										</div>
									)}
								</div>
							</div>

							{/* Form Footer */}
							<DialogFooter className="bg-white px-6 py-4 border-t flex-shrink-0">
								<div className="flex justify-end gap-3">
									<Button
										variant="outline"
										className="px-4 py-2 whitespace-nowrap"
										style={{
											color: company.brand_color,
											borderColor: company.brand_color
										}}
										onClick={() => {
											setShowForm(false);
											setEditingComponent(null);
										}}
										disabled={isLoading}
									>
										Cancel
									</Button>
									<Button
										onClick={handleSaveComponent}
										disabled={
											loading ||
											!editingComponent?.type ||
											!editingComponent?.manufacturer
										}
										style={{
											backgroundColor: company?.brand_color ?? "#2563eb",
											color: "#fff"
										}}
									>
										{isLoading ? (
											<div className="flex items-center gap-2">
												<div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
												Saving...
											</div>
										) : editingComponent?.id ? (
											"Update Component"
										) : (
											"Add Component"
										)}
									</Button>
								</div>
							</DialogFooter>
						</div>
					) : (
						/* Components List */
						<div className="h-full overflow-y-auto p-6">
							{components.length === 0 ? (
								<div className="text-center py-12">
									<div className="text-gray-400 mb-4">
										<svg
											className="w-16 h-16 mx-auto"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={1}
												d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
											/>
										</svg>
									</div>
									<h3 className="text-lg font-medium text-gray-900 mb-2">
										No Components Yet
									</h3>
									<p className="text-gray-500 mb-4">
										Get started by adding your first component.
									</p>
									<Button
										onClick={handleAddComponent}
										style={{
											backgroundColor: company?.brand_color || "#2563eb",
											color: "#fff"
										}}
									>
										Add Your First Component
									</Button>
								</div>
							) : (
								<div className="space-y-4">
									{[...components]
										.sort((a, b) => a.type.localeCompare(b.type))
										.map((component) => (
											<div
												key={component.id}
												className="border rounded-lg p-4 bg-white shadow-sm"
											>
												<div className="flex items-start justify-between">
													<div className="flex-1">
														<div className="flex items-center gap-3 mb-2">
															<h3 className="text-lg font-semibold text-gray-900">
																{component.type}
															</h3>
															<span className="px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded">
																{component.manufacturer}
															</span>
														</div>

														{component.notes && (
															<p className="text-gray-600 text-sm mb-3">
																{component.notes}
															</p>
														)}

														{component.attachments &&
															component.attachments.length > 0 && (
																<div className="mb-3">
																	<p className="text-sm font-medium text-gray-700 mb-2">
																		Attachments ({component.attachments.length})
																	</p>
																	<div className="flex flex-wrap gap-2">
																		{component.attachments.map(
																			(attachment, index) => (
																				<span
																					key={index}
																					className={`inline-flex items-center px-2 py-1 text-xs rounded ${attachment.required ? "bg-red-200 text-red-800" : "bg-blue-100 text-blue-800"}`}
																				>
																					{attachment.type}:{" "}
																					{attachment.title || "Untitled"}
																				</span>
																			)
																		)}
																	</div>
																</div>
															)}
													</div>

													<div className="flex gap-2 ml-4">
														<Button
															variant="outline"
															size="sm"
															onClick={() => handleEditComponent(component)}
															style={{
																color: company?.brand_color || "#2563eb",
																borderColor: company?.brand_color || "#2563eb"
															}}
														>
															Edit
														</Button>
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																handleDeleteComponent(component.id)
															}
															className="text-red-600 hover:text-red-700"
															disabled={isLoading}
														>
															Delete
														</Button>
													</div>
												</div>
											</div>
										))}
								</div>
							)}
						</div>
					)}

					{/* Footer for components list */}
					{!showForm && (
						<DialogFooter className="bg-white px-6 py-4 border-t flex-shrink-0">
							<div className="flex justify-end">
								<Button
									variant="outline"
									onClick={onClose}
									style={{
										color: company?.brand_color || "#2563eb",
										borderColor: company?.brand_color || "#2563eb"
									}}
								>
									Close
								</Button>
							</div>
						</DialogFooter>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
