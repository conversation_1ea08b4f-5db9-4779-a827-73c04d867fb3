"use client";

import { ExtendedCompany, ExtendedComponent } from "@/types/warranty";
import { useCallback, useState } from "react";

interface UseComponentManagerProps {
	company: ExtendedCompany;
	onComponentsChange?: (updatedComponents: ExtendedComponent[]) => void;
	onComponentCreated?: (component: ExtendedComponent) => void;
}

interface UseComponentManagerReturn {
	// State
	isUpdating: boolean;

	// Actions
	createComponents: (
		components: ExtendedComponent[]
	) => Promise<ExtendedComponent[]>;
	updateComponents: (
		components: ExtendedComponent[]
	) => Promise<ExtendedComponent[]>;
	handleComponentsUpdate: (
		components: ExtendedComponent[]
	) => Promise<ExtendedComponent[]>;
	deleteComponent: (componentId: string) => Promise<void>;
}

export function useComponentManager({
	company,
	onComponentsChange,
	onComponentCreated
}: UseComponentManagerProps): UseComponentManagerReturn {
	const [isUpdating, setIsUpdating] = useState(false);

	const createComponents = useCallback(
		async (components: ExtendedComponent[]): Promise<ExtendedComponent[]> => {
			const response = await fetch(`/api/companies/${company.id}/components`, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(
					components.length === 1 ? components[0] : components
				)
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to create components");
			}

			const data = await response.json();
			const created = Array.isArray(data.results)
				? data.results
						.filter((r: any) => r.success)
						.map((r: any) => r.component)
				: data.success
					? [data.component]
					: [];

			return created;
		},
		[company.id]
	);

	const updateComponents = useCallback(
		async (components: ExtendedComponent[]): Promise<ExtendedComponent[]> => {
			const response = await fetch(`/api/companies/${company.id}/components`, {
				method: "PUT",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(
					components.length === 1 ? components[0] : components
				)
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to update components");
			}

			const data = await response.json();
			const updated = Array.isArray(data.results)
				? data.results.filter((r: any) => r.success).map((r: any) => r.updated)
				: data.success
					? [data.updated]
					: [];

			return updated;
		},
		[company.id]
	);

	const deleteComponent = useCallback(
		async (componentId: string): Promise<void> => {
			const response = await fetch(
				`/api/companies/${company.id}/components/${componentId}`,
				{
					method: "DELETE",
					headers: { "Content-Type": "application/json" }
				}
			);

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to delete component");
			}
		},
		[company.id]
	);

	const handleComponentsUpdate = useCallback(
		async (components: ExtendedComponent[]): Promise<ExtendedComponent[]> => {
			setIsUpdating(true);

			try {
				const newComponents = components.filter((c) => !c.id);
				const existingComponents = components.filter((c) => c.id);

				const requests: Promise<ExtendedComponent[]>[] = [];
				const allUpdated: ExtendedComponent[] = [];

				// Handle new components
				if (newComponents.length > 0) {
					requests.push(createComponents(newComponents));
				}

				// Handle existing components
				if (existingComponents.length > 0) {
					requests.push(updateComponents(existingComponents));
				}

				const results = await Promise.all(requests);

				// Flatten results
				results.forEach((result) => {
					allUpdated.push(...result);
				});

				// Update local company components state
				let updatedCompanyComponents = [...company.components];

				// Handle created components
				const createdComponents = newComponents.length > 0 ? results[0] : [];
				if (createdComponents.length > 0) {
					// Remove temporary components and add created ones
					updatedCompanyComponents = [
						...updatedCompanyComponents.filter(
							(c) =>
								!newComponents.some(
									(nc) =>
										!nc.id &&
										c.type === nc.type &&
										c.manufacturer === nc.manufacturer
								)
						),
						...createdComponents
					];

					// Notify about component creation
					if (createdComponents.length === 1 && onComponentCreated) {
						onComponentCreated(createdComponents[0]);
					}
				}

				// Handle updated components
				const updatedComponents =
					existingComponents.length > 0
						? results[newComponents.length > 0 ? 1 : 0]
						: [];

				if (updatedComponents.length > 0) {
					updatedComponents.forEach((upd) => {
						const idx = updatedCompanyComponents.findIndex(
							(c) => c.id === upd.id
						);
						if (idx !== -1) {
							updatedCompanyComponents[idx] = upd;
						}
					});
				}

				// Notify about components change
				if (onComponentsChange) {
					onComponentsChange(updatedCompanyComponents);
				}

				return allUpdated;
			} catch (error) {
				console.error("Failed to update components:", error);
				throw error;
			} finally {
				setIsUpdating(false);
			}
		},
		[
			company.components,
			company.id,
			createComponents,
			updateComponents,
			onComponentsChange,
			onComponentCreated
		]
	);

	return {
		// State
		isUpdating,

		// Actions
		createComponents,
		updateComponents,
		handleComponentsUpdate,
		deleteComponent
	};
}
