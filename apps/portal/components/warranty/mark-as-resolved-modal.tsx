"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	Di<PERSON>Header,
	DialogTitle
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface MarkAsResolvedModalProps {
	open: boolean;
	onClose: () => void;
	request: ExtendedWarrantyRequest;
	company: ExtendedCompany;
	onStatusUpdated?: () => void;
}

export function MarkAsResolvedModal({
	open,
	onClose,
	request,
	company,
	onStatusUpdated
}: MarkAsResolvedModalProps) {
	const [reason, setReason] = useState("");
	const [isLoading, setIsLoading] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!reason.trim()) {
			toast.error("Please provide a reason for resolving this request");
			return;
		}

		setIsLoading(true);

		try {
			const response = await fetch(
				`/api/warranty-requests/${request.id}/cancel-job`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						update_notes: reason.trim()
					})
				}
			);

			const data = await response.json();

			if (response.ok) {
				toast.success(
					data.message || "Warranty request marked as resolved successfully"
				);
				setReason("");
				if (onStatusUpdated) {
					onStatusUpdated();
				}
				onClose();
			} else {
				toast.error(
					data.error || "Failed to mark warranty request as resolved"
				);
			}
		} catch (error) {
			console.error("Error marking warranty request as resolved:", error);
			toast.error("Failed to mark warranty request as resolved");
		} finally {
			setIsLoading(false);
		}
	};

	const handleClose = () => {
		setReason("");
		onClose();
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle>Mark as Resolved</DialogTitle>
					<DialogDescription>
						Please provide a reason for resolving this warranty request. This
						information will be recorded in the timeline.
					</DialogDescription>
				</DialogHeader>

				<div className="bg-amber-50 border border-amber-200 rounded-md p-3 mb-4">
					<p className="text-sm text-amber-800 font-medium">
						⚠️ Note: The reason for resolution will be visible to both the
						customer and any associated technicians.
					</p>
				</div>

				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="space-y-2">
						<label htmlFor="reason" className="text-sm font-medium">
							Reason for Resolution
						</label>
						<Textarea
							id="reason"
							value={reason}
							onChange={(e) => setReason(e.target.value)}
							placeholder="Please provide a detailed reason for resolving this warranty request..."
							className="min-h-[100px]"
							required
						/>
					</div>

					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={handleClose}
							disabled={isLoading}
							style={{
								color: company.brand_color,
								borderColor: company.brand_color
							}}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							disabled={isLoading || !reason.trim()}
							style={{
								backgroundColor: company.brand_color,
								color: "#fff"
							}}
						>
							{isLoading ? (
								<div className="flex items-center gap-2">
									<div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
									Marking as Resolved...
								</div>
							) : (
								"Mark as Resolved"
							)}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
