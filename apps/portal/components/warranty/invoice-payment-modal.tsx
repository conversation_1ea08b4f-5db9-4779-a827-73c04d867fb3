"use client";

import { InvoicePaymentComponent } from "@/components/payments/InvoicePayment";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { Listing } from "@rvhelp/database";
import { useCallback, useEffect, useState } from "react";

interface InvoicePaymentModalProps {
	open: boolean;
	onClose: () => void;
	request: ExtendedWarrantyRequest;
	onStatusUpdated?: () => void;
	company?: any;
}

interface InvoiceData {
	id: string;
	invoice_number: number;
	customer_name: string;
	customer_email: string;
	customer_phone?: string;
	amount: number;
	due_date?: Date;
	description?: string;
	items: Array<{
		description: string;
		quantity: number;
		unit_price: number;
		amount: number;
	}>;
	provider_id: string;
}

type ListingWithOwner = Listing & {
	owner: {
		first_name: string;
		last_name: string;
		email: string;
		phone: string;
	};
};

export function InvoicePaymentModal({
	open,
	onClose,
	request,
	onStatusUpdated,
	company: _company
}: InvoicePaymentModalProps) {
	const [invoice, setInvoice] = useState<InvoiceData | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [listing, setListing] = useState<Listing | null>(null);
	const [paymentSuccess, setPaymentSuccess] = useState(false);

	const fetchInvoice = useCallback(async () => {
		setLoading(true);
		setError(null);
		try {
			const response = await fetch(
				`/api/warranty-requests/${request.id}/invoice`
			);
			if (!response.ok) {
				throw new Error("Failed to fetch invoice");
			}
			const data = await response.json();

			// Transform the invoice data to match the expected structure
			const transformedInvoice: InvoiceData = {
				id: data.invoice.id,
				invoice_number: data.invoice.invoice_number,
				customer_name: data.invoice.customer_name,
				customer_email: data.invoice.customer_email,
				customer_phone: data.invoice.customer_phone,
				amount: data.invoice.amount,
				due_date: data.invoice.due_date
					? new Date(data.invoice.due_date)
					: undefined,
				description: data.invoice.description,
				items: data.invoice.items.map((item: any) => ({
					description: item.description,
					quantity: item.quantity,
					unit_price: item.unit_price,
					amount: item.amount
				})),
				provider_id: data.invoice.provider_id
			};

			setInvoice(transformedInvoice);

			setListing(data.invoice.provider);
		} catch (err) {
			setError(err instanceof Error ? err.message : "Failed to fetch invoice");
		} finally {
			setLoading(false);
		}
	}, [request.id]);

	useEffect(() => {
		if (open && request.id) {
			fetchInvoice();
			setPaymentSuccess(false);
		}
	}, [open, request.id, fetchInvoice]);

	const handleClose = () => {
		// If payment was successful, call onStatusUpdated before closing
		if (paymentSuccess && onStatusUpdated) {
			onStatusUpdated();
		}
		setInvoice(null);
		setError(null);
		setPaymentSuccess(false);
		onClose();
	};

	const handlePaymentSuccess = () => {
		setPaymentSuccess(true);
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent>
				{loading && (
					<div className="p-6 text-center">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
						<p className="mt-2 text-sm text-gray-500">Loading invoice...</p>
					</div>
				)}

				{error && (
					<div className="p-6">
						<div className="rounded-md bg-red-50 p-4">
							<div className="flex">
								<div className="flex-shrink-0">
									<svg
										className="h-5 w-5 text-red-400"
										viewBox="0 0 20 20"
										fill="currentColor"
									>
										<path
											fillRule="evenodd"
											d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
											clipRule="evenodd"
										/>
									</svg>
								</div>
								<div className="ml-3">
									<h3 className="text-sm font-medium text-red-800">
										Error Loading Invoice
									</h3>
									<div className="mt-2 text-sm text-red-700">
										<p>{error}</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				)}

				{invoice && !loading && !error && (
					<InvoicePaymentComponent
						invoice={invoice}
						listing={listing as ListingWithOwner}
					/>
				)}
			</DialogContent>
		</Dialog>
	);
}
