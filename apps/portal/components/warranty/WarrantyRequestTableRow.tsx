"use client";

import { TableCell, TableRow } from "@/components/ui/table";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { User } from "@rvhelp/database";
import WarrantyRequestActions from "./WarrantyRequestActions";
import {
	formatDate,
	getStatusColor,
	getWarrantyRequestState
} from "./warranty-utils";

interface WarrantyRequestTableRowProps {
	request: ExtendedWarrantyRequest;
	company?: any;
	index: number;
	onView: (request: ExtendedWarrantyRequest) => void;
	onUpdateStatus: (request: ExtendedWarrantyRequest) => void;
	onPayInvoice: (request: ExtendedWarrantyRequest) => void;
	onGeneratePlatformInvoice?: (request: ExtendedWarrantyRequest) => void;
	onDelete?: (requestId: string) => void;
	onResendEmail?: (request: ExtendedWarrantyRequest) => void;
	user?: User;
	isDeleting?: boolean;
}

export default function WarrantyRequestTableRow({
	request,
	company,
	index,
	onView,
	onUpdateStatus,
	onPayInvoice,
	onGeneratePlatformInvoice,
	onDelete,
	onResendEmail,
	user,
	isDeleting
}: WarrantyRequestTableRowProps) {
	return (
		<TableRow
			key={request.id}
			className={`h-16 transition-colors hover:bg-blue-50 ${index % 2 === 0 ? "bg-white" : "bg-gray-50/50"} cursor-pointer ${isDeleting ? 'opacity-50 pointer-events-none' : ''}`}
			onDoubleClick={() => onView(request)}
		>
			<TableCell className="font-mono text-sm font-medium text-gray-600 bg-gray-50/50 px-3">
				<div className="space-y-1 flex flex-col items-center">
					<span
						className={`px-2 py-0.5 inline-flex text-xs font-semibold rounded-full ${getStatusColor(request.status)}`}
					>
						{getWarrantyRequestState(request).state}
					</span>
					<span className="bg-white px-2 py-1 rounded border text-xs inline-block">
						{request.uuid?.slice(0, 8) || "N/A"}
					</span>
				</div>
			</TableCell>
			<TableCell className="px-4">
				<div className="space-y-1">
					<div className="font-semibold text-gray-900">
						{request.first_name} {request.last_name}
					</div>
					<div className="text-xs text-gray-600">{request.email}</div>
				</div>
			</TableCell>
			<TableCell className="px-4">
				<div className="max-w-xs">
					<div className="truncate font-medium text-gray-900 mb-1 text-sm">
						{request.complaint}
					</div>
					{request.component?.type && (
						<div className="text-xs text-gray-600 mb-1">
							{request.component.type} - {request.component.manufacturer}
						</div>
					)}
				</div>
			</TableCell>
			<TableCell className="px-4 min-w-[100px]">
				<div className="space-y-1">
					{request.rv_make && (
						<div className="text-sm text-gray-900 whitespace-nowrap">
							{request.rv_make}
						</div>
					)}
					{request.rv_model && (
						<div className="font-semibold text-gray-900 whitespace-nowrap">
							{request.rv_model}
						</div>
					)}
					{request.rv_year && (
						<div className="text-sm text-gray-600">{request.rv_year}</div>
					)}
				</div>
			</TableCell>
			<TableCell className="px-4">
				<div className="space-y-1">
					{request.approved_hours && (
						<span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs block w-fit">
							Authorized: {request.approved_hours}h
						</span>
					)}
					{request.estimated_hours && (
						<span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs block w-fit">
							Estimated: {request.estimated_hours}h
						</span>
					)}
					{request.actual_hours && (
						<span className="bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full text-xs block w-fit">
							Actual: {request.actual_hours}h
						</span>
					)}
				</div>
			</TableCell>
			<TableCell className="px-4">
				<div className="space-y-1">
					<div className="text-sm font-medium text-gray-700">
						{formatDate(request.created_at)}
					</div>
					<div className="text-xs text-gray-500">
						{request.oem_user?.first_name} {request.oem_user?.last_name}
					</div>
				</div>
			</TableCell>
			<TableCell>
				<WarrantyRequestActions
					request={request}
					company={company}
					onView={onView}
					onUpdateStatus={onUpdateStatus}
					onPayInvoice={onPayInvoice}
					onGeneratePlatformInvoice={onGeneratePlatformInvoice}
					onDelete={onDelete}
					onResendEmail={onResendEmail}
					user={user}
					isDeleting={isDeleting}
				/>
			</TableCell>
		</TableRow>
	);
}
