"use client";

import { TableCell, TableRow } from "@/components/ui/table";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger
} from "@/components/ui/tooltip";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { User } from "@prisma/client";
import { Eye, StickyNote } from "lucide-react";
import { useMemo, useState } from "react";
import {
	formatDate,
	getStatusColor,
	getWarrantyRequestState
} from "../warranty-utils";

import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

interface WarrantyRequestPendingRowProps {
	request: ExtendedWarrantyRequest;
	company?: any;
	index: number;
	onView: (request: ExtendedWarrantyRequest) => void;
	user?: User;
}

function darkenColor(color: string, amount: number) {
	// Simple darken: works for hex colors only
	if (!color.startsWith("#") || (color.length !== 7 && color.length !== 4))
		return color;
	let r, g, b;
	if (color.length === 7) {
		r = parseInt(color.slice(1, 3), 16);
		g = parseInt(color.slice(3, 5), 16);
		b = parseInt(color.slice(5, 7), 16);
	} else {
		r = parseInt(color[1] + color[1], 16);
		g = parseInt(color[2] + color[2], 16);
		b = parseInt(color[3] + color[3], 16);
	}
	r = Math.floor(r * amount);
	g = Math.floor(g * amount);
	b = Math.floor(b * amount);
	return `#${r.toString(16).padStart(2, "0")}${g.toString(16).padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
}

export function WarrantyRequestPendingRow({
	request,
	company,
	index,
	onView,
	user
}: WarrantyRequestPendingRowProps) {
	const [adminNotes, setAdminNotes] = useState<string>(
		request.admin_notes || ""
	);
	const [noteOpen, setNoteOpen] = useState(false);
	const [noteText, setNoteText] = useState<string>(request.admin_notes || "");
	const [saving, setSaving] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const invitationCount = useMemo(() => {
		const invitations = request.timeline_updates?.filter(
			(update) => update.event_type === "TECHNICIAN_INVITED"
		);
		const rejections = request.timeline_updates?.filter(
			(update) => update.event_type === "TECHNICIAN_REJECTED"
		);
		const acceptances = request.timeline_updates?.filter(
			(update) => update.event_type === "TECHNICIAN_ACCEPTED"
		);
		const outstanding =
			invitations?.length -
			(rejections?.length || 0) -
			(acceptances?.length || 0);
		return {
			invites: invitations?.length || 0,
			rejections: rejections?.length || 0,
			acceptances: acceptances?.length || 0,
			outstanding: outstanding
		};
	}, [request]);

	const lastUpdate = useMemo(() => {
		const updates = request.timeline_updates?.sort(
			(a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
		);
		const invitations = updates?.filter(
			(update) =>
				update.event_type === "CUSTOMER_REGISTERED" ||
				update.event_type === "TECHNICIAN_INVITED" ||
				update.event_type === "TECHNICIAN_ACCEPTED"
		);
		const lastActivity = invitations?.[invitations.length - 1];
		const diffTime = Math.abs(
			new Date().getTime() -
				new Date(lastActivity?.date || request.created_at).getTime()
		);
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
		if (!lastActivity) {
			return null;
		}
		(lastActivity as any).daysSince = diffDays;
		return lastActivity;
	}, [request]);

	return (
		<>
			<TableRow
				key={request.id}
				className={`h-16 transition-colors hover:bg-blue-50 ${index % 2 === 0 ? "bg-white" : "bg-gray-50/50"} cursor-pointer`}
				onDoubleClick={() => onView(request)}
			>
				<TableCell className="font-mono text-sm font-medium text-gray-600 bg-gray-50/50 px-3">
					<div className="space-y-1 flex flex-col items-center">
						<span
							className={`px-2 py-0.5 inline-flex text-xs font-semibold rounded-full ${getStatusColor(request.status)}`}
						>
							{getWarrantyRequestState(request).state}
						</span>
						<span className="bg-white px-2 py-1 rounded border text-xs inline-block">
							{request.uuid?.slice(0, 8) || "N/A"}
						</span>
					</div>
				</TableCell>
				<TableCell className="px-4">
					<div className="space-y-1">
						<div className="font-semibold text-gray-900">
							{request.first_name} {request.last_name}
						</div>
						<div className="text-xs text-gray-600">
							{request.email.toLowerCase()}
						</div>
					</div>
				</TableCell>
				<TableCell className="px-4">
					<div className="max-w-xs">
						<div className="truncate font-medium text-gray-900 mb-1 text-sm">
							{request.complaint}
						</div>
						{request.component?.type && (
							<div className="text-xs text-gray-600 mb-1">
								{request.component.type} - {request.component.manufacturer}
							</div>
						)}
						{user &&
							(user.role === "ADMIN" || user.role?.startsWith("OEM")) &&
							adminNotes && (
								<Tooltip>
									<TooltipTrigger asChild>
										<span className="inline-flex items-center gap-1 text-xs text-amber-700 bg-amber-50 border border-amber-200 rounded px-2 py-0.5">
											{/* <StickyNote className="h-3 w-3" /> */}
											{adminNotes}
										</span>
									</TooltipTrigger>
									<TooltipContent className="max-w-xs whitespace-pre-wrap">
										{adminNotes}
									</TooltipContent>
								</Tooltip>
							)}
					</div>
				</TableCell>
				<TableCell className="px-4 min-w-[100px]">
					<div className="space-y-1">
						<div className="text-xs text-gray-600">
							{invitationCount.invites} invites
						</div>
						<div className="text-xs text-gray-600">
							{invitationCount.rejections} rejections
						</div>
						<div className="text-xs text-gray-600">
							{invitationCount.acceptances} acceptances
						</div>
						<div className="text-xs text-gray-600">
							{invitationCount.outstanding} outstanding
						</div>
					</div>
				</TableCell>
				<TableCell className="px-4">
					<div className="space-y-1">
						{lastUpdate && (
							<>
								<div className="text-xs text-gray-500">
									{lastUpdate.event_type.replace("_", " ")}
								</div>
								<div className="text-sm font-medium text-gray-700">
									{formatDate(lastUpdate.date)}
								</div>
								<div className="text-xs text-gray-500">
									{(lastUpdate as any).daysSince
										? `${(lastUpdate as any).daysSince} days ago`
										: "N/A"}
								</div>
							</>
						)}
					</div>
				</TableCell>
				<TableCell>
					<div className="flex flex-row gap-2">
						<Tooltip>
							<TooltipTrigger asChild>
								<button
									type="button"
									onClick={() => onView(request)}
									aria-label="View request"
									style={{
										backgroundColor: company?.brand_color ?? "#2563eb",
										color: "#fff"
									}}
									className="w-6 h-6 rounded-full flex items-center justify-center transition-colors"
									onMouseEnter={(e) =>
										(e.currentTarget.style.backgroundColor = darkenColor(
											company?.brand_color ?? "#2563eb",
											0.9
										))
									}
									onMouseLeave={(e) =>
										(e.currentTarget.style.backgroundColor =
											company?.brand_color ?? "#2563eb")
									}
								>
									<Eye className="h-4 w-4 text-white" />
								</button>
							</TooltipTrigger>
							<TooltipContent>
								<p>View</p>
							</TooltipContent>
						</Tooltip>

						{user &&
							(user.role === "ADMIN" || user.role?.startsWith("OEM")) && (
								<Tooltip>
									<TooltipTrigger asChild>
										<button
											type="button"
											onClick={() => {
												setNoteText(adminNotes || "");
												setNoteOpen(true);
											}}
											aria-label={
												adminNotes ? "Edit admin note" : "Add admin note"
											}
											className="w-6 h-6 rounded-full flex items-center justify-center transition-colors bg-amber-500 hover:bg-amber-600"
										>
											<StickyNote className="h-4 w-4 text-white" />
										</button>
									</TooltipTrigger>
									<TooltipContent>
										<p>{adminNotes ? "Edit note" : "Add note"}</p>
									</TooltipContent>
								</Tooltip>
							)}
					</div>
				</TableCell>
			</TableRow>

			{user && (user.role === "ADMIN" || user.role?.startsWith("OEM")) && (
				<Dialog open={noteOpen} onOpenChange={setNoteOpen}>
					<DialogContent className="sm:max-w-md">
						<DialogHeader>
							<DialogTitle>
								{adminNotes ? "Edit Admin Note" : "Add Admin Note"}
							</DialogTitle>
						</DialogHeader>
						<div className="space-y-3">
							<Textarea
								value={noteText}
								onChange={(e) => setNoteText(e.target.value)}
								placeholder="Enter internal note (visible to admins only)"
								rows={4}
							/>
							{error && <p className="text-sm text-red-600">{error}</p>}
						</div>
						<DialogFooter>
							<Button
								variant="outline"
								onClick={() => setNoteOpen(false)}
								disabled={saving}
							>
								Cancel
							</Button>
							<Button
								onClick={async () => {
									try {
										setSaving(true);
										setError(null);
										const res = await fetch(
											`/api/warranty-requests/${request.id}`,
											{
												method: "PUT",
												headers: { "Content-Type": "application/json" },
												body: JSON.stringify({
													...request,
													admin_notes: noteText || null
												})
											}
										);
										if (!res.ok) {
											const data = await res.json().catch(() => ({}));
											throw new Error(data.error || "Failed to save note");
										}
										setAdminNotes(noteText || "");
										setNoteOpen(false);
									} catch (e: any) {
										setError(e.message || "Unknown error");
									} finally {
										setSaving(false);
									}
								}}
								disabled={saving}
								style={{ backgroundColor: company?.brand_color ?? "#2563eb" }}
								className="text-white"
							>
								Save
							</Button>
						</DialogFooter>
					</DialogContent>
				</Dialog>
			)}
		</>
	);
}
