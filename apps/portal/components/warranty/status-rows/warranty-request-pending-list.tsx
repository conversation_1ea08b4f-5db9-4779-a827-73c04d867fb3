"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { useCallback, useEffect, useState } from "react";
import WarrantyRequestCardDialog from "../warranty-request-card/warranty-request-card-dialog";
import { WarrantyRequestPendingRow } from "./warranty-request-pending-row";


type StatusKey = "new" | "pending" | "in-progress" | "done";

interface StatusWarrantyRequestListProps {
	company?: any;
	user: any;
	statusKey: StatusKey;
}

export default function StatusWarrantyRequestList({
	company,
	user,
	statusKey
}: StatusWarrantyRequestListProps) {
	const [requests, setRequests] = useState<ExtendedWarrantyRequest[]>([]);
	const [loading, setLoading] = useState<boolean>(true);
	const [currentPage, setCurrentPage] = useState<number>(1);
	const [pageSize] = useState<number>(10);
	const [totalCount, setTotalCount] = useState<number>(0);

	const [selectedRequest, setSelectedRequest] = useState<ExtendedWarrantyRequest | null>(null);
	const [viewModalOpen, setViewModalOpen] = useState(false);


	// Handle view action
	const handleView = (request: ExtendedWarrantyRequest) => {
		setSelectedRequest(request);
		setViewModalOpen(true);
	};
	
	const fetchRequests = useCallback(
		async (page: number) => {
			try {
				setLoading(true);
				const params = new URLSearchParams({
					page: String(page),
					pageSize: String(pageSize)
				});
				const res = await fetch(
					`/api/warranty-requests-ex/${statusKey}?${params.toString()}`
				);
				if (!res.ok) throw new Error("Failed to fetch");
				const data = await res.json();
				setRequests(data.requests || []);
				setTotalCount(data.totalCount || 0);
			} catch (err) {
				console.error("Failed to fetch status requests:", err);
				setRequests([]);
				setTotalCount(0);
			} finally {
				setLoading(false);
			}
		},
		[pageSize, statusKey]
	);

	useEffect(() => {
		setCurrentPage(1);
	}, [statusKey]);

	useEffect(() => {
		fetchRequests(currentPage);
	}, [currentPage, fetchRequests]);

	const totalPages = Math.max(1, Math.ceil(totalCount / pageSize));

	if (loading && requests.length === 0) {
		return (
			<TooltipProvider>
				<div className="space-y-4">
					<div className="rounded-lg border border-gray-200 shadow-sm bg-white overflow-hidden text-sm">
						<Table>
							<TableHeader
								style={{ backgroundColor: company?.brand_color ?? "#2563eb" }}
							>
								<TableRow>
									<TableHead className="text-white font-semibold">
										<Skeleton className="h-4 w-[90px] bg-white/20" />
									</TableHead>
									<TableHead className="text-white font-semibold">
										<Skeleton className="h-4 w-[110px] bg-white/20" />
									</TableHead>
									<TableHead className="text-white font-semibold">
										<Skeleton className="h-4 w-[140px] bg-white/20" />
									</TableHead>
									<TableHead className="text-white font-semibold">
										<Skeleton className="h-4 w-[100px] bg-white/20" />
									</TableHead>
									<TableHead className="text-white font-semibold">
										<Skeleton className="h-4 w-[110px] bg-white/20" />
									</TableHead>
									<TableHead className="text-white font-semibold">
										<Skeleton className="h-4 w-[80px] bg-white/20" />
									</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{Array.from({ length: 10 }).map((_, index) => (
									<TableRow
										key={`skeleton-${index}`}
										className={`h-16 ${index % 2 === 0 ? "bg-gray-50" : "bg-gray-100"}`}
									>
										<TableCell className="px-3">
											<div className="flex flex-col items-center gap-2">
												<Skeleton className="h-5 w-16" />
												<Skeleton className="h-5 w-20" />
											</div>
										</TableCell>
										<TableCell className="px-4">
											<div className="space-y-2">
												<Skeleton className="h-4 w-32" />
												<Skeleton className="h-3 w-40" />
											</div>
										</TableCell>
										<TableCell className="px-4">
											<div className="space-y-2">
												<Skeleton className="h-4 w-52" />
												<Skeleton className="h-3 w-40" />
											</div>
										</TableCell>
										<TableCell className="px-4">
											<div className="grid grid-cols-2 gap-2 w-40">
												{Array.from({ length: 4 }).map((__, i) => (
													<Skeleton key={i} className="h-3 w-full" />
												))}
											</div>
										</TableCell>
										<TableCell className="px-4">
											<div className="space-y-2">
												<Skeleton className="h-3 w-24" />
												<Skeleton className="h-4 w-28" />
												<Skeleton className="h-3 w-20" />
											</div>
										</TableCell>
										<TableCell className="px-4">
											<Skeleton className="h-6 w-6 rounded-full" />
										</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					</div>

					<div className="flex items-center justify-between pt-4">
						<Skeleton className="h-9 w-[100px]" />
						<Skeleton className="h-4 w-[120px]" />
						<Skeleton className="h-9 w-[100px]" />
					</div>
				</div>
			</TooltipProvider>
		);
	}

	return (
		<TooltipProvider>
			<div className="space-y-4">
				<div className="rounded-lg border border-gray-200 shadow-sm bg-white overflow-hidden text-sm">
					<Table>
						<TableHeader
							style={{ backgroundColor: company?.brand_color ?? "#2563eb" }}
						>
							<TableRow>
								<TableHead className="text-white font-semibold">
									ID/Status
								</TableHead>
								<TableHead className="text-white font-semibold">
									Customer
								</TableHead>
								<TableHead className="text-white font-semibold">
									Issue
								</TableHead>
								<TableHead className="text-white font-semibold">
									Invitations
								</TableHead>
								<TableHead className="text-white font-semibold">
									Last Activity
								</TableHead>
								<TableHead className="text-white font-semibold">
									Actions
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{Array.from({ length: 10 }, (_, index) => {
								const request = requests[index];
								if (!request) {
									return (
										<TableRow
											key={`empty-${index}`}
											className={`h-16 ${index % 2 === 0 ? "bg-gray-50" : "bg-gray-100"}`}
										>
											<TableCell
												colSpan={7}
												className="text-center text-muted-foreground"
											>
												{requests.length === 0 && index === 0
													? "No warranty requests found"
													: loading && index === 0
														? "Loading…"
														: ""}
											</TableCell>
										</TableRow>
									);
								}
								return (
									<WarrantyRequestPendingRow
										key={request.id}
										request={request}
										company={company}
										index={index}
										onView={handleView}
										user={user}
									/>
								);
							})}
						</TableBody>
					</Table>
				</div>

				<div className="flex items-center justify-between pt-4">
					<Button
						variant="outline"
						onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
						disabled={currentPage === 1 || loading || totalCount === 0}
						style={{
							backgroundColor: company?.brand_color ?? "#2563eb",
							color: "#fff"
						}}
					>
						Previous
					</Button>
					<span className="text-sm text-muted-foreground">
						{totalCount > 0
							? `Page ${currentPage} of ${totalPages}`
							: loading
								? ""
								: "No records"}
					</span>
					<Button
						variant="outline"
						onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
						disabled={currentPage === totalPages || loading || totalCount === 0}
						style={{
							backgroundColor: company?.brand_color ?? "#2563eb",
							color: "#fff"
						}}
					>
						Next
					</Button>
				</div>
			</div>

			{/* View Request Dialog */}
			{selectedRequest && (
				<WarrantyRequestCardDialog
					open={viewModalOpen}
					onClose={() => setViewModalOpen(false)}
					company={company}
					request={selectedRequest}
					onStatusUpdated={() => fetchRequests(currentPage)}
					user={user}
				/>
			)}
		</TooltipProvider>
	);
}
