"use client";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Progress } from "@/components/ui/progress";
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from "@/components/ui/tooltip";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { User } from "@rvhelp/database";
import { Clock, DollarSign, Eye, FileText, Loader2, Mail, MoreHorizontal } from "lucide-react";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

interface WarrantyRequestActionsProps {
    request: ExtendedWarrantyRequest;
    company?: any;
    onView: (request: ExtendedWarrantyRequest) => void;
    onUpdateStatus: (request: ExtendedWarrantyRequest) => void;
    onPayInvoice: (request: ExtendedWarrantyRequest) => void;
    onGeneratePlatformInvoice?: (request: ExtendedWarrantyRequest) => void;
    onDelete?: (requestId: string) => void;
    onResendEmail?: (request: ExtendedWarrantyRequest) => void;
    user?: User;
    isDeleting?: boolean;
}

function darkenColor(color: string, amount: number) {
    // Simple darken: works for hex colors only
    if (!color.startsWith("#") || (color.length !== 7 && color.length !== 4))
        return color;
    let r, g, b;
    if (color.length === 7) {
        r = parseInt(color.slice(1, 3), 16);
        g = parseInt(color.slice(3, 5), 16);
        b = parseInt(color.slice(5, 7), 16);
    } else {
        r = parseInt(color[1] + color[1], 16);
        g = parseInt(color[2] + color[2], 16);
        b = parseInt(color[3] + color[3], 16);
    }
    r = Math.floor(r * amount);
    g = Math.floor(g * amount);
    b = Math.floor(b * amount);
    return `#${r.toString(16).padStart(2, "0")}${g.toString(16).padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
}

export default function WarrantyRequestActions({
    request,
    company,
    onView,
    onUpdateStatus,
    onPayInvoice,
    onGeneratePlatformInvoice,
    onDelete,
    onResendEmail,
    user,
    isDeleting
}: WarrantyRequestActionsProps) {
    const [isDeletingLocal, setIsDeletingLocal] = useState(false);
    const [isGeneratingInvoice, setIsGeneratingInvoice] = useState(false);
    const [invoiceProgress, setInvoiceProgress] = useState(0);
    const [isResendingEmail, setIsResendingEmail] = useState(false);
    const shouldShowGeneratePlatformInvoice = !request.platform_invoice_id && user?.role === "ADMIN" && onGeneratePlatformInvoice;
    const shouldShowDelete = user?.role === "ADMIN" && request.oem_user?.role === "ADMIN";
    const shouldShowResendEmail = onResendEmail && request.email_sent_at; // Only show if email was previously sent
    const shouldShowMoreActions = user?.role === "ADMIN" && (shouldShowGeneratePlatformInvoice || shouldShowDelete || shouldShowResendEmail);

    // Progress simulation for invoice generation
    useEffect(() => {
        let progressInterval: NodeJS.Timeout;

        if (isGeneratingInvoice) {
            setInvoiceProgress(0);
            progressInterval = setInterval(() => {
                setInvoiceProgress((prev) => {
                    if (prev >= 90) {
                        clearInterval(progressInterval);
                        return 90;
                    }
                    return prev + 10;
                });
            }, 200);
        } else {
            setInvoiceProgress(0);
        }

        return () => {
            if (progressInterval) {
                clearInterval(progressInterval);
            }
        };
    }, [isGeneratingInvoice]);

    const handleDelete = async () => {
        if (!onDelete) {
            return;
        }

        setIsDeletingLocal(true);
        try {
            await onDelete(request.id);
            toast.success('Warranty request deleted successfully');
        } catch (error) {
            console.error('Error deleting warranty request:', error);
            toast.error('Failed to delete warranty request');
        } finally {
            setIsDeletingLocal(false);
        }
    };

    const handleGeneratePlatformInvoice = async () => {
        if (!onGeneratePlatformInvoice) return;

        setIsGeneratingInvoice(true);
        try {
            await onGeneratePlatformInvoice(request);
            setInvoiceProgress(100);
            // Small delay to show completion
            setTimeout(() => {
                setIsGeneratingInvoice(false);
                setInvoiceProgress(0);
            }, 500);
        } catch (error) {
            setIsGeneratingInvoice(false);
            setInvoiceProgress(0);
        }
    };

    const handleResendEmail = async () => {
        if (!onResendEmail) return;

        setIsResendingEmail(true);
        try {
            await onResendEmail(request);
        } catch (error) {
            console.error('Error resending warranty request email:', error);
        } finally {
            setIsResendingEmail(false);
        }
    };

    // Check if status is pending approval
    const isPendingApproval = (status: string, userRole?: string) => {
        if (userRole !== "ADMIN" && userRole !== "OEM") return false;
        return status === "AUTHORIZATION_REQUESTED" || status === "INVOICE_CREATED";
    };

    return (
        <>
            <div className="flex flex-row gap-2">
                <Tooltip>
                    <TooltipTrigger asChild>
                        <button
                            type="button"
                            onClick={() => onView(request)}
                            aria-label="View request"
                            style={{
                                backgroundColor: company?.brand_color ?? "#2563eb",
                                color: "#fff"
                            }}
                            className="w-6 h-6 rounded-full flex items-center justify-center transition-colors"
                            onMouseEnter={(e) =>
                            (e.currentTarget.style.backgroundColor = darkenColor(
                                company?.brand_color ?? "#2563eb",
                                0.9
                            ))
                            }
                            onMouseLeave={(e) =>
                            (e.currentTarget.style.backgroundColor =
                                company?.brand_color ?? "#2563eb")
                            }
                        >
                            <Eye className="h-4 w-4 text-white" />
                        </button>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>View</p>
                    </TooltipContent>
                </Tooltip>


                <Tooltip>
                    <TooltipTrigger asChild>
                        <button
                            type="button"
                            onClick={() => onUpdateStatus(request)}
                            aria-label="Update status"
                            disabled={!isPendingApproval(request.status, user?.role)}
                            style={
                                isPendingApproval(request.status, user?.role)
                                    ? {
                                        backgroundColor: company?.brand_color ?? "#2563eb",
                                        color: "#fff"
                                    }
                                    : {}
                            }
                            className={`w-6 h-6 rounded-full flex items-center justify-center transition-colors ${isPendingApproval(request.status, user?.role)
                                ? ""
                                : "bg-gray-200 text-gray-400 cursor-not-allowed"
                                }`}
                            onMouseEnter={
                                isPendingApproval(request.status, user?.role)
                                    ? (e) =>
                                    (e.currentTarget.style.backgroundColor = darkenColor(
                                        company?.brand_color ?? "#2563eb",
                                        0.9
                                    ))
                                    : undefined
                            }
                            onMouseLeave={
                                isPendingApproval(request.status, user?.role)
                                    ? (e) =>
                                    (e.currentTarget.style.backgroundColor =
                                        company?.brand_color ?? "#2563eb")
                                    : undefined
                            }
                        >
                            <Clock className="h-4 w-4" />
                        </button>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>
                            {isPendingApproval(request.status, user?.role)
                                ? "Update Status"
                                : "Status cannot be updated"}
                        </p>
                    </TooltipContent>
                </Tooltip>

                {request.status === "INVOICE_CREATED" && user?.role === "ADMIN" && (
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <button
                                type="button"
                                onClick={() => onPayInvoice(request)}
                                aria-label="Pay invoice"
                                style={
                                    isPendingApproval(request.status, user?.role)
                                        ? {
                                            backgroundColor: company?.brand_color ?? "#2563eb",
                                            color: "#fff"
                                        }
                                        : {}
                                }
                                className={`w-6 h-6 rounded-full flex items-center justify-center transition-colors ${isPendingApproval(request.status, user?.role)
                                    ? ""
                                    : "bg-gray-200 text-gray-400 cursor-not-allowed"
                                    }`}
                                onMouseEnter={
                                    isPendingApproval(request.status, user?.role)
                                        ? (e) =>
                                        (e.currentTarget.style.backgroundColor = darkenColor(
                                            company?.brand_color ?? "#2563eb",
                                            0.9
                                        ))
                                        : undefined
                                }
                                onMouseLeave={
                                    isPendingApproval(request.status, user?.role)
                                        ? (e) =>
                                        (e.currentTarget.style.backgroundColor =
                                            company?.brand_color ?? "#2563eb")
                                        : undefined
                                }
                            >
                                <DollarSign className="h-4 w-4 text-white" />
                            </button>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Pay Invoice</p>
                        </TooltipContent>
                    </Tooltip>
                )}

                {/* More actions dropdown - only show for admin users when invoice is paid */}
                {shouldShowMoreActions && (
                    <DropdownMenu>

                        {shouldShowGeneratePlatformInvoice && (
                            <DropdownMenuTrigger asChild>
                                <button
                                    type="button"
                                    aria-label="More actions"
                                    style={{
                                        backgroundColor: company?.brand_color ?? "#2563eb",
                                        color: "#fff"
                                    }}
                                    className="w-6 h-6 rounded-full flex items-center justify-center transition-colors"
                                    onMouseEnter={(e) =>
                                    (e.currentTarget.style.backgroundColor = darkenColor(
                                        company?.brand_color ?? "#2563eb",
                                        0.9
                                    ))
                                    }
                                    onMouseLeave={(e) =>
                                    (e.currentTarget.style.backgroundColor =
                                        company?.brand_color ?? "#2563eb")
                                    }
                                >
                                    <MoreHorizontal className="h-4 w-4 text-white" />
                                </button>
                            </DropdownMenuTrigger>
                        )}

                        {shouldShowMoreActions && (
                            <DropdownMenuContent align="end">

                                <DropdownMenuItem
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleResendEmail();
                                    }}
                                    disabled={isResendingEmail}
                                    className="flex items-center gap-2"
                                >
                                    <Mail className="h-4 w-4" />
                                    {isResendingEmail ? "Resending..." : "Resend Invite"}
                                </DropdownMenuItem>

                                {!request.platform_invoice_id && user?.role === "ADMIN" && onGeneratePlatformInvoice && (
                                    <DropdownMenuItem
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleGeneratePlatformInvoice();
                                        }}
                                        className="flex items-center gap-2"
                                    >
                                        <FileText className="h-4 w-4" />
                                        Generate Platform Invoice
                                    </DropdownMenuItem>
                                )}
                                {/* Delete button - only show for admin users and when the request creator is also an admin */}
                                {user?.role === "ADMIN" && request.oem_user?.role === "ADMIN" && (
                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <DropdownMenuItem
                                                onSelect={(e) => e.preventDefault()}
                                                className="flex items-center gap-2 text-red-600"
                                            >
                                                Delete Request
                                            </DropdownMenuItem>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Delete Warranty Request</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    Are you sure you want to delete this warranty request? This action cannot be undone and will permanently remove:
                                                    <ul className="list-disc list-inside mt-2 space-y-1">
                                                        <li>The warranty request and all its data</li>
                                                        <li>All timeline updates</li>
                                                        <li>Associated job and quotes</li>
                                                        <li>All related messages and invoices</li>
                                                    </ul>
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        handleDelete();
                                                    }}
                                                    disabled={isDeleting || isDeletingLocal}
                                                    className="bg-red-600 hover:bg-red-700 text-white"
                                                >
                                                    {isDeleting || isDeletingLocal ? "Deleting..." : "Delete Request"}
                                                </AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>
                                )}

                            </DropdownMenuContent>
                        )}
                    </DropdownMenu>
                )}
            </div>

            {/* Loading Modal for Platform Invoice Generation */}
            <Dialog open={isGeneratingInvoice} onOpenChange={() => { }}>
                <DialogContent className="max-w-md">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <Loader2 className="h-5 w-5 animate-spin" />
                            Generating Platform Invoice
                        </DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div className="text-center space-y-2">
                            <p className="text-sm text-muted-foreground">
                                Creating platform invoice for warranty request #{request.id.slice(0, 8)}...
                            </p>
                            <p className="text-xs text-muted-foreground">
                                This may take a few moments
                            </p>
                        </div>
                        <div className="space-y-2">
                            <Progress value={invoiceProgress} className="w-full" />
                            <p className="text-xs text-center text-muted-foreground">
                                {invoiceProgress}% complete
                            </p>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
}
