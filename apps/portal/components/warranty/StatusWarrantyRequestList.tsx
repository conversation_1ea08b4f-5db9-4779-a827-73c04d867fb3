"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { useCallback, useEffect, useState } from "react";
import WarrantyRequestTableRow from "./WarrantyRequestTableRow";

type StatusKey = "new" | "pending" | "in-progress" | "done";

interface StatusWarrantyRequestListProps {
	company?: any;
	user: any;
	statusKey: StatusKey;
}

export default function StatusWarrantyRequestList({
	company,
	user,
	statusKey
}: StatusWarrantyRequestListProps) {
	const [requests, setRequests] = useState<ExtendedWarrantyRequest[]>([]);
	const [loading, setLoading] = useState<boolean>(true);
	const [currentPage, setCurrentPage] = useState<number>(1);
	const [pageSize] = useState<number>(10);
	const [totalCount, setTotalCount] = useState<number>(0);
	const [deletingRequestId, setDeletingRequestId] = useState<string | null>(null);

	const fetchRequests = useCallback(
		async (page: number) => {
			try {
				setLoading(true);
				const params = new URLSearchParams({
					page: String(page),
					pageSize: String(pageSize)
				});
				const res = await fetch(
					`/api/warranty-requests-ex/${statusKey}?${params.toString()}`
				);
				if (!res.ok) throw new Error("Failed to fetch");
				const data = await res.json();
				setRequests(data.requests || []);
				setTotalCount(data.totalCount || 0);
			} catch (err) {
				console.error("Failed to fetch status requests:", err);
				setRequests([]);
				setTotalCount(0);
			} finally {
				setLoading(false);
			}
		},
		[pageSize, statusKey]
	);

	useEffect(() => {
		setCurrentPage(1);
	}, [statusKey]);

	useEffect(() => {
		fetchRequests(currentPage);
	}, [currentPage, fetchRequests]);

	const totalPages = Math.max(1, Math.ceil(totalCount / pageSize));

	// Handle delete warranty request
	const handleDelete = async (requestId: string) => {
		setDeletingRequestId(requestId);
		try {
			const response = await fetch(`/api/warranty-requests/${requestId}/delete`, {
				method: 'DELETE',
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to delete warranty request');
			}

			// Refresh the list after deletion
			fetchRequests(currentPage);

		} catch (error) {
			console.error('Error deleting warranty request:', error);
			throw error; // Re-throw to let the component handle the toast
		} finally {
			setDeletingRequestId(null);
		}
	};

	return (
		<TooltipProvider>
			<div className="space-y-4">
				<div className="rounded-lg border border-gray-200 shadow-sm bg-white overflow-hidden text-sm">
					<Table>
						<TableHeader
							style={{ backgroundColor: company?.brand_color ?? "#2563eb" }}
						>
							<TableRow>
								<TableHead className="text-white font-semibold">
									ID/Status
								</TableHead>
								<TableHead className="text-white font-semibold">
									Customer
								</TableHead>
								<TableHead className="text-white font-semibold">
									Issue
								</TableHead>
								<TableHead className="text-white font-semibold">
									RV Details
								</TableHead>
								<TableHead className="text-white font-semibold">
									Hours
								</TableHead>
								<TableHead className="text-white font-semibold">
									Submitted
								</TableHead>
								<TableHead className="text-white font-semibold">
									Actions
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{Array.from({ length: 10 }, (_, index) => {
								const request = requests[index];
								if (!request) {
									return (
										<TableRow
											key={`empty-${index}`}
											className={`h-16 ${index % 2 === 0 ? "bg-gray-50" : "bg-gray-100"}`}
										>
											<TableCell
												colSpan={7}
												className="text-center text-muted-foreground"
											>
												{requests.length === 0 && index === 0
													? "No warranty requests found"
													: loading && index === 0
														? "Loading…"
														: ""}
											</TableCell>
										</TableRow>
									);
								}
								return (
									<WarrantyRequestTableRow
										key={request.id}
										request={request}
										company={company}
										index={index}
										onView={() => { }}
										onUpdateStatus={() => { }}
										onPayInvoice={() => { }}
										onDelete={handleDelete}
										user={user}
										isDeleting={deletingRequestId === request.id}
									/>
								);
							})}
						</TableBody>
					</Table>
				</div>

				<div className="flex items-center justify-between pt-4">
					<Button
						variant="outline"
						onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
						disabled={currentPage === 1 || loading || totalCount === 0}
						style={{
							backgroundColor: company?.brand_color ?? "#2563eb",
							color: "#fff"
						}}
					>
						Previous
					</Button>
					<span className="text-sm text-muted-foreground">
						{totalCount > 0
							? `Page ${currentPage} of ${totalPages}`
							: loading
								? ""
								: "No records"}
					</span>
					<Button
						variant="outline"
						onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
						disabled={currentPage === totalPages || loading || totalCount === 0}
						style={{
							backgroundColor: company?.brand_color ?? "#2563eb",
							color: "#fff"
						}}
					>
						Next
					</Button>
				</div>
			</div>
		</TooltipProvider>
	);
}
