"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogFooter } from "@/components/ui/dialog";
import { ExtendedCompany } from "@/types/warranty";
import { FormEvent, ReactNode } from "react";

interface EditModalProps {
	open: boolean;
	onClose: () => void;
	children: ReactNode;
	onSave: (e: FormEvent) => void | Promise<void>;
	saveButtonText?: string;
	cancelButtonText?: string;
	isSaving?: boolean;
	disableSave?: boolean;
	formId?: string;
	company?: ExtendedCompany;
	noBorder?: boolean;
	hideCloseButton?: boolean;
}

export function EditModal({
	open,
	onClose,
	children,
	onSave,
	saveButtonText = "Save Changes",
	cancelButtonText = "Cancel",
	isSaving = false,
	disableSave = false,
	formId,
	company
}: EditModalProps) {
	const handleSave = async (e: FormEvent) => {
		e.preventDefault();
		await onSave(e);
	};

	return (
		<Dialog open={open} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="max-w-4xl">
				<form onSubmit={handleSave} className="flex flex-col" id={formId}>
					<div className="flex-1 overflow-y-auto">{children}</div>

					{/* Footer with action buttons */}
					<DialogFooter className="bg-white px-6 py-4 border-t border-gray-100">
						<div className="flex justify-end gap-3 w-full">
							<Button
								type="button"
								variant="outline"
								onClick={onClose}
								disabled={isSaving}
								className="px-6"
								style={{
									color: company?.brand_color,
									borderColor: company?.brand_color
								}}
							>
								{cancelButtonText}
							</Button>
							<Button
								type="submit"
								disabled={isSaving || disableSave}
								className="px-6"
								style={{
									backgroundColor: company?.brand_color ?? "#2563eb",
									color: "#fff"
								}}
							>
								{isSaving ? (
									<div className="flex items-center gap-2">
										<div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
										Saving...
									</div>
								) : (
									saveButtonText
								)}
							</Button>
						</div>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
