"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { Button } from "@/components/ui/button";
import {
	LocationForm,
	LocationSchema
} from "@/components/warranty/warranty-request-wizard/steps/location-step";
import { useAuth } from "@/lib/hooks/useAuth";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import { EditModal } from "../../edit-modal";

interface LocationModalProps {
	open: boolean;
	onClose: () => void;
	onUpdate: (data: any) => Promise<any>;
	company?: ExtendedCompany;
	request?: ExtendedWarrantyRequest;
}

export function LocationModal({
	open,
	onClose,
	onUpdate,
	company,
	request
}: LocationModalProps) {
	const [loading, setLoading] = useState(false);
	const [isConfirmed, setIsConfirmed] = useState(false);
	const { user } = useAuth();
	const form = useForm({
		resolver: zodResolver(LocationSchema)
	});

	const isAdmin = user?.role === "ADMIN";

	const onSubmit = async (data: any) => {
		setLoading(true);
		try {
			await onUpdate(data);

			// If admin, show confirmation instead of closing
			if (isAdmin) {
				setIsConfirmed(true);
			} else {
				onClose();
			}
		} catch (error) {
			console.error("Failed to update location:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleClose = () => {
		setIsConfirmed(false);
		onClose();
	};

	const handleSave = async (e: React.FormEvent) => {
		console.log(form.getValues());
		console.log(form.formState.errors);
		e.preventDefault();
		await form.handleSubmit(onSubmit)(e);
	};

	// Show confirmation state for admin users
	if (isConfirmed && isAdmin) {
		return (
			<EditModal
				open={open}
				onClose={onClose}
				onSave={handleSave}
				isSaving={loading}
				saveButtonText={loading ? "Saving..." : "Save Changes"}
				company={company}
			>
				<div className="flex flex-col">
					<div className="bg-gray-50 p-6">
						<div className="bg-white rounded-lg p-6 shadow-sm text-center">
							<div className="mb-4">
								<div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
									<svg
										className="w-6 h-6 text-green-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M5 13l4 4L19 7"
										/>
									</svg>
								</div>
							</div>
							<h3 className="text-lg font-semibold text-gray-900 mb-2">
								Location Updated Successfully
							</h3>
							<p className="text-gray-600 mb-6">
								The service location has been updated for this warranty request.
							</p>
							<Button
								onClick={handleClose}
								className="px-6"
								style={{
									backgroundColor: company?.brand_color ?? "#2563eb",
									color: "#fff"
								}}
							>
								Close
							</Button>
						</div>
					</div>
				</div>
			</EditModal>
		);
	}

	return (
		<EditModal
			open={open}
			onClose={onClose}
			onSave={handleSave}
			isSaving={loading}
			saveButtonText={loading ? "Saving..." : "Save Changes"}
			company={company}
		>
			<div className="flex flex-col">
				<div className="bg-gray-50 max-h-[60vh] overflow-y-auto">
					<div className="bg-white rounded-lg p-6 shadow-sm">
						<LocationForm form={form} company={company} />
					</div>
					<div className="h-px bg-gray-100 w-11/12 mx-auto"></div>
				</div>
			</div>
		</EditModal>
	);
}
