import { ExtendedWarrantyRequest } from '@/types/warranty';
import { Hammer } from 'lucide-react';

export function CauseCorrectionSection({
    brandColor,
    request,
    onEdit,
}: {
    brandColor: string;
    request: ExtendedWarrantyRequest;
    onEdit?: () => void;
}) {
    return (
        <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
            <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2 font-bold text-md text-slate-700">
                    <div
                        className={`${brandColor ? '' : 'bg-primary'} rounded-full p-1`}
                        style={brandColor ? { backgroundColor: brandColor } : {}}
                    >
                        <Hammer className="w-4 h-4 text-white" />
                    </div>
                    Cause & Correction
                </div>
                {onEdit && (
                    <button
                        type="button"
                        onClick={onEdit}
                        className="text-xs font-medium underline"
                        style={{ color: brandColor }}
                    >
                        Edit
                    </button>
                )}
            </div>
            <div className="grid grid-cols-1 gap-4 text-sm">
                <div>
                    <div className="text-xs font-semibold text-gray-600">Cause</div>
                    <div>
                        {request.cause || (
                            <span className="text-gray-500 italic">No cause specified</span>
                        )}
                    </div>
                </div>
                <div>
                    <div className="text-xs font-semibold text-gray-600">Correction</div>
                    <div>
                        {request.correction || (
                            <span className="text-gray-500 italic">No correction specified</span>
                        )}
                    </div>
                </div>
            </div>
            <div className="grid grid-cols-1 gap-2 pt-4">
                <div>
                    <div className="text-xs font-semibold text-gray-600">Estimated Hours</div>
                    <div>
                        {request.estimated_hours || (
                            <span className="text-gray-500 italic">No hours specified</span>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
