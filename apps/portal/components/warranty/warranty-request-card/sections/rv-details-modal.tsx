"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { EditModal } from "@/components/warranty/edit-modal";
import {
	RVDetailsForm,
	RVDetailsSchema
} from "@/components/warranty/warranty-request-wizard/steps/rv-details-step";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";

interface RVDetailsModalProps {
	open: boolean;
	onClose: () => void;
	onUpdate: (data: any) => Promise<any>;
	request?: ExtendedWarrantyRequest;
	company?: ExtendedCompany;
}

export function RVDetailsModal({
	open,
	onClose,
	onUpdate,
	company,
	request
}: RVDetailsModalProps) {
	const [loading, setLoading] = useState(false);
	const form = useForm({
		resolver: zodResolver(RVDetailsSchema)
	});

	const onSubmit = async (data: any) => {
		setLoading(true);
		try {
			await onUpdate(data);
			onClose();
		} catch (error) {
			console.error("Failed to update RV info:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleSave = async (e: React.FormEvent) => {
		e.preventDefault();
		await form.handleSubmit(onSubmit)(e);
	};

	return (
		<EditModal
			open={open}
			onClose={onClose}
			onSave={handleSave}
			isSaving={loading}
			saveButtonText={loading ? "Saving..." : "Save Changes"}
			company={company}
		>
			<div className="flex flex-col">
				<div className="bg-gray-50 max-h-[60vh] overflow-y-auto">
					<div className="bg-white rounded-lg p-6 shadow-sm">
						<RVDetailsForm form={form} company={company} request={request} />
					</div>
				</div>
			</div>
		</EditModal>
	);
}
