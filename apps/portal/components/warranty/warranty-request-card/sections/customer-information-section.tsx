import { ExtendedWarrantyRequest } from "@/types/warranty";
import { User } from "lucide-react";

export function CustomerInformationSection({
	brandColor,
	request,
	onEdit
}: {
	brandColor: string;
	request: ExtendedWarrantyRequest;
	onEdit?: () => void;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
			<div className="flex items-center justify-between mb-3">
				<div className="flex items-center gap-2 font-bold text-md text-slate-700">
					<div
						className={`${brandColor ? "" : "bg-primary"} rounded-full p-1`}
						style={brandColor ? { backgroundColor: brandColor } : {}}
					>
						<User className="w-4 h-4 text-white" />
					</div>
					Customer Information
				</div>
				{onEdit && (
					<button
						type="button"
						onClick={onEdit}
						className="text-xs font-medium underline"
						style={{ color: brandColor }}
					>
						Edit
					</button>
				)}
			</div>
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-2 text-sm">
				<div>
					<div className="text-xs font-semibold text-gray-600">Name</div>
					<div>
						{request.first_name} {request.last_name}
					</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Email</div>
					<div>{request.email}</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Phone</div>
					<div>{request.phone}</div>
				</div>
			</div>
		</div>
	);
}
