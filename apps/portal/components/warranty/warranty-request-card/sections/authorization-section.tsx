import { ExtendedWarrantyRequest } from '@/types/warranty';
import { ShieldCheck } from 'lucide-react';

export function AuthorizationSection({
    brandColor,
    request,
    onEdit,
}: {
    brandColor: string;
    request: ExtendedWarrantyRequest;
    onEdit?: () => void;
}) {
    return (
        <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200 flex-1 flex flex-col">
            <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-1 font-bold text-md text-slate-700">
                    <div
                        className={`${brandColor ? '' : 'bg-primary'} rounded-full p-1`}
                        style={brandColor ? { backgroundColor: brandColor } : {}}
                    >
                        <ShieldCheck className="w-4 h-4 text-white" />
                    </div>
                    Service Authorization
                </div>
                {onEdit && (
                    <button
                        type="button"
                        onClick={onEdit}
                        className="text-xs font-medium underline"
                        style={{ color: brandColor }}
                    >
                        Edit
                    </button>
                )}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 text-sm">
                <div>
                    <div className="text-xs font-semibold text-gray-600">Authorization Type</div>
                    <div>
                        {request.authorization_type || (
                            <span className="text-gray-500 italic">No hours specified</span>
                        )}
                    </div>
                </div>

                <div>
                    <div className="text-xs font-semibold text-gray-600">Authorized Hours</div>
                    <div>
                        {request.approved_hours || (
                            <span className="text-gray-500 italic">No hours specified</span>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
