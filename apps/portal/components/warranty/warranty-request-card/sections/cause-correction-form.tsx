"use client";

import { Textarea } from "@/components/ui/textarea";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

export const CauseCorrectionSchema = z.object({
	// Service Request Details
	cause: z.string().max(500, "Cause cannot exceed 500 characters"),
	correction: z
		.string()
		.max(500, "Suggested correction cannot exceed 500 characters")
});

type CauseCorrectionFormData = z.infer<typeof CauseCorrectionSchema>;

interface CauseCorrectionFormProps {
	form: UseFormReturn<CauseCorrectionFormData>;
	request?: ExtendedWarrantyRequest;
}

export function CauseCorrectionForm({
	form,
	request
}: CauseCorrectionFormProps) {
	const { register, formState } = form;

	useEffect(() => {
		if (request) {
			form.setValue("cause", request.cause);
			form.setValue("correction", request.correction);
		}
	}, [request, form]);
	return (
		<div className="space-y-6">
			{/* Service Request Details */}
			<div>
				<div className="font-semibold mb-4">Diagnostic Details</div>
				<div className="space-y-4">
					<Textarea
						{...register("cause")}
						error={String(formState.errors.cause?.message || "")}
						placeholder="What do you believe caused this issue?"
						rows={2}
						label={"Cause (if known)"}
					/>
					<Textarea
						{...register("correction")}
						error={String(formState.errors.correction?.message || "")}
						placeholder="How do you think this should be fixed?"
						rows={2}
						label={"Suggested Correction"}
					/>
				</div>
			</div>
		</div>
	);
}
