"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { EditModal } from "@/components/warranty/edit-modal";
import {
	IssueDescriptionSchema,
	IssueDescriptionStep
} from "@/components/warranty/warranty-request-wizard/steps/issue-description-step";
import {
	ExtendedCompany,
	ExtendedComponent,
	ExtendedWarrantyRequest
} from "@/types/warranty";

interface IssueDescriptionModalProps {
	open: boolean;
	onClose: () => void;
	company: ExtendedCompany;
	request: ExtendedWarrantyRequest;
	onUpdate: (data: any) => Promise<any>;
	componentManager?: {
		handleComponentsUpdate: (
			components: ExtendedComponent[]
		) => Promise<ExtendedComponent[]>;
		deleteComponent: (componentId: string) => Promise<void>;
		isUpdating: boolean;
	};
}

export function IssueDescriptionModal({
	open,
	onClose,
	company,
	onUpdate,
	request,
	componentManager
}: IssueDescriptionModalProps) {
	const [loading, setLoading] = useState(false);

	const form = useForm({
		resolver: zodResolver(IssueDescriptionSchema)
	});

	const onSubmit = async (data: any) => {
		setLoading(true);
		try {
			await onUpdate(data);
			onClose();
		} catch (error) {
			console.error("Failed to update issue details:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleSave = async (e: React.FormEvent) => {
		e.preventDefault();
		await form.handleSubmit(onSubmit)(e);
	};

	return (
		<EditModal
			open={open}
			onClose={onClose}
			onSave={handleSave}
			isSaving={loading}
			saveButtonText={loading ? "Saving..." : "Save Changes"}
			company={company}
		>
			<div className="bg-gray-50 max-h-[60vh]">
				<div className="bg-white rounded-lg p-6 shadow-sm">
					<div className="flex items-center gap-2 mb-4">
						<div
							className="rounded-full p-1"
							style={{ backgroundColor: company?.brand_color || "#2563eb" }}
						>
							<svg
								className="w-4 h-4 text-white"
								fill="currentColor"
								viewBox="0 0 20 20"
							>
								<path
									fillRule="evenodd"
									d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
									clipRule="evenodd"
								/>
							</svg>
						</div>
						<h3 className="font-semibold text-gray-900">Issue Details</h3>
					</div>
					<IssueDescriptionStep
						form={form}
						company={company}
						request={request}
						componentManager={componentManager}
					/>
				</div>
			</div>
		</EditModal>
	);
}
