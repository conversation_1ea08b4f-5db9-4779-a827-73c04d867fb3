import { ExtendedWarrantyRequest } from "@/types/warranty";
import { Info } from "lucide-react";

export function IssueDescriptionSection({
	brandColor,
	request,
	onEdit
}: {
	brandColor: string;
	request: ExtendedWarrantyRequest;
	onEdit?: () => void;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200 flex-1 flex flex-col">
			<div className="flex items-center justify-between mb-3">
				<div className="flex items-center gap-1 font-bold text-md text-slate-700">
					<div
						className={`${brandColor ? "" : "bg-primary"} rounded-full p-1`}
						style={brandColor ? { backgroundColor: brandColor } : {}}
					>
						<Info className="w-4 h-4 text-white" />
					</div>
					Issue Description
				</div>
				{onEdit && (
					<button
						type="button"
						onClick={onEdit}
						className="text-xs font-medium underline"
						style={{ color: brandColor }}
					>
						Edit
					</button>
				)}
			</div>
			<div className="grid grid-cols-1 gap-2 text-sm">
				<div>
					<div className="text-xs font-semibold text-gray-600">Description</div>
					<div>{request.complaint}</div>
				</div>
				{request.notes_to_provider && (
					<div>
						<div className="text-xs font-semibold text-gray-600 pt-2">
							Notes to Provider
						</div>
						<div>{request.notes_to_provider}</div>
					</div>
				)}
				{request.listing?.pricing_settings && (
					<div>
						<div className="text-xs font-semibold text-gray-600 pt-2">
							Pricing Information
						</div>
						<div className="space-y-1">
							{request.listing.pricing_settings.hourly_rate && (
								<div className="text-sm">
									<span className="font-medium">Hourly Rate:</span> $
									{request.listing.pricing_settings.hourly_rate}/hr
								</div>
							)}
							{request.listing.pricing_settings.dispatch_fee && (
								<div className="text-sm">
									<span className="font-medium">Dispatch Fee:</span> $
									{request.listing.pricing_settings.dispatch_fee}
								</div>
							)}
						</div>
					</div>
				)}
				<div>
					<div className="text-xs font-semibold text-gray-600 pt-2">
						Affected Component
					</div>
					{request.component?.type || request.component?.manufacturer ? (
						<div>{`${request.component.type} (${request.component.manufacturer})`}</div>
					) : (
						<div>No component information specified</div>
					)}
					{request.requires_return && (
						<div className="text-xs font-semibold text-gray-600 pt-2">
							NOTE: Requires return of original component
						</div>
					)}
				</div>
			</div>
			<div className="pt-2 space-y-2 flex-1 overflow-y-auto">
				{request.attachments?.length > 0 && (
					<div className="mt-2">
						<div className="text-xs font-semibold text-gray-600 mb-2">
							Attachments
						</div>
						<div className="grid grid-cols-1 gap-2">
							{request.attachments
								.sort((a, b) => (b.required ? 1 : 0) - (a.required ? 1 : 0))
								.map((attachment, index) => (
									<div key={index} className="flex items-center gap-2">
										<a
											href={attachment.url}
											target="_blank"
											rel="noopener noreferrer"
											className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1 underline"
										>
											<svg
												className="w-4 h-4"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth={2}
													d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
												/>
											</svg>
											{attachment.title}
											{attachment.required && (
												<span className="text-xs text-red-600 ml-1">
													(Required)
												</span>
											)}
										</a>
									</div>
								))}
						</div>
					</div>
				)}
			</div>
		</div>
	);
}
