import { ExtendedWarrantyRequest } from '@/types/warranty';
import { MapPin } from 'lucide-react';

export function LocationSection({
    brandColor,
    request,
    onEdit,
}: {
    brandColor: string;
    request: ExtendedWarrantyRequest;
    onEdit?: () => void;
}) {
    return (
        <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
            <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2 font-bold text-md text-slate-700">
                    <div
                        className={`${brandColor ? '' : 'bg-primary'} rounded-full p-1`}
                        style={brandColor ? { backgroundColor: brandColor } : {}}
                    >
                        <MapPin className="w-4 h-4 text-white" />
                    </div>
                    Service Location
                </div>
                {onEdit && (
                    <button
                        type="button"
                        onClick={onEdit}
                        className="text-xs font-medium underline"
                        style={{ color: brandColor }}
                    >
                        Edit
                    </button>
                )}
            </div>
            <div className="text-sm">
                <div className="text-xs font-semibold text-gray-600">Address</div>
                <div>{request.location.address}</div>
            </div>
        </div>
    );
}
