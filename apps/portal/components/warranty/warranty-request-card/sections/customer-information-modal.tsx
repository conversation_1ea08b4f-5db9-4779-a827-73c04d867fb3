"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { EditModal } from "@/components/warranty/edit-modal";
import {
	CustomerInfoForm,
	CustomerInformationStepData,
	customerInfoSchema
} from "@/components/warranty/warranty-request-wizard/steps/customer-information-step";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";

interface CustomerInformationModalProps {
	open: boolean;
	onClose: () => void;
	request: ExtendedWarrantyRequest;
	onUpdate: (data: any) => Promise<any>;
	company: ExtendedCompany;
}

export function CustomerInformationModal({
	open,
	onClose,
	request,
	onUpdate,
	company
}: CustomerInformationModalProps) {
	const [loading, setLoading] = useState(false);
	const form = useForm<CustomerInformationStepData>({
		resolver: zodResolver(customerInfoSchema)
	});

	const onSubmit = async (data: any) => {
		setLoading(true);
		try {
			await onUpdate(data);
			onClose();
		} catch (error) {
			console.error("Failed to update customer info:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleSave = async (e: React.FormEvent) => {
		e.preventDefault();
		await form.handleSubmit(onSubmit)(e);
	};

	return (
		<EditModal
			open={open}
			onClose={onClose}
			onSave={handleSave}
			isSaving={loading}
			saveButtonText={loading ? "Saving..." : "Save Changes"}
			company={company}
		>
			<div className="flex flex-col">
				<div className="bg-gray-50 max-h-[60vh] overflow-y-auto">
					<div className="bg-white rounded-lg p-6 shadow-sm">
						<CustomerInfoForm form={form} company={company} request={request} />
					</div>
				</div>
			</div>
		</EditModal>
	);
}
