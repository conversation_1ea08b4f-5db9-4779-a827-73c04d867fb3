"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { EditModal } from "@/components/warranty/edit-modal";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import {
	AuthorizationSchema,
	AuthorizationStep
} from "../../warranty-request-wizard/steps/authorization-step";

interface AuthorizationModalProps {
	open: boolean;
	onClose: () => void;
	onUpdate: (data: any) => Promise<any>;
	company?: ExtendedCompany;
	request?: ExtendedWarrantyRequest;
}

export function AuthorizationModal({
	open,
	onClose,
	onUpdate,
	company,
	request
}: AuthorizationModalProps) {
	const [loading, setLoading] = useState(false);
	const form = useForm({
		resolver: zodResolver(AuthorizationSchema)
	});

	const onSubmit = async (data: any) => {
		setLoading(true);
		try {
			await onUpdate(data);
			onClose();
		} catch (error) {
			console.error("Failed to update RV info:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleSave = async (e: React.FormEvent) => {
		e.preventDefault();
		await form.handleSubmit(onSubmit)(e);
	};

	return (
		<EditModal
			open={open}
			onClose={onClose}
			onSave={handleSave}
			isSaving={loading}
			saveButtonText={loading ? "Saving..." : "Save Changes"}
			company={company}
		>
			<div className="flex flex-col">
				<div className="bg-gray-50 max-h-[60vh] overflow-y-auto">
					<div className="bg-white rounded-lg p-6 shadow-sm">
						<AuthorizationStep
							form={form}
							company={company}
							request={request}
						/>
					</div>
				</div>
			</div>
		</EditModal>
	);
}
