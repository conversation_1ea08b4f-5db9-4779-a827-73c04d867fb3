"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { CauseCorrectionForm } from "@/components/warranty/warranty-request-card/sections/cause-correction-form";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import { EditModal } from "../../edit-modal";
import { CauseCorrectionSchema } from "./cause-correction-form";

interface CauseCorrectionModalProps {
	open: boolean;
	onClose: () => void;
	onUpdate: (data: any) => Promise<any>;
	company?: ExtendedCompany;
	request?: ExtendedWarrantyRequest;
}

export function CauseCorrectionModal({
	open,
	onClose,
	onUpdate,
	company,
	request
}: CauseCorrectionModalProps) {
	const [loading, setLoading] = useState(false);
	const form = useForm({
		resolver: zodResolver(CauseCorrectionSchema)
	});

	const onSubmit = async (data: any) => {
		setLoading(true);
		try {
			await onUpdate(data);
			onClose();
		} catch (error) {
			console.error("Failed to update RV info:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleSave = async (e: React.FormEvent) => {
		e.preventDefault();
		await form.handleSubmit(onSubmit)(e);
	};

	return (
		<EditModal
			open={open}
			onClose={onClose}
			onSave={handleSave}
			isSaving={loading}
			saveButtonText={loading ? "Saving..." : "Save Changes"}
			company={company}
		>
			<div className="flex flex-col">
				<div className="bg-gray-50 max-h-[60vh] overflow-y-auto">
					<div className="bg-white rounded-lg p-6 shadow-sm">
						<CauseCorrectionForm form={form} request={request} />
					</div>
				</div>
			</div>
		</EditModal>
	);
}
