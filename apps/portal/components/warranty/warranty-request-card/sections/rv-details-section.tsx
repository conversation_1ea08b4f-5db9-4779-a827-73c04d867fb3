import { WarrantyRequest } from "@rvhelp/database";
import { Caravan } from "lucide-react";

export function RVDetailsSection({
	brandColor,
	request,
	onEdit
}: {
	brandColor: string;
	request: WarrantyRequest;
	onEdit?: () => void;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
			<div className="flex items-center justify-between mb-3">
				<div className="flex items-center gap-2 font-bold text-md text-slate-700">
					<div
						className={`${brandColor ? "" : "bg-primary"} rounded-full p-1`}
						style={brandColor ? { backgroundColor: brandColor } : {}}
					>
						<Caravan className="w-4 h-4 text-white" />
					</div>
					RV Details
				</div>
				{onEdit && (
					<button
						type="button"
						onClick={onEdit}
						className="text-xs font-medium underline"
						style={{ color: brandColor }}
					>
						Edit
					</button>
				)}
			</div>
			<div className="pb-2">
				<div className="text-xs font-semibold text-gray-600">
					Vehicle Identification Number (VIN)
				</div>
				<div>{request.rv_vin}</div>
			</div>
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-2 text-sm">
				<div>
					<div className="text-xs font-semibold text-gray-600">Make</div>
					<div>{request.rv_make}</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Model</div>
					<div>{request.rv_model}</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Year</div>
					<div>{request.rv_year}</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Type</div>
					<div>{request.rv_type}</div>
				</div>
			</div>
		</div>
	);
}
