import { Listing } from "@rvhelp/database";
import { HardHat } from "lucide-react";

export function ProviderInformationSection({
	brandColor,
	listing
}: {
	brandColor: string;
	listing: Partial<Listing>;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
			<div>
				<div className="flex items-center justify-between mb-3">
					<div className="flex items-center gap-2 font-bold text-md text-slate-700">
						<div
							className="rounded-full p-1"
							style={{ backgroundColor: brandColor }}
						>
							<HardHat className="w-4 h-4 text-white" />
						</div>
						Technician Information
					</div>
				</div>
				{listing ? (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2 text-sm">
						{listing.business_name && (
							<div>
								<div className="text-xs font-semibold text-gray-600">
									Business Name
								</div>
								<div>{listing.business_name}</div>
							</div>
						)}
						<div>
							<div className="text-xs font-semibold text-gray-600">Name</div>
							<div>
								{listing.first_name} {listing.last_name}
							</div>
						</div>
						{listing.email && (
							<div>
								<div className="text-xs font-semibold text-gray-600">Email</div>
								<div>{listing.email}</div>
							</div>
						)}
						{listing.phone && (
							<div>
								<div className="text-xs font-semibold text-gray-600">Phone</div>
								<div>{listing.phone}</div>
							</div>
						)}
					</div>
				) : (
					<div className="text-center py-8 text-gray-500">
						No technician assigned yet
					</div>
				)}
			</div>
		</div>
	);
}
