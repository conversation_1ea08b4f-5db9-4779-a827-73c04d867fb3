"use client";

import { Dialog, DialogContent } from "@/components/ui/dialog";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import { User } from "@rvhelp/database";
import WarrantyRequestCard from "./warranty-request-card";

interface WarrantyRequestCardDialogProps {
	open: boolean;
	onClose: () => void;
	company: ExtendedCompany;
	request: ExtendedWarrantyRequest;
	user?: User;
	onStatusUpdated?: () => void;
}

export default function WarrantyRequestCardDialog({
	open,
	onClose,
	company,
	request,
	user,
	onStatusUpdated
}: WarrantyRequestCardDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="max-w-6xl max-h-[90vh] p-0">
				<div className="h-full">
					<WarrantyRequestCard
						company={company}
						request={request}
						user={user}
						onStatusUpdated={onStatusUpdated}
						onClose={onClose}
					/>
				</div>
			</DialogContent>
		</Dialog>
	);
}
