"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { lightenColor } from "@/lib/utils";
import { useState, useMemo } from "react";

import { AttachmentSection } from "./sections/attachment-section";
import { AuthorizationSection } from "./sections/authorization-section";
import { CauseCorrectionSection } from "./sections/cause-correction-section";
import { CustomerInformationSection } from "./sections/customer-information-section";
import { IssueDescriptionSection } from "./sections/issue-description-section";
import { LocationSection } from "./sections/location-section";
import { ProviderInformationSection } from "./sections/provider-information-section";
import { RVDetailsSection } from "./sections/rv-details-section";
import { StatusHistorySection } from "./sections/status-history-section";

import { ApprovalModal } from "@/components/warranty/approval-modal";
import { useComponentManager } from "@/components/warranty/component-manager/use-component-manager";
import { InvoicePaymentModal } from "@/components/warranty/invoice-payment-modal";
import { MarkAsResolvedModal } from "@/components/warranty/mark-as-resolved-modal";
import { CustomerInformationModal } from "@/components/warranty/warranty-request-card/sections/customer-information-modal";
import { IssueDescriptionModal } from "@/components/warranty/warranty-request-card/sections/issue-description-modal";
import { LocationModal } from "@/components/warranty/warranty-request-card/sections/location-modal";
import { RVDetailsModal } from "@/components/warranty/warranty-request-card/sections/rv-details-modal";

import type { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
// import { User as PrismaUser } from "@rvhelp/database";
import {
	formatDate,
	getStatusColor,
	getWarrantyRequestState,
	isPendingApproval
} from "../warranty-utils";
import { AuthorizationModal } from "./sections/authorization-modal";
import { CauseCorrectionModal } from "./sections/cause-correct-modal";

interface WarrantyRequestCardProps {
	company: ExtendedCompany;
	request: ExtendedWarrantyRequest;
	user?: any;
	onStatusUpdated?: () => void;
	onClose?: () => void;
}

// Modal components have been moved to their own files in the modals directory

export default function WarrantyRequestCard({
	company,
	request,
	user,
	onStatusUpdated,
	onClose
}: WarrantyRequestCardProps) {
	const [refreshKey, setRefreshKey] = useState(0);
	const [currentRequest, setCurrentRequest] = useState(request);
	const [currentCompany, setCurrentCompany] = useState(company);

	const [customerInformationModalOpen, setCustomerInformationModalOpen] =
		useState(false);
	const [rvDetailsModalOpen, setRvDetailsModalOpen] = useState(false);
	const [locationModalOpen, setLocationModalOpen] = useState(false);
	const [issueDescriptionModalOpen, setIssueDescriptionModalOpen] =
		useState(false);
	const [authorizationModalOpen, setAuthorizationModalOpen] = useState(false);
	const [causeCorrectionModalOpen, setCauseCorrectionModalOpen] =
		useState(false);

	const [approvalModalOpen, setApprovalModalOpen] = useState(false);
	const [invoicePaymentModalOpen, setInvoicePaymentModalOpen] = useState(false);
	const [markAsResolvedModalOpen, setMarkAsResolvedModalOpen] = useState(false);

	// Component management hook
	const componentManager = useComponentManager({
		company: currentCompany,
		onComponentsChange: (updatedComponents) => {
			setCurrentCompany({ ...currentCompany, components: updatedComponents });
		},
		onComponentCreated: (component) => {
			setCurrentRequest((prev) => ({
				...prev,
				component: component,
				component_id: component.id
			}));
		}
	});

	const handleStatusUpdated = () => {
		setRefreshKey((k) => k + 1);
		if (onStatusUpdated) onStatusUpdated();
	};

	const handleStatusClick = (e: React.MouseEvent) => {
		e.stopPropagation();
		if (currentRequest.status === "AUTHORIZATION_REQUESTED") {
			setApprovalModalOpen(true);
		} else if (currentRequest.status === "INVOICE_CREATED") {
			setInvoicePaymentModalOpen(true);
		}
	};

	const isJobCancellable = useMemo(() => {
		return (
			currentRequest.status === "REQUEST_CREATED" ||
			currentRequest.status === "REQUEST_APPROVED" ||
			currentRequest.status === "REQUEST_REJECTED" ||
			currentRequest.status === "JOB_REGISTERED" ||
			currentRequest.status === "JOB_REQUESTED" ||
			currentRequest.status === "JOB_ACCEPTED" ||
			currentRequest.status === "JOB_STARTED"
		);
	}, [currentRequest.status]);

	// Individual edit handlers
	const handleUpdateSection = async (sectionData: any) => {
		try {
			const updatedRequest = {
				...currentRequest,
				...sectionData
			};

			if (sectionData.component_id) {
				const component = currentCompany.components.find(
					(c) => c.id === sectionData.component_id
				);
				if (!component) {
					throw new Error("Component not found");
				}
				updatedRequest.component = component;
			}

			const res = await fetch(`/api/warranty-requests/${currentRequest.id}`, {
				method: "PUT",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(updatedRequest)
			});

			if (!res.ok) {
				const data = await res.json();
				throw new Error(data.error || "Failed to update request");
			}

			const updated = await res.json();
			setCurrentRequest(updated);
			setRefreshKey((k) => k + 1);
			if (onStatusUpdated) onStatusUpdated();

			return updated;
		} catch (error) {
			console.error("Failed to update section:", error);
			throw error;
		}
	};

	return (
		<>
			<Card
				className="transition-shadow hover:shadow-lg overflow-hidden h-full flex flex-col"
				key={refreshKey}
			>
				<div
					className="text-white p-4 flex-shrink-0"
					style={{ backgroundColor: company.brand_color }}
				>
					<div className="flex items-center justify-between">
						<div className="flex flex-col">
							<div className="flex items-center gap-2 mb-2">
								<div className="bg-white/20 rounded-full p-1">
									<svg
										className="w-4 h-4"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path
											fillRule="evenodd"
											d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1z"
											clipRule="evenodd"
										/>
									</svg>
								</div>
								<span className="text-md font-bold opacity-90">
									VIN: {currentRequest.rv_vin}
								</span>
							</div>
							<span className="text-sm opacity-90 mb-2">
								Submitted by{" "}
								<span className="font-medium">
									{currentRequest.oem_user?.first_name}{" "}
									{currentRequest.oem_user?.last_name}
								</span>{" "}
								on {formatDate(currentRequest.created_at)}
							</span>

							<div className="flex flex-wrap gap-x-4 gap-y-1">
								{currentRequest.rv_make && (
									<span className="text-sm bg-white/10 px-2 py-1 rounded">
										Make: {currentRequest.rv_make}
									</span>
								)}
								{currentRequest.rv_model && (
									<span className="text-sm bg-white/10 px-2 py-1 rounded">
										Model: {currentRequest.rv_model}
									</span>
								)}
								{currentRequest.rv_year && (
									<span className="text-sm bg-white/10 px-2 py-1 rounded">
										Year: {currentRequest.rv_year}
									</span>
								)}
								{currentRequest.approved_hours && (
									<span className="text-sm bg-white/10 px-2 py-1 rounded">
										Approved Hours: {currentRequest.approved_hours}
									</span>
								)}
								{currentRequest.estimated_hours && (
									<span className="text-sm bg-white/10 px-2 py-1 rounded">
										Estimated Hours: {currentRequest.estimated_hours}
									</span>
								)}
							</div>
						</div>
						<span
							className={`ml-2 px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(currentRequest.status)} shadow-sm`}
						>
							{getWarrantyRequestState(currentRequest).state}
						</span>
					</div>
				</div>

				<CardContent className="p-4 flex-1 flex flex-col overflow-hidden">
					<Tabs
						defaultValue="customer-rv"
						className="w-full flex-1 flex flex-col min-h-0"
					>
						<TabsList
							className="grid w-full grid-cols-4 mb-4 flex-shrink-0 text-black"
							style={{
								backgroundColor: lightenColor(company.brand_color, 0.85)
							}}
						>
							<TabsTrigger value="customer-rv">Customer & Provider</TabsTrigger>
							<TabsTrigger value="issue">Service Details</TabsTrigger>
							<TabsTrigger value="attachments">Attachments</TabsTrigger>
							<TabsTrigger value="status">History</TabsTrigger>
						</TabsList>

						<TabsContent
							value="customer-rv"
							className="space-y-4 flex-1 overflow-y-auto min-h-96 h-96"
						>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<CustomerInformationSection
									brandColor={company.brand_color}
									request={currentRequest}
									onEdit={() => setCustomerInformationModalOpen(true)}
								/>
								<LocationSection
									brandColor={company.brand_color}
									request={currentRequest}
									onEdit={() => setLocationModalOpen(true)}
								/>
								<RVDetailsSection
									brandColor={company.brand_color}
									request={currentRequest}
									onEdit={() => setRvDetailsModalOpen(true)}
								/>
								<ProviderInformationSection
									brandColor={company.brand_color}
									listing={currentRequest.listing}
								/>
							</div>
						</TabsContent>

						<TabsContent
							value="issue"
							className="space-y-4 flex-1 overflow-y-auto min-h-96 h-96"
						>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
								<div className="flex flex-col gap-4 h-full">
									<IssueDescriptionSection
										brandColor={company.brand_color}
										request={currentRequest}
										onEdit={() => setIssueDescriptionModalOpen(true)}
									/>
									<AuthorizationSection
										brandColor={company.brand_color}
										request={currentRequest}
										onEdit={() => setAuthorizationModalOpen(true)}
									/>
								</div>
								<CauseCorrectionSection
									brandColor={company.brand_color}
									request={currentRequest}
									onEdit={() => setCauseCorrectionModalOpen(true)}
								/>
							</div>
						</TabsContent>

						<TabsContent
							value="attachments"
							className="space-y-4 flex-1 overflow-y-auto min-h-96 h-96"
						>
							<AttachmentSection request={currentRequest} />
						</TabsContent>

						<TabsContent
							value="status"
							className="space-y-4 flex-1 overflow-y-auto min-h-96 h-96"
						>
							<StatusHistorySection
								brandColor={company.brand_color}
								request={currentRequest}
							/>
						</TabsContent>
					</Tabs>
					<div>
						<span className="text-sm bg-white/10 px-2 py-1 rounded">
							ID: {currentRequest.uuid}
						</span>
					</div>
					<div className="flex justify-between gap-2 mt-4 flex-shrink-0">
						<div className="flex gap-2">
							{currentRequest.status === "INVOICE_CREATED" ? (
								<Button
									type="button"
									style={{
										backgroundColor: company.brand_color,
										color: "#fff"
									}}
									onClick={() => setInvoicePaymentModalOpen(true)}
									disabled={user?.role !== "ADMIN"}
								>
									Pay Invoice
								</Button>
							) : (
								<Button
									type="button"
									style={{
										backgroundColor: company.brand_color,
										color: "#fff"
									}}
									onClick={handleStatusClick}
									disabled={
										!isPendingApproval(currentRequest.status, user?.role)
									}
								>
									Update Status
								</Button>
							)}

							{/* Mark as Resolved button - only show if not already cancelled */}
							<Button
								type="button"
								variant="outline"
								disabled={!isJobCancellable}
								style={{
									color: "#dc2626",
									borderColor: "#dc2626"
								}}
								onClick={() => setMarkAsResolvedModalOpen(true)}
							>
								Mark as Resolved
							</Button>
						</div>

						<div className="flex gap-2">
							<Button
								variant="outline"
								type="button"
								style={{
									color: company.brand_color,
									borderColor: company.brand_color
								}}
								onClick={(e) => {
									e.stopPropagation();
									onClose?.();
								}}
							>
								Close
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Individual Edit Modals */}
			<CustomerInformationModal
				open={customerInformationModalOpen}
				onClose={() => setCustomerInformationModalOpen(false)}
				request={currentRequest}
				company={currentCompany}
				onUpdate={handleUpdateSection}
			/>

			<RVDetailsModal
				open={rvDetailsModalOpen}
				onClose={() => setRvDetailsModalOpen(false)}
				onUpdate={handleUpdateSection}
				company={currentCompany}
				request={currentRequest}
			/>

			<LocationModal
				open={locationModalOpen}
				onClose={() => setLocationModalOpen(false)}
				onUpdate={handleUpdateSection}
				company={currentCompany}
				request={currentRequest}
			/>

			<IssueDescriptionModal
				open={issueDescriptionModalOpen}
				onClose={() => setIssueDescriptionModalOpen(false)}
				company={currentCompany}
				request={currentRequest}
				onUpdate={handleUpdateSection}
				componentManager={componentManager}
			/>

			<CauseCorrectionModal
				open={causeCorrectionModalOpen}
				onClose={() => setCauseCorrectionModalOpen(false)}
				company={currentCompany}
				onUpdate={handleUpdateSection}
				request={currentRequest}
			/>

			<AuthorizationModal
				open={authorizationModalOpen}
				onClose={() => setAuthorizationModalOpen(false)}
				onUpdate={handleUpdateSection}
				company={currentCompany}
				request={currentRequest}
			/>

			<ApprovalModal
				open={approvalModalOpen}
				onClose={() => setApprovalModalOpen(false)}
				request={currentRequest}
				onStatusUpdated={handleStatusUpdated}
				company={currentCompany}
			/>

			<InvoicePaymentModal
				open={invoicePaymentModalOpen}
				onClose={() => setInvoicePaymentModalOpen(false)}
				request={currentRequest}
				onStatusUpdated={() => {
					setInvoicePaymentModalOpen(false);
					handleStatusUpdated();
				}}
				company={currentCompany}
			/>

			<MarkAsResolvedModal
				open={markAsResolvedModalOpen}
				onClose={() => setMarkAsResolvedModalOpen(false)}
				request={currentRequest}
				company={currentCompany}
				onStatusUpdated={handleStatusUpdated}
			/>
		</>
	);
}
