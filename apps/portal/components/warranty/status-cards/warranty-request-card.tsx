"use client";

import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { formatDistanceToNow } from "date-fns";
import {
    Calendar,
    Clock,
    User
} from "lucide-react";

interface WarrantyRequestCardProps {
    request: ExtendedWarrantyRequest;
    company: any;
    onView: (request: ExtendedWarrantyRequest) => void;
}

export default function WarrantyRequestCard({
    request,
    company,
    onView
}: WarrantyRequestCardProps) {
    // Get the last activity from timeline updates
    const lastActivity = request.timeline_updates && request.timeline_updates.length > 0
        ? request.timeline_updates[0]
        : null;

    return (
        <Card
            className="hover:shadow-md transition-shadow duration-200 border-l-4 cursor-pointer"
            style={{ borderLeftColor: company?.brand_color || "#2563eb" }}
            onClick={() => onView(request)}
        >
            <CardContent className="p-4">
                {/* Header with customer name and creation date */}
                <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                        <CardTitle className="text-base font-semibold text-gray-900 truncate">
                            {request.first_name} {request.last_name}
                        </CardTitle>
                        {request.approved_hours && (
                            <div className="flex items-center gap-1 mt-1">
                                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                    {request.approved_hours}h approved
                                </span>
                            </div>
                        )}
                    </div>
                    <div className="text-right text-xs text-gray-500 ml-2">
                        <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {formatDistanceToNow(new Date(request.created_at), { addSuffix: true })}
                        </div>
                    </div>
                </div>

                {/* Issue Description */}
                <div className="mb-3">
                    <p className="text-sm text-gray-700 line-clamp-2">
                        {request.complaint}
                    </p>
                </div>


                {/* Contact Information */}
                <div className="mb-3">
                    <div className="flex items-center gap-1 text-xs text-gray-600 mb-1">
                        <User className="w-3 h-3" />
                        <span className="font-medium">Contact:</span>
                    </div>
                    <p className="text-xs text-gray-700 truncate">{request.email}</p>
                </div>

                {/* Last Activity */}
                {lastActivity && (
                    <div className="pt-2 border-t border-gray-100">
                        <div className="flex items-center gap-1 text-xs text-gray-600 mb-1">
                            <Clock className="w-3 h-3" />
                            <span className="font-medium">Last Activity:</span>
                        </div>
                        <div className="flex items-center justify-between">
                            <p className="text-xs text-gray-700">
                                {lastActivity.event_type?.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                            </p>
                            <p className="text-xs text-gray-500">
                                {formatDistanceToNow(new Date(lastActivity.date), { addSuffix: true })}
                            </p>
                        </div>
                        {lastActivity.updated_by && (
                            <p className="text-xs text-gray-500 mt-1">
                                by {lastActivity.updated_by.first_name} {lastActivity.updated_by.last_name}
                            </p>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
