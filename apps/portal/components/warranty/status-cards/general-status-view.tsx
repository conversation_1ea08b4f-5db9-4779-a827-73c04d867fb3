"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import {
    AlertCircle,
    CheckCircle,
    CreditCard,
    Eye,
    FileText,
    Package,
    RefreshCw,
    Wrench,
    XCircle
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import WarrantyRequestCardDialog from "../warranty-request-card/warranty-request-card-dialog";
import WarrantyRequestCard from "./warranty-request-card";

interface GeneralStatusViewProps {
    company: any;
    user: any;
}

const getStatusInfo = (status: string) => {
    const statusMap: { [key: string]: { label: string; color: string; icon: any; bgColor: string; description: string } } = {
        REQUEST_CREATED: {
            label: "New Requests",
            color: "text-blue-600",
            icon: FileText,
            bgColor: "bg-blue-50",
            description: "New warranty requests awaiting review"
        },
        REQUEST_APPROVED: {
            label: "Pre-approved",
            color: "text-green-600",
            icon: CheckCircle,
            bgColor: "bg-green-50",
            description: "Requests that have been pre-approved"
        },
        REQUEST_REJECTED: {
            label: "Rejected",
            color: "text-red-600",
            icon: XCircle,
            bgColor: "bg-red-50",
            description: "Requests that have been rejected"
        },
        JOB_REGISTERED: {
            label: "Job Registered",
            color: "text-blue-600",
            icon: FileText,
            bgColor: "bg-blue-50",
            description: "Jobs that have been registered"
        },
        JOB_REQUESTED: {
            label: "Provider Invited",
            color: "text-amber-600",
            icon: AlertCircle,
            bgColor: "bg-amber-50",
            description: "Jobs where providers have been invited"
        },
        JOB_ACCEPTED: {
            label: "Job Accepted",
            color: "text-green-600",
            icon: CheckCircle,
            bgColor: "bg-green-50",
            description: "Jobs that have been accepted"
        },
        JOB_STARTED: {
            label: "In Progress",
            color: "text-blue-600",
            icon: Wrench,
            bgColor: "bg-blue-50",
            description: "Jobs currently in progress"
        },
        JOB_COMPLETED: {
            label: "Completed",
            color: "text-green-600",
            icon: CheckCircle,
            bgColor: "bg-green-50",
            description: "Jobs that have been completed"
        },
        AUTHORIZATION_REQUESTED: {
            label: "Auth Required",
            color: "text-amber-600",
            icon: AlertCircle,
            bgColor: "bg-amber-50",
            description: "Requests requiring authorization"
        },
        AUTHORIZATION_APPROVED: {
            label: "Auth Approved",
            color: "text-green-600",
            icon: CheckCircle,
            bgColor: "bg-green-50",
            description: "Authorizations that have been approved"
        },
        AUTHORIZATION_REJECTED: {
            label: "Auth Rejected",
            color: "text-red-600",
            icon: XCircle,
            bgColor: "bg-red-50",
            description: "Authorizations that have been rejected"
        },
        AUTHORIZATION_FEEDBACK: {
            label: "Auth Feedback",
            color: "text-amber-600",
            icon: AlertCircle,
            bgColor: "bg-amber-50",
            description: "Authorizations requiring feedback"
        },
        PARTS_ORDERED: {
            label: "Parts Ordered",
            color: "text-blue-600",
            icon: Package,
            bgColor: "bg-blue-50",
            description: "Parts that have been ordered"
        },
        INVOICE_CREATED: {
            label: "Invoice Created",
            color: "text-amber-600",
            icon: CreditCard,
            bgColor: "bg-amber-50",
            description: "Invoices that have been created"
        },
        INVOICE_PAID: {
            label: "Invoice Paid",
            color: "text-green-600",
            icon: CheckCircle,
            bgColor: "bg-green-50",
            description: "Invoices that have been paid"
        },
        JOB_CANCELLED: {
            label: "Cancelled",
            color: "text-red-600",
            icon: XCircle,
            bgColor: "bg-red-50",
            description: "Jobs that have been cancelled"
        },
    };

    return statusMap[status] || {
        label: status,
        color: "text-gray-600",
        icon: FileText,
        bgColor: "bg-gray-50",
        description: "Requests in this status"
    };
};

export default function GeneralStatusView({ company, user }: GeneralStatusViewProps) {
    const [requests, setRequests] = useState<ExtendedWarrantyRequest[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [hideCompleted, setHideCompleted] = useState<boolean>(false);
    const [selectedRequest, setSelectedRequest] = useState<ExtendedWarrantyRequest | null>(null);
    const [viewModalOpen, setViewModalOpen] = useState(false);

    const fetchRequests = useCallback(async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams({
                hideCompleted: hideCompleted.toString(),
                pageSize: '100' // Get more requests for better overview
            });
            const res = await fetch(`/api/warranty-requests-ex/all?${params.toString()}`);
            if (!res.ok) throw new Error("Failed to fetch");
            const data = await res.json();
            setRequests(data.requests || []);
        } catch (err) {
            console.error("Failed to fetch requests:", err);
            setRequests([]);
        } finally {
            setLoading(false);
        }
    }, [hideCompleted]);

    useEffect(() => {
        fetchRequests();
    }, [fetchRequests]);

    // Group requests by status
    const groupedRequests = requests.reduce((acc, request) => {
        const status = request.status;
        if (!acc[status]) {
            acc[status] = [];
        }
        acc[status].push(request);
        return acc;
    }, {} as { [key: string]: ExtendedWarrantyRequest[] });

    // Sort statuses according to the WarrantyRequestStatus enum order
    const enumOrder = [
        'REQUEST_CREATED',
        'REQUEST_APPROVED',
        'REQUEST_REJECTED',
        'JOB_REGISTERED',
        'JOB_REQUESTED',
        'JOB_ACCEPTED',
        'JOB_STARTED',
        'JOB_COMPLETED',
        'AUTHORIZATION_REQUESTED',
        'AUTHORIZATION_APPROVED',
        'AUTHORIZATION_REJECTED',
        'AUTHORIZATION_FEEDBACK',
        'PARTS_ORDERED',
        'INVOICE_CREATED',
        'INVOICE_PAID',
        'JOB_CANCELLED',
    ];

    const sortedStatuses = Object.keys(groupedRequests).sort((a, b) => {
        const aIndex = enumOrder.indexOf(a);
        const bIndex = enumOrder.indexOf(b);
        return aIndex - bIndex;
    });

    // Handle view action
    const handleView = (request: ExtendedWarrantyRequest) => {
        setSelectedRequest(request);
        setViewModalOpen(true);
    };



    // Handle status updated callback
    const handleStatusUpdated = () => {
        fetchRequests();
    };

    if (loading) {
        return (
            <div className="space-y-6">
                {/* Header Skeleton */}
                <div className="flex items-center justify-between">
                    <Skeleton className="h-8 w-64" />
                    <Skeleton className="h-6 w-32" />
                </div>

                {/* Status Sections Skeleton - Horizontal Layout */}
                <div className="overflow-x-auto pb-4">
                    <div className="flex gap-6 min-w-max">
                        {Array.from({ length: 4 }).map((_, i) => (
                            <div key={i} className="w-[400px] flex-shrink-0">
                                <Card className="h-full">
                                    <CardHeader className="pb-3">
                                        <Skeleton className="h-6 w-48" />
                                        <Skeleton className="h-4 w-64" />
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <div className="space-y-4">
                                            {Array.from({ length: 3 }).map((_, j) => (
                                                <Skeleton key={j} className="h-48 w-full" />
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Service Request Status Overview</h1>
                    <p className="text-gray-600">View all warranty requests organized by status</p>
                </div>
                <div className="flex items-center gap-4">
                    <div className="flex items-center space-x-2">
                        <Switch
                            id="hide-completed"
                            checked={hideCompleted}
                            onCheckedChange={setHideCompleted}
                        />
                        <Label htmlFor="hide-completed" className="text-sm font-medium">
                            Hide Completed
                        </Label>
                    </div>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchRequests}
                        style={{
                            borderColor: company?.brand_color || "#2563eb",
                            color: company?.brand_color || "#2563eb"
                        }}
                    >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Refresh
                    </Button>
                </div>
            </div>



            {/* Status Sections - Horizontal Trello Layout */}
            <div className="overflow-x-auto pb-4">
                <div className="flex gap-6 min-w-max">
                    {sortedStatuses.map((status) => {
                        const statusRequests = groupedRequests[status];
                        const statusInfo = getStatusInfo(status);
                        const StatusIcon = statusInfo.icon;

                        return (
                            <div key={status} className="w-[400px] flex-shrink-0">
                                <Card className="h-full">
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center gap-3">
                                            <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full ${statusInfo.bgColor}`}>
                                                <StatusIcon className={`w-5 h-5 ${statusInfo.color}`} />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <CardTitle className="text-lg font-semibold truncate">
                                                    {statusInfo.label} ({statusRequests.length})
                                                </CardTitle>
                                                <p className="text-sm text-gray-600 truncate">{statusInfo.description}</p>
                                            </div>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        {statusRequests.length === 0 ? (
                                            <div className="text-center py-8 text-gray-500">
                                                No requests in this status
                                            </div>
                                        ) : (
                                            <div className="space-y-4">
                                                {statusRequests.map((request) => (
                                                    <WarrantyRequestCard
                                                        key={request.id}
                                                        request={request}
                                                        company={company}
                                                        onView={handleView}
                                                    />
                                                ))}
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>
                        );
                    })}
                </div>
            </div>

            {/* Empty State */}
            {requests.length === 0 && !loading && (
                <Card>
                    <CardContent className="text-center py-12">
                        <Eye className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                            No service requests found
                        </h3>
                        <p className="text-gray-600">
                            {hideCompleted
                                ? "No active service requests found. Try unchecking 'Hide Completed' to see all requests."
                                : "No service requests have been created yet."
                            }
                        </p>
                    </CardContent>
                </Card>
            )}

            {/* Modals */}
            {selectedRequest && (
                <WarrantyRequestCardDialog
                    open={viewModalOpen}
                    onClose={() => setViewModalOpen(false)}
                    company={company}
                    request={selectedRequest}
                    onStatusUpdated={handleStatusUpdated}
                    user={user}
                />
            )}
        </div>
    );
}
