const nextJest = require("next/jest");

const createJestConfig = nextJest({
	dir: "./"
});

const customJestConfig = {
	testEnvironment: "jest-environment-node",
	transformIgnorePatterns: [
		"node_modules/(?!(@auth/prisma-adapter|@auth/core)/)"
	],
	transform: {
		"^.+\\.(t|j)sx?$": ["@swc/jest"]
	},
	moduleNameMapper: {
		"^@/(.*)$": "<rootDir>/$1"
	},
	watchman: false
};

module.exports = createJestConfig(customJestConfig);
