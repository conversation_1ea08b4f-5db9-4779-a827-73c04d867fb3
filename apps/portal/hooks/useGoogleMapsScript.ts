'use client';

import config from '@/config';
import { Loader } from '@googlemaps/js-api-loader';
import { useEffect, useState } from 'react';

export function useGoogleMapsScript() {
    const [isLoaded, setIsLoaded] = useState(false);
    const [loadError, setLoadError] = useState<Error | null>(null);

    useEffect(() => {
        console.log("config.google.apiKey", config.google.apiKey);
        const loader = new Loader({
            apiKey: config.google.apiKey,
            version: 'weekly',
            libraries: ['places'],
        });

        loader
            .load()
            .then(() => {
                setIsLoaded(true);
            })
            .catch((error) => {
                setLoadError(error);
            });
    }, [config.google.apiKey]);

    return { isLoaded, loadError };
}
