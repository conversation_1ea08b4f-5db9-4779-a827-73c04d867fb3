import { useEffect, useState } from 'react';

const MOBILE_BREAKPOINT = 768;

export function useIsMobile() {
    const isMobileInitial = typeof window !== 'undefined' && window.innerWidth < 768;

    const [isMobile, setIsMobile] = useState<boolean | undefined>(isMobileInitial);

    useEffect(() => {
        const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);

        const onChange = () => {
            setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
        };

        // Set initial value
        onChange();

        // Add event listener
        mql.addEventListener('change', onChange);

        // Cleanup
        return () => mql.removeEventListener('change', onChange);
    }, []);

    return !!isMobile;
}
