# Sentry Integration Setup

This document outlines the Sentry integration setup for the RVHelp Next.js application.

## What's Been Configured

### 1. Dependencies

- `@sentry/nextjs` package installed

### 2. Configuration Files

- `sentry.server.config.ts` - Server-side Sentry configuration
- `sentry.edge.config.ts` - Edge runtime Sentry configuration
- `instrumentation-client.ts` - Client-side Sentry configuration
- `instrumentation.ts` - Next.js instrumentation for Sentry

### 3. Integration Points

- **Global Error Handling**: Updated `app/global-error.tsx` to capture unhandled errors
- **Layout Integration**: Added Sentry trace data to `app/layout.tsx` metadata
- **Next.js Config**: Updated `next.config.js` with Sentry webpack plugin

## Features Enabled

### Error Tracking

- Automatic capture of unhandled JavaScript errors
- Manual error reporting via `Sentry.captureException()`
- Custom message capture via `Sentry.captureMessage()`

### Performance Monitoring

- Automatic page load performance tracking
- Navigation performance monitoring
- Transaction sampling rate: 10% (optimized for performance)

### Logging

- Application logs sent to Sentry
- Structured logging support

## Environment Variables

The following environment variables are automatically configured by Sentry:

- `SENTRY_DSN` - Your Sentry project DSN
- `SENTRY_ORG` - Your Sentry organization
- `SENTRY_PROJECT` - Your Sentry project name

## Testing the Integration

Visit `/sentry-test` to test the Sentry integration:

1. **Error Capture**: Click "Test Error Capture" to send a test error
2. **Message Capture**: Click "Test Message Capture" to send a test message
3. **Performance**: Performance tracking is automatically enabled

## Production Configuration

### Build Performance Optimizations

- **Development builds**: Sentry is completely disabled to avoid build time impact
- **Production builds**: Optimized configuration for faster builds
- **Source maps**: Limited upload scope for faster builds (can be expanded if needed)

### Source Maps

Source maps are automatically uploaded during build for better stack traces (with reduced scope for faster builds).

### Sampling Rates

- Error sampling: 100% (all errors captured)
- Performance sampling: 10% (optimized for build performance)

### Environment Detection

Sentry automatically detects the environment based on your deployment platform.

## Vercel Integration

If deploying to Vercel, consider setting up the Sentry Vercel integration for:

- Automatic auth token management
- Better deployment tracking
- Enhanced performance monitoring

## Monitoring Dashboard

Access your Sentry dashboard at: https://sentry.io/organizations/rv-help/projects/javascript-nextjs/

## Customization

### Adjusting Sampling Rates

Edit the configuration files to adjust sampling rates:

```typescript
// In sentry.server.config.ts and sentry.edge.config.ts
tracesSampleRate: 0.1, // 10% of transactions
```

### Adding Custom Context

Add user context or custom tags:

```typescript
Sentry.setUser({ id: "user123", email: "<EMAIL>" });
Sentry.setTag("feature", "checkout");
```

### Filtering Events

Configure beforeSend to filter events:

```typescript
beforeSend(event) {
  // Filter out specific errors
  if (event.exception && event.exception.values) {
    const exception = event.exception.values[0];
    if (exception.value && exception.value.includes('NetworkError')) {
      return null;
    }
  }
  return event;
}
```

## Troubleshooting

### Common Issues

1. **Source maps not working**: Ensure `widenClientFileUpload: true` in next.config.js
2. **No events appearing**: Check DSN configuration and network connectivity
3. **Performance data missing**: Verify `tracesSampleRate` is > 0

### Debug Mode

Enable debug mode temporarily to see Sentry initialization logs:

```typescript
debug: true;
```

## Security Considerations

- DSN is safe to expose in client-side code
- Source maps are uploaded to Sentry's secure servers
- No sensitive data is automatically captured
- Review and configure data scrubbing as needed

## Support

For Sentry-specific issues, refer to:

- [Sentry Next.js Documentation](https://docs.sentry.io/platforms/javascript/guides/nextjs/)
- [Sentry JavaScript SDK Documentation](https://docs.sentry.io/platforms/javascript/)
