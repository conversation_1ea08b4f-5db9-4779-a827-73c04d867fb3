import { sanitizeShortDescription } from "@/lib/utils";
import { Article, Listing, Location } from "@rvhelp/database";
import { MapPin } from "lucide-react";
import Link from "next/link";

interface FeaturedProviderSpotlightProps {
	article: Article;
	provider: (Listing & { location: Location | null }) | null;
}

export default function FeaturedProviderSpotlight({
	article,
	provider
}: FeaturedProviderSpotlightProps) {
	// Parse the article content to extract provider info (this is a simplified approach)
	// In a real implementation, you might want to structure this data differently
	const providerName = provider?.business_name || "Featured Provider";

	const certification = "NRVIA Certified Inspector";

	const contentExcerpt =
		sanitizeShortDescription(article.content.slice(0, 250)) + "...";

	return (
		<section className="py-12 bg-gradient-to-b from-[#43806c] to-[#5a9a7f]">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				{/* Header */}
				<div className="text-center mb-8">
					<h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
						Service Provider Spotlight
					</h2>
					<p className="text-lg text-white/90 max-w-2xl mx-auto">
						Real stories from the dedicated professionals who go above and
						beyond
					</p>
				</div>

				{/* Main Content */}
				<div className="bg-white rounded-2xl shadow-xl overflow-hidden max-w-5xl mx-auto">
					<div className="grid grid-cols-1 lg:grid-cols-5 gap-8 p-8">
						{/* Left Side - Provider Image and Stats */}
						<div className="flex flex-col items-center lg:items-start col-span-2">
							{/* Provider Image */}
							<div className="relative mb-6">
								{article.meta_image ? (
									<img
										src={article.meta_image}
										alt={providerName}
										className="w-full max-w-sm h-64 rounded-lg object-cover shadow-lg"
									/>
								) : (
									<div className="w-full max-w-sm h-64 rounded-lg bg-gray-200 flex items-center justify-center shadow-lg">
										<span className="text-gray-500 text-lg">
											Provider Photo
										</span>
									</div>
								)}

								{/* Certification Badge */}
								<div className="absolute -bottom-2 -right-2">
									<span className="bg-yellow-400 text-black text-sm font-semibold px-4 py-2 rounded-full shadow-lg flex-shrink-0">
										{certification}
									</span>
								</div>
							</div>
						</div>

						{/* Right Side - Content */}
						<div className="flex flex-col justify-center col-span-3">
							<h3 className="text-3xl font-bold text-gray-900 mb-2">
								{article.title}
							</h3>

							{/* Certification Details */}
							<div className="text-sm text-gray-600 mb-2">
								NRVIA Certified Technical Trainer Veteran-Owned
							</div>

							{provider && (
								<div className="flex items-center text-gray-600 mb-6">
									<MapPin className="w-5 h-5 mr-2 text-red-500" />
									{provider.location?.city}, {provider.location?.state}
								</div>
							)}

							{/* Story excerpt with proper spacing */}
							<div className="text-gray-700 mb-6 leading-relaxed">
								{contentExcerpt}
							</div>

							{/* CTA Button */}
							<Link
								href={`/blog/${article.slug}`}
								className="inline-flex items-center justify-center bg-[#43806c] text-white font-semibold px-8 py-3 rounded-lg hover:bg-[#376356] transition-colors duration-200 self-start"
							>
								Read {providerName}'s Story
							</Link>
						</div>
					</div>
				</div>

				{/* Footer CTA */}
				<div className="text-center mt-8">
					<p className="text-white/90 text-lg mb-4">
						Know a provider with an amazing story?
					</p>
					<Link
						href="/contact"
						className="text-white hover:text-white/80 underline inline-flex items-center gap-1 text-lg font-medium"
					>
						Nominate them for our next spotlight →
					</Link>
				</div>
			</div>
		</section>
	);
}
