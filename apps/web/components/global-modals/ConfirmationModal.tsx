import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialog<PERSON>ooter,
	AlertDialogHeader,
	AlertDialogTitle
} from "@/components/ui/alert-dialog";
import { AlertTriangle, CheckCircle, XCircle } from "lucide-react";

export interface ConfirmationModalProps {
	isOpen: boolean;
	onClose: () => void;
	onConfirm: () => void;
	title: string;
	description: string;
	confirmText: string;
	cancelText?: string;
	variant?: "default" | "destructive" | "success";
	isLoading?: boolean;
}

export function ConfirmationModal({
	isOpen,
	onClose,
	onConfirm,
	title,
	description,
	confirmText,
	cancelText = "Cancel",
	variant = "default",
	isLoading = false
}: ConfirmationModalProps) {
	const getIcon = () => {
		switch (variant) {
			case "success":
				return <CheckCircle className="h-6 w-6 text-green-600" />;
			case "destructive":
				return <XCircle className="h-6 w-6 text-red-600" />;
			default:
				return <AlertTriangle className="h-6 w-6 text-blue-600" />;
		}
	};

	const getConfirmButtonStyle = () => {
		switch (variant) {
			case "success":
				return "bg-green-600 hover:bg-green-700 text-white";
			case "destructive":
				return "bg-red-600 hover:bg-red-700 text-white";
			default:
				return "bg-blue-600 hover:bg-blue-700 text-white";
		}
	};

	return (
		<AlertDialog open={isOpen} onOpenChange={onClose}>
			<AlertDialogContent className="max-w-md">
				<AlertDialogHeader>
					<div className="flex items-center gap-3">
						{getIcon()}
						<AlertDialogTitle className="text-lg">{title}</AlertDialogTitle>
					</div>
					<AlertDialogDescription className="text-sm text-gray-600 mt-2">
						{description}
					</AlertDialogDescription>
				</AlertDialogHeader>
				<AlertDialogFooter className="flex gap-2">
					<AlertDialogCancel
						onClick={onClose}
						disabled={isLoading}
						className="flex-1"
					>
						{cancelText}
					</AlertDialogCancel>
					<AlertDialogAction
						onClick={onConfirm}
						disabled={isLoading}
						className={`flex-1 ${getConfirmButtonStyle()}`}
					>
						{isLoading ? "Processing..." : confirmText}
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}
