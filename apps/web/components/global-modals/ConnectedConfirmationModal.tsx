import { useModal } from "@/lib/context/modal-context";
import { ConfirmationModal } from "./ConfirmationModal";

export function ConnectedConfirmationModal() {
	const {
		isConfirmationModalOpen,
		closeConfirmationModal,
		confirmationModalConfig,
		isConfirmationLoading,
		setIsConfirmationLoading
	} = useModal();

	if (!confirmationModalConfig) return null;

	const handleConfirm = async () => {
		try {
			// Set loading state
			setIsConfirmationLoading(true);

			await confirmationModalConfig.onConfirm();
			closeConfirmationModal();
		} catch (error) {
			console.error("Error during confirmation:", error);
			// Keep modal open on error so user can try again
		} finally {
			// Reset loading state
			setIsConfirmationLoading(false);
		}
	};

	return (
		<ConfirmationModal
			isOpen={isConfirmationModalOpen}
			onClose={closeConfirmationModal}
			onConfirm={handleConfirm}
			title={confirmationModalConfig.title}
			description={confirmationModalConfig.description}
			confirmText={confirmationModalConfig.confirmText}
			cancelText={confirmationModalConfig.cancelText}
			variant={confirmationModalConfig.variant}
			isLoading={isConfirmationLoading}
		/>
	);
}
