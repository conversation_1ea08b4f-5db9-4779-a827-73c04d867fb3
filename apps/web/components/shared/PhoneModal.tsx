import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Phone } from "lucide-react";

interface PhoneModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  phoneNumber: string;
  name?: string;
}

export function PhoneModal({ open, onOpenChange, phoneNumber, name }: PhoneModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Contact Information</DialogTitle>
          <DialogDescription>
            {name ? `${name}'s phone number:` : "Phone number:"}
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center py-6">
          <a
            href={`tel:${phoneNumber}`}
            className="text-2xl font-semibold text-primary hover:text-primary/90 flex items-center gap-2"
          >
            <Phone className="h-6 w-6" />
            {phoneNumber}
          </a>
        </div>
        <DialogFooter className="flex justify-between sm:justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
          <Button
            type="button"
            onClick={() => window.location.href = `tel:${phoneNumber}`}
          >
            Call Now
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 