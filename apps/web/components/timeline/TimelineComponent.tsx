"use client";

import { Card, CardContent } from "@/components/ui/card";
import { TimelineEventType } from "@rvhelp/database";
import { formatDistanceToNow } from "date-fns";
import {
	AlertCircle,
	CheckCircle,
	Clock,
	MessageSquare,
	Star,
	Wrench,
	X
} from "lucide-react";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

interface TimelineUpdate {
	id: string;
	event_type: TimelineEventType;
	date: string;
	details?: {
		notes?: string;
		[key: string]: any;
	};
	updated_by: {
		first_name: string | null;
		last_name: string | null;
		email: string;
	};
}

interface TimelineComponentProps {
	jobId?: string;
	warrantyRequestId?: string;
	canAddUpdates?: boolean;
	showAddUpdate?: boolean;
}

const eventTypeLabels: Record<TimelineEventType, string> = {
	PREAUTHORIZATION_APPROVED: "Pre-authorization Approved",
	PREAUTHORIZATION_REJECTED: "Pre-authorization Rejected",
	CUSTOMER_REGISTERED: "Customer Registered",
	TECHNICIAN_INVITED: "Technician Invited",
	TECHNICIAN_ACCEPTED: "Technician Accepted",
	TECHNICIAN_REJECTED: "Technician Declined",
	TECHNICIAN_REQUESTED_INFO: "Information Requested",
	TECHNICIAN_WITHDRAWN: "Availability Withdrawn",
	CUSTOMER_ACCEPTED: "Customer Accepted",
	CUSTOMER_REJECTED: "Customer Rejected",
	AUTHORIZATION_REQUESTED: "Authorization Requested",
	AUTHORIZATION_APPROVED: "Authorization Approved",
	AUTHORIZATION_REJECTED: "Authorization Rejected",
	AUTHORIZATION_FEEDBACK: "Authorization Feedback",
	PARTS_ORDERED: "Parts Ordered",
	JOB_STARTED: "Job Started",
	JOB_COMPLETED: "Job Completed",
	JOB_CANCELLED: "Job Cancelled",
	JOB_PAUSED: "Job Paused",
	INVOICE_CREATED: "Invoice Created",
	INVOICE_PAID: "Invoice Paid",
	ISSUE_RESOLVED: "Issue Resolved",
	REVIEW_REQUESTED: "Review Requested",
	REVIEW_LEFT: "Review Left",
	TECHNICIAN_UPDATED: "Technician Updated"
};

const eventTypeIcons: Record<TimelineEventType, React.ComponentType<any>> = {
	PREAUTHORIZATION_APPROVED: CheckCircle,
	PREAUTHORIZATION_REJECTED: X,
	CUSTOMER_REGISTERED: CheckCircle,
	TECHNICIAN_INVITED: MessageSquare,
	TECHNICIAN_ACCEPTED: CheckCircle,
	TECHNICIAN_REJECTED: X,
	TECHNICIAN_REQUESTED_INFO: MessageSquare,
	TECHNICIAN_WITHDRAWN: X,
	CUSTOMER_ACCEPTED: CheckCircle,
	CUSTOMER_REJECTED: X,
	AUTHORIZATION_REQUESTED: Clock,
	AUTHORIZATION_APPROVED: CheckCircle,
	AUTHORIZATION_REJECTED: X,
	AUTHORIZATION_FEEDBACK: MessageSquare,
	PARTS_ORDERED: CheckCircle,
	JOB_STARTED: Wrench,
	JOB_COMPLETED: CheckCircle,
	JOB_CANCELLED: X,
	JOB_PAUSED: Clock,
	INVOICE_CREATED: AlertCircle,
	INVOICE_PAID: CheckCircle,
	ISSUE_RESOLVED: CheckCircle,
	REVIEW_REQUESTED: Star,
	REVIEW_LEFT: Star,
	TECHNICIAN_UPDATED: MessageSquare
};

const eventTypeColors: Record<TimelineEventType, string> = {
	PREAUTHORIZATION_APPROVED: "bg-emerald-500",
	PREAUTHORIZATION_REJECTED: "bg-red-500",
	CUSTOMER_REGISTERED: "bg-blue-500",
	TECHNICIAN_INVITED: "bg-blue-500",
	TECHNICIAN_ACCEPTED: "bg-emerald-500",
	TECHNICIAN_REJECTED: "bg-red-500",
	TECHNICIAN_REQUESTED_INFO: "bg-orange-500",
	TECHNICIAN_WITHDRAWN: "bg-red-500",
	CUSTOMER_ACCEPTED: "bg-emerald-500",
	CUSTOMER_REJECTED: "bg-red-500",
	AUTHORIZATION_REQUESTED: "bg-orange-500",
	AUTHORIZATION_APPROVED: "bg-emerald-500",
	AUTHORIZATION_REJECTED: "bg-red-500",
	AUTHORIZATION_FEEDBACK: "bg-orange-500",
	PARTS_ORDERED: "bg-emerald-500",
	JOB_STARTED: "bg-blue-500",
	JOB_COMPLETED: "bg-emerald-500",
	JOB_CANCELLED: "bg-red-500",
	JOB_PAUSED: "bg-orange-500",
	INVOICE_CREATED: "bg-orange-500",
	INVOICE_PAID: "bg-emerald-500",
	ISSUE_RESOLVED: "bg-emerald-500",
	REVIEW_REQUESTED: "bg-purple-500",
	REVIEW_LEFT: "bg-purple-500",
	TECHNICIAN_UPDATED: "bg-blue-500"
};

export function TimelineComponent({
	jobId,
	warrantyRequestId
}: TimelineComponentProps) {
	const [updates, setUpdates] = useState<TimelineUpdate[]>([]);
	const [isLoading, setIsLoading] = useState(true);

	const fetchUpdates = async () => {
		if (!jobId && !warrantyRequestId) return;

		setIsLoading(true);
		try {
			const params = new URLSearchParams();
			if (jobId) params.append("job_id", jobId);
			if (warrantyRequestId)
				params.append("warranty_request_id", warrantyRequestId);

			const response = await fetch(`/api/timeline-updates?${params}`);
			if (!response.ok) {
				throw new Error("Failed to fetch timeline updates");
			}

			const data = await response.json();
			setUpdates(data);
		} catch (error) {
			console.error("Error fetching timeline updates:", error);
			toast.error("Failed to load timeline updates");
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchUpdates();
	}, [jobId, warrantyRequestId]);

	if (isLoading) {
		return (
			<Card>
				<CardContent>
					<div className="animate-pulse space-y-4">
						{[1, 2, 3].map((i) => (
							<div key={i} className="flex gap-4">
								<div className="w-8 h-8 bg-gray-200 rounded-full" />
								<div className="flex-1 space-y-2">
									<div className="h-4 bg-gray-200 rounded w-1/4" />
									<div className="h-3 bg-gray-200 rounded w-3/4" />
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="space-y-6">
			{/* Timeline Updates */}
			{updates.length === 0 ? (
				<div className="text-center py-8 text-gray-500">
					<Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
					<p>No timeline updates yet</p>
				</div>
			) : (
				<div className="space-y-6">
					{updates.map((update, index) => {
						const Icon = eventTypeIcons[update.event_type];
						const colorClass = eventTypeColors[update.event_type];
						const isLast = index === updates.length - 1;

						return (
							<div key={update.id} className="relative flex gap-4">
								{/* Timeline line */}
								{!isLast && (
									<div className="absolute left-4 top-8 w-0.5 h-full bg-gray-200" />
								)}

								{/* Event icon */}
								<div
									className={`flex-shrink-0 w-8 h-8 rounded-full ${colorClass} flex items-center justify-center`}
								>
									<Icon className="h-4 w-4 text-white" />
								</div>

								{/* Event content */}
								<div className="flex-1 min-w-0">
									<div className="flex items-start justify-between">
										<div className="space-y-1">
											<h4 className="font-medium text-gray-900">
												{eventTypeLabels[update.event_type]}
											</h4>
											{update.details?.notes && (
												<p className="text-sm text-gray-600">
													{update.details.notes}
												</p>
											)}
											<div className="flex items-center gap-2 text-xs text-gray-500">
												<span>
													{update.updated_by.first_name}{" "}
													{update.updated_by.last_name}
												</span>
												<span>•</span>
												<span>
													{formatDistanceToNow(new Date(update.date), {
														addSuffix: true
													})}
												</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						);
					})}
				</div>
			)}
		</div>
	);
}
