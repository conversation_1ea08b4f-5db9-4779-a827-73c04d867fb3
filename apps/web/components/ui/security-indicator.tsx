import {
	Tooltip,
	TooltipContent,
	Toolt<PERSON>Provider,
	TooltipTrigger
} from "@/components/ui/tooltip";
import { AlertTriangle, Ban, Mail, Shield } from "lucide-react";

interface SecurityIndicatorProps {
	flaggedForFraud?: boolean;
	userBlacklisted?: boolean;
	emailBlacklisted?: boolean;
	blacklistMessage?: string;
	className?: string;
}

export function SecurityIndicator({
	flaggedForFraud,
	userBlacklisted,
	emailBlacklisted,
	blacklistMessage,
	className = ""
}: SecurityIndicatorProps) {
	const hasSecurityIssues =
		flaggedForFraud || userBlacklisted || emailBlacklisted;

	if (!hasSecurityIssues) {
		return null;
	}

	const getSecurityLevel = () => {
		if (userBlacklisted) return "high";
		if (flaggedForFraud) return "medium";
		if (emailBlacklisted) return "low";
		return "none";
	};

	const getIcon = () => {
		if (userBlacklisted) return <Ban className="h-3 w-3" />;
		if (flaggedForFraud) return <AlertTriangle className="h-3 w-3" />;
		if (emailBlacklisted) return <Mail className="h-3 w-3" />;
		return <Shield className="h-3 w-3" />;
	};

	const getTooltipContent = () => {
		const issues = [];
		if (flaggedForFraud) issues.push("🚨 Flagged for fraud");
		if (userBlacklisted) issues.push("🚫 User banned");
		if (emailBlacklisted) issues.push("📧 Email banned");

		if (blacklistMessage) {
			issues.push(`\nReason: ${blacklistMessage}`);
		}

		return issues.join("\n");
	};

	const securityLevel = getSecurityLevel();
	const icon = getIcon();
	const tooltipContent = getTooltipContent();

	return (
		<TooltipProvider>
			<Tooltip>
				<TooltipTrigger asChild>
					<div
						className={`
							inline-flex items-center justify-center w-5 h-5 rounded-full border-2
							${
								securityLevel === "high"
									? "bg-red-100 border-red-500 text-red-600"
									: securityLevel === "medium"
										? "bg-orange-100 border-orange-500 text-orange-600"
										: "bg-yellow-100 border-yellow-500 text-yellow-600"
							}
							hover:scale-110 transition-transform cursor-help
							${className}
						`}
					>
						{icon}
					</div>
				</TooltipTrigger>
				<TooltipContent side="top" className="max-w-xs whitespace-pre-line">
					<div className="font-medium mb-1">Security Issues:</div>
					{tooltipContent}
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
}
