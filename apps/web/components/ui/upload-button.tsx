import { useState } from 'react';
import { Button } from './button';
import { Loader2, Upload } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface UploadButtonProps {
    onUploadComplete: (url: string) => void;
    accept?: string;
    maxSize?: number; // in bytes
    className?: string;
}

export function UploadButton({ 
    onUploadComplete, 
    accept = '.pdf,.doc,.docx,.jpg,.jpeg,.png', 
    maxSize = 10 * 1024 * 1024, // 10MB default
    className = '' 
}: UploadButtonProps) {
    const [isUploading, setIsUploading] = useState(false);

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        // Check file size
        if (file.size > maxSize) {
            toast.error(`File size must be less than ${maxSize / (1024 * 1024)}MB`);
            return;
        }

        // Check file type
        const acceptedTypes = accept.split(',').map(type => type.trim());
        const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;
        if (!acceptedTypes.includes(fileExtension)) {
            toast.error(`File type must be one of: ${acceptedTypes.join(', ')}`);
            return;
        }

        setIsUploading(true);

        try {
            // Create form data
            const formData = new FormData();
            formData.append('file', file);

            // Upload file
            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                throw new Error('Upload failed');
            }

            const data = await response.json();
            onUploadComplete(data.url);
            toast.success('File uploaded successfully');
        } catch (error) {
            console.error('Upload error:', error);
            toast.error('Failed to upload file');
        } finally {
            setIsUploading(false);
            // Reset input
            e.target.value = '';
        }
    };

    return (
        <div className={className}>
            <input
                type="file"
                id="file-upload"
                className="hidden"
                accept={accept}
                onChange={handleFileChange}
                disabled={isUploading}
            />
            <label htmlFor="file-upload">
                <Button
                    type="button"
                    variant="outline"
                    className="w-full cursor-pointer"
                    disabled={isUploading}
                >
                    {isUploading ? (
                        <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Uploading...
                        </>
                    ) : (
                        <>
                            <Upload className="mr-2 h-4 w-4" />
                            Upload File
                        </>
                    )}
                </Button>
            </label>
        </div>
    );
} 