import { Badge } from "@/components/ui/badge";
import { Crown, Star, User } from "lucide-react";

interface MembershipBadgeProps {
	level: "FREE" | "STANDARD" | "PREMIUM" | string;
	className?: string;
}

export function MembershipBadge({ level, className }: MembershipBadgeProps) {
	const getBadgeConfig = (level: string) => {
		switch (level) {
			case "PREMIUM":
				return {
					variant: "default" as const,
					icon: Crown,
					className:
						"bg-gradient-to-r from-amber-500 to-yellow-500 text-white border-0"
				};
			case "STANDARD":
				return {
					variant: "secondary" as const,
					icon: Star,
					className:
						"bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-0"
				};
			case "FREE":
			default:
				return {
					variant: "outline" as const,
					icon: User,
					className: "text-gray-600 border-gray-300"
				};
		}
	};

	const config = getBadgeConfig(level);
	const IconComponent = config.icon;

	return (
		<Badge
			variant={config.variant}
			className={`${config.className} ${className || ""}`}
		>
			<IconComponent className="h-3 w-3 mr-1" />
			{level}
		</Badge>
	);
}
