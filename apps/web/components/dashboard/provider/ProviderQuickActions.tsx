import {
    <PERSON>,
    <PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>er,
    <PERSON><PERSON>itle,
} from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { ListingWithLocation } from "@/types/global";
import { ChevronRight, Clock, MapPin } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import VacationModeDialog from "../../VacationModeDialog";

export function ProviderQuickActions({
    listing,
}: {
    listing: ListingWithLocation | null
}) {
    const [vacationDialogOpen, setVacationDialogOpen] = useState(false);
    const [vacationModeEnabled, setVacationModeEnabled] = useState(false);
    const [vacationMessage, setVacationMessage] = useState("");

    // Initialize from listing data when component mounts or listing changes
    useEffect(() => {
        if (listing?.vacation_mode) {
            const vacation_mode = listing.vacation_mode || {};
            if (typeof vacation_mode === "object" && vacation_mode !== null && "enabled" in vacation_mode) {
                setVacationModeEnabled(!!(vacation_mode as { enabled: boolean }).enabled);
                setVacationMessage((vacation_mode as { message?: string }).message || "");
            } else {
                setVacationModeEnabled(false);
                setVacationMessage("");
            }
        }
    }, [listing]);

    const handleVacationModeClick = (e: React.MouseEvent) => {
        e.preventDefault();
        setVacationDialogOpen(true);
    };

    const quickLinks = [
        {
            title: "Update My Location",
            description: "Keep your service area up to date to receive relevant job requests",
            icon: MapPin,
            href: `/provider/business/location`,
            disabled: false,
            details: `Currently serving ${listing?.location?.city}, ${listing?.location?.state} area`,
        },
        {
            title: "Toggle Vacation Mode",
            description: "Toggle vacation mode to reactivate your listing", // "Temporarily turn off your listing",
            icon: Clock,
            href: "#",
            disabled: false,
            details: `Vacation mode is currently ${vacationModeEnabled ? "ON" : "OFF"}`,
            onClick: handleVacationModeClick,
        },
        // {
        //     title: "Update Availability",
        //     description: "Set your working hours",
        //     icon: Calendar,
        //     href: "#",
        //     disabled: true, // Placeholder for future functionality
        // },
        // {
        //     title: "Share Your Listing",
        //     description: "Help others discover your services",
        //     icon: Share2,
        //     href: "#",
        //     disabled: true,
        // },
    ]

    return (
        <>
            <Card className="shadow-sm border-gray-200 rounded-2xl overflow-hidden mb-8">
                <CardHeader className="p-6 border-b border-gray-100 flex flex-col sm:flex-row justify-between items-start sm:items-center bg-white">
                    <div>
                        <CardTitle className="text-lg font-medium text-gray-900">
                            Quick Actions
                        </CardTitle>
                    </div>
                </CardHeader>
                <CardContent className="p-6 bg-gradient-to-br from-white to-gray-50">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {quickLinks.map((link) => (
                            <Link
                                key={link.title}
                                href={link.disabled ? "#" : link.href}
                                className={cn(
                                    "flex items-center justify-between p-4 rounded-lg border border-[#e5e7eb] bg-white hover:bg-gray-50 transition-colors",
                                    link.disabled && "opacity-50 cursor-not-allowed"
                                )}
                                onClick={link.onClick}
                            >
                                <div className="flex items-center">
                                    <div className="w-10 h-10 rounded-full bg-[#f3f4f6] flex items-center justify-center mr-3">
                                        <link.icon className="h-5 w-5 text-[#4d7c6f]" />
                                    </div>
                                    <div>
                                        <h3 className="font-bold text-primary">{link.title}</h3>
                                        <p className="text-sm text-gray-600">{link.description}</p>
                                        <p className="pt-1 text-sm font-medium italic text-gray-800">{link.details}</p>
                                    </div>
                                </div>
                                <ChevronRight className="h-5 w-5 text-gray-400" />
                            </Link>
                        ))}
                    </div>
                </CardContent>
            </Card>

            <VacationModeDialog
                open={vacationDialogOpen}
                onOpenChange={setVacationDialogOpen}
                listingId={listing?.id}
                initialEnabled={vacationModeEnabled}
                initialMessage={vacationMessage}
                onSuccess={() => window.location.reload()}
            />
        </>
    )
}
