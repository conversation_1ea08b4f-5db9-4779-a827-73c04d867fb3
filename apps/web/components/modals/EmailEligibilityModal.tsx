"use client";

import Button from "@/components/Button";
import { AlertCircle, Mail, X } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface EmailEligibilityModalProps {
	isOpen: boolean;
	onClose: () => void;
	onContinueWithoutDiscount?: (email?: string) => void;
	selectedPriceId?: string;
}

interface EligibilityResponse {
	eligible: boolean;
	reason: string;
	userExists: boolean;
	userId?: string;
	offerId?: string;
	discountInfo?: any;
	offer?: any;
	lastOfferShown?: Date | null;
}

export default function EmailEligibilityModal({
	isOpen,
	onClose,
	onContinueWithoutDiscount,
	selectedPriceId
}: EmailEligibilityModalProps) {
	const [email, setEmail] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [eligibilityResult, setEligibilityResult] =
		useState<EligibilityResponse | null>(null);
	const [error, setError] = useState("");

	const handleCheckout = async (email?: string) => {
		const requestBody: any = {
			email,
			priceId: selectedPriceId
		};

		setIsLoading(true);
		try {
			const response = await fetch("/api/stripe/checkout", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(requestBody)
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Something went wrong");
			}

			// Redirect to Stripe Checkout
			window.location.href = data.url;
		} catch (error) {
			console.error("Checkout error:", error);
			toast.error("Failed to start checkout process. Please try again.");
		} finally {
			setIsLoading(false);
		}
	};

	const handleCheckEligibility = async () => {
		if (!email) {
			setError("Please enter your email address");
			return;
		}

		setIsLoading(true);
		setError("");
		setEligibilityResult(null);

		try {
			const response = await fetch("/api/membership/check-eligibility", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({ email })
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Failed to check eligibility");
			}

			setEligibilityResult(data);

			if (data.eligible) {
				// Call the onEligible callback with the email address
				// The checkout route will handle eligibility verification server-side
				handleCheckout(email);
			} else {
				// If not eligible, just proceed with normal checkout
				handleCheckout(email);
			}
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			handleCheckEligibility();
		}
	};

	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
			<div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
				<div className="p-6">
					{/* Header */}
					<div className="flex items-center justify-between mb-6">
						<div className="flex items-center space-x-3">
							<div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
								<Mail className="w-5 h-5 text-primary" />
							</div>
							<div>
								<h2 className="text-xl font-bold text-gray-900">
									Check Your Eligibility
								</h2>
								<p className="text-sm text-gray-600">
									Required step to continue with checkout
								</p>
							</div>
						</div>
						<button
							onClick={onClose}
							className="text-gray-400 hover:text-gray-600 transition-colors"
						>
							<X className="w-6 h-6" />
						</button>
					</div>

					{/* Email Input */}
					<div className="mb-6">
						<label
							htmlFor="email"
							className="block text-sm font-medium text-gray-700 mb-2"
						>
							Email Address
						</label>
						<input
							type="email"
							id="email"
							value={email}
							onChange={(e) => setEmail(e.target.value)}
							onKeyPress={handleKeyPress}
							placeholder="Enter your email address"
							className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
							disabled={isLoading}
						/>
					</div>

					{/* Error Message */}
					{error && (
						<div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
							<div className="flex items-center space-x-2">
								<AlertCircle className="w-4 h-4 text-red-500" />
								<span className="text-sm text-red-700">{error}</span>
							</div>
						</div>
					)}

					{/* Action Buttons */}
					<div className="flex flex-col space-y-3 items-center">
						<Button
							onClick={handleCheckEligibility}
							className="flex w-full items-center justify-center bg-primary hover:bg-primary-dark text-white"
							disabled={isLoading}
						>
							{isLoading ? "Checking..." : "Check Eligibility"}
						</Button>

						<Link
							href="#"
							onClick={(e) => {
								e.preventDefault();
								onClose();
								if (onContinueWithoutDiscount) {
									onContinueWithoutDiscount();
								}
							}}
							className="text-sm text-gray-500 hover:text-gray-700"
						>
							Continue Without Discount
						</Link>
					</div>

					{/* Info Text */}
					<div className="mt-4 text-xs text-gray-500 text-center">
						<p>
							We'll check if you're eligible for special discounts based on your
							account activity.
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
