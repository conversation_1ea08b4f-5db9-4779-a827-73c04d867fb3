"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { JobWithUserAndLocation } from "@/types/global";
import { CheckCircle, XCircle } from "lucide-react";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface JobStatusModalProps {
	isOpen: boolean;
	onClose: () => void;
	job: JobWithUserAndLocation;
	action: "complete" | "cancel";
	onSuccess: () => void;
}

export function JobStatusModal({
	isOpen,
	onClose,
	job,
	action,
	onSuccess
}: JobStatusModalProps) {
	const [reason, setReason] = useState("");
	const [selectedProviderId, setSelectedProviderId] = useState<string>("");
	const [isLoading, setIsLoading] = useState(false);

	// Check if job is in select-provider stage (no accepted quote)
	const isSelectProviderStage = !job.accepted_quote_id;

	// Get available providers for selection (only those with accepted/pending quotes)
	const availableProviders =
		job.quotes?.filter(
			(quote) => quote.status === "ACCEPTED" || quote.status === "PENDING"
		) || [];

	const handleSubmit = async () => {
		if (!reason.trim()) {
			toast.error("Please provide a reason");
			return;
		}

		// For completion during select-provider stage, we need either a provider selection or confirmation they didn't work with any
		if (
			action === "complete" &&
			isSelectProviderStage &&
			availableProviders.length > 0
		) {
			// If no provider selected, ask for confirmation
			if (!selectedProviderId) {
				const confirmed = window.confirm(
					"You haven't selected a provider. This means you completed the work yourself or found someone else not from our list. Continue?"
				);
				if (!confirmed) return;
			}
		}

		setIsLoading(true);

		try {
			const requestBody: any = {
				status: action === "complete" ? "COMPLETED" : "CANCELLED",
				reason: reason.trim()
			};

			// Include selected provider if completing during select-provider stage
			if (
				action === "complete" &&
				isSelectProviderStage &&
				selectedProviderId
			) {
				requestBody.selectedProviderId = selectedProviderId;
			}

			const response = await fetch(`/api/jobs/${job.id}/customer-status`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(requestBody)
			});

			const data = await response.json();

			if (response.ok) {
				toast.success(data.message);
				onSuccess();
				handleClose();
			} else {
				toast.error(data.error || "Failed to update job status");
			}
		} catch (error) {
			console.error("Error updating job status:", error);
			toast.error("Failed to update job status");
		} finally {
			setIsLoading(false);
		}
	};

	const handleClose = () => {
		setReason("");
		setSelectedProviderId("");
		onClose();
	};

	const getIcon = () => {
		return action === "complete" ? (
			<CheckCircle className="h-6 w-6 text-green-600" />
		) : (
			<XCircle className="h-6 w-6 text-red-600" />
		);
	};

	const getTitle = () => {
		return action === "complete" ? "Mark Job as Completed" : "Cancel Job";
	};

	const getDescription = () => {
		if (
			action === "complete" &&
			isSelectProviderStage &&
			availableProviders.length > 0
		) {
			return "Please let us know if you worked with one of the invited providers or completed the work another way.";
		}
		return action === "complete"
			? "Please let us know why you're marking this job as completed. This will help us improve our service and notify the provider."
			: "Please let us know why you're cancelling this job. This will help us improve our service and notify the provider.";
	};

	const getButtonText = () => {
		return action === "complete" ? "Mark as Completed" : "Cancel Job";
	};

	const getButtonStyle = () => {
		return action === "complete"
			? "bg-green-600 hover:bg-green-700 text-white"
			: "bg-red-600 hover:bg-red-700 text-white";
	};

	const getPlaceholder = () => {
		return action === "complete"
			? "e.g., The work was completed successfully and I'm satisfied with the service..."
			: "e.g., I found another provider, no longer need the service, etc...";
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleClose}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<div className="flex items-center gap-3">
						{getIcon()}
						<DialogTitle className="text-lg">{getTitle()}</DialogTitle>
					</div>
					<p className="text-sm text-gray-600 mt-2">{getDescription()}</p>
				</DialogHeader>

				<div className="space-y-4">
					{/* Provider Selection - only show for completion during select-provider stage */}
					{action === "complete" &&
						isSelectProviderStage &&
						availableProviders.length > 0 && (
							<div>
								<Label htmlFor="provider" className="text-sm font-medium">
									Did you work with one of these invited providers? (Optional)
								</Label>
								<Select
									value={selectedProviderId}
									onValueChange={setSelectedProviderId}
								>
									<SelectTrigger className="mt-1">
										<SelectValue placeholder="Select a provider (or leave blank if you didn't work with any)" />
									</SelectTrigger>
									<SelectContent>
										{availableProviders.map((quote) => {
											const providerName =
												quote.listing.business_name ||
												`${quote.listing.first_name} ${quote.listing.last_name}`;
											return (
												<SelectItem
													key={quote.listing.id}
													value={quote.listing.id}
												>
													{providerName}
												</SelectItem>
											);
										})}
									</SelectContent>
								</Select>
								<p className="text-xs text-gray-500 mt-1">
									Leave blank if you completed the work yourself or found
									someone else
								</p>
							</div>
						)}

					<div>
						<Label htmlFor="reason" className="text-sm font-medium">
							Reason{" "}
							{action === "complete" ? "(Optional details)" : "(Required)"}
						</Label>
						<Textarea
							id="reason"
							value={reason}
							onChange={(e) => setReason(e.target.value)}
							placeholder={getPlaceholder()}
							className="mt-1 min-h-[100px]"
							disabled={isLoading}
						/>
					</div>

					<div className="flex gap-2 pt-2">
						<Button
							variant="outline"
							onClick={handleClose}
							disabled={isLoading}
							className="flex-1"
						>
							Cancel
						</Button>
						<Button
							onClick={handleSubmit}
							disabled={isLoading || !reason.trim()}
							className={`flex-1 ${getButtonStyle()}`}
						>
							{isLoading ? "Processing..." : getButtonText()}
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
