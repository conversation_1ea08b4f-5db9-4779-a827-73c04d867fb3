"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { ExistingJob } from "@/lib/services/job.service";
import { ListingWithLocation } from "@/types/global";
import { formatDistanceToNow } from "date-fns";
import { Building2, Calendar, Car, Wrench } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";
import { useModal } from "../../lib/context/modal-context";

interface ExistingJobChoiceModalProps {
	isOpen: boolean;
	onClose: () => void;
	mostRecentActiveJob?: ExistingJob | null;
	onCreateNewJob: () => void;
	listing?: ListingWithLocation; // The provider the user wants to contact
}

export default function ExistingJobChoiceModal({
	isOpen,
	onClose,
	mostRecentActiveJob,
	onCreateNewJob,
	listing
}: ExistingJobChoiceModalProps) {
	const router = useRouter();
	const [isInviting, setIsInviting] = useState(false);
	const { closeSendMessageModal } = useModal();

	const isWarrantyRelated = mostRecentActiveJob?.warranty_request_id;
	const manufacturerName = mostRecentActiveJob?.company_name;

	const handleUseExistingJob = async () => {
		if (!mostRecentActiveJob) {
			return;
		}

		try {
			setIsInviting(true);
			const response = await fetch(
				`/api/jobs/${mostRecentActiveJob.job_id}/invite`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						providerIds: [listing.id]
					})
				}
			);

			if (!response.ok) {
				throw new Error("Failed to invite provider");
			}

			toast.success(
				`Successfully invited ${listing.business_name || `${listing.first_name} ${listing.last_name}`} to your existing job!`
			);

			// Navigate to the job workroom
			router.push(`/service-requests/${mostRecentActiveJob.job_id}`);
			closeSendMessageModal();
			onClose();
		} catch (error) {
			console.error("Error inviting provider to existing job:", error);
			toast.error("Failed to invite provider. Please try again.");
		} finally {
			setIsInviting(false);
		}
	};

	const handleCreateNewJob = () => {
		onCreateNewJob();
		onClose();
	};

	const getStatusDisplayText = (status: string, type: string) => {
		if (type === "job") {
			switch (status) {
				case "OPEN":
					return "Looking for providers";
				case "ASSIGNED":
					return "Provider assigned";
				case "IN_PROGRESS":
					return "Work in progress";
				default:
					return status.replace(/_/g, " ").toLowerCase();
			}
		} else {
			// Warranty request statuses
			switch (status) {
				case "REQUEST_APPROVED":
					return "Pre-approved";
				case "JOB_REQUESTED":
					return "Looking for providers";
				case "JOB_ACCEPTED":
					return "Provider accepted";
				case "JOB_STARTED":
					return "Work in progress";
				case "AUTHORIZATION_REQUESTED":
					return "Pending authorization";
				case "AUTHORIZATION_APPROVED":
					return "Authorized";
				case "AUTHORIZATION_FEEDBACK":
					return "Feedback requested";
				case "AUTHORIZATION_REJECTED":
					return "Authorization rejected";
				case "PARTS_ORDERED":
					return "Parts ordered";
				default:
					return status.replace(/_/g, " ").toLowerCase();
			}
		}
	};

	const getStatusColor = (status: string, type: string) => {
		if (type === "job") {
			switch (status) {
				case "OPEN":
					return "bg-blue-100 text-blue-800 border-blue-200";
				case "ASSIGNED":
				case "IN_PROGRESS":
					return "bg-orange-100 text-orange-800 border-orange-200";
				default:
					return "bg-gray-100 text-gray-800 border-gray-200";
			}
		} else {
			// Warranty request statuses
			switch (status) {
				case "REQUEST_APPROVED":
					return "bg-green-100 text-green-800 border-green-200";
				case "JOB_REQUESTED":
					return "bg-blue-100 text-blue-800 border-blue-200";
				case "JOB_ACCEPTED":
				case "JOB_STARTED":
					return "bg-orange-100 text-orange-800 border-orange-200";
				case "AUTHORIZATION_REQUESTED":
					return "bg-yellow-100 text-yellow-800 border-yellow-200";
				case "AUTHORIZATION_APPROVED":
					return "bg-green-100 text-green-800 border-green-200";
				case "AUTHORIZATION_FEEDBACK":
					return "bg-yellow-100 text-yellow-800 border-yellow-200";
				case "AUTHORIZATION_REJECTED":
					return "bg-red-100 text-red-800 border-red-200";
				case "PARTS_ORDERED":
					return "bg-blue-100 text-blue-800 border-blue-200";
				default:
					return "bg-gray-100 text-gray-800 border-gray-200";
			}
		}
	};

	const getProviderName = () => {
		if (!listing) return "";
		return (
			listing.business_name || `${listing.first_name} ${listing.last_name}`
		);
	};

	const getJobTypeText = (job: ExistingJob) => {
		if (job.type === "job") {
			// Determine if it's warranty or regular job
			if (job.warranty_request_id && job.company_name) {
				return `${job.company_name} Warranty Job`;
			}
			return job.warranty_request_id ? "Warranty Job" : "Service Request";
		} else {
			// Warranty request
			return job.company_name
				? `${job.company_name} Warranty Request`
				: "Warranty Request";
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-[600px]">
				<DialogHeader>
					<DialogTitle className="text-2xl font-semibold text-center">
						{isWarrantyRelated && manufacturerName
							? `Pre-approved ${manufacturerName} Warranty Claim Found`
							: "Existing Job Found"}
					</DialogTitle>
				</DialogHeader>

				<div className="py-4">
					<div className="mb-6 text-center text-gray-600">
						{isWarrantyRelated && manufacturerName ? (
							<>
								You have a pre-approved warranty claim from{" "}
								<strong>{manufacturerName}</strong>.
								<br />
								{listing && (
									<>
										Is this service request related to your warranty claim?
										<br />
										Would you like to invite{" "}
										<strong>{getProviderName()}</strong> to your existing
										warranty job?
									</>
								)}
								{!listing && (
									<>Is this service request related to your warranty claim?</>
								)}
							</>
						) : (
							<>
								You have an existing job.
								{listing && (
									<>
										<br />
										Would you like to invite{" "}
										<strong>{getProviderName()}</strong> to your existing job or
										create a new job?
									</>
								)}
								{!listing && (
									<>
										<br />
										Would you like to invite another provider to your existing
										job or create a new job?
									</>
								)}
							</>
						)}
					</div>

					{/* Show most recent/active job details */}
					{mostRecentActiveJob && (
						<div className="mb-6 p-4 border rounded-lg bg-gray-50">
							<div className="flex items-center justify-between mb-3">
								<h3 className="font-medium text-lg">
									{getJobTypeText(mostRecentActiveJob)}
								</h3>
								<span
									className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(mostRecentActiveJob.status as string, mostRecentActiveJob.type)}`}
								>
									{getStatusDisplayText(
										mostRecentActiveJob.status as string,
										mostRecentActiveJob.type
									)}
								</span>
							</div>

							<div className="grid grid-cols-2 gap-4 text-sm">
								{mostRecentActiveJob.rv_year && mostRecentActiveJob.rv_make && (
									<div className="flex items-center gap-2">
										<Car className="h-4 w-4 text-gray-500" />
										<span>
											{mostRecentActiveJob.rv_year}{" "}
											{mostRecentActiveJob.rv_make}{" "}
											{mostRecentActiveJob.rv_model}
										</span>
									</div>
								)}

								<div className="flex items-center gap-2">
									<Calendar className="h-4 w-4 text-gray-500" />
									<span>
										Created{" "}
										{formatDistanceToNow(
											new Date(mostRecentActiveJob.created_at)
										)}{" "}
										ago
									</span>
								</div>

								{mostRecentActiveJob.job_id && (
									<div className="flex items-center gap-2">
										<Wrench className="h-4 w-4 text-gray-500" />
										<span>Job #{mostRecentActiveJob.job_id.slice(-8)}</span>
									</div>
								)}
							</div>
						</div>
					)}

					{/* Action buttons */}
					<div className="space-y-4">
						{/* Primary button - Use existing job (more prominent) */}
						<Button
							onClick={handleUseExistingJob}
							className="w-full h-20 text-left flex flex-col items-start justify-center bg-green-600 hover:bg-green-700 shadow-lg border-2 border-green-500"
							disabled={!mostRecentActiveJob || isInviting}
						>
							<div className="flex items-center gap-3 mb-1">
								<Building2 className="h-6 w-6" />
								<span className="font-bold text-lg">
									{isInviting
										? "Processing..."
										: isWarrantyRelated && manufacturerName
											? `✓ Yes, Use ${manufacturerName} Warranty`
											: "✓ Use Existing Job"}
								</span>
							</div>
							<span className="text-sm opacity-95 font-medium">
								{isWarrantyRelated && manufacturerName
									? listing
										? `Add ${getProviderName()} to your warranty claim (Recommended)`
										: "Continue with your warranty claim (Recommended)"
									: listing
										? `Add ${getProviderName()} to your current job (Recommended)`
										: "Add this provider to your current job (Recommended)"}
							</span>
						</Button>

						{/* Secondary button - Create new job (less prominent) */}
						<Button
							onClick={handleCreateNewJob}
							variant="outline"
							className="w-full h-16 text-left flex flex-col items-start justify-center border border-gray-300 text-gray-700 hover:bg-gray-50"
							disabled={isInviting}
						>
							<div className="flex items-center gap-2 mb-1">
								<Wrench className="h-4 w-4" />
								<span className="font-medium">
									{isWarrantyRelated
										? "No, Create Separate Job"
										: "Create New Job"}
								</span>
							</div>
							<span className="text-xs text-gray-500">
								{isWarrantyRelated
									? "This is for a different, non-warranty issue"
									: "Only if this is for a different issue"}
							</span>
						</Button>
					</div>

					<div className="mt-4 text-center">
						<Button
							variant="ghost"
							onClick={onClose}
							className="text-gray-500"
							disabled={isInviting}
						>
							Cancel
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
