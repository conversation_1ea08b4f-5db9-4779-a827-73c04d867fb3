"use client";

import { PlacesAutocomplete } from "@/components/places/PlacesAutocomplete";
import { Button } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON>alogContent,
	<PERSON><PERSON>Header,
	DialogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/lib/hooks/useAuth";
import { zodResolver } from "@hookform/resolvers/zod";
import { CheckCircle, Crown, Loader2, Sparkles, Star } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

const proSupportSchema = z.object({
	name: z.string().min(1, "Name is required"),
	email: z.string().email("Invalid email address"),
	phone: z
		.string()
		.min(1, "Phone number is required")
		.regex(
			/^[\+]?[1-9][\d]{0,15}$|^[\+]?[(]?[\d\s\-\(\)]{10,17}$/,
			"Please enter a valid phone number"
		)
		.refine(
			(val) => {
				// Remove all non-digit characters to check length
				const digitsOnly = val.replace(/\D/g, "");
				return digitsOnly.length >= 10 && digitsOnly.length <= 15;
			},
			{
				message: "Phone number must contain 10-15 digits"
			}
		),
	location: z
		.object({
			address: z.string().min(1, "Please enter your location"),
			latitude: z.number(),
			longitude: z.number(),
			city: z.string().optional(),
			state: z.string().optional(),
			zip: z.string().optional(),
			country: z.string().optional()
		})
		.nullable()
		.refine((val) => val !== null, {
			message: "Please enter your location"
		}),
	subject: z.string().min(1, "Subject is required"),
	message: z.string().min(10, "Message must be at least 10 characters")
});

type ProSupportFormData = z.infer<typeof proSupportSchema>;

interface ProSupportModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	defaultSubject?: string;
	defaultMessage?: string;
}

export function ProSupportModal({
	open,
	onOpenChange,
	defaultSubject = "",
	defaultMessage = ""
}: ProSupportModalProps) {
	const { user, isPremium } = useAuth();
	const [isSubmitted, setIsSubmitted] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [locationConfirmed, setLocationConfirmed] = useState(false);

	const form = useForm<ProSupportFormData>({
		resolver: zodResolver(proSupportSchema),
		defaultValues: {
			name: user ? `${user.first_name} ${user.last_name}` : "",
			email: user?.email || "",
			phone: user?.phone || "",
			location: null,
			subject: defaultSubject,
			message: defaultMessage
		}
	});

	const handleLocationSelect = (address: string, details: any) => {
		if (details?.geometry?.location) {
			// Clear any existing validation errors for the location field
			form.clearErrors("location");

			// Create location data with address, latitude, longitude, and additional details
			const locationData = {
				address,
				latitude: details.geometry.location.lat(),
				longitude: details.geometry.location.lng(),
				city:
					details.address_components?.find((c) => c.types.includes("locality"))
						?.long_name || "",
				state:
					details.address_components?.find((c) =>
						c.types.includes("administrative_area_level_1")
					)?.short_name || "",
				zip:
					details.address_components?.find((c) =>
						c.types.includes("postal_code")
					)?.long_name || "",
				country:
					details.address_components?.find((c) => c.types.includes("country"))
						?.short_name || "US"
			};

			form.setValue("location", locationData);
			setLocationConfirmed(true);
		}
	};

	const onSubmit = async (data: ProSupportFormData) => {
		if (isSubmitting) return; // Prevent double submission

		setIsSubmitting(true);
		try {
			const response = await fetch("/api/support/pro-contact", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(data)
			});

			if (!response.ok) {
				// If the pro-contact endpoint doesn't exist, fallback to regular support
				const fallbackResponse = await fetch("/api/support/contact", {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						...data,
						// Include location in the message for fallback
						message: `${data.message}\n\nLocation: ${data.location?.address}`
					})
				});

				if (!fallbackResponse.ok)
					throw new Error("Failed to send support request");
			}

			setIsSubmitted(true);
			form.reset();
			setLocationConfirmed(false);
			toast.success("Support request sent successfully!");
		} catch (error) {
			console.error("Support request error:", error);
			toast.error("Failed to send message. Please try again.");
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		if (isSubmitted) {
			setIsSubmitted(false);
		}
		onOpenChange(false);
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-[600px]">
				{/* Premium Header with Gradient */}
				<DialogHeader className="relative bg-gradient-to-r from-amber-500 to-orange-500 -mx-6 mb-6 p-6 text-white">
					<div className="absolute top-0 right-0 opacity-20">
						<Sparkles className="w-12 h-12" />
					</div>
					<div className="py-6">
						<div className="flex items-center gap-3">
							<div className="bg-white/20 p-2 rounded-full">
								<Crown className="h-6 w-6 text-white" />
							</div>
							<div>
								<DialogTitle className="text-white text-xl">
									Pro Member Support
								</DialogTitle>
								<div className="flex items-center gap-2 mt-1">
									<span className="bg-white/20 text-white px-2 py-1 rounded-full text-xs font-medium">
										{isPremium ? "PREMIUM" : "STANDARD"} MEMBER
									</span>
									<div className="flex items-center gap-1">
										<Star className="w-3 h-3 text-amber-200" />
										<span className="text-xs text-amber-200">
											Priority Support
										</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</DialogHeader>

				{isSubmitted ? (
					<div className="flex flex-col items-center gap-6 py-8">
						<div className="relative">
							<div className="h-16 w-16 rounded-full bg-gradient-to-r from-green-400 to-emerald-500 flex items-center justify-center shadow-lg">
								<CheckCircle className="h-8 w-8 text-white" />
							</div>
							<div className="absolute -top-1 -right-1 bg-amber-500 p-1 rounded-full">
								<Crown className="h-4 w-4 text-white" />
							</div>
						</div>
						<div className="text-center space-y-3">
							<h3 className="text-xl font-semibold text-gray-900">
								Message Sent Successfully!
							</h3>
							<div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-4">
								<p className="text-sm text-amber-800 font-medium">
									✨ Pro Member Priority Response
								</p>
								<p className="text-sm text-amber-700 mt-1">
									Our Pro Member support team will respond as soon as possible.
									We will do our best to get back to you within 24 hours during
									business hours (M-F 9am-5pm EST).
								</p>
							</div>
						</div>
						<Button
							onClick={handleClose}
							className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-8"
						>
							Close
						</Button>
					</div>
				) : (
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
						{/* Premium Benefits Callout */}
						<div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 mb-6">
							<div className="flex items-center gap-2 mb-2">
								<Crown className="w-4 h-4 text-blue-600" />
								<span className="text-sm font-semibold text-blue-800">
									Your Pro Member Benefits
								</span>
							</div>
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs text-blue-700">
								<div className="flex items-center gap-1">
									<div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
									<span>Priority response times</span>
								</div>
								<div className="flex items-center gap-1">
									<div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
									<span>Location-based assistance</span>
								</div>
								<div className="flex items-center gap-1">
									<div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
									<span>Dedicated support team</span>
								</div>
								<div className="flex items-center gap-1">
									<div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
									<span>Concierge-level service</span>
								</div>
							</div>
						</div>

						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
							<div className="space-y-2">
								<label
									htmlFor="subject"
									className="text-sm font-medium text-gray-700"
								>
									Subject *
								</label>
								<Input
									id="subject"
									{...form.register("subject")}
									error={form.formState.errors.subject?.message}
									className="focus:ring-amber-500 focus:border-amber-500"
									disabled={isSubmitting}
								/>
							</div>

							<div className="space-y-2">
								<label
									htmlFor="name"
									className="text-sm font-medium text-gray-700"
								>
									Name *
								</label>
								<Input
									id="name"
									{...form.register("name")}
									error={form.formState.errors.name?.message}
									className="focus:ring-amber-500 focus:border-amber-500"
									disabled={isSubmitting}
								/>
							</div>
						</div>

						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
							<div className="space-y-2">
								<label
									htmlFor="email"
									className="text-sm font-medium text-gray-700"
								>
									Email *
								</label>
								<Input
									id="email"
									type="email"
									{...form.register("email")}
									error={form.formState.errors.email?.message}
									className="focus:ring-amber-500 focus:border-amber-500"
									disabled={isSubmitting}
								/>
							</div>

							<div className="space-y-2">
								<label
									htmlFor="phone"
									className="text-sm font-medium text-gray-700"
								>
									Phone Number *
								</label>
								<Input
									id="phone"
									type="tel"
									{...form.register("phone")}
									error={form.formState.errors.phone?.message}
									placeholder="(*************"
									className="focus:ring-amber-500 focus:border-amber-500"
									disabled={isSubmitting}
								/>
							</div>
						</div>

						<div className="space-y-2">
							<label
								htmlFor="location"
								className="text-sm font-medium text-gray-700"
							>
								Your Location *
							</label>
							<div className="relative">
								<PlacesAutocomplete
									placeholder="Enter your street address"
									value={form.watch("location")?.address || ""}
									onPlaceSelect={handleLocationSelect}
									onChange={(e) => {
										if (!e.target.value) {
											form.setValue("location", null);
											setLocationConfirmed(false);
										}
									}}
									locationType="precise"
									className="focus:ring-amber-500 focus:border-amber-500 transition-all duration-200"
									disabled={isSubmitting}
								/>
								{locationConfirmed && (
									<div className="absolute right-3 top-1/2 -translate-y-1/2">
										<div className="bg-green-100 p-1 rounded-full">
											<CheckCircle className="w-4 h-4 text-green-600" />
										</div>
									</div>
								)}
							</div>
							{form.formState.errors.location && (
								<p className="text-sm text-red-500">
									{form.formState.errors.location.message}
								</p>
							)}
							{locationConfirmed && form.watch("location")?.address && (
								<div className="mt-2 text-sm text-green-600 flex items-center gap-1">
									<CheckCircle className="w-4 h-4" />
									<span>Location confirmed</span>
								</div>
							)}
							<p className="text-xs text-gray-500">
								This helps us connect you with local resources and provide
								location-specific assistance.
							</p>
						</div>

						<div className="space-y-2">
							<label
								htmlFor="message"
								className="text-sm font-medium text-gray-700"
							>
								How can we help you? *
							</label>
							<Textarea
								id="message"
								rows={4}
								{...form.register("message")}
								error={form.formState.errors.message?.message}
								placeholder="Please describe how our Pro Member support team can assist you..."
								className="focus:ring-amber-500 focus:border-amber-500 resize-none"
								disabled={isSubmitting}
							/>
						</div>

						<Button
							type="submit"
							disabled={isSubmitting}
							className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white py-3 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
						>
							{isSubmitting ? (
								<>
									<Loader2 className="w-4 h-4 mr-2 animate-spin" />
									Sending Request...
								</>
							) : (
								<>
									<Crown className="w-4 h-4 mr-2" />
									Send Pro Support Request
								</>
							)}
						</Button>
					</form>
				)}
			</DialogContent>
		</Dialog>
	);
}
