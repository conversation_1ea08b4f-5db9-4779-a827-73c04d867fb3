"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { JobWithUserAndLocation } from "@/types/global";
import { Listing } from "@rvhelp/database";
import { Check, ChevronDown, ChevronUp, Hourglass, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import ProviderCard from "../../jobs/provider-card";

interface ListingWithDistance extends Listing {
	distance: number;
}

interface InviteMoreProvidersProps {
	job: JobWithUserAndLocation;
	timeLeft: { days: number; hours: number; minutes: number; seconds: number };
	isExpired: boolean;
	isCheckingOut: boolean;
	onUpgrade: (selectedProviders: string[]) => void;
	hasDiscount?: boolean;
	onProvidersSelected?: (providers: string[]) => void;
}

export default function InviteMoreProviders({
	job,
	timeLeft,
	isExpired,
	isCheckingOut,
	onUpgrade,
	hasDiscount = false,
	onProvidersSelected
}: InviteMoreProvidersProps) {
	const [showAllBenefits, setShowAllBenefits] = useState(false);
	const [providers, setProviders] = useState<ListingWithDistance[]>([]);
	const [selectedProviders, setSelectedProviders] = useState<Set<string>>(
		new Set()
	);
	const [isLoadingProviders, setIsLoadingProviders] = useState(false);
	const [hasShownToast, setHasShownToast] = useState(false);

	// Calculate how many providers have already been invited
	const existingQuotes = job.quotes || [];
	const activeQuotes = existingQuotes.filter(
		(quote) => quote.status !== "REJECTED" && quote.status !== "WITHDRAWN"
	);

	// Show the benefit of upgrading from 1 provider (free) to 5 providers (paid)
	const currentLimit = 1; // Free users current limit
	const upgradeLimit = 5; // What they get with Pro
	const remainingInvitations = upgradeLimit - activeQuotes.length;

	const additionalBenefits = [
		"Emergency roadside assistance network",
		"Emergency dispatch to nearest 20 providers",
		"24/7 customer support hotline",
		"Quick diagnostic calls with certified RV techs",
		"Save up to 25% on mobile service fees",
		"RV maintenance tracking and reminders",
		"Priority booking during peak seasons"
	];

	// Fetch providers when component mounts
	useEffect(() => {
		console.log("job", job);
		if (job.location?.latitude && job.location?.longitude && job.category) {
			fetchProviders();
		}
	}, [job.location?.latitude, job.location?.longitude, job.category]);

	const fetchProviders = async () => {
		setIsLoadingProviders(true);
		try {
			const response = await fetch(
				`/api/listings/search?limit=20&page=1&category=${job.category}&lat=${job.location.latitude}&lng=${job.location.longitude}`
			);
			if (!response.ok) throw new Error("Failed to fetch providers");
			const data = await response.json();

			// Filter out providers that have already been invited
			const invitedListingIds = new Set(
				existingQuotes.map((quote) => quote.listing_id)
			);
			const availableProviders = data.listings.filter(
				(provider: ListingWithDistance) => !invitedListingIds.has(provider.id)
			);
			setProviders(availableProviders);
		} catch (error) {
			console.error("Error fetching providers:", error);
		} finally {
			setIsLoadingProviders(false);
		}
	};

	const handleProviderSelect = (providerId: string) => {
		setSelectedProviders((prev) => {
			const newSet = new Set(prev);
			if (newSet.has(providerId)) {
				newSet.delete(providerId);
			} else if (newSet.size < remainingInvitations) {
				newSet.add(providerId);
			}

			// Call the callback with the updated providers
			const updatedProviders = Array.from(newSet);
			onProvidersSelected?.(updatedProviders);

			return newSet;
		});

		// Show a toast encouraging upgrade (only if we haven't shown it recently)
		if (
			!selectedProviders.has(providerId) &&
			selectedProviders.size < remainingInvitations &&
			!hasShownToast
		) {
			toast.success(
				"Provider selected! Upgrade to send invitations to multiple providers.",
				{
					duration: 3000,
					icon: "🚀"
				}
			);
			setHasShownToast(true);
			// Reset the flag after 5 seconds
			setTimeout(() => setHasShownToast(false), 5000);
		}
	};

	const formatTime = () => {
		return `${timeLeft.days > 0 ? `${timeLeft.days}d ` : ""}${timeLeft.hours}h ${timeLeft.minutes}m ${timeLeft.seconds}s`;
	};

	const handleUpgrade = () => {
		onUpgrade(Array.from(selectedProviders));
	};

	return (
		<div className="space-y-4">
			<div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
				<p className="text-blue-800">
					{activeQuotes.length > 0 ? (
						<>
							You have {activeQuotes.length} provider
							{activeQuotes.length > 1 ? "s" : ""} already invited. As a free
							user, you're limited to {currentLimit} provider. Upgrade to Pro to
							invite up to {upgradeLimit} providers total and get{" "}
							{remainingInvitations} more invitations!
						</>
					) : (
						<>
							As a free user, you can only invite {currentLimit} provider.
							Upgrade to Pro to invite up to {upgradeLimit} providers and
							increase your chances of getting quick responses!
						</>
					)}
				</p>
			</div>

			{/* Provider Selection Section */}
			<div className="space-y-3">
				<div className="space-y-2">
					<div className="flex items-center gap-2">
						<Users className="w-5 h-5 text-gray-600" />
						<span className="font-medium text-gray-900">
							{providers.length} providers available in your area
						</span>
					</div>
					<div className="flex items-center justify-between text-sm">
						<span className="text-gray-600">
							Select providers to see how Pro membership works
						</span>
						{selectedProviders.size > 0 && (
							<span className="font-medium text-green-600">
								{selectedProviders.size} selected (upgrade to invite them all!)
							</span>
						)}
					</div>
				</div>

				<ScrollArea className="h-[450px] border rounded-lg p-4 bg-gray-50">
					{isLoadingProviders ? (
						// Loading skeletons
						Array.from({ length: 6 }).map((_, i) => (
							<div
								key={i}
								className="flex items-start space-x-4 p-4 border rounded-lg mb-3 bg-white"
							>
								<Skeleton className="h-12 w-12 rounded-full" />
								<div className="flex-1 space-y-2">
									<Skeleton className="h-4 w-[200px]" />
									<Skeleton className="h-4 w-[150px]" />
								</div>
							</div>
						))
					) : providers.length > 0 ? (
						<div className="space-y-3">
							{providers.slice(0, 10).map((provider) => (
								<div key={provider.id} className="bg-white rounded-lg border">
									<ProviderCard
										category={job.category}
										provider={provider as any}
										onSelect={handleProviderSelect}
										isSelected={selectedProviders.has(provider.id)}
									/>
								</div>
							))}
						</div>
					) : (
						<div className="text-center py-8 text-gray-500">
							<Users className="w-12 h-12 mx-auto mb-3 text-gray-300" />
							<p>No providers found in your area</p>
						</div>
					)}
				</ScrollArea>

				{/* Selection Summary */}
				{selectedProviders.size > 0 && (
					<div className="bg-green-50 border border-green-200 rounded-lg p-3">
						<div className="flex items-center gap-2 mb-1">
							<Check className="w-4 h-4 text-green-600" />
							<span className="font-medium text-green-800">
								You've selected {selectedProviders.size} provider
								{selectedProviders.size > 1 ? "s" : ""}
							</span>
						</div>
						<p className="text-sm text-green-700">
							Upgrade to RV Help Pro to send your service request to all
							selected providers and get faster responses!
						</p>
					</div>
				)}
			</div>

			{/* Urgency Timer - only show if user has discount */}
			{hasDiscount && !isExpired && (
				<Card className="bg-yellow-50 border-yellow-200">
					<CardContent className="p-3 text-center">
						<div className="flex items-center justify-center gap-2 text-sm font-medium text-yellow-700">
							<Hourglass className="w-4 h-4 animate-pulse" />
							Special offer expires in: {formatTime()}
						</div>
					</CardContent>
				</Card>
			)}

			{/* Upgrade Benefits */}
			<div className="bg-green-50 border border-green-200 rounded-lg p-3">
				<div className="flex items-center gap-2">
					<Check className="w-4 h-4 text-green-600 flex-shrink-0" />
					<span className="text-sm text-green-800">
						Get up to {upgradeLimit - 1} more responses when you invite multiple
						providers
					</span>
				</div>
			</div>

			{/* Additional Benefits (Expandable) */}
			<div>
				<button
					onClick={() => setShowAllBenefits(!showAllBenefits)}
					className="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
				>
					<span>View all Pro membership benefits</span>
					{showAllBenefits ? (
						<ChevronUp className="w-4 h-4" />
					) : (
						<ChevronDown className="w-4 h-4" />
					)}
				</button>

				{showAllBenefits && (
					<div className="mt-3 space-y-2">
						{additionalBenefits.map((benefit, index) => (
							<div
								key={index}
								className="flex items-center gap-2 text-sm text-gray-600"
							>
								<Check className="w-3 h-3 text-green-600 flex-shrink-0" />
								<span>{benefit}</span>
							</div>
						))}
					</div>
				)}
			</div>

			{/* Call to Action */}
			<div className="pt-4 space-y-3">
				<Button
					onClick={handleUpgrade}
					disabled={isCheckingOut}
					className="w-full py-4 text-lg font-semibold bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
				>
					{isCheckingOut ? (
						<>
							<div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
							Processing...
						</>
					) : hasDiscount ? (
						`Upgrade Now - Save 50%`
					) : (
						`Upgrade to Pro - $99 yearly`
					)}
				</Button>

				<div className="text-center text-xs text-gray-500">
					{hasDiscount ? "Limited time offer" : "Cancel anytime"}
				</div>
			</div>
		</div>
	);
}
