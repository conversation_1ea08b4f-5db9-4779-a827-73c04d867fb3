"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { JobWithUserAndLocation } from "@/types/global";
import {
	Check,
	ChevronDown,
	ChevronUp,
	Clock,
	Hourglass,
	Phone,
	Shield,
	UserCheck
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface ProSupportProps {
	job: JobWithUserAndLocation;
	timeLeft: { days: number; hours: number; minutes: number; seconds: number };
	isExpired: boolean;
	isCheckingOut: boolean;
	onUpgrade: () => void;
	hasDiscount?: boolean;
}

export default function ProSupport({
	job,
	timeLeft,
	isExpired,
	isCheckingOut,
	onUpgrade,
	hasDiscount = false
}: ProSupportProps) {
	const [showAllBenefits, setShowAllBenefits] = useState(false);

	const supportBenefits = [
		{
			icon: UserCheck,
			title: "Personal Support When You Need It",
			description:
				"Get direct access to a dedicated support specialist if you need help with your service request"
		},
		{
			icon: Phone,
			title: "Help Finding the Right Providers",
			description:
				"If you're having trouble getting responses, we can help connect you with additional qualified providers"
		},
		{
			icon: Clock,
			title: "Priority Response Guarantee",
			description:
				"If your service request isn't progressing, we'll step in to help ensure you get the attention you need"
		},
		{
			icon: Shield,
			title: "Expert Guidance & Support",
			description:
				"Get advice on quotes, help with provider communication, and support throughout your service process"
		}
	];

	const additionalBenefits = [
		"Priority phone support line with real humans",
		"Emergency roadside assistance network",
		"Emergency dispatch to nearest 20 providers",
		"Quick diagnostic calls with certified RV techs",
		"Save up to 25% on mobile service fees",
		"RV maintenance tracking and reminders",
		"Warranty assistance and claims support"
	];

	const formatTime = () => {
		return `${timeLeft.days > 0 ? `${timeLeft.days}d ` : ""}${timeLeft.hours}h ${timeLeft.minutes}m ${timeLeft.seconds}s`;
	};

	return (
		<div className="space-y-4">
			<div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
				<h3 className="font-semibold text-blue-900 mb-2">
					Want Extra Peace of Mind?
				</h3>
				<p className="text-blue-800">
					With Pro support, you'll have a dedicated team standing by to help if
					you need assistance with your service request. Think of it as your
					safety net for getting the RV help you need.
				</p>
			</div>

			{/* Main Support Benefits */}
			<div className="space-y-3">
				{supportBenefits.map((benefit, index) => (
					<div
						key={index}
						className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg border"
					>
						<div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
							<benefit.icon className="w-4 h-4 text-green-600" />
						</div>
						<div>
							<h4 className="font-medium text-gray-900 mb-1">
								{benefit.title}
							</h4>
							<p className="text-sm text-gray-600">{benefit.description}</p>
						</div>
					</div>
				))}
			</div>

			{/* How It Works */}
			<div className="bg-green-50 border border-green-200 rounded-lg p-4">
				<h4 className="font-semibold text-green-900 mb-3">
					How Pro Support Works:
				</h4>
				<div className="space-y-2 text-sm text-green-800">
					<div className="flex items-center gap-2">
						<span className="w-5 h-5 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-semibold">
							1
						</span>
						<span>You handle your service request as normal</span>
					</div>
					<div className="flex items-center gap-2">
						<span className="w-5 h-5 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-semibold">
							2
						</span>
						<span>If you need help, contact our support team anytime</span>
					</div>
					<div className="flex items-center gap-2">
						<span className="w-5 h-5 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-semibold">
							3
						</span>
						<span>
							We'll assist with provider outreach, quote review, or any issues
						</span>
					</div>
					<div className="flex items-center gap-2">
						<span className="w-5 h-5 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-semibold">
							4
						</span>
						<span>Get the confidence that expert help is always available</span>
					</div>
				</div>
			</div>

			{/* Urgency Timer - only show if user has discount */}
			{hasDiscount && !isExpired && (
				<Card className="bg-yellow-50 border-yellow-200">
					<CardContent className="p-3 text-center">
						<div className="flex items-center justify-center gap-2 text-sm font-medium text-yellow-700">
							<Hourglass className="w-4 h-4 animate-pulse" />
							Special offer expires in: {formatTime()}
						</div>
					</CardContent>
				</Card>
			)}

			{/* Additional Benefits (Expandable) */}
			<div>
				<button
					onClick={() => setShowAllBenefits(!showAllBenefits)}
					className="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
				>
					<span>View all Pro membership benefits</span>
					{showAllBenefits ? (
						<ChevronUp className="w-4 h-4" />
					) : (
						<ChevronDown className="w-4 h-4" />
					)}
				</button>

				{showAllBenefits && (
					<div className="mt-3 space-y-2">
						{additionalBenefits.map((benefit, index) => (
							<div
								key={index}
								className="flex items-center gap-2 text-sm text-gray-600"
							>
								<Check className="w-3 h-3 text-green-600 flex-shrink-0" />
								<span>{benefit}</span>
							</div>
						))}
					</div>
				)}
			</div>

			<div className="space-y-2 mt-4">
				{/* CTA Button */}
				<Button
					onClick={onUpgrade}
					disabled={isCheckingOut}
					className="w-full bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white py-3 text-base font-semibold"
				>
					{isCheckingOut ? (
						<>
							<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
							Processing...
						</>
					) : (
						<>Get Pro Support & Peace of Mind</>
					)}
				</Button>

				{/* Pricing */}
				<div className="text-center text-sm text-gray-600 mt-2">
					{hasDiscount && !isExpired
						? "$49/year (first year, then $99/year)"
						: "$99/year"}
				</div>

				{/* Pro Benefits Link */}
				<div className="text-center text-xs text-gray-500 mt-4">
					Learn more about our{" "}
					<Link
						href="/pro-membership"
						target="_blank"
						className="text-green-600 hover:underline font-medium"
					>
						Pro membership benefits →
					</Link>
				</div>
			</div>
		</div>
	);
}
