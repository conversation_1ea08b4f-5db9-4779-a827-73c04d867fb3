"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog";
import { useAuth } from "@/lib/hooks/useAuth";
import { Download, Share2, Star } from "lucide-react";
import { QRCodeCanvas } from "qrcode.react";
import { useState } from "react";
import toast from "react-hot-toast";

interface ShareListingModalProps {
    isOpen: boolean;
    onClose: () => void;
    listing?: {
        slug: string;
        business_name?: string;
        first_name?: string;
        last_name?: string;
    };
}

type ShareType = "listing" | "review" | null;

export default function ShareListingModal({
    isOpen,
    onClose,
    listing
}: ShareListingModalProps) {
    const { user } = useAuth();
    const [shareType, setShareType] = useState<ShareType>(null);

    if (!user?.referral_code || !listing) {
        return null;
    }

    const businessName = listing.business_name || `${listing.first_name} ${listing.last_name}`;

    // Generate URLs with redirect parameters
    const listingShareUrl = `${window.location.origin}/ref/${user.referral_code}?redirect=${encodeURIComponent(`/providers/${listing.slug}`)}`;
    const reviewShareUrl = `${window.location.origin}/ref/${user.referral_code}?redirect=${encodeURIComponent(`/providers/${listing.slug}/review`)}`;

    const handleCopyUrl = async (url: string, type: string) => {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(url);
                toast.success(`${type} link copied to clipboard`);
            } else {
                const textArea = document.createElement("textarea");
                textArea.value = url;
                textArea.style.position = "fixed";
                textArea.style.left = "-999999px";
                textArea.style.top = "-999999px";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand("copy");
                    toast.success(`${type} link copied to clipboard`);
                } catch (err) {
                    toast.error("Failed to copy URL to clipboard");
                }
                textArea.remove();
            }
        } catch (error) {
            console.error("Failed to copy URL:", error);
            toast.error("Failed to copy URL to clipboard");
        }
    };

    const handleDownloadQR = (type: string) => {
        const canvas = document.getElementById(
            `share-qr-code-${type}`
        ) as HTMLCanvasElement;
        if (!canvas) return;

        const url = canvas.toDataURL("image/png");
        const link = document.createElement("a");
        link.download = `${businessName.toLowerCase().replace(/\s+/g, "-")}-${type}-qr.png`;
        link.href = url;
        link.click();
    };

    const handleReset = () => {
        setShareType(null);
    };

    const handleClose = () => {
        setShareType(null);
        onClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-md mx-4">
                <DialogHeader>
                    <DialogTitle>Share Your Business</DialogTitle>
                    <DialogDescription>
                        Choose what you'd like customers to access when they scan your QR code
                    </DialogDescription>
                </DialogHeader>

                {!shareType ? (
                    // Selection Screen
                    <div className="flex flex-col gap-4 py-4">
                        <Button
                            variant="outline"
                            size="lg"
                            className="flex items-center justify-start gap-4 h-auto p-6 text-left"
                            onClick={() => setShareType("listing")}
                        >
                            <div className="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg">
                                <Share2 className="w-6 h-6 text-primary" />
                            </div>
                            <div className="flex-1">
                                <div className="font-semibold">Share My Listing</div>
                                <div className="text-sm text-gray-500">
                                    Direct customers to your full business profile
                                </div>
                            </div>
                        </Button>

                        <Button
                            variant="outline"
                            size="lg"
                            className="flex items-center justify-start gap-4 h-auto p-6 text-left"
                            onClick={() => setShareType("review")}
                        >
                            <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg">
                                <Star className="w-6 h-6 text-yellow-600" />
                            </div>
                            <div className="flex-1">
                                <div className="font-semibold">Get Reviews</div>
                                <div className="text-sm text-gray-500">
                                    Direct customers to leave you a review
                                </div>
                            </div>
                        </Button>
                    </div>
                ) : (
                    // QR Code Display Screen
                    <div className="flex flex-col items-center gap-6 py-4">
                        <div className="text-center">
                            <h3 className="font-semibold text-lg">
                                {shareType === "listing" ? "Share My Listing" : "Get Reviews"}
                            </h3>
                            <p className="text-sm text-gray-500 mt-1">
                                {shareType === "listing"
                                    ? "Customers will see your full business profile"
                                    : "Customers will be directed to leave you a review"}
                            </p>
                        </div>

                        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
                            <QRCodeCanvas
                                id={`share-qr-code-${shareType}`}
                                value={shareType === "listing" ? listingShareUrl : reviewShareUrl}
                                size={200}
                                level="H"
                                includeMargin={true}
                                className="rounded-lg"
                            />
                        </div>

                        <div className="flex w-full gap-2">
                            <Button
                                variant="outline"
                                className="flex-1"
                                onClick={() =>
                                    handleCopyUrl(
                                        shareType === "listing" ? listingShareUrl : reviewShareUrl,
                                        shareType === "listing" ? "Listing" : "Review"
                                    )
                                }
                            >
                                Copy URL
                            </Button>
                            <Button
                                className="flex-1"
                                onClick={() => handleDownloadQR(shareType)}
                            >
                                <Download className="mr-2 h-4 w-4" />
                                Download QR
                            </Button>
                        </div>

                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleReset}
                            className="text-gray-500"
                        >
                            ← Back to options
                        </Button>
                    </div>
                )}
            </DialogContent>
        </Dialog>
    );
} 