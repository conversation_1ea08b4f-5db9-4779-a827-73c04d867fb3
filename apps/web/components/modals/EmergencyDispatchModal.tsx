"use client";

import { PlacesAutocomplete } from "@/components/places/PlacesAutocomplete";
import { Button } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/lib/hooks/useAuth";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertTriangle, CheckCircle, X } from "lucide-react";
import { useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

const emergencyDispatchSchema = z.object({
	firstName: z.string().min(1, "First name is required"),
	lastName: z.string().min(1, "Last name is required"),
	email: z.string().email("Invalid email address"),
	phone: z
		.string()
		.min(1, "Phone number is required")
		.regex(
			/^[\+]?[1-9][\d]{0,15}$|^[\+]?[(]?[\d\s\-\(\)]{10,17}$/,
			"Please enter a valid phone number"
		),
	location: z.object({
		address: z.string().min(1, "Location is required"),
		latitude: z.number(),
		longitude: z.number()
	}),
	currentLocation: z.string().optional(),
	emergencyDescription: z
		.string()
		.min(10, "Please provide more detail about the emergency"),
	uploadedFiles: z
		.array(
			z.object({
				name: z.string(),
				size: z.string(),
				type: z.string(),
				url: z.string()
			})
		)
		.optional()
});

type EmergencyDispatchFormData = z.infer<typeof emergencyDispatchSchema>;

interface EmergencyDispatchModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

const EMERGENCY_TYPES = [
	{
		icon: "⚡",
		text: "Complete electrical system failure (no power to house systems)"
	},
	{
		icon: "🔥",
		text: "AC or heating failure in extreme temperatures"
	},
	{
		icon: "🚐",
		text: "Slide-out stuck open preventing safe driving"
	},
	{
		icon: "💧",
		text: "Major water leak flooding the RV"
	},
	{
		icon: "⛽",
		text: "Propane leak or gas system failure"
	},
	{
		icon: "🔧",
		text: "Leveling jacks stuck down/won't retract"
	},
	{
		icon: "🔋",
		text: "Generator failure when boondocking (no shore power available)"
	},
	{
		icon: "⚠️",
		text: "Any critical house system preventing safe travel or habitability"
	}
];

export function EmergencyDispatchModal({
	open,
	onOpenChange
}: EmergencyDispatchModalProps) {
	const { user } = useAuth();
	const [isSubmitted, setIsSubmitted] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
	const [userLocation, setUserLocation] = useState<string>("");

	// Add ref to track if a submission is in progress
	const submissionInProgress = useRef(false);

	const form = useForm<EmergencyDispatchFormData>({
		resolver: zodResolver(emergencyDispatchSchema),
		defaultValues: {
			firstName: user?.first_name || "",
			lastName: user?.last_name || "",
			email: user?.email || "",
			phone: user?.phone || "",
			currentLocation: "",
			emergencyDescription: "",
			uploadedFiles: []
		}
	});

	// Get user's current location
	const getCurrentLocation = () => {
		if (navigator.geolocation) {
			navigator.geolocation.getCurrentPosition(
				(position) => {
					const { latitude, longitude } = position.coords;
					// Reverse geocode to get address
					fetch(
						`https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${process.env.NEXT_PUBLIC_MAPBOX_TOKEN}`
					)
						.then((res) => res.json())
						.then((data) => {
							if (data.features && data.features.length > 0) {
								const address = data.features[0].place_name;
								setUserLocation(address);
								form.setValue("location", {
									address,
									latitude,
									longitude
								});
							}
						})
						.catch(console.error);
				},
				(error) => {
					console.error("Error getting location:", error);
					toast.error("Unable to get your location. Please enter it manually.");
				}
			);
		}
	};

	const handleLocationSelect = (address: string, details: any) => {
		if (details?.geometry?.location) {
			form.setValue("location", {
				address,
				latitude: details.geometry.location.lat(),
				longitude: details.geometry.location.lng()
			});
		}
	};

	const onSubmit = async (data: EmergencyDispatchFormData) => {
		// Prevent multiple simultaneous submissions
		if (submissionInProgress.current) {
			console.log(
				"🚫 Emergency dispatch submission already in progress, ignoring duplicate submission"
			);
			return;
		}

		// Set submission in progress flag
		submissionInProgress.current = true;
		setIsSubmitting(true);

		try {
			const response = await fetch("/api/emergency-dispatch", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					...data,
					uploadedFiles
				})
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(
					errorData.error || "Failed to submit emergency request"
				);
			}

			setIsSubmitted(true);
			form.reset();
			setUploadedFiles([]);
		} catch (error) {
			console.error("Error submitting emergency request:", error);
			toast.error("Failed to submit emergency request. Please try again.");
		} finally {
			setIsSubmitting(false);
			// Reset submission in progress flag
			submissionInProgress.current = false;
		}
	};

	const handleClose = () => {
		if (isSubmitted) {
			setIsSubmitted(false);
		}
		onOpenChange(false);
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2 text-red-600">
						<AlertTriangle className="h-5 w-5" />
						Emergency RV Dispatch
					</DialogTitle>
				</DialogHeader>

				{isSubmitted ? (
					<div className="flex flex-col items-center gap-4 py-6">
						<div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
							<CheckCircle className="h-6 w-6 text-green-600" />
						</div>
						<div className="text-center space-y-2">
							<h3 className="font-medium text-gray-900">
								Emergency Request Submitted!
							</h3>
							<p className="text-sm text-gray-500">
								Your emergency dispatch request has been submitted. We'll
								broadcast this to all available RV technicians within 50 miles.
								You should receive responses within 1 business day.
							</p>
							<div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
								<p className="text-sm text-blue-800">
									<strong>Important:</strong> This is NOT a roadside assistance
									or towing service. Emergency Dispatch sends urgent alerts to
									all available RV technicians in your area for service calls
									and repairs.
								</p>
							</div>
						</div>
						<Button onClick={handleClose} className="mt-4">
							Close
						</Button>
					</div>
				) : (
					<div className="space-y-6">
						{/* Emergency Types Info */}
						<div className="bg-red-50 border border-red-200 rounded-lg p-4">
							<h3 className="font-medium text-red-800 mb-3">
								When to use Emergency Dispatch:
							</h3>
							<div className="grid grid-cols-1 gap-2 text-sm text-red-700">
								{EMERGENCY_TYPES.map((type, index) => (
									<div key={index} className="flex items-start gap-2">
										<span className="text-base">{type.icon}</span>
										<span>{type.text}</span>
									</div>
								))}
							</div>
						</div>

						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
							{/* Personal Information */}
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div className="space-y-2">
									<label htmlFor="firstName" className="text-sm font-medium">
										First Name
									</label>
									<Input
										id="firstName"
										{...form.register("firstName")}
										error={form.formState.errors.firstName?.message}
									/>
								</div>
								<div className="space-y-2">
									<label htmlFor="lastName" className="text-sm font-medium">
										Last Name
									</label>
									<Input
										id="lastName"
										{...form.register("lastName")}
										error={form.formState.errors.lastName?.message}
									/>
								</div>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div className="space-y-2">
									<label htmlFor="email" className="text-sm font-medium">
										Email
									</label>
									<Input
										id="email"
										type="email"
										{...form.register("email")}
										error={form.formState.errors.email?.message}
									/>
								</div>
								<div className="space-y-2">
									<label htmlFor="phone" className="text-sm font-medium">
										Best Contact Number
									</label>
									<Input
										id="phone"
										type="tel"
										{...form.register("phone")}
										error={form.formState.errors.phone?.message}
										placeholder="(*************"
									/>
								</div>
							</div>

							{/* Location */}
							<div className="space-y-2">
								<div className="flex items-center justify-between">
									<label className="text-sm font-medium">Your Location</label>
									<Button
										type="button"
										variant="outline"
										size="sm"
										onClick={getCurrentLocation}
										className="text-xs"
									>
										Use Current Location
									</Button>
								</div>
								<PlacesAutocomplete
									placeholder="Enter your street address"
									value={form.watch("location")?.address || ""}
									onPlaceSelect={handleLocationSelect}
									locationType="precise"
									className="w-full"
								/>
								{form.formState.errors.location?.address && (
									<p className="text-sm text-red-500">
										{form.formState.errors.location.address.message}
									</p>
								)}
								<div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-2">
									<p className="text-sm text-blue-800">
										<strong>Your Location:</strong>{" "}
										{userLocation ||
											form.watch("location")?.address ||
											"Cove, Texas"}
									</p>
									<p className="text-sm text-blue-700 mt-1">
										We'll broadcast your emergency to all available RV
										technicians within 50 miles. Responding techs will contact
										you directly with availability and pricing.
									</p>
								</div>
							</div>

							{/* Current Location (if different) */}
							<div className="space-y-2">
								<label
									htmlFor="currentLocation"
									className="text-sm font-medium"
								>
									Your Current Location (if different from above)
								</label>
								<Input
									id="currentLocation"
									{...form.register("currentLocation")}
									placeholder="Street address or landmark"
								/>
							</div>

							{/* Emergency Description */}
							<div className="space-y-2">
								<label
									htmlFor="emergencyDescription"
									className="text-sm font-medium"
								>
									Describe Your Emergency *
								</label>
								<Textarea
									id="emergencyDescription"
									rows={4}
									{...form.register("emergencyDescription")}
									error={form.formState.errors.emergencyDescription?.message}
									placeholder="Please describe the critical house system issue (electrical, HVAC, slides, water, propane, etc.)..."
								/>
							</div>

							{/* File Upload */}
							<div className="space-y-2">
								<label className="text-sm font-medium">
									Upload Photos (Optional)
								</label>
								<div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
									<div className="mx-auto w-12 h-12 mb-4">
										<svg
											className="w-full h-full text-gray-400"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
											/>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
											/>
										</svg>
									</div>
									<p className="text-sm text-gray-600">
										Click to upload photos or drag and drop
									</p>
									<p className="text-xs text-gray-500 mt-1">
										Max 5 photos, 10MB each
									</p>
								</div>
								{uploadedFiles.length > 0 && (
									<div className="space-y-2">
										{uploadedFiles.map((file, index) => (
											<div
												key={index}
												className="flex items-center justify-between p-2 bg-gray-50 rounded"
											>
												<span className="text-sm text-gray-700">
													{file.name}
												</span>
												<Button
													type="button"
													variant="ghost"
													size="sm"
													onClick={() =>
														setUploadedFiles((files) =>
															files.filter((_, i) => i !== index)
														)
													}
												>
													<X className="h-4 w-4" />
												</Button>
											</div>
										))}
									</div>
								)}
							</div>

							{/* Important Notice */}
							<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
								<div className="flex items-start gap-2">
									<AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
									<div className="text-sm text-yellow-800">
										<p className="font-medium mb-1">Important:</p>
										<p>
											This is NOT a roadside assistance or towing service.
											Emergency Dispatch sends urgent alerts to all available RV
											technicians in your area. You will still need to pay the
											responding technician for their service call and repairs.
											This feature helps you quickly connect with multiple techs
											when time is critical.
										</p>
									</div>
								</div>
							</div>

							<Button
								type="submit"
								className="w-full bg-red-600 hover:bg-red-700 text-white"
								disabled={isSubmitting}
							>
								{isSubmitting ? "Submitting..." : "Broadcast Emergency"}
							</Button>
						</form>
					</div>
				)}
			</DialogContent>
		</Dialog>
	);
}
