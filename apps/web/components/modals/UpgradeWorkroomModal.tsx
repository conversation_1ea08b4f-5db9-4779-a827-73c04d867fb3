"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import config from "@/config";
import { JobWithUserAndLocation } from "@/types/global";
import { HeadphonesIcon, Truck } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import InviteMoreProviders from "./upgrade-offers/invite-more-providers";
import ProSupport from "./upgrade-offers/pro-support";

type UpgradeVariant = "invite-providers" | "pro-support";

interface RVHelpUpgradeModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	job: JobWithUserAndLocation;
	variant?: UpgradeVariant;
	hasDiscount?: boolean;
}

export default function RVHelpUpgradeModal({
	open,
	onOpenChange,
	job,
	variant = "invite-providers",
	hasDiscount = false
}: RVHelpUpgradeModalProps) {
	const [timeLeft, setTimeLeft] = useState({
		days: 0,
		hours: 0,
		minutes: 0,
		seconds: 0
	});
	const [isExpired, setIsExpired] = useState(false);
	const [isCheckingOut, setIsCheckingOut] = useState(false);
	const [selectedProviders, setSelectedProviders] = useState<string[]>([]);

	useEffect(() => {
		if (!open || !job.created_at) return;

		const expirationTime =
			new Date(job.created_at).getTime() + 72 * 60 * 60 * 1000;

		const updateTimer = () => {
			const now = new Date().getTime();
			const distance = expirationTime - now;

			if (distance < 0) {
				setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
				setIsExpired(true);
				return;
			}

			const days = Math.floor(distance / (1000 * 60 * 60 * 24));
			const hours = Math.floor(
				(distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
			);
			const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
			const seconds = Math.floor((distance % (1000 * 60)) / 1000);

			setTimeLeft({ days, hours, minutes, seconds });
			setIsExpired(false);
		};

		updateTimer();
		const timer = setInterval(updateTimer, 1000);
		return () => clearInterval(timer);
	}, [open, job.created_at]);

	const upgradeToProMembership = async (providersToInvite: string[] = []) => {
		try {
			setIsCheckingOut(true);

			// Use standard membership pricing (matching your $49/$99 structure)
			const priceId = config.stripe.membership.standard.priceId;

			const response = await fetch("/api/stripe/checkout", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					priceId,
					applyDiscount: hasDiscount && !isExpired, // Only apply discount if user is eligible and offer hasn't expired
					serviceRequestId: job.id, // Pass service request ID for custom success URL
					selectedProviders: providersToInvite // Pass selected providers to be invited after successful checkout
				})
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Something went wrong");
			}

			// Redirect to Stripe Checkout
			window.location.href = data.url;
		} catch (error) {
			console.error("Checkout error:", error);
			toast.error("Failed to start checkout process. Please try again.");
		} finally {
			setIsCheckingOut(false);
		}
	};

	const getModalTitle = () => {
		switch (variant) {
			case "pro-support":
				return "Get Personal RV Support";
			case "invite-providers":
			default:
				return "Get Faster Service — Invite More Pros";
		}
	};

	const getModalIcon = () => {
		switch (variant) {
			case "pro-support":
				return HeadphonesIcon;
			case "invite-providers":
			default:
				return Truck;
		}
	};

	const ModalIcon = getModalIcon();

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-2xl mx-auto max-h-[90vh] overflow-y-auto">
				<div className="pt-4 space-y-4">
					{/* Modal Header */}
					<div className="flex items-center gap-2 mb-5 text-sm font-medium text-gray-600">
						<div className="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
							<ModalIcon className="w-4 h-4 text-white" />
						</div>
						<h2 className="text-xl font-semibold text-gray-900 leading-tight">
							{getModalTitle()}
						</h2>
						<p className="text-sm text-gray-500">
							Upgrade to get faster service and more support (Optional).
						</p>
					</div>

					{/* Variant Content */}
					{variant === "invite-providers" ? (
						<InviteMoreProviders
							job={job}
							timeLeft={timeLeft}
							isExpired={isExpired}
							isCheckingOut={isCheckingOut}
							onUpgrade={upgradeToProMembership}
							hasDiscount={hasDiscount}
							onProvidersSelected={setSelectedProviders}
						/>
					) : (
						<ProSupport
							job={job}
							timeLeft={timeLeft}
							isExpired={isExpired}
							isCheckingOut={isCheckingOut}
							onUpgrade={upgradeToProMembership}
							hasDiscount={hasDiscount}
						/>
					)}

					{/* Common Footer Actions */}
					<div className="pt-2 border-t">
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
							disabled={isCheckingOut}
							className="w-full py-3 text-base text-gray-600 hover:bg-gray-50 disabled:opacity-50"
						>
							Not Right Now
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
