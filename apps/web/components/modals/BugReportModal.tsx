"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	DialogTitle
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/lib/hooks/useAuth";
import { AlertCircle, Bug, CheckCircle } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface BugReportModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export function BugReportModal({ open, onOpenChange }: BugReportModalProps) {
	const { user } = useAuth();
	const [type, setType] = useState("bug");
	const [description, setDescription] = useState("");
	const [priority, setPriority] = useState("medium");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSubmitted, setIsSubmitted] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!user) {
			toast.error("You must be logged in to submit feedback");
			return;
		}

		setIsSubmitting(true);

		try {
			const response = await fetch("/api/feedback", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					title: `${type === "bug" ? "Bug Report" : "Feature Request"} - ${priority} priority`,
					description,
					type: type === "bug" ? "bug" : "feature",
					userId: user.id,
					userEmail: user.email,
					userName: `${user.first_name} ${user.last_name}`
				})
			});

			if (!response.ok) {
				throw new Error("Failed to submit feedback");
			}

			setIsSubmitted(true);
			setDescription("");
			toast.success("Thank you for your feedback!");
		} catch (error) {
			console.error("Error submitting feedback:", error);
			toast.error("Failed to submit feedback. Please try again.");
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		if (isSubmitted) {
			setIsSubmitted(false);
			setType("bug");
			setPriority("medium");
		}
		onOpenChange(false);
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-xl">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						{type === "bug" ? (
							<Bug className="h-5 w-5" />
						) : (
							<AlertCircle className="h-5 w-5" />
						)}
						Submit {type === "bug" ? "Bug Report" : "Feature Request"}
					</DialogTitle>
					<p className="text-gray-600">
						Help us improve RV Help by reporting bugs or suggesting new
						features.
					</p>
					{!user && (
						<div className="text-sm text-amber-600 bg-amber-50 p-3 rounded">
							<p className="mb-2">You must be logged in to submit feedback.</p>
							<Link
								href="/register"
								className="text-blue-600 hover:text-blue-800 underline font-medium"
							>
								Create an account
							</Link>
							{" or "}
							<Link
								href="/login"
								className="text-blue-600 hover:text-blue-800 underline font-medium"
							>
								sign in
							</Link>
						</div>
					)}
				</DialogHeader>

				{isSubmitted ? (
					<div className="flex flex-col items-center gap-6 py-8">
						<div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center">
							<CheckCircle className="h-8 w-8 text-green-600" />
						</div>
						<div className="text-center space-y-2 max-w-md">
							<h3 className="text-xl font-medium text-gray-900">
								Feedback Submitted Successfully!
							</h3>
							<p className="text-gray-600">
								Thank you for your feedback. We'll review it and get back to you
								if needed.
							</p>
						</div>
						<Button onClick={handleClose} className="mt-2">
							Close
						</Button>
					</div>
				) : (
					<form onSubmit={handleSubmit} className="space-y-6 pt-4">
						<div className="space-y-4">
							<RadioGroup
								value={type}
								onValueChange={setType}
								className="flex gap-4"
							>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="bug" id="bug" />
									<Label htmlFor="bug">Bug Report</Label>
								</div>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="feature" id="feature" />
									<Label htmlFor="feature">Feature Request</Label>
								</div>
							</RadioGroup>

							<RadioGroup
								value={priority}
								onValueChange={setPriority}
								className="flex gap-4"
							>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="low" id="low" />
									<Label htmlFor="low">Low Priority</Label>
								</div>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="medium" id="medium" />
									<Label htmlFor="medium">Medium Priority</Label>
								</div>
								<div className="flex items-center space-x-2">
									<RadioGroupItem value="high" id="high" />
									<Label htmlFor="high">High Priority</Label>
								</div>
							</RadioGroup>

							<Textarea
								value={description}
								onChange={(e) => setDescription(e.target.value)}
								placeholder={
									type === "bug"
										? "Describe the bug and steps to reproduce..."
										: "Describe your feature suggestion..."
								}
								className="h-32"
								required
								minLength={10}
							/>
							{description.length > 0 && description.length < 10 && (
								<p className="text-sm text-red-600">
									Description must be at least 10 characters (
									{description.length}/10)
								</p>
							)}
						</div>

						<Button
							type="submit"
							className="w-full flex items-center justify-center gap-2"
							disabled={isSubmitting || !user || description.length < 10}
						>
							{isSubmitting
								? "Submitting..."
								: !user
									? "Login Required"
									: description.length < 10
										? "Description too short"
										: "Submit Feedback"}
						</Button>
					</form>
				)}
			</DialogContent>
		</Dialog>
	);
}
