"use client";

import { useState } from "react";

interface Review {
    text: string;
    provider: string;
    title: string;
}

interface ReviewCardProps {
    review: Review;
}

const MAX_LENGTH = 200; // Characters to show before truncating

export default function ReviewCard({ review }: ReviewCardProps) {
    const [isExpanded, setIsExpanded] = useState(false);

    const shouldTruncate = review.text.length > MAX_LENGTH;
    const displayText = shouldTruncate && !isExpanded
        ? review.text.substring(0, MAX_LENGTH) + "..."
        : review.text;

    return (
        <div className="bg-white border rounded-lg shadow p-6 flex flex-col h-full">
            <p className="italic text-lg mb-4">
                {'"'}{displayText}{'"'}
            </p>

            {shouldTruncate && (
                <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="text-[#43806c] hover:text-[#2c5446] text-sm font-medium self-start mb-2 underline"
                >
                    {isExpanded ? "Read less" : "Read more"}
                </button>
            )}

            <div className="mt-auto text-sm text-gray-600">
                — Review for <span className="font-semibold">{review.provider}</span>, {review.title}
            </div>
        </div>
    );
} 