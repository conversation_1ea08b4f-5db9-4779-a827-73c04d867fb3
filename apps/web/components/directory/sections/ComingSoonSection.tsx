"use client";

import { ListingAvatar } from "@/components/directory/listing-card/ListingAvatar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { getCategoryName } from "@/lib/categories";
import { cn } from "@/lib/utils";
import { ComingSoonApiResponse, ComingSoonListing } from "@/types/coming-soon";
import { RVHelpVerificationLevel } from "@prisma/client";
import { CalendarIcon, MapPinIcon, StarIcon } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

interface ComingSoonSectionProps {
	latitude: number;
	longitude: number;
	category: string;
	subcategory?: string;
	radius?: string;
}

const ComingSoonCard = ({ listing }: { listing: ComingSoonListing }) => {
	const startDate = new Date(listing.upcoming_location.start_date);
	const endDate = listing.upcoming_location.end_date
		? new Date(listing.upcoming_location.end_date)
		: null;

	const formatDate = (date: Date) => {
		return date.toLocaleDateString("en-US", {
			month: "short",
			day: "numeric"
		});
	};

	const formatDateRange = () => {
		if (endDate && endDate.getTime() !== startDate.getTime()) {
			return `${formatDate(startDate)} - ${formatDate(endDate)}`;
		}
		return `Starting ${formatDate(startDate)}`;
	};

	return (
		<Link href={`/providers/${listing.slug}`} className="block">
			<Card className="h-full hover:shadow-md transition-shadow duration-200 group border border-gray-200">
				<CardHeader className="pb-3">
					<div className="flex items-start gap-3">
						<ListingAvatar
							profileImage={listing.profile_image}
							firstName={listing.first_name}
							lastName={listing.last_name}
							size="sm"
							isVerified={
								listing.rv_help_verification_level ===
								RVHelpVerificationLevel.VERIFIED
							}
							isCertifiedPro={
								listing.rv_help_verification_level ===
								RVHelpVerificationLevel.CERTIFIED_PRO
							}
						/>
						<div className="flex-1 min-w-0">
							<CardTitle className="text-sm font-semibold text-gray-900 group-hover:text-green-700 transition-colors line-clamp-1">
								{listing.business_name}
							</CardTitle>
							<p className="text-xs text-gray-600 mt-1">
								{listing.first_name} {listing.last_name}
							</p>
						</div>
					</div>
				</CardHeader>
				<CardContent className="pt-0 space-y-2">
					{/* Rating */}
					{listing.rating && listing.num_reviews > 0 && (
						<div className="flex items-center gap-1">
							<StarIcon className="w-3 h-3 text-yellow-400 fill-current" />
							<span className="text-xs font-medium text-gray-700">
								{listing.rating.toFixed(1)}
							</span>
							<span className="text-xs text-gray-500">
								({listing.num_reviews})
							</span>
						</div>
					)}

					{/* Date Range with Coming Soon Badge */}
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-1 text-xs text-orange-700">
							<CalendarIcon className="w-3 h-3" />
							<span className="font-medium">{formatDateRange()}</span>
						</div>
					</div>

					{/* Location */}
					<div className="flex items-center gap-1 text-xs text-gray-600">
						<MapPinIcon className="w-3 h-3" />
						<span>
							{listing.upcoming_location.city},{" "}
							{listing.upcoming_location.state}
						</span>
					</div>

					{/* Description */}
					{listing.short_description && (
						<p className="text-xs text-gray-600 line-clamp-2">
							{listing.short_description}
						</p>
					)}
				</CardContent>
			</Card>
		</Link>
	);
};

const ComingSoonSkeleton = () => (
	<Card className="h-full">
		<CardHeader className="pb-3">
			<div className="flex items-start gap-3">
				<Skeleton className="w-10 h-10 rounded-full" />
				<div className="flex-1 space-y-2">
					<Skeleton className="h-4 w-32" />
					<Skeleton className="h-3 w-24" />
				</div>
			</div>
		</CardHeader>
		<CardContent className="pt-0 space-y-2">
			<Skeleton className="h-3 w-20" />
			<Skeleton className="h-3 w-28" />
			<Skeleton className="h-3 w-24" />
			<Skeleton className="h-8 w-16" />
		</CardContent>
	</Card>
);

export default function ComingSoonSection({
	latitude,
	longitude,
	category,
	subcategory,
	radius = "100"
}: ComingSoonSectionProps) {
	const [listings, setListings] = useState<ComingSoonListing[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchComingSoonListings = async () => {
			try {
				setIsLoading(true);
				setError(null);

				const params = new URLSearchParams({
					lat: latitude.toString(),
					lng: longitude.toString(),
					category,
					radius,
					...(subcategory && { subcategory })
				});

				const response = await fetch(`/api/listings/coming-soon?${params}`);

				if (!response.ok) {
					throw new Error("Failed to fetch coming soon providers");
				}

				const data: ComingSoonApiResponse = await response.json();

				if (data.success) {
					setListings(data.listings);
				} else {
					throw new Error("Failed to fetch coming soon providers");
				}
			} catch (err) {
				console.error("Error fetching coming soon listings:", err);
				setError(err instanceof Error ? err.message : "An error occurred");
			} finally {
				setIsLoading(false);
			}
		};

		if (latitude && longitude && category) {
			fetchComingSoonListings();
		}
	}, [latitude, longitude, category, subcategory, radius]);

	// Don't render if still loading, loading failed, or no listings
	if (isLoading || error || listings.length === 0) {
		return null;
	}

	const categoryName = getCategoryName(category);

	const getGridClasses = () => {
		const count = listings.length;
		if (count === 1) return "grid-cols-1";
		if (count === 2) return "grid-cols-1 md:grid-cols-2";
		if (count === 3) return "grid-cols-1 md:grid-cols-3";
		// For 4+ items, use horizontal scroll on mobile, grid on desktop
		return "grid-cols-1 md:grid-cols-4";
	};

	const shouldUseCarousel = listings.length > 4;

	return (
		<div className="mb-6 bg-yellow-50 rounded-lg p-6 border border-yellow-200 pb-4">
			<div className="mb-4">
				<h2 className="text-lg font-semibold text-yellow-800 mb-1">
					Coming to Your Area Soon
				</h2>
				<p className="text-sm text-yellow-700">
					{categoryName} providers planning to visit your area in the next 2
					weeks
				</p>
			</div>

			{shouldUseCarousel ? (
				// Horizontal scroll for 4+ items
				<div className="relative">
					<div className="flex gap-4 overflow-x-auto scrollbar-hide pb-2">
						{listings.map((listing) => (
							<div key={listing.id} className="flex-none w-64">
								<ComingSoonCard listing={listing} />
							</div>
						))}
					</div>
					{/* Gradient fade on right side to indicate more content */}
					<div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-yellow-50 to-transparent pointer-events-none" />
				</div>
			) : (
				// Responsive grid for 1-4 items
				<div className={cn("grid gap-4", getGridClasses())}>
					{listings.map((listing) => (
						<div key={listing.id} className="w-64">
							<ComingSoonCard listing={listing} />
						</div>
					))}
				</div>
			)}
		</div>
	);
}
