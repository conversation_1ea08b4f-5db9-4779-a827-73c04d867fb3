import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";

interface VacationModeAlertProps {
	vacationMode: any; // JSON type from database
}

export default function VacationModeAlert({
	vacationMode
}: VacationModeAlertProps) {
	// Handle JSON type from database
	if (
		!vacationMode ||
		typeof vacationMode !== "object" ||
		!vacationMode.enabled
	) {
		return null;
	}

	return (
		<Alert className="mb-6 border-amber-200 bg-amber-50">
			<AlertTriangle className="h-4 w-4 text-amber-600" />
			<AlertDescription className="text-amber-800">
				<span className="font-medium">
					This provider is currently unavailable.
				</span>
				{vacationMode.message && (
					<span className="block mt-1 text-sm">{vacationMode.message}</span>
				)}
			</AlertDescription>
		</Alert>
	);
}
