"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { ListingWithLocation } from "@/types/global";
import { Calendar, MapPin } from "lucide-react";
import { useEffect, useState } from "react";

const ServiceAreasSection = ({ listing }: { listing: ListingWithLocation }) => {
	const [isMounted, setIsMounted] = useState(false);
	const [activeLocation, setActiveLocation] = useState<any>(null);
	const [sortedLocations, setSortedLocations] = useState(listing.locations);

	// Filter out locations where travel dates have passed
	const filterUpcomingLocations = (locations: any[]) => {
		const now = new Date();
		return locations.filter(location => {
			// Always include default locations (no date restrictions)
			if (location.default) return true;

			// Include locations with no dates (permanent locations)
			if (!location.start_date && !location.end_date) return true;

			// Include locations where end_date hasn't passed yet
			if (location.end_date && new Date(location.end_date) >= now) return true;

			// Include locations with only start_date that's in the future
			if (location.start_date && !location.end_date && new Date(location.start_date) >= now) return true;

			return false;
		});
	};

	// Local function to determine active location without modifying the original listing
	const getActiveLocation = (locations: any[]) => {
		const now = new Date();

		if (locations.length === 1) {
			return locations[0];
		}

		// Find location that's currently active (between start_date and end_date)
		const result = locations.find(
			(loc) =>
				loc.start_date &&
				loc.end_date &&
				new Date(loc.start_date) <= now &&
				new Date(loc.end_date) >= now
		);
		if (result) {
			return result;
		}

		// If no active location found, use the default location
		const defaultLocation = locations.find((loc) => loc.default === true);
		if (defaultLocation) {
			return defaultLocation;
		}

		// If no default location, use the first location
		return locations[0];
	};

	// Set isMounted to true after the component mounts and determine active location
	useEffect(() => {
		setIsMounted(true);

		// Filter locations to only show upcoming/current travel dates
		const filteredLocations = filterUpcomingLocations(listing.locations);

		// Determine active location on client-side only to prevent hydration mismatch
		const active = getActiveLocation(filteredLocations);
		setActiveLocation(active);

		// Sort locations with active location first
		const sorted = [...filteredLocations].sort((a, b) => {
			if (a.id === active?.id) return -1;
			if (b.id === active?.id) return 1;
			return 0;
		});
		setSortedLocations(sorted);
	}, [listing]);

	// Format date to readable string
	const formatDate = (dateString) => {
		if (!dateString) return "";
		return new Date(dateString).toLocaleDateString(undefined, {
			year: "numeric",
			month: "short",
			day: "numeric"
		});
	};

	// Create a natural service area description
	const getServiceDescription = (location) => {
		const serviceAreaDescription = `Serving ${location.city}${location.state ? `, ${location.state}` : ""} area`;
		if (location.description)
			return `${location.description} (${serviceAreaDescription})`;
		return serviceAreaDescription;
	};

	// Filter locations for the initial render (before client-side hydration)
	const initialFilteredLocations = filterUpcomingLocations(listing.locations);

	return (
		<Card className="mb-8 shadow-sm">
			<CardHeader className="border-b border-gray-100">
				<CardTitle className="text-xl font-semibold text-gray-800">
					Service Areas
				</CardTitle>
			</CardHeader>

			<CardContent className="pt-6">
				{!isMounted ? (
					<div className="space-y-6">
						{initialFilteredLocations.map((location, index) => (
							<div
								key={location.id || index}
								className="p-4 rounded-lg bg-gray-50 border border-gray-100"
							>
								<div className="flex justify-between items-start mb-2">
									<h3 className="font-medium text-lg">
										{getServiceDescription(location)}
									</h3>
									<div className="flex gap-2">
										{location.default && (
											<Badge
												variant="outline"
												className="bg-primary/10 text-primary border-primary/20"
											>
												Primary
											</Badge>
										)}
									</div>
								</div>

								<div className="space-y-3">
									<div className="flex items-start gap-2 text-gray-700">
										<MapPin className="h-4 w-4 shrink-0 mt-1 text-[#43806c]" />
										<div>
											<div>
												Travels up to{" "}
												<span className="font-medium">
													{location.radius} miles
												</span>{" "}
												from {location.city}, {location.state}
											</div>
											{location.inspection_radius &&
												location.inspection_radius !== location.radius && (
													<div className="text-sm text-gray-600">
														• Up to{" "}
														<span className="font-medium">
															{location.inspection_radius} miles
														</span>{" "}
														for inspections
													</div>
												)}
											{location.emergency_radius &&
												location.emergency_radius !== location.radius && (
													<div className="text-sm text-gray-600">
														• Up to{" "}
														<span className="font-medium">
															{location.emergency_radius} miles
														</span>{" "}
														for emergency service
													</div>
												)}
										</div>
									</div>

									{!location.default &&
										location.start_date &&
										location.end_date && (
											<div className="flex items-center gap-2 text-gray-700">
												<Calendar className="h-4 w-4 shrink-0 text-[#43806c]" />
												<span>
													Available from{" "}
													<span className="font-medium">
														{formatDate(location.start_date)}
													</span>{" "}
													to{" "}
													<span className="font-medium">
														{formatDate(location.end_date)}
													</span>
												</span>
											</div>
										)}
								</div>
							</div>
						))}
					</div>
				) : sortedLocations && sortedLocations.length > 0 ? (
					<div className="space-y-6">
						{sortedLocations.map((location, index) => (
							<div
								key={location.id || index}
								className={`p-4 rounded-lg ${activeLocation?.id === location.id
									? "bg-green-50 border border-green-100"
									: location.default
										? "bg-primary/5 border border-primary/20"
										: "bg-gray-50 border border-gray-100"
									}`}
							>
								<div className="flex justify-between items-start mb-2">
									<h3 className="font-medium text-lg">
										{getServiceDescription(location)}
									</h3>
									<div className="flex gap-2">
										{activeLocation?.id === location.id ? (
											<Badge
												variant="outline"
												className="bg-green-100 text-green-800 border-green-200"
											>
												Active Now
											</Badge>
										) : (
											<Badge
												variant="outline"
												className="bg-gray-100 text-gray-800 border-gray-200"
											>
												Not Active
											</Badge>
										)}
										{location.default && (
											<Badge
												variant="outline"
												className="bg-primary/10 text-primary border-primary/20"
											>
												Primary
											</Badge>
										)}
									</div>
								</div>

								<div className="space-y-3">
									<div className="flex items-start gap-2 text-gray-700">
										<MapPin className="h-4 w-4 shrink-0 mt-1 text-[#43806c]" />
										<div>
											<div>
												Travels up to{" "}
												<span className="font-medium">
													{location.radius} miles
												</span>{" "}
												from {location.city}, {location.state}
											</div>
											{location.inspection_radius &&
												location.inspection_radius !== location.radius && (
													<div className="text-sm text-gray-600">
														• Up to{" "}
														<span className="font-medium">
															{location.inspection_radius} miles
														</span>{" "}
														for inspections
													</div>
												)}
											{location.emergency_radius &&
												location.emergency_radius !== location.radius && (
													<div className="text-sm text-gray-600">
														• Up to{" "}
														<span className="font-medium">
															{location.emergency_radius} miles
														</span>{" "}
														for emergency service
													</div>
												)}
										</div>
									</div>

									{!location.default &&
										location.start_date &&
										location.end_date && (
											<div className="flex items-center gap-2 text-gray-700">
												<Calendar className="h-4 w-4 shrink-0 text-[#43806c]" />
												<span>
													Available from{" "}
													<span className="font-medium">
														{formatDate(location.start_date)}
													</span>{" "}
													to{" "}
													<span className="font-medium">
														{formatDate(location.end_date)}
													</span>
												</span>
											</div>
										)}
								</div>
							</div>
						))}
					</div>
				) : (
					<div className="text-center py-6 text-gray-500">
						No service areas defined
					</div>
				)}
			</CardContent>
		</Card>
	);
};

export default ServiceAreasSection;
