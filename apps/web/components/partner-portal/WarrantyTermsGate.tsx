import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface WarrantyTermsGateProps {
    companyName: string;
    onAccept: () => Promise<void>;
    isLoading?: boolean;
    customerName?: string;
    location?: string;
    rvDetails?: string;
    issueDescription?: string;
    leadId: string;
    open: boolean;
    onOpenChange?: (open: boolean) => void;
}

export function WarrantyTermsGate({
    companyName,
    onAccept,
    isLoading = false,
    customerName = "Customer",
    location = "Location Available",
    rvDetails = "RV Details Available",
    issueDescription = "Issue details will be available after accepting this lead.",
    leadId,
    open,
    onOpenChange
}: WarrantyTermsGateProps) {
    const [acceptsTerms, setAcceptsTerms] = useState(true); // Default to checked like in the HTML
    const router = useRouter();

    const handleAccept = async () => {
        await onAccept();
        // Reset form state
        setAcceptsTerms(true);
    };

    const canAccept = acceptsTerms && !isLoading;

    return (
        <Dialog open={open} onOpenChange={onOpenChange} modal={true} >
            <DialogContent className="sm:max-w-lg">
                {/* Header */}
                <DialogHeader className="bg-primary text-white p-5 -mx-6 mb-6 relative">
                    <DialogTitle className="text-xl font-semibold leading-tight mb-1 text-white">
                        Accept Terms to View {companyName} Warranty Lead
                    </DialogTitle>
                    <p className="text-sm opacity-90">
                        Review and accept warranty terms to see full job details
                    </p>
                </DialogHeader>

                {/* Content */}
                <div className="space-y-6">
                    {/* Lead Preview */}
                    <div className="bg-gray-100 rounded-lg p-5 border border-gray-200">
                        <h3 className="text-base font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <span className="text-emerald-600 text-xl">✓</span>
                            Accept Terms to View Lead Details
                        </h3>
                        <p className="text-sm text-gray-600 mb-2 leading-relaxed">
                            By accepting these terms, you'll be able to view the full warranty request details and decide if you want to take the job.
                        </p>
                        <p className="text-sm font-semibold text-emerald-700 mt-3">
                            This does NOT commit you to completing the work.
                        </p>
                    </div>

                    {/* Info Note */}
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-center text-sm text-blue-800">
                        ℹ️ You can review the job details and decline if it's not a good fit
                    </div>

                    {/* Green Terms Section */}
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <strong className="text-green-900">
                            Terms for {companyName} Warranty Leads:
                        </strong>
                        <div className="mt-2 space-y-1 text-green-800">
                            <div>• Fill out cause, complaint, and correction with photos</div>
                            <div>• Use RV Help's invoicing process for warranty jobs</div>
                            <div>• Communicate with customers through RV Help messaging when possible</div>
                        </div>
                    </div>

                    {/* Terms Section */}
                    <div>
                        <h4 className="text-sm font-semibold text-gray-700 mb-3">
                            What You're Agreeing To:
                        </h4>
                        <ul className="space-y-2">
                            <li className="flex items-start gap-2 text-sm text-gray-600">
                                <span className="text-gray-500 text-base flex-shrink-0 mt-0.5">📤</span>
                                <span>More warranty leads sent directly from {companyName}</span>
                            </li>
                            <li className="flex items-start gap-2 text-sm text-gray-600">
                                <span className="text-gray-500 text-base flex-shrink-0 mt-0.5">📞</span>
                                <span>Guaranteed service call fee for completed jobs</span>
                            </li>
                            <li className="flex items-start gap-2 text-sm text-gray-600">
                                <span className="text-gray-500 text-base flex-shrink-0 mt-0.5">📄</span>
                                <span>Simple complaint, cause, & correction forms in RV Help</span>
                            </li>
                            <li className="flex items-start gap-2 text-sm text-gray-600">
                                <span className="text-gray-500 text-base flex-shrink-0 mt-0.5">💰</span>
                                <span>Fast payouts through RV Help's invoice generator</span>
                            </li>
                            <li className="flex items-start gap-2 text-sm text-gray-600">
                                <span className="text-gray-500 text-base flex-shrink-0 mt-0.5">🚚</span>
                                <span>Expedited parts shipping for warranty repairs</span>
                            </li>
                            <li className="flex items-start gap-2 text-sm text-gray-600">
                                <span className="text-gray-500 text-base flex-shrink-0 mt-0.5">💬</span>
                                <span>Direct communication with {companyName} support team</span>
                            </li>
                        </ul>
                    </div>

                    {/* Agreement Checkbox */}
                    <div className="flex items-start gap-2 text-xs text-gray-500">
                        <Checkbox
                            id="agree"
                            checked={acceptsTerms}
                            onCheckedChange={(checked) => setAcceptsTerms(checked === true)}
                            className="mt-0.5 flex-shrink-0"
                        />
                        <label htmlFor="agree" className="leading-relaxed">
                            By clicking Accept, you agree to the terms above and will be eligible to start receiving warranty leads directly from {companyName}.
                        </label>
                    </div>

                    {/* Action Buttons */}
                    <div className="space-y-3">
                        <Button
                            onClick={handleAccept}
                            disabled={!canAccept}
                            className="w-full bg-primary hover:bg-primary/90 text-white text-base py-3 rounded-lg font-semibold transition-colors"
                        >
                            {isLoading ? "Processing..." : "Accept Terms & View Full Details →"}
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => router.push("/provider/leads")}
                            disabled={isLoading}
                            className="w-full bg-white hover:bg-gray-50 text-gray-500 border border-gray-200 text-base py-3 rounded-lg font-semibold transition-colors"
                        >
                            Not Right Now
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
