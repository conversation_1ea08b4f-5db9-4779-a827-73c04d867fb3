import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	<PERSON>ert<PERSON>riangle,
	BookOpen,
	Lock,
	Shield,
	Trophy,
	Zap
} from "lucide-react";
import { useState } from "react";

interface WarrantyCertificationNoticeProps {
	companyName: string;
	certificationName: string;
	onStartCertification: () => void;
	className?: string;
}

export function WarrantyCertificationNotice({
	companyName,
	certificationName,
	onStartCertification,
	className = ""
}: WarrantyCertificationNoticeProps) {
	const [isStarting, setIsStarting] = useState(false);

	const handleStartCertification = async () => {
		setIsStarting(true);
		try {
			await onStartCertification();
		} catch (error) {
			console.error("Error starting certification:", error);
		} finally {
			setIsStarting(false);
		}
	};

	const getCertificationDisplayName = (name: string) => {
		switch (name) {
			case "keystone-warranty":
				return "Keystone Warranty Certification";
			default:
				return name.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
		}
	};

	return (
		<Card className={`border-orange-200 bg-orange-50 ${className}`}>
			<CardHeader className="pb-4">
				<div className="flex items-center gap-3">
					<div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
						<Shield className="w-5 h-5 text-white" />
					</div>
					<div className="flex-1">
						<CardTitle className="text-lg text-orange-900 flex items-center gap-2">
							{companyName} Partner Portal Job
							<Badge
								variant="secondary"
								className="bg-orange-100 text-orange-800 border-orange-200"
							>
								Partner Program
							</Badge>
						</CardTitle>
						<p className="text-sm text-orange-700 mt-1">
							This is a {companyName} warranty job through our new partner
							portal program
						</p>
					</div>
				</div>
			</CardHeader>
			<CardContent className="space-y-4">
				<Alert className="border-orange-200 bg-orange-100">
					<AlertTriangle className="h-4 w-4 text-orange-600" />
					<AlertDescription className="text-orange-800">
						<strong>Certification Required:</strong> To view customer contact
						information and message this customer, you must complete the{" "}
						{getCertificationDisplayName(certificationName)} training and accept
						our terms & conditions.
					</AlertDescription>
				</Alert>

				<div className="bg-white rounded-lg p-4 border border-orange-200">
					<h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
						<Lock className="w-4 h-4 text-orange-600" />
						Information Currently Hidden
					</h4>
					<ul className="text-sm text-gray-600 space-y-1">
						<li>• Customer contact information (phone, email)</li>
						<li>• Direct messaging capabilities</li>
						<li>• Full warranty claim details</li>
					</ul>
				</div>

				<div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
					<h4 className="font-medium text-green-900 mb-3 flex items-center gap-2">
						<Trophy className="w-4 h-4 text-green-600" />
						Benefits After Certification
					</h4>
					<ul className="text-sm text-green-800 space-y-1">
						<li>• Access to {companyName} warranty jobs</li>
						<li>• Priority support and faster payouts</li>
						<li>
							• Official certification badge on your profile (if eligible)
						</li>
						<li>• Direct access to {companyName} technical support</li>
					</ul>
				</div>

				<div className="flex gap-3 pt-2">
					<Button
						onClick={handleStartCertification}
						disabled={isStarting}
						className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-all hover:shadow-lg hover:-translate-y-0.5 flex items-center justify-center gap-2"
					>
						{isStarting ? (
							<>
								<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
								Starting Training...
							</>
						) : (
							<>
								<BookOpen className="w-4 h-4" />
								Start Certification Training
							</>
						)}
					</Button>
					<Button
						variant="outline"
						onClick={() => window.open("/provider/partner-portal", "_blank")}
						className="bg-white hover:bg-gray-50 text-gray-700 font-medium py-3 px-6 rounded-lg border border-gray-300 transition-colors flex items-center justify-center gap-2"
					>
						<Zap className="w-4 h-4" />
						Learn More
					</Button>
				</div>

				<p className="text-xs text-orange-600 text-center">
					Training takes approximately 15-20 minutes to complete
				</p>
			</CardContent>
		</Card>
	);
}
