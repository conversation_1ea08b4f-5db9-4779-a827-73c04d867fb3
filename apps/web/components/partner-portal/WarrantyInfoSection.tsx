import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Clock, Cog, Factory } from "lucide-react";

interface WarrantyInfoSectionProps {
	manufacturer: string;
	approvedHours: number | null;
	componentType: string;
	companyName: string;
	className?: string;
}

export function WarrantyInfoSection({
	manufacturer,
	approvedHours,
	componentType,
	companyName,
	className = ""
}: WarrantyInfoSectionProps) {
	return (
		<Card
			className={`border-[#43806c]/30 bg-gradient-to-r from-[#43806c]/5 to-emerald-50/50 ${className}`}
		>
			<CardHeader className="bg-[#43806c] text-white pb-3">
				<CardTitle className="text-lg flex items-center gap-2">
					<Cog className="w-5 h-5" />
					{companyName} Warranty Work
				</CardTitle>
			</CardHeader>
			<CardContent className="p-4">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					{/* Manufacturer */}
					<div className="flex items-center gap-3">
						<div className="w-10 h-10 bg-[#43806c] rounded-lg flex items-center justify-center flex-shrink-0">
							<Factory className="w-5 h-5 text-white" />
						</div>
						<div className="min-w-0 flex-1">
							<p className="text-xs font-medium text-gray-600 uppercase tracking-wide">
								Manufacturer
							</p>
							<p className="text-sm font-semibold text-gray-900 truncate">
								{manufacturer}
							</p>
						</div>
					</div>

					{/* Pre-approved Hours */}
					<div className="flex items-center gap-3">
						<div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center flex-shrink-0">
							<Clock className="w-5 h-5 text-white" />
						</div>
						<div className="min-w-0 flex-1">
							<p className="text-xs font-medium text-gray-600 uppercase tracking-wide">
								Pre-approved Hours
							</p>
							<p className="text-sm font-semibold text-gray-900">
								{approvedHours
									? `${approvedHours} hrs + Service call fee`
									: "TBD"}
							</p>
						</div>
					</div>

					{/* Affected Component */}
					<div className="flex items-center gap-3">
						<div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
							<Cog className="w-5 h-5 text-white" />
						</div>
						<div className="min-w-0 flex-1">
							<p className="text-xs font-medium text-gray-600 uppercase tracking-wide">
								Affected Component
							</p>
							<p className="text-sm font-semibold text-gray-900 truncate">
								{componentType}
							</p>
						</div>
					</div>
				</div>

				{/* Warranty Badge */}
				<div className="mt-3 pt-3 border-t border-gray-200/50">
					<Badge className="bg-green-100 text-green-800 border-green-200 text-xs px-2 py-1">
						Authorized Warranty Work
					</Badge>
				</div>
			</CardContent>
		</Card>
	);
}
