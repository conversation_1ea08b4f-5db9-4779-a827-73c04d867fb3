import { Alert, AlertDescription } from "@/components/ui/alert";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	AlertTriangle,
	Briefcase,
	CreditCard,
	Package,
	Phone,
	Trophy,
	Zap
} from "lucide-react";

interface KeystonePartnershipModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export default function KeystonePartnershipModal({
	isOpen,
	onClose
}: KeystonePartnershipModalProps) {
	const handleStartEnrollment = async () => {
		try {
			// Start the certification process first
			const response = await fetch("/api/provider/certifications", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					certificationName: "keystone-warranty"
				})
			});

			if (!response.ok) {
				const error = await response.json();
				console.error("Failed to start certification:", error);
				alert("Failed to start certification. Please try again.");
				return;
			}

			// Close the modal
			onClose();

			// Navigate to the Keystone certification training
			window.location.href = "/provider/certifications/keystone-warranty";
		} catch (error) {
			console.error("Error starting certification:", error);
			alert("Failed to start certification. Please try again.");
		}
	};

	const handleLearnMore = () => {
		// Could open a more detailed page or external link
		window.open("https://www.keystonerv.com/", "_blank");
	};

	const benefits = [
		{
			icon: Package,
			title: "FREE Parts & Expedited Shipping",
			description:
				"Get Keystone OEM parts at no cost with priority shipping directly to your location"
		},
		{
			icon: CreditCard,
			title: "Fast Warranty Payouts",
			description:
				"Receive payment within 5-7 business days instead of the typical 30-45 days"
		},
		{
			icon: Trophy,
			title: "Keystone Pre-Authorized Status",
			description:
				"Stand out to customers with official Keystone certification on your profile",
			badge: true
		},
		{
			icon: Phone,
			title: "Direct Technical Support",
			description:
				"Skip the queue with dedicated hotline access to Keystone tech experts"
		},
		{
			icon: Briefcase,
			title: "Priority Job Matching",
			description:
				"Get first access to Keystone warranty jobs in your service area"
		}
	];

	const requirements = [
		"Verified RV Help Provider status",
		"Certified RV Technician credentials",
		"Complete Keystone Pre-Authorization Training",
		"Minimum 4.75 star rating on RV Help"
	];

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-2xl">
				<DialogHeader className="text-center">
					<div className="w-18 h-18 bg-orange-500 rounded-xl flex items-center justify-center mx-auto mb-5 shadow-lg">
						<span className="text-3xl font-bold text-white">K</span>
					</div>
					<DialogTitle className="text-2xl font-semibold text-gray-900 mb-3">
						Join the Keystone Partnership
					</DialogTitle>
					<DialogDescription className="text-gray-600">
						Unlock exclusive benefits, priority support, and increased earning
						potential with Keystone RV
					</DialogDescription>
				</DialogHeader>

				{/* Benefits */}
				<div className="space-y-4 mb-7">
					{benefits.map((benefit, index) => {
						const Icon = benefit.icon;
						return (
							<div
								key={index}
								className="flex items-start p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
							>
								<div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
									<Icon className="w-5 h-5 text-white" />
								</div>
								<div className="flex-1">
									<h3 className="font-medium text-gray-900 mb-1 flex items-center">
										{benefit.title}
										{benefit.badge && (
											<span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">
												Pre-Authorized
											</span>
										)}
									</h3>
									<p className="text-sm text-gray-600 leading-relaxed">
										{benefit.description}
									</p>
								</div>
							</div>
						);
					})}
				</div>

				{/* Requirements Alert */}
				<Alert className="mb-6 border-yellow-200 bg-yellow-50">
					<AlertTriangle className="h-4 w-4 text-yellow-600" />
					<AlertDescription className="text-yellow-800">
						<div className="font-medium mb-2">Eligibility Requirements</div>
						<ul className="space-y-1">
							{requirements.map((requirement, index) => (
								<li key={index} className="flex items-center text-sm">
									<span className="text-green-600 font-bold mr-2">✓</span>
									{requirement}
								</li>
							))}
						</ul>
					</AlertDescription>
				</Alert>

				{/* Action Buttons */}
				<div className="flex gap-3">
					<button
						onClick={handleStartEnrollment}
						className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-all hover:shadow-lg hover:-translate-y-0.5 flex items-center justify-center gap-2"
					>
						<Zap className="w-4 h-4" />
						Start Enrollment
					</button>
					<button
						onClick={handleLearnMore}
						className="flex-1 bg-white hover:bg-gray-50 text-gray-700 font-medium py-3 px-6 rounded-lg border border-gray-300 transition-colors"
					>
						Learn More
					</button>
				</div>
			</DialogContent>
		</Dialog>
	);
}
