import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { EyeOff, Mail, MapPin, Phone, Shield, User } from "lucide-react";

interface RedactedContactInfoProps {
	firstName: string;
	lastName: string;
	location?: {
		address?: string;
		city?: string;
		state?: string;
	};
	className?: string;
}

export function RedactedContactInfo({
	firstName,
	lastName,
	location,
	className = ""
}: RedactedContactInfoProps) {
	return (
		<Card className={`border-gray-200 bg-gray-50 ${className}`}>
			<CardHeader className="pb-4">
				<div className="flex items-center gap-3">
					<div className="w-10 h-10 bg-gray-500 rounded-lg flex items-center justify-center">
						<Shield className="w-5 h-5 text-white" />
					</div>
					<div className="flex-1">
						<CardTitle className="text-lg text-gray-900 flex items-center gap-2">
							Customer Information
							<Badge
								variant="secondary"
								className="bg-gray-100 text-gray-600 border-gray-200"
							>
								<EyeOff className="w-3 h-3 mr-1" />
								Redacted
							</Badge>
						</CardTitle>
						<p className="text-sm text-gray-600 mt-1">
							Contact details are hidden until terms and conditions are accepted
						</p>
					</div>
				</div>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
					{/* Name - Partially visible */}
					<div className="flex items-center gap-3">
						<User className="h-5 w-5 text-gray-400" />
						<div>
							<p className="text-sm font-medium text-gray-900">Name</p>
							<p className="text-sm text-gray-600">
								{firstName} {lastName.charAt(0)}.
							</p>
						</div>
					</div>

					{/* Email - Redacted */}
					<div className="flex items-center gap-3">
						<Mail className="h-5 w-5 text-gray-400" />
						<div>
							<p className="text-sm font-medium text-gray-900">Email</p>
							<p className="text-sm text-gray-400">••••••••@••••••••.com</p>
						</div>
					</div>

					{/* Phone - Redacted */}
					<div className="flex items-center gap-3">
						<Phone className="h-5 w-5 text-gray-400" />
						<div>
							<p className="text-sm font-medium text-gray-900">Phone</p>
							<p className="text-sm text-gray-400">(•••) •••-••••</p>
						</div>
					</div>

					{/* Location - Partially visible */}
					{location && (
						<div className="flex items-center gap-3 sm:col-span-2">
							<MapPin className="h-5 w-5 text-gray-400" />
							<div>
								<p className="text-sm font-medium text-gray-900">Location</p>
								<p className="text-sm text-gray-600">
									{location.city && location.state
										? `${location.city}, ${location.state}`
										: location.address
											? location.address
											: "Location available after certification"}
								</p>
							</div>
						</div>
					)}
				</div>

				<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
					<div className="flex items-start gap-2">
						<EyeOff className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
						<div className="text-sm text-yellow-800">
							<strong>Contact information is redacted</strong> because this is a
							partner portal warranty job. Review the terms and conditions to
							enable messaging and view full customer details.
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
