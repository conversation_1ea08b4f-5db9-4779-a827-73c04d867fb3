import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogTitle
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
	CheckCircle,
	Clock,
	DollarSign,
	FileText,
	MessageSquare,
	Package,
	Truck
} from "lucide-react";
import { useState } from "react";

interface WarrantyTermsModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	companyName: string;
	onAccept: () => Promise<void>;
	isLoading?: boolean;
	customerName?: string;
	location?: string;
	rvDetails?: string;
	issueDescription?: string;
}

export function WarrantyTermsModal({
	open,
	onOpenChange,
	companyName,
	onAccept,
	isLoading = false,
	customerName = "Customer",
	location = "Location Available",
	rvDetails = "RV Details Available",
	issueDescription = "Issue details will be available after accepting this lead."
}: WarrantyTermsModalProps) {
	const [acceptsTerms, setAcceptsTerms] = useState(false);

	const handleAccept = async () => {
		await onAccept();
		// Reset form state
		setAcceptsTerms(false);
	};

	const canAccept = acceptsTerms && !isLoading;

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-2xl p-0">
				{/* Header */}
				<div className="bg-gradient-to-r from-[#43806c] to-[#5a8d69] text-white p-6 text-center">
					<DialogTitle className="text-xl font-bold mb-1">
						New Warranty Lead from {companyName}
					</DialogTitle>
					<DialogDescription className="text-white/90">
						Direct warranty work opportunity
					</DialogDescription>
				</div>

				<ScrollArea className="max-h-[60vh]">
					<div className="p-6 space-y-6">
						{/* Service Request Details */}
						<div className="border-2 border-[#43806c] rounded-lg p-5 bg-gradient-to-r from-[#43806c]/5 to-emerald-50/50">
							<div className="flex items-center gap-2 mb-4">
								<CheckCircle className="w-5 h-5 text-[#43806c]" />
								<h3 className="font-semibold text-[#43806c]">
									Warranty Service Request - Direct from {companyName}
								</h3>
							</div>
							<p className="text-sm text-gray-700 mb-4 leading-relaxed">
								This verified warranty lead was sent directly to you through RV
								Help's Warranty Portal. The customer has already been
								pre-approved for warranty service from {companyName}.
							</p>

							<div className="space-y-2 mb-4">
								<h4 className="font-medium text-gray-900">
									Customer Information
								</h4>
								<div className="grid grid-cols-1 gap-1 text-sm">
									<div className="flex justify-between">
										<span className="text-gray-600">Name:</span>
										<span className="font-medium">{customerName}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Location:</span>
										<span className="font-medium">{location}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">RV:</span>
										<span className="font-medium">{rvDetails}</span>
									</div>
								</div>
							</div>

							<div className="bg-orange-50 border-l-4 border-orange-400 p-3 rounded">
								<h4 className="font-medium text-gray-900 mb-2">
									Notes from {companyName} Team:
								</h4>
								<p className="text-sm text-gray-700 italic leading-relaxed">
									"{issueDescription}"
								</p>
							</div>
						</div>

						{/* Benefits Section */}
						<div>
							<h3 className="font-semibold text-gray-900 mb-2">
								RV Help + {companyName} Warranty Portal
							</h3>
							<p className="text-sm text-gray-700 mb-4 leading-relaxed">
								We partnered with {companyName} to streamline the warranty
								process. No more months-long payouts or paperwork nightmares.
								This direct-to-tech approach respects your time and gets you
								paid faster.
							</p>

							<h4 className="font-medium text-gray-900 mb-3">Your benefits:</h4>
							<div className="space-y-3">
								<div className="flex items-start gap-3">
									<CheckCircle className="w-5 h-5 text-[#43806c] mt-0.5 flex-shrink-0" />
									<div className="flex items-center gap-2">
										<Truck className="w-4 h-4 text-gray-600" />
										<span>
											More warranty leads sent directly from {companyName}
										</span>
									</div>
								</div>
								<div className="flex items-start gap-3">
									<CheckCircle className="w-5 h-5 text-[#43806c] mt-0.5 flex-shrink-0" />
									<div className="flex items-center gap-2">
										<DollarSign className="w-4 h-4 text-gray-600" />
										<span>Guaranteed service call fee for completed jobs</span>
									</div>
								</div>
								<div className="flex items-start gap-3">
									<CheckCircle className="w-5 h-5 text-[#43806c] mt-0.5 flex-shrink-0" />
									<div className="flex items-center gap-2">
										<FileText className="w-4 h-4 text-gray-600" />
										<span>
											Simple complaint, cause, & correction forms in RV Help
										</span>
									</div>
								</div>
								<div className="flex items-start gap-3">
									<CheckCircle className="w-5 h-5 text-[#43806c] mt-0.5 flex-shrink-0" />
									<div className="flex items-center gap-2">
										<Clock className="w-4 h-4 text-gray-600" />
										<span className="">
											Fast payouts through RV Help's invoice generator
										</span>
									</div>
								</div>
								<div className="flex items-start gap-3">
									<CheckCircle className="w-5 h-5 text-[#43806c] mt-0.5 flex-shrink-0" />
									<div className="flex items-center gap-2">
										<Package className="w-4 h-4 text-gray-600" />
										<span className="">
											Expedited parts shipping for warranty repairs
										</span>
									</div>
								</div>
								<div className="flex items-start gap-3">
									<CheckCircle className="w-5 h-5 text-[#43806c] mt-0.5 flex-shrink-0" />
									<div className="flex items-center gap-2">
										<MessageSquare className="w-4 h-4 text-gray-600" />
										<span className="">
											Direct communication with {companyName} support team
										</span>
									</div>
								</div>
							</div>
						</div>

						{/* Simple Terms */}
						<div className="bg-green-50 border border-green-200 rounded-lg p-4">
							<div className="text">
								<strong className="text-green-900">
									Terms for {companyName} Warranty Leads:
								</strong>
								<div className="mt-2 space-y-1 text-green-800">
									<div>
										• You agree to fill out complaint, cause, and correction
										with photos
									</div>
									<div>
										• You agree to use RV Help's invoicing process for warranty
										jobs
									</div>
									<div>
										• You agree to communicate with customers through RV Help
										messaging when possible
									</div>
									<div>
										• You agree that we can share your warranty pricing
										information with {companyName}
									</div>
								</div>
							</div>
						</div>

						{/* Acceptance Checkbox */}
						<div className="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
							<Checkbox
								id="accept-terms"
								checked={acceptsTerms}
								onCheckedChange={(checked) => setAcceptsTerms(checked === true)}
							/>
							<label
								htmlFor="accept-terms"
								className="text-sm text-gray-700 cursor-pointer leading-relaxed"
							>
								By clicking Accept, you agree to the terms above and will be
								eligible to start receiving warranty leads directly from{" "}
								{companyName}.
							</label>
						</div>
					</div>
				</ScrollArea>

				<DialogFooter className="p-6 pt-0">
					<div className="flex flex-col w-full gap-3">
						<Button
							onClick={handleAccept}
							disabled={!canAccept}
							className="w-full bg-[#43806c] hover:bg-[#2c5446] text-white text-base py-3"
						>
							{isLoading ? "Processing..." : "Accept & Contact Customer →"}
						</Button>
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
							disabled={isLoading}
						>
							Not Right Now
						</Button>
					</div>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
