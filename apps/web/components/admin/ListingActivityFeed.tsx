"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	Di<PERSON>Header,
	DialogTitle
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { format } from "date-fns";
import { Eye, Mail, MessageSquare, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface Notification {
	id: string;
	type: "email" | "sms";
	to: string;
	subject: string;
	content: string;
	status: string;
	timestamp: string;
	response: string;
}

interface ListingInfo {
	id: string;
	business_name: string;
	first_name: string;
	last_name: string;
	phone: string | null;
	notification_sms: string | null;
	email: string | null;
	notification_email: string | null;
}

interface ListingActivityFeedProps {
	isOpen: boolean;
	onClose: () => void;
	listingId: string;
}

// Helper functions
const getStatusBadge = (status: string) => {
	const statusConfig = {
		delivered: { label: "Delivered", className: "bg-green-100 text-green-800" },
		sent: { label: "Sent", className: "bg-blue-100 text-blue-800" },
		failed: { label: "Failed", className: "bg-red-100 text-red-800" },
		undelivered: {
			label: "Undelivered",
			className: "bg-yellow-100 text-yellow-800"
		},
		queued: { label: "Queued", className: "bg-gray-100 text-gray-800" }
	};

	const config = statusConfig[status as keyof typeof statusConfig] || {
		label: status,
		className: "bg-gray-100 text-gray-800"
	};

	return <Badge className={config.className}>{config.label}</Badge>;
};

const getNotificationIcon = (type: string) => {
	return type === "email" ? (
		<Mail className="h-4 w-4" />
	) : (
		<MessageSquare className="h-4 w-4" />
	);
};

const formatContent = (content: string) => {
	// Remove HTML tags and truncate
	const plainText = content.replace(/<[^>]*>/g, "");
	return plainText.length > 150
		? plainText.substring(0, 150) + "..."
		: plainText;
};

export default function ListingActivityFeed({
	isOpen,
	onClose,
	listingId
}: ListingActivityFeedProps) {
	const [notifications, setNotifications] = useState<Notification[]>([]);
	const [listing, setListing] = useState<ListingInfo | null>(null);
	const [loading, setLoading] = useState(false);
	const [selectedNotification, setSelectedNotification] =
		useState<Notification | null>(null);
	const [activeTab, setActiveTab] = useState("all");
	const [emailCount, setEmailCount] = useState(0);
	const [smsCount, setSmsCount] = useState(0);

	const fetchNotifications = async (type: string = "all") => {
		setLoading(true);
		try {
			const response = await fetch(
				`/api/admin/listings/${listingId}/notifications?type=${type}&limit=100`
			);

			if (!response.ok) {
				throw new Error("Failed to fetch notifications");
			}

			const data = await response.json();
			setNotifications(data.notifications);
			setListing(data.listing);
			setEmailCount(data.emailCount);
			setSmsCount(data.smsCount);
		} catch (error) {
			console.error("Error fetching notifications:", error);
			toast.error("Failed to load notifications");
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (isOpen && listingId) {
			fetchNotifications(activeTab);
		}
	}, [isOpen, listingId, activeTab]);

	const handleTabChange = (value: string) => {
		setActiveTab(value);
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl max-h-[80vh]">
				<DialogHeader>
					<DialogTitle className="flex items-center justify-between">
						<span>
							Activity Feed - {listing?.business_name || listing?.first_name}
						</span>
						<Button
							variant="ghost"
							size="sm"
							onClick={onClose}
							className="h-8 w-8 p-0"
						>
							<X className="h-4 w-4" />
						</Button>
					</DialogTitle>
				</DialogHeader>

				{listing && (
					<div className="mb-4 p-3 bg-gray-50 rounded-lg">
						<div className="grid grid-cols-2 gap-4 text-sm">
							<div>
								<strong>Business:</strong> {listing.business_name}
							</div>
							<div>
								<strong>Contact:</strong> {listing.first_name}{" "}
								{listing.last_name}
							</div>
							<div>
								<strong>Email:</strong>{" "}
								{listing.email || listing.notification_email || "N/A"}
							</div>
							<div>
								<strong>Phone:</strong>{" "}
								{listing.phone || listing.notification_sms || "N/A"}
							</div>
						</div>
					</div>
				)}

				<Tabs value={activeTab} onValueChange={handleTabChange}>
					<TabsList className="grid w-full grid-cols-3">
						<TabsTrigger value="all">All ({notifications.length})</TabsTrigger>
						<TabsTrigger value="email">Email ({emailCount})</TabsTrigger>
						<TabsTrigger value="sms">SMS ({smsCount})</TabsTrigger>
					</TabsList>

					<TabsContent value="all" className="mt-4">
						<NotificationList
							notifications={notifications}
							loading={loading}
							onNotificationClick={setSelectedNotification}
						/>
					</TabsContent>

					<TabsContent value="email" className="mt-4">
						<NotificationList
							notifications={notifications.filter((n) => n.type === "email")}
							loading={loading}
							onNotificationClick={setSelectedNotification}
						/>
					</TabsContent>

					<TabsContent value="sms" className="mt-4">
						<NotificationList
							notifications={notifications.filter((n) => n.type === "sms")}
							loading={loading}
							onNotificationClick={setSelectedNotification}
						/>
					</TabsContent>
				</Tabs>

				{/* Notification Detail Modal */}
				{selectedNotification && (
					<Dialog
						open={!!selectedNotification}
						onOpenChange={() => setSelectedNotification(null)}
					>
						<DialogContent className="max-w-2xl">
							<DialogHeader>
								<DialogTitle className="flex items-center gap-2">
									{getNotificationIcon(selectedNotification.type)}
									{selectedNotification.type === "email"
										? "Email Details"
										: "SMS Details"}
								</DialogTitle>
							</DialogHeader>

							<div className="space-y-4">
								<div>
									<strong>To:</strong> {selectedNotification.to}
								</div>
								<div>
									<strong>Subject:</strong> {selectedNotification.subject}
								</div>
								<div>
									<strong>Status:</strong>{" "}
									{getStatusBadge(selectedNotification.status)}
								</div>
								<div>
									<strong>Sent:</strong>{" "}
									{format(
										new Date(selectedNotification.timestamp),
										"PPP 'at' p"
									)}
								</div>
								<div>
									<strong>Content:</strong>
									<div className="mt-2 p-3 bg-gray-50 rounded text-sm whitespace-pre-wrap">
										{selectedNotification.content}
									</div>
								</div>
								{selectedNotification.response && (
									<div>
										<strong>Response:</strong>
										<div className="mt-2 p-3 bg-gray-50 rounded text-xs font-mono overflow-auto">
											{selectedNotification.response}
										</div>
									</div>
								)}
							</div>
						</DialogContent>
					</Dialog>
				)}
			</DialogContent>
		</Dialog>
	);
}

interface NotificationListProps {
	notifications: Notification[];
	loading: boolean;
	onNotificationClick: (notification: Notification) => void;
}

function NotificationList({
	notifications,
	loading,
	onNotificationClick
}: NotificationListProps) {
	if (loading) {
		return (
			<div className="flex items-center justify-center py-8">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
			</div>
		);
	}

	if (notifications.length === 0) {
		return (
			<div className="text-center py-8 text-gray-500">
				No notifications found
			</div>
		);
	}

	return (
		<ScrollArea className="h-[400px]">
			<div className="space-y-3">
				{notifications.map((notification) => (
					<div
						key={notification.id}
						className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
						onClick={() => onNotificationClick(notification)}
					>
						<div className="flex items-start justify-between">
							<div className="flex items-center gap-3 flex-1">
								<div className="flex-shrink-0">
									{getNotificationIcon(notification.type)}
								</div>
								<div className="flex-1 min-w-0">
									<div className="flex items-center gap-2 mb-1">
										<span className="font-medium text-sm">
											{notification.subject}
										</span>
										{getStatusBadge(notification.status)}
									</div>
									<div className="text-sm text-gray-600 mb-1">
										To: {notification.to}
									</div>
									<div className="text-sm text-gray-500">
										{formatContent(notification.content)}
									</div>
								</div>
							</div>
							<div className="flex items-center gap-2">
								<div className="text-xs text-gray-500">
									{format(new Date(notification.timestamp), "MMM d, h:mm a")}
								</div>
								<Eye className="h-4 w-4 text-gray-400" />
							</div>
						</div>
					</div>
				))}
			</div>
		</ScrollArea>
	);
}
