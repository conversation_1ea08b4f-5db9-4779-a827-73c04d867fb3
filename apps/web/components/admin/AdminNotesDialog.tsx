"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { format } from "date-fns";
import { Edit, Save, X } from "lucide-react";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface AdminNotesDialogProps {
	isOpen: boolean;
	onClose: () => void;
	title: string;
	entityType: "user" | "listing";
	entityData: {
		id: string;
		name: string;
		email?: string;
		business_name?: string;
		admin_notes?: string | null;
		created_at: string;
		updated_at: string;
	};
	onNotesUpdated?: () => void; // Callback to refresh data after save
}

export default function AdminNotesDialog({
	isOpen,
	onClose,
	title,
	entityType,
	entityData,
	onNotesUpdated
}: AdminNotesDialogProps) {
	const [isEditing, setIsEditing] = useState(false);
	const [notes, setNotes] = useState(entityData.admin_notes || "");
	const [isSaving, setIsSaving] = useState(false);

	const displayName =
		entityType === "user"
			? entityData.name
			: entityData.business_name || entityData.name;

	const handleEdit = () => {
		setIsEditing(true);
		setNotes(entityData.admin_notes || "");
	};

	const handleCancel = () => {
		setIsEditing(false);
		setNotes(entityData.admin_notes || "");
	};

	const handleSave = async () => {
		setIsSaving(true);
		try {
			let response;

			if (entityType === "listing") {
				// Update listing admin notes using dedicated endpoint
				response = await fetch(`/api/listings/${entityData.id}/admin-notes`, {
					method: "PUT",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						admin_notes: notes.trim() || null
					})
				});
			} else {
				// Update user admin notes using dedicated endpoint
				response = await fetch(
					`/api/admin/users/${entityData.id}/admin-notes`,
					{
						method: "PUT",
						headers: {
							"Content-Type": "application/json"
						},
						body: JSON.stringify({
							admin_notes: notes.trim() || null
						})
					}
				);
			}

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(
					errorData.error || `Failed to update ${entityType} admin notes`
				);
			}

			toast.success("Admin notes updated successfully");
			setIsEditing(false);

			// Call the callback to refresh parent data
			if (onNotesUpdated) {
				onNotesUpdated();
			}
		} catch (error) {
			console.error("Error saving admin notes:", error);
			toast.error(error.message || "Failed to save admin notes");
		} finally {
			setIsSaving(false);
		}
	};

	const handleClose = () => {
		if (isEditing) {
			handleCancel();
		}
		onClose();
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleClose}>
			<DialogContent className="max-w-2xl">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
				</DialogHeader>

				<div className="space-y-4">
					{/* Entity Info */}
					<div className="border-b pb-4">
						<div className="flex items-center justify-between">
							<div>
								<h3 className="font-medium">{displayName}</h3>
								{entityData.email && (
									<p className="text-sm text-gray-500">{entityData.email}</p>
								)}
							</div>
							<Badge variant="outline" className="capitalize">
								{entityType}
							</Badge>
						</div>
						<div className="flex gap-4 mt-2 text-xs text-gray-500">
							<span>
								Created:{" "}
								{format(new Date(entityData.created_at), "MMM d, yyyy")}
							</span>
							<span>
								Updated:{" "}
								{format(new Date(entityData.updated_at), "MMM d, yyyy")}
							</span>
						</div>
					</div>

					{/* Admin Notes */}
					<div>
						<div className="flex items-center gap-2 mb-2">
							<h4 className="font-medium text-red-600">Admin Notes</h4>
							{!isEditing && (
								<Button
									variant="ghost"
									size="sm"
									onClick={handleEdit}
									className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
									title="Edit Notes"
								>
									<Edit className="h-3 w-3" />
								</Button>
							)}
						</div>
						{isEditing ? (
							<div className="space-y-3">
								<Textarea
									value={notes}
									onChange={(e) => setNotes(e.target.value)}
									placeholder="Enter admin notes (internal use only)..."
									rows={6}
									className="w-full"
								/>
								<div className="flex gap-2">
									<Button
										onClick={handleSave}
										disabled={isSaving}
										className="flex items-center gap-2"
									>
										<Save className="h-4 w-4" />
										{isSaving ? "Saving..." : "Save Notes"}
									</Button>
									<Button
										variant="outline"
										onClick={handleCancel}
										disabled={isSaving}
										className="flex items-center gap-2"
									>
										<X className="h-4 w-4" />
										Cancel
									</Button>
								</div>
							</div>
						) : (
							<div>
								{entityData.admin_notes ? (
									<div className="bg-red-50 border border-red-200 rounded-lg p-4">
										<pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans">
											{entityData.admin_notes}
										</pre>
									</div>
								) : (
									<div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
										<p className="text-gray-500 text-sm italic">
											No admin notes recorded for this {entityType}.
										</p>
									</div>
								)}
							</div>
						)}
					</div>

					{/* Warning */}
					{!isEditing && (
						<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
							<p className="text-xs text-yellow-800">
								<strong>⚠️ Admin Only:</strong> These notes are internal and not
								visible to users. Click &quot;Edit Notes&quot; to modify.
							</p>
						</div>
					)}
				</div>

				{!isEditing && (
					<div className="flex justify-end pt-4">
						<Button variant="outline" onClick={handleClose}>
							Close
						</Button>
					</div>
				)}
			</DialogContent>
		</Dialog>
	);
}
