"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Play, RefreshCw, Clock, Mail, MessageSquare } from "lucide-react";

interface TestResult {
  success: boolean;
  scenario: string;
  result?: any;
  message?: string;
  error?: string;
}

interface TestJob {
  id: string;
  status: string;
  created_at: string;
  message: string;
  quotes: Array<{
    id: string;
    status: string;
    listing: {
      business_name: string;
      first_name: string;
      last_name: string;
    };
  }>;
}

export default function JobTestingPanel() {
  const [loading, setLoading] = useState<string | null>(null);
  const [results, setResults] = useState<TestResult[]>([]);
  const [testJobs, setTestJobs] = useState<TestJob[]>([]);
  const [jobId, setJobId] = useState("");
  const [hoursBack, setHoursBack] = useState(24);

  const runTestScenario = async (scenario: string, params: any = {}) => {
    setLoading(scenario);
    try {
      const response = await fetch("/api/admin/testing/jobs", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          scenario,
          ...params
        }),
      });

      const result = await response.json();
      setResults(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 results
      
      // Refresh job list after creating new test data
      if (scenario.includes("create") || scenario.includes("backdate")) {
        await loadTestJobs();
      }
    } catch (error) {
      setResults(prev => [{
        success: false,
        scenario,
        error: error.message
      }, ...prev.slice(0, 9)]);
    } finally {
      setLoading(null);
    }
  };

  const loadTestJobs = async () => {
    try {
      const response = await fetch("/api/admin/testing/jobs");
      const data = await response.json();
      setTestJobs(data.current_test_jobs || []);
    } catch (error) {
      console.error("Failed to load test jobs:", error);
    }
  };

  const testScenarios = [
    {
      category: "Create Test Jobs",
      scenarios: [
        {
          id: "create_24hr_old_job",
          name: "24hr Old Job",
          description: "Creates a job that's 24 hours old for testing provider reminders",
          icon: <Clock className="w-4 h-4" />
        },
        {
          id: "create_72hr_old_job", 
          name: "72hr Old Job",
          description: "Creates a job that's 72 hours old for testing expiration workflow",
          icon: <Clock className="w-4 h-4" />
        },
        {
          id: "create_job_with_pending_quotes",
          name: "Job with Pending Quotes",
          description: "Creates a job with providers invited but no responses",
          icon: <MessageSquare className="w-4 h-4" />
        },
        {
          id: "create_job_with_mixed_quotes",
          name: "Job with Mixed Quotes",
          description: "Creates a job with one quote and one rejection",
          icon: <MessageSquare className="w-4 h-4" />
        },
        {
          id: "create_completed_job",
          name: "Completed Job",
          description: "Creates a completed job for testing review workflows",
          icon: <MessageSquare className="w-4 h-4" />
        }
      ]
    },
    {
      category: "Trigger Notifications",
      scenarios: [
        {
          id: "trigger_provider_reminders",
          name: "Provider Reminders",
          description: "Manually trigger 24hr provider reminder emails",
          icon: <Mail className="w-4 h-4" />
        },
        {
          id: "trigger_customer_followup",
          name: "Customer Follow-up",
          description: "Manually trigger customer follow-up emails",
          icon: <Mail className="w-4 h-4" />
        },
        {
          id: "trigger_job_expiration",
          name: "Job Expiration",
          description: "Manually trigger 72hr job expiration processing",
          icon: <Mail className="w-4 h-4" />
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Job Testing Panel</h2>
          <p className="text-muted-foreground">
            Test job workflows and notification systems (Development Only)
          </p>
        </div>
        <Button onClick={loadTestJobs} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Scenarios */}
        <div className="space-y-6">
          {testScenarios.map((category) => (
            <Card key={category.category}>
              <CardHeader>
                <CardTitle>{category.category}</CardTitle>
                <CardDescription>
                  {category.category === "Create Test Jobs" 
                    ? "Create test data in various states"
                    : "Manually trigger cron job workflows"
                  }
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {category.scenarios.map((scenario) => (
                  <div key={scenario.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {scenario.icon}
                      <div>
                        <div className="font-medium">{scenario.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {scenario.description}
                        </div>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => runTestScenario(scenario.id)}
                      disabled={loading === scenario.id}
                    >
                      {loading === scenario.id ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Play className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          ))}

          {/* Backdate Existing Job */}
          <Card>
            <CardHeader>
              <CardTitle>Backdate Existing Job</CardTitle>
              <CardDescription>
                Modify an existing job's timestamp for testing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="jobId">Job ID</Label>
                  <Input
                    id="jobId"
                    value={jobId}
                    onChange={(e) => setJobId(e.target.value)}
                    placeholder="Enter job ID"
                  />
                </div>
                <div>
                  <Label htmlFor="hoursBack">Hours Back</Label>
                  <Input
                    id="hoursBack"
                    type="number"
                    value={hoursBack}
                    onChange={(e) => setHoursBack(parseInt(e.target.value))}
                    min={1}
                    max={168}
                  />
                </div>
              </div>
              <Button
                onClick={() => runTestScenario("backdate_existing_job", { jobId, hoursBack })}
                disabled={!jobId || loading === "backdate_existing_job"}
                className="w-full"
              >
                {loading === "backdate_existing_job" ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : (
                  <Clock className="w-4 h-4 mr-2" />
                )}
                Backdate Job
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Results and Test Jobs */}
        <div className="space-y-6">
          {/* Recent Results */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Results</CardTitle>
              <CardDescription>
                Latest test scenario results
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3 max-h-96 overflow-y-auto">
              {results.length === 0 ? (
                <div className="text-center text-muted-foreground py-6">
                  No test results yet. Run a scenario to see results.
                </div>
              ) : (
                results.map((result, index) => (
                  <Alert key={index} className={result.success ? "border-green-200" : "border-red-200"}>
                    <AlertDescription>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{result.scenario}</span>
                        <Badge variant={result.success ? "default" : "destructive"}>
                          {result.success ? "Success" : "Failed"}
                        </Badge>
                      </div>
                      <div className="text-sm mt-1">
                        {result.message || result.error}
                      </div>
                      {result.result && (
                        <details className="mt-2">
                          <summary className="text-sm cursor-pointer text-blue-600">
                            View Details
                          </summary>
                          <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-x-auto">
                            {JSON.stringify(result.result, null, 2)}
                          </pre>
                        </details>
                      )}
                    </AlertDescription>
                  </Alert>
                ))
              )}
            </CardContent>
          </Card>

          {/* Current Test Jobs */}
          <Card>
            <CardHeader>
              <CardTitle>Current Test Jobs</CardTitle>
              <CardDescription>
                Jobs created by the testing system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3 max-h-96 overflow-y-auto">
              {testJobs.length === 0 ? (
                <div className="text-center text-muted-foreground py-6">
                  No test jobs found. Create some test scenarios.
                </div>
              ) : (
                testJobs.map((job) => (
                  <div key={job.id} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{job.status}</Badge>
                        <span className="text-sm font-mono">{job.id.slice(-8)}</span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {new Date(job.created_at).toLocaleString()}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {job.message}
                    </div>
                    {job.quotes.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {job.quotes.map((quote) => (
                          <div key={quote.id} className="flex items-center justify-between text-xs">
                            <span>
                              {quote.listing.business_name || 
                               `${quote.listing.first_name} ${quote.listing.last_name}`}
                            </span>
                            <Badge variant="secondary" className="text-xs">
                              {quote.status}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 