"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { BlacklistType } from "@rvhelp/database";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

const formSchema = z.object({
	type: z.enum(["EMAIL", "DOMAIN", "USER_ID", "IP_ADDRESS"]),
	value: z.string().min(1, "Value is required"),
	reason: z.string().min(1, "Reason is required"),
	message: z.string().optional(),
	expiresAt: z.string().optional()
});

type FormData = z.infer<typeof formSchema>;

interface AddBlacklistEntryProps {
	onSuccess?: () => void;
}

export function AddBlacklistEntry({ onSuccess }: AddBlacklistEntryProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const form = useForm<FormData>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			type: "EMAIL",
			value: "",
			reason: "",
			message: "",
			expiresAt: ""
		}
	});

	const onSubmit = async (data: FormData) => {
		setIsSubmitting(true);
		try {
			const response = await fetch("/api/admin/blacklist", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(data)
			});

			if (!response.ok) throw new Error("Failed to add entry");

			toast.success("Entry added successfully");
			form.reset();
			setIsOpen(false);
			onSuccess?.();
		} catch (error) {
			toast.error("Failed to add entry");
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<div className="space-y-4">
			<Button onClick={() => setIsOpen(!isOpen)}>
				{isOpen ? "Cancel" : "Add New Blacklist Entry"}
			</Button>

			{isOpen && (
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-4 bg-card p-4 rounded-lg border max-w-xl"
					>
						<FormField
							control={form.control}
							name="type"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Type</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select type" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{Object.values(BlacklistType).map((type) => (
												<SelectItem key={type} value={type}>
													{type}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="value"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Value</FormLabel>
									<FormControl>
										<Input placeholder="Enter value" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="reason"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Reason (Required)</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Enter the reason for blacklisting"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="message"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Custom Message (Optional)</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Custom message to show to banned users"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="expiresAt"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Expiry Date (Optional)</FormLabel>
									<FormControl>
										<Input type="datetime-local" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<Button type="submit" disabled={isSubmitting}>
							{isSubmitting ? "Adding..." : "Add Entry"}
						</Button>
					</form>
				</Form>
			)}
		</div>
	);
}
