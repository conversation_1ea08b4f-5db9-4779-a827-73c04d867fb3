"use client";
import { forwardRef, useImperative<PERSON><PERSON>le } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { BlacklistType } from "@rvhelp/database";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

type BlacklistEntry = {
	id: string;
	type: BlacklistType;
	value: string;
	reason: string;
	message: string | null;
	created_at: string;
	expires_at: string | null;
	is_active: boolean;
	created_by: string;
};

export const BlacklistTable = forwardRef(function BlacklistTable(_, ref) {
	const [entries, setEntries] = useState<BlacklistEntry[]>([]);
	const [isLoading, setIsLoading] = useState(true);

	const fetchEntries = async () => {
		try {
			const response = await fetch("/api/admin/blacklist");
			const data = await response.json();
			setEntries(data);
		} catch (error) {
			toast.error("Failed to fetch entries");
		} finally {
			setIsLoading(false);
		}
	};

	const handleDelete = async (id: string) => {
		try {
			const response = await fetch(`/api/admin/blacklist?id=${id}`, {
				method: "DELETE"
			});
			if (!response.ok) throw new Error("Failed to delete entry");

			toast.success("Entry removed successfully");
			fetchEntries(); // Refresh the list
		} catch (error) {
			toast.error("Failed to remove entry");
		}
	};

	const handleClearMarkers = async (entry: BlacklistEntry) => {
		try {
			const response = await fetch("/api/admin/blacklist/clear-markers", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					value: entry.value,
					type: entry.type
				})
			});

			if (!response.ok) throw new Error("Failed to clear markers");

			const data = await response.json();

			// Show the clear script to the admin
			if (data.clearScript) {
				const shouldExecute = confirm(
					`Clear ban markers for ${entry.type}: ${entry.value}?\n\n` +
						"This will provide you with a script that the user can execute " +
						"to clear their ban markers from localStorage and cookies.\n\n" +
						"Click OK to see the script."
				);

				if (shouldExecute) {
					// Create a popup with the script
					const scriptWindow = window.open(
						"",
						"_blank",
						"width=600,height=400"
					);
					if (scriptWindow) {
						scriptWindow.document.write(`
                            <html>
                                <head><title>Clear Ban Markers Script</title></head>
                                <body>
                                    <h3>Clear Ban Markers Script</h3>
                                    <p>Have the user paste this in their browser console:</p>
                                    <textarea rows="10" cols="80" readonly>${data.clearScript}</textarea>
                                    <br><br>
                                    <button onclick="navigator.clipboard.writeText(\`${data.clearScript}\`)">Copy Script</button>
                                    <p><em>Or have them visit this page and execute the script automatically:</em></p>
                                    <button onclick="eval(\`${data.clearScript}\`)">Execute Now (if you are the user)</button>
                                </body>
                            </html>
                        `);
					}
				}
			}

			toast.success(data.message);
		} catch (error) {
			toast.error("Failed to clear markers");
		}
	};

	useEffect(() => {
		fetchEntries();
	}, []);

	useImperativeHandle(ref, () => ({
		fetchEntries
	}));

	if (isLoading) return <div>Loading...</div>;

	return (
		<div className="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Type</TableHead>
						<TableHead>Value</TableHead>
						<TableHead>Reason</TableHead>
						<TableHead>Message</TableHead>
						<TableHead>Added By</TableHead>
						<TableHead>Added On</TableHead>
						<TableHead>Expires</TableHead>
						<TableHead>Status</TableHead>
						<TableHead>Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{entries?.map((entry) => (
						<TableRow key={entry.id}>
							<TableCell>
								<Badge variant="outline">{entry.type}</Badge>
							</TableCell>
							<TableCell className="font-mono text-sm">{entry.value}</TableCell>
							<TableCell className="max-w-xs truncate" title={entry.reason}>
								{entry.reason}
							</TableCell>
							<TableCell
								className="max-w-xs truncate"
								title={entry.message || ""}
							>
								{entry.message || "-"}
							</TableCell>
							<TableCell>{entry.created_by || "System"}</TableCell>
							<TableCell>
								{format(new Date(entry.created_at), "MMM d, yyyy")}
							</TableCell>
							<TableCell>
								{entry.expires_at
									? format(new Date(entry.expires_at), "MMM d, yyyy")
									: "Never"}
							</TableCell>
							<TableCell>
								<Badge variant={entry.is_active ? "destructive" : "secondary"}>
									{entry.is_active ? "Active" : "Inactive"}
								</Badge>
							</TableCell>
							<TableCell>
								<div className="flex gap-2">
									<Button
										variant="destructive"
										size="sm"
										onClick={() => handleDelete(entry.id)}
									>
										Remove
									</Button>
									{(entry.type === "USER_ID" || entry.type === "EMAIL") && (
										<Button
											variant="outline"
											size="sm"
											onClick={() => handleClearMarkers(entry)}
											title="Clear ban markers from user's browser"
										>
											Clear Markers
										</Button>
									)}
								</div>
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
		</div>
	);
});
