"use client";

import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import {
	BookOpen,
	CheckCircle,
	ChevronLeft,
	ChevronRight,
	FileText,
	Video
} from "lucide-react";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface TrainingModule {
	id: string;
	title: string;
	video_url?: string;
	content?: string;
}

interface CertificationTrainingProps {
	certificationName: string;
	displayName: string;
	trainingContent: {
		modules: TrainingModule[];
	};
	termsConditions: string;
	onComplete: () => void;
	onOptOut: (reason?: string) => void;
}

export function CertificationTraining({
	certificationName,
	displayName,
	trainingContent,
	termsConditions,
	onComplete,
	onOptOut
}: CertificationTrainingProps) {
	const [currentModuleIndex, setCurrentModuleIndex] = useState(0);
	const [progress, setProgress] = useState<Record<string, boolean>>({});
	const [termsAccepted, setTermsAccepted] = useState(false);
	const [isCompleting, setIsCompleting] = useState(false);
	const [isOptingOut, setIsOptingOut] = useState(false);

	const currentModule = trainingContent.modules[currentModuleIndex];
	const totalModules = trainingContent.modules.length;
	const completedModules = Object.keys(progress).length;
	const progressPercentage = (completedModules / totalModules) * 100;

	const isLastModule = currentModuleIndex === totalModules - 1;
	const canProceed = progress[currentModule.id] || currentModuleIndex === 0;

	const handleModuleComplete = () => {
		setProgress((prev) => ({ ...prev, [currentModule.id]: true }));

		if (isLastModule) {
			// Show terms and conditions
			return;
		}

		setCurrentModuleIndex((prev) => prev + 1);
	};

	const handleComplete = async () => {
		setIsCompleting(true);
		try {
			const response = await fetch(
				`/api/provider/certifications/${certificationName}/complete`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					}
				}
			);

			if (response.ok) {
				toast.success("Certification completed successfully!");
				onComplete();
			} else {
				const error = await response.json();
				toast.error(error.error || "Failed to complete certification");
			}
		} catch (error) {
			console.error("Error completing certification:", error);
			toast.error("Failed to complete certification");
		} finally {
			setIsCompleting(false);
		}
	};

	const handleOptOut = async () => {
		setIsOptingOut(true);
		try {
			const response = await fetch(
				`/api/provider/certifications/${certificationName}/opt-out`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						reason: "User opted out during training"
					})
				}
			);

			if (response.ok) {
				toast.success("You have opted out of this certification");
				onOptOut();
			} else {
				const error = await response.json();
				toast.error(error.error || "Failed to opt out");
			}
		} catch (error) {
			console.error("Error opting out:", error);
			toast.error("Failed to opt out");
		} finally {
			setIsOptingOut(false);
		}
	};

	const renderModuleContent = () => {
		if (isLastModule && termsAccepted) {
			return (
				<div className="space-y-6">
					<Alert>
						<CheckCircle className="h-4 w-4" />
						<AlertDescription>
							You have completed all training modules and accepted the terms and
							conditions. Click "Complete Certification" to finish the process.
						</AlertDescription>
					</Alert>

					<div className="flex gap-4">
						<Button
							onClick={handleComplete}
							disabled={isCompleting}
							className="flex-1"
						>
							{isCompleting ? "Completing..." : "Complete Certification"}
						</Button>
						<Button
							variant="outline"
							onClick={handleOptOut}
							disabled={isOptingOut}
						>
							{isOptingOut ? "Opting Out..." : "Opt Out"}
						</Button>
					</div>
				</div>
			);
		}

		if (isLastModule) {
			return (
				<div className="space-y-6">
					<div className="prose max-w-none">
						<div dangerouslySetInnerHTML={{ __html: termsConditions }} />
					</div>

					<div className="flex items-center space-x-2">
						<Checkbox
							id="terms"
							checked={termsAccepted}
							onCheckedChange={(checked) =>
								setTermsAccepted(checked as boolean)
							}
						/>
						<label
							htmlFor="terms"
							className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
						>
							I have read and agree to the terms and conditions
						</label>
					</div>

					<Button
						onClick={handleModuleComplete}
						disabled={!termsAccepted}
						className="w-full"
					>
						Accept Terms and Complete Training
					</Button>
				</div>
			);
		}

		return (
			<div className="space-y-6">
				{currentModule.video_url && (
					<div className="aspect-video w-full">
						<iframe
							src={currentModule.video_url}
							className="w-full h-full rounded-lg"
							allowFullScreen
							title={currentModule.title}
						/>
					</div>
				)}

				{currentModule.content && (
					<div className="prose max-w-none">
						<div dangerouslySetInnerHTML={{ __html: currentModule.content }} />
					</div>
				)}

				<Button onClick={handleModuleComplete} className="w-full">
					Continue
				</Button>
			</div>
		);
	};

	const getModuleIcon = (module: TrainingModule) => {
		if (module.video_url && module.content) {
			return <Video className="h-4 w-4" />;
		} else if (module.video_url) {
			return <Video className="h-4 w-4" />;
		} else if (module.content) {
			return <FileText className="h-4 w-4" />;
		}
		return <BookOpen className="h-4 w-4" />;
	};

	return (
		<div className="max-w-4xl mx-auto space-y-6">
			{/* Header */}
			<div className="text-center space-y-2">
				<h1 className="text-3xl font-bold">{displayName}</h1>
				<p className="text-gray-600">
					Complete the training to become certified
				</p>
			</div>

			{/* Progress */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<CardTitle className="text-lg">Progress</CardTitle>
						<Badge variant="secondary">
							{completedModules} of {totalModules} modules
						</Badge>
					</div>
					<Progress value={progressPercentage} className="w-full" />
				</CardHeader>
			</Card>

			{/* Module Navigation */}
			<div className="flex items-center justify-between">
				<Button
					variant="outline"
					onClick={() => setCurrentModuleIndex((prev) => Math.max(0, prev - 1))}
					disabled={currentModuleIndex === 0}
				>
					<ChevronLeft className="h-4 w-4 mr-2" />
					Previous
				</Button>

				<div className="flex items-center space-x-2">
					{trainingContent.modules.map((module, index) => (
						<div
							key={module.id}
							className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${
								index === currentModuleIndex
									? "bg-primary text-primary-foreground"
									: progress[module.id]
										? "bg-green-100 text-green-800"
										: "bg-gray-100 text-gray-600"
							}`}
						>
							{getModuleIcon(module)}
							<span>{index + 1}</span>
						</div>
					))}
				</div>

				<Button
					variant="outline"
					onClick={() =>
						setCurrentModuleIndex((prev) =>
							Math.min(totalModules - 1, prev + 1)
						)
					}
					disabled={currentModuleIndex === totalModules - 1}
				>
					Next
					<ChevronRight className="h-4 w-4 ml-2" />
				</Button>
			</div>

			{/* Current Module */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center space-x-2">
						{getModuleIcon(currentModule)}
						<span>
							Module {currentModuleIndex + 1}: {currentModule.title}
						</span>
						{progress[currentModule.id] && (
							<CheckCircle className="h-5 w-5 text-green-600" />
						)}
					</CardTitle>
				</CardHeader>
				<CardContent>{renderModuleContent()}</CardContent>
			</Card>
		</div>
	);
}
