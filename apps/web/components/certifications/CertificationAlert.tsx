"use client";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	AlertCircle,
	ArrowRight,
	BookOpen,
	CheckCircle,
	Clock,
	XCircle
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface Certification {
	name: string;
	display_name: string;
	description?: string;
}

interface CertificationStatus {
	id: string;
	status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "EXPIRED" | "REVOKED";
	opted_out: boolean;
	opted_out_at?: string;
	training_completed_at?: string;
	terms_accepted_at?: string;
	certification: Certification;
}

interface CertificationAlertProps {
	onStartTraining: (certificationName: string) => void;
}

export function CertificationAlert({
	onStartTraining
}: CertificationAlertProps) {
	const [certifications, setCertifications] = useState<CertificationStatus[]>(
		[]
	);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchCertifications();
	}, []);

	const fetchCertifications = async () => {
		try {
			const response = await fetch("/api/provider/certifications");
			if (!response.ok) {
				throw new Error("Failed to fetch certifications");
			}
			const data = await response.json();
			setCertifications(data);
		} catch (error) {
			console.error("Error fetching certifications:", error);
			toast.error("Failed to load certifications");
		} finally {
			setLoading(false);
		}
	};

	const handleOptOut = async (certificationName: string) => {
		try {
			const response = await fetch(
				`/api/provider/certifications/${certificationName}/opt-out`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						reason: "Provider chose to opt out"
					})
				}
			);

			if (!response.ok) {
				throw new Error("Failed to opt out");
			}

			toast.success("You have opted out of this certification");
			fetchCertifications(); // Refresh the list
		} catch (error) {
			console.error("Error opting out:", error);
			toast.error("Failed to opt out. Please try again.");
		}
	};

	const getStatusBadge = (status: CertificationStatus) => {
		if (status.opted_out) {
			return (
				<Badge variant="secondary" className="flex items-center gap-1">
					<XCircle className="h-3 w-3" />
					Opted Out
				</Badge>
			);
		}

		switch (status.status) {
			case "COMPLETED":
				return (
					<Badge variant="default" className="flex items-center gap-1">
						<CheckCircle className="h-3 w-3" />
						Completed
					</Badge>
				);
			case "IN_PROGRESS":
				return (
					<Badge variant="outline" className="flex items-center gap-1">
						<Clock className="h-3 w-3" />
						In Progress
					</Badge>
				);
			default:
				return (
					<Badge variant="destructive" className="flex items-center gap-1">
						<AlertCircle className="h-3 w-3" />
						Pending
					</Badge>
				);
		}
	};

	const getStatusDescription = (status: CertificationStatus) => {
		if (status.opted_out) {
			return "You have opted out of this certification and will not receive related work.";
		}

		switch (status.status) {
			case "COMPLETED":
				return "You are certified and eligible to receive related work.";
			case "IN_PROGRESS":
				return "You have started the training but haven't completed it yet.";
			default:
				return "Complete this certification to be eligible for related work opportunities.";
		}
	};

	const pendingCertifications = certifications.filter(
		(cert) => !cert.opted_out && cert.status !== "COMPLETED"
	);

	const completedCertifications = certifications.filter(
		(cert) => cert.status === "COMPLETED"
	);

	const optedOutCertifications = certifications.filter(
		(cert) => cert.opted_out
	);

	if (loading) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="animate-pulse space-y-4">
						<div className="h-4 bg-gray-200 rounded w-1/4"></div>
						<div className="h-4 bg-gray-200 rounded w-3/4"></div>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (certifications.length === 0) {
		return null;
	}

	return (
		<div className="space-y-4">
			{/* Pending Certifications Alert */}
			{pendingCertifications.length > 0 && (
				<Alert className="border-orange-200 bg-orange-50">
					<AlertCircle className="h-4 w-4 text-orange-600" />
					<AlertTitle className="text-orange-800">
						Certification Required
					</AlertTitle>
					<AlertDescription className="text-orange-700">
						You have {pendingCertifications.length} certification
						{pendingCertifications.length > 1 ? "s" : ""} that need
						{pendingCertifications.length > 1 ? "" : "s"} to be completed to
						receive certain types of work.
					</AlertDescription>
				</Alert>
			)}

			{/* Certifications List */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<BookOpen className="h-5 w-5" />
						Professional Certifications
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{certifications.map((cert) => (
						<div
							key={cert.id}
							className="flex items-center justify-between p-4 border rounded-lg"
						>
							<div className="flex-1">
								<div className="flex items-center gap-2 mb-2">
									<h4 className="font-medium">
										{cert.certification.display_name}
									</h4>
									{getStatusBadge(cert)}
								</div>
								{cert.certification.description && (
									<p className="text-sm text-gray-600 mb-2">
										{cert.certification.description}
									</p>
								)}
								<p className="text-sm text-gray-500">
									{getStatusDescription(cert)}
								</p>
								{cert.opted_out_at && (
									<p className="text-xs text-gray-400 mt-1">
										Opted out on{" "}
										{new Date(cert.opted_out_at).toLocaleDateString()}
									</p>
								)}
								{cert.training_completed_at && (
									<p className="text-xs text-gray-400 mt-1">
										Completed on{" "}
										{new Date(cert.training_completed_at).toLocaleDateString()}
									</p>
								)}
							</div>

							<div className="flex gap-2">
								{!cert.opted_out && cert.status !== "COMPLETED" && (
									<>
										<Button
											size="sm"
											onClick={() => onStartTraining(cert.certification.name)}
											className="flex items-center gap-1"
										>
											Start Training
											<ArrowRight className="h-3 w-3" />
										</Button>
										<Button
											size="sm"
											variant="outline"
											onClick={() => handleOptOut(cert.certification.name)}
										>
											Opt Out
										</Button>
									</>
								)}
								{cert.status === "COMPLETED" && (
									<Button size="sm" variant="outline" disabled>
										<CheckCircle className="h-3 w-3 mr-1" />
										Certified
									</Button>
								)}
								{cert.opted_out && (
									<Button size="sm" variant="outline" disabled>
										<XCircle className="h-3 w-3 mr-1" />
										Opted Out
									</Button>
								)}
							</div>
						</div>
					))}
				</CardContent>
			</Card>

			{/* Summary Stats */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<Card>
					<CardContent className="p-4">
						<div className="flex items-center gap-2">
							<CheckCircle className="h-5 w-5 text-green-600" />
							<div>
								<p className="text-sm text-gray-600">Completed</p>
								<p className="text-2xl font-bold text-green-600">
									{completedCertifications.length}
								</p>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-4">
						<div className="flex items-center gap-2">
							<Clock className="h-5 w-5 text-orange-600" />
							<div>
								<p className="text-sm text-gray-600">Pending</p>
								<p className="text-2xl font-bold text-orange-600">
									{pendingCertifications.length}
								</p>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-4">
						<div className="flex items-center gap-2">
							<XCircle className="h-5 w-5 text-gray-600" />
							<div>
								<p className="text-sm text-gray-600">Opted Out</p>
								<p className="text-2xl font-bold text-gray-600">
									{optedOutCertifications.length}
								</p>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
