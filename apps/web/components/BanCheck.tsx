"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

export function BanCheck() {
    const router = useRouter();

    useEffect(() => {
        // Check for ban markers in localStorage
        const isBanned = localStorage.getItem('rvhelp_banned') === 'true';
        const banMessage = localStorage.getItem('rvhelp_ban_message');

        if (isBanned) {
            const bannedUrl = new URL('/banned', window.location.origin);
            if (banMessage) {
                bannedUrl.searchParams.set('message', banMessage);
            }
            router.push(bannedUrl.toString());
        }
    }, [router]);

    return null; // This component doesn't render anything
} 