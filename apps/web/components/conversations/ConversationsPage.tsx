"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/lib/hooks/useAuth";
import { ConversationResponse } from "@/lib/services/messaging.service";
import { formatDistanceToNow } from "date-fns";
import { CheckCircle, MessageSquare } from "lucide-react";
import { redirect } from "next/navigation";
import { useEffect, useState } from "react";
import ConversationMessageThread from "./ConversationMessageThread";

type ViewerRole = "USER" | "PROVIDER";

interface ConversationsPageProps {
	viewerRole: ViewerRole;
	title?: string;
}

export default function ConversationsPage({
	viewerRole,
	title = "Messages"
}: ConversationsPageProps) {
	const { user, loading: authLoading } = useAuth();
	const [conversations, setConversations] = useState<ConversationResponse[]>(
		[]
	);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selectedConversationId, setSelectedConversationId] = useState<
		string | null
	>(null);
	const [listingId, setListingId] = useState<string | null>(null);

	useEffect(() => {
		if (authLoading) return;

		if (!user) {
			const redirectUrl =
				viewerRole === "USER" ? "/conversations" : "/provider/conversations";
			redirect("/login?redirectUrl=" + encodeURIComponent(redirectUrl));
			return;
		}

		// Inline fetchListingId logic
		const initializeData = async () => {
			// Fetch listing ID for providers
			if (viewerRole === "PROVIDER") {
				try {
					const response = await fetch("/api/user/listings");
					if (response.ok) {
						const data = await response.json();
						setListingId(data[0]?.id || null);
					}
				} catch (error) {
					console.error("Error fetching listing ID:", error);
					setError("Failed to load provider information. Please try again.");
				}
			}

			// Fetch conversations
			try {
				setLoading(true);
				setError(null);

				const context = viewerRole === "USER" ? "user" : "provider";
				const response = await fetch(`/api/conversations?context=${context}`);

				if (!response.ok) {
					throw new Error("Failed to fetch conversations");
				}

				const data = await response.json();
				setConversations(data.conversations || []);
			} catch (error) {
				console.error("Error fetching conversations:", error);
				setError("Failed to load conversations. Please try again.");
			} finally {
				setLoading(false);
			}
		};

		initializeData();
	}, [user, authLoading, viewerRole]);

	// Listen for conversation updates
	useEffect(() => {
		// Handle new messages - refetch all conversations
		const handleConversationUpdate = async () => {
			try {
				const context = viewerRole === "USER" ? "user" : "provider";
				const response = await fetch(`/api/conversations?context=${context}`);

				if (!response.ok) {
					throw new Error("Failed to fetch conversations");
				}

				const data = await response.json();
				setConversations(data.conversations || []);
			} catch (error) {
				console.error("Error fetching conversations:", error);
			}
		};

		// Handle mark as read - update local state only
		const handleConversationMarkedAsRead = (event: CustomEvent) => {
			const { quoteId } = event.detail;
			setConversations((prev) =>
				prev.map((conv) =>
					conv.quote_id === quoteId ? { ...conv, unread_count: 0 } : conv
				)
			);
		};

		window.addEventListener("conversationUpdated", handleConversationUpdate);
		window.addEventListener(
			"conversationMarkedAsRead",
			handleConversationMarkedAsRead
		);

		return () => {
			window.removeEventListener(
				"conversationUpdated",
				handleConversationUpdate
			);
			window.removeEventListener(
				"conversationMarkedAsRead",
				handleConversationMarkedAsRead
			);
		};
	}, [viewerRole]);

	// Auto-select first conversation ONLY ONCE when conversations first load
	useEffect(() => {
		if (!selectedConversationId && conversations.length > 0) {
			const activeConversation =
				conversations.find((conv) => conv.unread_count > 0) || conversations[0];
			setSelectedConversationId(activeConversation.quote_id);
		}
	}, [conversations, selectedConversationId]);

	// Calculate stats
	const totalUnread = conversations.reduce(
		(sum, conv) => sum + conv.unread_count,
		0
	);
	const activeConversations = conversations.filter(
		(conv) => conv.unread_count > 0 || conv.messages.length > 0
	).length;

	// Get the conversation participant info from messages
	const getConversationParticipant = (conversation: ConversationResponse) => {
		if (!conversation.messages.length) {
			return {
				id: "unknown",
				name: "Unknown",
				image: null,
				type: "unknown" as const
			};
		}

		// Find the participant who is NOT the current viewer
		const firstMessage = conversation.messages[0];

		if (viewerRole === "USER") {
			// User sees provider info - look for PROVIDER in sender or recipient
			const providerSender =
				firstMessage.sender.type === "PROVIDER" ? firstMessage.sender : null;
			const providerRecipient =
				firstMessage.recipient.type === "PROVIDER"
					? firstMessage.recipient
					: null;
			const provider = providerSender || providerRecipient;

			return {
				id: provider?.id || "unknown",
				name: provider?.name || "Service Provider",
				image: provider?.avatar || null,
				type: "provider" as const
			};
		} else {
			// Provider sees customer info - look for USER in sender or recipient
			const userSender =
				firstMessage.sender.type === "USER" ? firstMessage.sender : null;
			const userRecipient =
				firstMessage.recipient.type === "USER" ? firstMessage.recipient : null;
			const customer = userSender || userRecipient;

			return {
				id: customer?.id || "unknown",
				name: customer?.name || "Customer",
				image: customer?.avatar || null,
				type: "customer" as const
			};
		}
	};

	// Get job category and RV info from message context (if available)
	const getJobInfo = (conversation: ConversationResponse) => {
		// For now, return placeholder since we don't have job context in the simplified structure
		// TODO: If needed, we can add job context back or fetch it separately
		return {
			category: "Service Request",
			rvInfo: ""
		};
	};

	if (authLoading || loading) {
		return (
			<div className="container mx-auto py-8 max-w-7xl">
				<div className="flex items-center gap-3 mb-6">
					<MessageSquare className="h-8 w-8 text-primary" />
					<h1 className="text-3xl font-bold text-gray-900">{title}</h1>
				</div>
				<Card className="h-[600px]">
					<CardContent className="p-0 h-full">
						<div className="grid grid-cols-1 lg:grid-cols-3 h-full">
							<div className="lg:col-span-1 border-r flex flex-col">
								<div className="p-4 border-b">
									<Skeleton className="h-6 w-32 mb-2" />
									<Skeleton className="h-4 w-24" />
								</div>
								<div className="flex-1 p-2 space-y-2">
									{Array.from({ length: 5 }).map((_, i) => (
										<div key={i} className="p-3 space-y-2">
											<div className="flex items-start gap-3">
												<Skeleton className="h-10 w-10 rounded-full" />
												<div className="flex-1 space-y-1">
													<Skeleton className="h-4 w-32" />
													<Skeleton className="h-3 w-24" />
													<Skeleton className="h-3 w-16" />
												</div>
											</div>
										</div>
									))}
								</div>
							</div>
							<div className="lg:col-span-2">
								<Skeleton className="h-full" />
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto py-8 max-w-7xl">
				<div className="flex items-center gap-3 mb-6">
					<MessageSquare className="h-8 w-8 text-primary" />
					<h1 className="text-3xl font-bold text-gray-900">{title}</h1>
				</div>
				<div className="text-center py-12">
					<p className="text-red-600 mb-4">{error}</p>
					<button
						onClick={() => window.location.reload()}
						className="text-blue-600 hover:text-blue-800 underline"
					>
						Try again
					</button>
				</div>
			</div>
		);
	}

	if (conversations.length === 0) {
		const emptyMessage =
			viewerRole === "USER"
				? "You haven't started any conversations with RV service providers yet."
				: "You don't have any customer conversations yet.";

		return (
			<div className="container mx-auto py-8 max-w-7xl">
				<div className="flex items-center gap-3 mb-6">
					<MessageSquare className="h-8 w-8 text-primary" />
					<h1 className="text-3xl font-bold text-gray-900">{title}</h1>
				</div>
				<div className="text-center py-12">
					<div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<MessageSquare className="w-12 h-12 text-gray-400" />
					</div>
					<h3 className="text-xl font-semibold text-gray-900 mb-2">
						No conversations yet
					</h3>
					<p className="text-gray-600 mb-6 max-w-md mx-auto">{emptyMessage}</p>
				</div>
			</div>
		);
	}

	const selectedConversation = conversations.find(
		(conv) => conv.quote_id === selectedConversationId
	);

	return (
		<div className="container mx-auto py-8 max-w-7xl">
			<div className="flex items-center gap-3 mb-6">
				<MessageSquare className="h-8 w-8 text-primary" />
				<h1 className="text-3xl font-bold text-gray-900">{title}</h1>
				<span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
					{conversations.length} conversation
					{conversations.length !== 1 ? "s" : ""}
				</span>
			</div>

			<Card className="shadow-sm">
				<CardContent className="p-0">
					<div className="grid grid-cols-1 lg:grid-cols-3">
						{/* Conversations List */}
						<div className="lg:col-span-1 border-r flex flex-col">
							<div className="p-4 border-b flex-shrink-0">
								<h3 className="font-semibold text-lg">Conversations</h3>
								<p className="text-sm text-gray-600">
									{activeConversations} active • {totalUnread} unread
								</p>
							</div>
							<div className="flex-1 overflow-hidden">
								<div className="p-2 pr-4">
									{conversations.map((conversation) => {
										const participant =
											getConversationParticipant(conversation);
										const lastMessage =
											conversation.messages?.[conversation.messages.length - 1];
										const jobInfo = getJobInfo(conversation);

										return (
											<div
												key={conversation.quote_id}
												className={`p-3 rounded-lg cursor-pointer transition-all duration-200 mb-3 w-full ${
													selectedConversationId === conversation.quote_id
														? "bg-blue-50 border border-blue-200 shadow-md"
														: "hover:bg-gray-50 border border-gray-200 shadow-sm hover:shadow-md"
												}`}
												onClick={() =>
													setSelectedConversationId(conversation.quote_id)
												}
											>
												<div className="flex items-start gap-3 w-full">
													<div className="relative">
														<Avatar className="h-10 w-10">
															{participant.image ? (
																<AvatarImage
																	src={participant.image}
																	alt={participant.name}
																/>
															) : (
																<AvatarFallback
																	className={
																		participant.type === "provider"
																			? "bg-green-100 text-green-600"
																			: "bg-blue-100 text-blue-600"
																	}
																>
																	{participant.name
																		.split(" ")
																		.map((n) => n[0])
																		.join("")
																		.toUpperCase()}
																</AvatarFallback>
															)}
														</Avatar>
														{conversation.unread_count > 0 && (
															<div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
																{conversation.unread_count > 9
																	? "9+"
																	: conversation.unread_count}
															</div>
														)}
													</div>
													<div className="flex-1 min-w-0">
														<div className="flex items-center justify-between mb-1">
															<h4 className="font-medium text-sm truncate flex-1">
																{participant.name}
															</h4>
															<div className="flex items-center gap-1 flex-shrink-0 ml-2">
																<CheckCircle className="h-4 w-4 text-green-600" />
															</div>
														</div>
														<p className="text-xs text-gray-600 mb-1 capitalize truncate">
															{jobInfo.category}
															{jobInfo.rvInfo && ` • ${jobInfo.rvInfo}`}
														</p>
														{lastMessage ? (
															<p className="text-xs text-gray-600 line-clamp-1 mb-1">
																<span className="font-medium truncate">
																	{lastMessage.sender.name}:
																</span>{" "}
																<span className="truncate">
																	{lastMessage.content}
																</span>
															</p>
														) : (
															<p className="text-xs text-gray-600 line-clamp-1 mb-1">
																New conversation
															</p>
														)}
														<div className="flex items-center justify-between">
															<span className="text-xs text-gray-500 truncate flex-1">
																{formatDistanceToNow(
																	new Date(conversation.last_message_at),
																	{ addSuffix: true }
																)}
															</span>
															<Badge
																variant="secondary"
																className="text-xs flex-shrink-0 ml-2"
															>
																active
															</Badge>
														</div>
													</div>
												</div>
											</div>
										);
									})}
								</div>
							</div>
						</div>

						{/* Message Thread */}
						<div className="lg:col-span-2 flex flex-col min-h-0">
							{selectedConversation ? (
								<>
									{/* Conversation Header */}
									<div className="border-b p-4 bg-white flex-shrink-0">
										<div className="flex items-center gap-3">
											<Avatar className="h-10 w-10">
												{(() => {
													const participant =
														getConversationParticipant(selectedConversation);
													return participant.image ? (
														<AvatarImage
															src={participant.image}
															alt={participant.name}
														/>
													) : (
														<AvatarFallback
															className={
																participant.type === "provider"
																	? "bg-green-100 text-green-600"
																	: "bg-blue-100 text-blue-600"
															}
														>
															{participant.name
																.split(" ")
																.map((n) => n[0])
																.join("")
																.toUpperCase()}
														</AvatarFallback>
													);
												})()}
											</Avatar>
											<div>
												<h3 className="font-medium">
													{
														getConversationParticipant(selectedConversation)
															.name
													}
												</h3>
											</div>
										</div>
									</div>

									{/* Message Thread */}
									<div className="flex-1 min-h-0">
										<ConversationMessageThread
											quoteId={selectedConversation.quote_id}
											viewerRole={viewerRole}
											userId={viewerRole === "USER" ? user?.id : undefined}
											providerId={
												viewerRole === "PROVIDER" ? listingId : undefined
											}
											isVisible={true}
										/>
									</div>
								</>
							) : (
								<div className="h-full flex items-center justify-center">
									<div className="text-center">
										<MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
										<h3 className="text-lg font-medium mb-2">
											Select a conversation
										</h3>
										<p className="text-gray-600">
											Choose a conversation to start messaging
										</p>
									</div>
								</div>
							)}
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
