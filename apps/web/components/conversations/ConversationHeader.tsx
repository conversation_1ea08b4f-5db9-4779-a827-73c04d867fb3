"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { getCategoryName } from "@/lib/categories";
import { ExternalLink, MapPin, Wrench } from "lucide-react";
import Link from "next/link";

type JobData = {
	id: string;
	category: string;
	message: string;
	rv_year?: string;
	rv_make?: string;
	rv_model?: string;
	rv_type?: string;
	status: string;
	warranty_request_id?: string;
	accepted_quote_id?: string;
	warranty_request?: {
		id: string;
		company: {
			name: string;
		};
	} | null;
	accepted_quote?: {
		id: string;
		listing: {
			id: string;
			business_name?: string;
			first_name: string;
			last_name: string;
		};
	} | null;
};

type QuoteData = {
	id: string;
	status: string;
	listing: {
		id: string;
		business_name?: string;
		first_name: string;
		last_name: string;
	};
};

type ConversationHeaderProps = {
	job: JobData;
	quote: QuoteData;
	viewerRole: "USER" | "PROVIDER";
};

export default function ConversationHeader({
	job,
	quote,
	viewerRole
}: ConversationHeaderProps) {
	// Determine if this is a warranty job
	const isWarrantyJob = !!job.warranty_request_id;

	// Determine if this is an accepted job
	const isAcceptedJob = !!job.accepted_quote_id;

	// Get RV info
	const rvInfo = [job.rv_year, job.rv_make, job.rv_model, job.rv_type]
		.filter(Boolean)
		.join(" ");

	// Get navigation link based on user role and job type
	const getNavigationLink = () => {
		if (viewerRole === "PROVIDER") {
			if (isWarrantyJob && isAcceptedJob) {
				// Warranty job that's been accepted - go to jobs page
				return `/provider/jobs/${job.id}`;
			} else {
				// Regular lead or unaccepted warranty job - go to leads page
				return `/provider/leads/${job.id}`;
			}
		} else {
			// Customer - go to service request page
			return `/service-requests/${job.id}`;
		}
	};

	// Get navigation text
	const getNavigationText = () => {
		if (viewerRole === "PROVIDER") {
			if (isWarrantyJob && isAcceptedJob) {
				return "View Job";
			} else {
				return "View Lead";
			}
		} else {
			return "View Service Request";
		}
	};

	// Get status badge
	const getStatusBadge = () => {
		if (isWarrantyJob) {
			return (
				<Badge variant="outline" className="text-xs">
					{job.warranty_request?.company.name} Warranty
				</Badge>
			);
		}

		if (isAcceptedJob) {
			return (
				<Badge variant="default" className="text-xs bg-green-600">
					Accepted
				</Badge>
			);
		}

		return (
			<Badge variant="secondary" className="text-xs">
				{job.status}
			</Badge>
		);
	};

	return (
		<div className="flex items-center justify-between py-2 px-1">
			<div className="flex items-center gap-2 flex-1 min-w-0">
				<Wrench className="h-4 w-4 text-gray-500 flex-shrink-0" />
				<span className="font-medium text-gray-900 text-sm">
					{getCategoryName(job.category)}
				</span>
				{rvInfo && (
					<>
						<span className="text-gray-400">•</span>
						<div className="flex items-center gap-1 text-sm text-gray-600">
							<MapPin className="h-3 w-3 flex-shrink-0" />
							<span className="truncate">{rvInfo}</span>
						</div>
					</>
				)}
				{getStatusBadge()}
			</div>

			{/* Action Button */}
			<div className="flex-shrink-0 ml-2">
				<Button
					asChild
					variant="outline"
					size="sm"
					className="text-xs px-2 py-1 h-7"
				>
					<Link href={getNavigationLink()}>
						{getNavigationText()}
						<ExternalLink className="h-3 w-3 ml-1" />
					</Link>
				</Button>
			</div>
		</div>
	);
}
