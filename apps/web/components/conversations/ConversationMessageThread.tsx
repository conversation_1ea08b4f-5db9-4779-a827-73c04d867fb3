"use client";

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { zodResolver } from "@hookform/resolvers/zod";
import { formatDistanceToNow } from "date-fns";
import { Paperclip, Send } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { ApiQuoteMessage } from "../../types/global";
import ConversationHeader from "./ConversationHeader";

const messageSchema = z.object({
	content: z.string().min(1, "Message cannot be empty")
});

type MessageFormData = z.infer<typeof messageSchema>;

type JobData = {
	id: string;
	category: string;
	message: string;
	rv_year?: string;
	rv_make?: string;
	rv_model?: string;
	rv_type?: string;
	status: string;
	warranty_request_id?: string;
	accepted_quote_id?: string;
	warranty_request?: {
		id: string;
		company: {
			name: string;
		};
	} | null;
	accepted_quote?: {
		id: string;
		listing: {
			id: string;
			business_name?: string;
			first_name: string;
			last_name: string;
		};
	} | null;
};

type QuoteData = {
	id: string;
	status: string;
	listing: {
		id: string;
		business_name?: string;
		first_name: string;
		last_name: string;
	};
};

type ConversationMessageThreadProps = {
	quoteId: string;
	viewerRole: "USER" | "PROVIDER";
	userId?: string;
	providerId?: string;
	isVisible: boolean;
	hideServiceRequestHeader?: boolean;
};

export default function ConversationMessageThread({
	quoteId,
	viewerRole,
	userId,
	providerId,
	isVisible,
	hideServiceRequestHeader = false
}: ConversationMessageThreadProps) {
	const [messages, setMessages] = useState<Array<ApiQuoteMessage>>([]);
	const [jobData, setJobData] = useState<JobData | null>(null);
	const [quoteData, setQuoteData] = useState<QuoteData | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [isSending, setIsSending] = useState(false);
	const [hasMarkedAsRead, setHasMarkedAsRead] = useState(false);
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const pollingIntervalRef = useRef<NodeJS.Timeout>();
	const markAsReadAttemptedRef = useRef<string | null>(null);

	const {
		register,
		handleSubmit,
		reset,
		formState: { errors }
	} = useForm<MessageFormData>({
		resolver: zodResolver(messageSchema)
	});

	// Helper function to determine if sender is current viewer
	const isCurrentViewer = (senderType: "USER" | "PROVIDER") => {
		return senderType === viewerRole;
	};

	// Get other participant info from messages
	const otherParticipant =
		messages.length > 0
			? messages.find((msg) => msg.sender.type !== viewerRole)?.sender ||
			messages.find((msg) => msg.recipient.type !== viewerRole)?.recipient
			: null;

	// Fetch messages when component mounts or quoteId changes
	useEffect(() => {
		fetchMessages();
		setHasMarkedAsRead(false); // Reset when quote changes
		markAsReadAttemptedRef.current = null; // Reset mark as read tracking

		// Set up polling every 30 seconds for real-time updates
		pollingIntervalRef.current = setInterval(() => {
			fetchMessages();
		}, 30000);

		return () => {
			if (pollingIntervalRef.current) {
				clearInterval(pollingIntervalRef.current);
			}
		};
	}, [quoteId]);

	// Mark as read when conversation becomes visible AND we haven't marked it yet
	useEffect(() => {
		if (
			isVisible &&
			!hasMarkedAsRead &&
			messages.length > 0 &&
			markAsReadAttemptedRef.current !== quoteId
		) {
			markAsReadAttemptedRef.current = quoteId;
			markAsRead();
		}
	}, [isVisible, hasMarkedAsRead, messages.length, quoteId]);

	const fetchMessages = async () => {
		try {
			setIsLoading(true);
			const response = await fetch(`/api/conversations/${quoteId}`);
			const data = await response.json();
			if (data.messages) {
				setMessages(data.messages);
			}
			if (data.job) {
				setJobData(data.job);
			}
			if (data.quote) {
				setQuoteData(data.quote);
			}
		} catch (error) {
			console.error("Error fetching messages:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const onSubmit = async (data: MessageFormData) => {
		try {
			setIsSending(true);

			const requestBody: any = {
				content: data.content,
				type: "TEXT",
				attachments: [],
				metadata: {}
			};

			if (viewerRole === "USER") {
				requestBody.userId = userId;
			} else if (viewerRole === "PROVIDER") {
				requestBody.providerId = providerId;
			} else {
				throw new Error("Missing required ID parameter for message sending");
			}

			const response = await fetch(`/api/conversations/${quoteId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(requestBody)
			});

			if (response.ok) {
				const result = await response.json();
				setMessages((prev) => [...prev, result.message]);
				reset();

				// Dispatch custom event to refresh header notification count
				window.dispatchEvent(new CustomEvent("conversationUpdated"));
			} else {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to send message");
			}
		} catch (error) {
			console.error("Error sending message:", error);
		} finally {
			setIsSending(false);
		}
	};

	const markAsRead = async () => {
		if (hasMarkedAsRead) {
			return;
		}

		try {
			const response = await fetch(
				`/api/conversations/${quoteId}/mark-as-read`,
				{
					method: "POST"
				}
			);
			if (!response.ok) throw new Error("Failed to mark messages as read");

			setHasMarkedAsRead(true);

			// Dispatch custom event with quote ID to update specific conversation locally
			window.dispatchEvent(
				new CustomEvent("conversationMarkedAsRead", {
					detail: { quoteId }
				})
			);
		} catch (error) {
			console.error("Error marking messages as read:", error);
		}
	};

	if (isLoading) {
		return (
			<div className="flex flex-col h-full bg-white">
				<div className="flex-1 p-4 overflow-y-auto bg-gray-100">
					<div className="space-y-4">
						{Array.from({ length: 3 }).map((_, i) => (
							<div key={i} className="flex justify-start">
								<div className="max-w-[80%] flex items-start gap-2">
									<Skeleton className="h-8 w-8 rounded-full mt-1" />
									<div>
										<Skeleton className="h-3 w-16 mb-1" />
										<Skeleton className="h-16 w-48 rounded-lg" />
										<Skeleton className="h-3 w-12 mt-1" />
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-full max-h-[calc(100vh-100px)] bg-white">
			{/* Job Details Header */}
			{jobData && quoteData && !hideServiceRequestHeader && (
				<div className="p-4 border-b bg-gray-50">
					<ConversationHeader
						job={jobData}
						quote={quoteData}
						viewerRole={viewerRole}
					/>
				</div>
			)}

			<div className="flex-1 p-4 overflow-y-auto bg-gray-100 min-h-0">
				{messages.length === 0 ? (
					<div className="h-full flex flex-col items-center justify-center text-center p-4">
						<div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
							<Send className="w-8 h-8 text-gray-400" />
						</div>
						<h3 className="text-lg font-medium mb-1">No messages yet</h3>
						<p className="text-gray-500 max-w-md">Start the conversation.</p>
					</div>
				) : (
					<div className="space-y-4">
						{messages.map((message) => {
							const isCurrentUser = isCurrentViewer(message.sender.type);
							return (
								<div
									key={message.id}
									className={`flex ${isCurrentUser ? "justify-end" : "justify-start"}`}
								>
									{isCurrentUser ? (
										// Current user's message - no avatar, right aligned
										<div className="max-w-[80%]">
											<div className="text-xs text-gray-600 mb-1 text-right">
												You
											</div>
											<div className="bg-green-700 text-white rounded-lg p-3">
												<p className="text-sm">{message.content}</p>
											</div>
											<div className="mt-1 flex justify-end">
												<span className="text-xs text-gray-500">
													{formatDistanceToNow(new Date(message.created_at), {
														addSuffix: true
													})}
												</span>
											</div>
										</div>
									) : (
										// Other person's message - avatar on left, name above message
										<div className="max-w-[80%] flex items-start gap-2">
											<Avatar className="h-8 w-8 mt-1">
												<AvatarFallback className="text-gray-100 bg-gray-400">
													{message.sender.name?.[0] || "U"}
												</AvatarFallback>
											</Avatar>
											<div>
												<div className="text-xs text-gray-600 mb-1">
													{message.sender.name}
												</div>
												<div className="bg-blue-500 text-white rounded-lg p-3">
													<p className="text-sm">{message.content}</p>
												</div>
												<div className="mt-1 flex justify-start">
													<span className="text-xs text-gray-500">
														{formatDistanceToNow(new Date(message.created_at), {
															addSuffix: true
														})}
													</span>
												</div>
											</div>
										</div>
									)}
								</div>
							);
						})}
						<div ref={messagesEndRef} />
					</div>
				)}
			</div>

			<div className="border-t p-4 bg-white flex-shrink-0">
				<form onSubmit={handleSubmit(onSubmit)} className="flex gap-2">
					<Button
						type="button"
						variant="outline"
						size="icon"
						className="shrink-0"
					>
						<Paperclip className="h-4 w-4" />
					</Button>
					<Input
						type="text"
						placeholder="Type your message..."
						className="flex-1"
						{...register("content")}
					/>
					<Button
						type="submit"
						className="shrink-0 bg-green-700 hover:bg-green-800"
						disabled={isSending}
					>
						<Send className="h-4 w-4 mr-2" />
						Send
					</Button>
				</form>
				{errors.content && (
					<p className="text-sm text-red-500 mt-1">{errors.content.message}</p>
				)}
			</div>
		</div>
	);
}
