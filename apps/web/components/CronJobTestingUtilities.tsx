"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>D<PERSON><PERSON>, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	getBusinessDayMessage,
	isBusinessDay
} from "@/lib/utils/business-days";
import {
	AlertCircle,
	CheckCircle,
	Clock,
	Info,
	Mail,
	MessageSquare,
	RefreshCw,
	Settings,
	Star,
	XCircle
} from "lucide-react";
import { useState } from "react";

interface CronJobResult {
	success: boolean;
	message: string;
	data?: any;
	error?: string;
}

interface CronJobTest {
	id: string;
	name: string;
	description: string;
	endpoint: string;
	icon: React.ReactNode;
	timeframe: string;
}

const CRON_JOBS: CronJobTest[] = [
	{
		id: "remind-providers",
		name: "24h Provider Reminder",
		description:
			"Sends email/SMS reminders to providers who haven't responded to jobs created 24-25 hours ago",
		endpoint: "/api/test/cron",
		icon: <Clock className="h-4 w-4" />,
		timeframe: "24-25 hours"
	},
	{
		id: "lead-follow-up",
		name: "24h Customer Follow-up",
		description:
			"Sends follow-up emails to customers 24 hours after job submission",
		endpoint: "/api/test/cron",
		icon: <Mail className="h-4 w-4" />,
		timeframe: "24 hours"
	},
	{
		id: "offers",
		name: "48h Offer Reminder",
		description:
			"Sends Pro membership offer reminders to free users 48 hours after job creation",
		endpoint: "/api/test/cron",
		icon: <Star className="h-4 w-4" />,
		timeframe: "48 hours"
	},
	{
		id: "job-72h-reminders",
		name: "72h Job Reminders",
		description:
			"Sends escalation reminders to customers about jobs with no responses after 72 hours",
		endpoint: "/api/test/cron",
		icon: <AlertCircle className="h-4 w-4" />,
		timeframe: "72 hours"
	},
	{
		id: "quote-72h-reminders",
		name: "72h Quote Reminders",
		description:
			"Sends urgent reminders to providers about pending quotes after 72 hours",
		endpoint: "/api/test/cron",
		icon: <Clock className="h-4 w-4" />,
		timeframe: "72 hours"
	},
	{
		id: "job-1w-reminders",
		name: "1-Week Job Reminders",
		description:
			"Sends 'last chance' reminders to customers about jobs with no responses after 1 week",
		endpoint: "/api/test/cron",
		icon: <AlertCircle className="h-4 w-4" />,
		timeframe: "1 week"
	},
	{
		id: "quote-1w-reminders",
		name: "1-Week Quote Reminders",
		description:
			"Sends final notice reminders to providers about pending quotes after 1 week",
		endpoint: "/api/test/cron",
		icon: <Clock className="h-4 w-4" />,
		timeframe: "1 week"
	},
	{
		id: "job-expiration",
		name: "Final Job Expiration",
		description:
			"Marks jobs as expired after 10 days when no quotes have been accepted (final step)",
		endpoint: "/api/test/cron",
		icon: <XCircle className="h-4 w-4" />,
		timeframe: "10 days"
	},
	{
		id: "review-requests",
		name: "Review Requests",
		description:
			"Sends review request emails to customers after job completion",
		endpoint: "/api/test/cron",
		icon: <MessageSquare className="h-4 w-4" />,
		timeframe: "Variable"
	}
];

export default function CronJobTestingUtilities() {
	const [results, setResults] = useState<Record<string, CronJobResult>>({});
	const [loading, setLoading] = useState<Record<string, boolean>>({});
	const [showUtilities, setShowUtilities] = useState(false);

	const testCronJob = async (job: CronJobTest) => {
		setLoading((prev) => ({ ...prev, [job.id]: true }));

		try {
			const response = await fetch(job.endpoint, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ job: job.id })
			});

			const data = await response.json();

			if (response.ok) {
				setResults((prev) => ({
					...prev,
					[job.id]: {
						success: true,
						message: `Success: ${data.message || "Cron job executed successfully"}`,
						data
					}
				}));
			} else {
				setResults((prev) => ({
					...prev,
					[job.id]: {
						success: false,
						message: `Error: ${data.error || "Unknown error"}`,
						error: data.error
					}
				}));
			}
		} catch (error) {
			setResults((prev) => ({
				...prev,
				[job.id]: {
					success: false,
					message: `Network Error: ${error instanceof Error ? error.message : "Unknown error"}`,
					error: error instanceof Error ? error.message : "Unknown error"
				}
			}));
		} finally {
			setLoading((prev) => ({ ...prev, [job.id]: false }));
		}
	};

	const clearResults = () => {
		setResults({});
	};

	const currentlyBusinessDay = isBusinessDay();

	if (!showUtilities) {
		return (
			<Alert className="border-blue-200 bg-blue-50 mb-6">
				<Settings className="h-4 w-4 text-blue-600" />
				<AlertTitle className="text-blue-800">Development Tools</AlertTitle>
				<AlertDescription className="flex justify-between items-center">
					<span className="text-blue-700">
						Cron job testing utilities are available for development
					</span>
					<Button
						variant="outline"
						size="sm"
						onClick={() => setShowUtilities(true)}
						className="border-blue-300 text-blue-700 hover:bg-blue-100"
					>
						Show Utilities
					</Button>
				</AlertDescription>
			</Alert>
		);
	}

	return (
		<Card className="mb-6 border-blue-200">
			<CardHeader>
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<Settings className="h-5 w-5 text-blue-600" />
						<CardTitle className="text-blue-800">
							Cron Job Testing Utilities
						</CardTitle>
					</div>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							onClick={clearResults}
							className="text-gray-600"
						>
							Clear Results
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => setShowUtilities(false)}
							className="text-gray-600"
						>
							Hide
						</Button>
					</div>
				</div>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Business Day Policy Notice */}
				<Alert
					className={`border-2 ${currentlyBusinessDay ? "border-green-200 bg-green-50" : "border-orange-200 bg-orange-50"}`}
				>
					<Info
						className={`h-4 w-4 ${currentlyBusinessDay ? "text-green-600" : "text-orange-600"}`}
					/>
					<AlertTitle
						className={
							currentlyBusinessDay ? "text-green-800" : "text-orange-800"
						}
					>
						Business Day Policy
					</AlertTitle>
					<AlertDescription
						className={
							currentlyBusinessDay ? "text-green-700" : "text-orange-700"
						}
					>
						<div className="space-y-2">
							<p>{getBusinessDayMessage()}</p>
							<p className="text-sm">
								<strong>
									Today is{" "}
									{currentlyBusinessDay
										? "a business day"
										: "Sunday (non-business day)"}
								</strong>
								{!currentlyBusinessDay &&
									" - reminder jobs will be skipped until Monday"}
							</p>
						</div>
					</AlertDescription>
				</Alert>

				{CRON_JOBS.map((job) => (
					<div key={job.id} className="space-y-2">
						<div className="flex items-center justify-between p-3 border rounded-lg">
							<div className="flex items-center gap-3">
								{job.icon}
								<div>
									<h4 className="font-medium">{job.name}</h4>
									<p className="text-sm text-gray-600">{job.description}</p>
									<p className="text-xs text-gray-500">
										Timeframe: {job.timeframe}
									</p>
								</div>
							</div>
							<Button
								onClick={() => testCronJob(job)}
								disabled={loading[job.id]}
								variant="outline"
								size="sm"
								className="flex items-center gap-2"
							>
								{loading[job.id] ? (
									<RefreshCw className="h-4 w-4 animate-spin" />
								) : (
									<RefreshCw className="h-4 w-4" />
								)}
								Test
							</Button>
						</div>

						{/* Results */}
						{results[job.id] && (
							<Alert
								variant={results[job.id].success ? "default" : "destructive"}
								className={
									results[job.id].success
										? "border-green-200 bg-green-50"
										: "border-red-200 bg-red-50"
								}
							>
								{results[job.id].success ? (
									<CheckCircle className="h-4 w-4 text-green-600" />
								) : (
									<XCircle className="h-4 w-4 text-red-600" />
								)}
								<AlertTitle
									className={
										results[job.id].success ? "text-green-800" : "text-red-800"
									}
								>
									{results[job.id].success ? "Success" : "Error"}
								</AlertTitle>
								<AlertDescription
									className={
										results[job.id].success ? "text-green-700" : "text-red-700"
									}
								>
									{results[job.id].message}
									{results[job.id].data && (
										<details className="mt-2">
											<summary className="cursor-pointer text-sm">
												View Details
											</summary>
											<pre className="mt-2 text-xs bg-white p-2 rounded border overflow-auto">
												{JSON.stringify(results[job.id].data, null, 2)}
											</pre>
										</details>
									)}
								</AlertDescription>
							</Alert>
						)}
					</div>
				))}
			</CardContent>
		</Card>
	);
}
