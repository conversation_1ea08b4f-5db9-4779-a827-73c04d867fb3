"use client";

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { zodResolver } from "@hookform/resolvers/zod";
import { formatDate, formatDistanceToNow } from "date-fns";
import { Send } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { ApiQuoteMessage } from "../types/global";

const messageSchema = z.object({
	content: z.string().min(1, "Message cannot be empty")
});

type MessageFormData = z.infer<typeof messageSchema>;

type JobContext = {
	message: string;
	category: string;
	rvYear?: string;
	rvMake?: string;
	rvModel?: string;
	rvType?: string;
	location?: {
		address: string;
		city: string;
		state: string;
	};
	createdAt: string;
	customerName: string;
};

type MessageThreadProps = {
	quoteId: string;
	viewerRole: "USER" | "PROVIDER";
	// Either userId (for USER role) or providerId (for PROVIDER role)
	userId?: string;
	providerId?: string;
	onMessageSent: (message: ApiQuoteMessage) => void;
	onMessagesRead?: () => void; // New callback for when messages are marked as read
	jobContext?: JobContext;
	disableMessages?: boolean;
};

export default function MessageThread({
	quoteId,
	viewerRole,
	userId,
	providerId,
	onMessageSent,
	onMessagesRead,
	jobContext,
	disableMessages
}: MessageThreadProps) {
	const [messages, setMessages] = useState<Array<ApiQuoteMessage>>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [isInitialLoad, setIsInitialLoad] = useState(true);
	const [isSending, setIsSending] = useState(false);
	const [hasMarkedAsRead, setHasMarkedAsRead] = useState(false);
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const pollingIntervalRef = useRef<NodeJS.Timeout>();

	const {
		register,
		handleSubmit,
		reset,
		formState: { errors }
	} = useForm<MessageFormData>({
		resolver: zodResolver(messageSchema)
	});

	// Helper function to determine if sender is current viewer
	const isCurrentViewer = (senderType: "USER" | "PROVIDER") => {
		return senderType === viewerRole;
	};

	// Get other participant info from messages
	const otherParticipant =
		messages.length > 0
			? messages.find((msg) => msg.sender.type !== viewerRole)?.sender ||
				messages.find((msg) => msg.recipient.type !== viewerRole)?.recipient
			: null;

	useEffect(() => {
		// Reset state when quoteId changes
		if (isInitialLoad || !hasMarkedAsRead) {
			fetchMessages();
			if (!hasMarkedAsRead) {
				markAsRead();
			}
			setIsInitialLoad(false);
		}

		// Start polling for new messages every 30 seconds
		pollingIntervalRef.current = setInterval(() => {
			fetchMessages();
		}, 30000);

		// Cleanup interval on unmount
		return () => {
			if (pollingIntervalRef.current) {
				clearInterval(pollingIntervalRef.current);
			}
		};
	}, [quoteId]); // Add quoteId as dependency

	// Reset hasMarkedAsRead when quoteId changes
	useEffect(() => {
		setHasMarkedAsRead(false);
	}, [quoteId]);

	const fetchMessages = async () => {
		try {
			setIsLoading(true);
			const response = await fetch(`/api/conversations/${quoteId}`);
			const data = await response.json();
			if (data.messages) {
				setMessages(data.messages);
			}
		} catch (error) {
			console.error("Error fetching messages:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const onSubmit = async (data: MessageFormData) => {
		try {
			setIsSending(true);

			// Determine which ID to send based on viewer role
			const requestBody: any = {
				content: data.content,
				type: "TEXT",
				attachments: [],
				metadata: {}
			};

			if (viewerRole === "USER") {
				requestBody.userId = userId;
			} else if (viewerRole === "PROVIDER") {
				requestBody.providerId = providerId;
			} else {
				throw new Error("Missing required ID parameter for message sending");
			}

			const response = await fetch(`/api/conversations/${quoteId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(requestBody)
			});

			if (response.ok) {
				const result = await response.json();
				setMessages((prev) => [...prev, result.message]);
				onMessageSent(result.message);
				reset();
			} else {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to send message");
			}
		} catch (error) {
			console.error("Error sending message:", error);
		} finally {
			setIsSending(false);
		}
	};

	const markAsRead = async () => {
		if (hasMarkedAsRead) {
			return;
		}

		try {
			const response = await fetch(
				`/api/conversations/${quoteId}/mark-as-read`,
				{
					method: "POST"
				}
			);
			if (!response.ok) throw new Error("Failed to mark messages as read");

			setHasMarkedAsRead(true);

			// Notify parent components that messages have been marked as read
			if (onMessagesRead) {
				onMessagesRead();
			}
		} catch (error) {
			console.error("Error marking messages as read:", error);
		}
	};

	if (isLoading && isInitialLoad) {
		return (
			<div className="flex flex-col min-h-[calc(100vh-100px)] border rounded-md overflow-hidden bg-white">
				{/* Header skeleton */}
				<div className="border-b p-4 bg-white">
					<div className="flex items-center gap-4">
						<Skeleton className="h-12 w-12 rounded-full" />
						<div className="space-y-2">
							<Skeleton className="h-4 w-32" />
							<Skeleton className="h-3 w-24" />
						</div>
					</div>
				</div>

				{/* Messages area skeleton */}
				<div className="flex-1 p-4 overflow-y-auto bg-gray-100">
					<div className="space-y-4">
						{/* Mock conversation with alternating message sides */}
						<div className="flex justify-start">
							<div className="max-w-[80%] flex items-start gap-2">
								<Skeleton className="h-8 w-8 rounded-full mt-1" />
								<div>
									<Skeleton className="h-3 w-16 mb-1" />
									<Skeleton className="h-10 w-48 rounded-lg" />
									<Skeleton className="h-3 w-12 mt-1" />
								</div>
							</div>
						</div>

						<div className="flex justify-end">
							<div className="max-w-[80%]">
								<Skeleton className="h-10 w-36 rounded-lg" />
								<div className="mt-1 flex justify-end">
									<Skeleton className="h-3 w-12" />
								</div>
							</div>
						</div>

						<div className="flex justify-start">
							<div className="max-w-[80%] flex items-start gap-2">
								<Skeleton className="h-8 w-8 rounded-full mt-1" />
								<div>
									<Skeleton className="h-3 w-16 mb-1" />
									<Skeleton className="h-16 w-56 rounded-lg" />
									<Skeleton className="h-3 w-12 mt-1" />
								</div>
							</div>
						</div>

						<div className="flex justify-end">
							<div className="max-w-[80%]">
								<Skeleton className="h-8 w-28 rounded-lg" />
								<div className="mt-1 flex justify-end">
									<Skeleton className="h-3 w-12" />
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Input form skeleton */}
				<div className="border-t p-4 bg-white">
					<div className="flex gap-2">
						<Skeleton className="h-10 w-10 rounded-md" />
						<Skeleton className="h-10 flex-1 rounded-md" />
						<Skeleton className="h-10 w-20 rounded-md" />
					</div>
				</div>
			</div>
		);
	}

	return (
		<>
			<div className="flex flex-col h-full border rounded-md overflow-hidden bg-white">
				{otherParticipant && (
					<div className="border-b p-4 bg-white flex-shrink-0">
						<div className="flex items-center gap-4">
							<Avatar className="h-12 w-12 bg-gray-100">
								<AvatarFallback className="text-gray-500">
									{otherParticipant.name?.[0] || "U"}
								</AvatarFallback>
							</Avatar>
							<div>
								<h3 className="font-medium">{otherParticipant.name}</h3>
							</div>
						</div>
					</div>
				)}

				<div className="flex-1 p-4 overflow-y-auto bg-gray-100 min-h-0">
					{messages.length === 0 && !jobContext ? (
						<div className="h-full flex flex-col items-center justify-center text-center p-4">
							<div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
								<Send className="w-8 h-8 text-gray-400" />
							</div>
							<h3 className="text-lg font-medium mb-1">No messages yet</h3>
							<p className="text-gray-500 max-w-md">Start the conversation.</p>
						</div>
					) : (
						<div className="space-y-4">
							{/* Simple System Message */}
							{jobContext && (
								<div className="flex justify-center mb-4">
									<div className="bg-gray-100 text-gray-600 text-sm px-3 py-2 rounded-lg text-center">
										<p>Your job and contact details were shared</p>
										<p className="text-xs mt-1">
											{formatDate(
												new Date(jobContext.createdAt),
												"MMM d, yyyy"
											)}
										</p>
									</div>
								</div>
							)}

							{/* Regular Messages */}
							{messages.map((message) => {
								const isCurrentUser = isCurrentViewer(message.sender.type);

								// Handle system messages - use the actual sender information
								if (message.type === "SYSTEM") {
									// System messages can be from either user or provider, so use the actual sender type
									const isCurrentUser = isCurrentViewer(message.sender.type);

									return (
										<div
											key={message.id}
											className={`flex ${isCurrentUser ? "justify-end" : "justify-start"}`}
										>
											{isCurrentUser ? (
												// Current user's message - no avatar, right aligned, green background
												<div className="max-w-[80%]">
													<div className="text-xs text-gray-600 mb-1 text-right">
														You
													</div>
													<div className="bg-green-700 text-white rounded-lg p-3">
														<p className="text-sm whitespace-pre-wrap">
															{message.content}
														</p>
													</div>
													<div className="mt-1 flex justify-end">
														<span className="text-xs text-gray-500">
															{formatDistanceToNow(
																new Date(message.created_at),
																{
																	addSuffix: true
																}
															)}
														</span>
													</div>
												</div>
											) : (
												// Other person's message - avatar on left, name above message, blue background
												<div className="max-w-[80%] flex items-start gap-2">
													<Avatar className="h-8 w-8 mt-1">
														<AvatarFallback className="text-gray-100 bg-gray-400">
															{message.sender.name?.[0] || "U"}
														</AvatarFallback>
													</Avatar>
													<div>
														<div className="text-xs text-gray-600 mb-1">
															{message.sender.name}
														</div>
														<div className="bg-blue-500 text-white rounded-lg p-3">
															<p className="text-sm whitespace-pre-wrap">
																{message.content}
															</p>
														</div>
														<div className="mt-1 flex justify-start">
															<span className="text-xs text-gray-500">
																{formatDistanceToNow(
																	new Date(message.created_at),
																	{
																		addSuffix: true
																	}
																)}
															</span>
														</div>
													</div>
												</div>
											)}
										</div>
									);
								}

								return (
									<div
										key={message.id}
										className={`flex ${isCurrentUser ? "justify-end" : "justify-start"}`}
									>
										{isCurrentUser ? (
											// Current user's message - no avatar, right aligned, green background
											<div className="max-w-[80%]">
												<div className="text-xs text-gray-600 mb-1 text-right">
													You
												</div>
												<div className="bg-green-700 text-white rounded-lg p-3">
													<p className="text-sm">{message.content}</p>
												</div>
												<div className="mt-1 flex justify-end">
													<span className="text-xs text-gray-500">
														{formatDistanceToNow(new Date(message.created_at), {
															addSuffix: true
														})}
													</span>
												</div>
											</div>
										) : (
											// Other person's message - avatar on left, name above message, blue background
											<div className="max-w-[80%] flex items-start gap-2">
												<Avatar className="h-8 w-8 mt-1">
													<AvatarFallback className="text-gray-100 bg-gray-400">
														{message.sender.name?.[0] || "U"}
													</AvatarFallback>
												</Avatar>
												<div>
													<div className="text-xs text-gray-600 mb-1">
														{message.sender.name}
													</div>
													<div className="bg-blue-500 text-white rounded-lg p-3">
														<p className="text-sm">{message.content}</p>
													</div>
													<div className="mt-1 flex justify-start">
														<span className="text-xs text-gray-500">
															{formatDistanceToNow(
																new Date(message.created_at),
																{
																	addSuffix: true
																}
															)}
														</span>
													</div>
												</div>
											</div>
										)}
									</div>
								);
							})}
							<div ref={messagesEndRef} />
						</div>
					)}
				</div>

				<div className="border-t p-4 bg-white flex-shrink-0">
					<form onSubmit={handleSubmit(onSubmit)} className="flex gap-2">
						<Input
							type="text"
							placeholder="Type your message..."
							className="flex-1"
							disabled={disableMessages}
							{...register("content")}
						/>
						<Button
							type="submit"
							className="shrink-0 bg-green-700 hover:bg-green-800"
							disabled={isSending || disableMessages}
						>
							<Send className="h-4 w-4 mr-2" />
							Send
						</Button>
					</form>
					{errors.content && (
						<p className="text-sm text-red-500 mt-1">
							{errors.content.message}
						</p>
					)}
				</div>
			</div>
		</>
	);
}
