"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Download, Loader2 } from "lucide-react";
import { useState } from "react";
import toast from "react-hot-toast";

interface LeadMagnet {
    id: string;
    title: string;
    description: string;
    image?: string;
}

interface LeadMagnetFormProps {
    leadMagnet: LeadMagnet;
    variant?: "sidebar" | "inline" | "compact";
    className?: string;
}

export function LeadMagnetForm({ leadMagnet, variant = "inline", className }: LeadMagnetFormProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [formData, setFormData] = useState({
        email: "",
        first_name: "",
        last_name: ""
    });

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.email) {
            toast.error("Email is required");
            return;
        }

        setIsSubmitting(true);

        try {
            const response = await fetch("/api/newsletter/subscribe-lead-magnet", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    ...formData,
                    lead_magnet_id: leadMagnet.id
                })
            });

            if (response.ok) {
                const data = await response.json();
                setIsSubmitted(true);
                toast.success("Thanks! Check your email for your free content.");
            } else {
                const errorData = await response.json();
                toast.error(errorData.error || "Failed to subscribe. Please try again.");
            }
        } catch (error) {
            console.error("Subscription error:", error);
            toast.error("Something went wrong. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData(prev => ({
            ...prev,
            [field]: e.target.value
        }));
    };

    if (isSubmitted) {


        return (
            <Card className={`${className}`}>
                <CardContent className="p-6 text-center">
                    <div className="flex justify-center mb-4">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <Download className="w-6 h-6 text-green-600" />
                        </div>
                    </div>
                    <h3 className="text-lg font-semibold mb-2">Success!</h3>
                    <p className="text-gray-600">
                        Check your email for your free content. You should receive it within a few minutes.
                    </p>
                </CardContent>
            </Card>
        );
    }

    const showImage = leadMagnet.image && variant !== "compact";
    const formLayout = variant === "compact" ? "md:grid-cols-3 gap-4" : "grid-cols-2 gap-3";

    return (
        <Card className={`${className}`}>
            <div className="p-4 pb-0">
                {showImage && (
                    <div className="mb-4">
                        <img
                            src={leadMagnet.image}
                            alt={leadMagnet.title}
                            className="w-full h-32 object-cover rounded-lg"
                        />
                    </div>
                )}
                <CardTitle className={variant === "compact" ? "text-lg text-center" : "text-xl"}>
                    {leadMagnet.title}
                </CardTitle>
            </div>
            <div className={"p-4"}>
                <p className={`text-gray-600 mb-6 ${variant === "compact" ? "text-center text-sm" : ""}`}>
                    {leadMagnet.description}
                </p>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className={`grid ${formLayout}`}>
                        <div>
                            <Label htmlFor="first_name" className={variant === "compact" ? "text-sm" : ""}>
                                First Name
                            </Label>
                            <Input
                                id="first_name"
                                type="text"
                                value={formData.first_name}
                                onChange={handleInputChange("first_name")}
                                placeholder="John"
                                disabled={isSubmitting}
                                className={variant === "compact" ? "h-9" : ""}
                            />
                        </div>
                        <div>
                            <Label htmlFor="last_name" className={variant === "compact" ? "text-sm" : ""}>
                                Last Name
                            </Label>
                            <Input
                                id="last_name"
                                type="text"
                                value={formData.last_name}
                                onChange={handleInputChange("last_name")}
                                placeholder="Doe"
                                disabled={isSubmitting}
                                className={variant === "compact" ? "h-9" : ""}
                            />
                        </div>
                        {variant === "compact" && (
                            <div>
                                <Label htmlFor="email" className="text-sm">Email Address *</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={formData.email}
                                    onChange={handleInputChange("email")}
                                    placeholder="<EMAIL>"
                                    required
                                    disabled={isSubmitting}
                                    className="h-9"
                                />
                            </div>
                        )}
                    </div>

                    {variant !== "compact" && (
                        <div>
                            <Label htmlFor="email">Email Address *</Label>
                            <Input
                                id="email"
                                type="email"
                                value={formData.email}
                                onChange={handleInputChange("email")}
                                placeholder="<EMAIL>"
                                required
                                disabled={isSubmitting}
                            />
                        </div>
                    )}

                    <Button
                        type="submit"
                        className={`w-full ${variant === "compact" ? "h-9" : ""}`}
                        disabled={isSubmitting}
                    >
                        {isSubmitting ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Getting Your Download...
                            </>
                        ) : (
                            <>
                                <Download className="w-4 h-4 mr-2" />
                                Get Free Download
                            </>
                        )}
                    </Button>
                </form>

                <p className={`text-gray-500 mt-4 text-center ${variant === "compact" ? "text-xs" : "text-xs"}`}>
                    By submitting this form, you'll receive our newsletter and the free download.
                    You can unsubscribe at any time.
                </p>
            </div>
        </Card>
    );
} 