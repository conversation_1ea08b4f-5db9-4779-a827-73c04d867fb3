"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { Loader2, Mail } from "lucide-react";
import { useState } from "react";
import toast from "react-hot-toast";

interface BlogNewsletterFormProps {
    variant?: "sidebar" | "inline" | "compact";
    className?: string;
    title?: string;
    description?: string;
}

export function BlogNewsletterForm({
    variant = "inline",
    className,
    title = "Stay Updated",
    description = "Get the latest RV tips, guides, and industry insights delivered to your inbox."
}: BlogNewsletterFormProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [formData, setFormData] = useState({
        email: "",
        first_name: "",
        last_name: ""
    });

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.email) {
            toast.error("Email is required");
            return;
        }

        setIsSubmitting(true);

        try {
            const response = await fetch("/api/newsletter/subscribe", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    ...formData,
                    source: "Blog Newsletter Form"
                })
            });

            if (response.ok) {
                setIsSubmitted(true);
                toast.success("Thanks for subscribing! Check your email for confirmation.");
            } else {
                const errorData = await response.json();
                toast.error(errorData.error || "Failed to subscribe. Please try again.");
            }
        } catch (error) {
            console.error("Subscription error:", error);
            toast.error("Something went wrong. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData(prev => ({
            ...prev,
            [field]: e.target.value
        }));
    };

    const cardClasses = variant === "sidebar"
        ? "max-w-sm"
        : variant === "compact"
            ? "max-w-lg mx-auto"
            : "max-w-md mx-auto";

    if (isSubmitted) {
        return (
            <Card className={`${cardClasses} ${className}`}>
                <CardContent className="p-6 text-center">
                    <div className="flex justify-center mb-4">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <Mail className="w-6 h-6 text-green-600" />
                        </div>
                    </div>
                    <h3 className="text-lg font-semibold mb-2">Welcome!</h3>
                    <p className="text-gray-600">
                        Thanks for subscribing to our newsletter. Check your email for confirmation.
                    </p>
                </CardContent>
            </Card>
        );
    }

    const formLayout = variant === "compact" ? "md:grid-cols-3 gap-4" : "grid-cols-2 gap-3";

    return (
        <Card className={`${className}`}>
            <div className="p-4 pb-0">
                <CardTitle className={variant === "compact" ? "text-lg text-center" : "text-xl"}>
                    {title}
                </CardTitle>
            </div>
            <div className={cn(
                "p-4",
                variant === "compact" && "p-0"
            )}>
                <p className={`text-gray-600 mb-6 ${variant === "compact" ? "text-center text-sm" : ""}`}>
                    {description}
                </p>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className={`grid ${formLayout}`}>
                        <div>
                            <Label htmlFor="first_name" className={variant === "compact" ? "text-sm" : ""}>
                                First Name
                            </Label>
                            <Input
                                id="first_name"
                                type="text"
                                value={formData.first_name}
                                onChange={handleInputChange("first_name")}
                                placeholder="John"
                                disabled={isSubmitting}
                                className={variant === "compact" ? "h-9" : ""}
                            />
                        </div>
                        <div>
                            <Label htmlFor="last_name" className={variant === "compact" ? "text-sm" : ""}>
                                Last Name
                            </Label>
                            <Input
                                id="last_name"
                                type="text"
                                value={formData.last_name}
                                onChange={handleInputChange("last_name")}
                                placeholder="Doe"
                                disabled={isSubmitting}
                                className={variant === "compact" ? "h-9" : ""}
                            />
                        </div>
                        {variant === "compact" && (
                            <div>
                                <Label htmlFor="email" className="text-sm">Email Address *</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={formData.email}
                                    onChange={handleInputChange("email")}
                                    placeholder="<EMAIL>"
                                    required
                                    disabled={isSubmitting}
                                    className="h-9"
                                />
                            </div>
                        )}
                    </div>

                    {variant !== "compact" && (
                        <div>
                            <Label htmlFor="email">Email Address *</Label>
                            <Input
                                id="email"
                                type="email"
                                value={formData.email}
                                onChange={handleInputChange("email")}
                                placeholder="<EMAIL>"
                                required
                                disabled={isSubmitting}
                            />
                        </div>
                    )}

                    <Button
                        type="submit"
                        className={`w-full ${variant === "compact" ? "h-9" : ""}`}
                        disabled={isSubmitting}
                    >
                        {isSubmitting ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Subscribing...
                            </>
                        ) : (
                            <>
                                <Mail className="w-4 h-4 mr-2" />
                                Subscribe to Newsletter
                            </>
                        )}
                    </Button>
                </form>

                <p className={`text-gray-500 mt-4 text-center ${variant === "compact" ? "text-xs" : "text-xs"}`}>
                    No spam, ever. Unsubscribe at any time.
                </p>
            </div>
        </Card>
    );
} 