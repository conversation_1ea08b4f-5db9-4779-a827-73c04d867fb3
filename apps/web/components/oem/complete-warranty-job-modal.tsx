import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { ResolutionStatus } from "@prisma/client";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { JobWithQuoteAndMessages, QuoteWithMessages } from "../types";

interface CompleteJobModalProps {
	open: boolean;
	onClose: () => void;
	job: JobWithQuoteAndMessages;
	quote: QuoteWithMessages;
	onJobCompleted: () => void;
}

const resolutionStatusOptions = [
	{ value: "completed", label: "Completed Successfully" },
	{ value: "cancelled", label: "Cancelled" },
	{ value: "no_response", label: "No Response from Customer" },
	{ value: "not_viable", label: "Not Viable" },
	{ value: "referred", label: "Referred to Another Provider" },
	{ value: "other", label: "Other" }
];

export function CompleteJobModal({
	open,
	onClose,
	job,
	quote,
	onJobCompleted
}: CompleteJobModalProps) {
	const [step, setStep] = useState<"confirm" | "options">("confirm");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [resolutionStatus, setResolutionStatus] = useState<ResolutionStatus>(
		ResolutionStatus.COMPLETED
	);
	const [notes, setNotes] = useState("");
	const [sendInvoice, setSendInvoice] = useState(false);
	const [requestReview, setRequestReview] = useState(false);
	const [reviewDelay, setReviewDelay] = useState(0);
	const [reviewDelayUnit, setReviewDelayUnit] = useState<"hours" | "days">(
		"hours"
	);

	const handleCompleteJob = async () => {
		if (!quote) {
			toast.error("No job found");
			return;
		}

		setIsSubmitting(true);
		try {
			const response = await fetch(
				`/api/provider/quotes/${quote.id}/complete`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						// Existing API structure
						sendInvoice,
						requestReview,
						reviewDelayHours: requestReview
							? reviewDelayUnit === "days"
								? reviewDelay * 24
								: reviewDelay
							: undefined,
						// Additional resolution data (will be stored in metadata or handled by updated API)
						resolutionStatus,
						resolutionNotes: notes.trim() || undefined
					})
				}
			);

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.message || "Failed to complete job");
			}

			toast.success("Job completed successfully");
			onJobCompleted();
			onClose();
		} catch (error) {
			console.error("Error completing job:", error);
			toast.error(
				error instanceof Error ? error.message : "Failed to complete job"
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		if (!isSubmitting) {
			setStep("confirm");
			setResolutionStatus(ResolutionStatus.COMPLETED);
			setNotes("");
			setSendInvoice(false);
			setRequestReview(false);
			setReviewDelay(24);
			setReviewDelayUnit("hours");
			onClose();
		}
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>
						{step === "confirm" ? "Complete Job" : "Additional Options"}
					</DialogTitle>
				</DialogHeader>
				<div className="space-y-4 py-4">
					{step === "confirm" ? (
						<div className="space-y-4">
							<div>
								<Label>Service Request</Label>
								<div className="mt-1 p-3 bg-gray-50 rounded-md">
									<p className="text-sm text-gray-700 whitespace-pre-wrap">
										{job.message}
									</p>
								</div>
							</div>

							<div>
								<Label htmlFor="resolution-status">Resolution Status *</Label>
								<p
									className="mt-1 text-sm text-gray-500"
									id="resolution-status-help"
								>
									How was this service request resolved?
								</p>
								<Select
									value={resolutionStatus}
									onValueChange={(value) =>
										setResolutionStatus(value as ResolutionStatus)
									}
								>
									<SelectTrigger className="mt-1" id="resolution-status">
										<SelectValue placeholder="Select resolution status" />
									</SelectTrigger>
									<SelectContent>
										{resolutionStatusOptions.map((option) => (
											<SelectItem key={option.value} value={option.value}>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>

							<div>
								<Label htmlFor="notes">Notes (Optional)</Label>
								<Textarea
									id="notes"
									value={notes}
									onChange={(e) => setNotes(e.target.value)}
									placeholder="Add any additional notes about the job completion..."
									className="min-h-[100px] mt-1"
									aria-describedby="notes-help"
								/>
								<p className="mt-1 text-sm text-gray-500" id="notes-help">
									Add any relevant details about the work performed or
									resolution
								</p>
							</div>

							<div className="flex justify-end gap-3">
								<Button
									variant="outline"
									onClick={handleClose}
									disabled={isSubmitting}
								>
									Cancel
								</Button>
								<Button
									variant="default"
									onClick={() => setStep("options")}
									className="bg-emerald-600 hover:bg-emerald-700"
									disabled={isSubmitting}
								>
									Continue
								</Button>
							</div>
						</div>
					) : (
						<div className="space-y-6">
							<div className="space-y-4">
								{/* <div className="p-4 bg-gray-50 rounded-lg">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											<Lock className="w-4 h-4 text-gray-400" />
											<div className="space-y-0.5">
												<label className="text-sm font-medium">
													Send Invoice
												</label>
												<p className="text-sm text-gray-500">Coming soon</p>
											</div>
										</div>
										<Switch disabled checked={false} />
									</div>
								</div> */}

								<div className="p-4 bg-gray-50 rounded-lg">
									<div className="flex items-center gap-2">
										<Switch
											checked={requestReview}
											onCheckedChange={setRequestReview}
											disabled={isSubmitting}
										/>
										<span className="font-medium">
											Request a review from the customer
										</span>
									</div>

									{requestReview && (
										<div className="mt-4 space-y-3">
											<p className="text-sm text-gray-600">
												When should we send the review request?
											</p>

											<div className="space-y-3">
												<label className="flex items-center gap-2 cursor-pointer">
													<input
														type="radio"
														name="reviewTiming"
														checked={reviewDelay === 0}
														onChange={() => {
															setReviewDelay(0);
															setReviewDelayUnit("hours");
														}}
														className="w-4 h-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
														disabled={isSubmitting}
													/>
													<span className="text-sm">Send immediately</span>
												</label>

												<label className="flex items-center gap-2 cursor-pointer">
													<input
														type="radio"
														name="reviewTiming"
														checked={reviewDelay > 0}
														onChange={() => {
															if (reviewDelay === 0) {
																setReviewDelay(24);
															}
														}}
														className="w-4 h-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
														disabled={isSubmitting}
													/>
													<span className="text-sm">Send after a delay</span>
												</label>

												{reviewDelay > 0 && (
													<div className="ml-6 flex items-center gap-3">
														<input
															type="number"
															min="1"
															value={reviewDelay}
															onChange={(e) =>
																setReviewDelay(parseInt(e.target.value) || 1)
															}
															className="w-20 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-emerald-500"
															disabled={isSubmitting}
														/>
														<select
															value={reviewDelayUnit}
															onChange={(e) =>
																setReviewDelayUnit(
																	e.target.value as "hours" | "days"
																)
															}
															className="px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-emerald-500"
															disabled={isSubmitting}
														>
															<option value="hours">hours</option>
															<option value="days">days</option>
														</select>
														<span className="text-sm text-gray-600">
															after marking as completed
														</span>
													</div>
												)}
											</div>
										</div>
									)}
								</div>
							</div>

							<div className="flex justify-end gap-3">
								<Button
									variant="outline"
									onClick={() => setStep("confirm")}
									disabled={isSubmitting}
								>
									Back
								</Button>
								<Button
									variant="default"
									onClick={handleCompleteJob}
									disabled={isSubmitting}
									className="bg-emerald-600 hover:bg-emerald-700"
								>
									{isSubmitting ? "Completing..." : "Complete Job"}
								</Button>
							</div>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
