import { CheckCir<PERSON>, Circle, Clock } from "lucide-react";


const getItemClassName = (isCompleted: boolean, isWarning: boolean, isFailed: boolean, isCurrent: boolean, isDisabled: boolean) => {
    if (isCurrent) {
        return "bg-blue-50 border-2 border-blue-300 hover:bg-blue-100 cursor-pointer shadow-sm hover:shadow-md"
    }
    else if (isWarning) {
        return "bg-amber-50 border-2 border-amber-300 shadow-sm"
    }
    else if (isFailed) {
        return "bg-red-50 border-2 border-red-300 shadow-sm"
    }
    else if (isDisabled) {
        return "bg-gray-50 border border-gray-200 opacity-60 cursor-not-allowed"
    }
    else if (isCompleted) {
        return "bg-gray-50 border border-gray-200 shadow-sm"
    }
    else {
        return "bg-gray-50 border border-gray-200 hover:bg-gray-100 cursor-pointer shadow-sm hover:shadow-md"
    }
}

const getItemIcon = (isCompleted: boolean, isWarning: boolean, isFailed: boolean, isCurrent: boolean, isDisabled: boolean) => {
    if (isCurrent) {
        return <Clock className="h-5 w-5 text-blue-600" />
    }
    else if (isWarning) {
        return <Circle className="h-5 w-5 text-amber-600" />
    }
    else if (isFailed) {
        return <Circle className="h-5 w-5 text-red-600" />
    }
    else if (isDisabled) {
        return <Circle className="h-5 w-5 text-gray-300" />
    }    
    else if (isCompleted) {
        return <CheckCircle className="h-5 w-5 text-green-600" />
    }
    else {
        return <Circle className="h-5 w-5 text-gray-400" />
    }
}

const getItemTextColor = (isCompleted: boolean, isWarning: boolean, isFailed: boolean, isCurrent: boolean, isDisabled: boolean) => {
    if (isCurrent) {
        return "text-blue-800"
    }
    else if (isWarning) {
        return "text-amber-800"
    }
    else if (isFailed) {
        return "text-red-800"
    }
    else if (isDisabled) {
        return "text-gray-400"
    }
    else if (isCompleted) {
        return "text-gray-800"
    }
    else {
        return "text-gray-900"
    }
}

const getItemBadge = (isCompleted: boolean, isWarning: boolean, isFailed: boolean, isCurrent: boolean, isDisabled: boolean) => {
    if (isCompleted) {
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Complete</span>
    }
    else if (isCurrent) {
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Current</span>
    }
    return null;
}

interface ProviderChecklistItemProps {
	item: any
    isCurrent: boolean
    isDisabled: boolean
}

export function ProviderChecklistItem({
    item,
    isCurrent,
    isDisabled,
}: ProviderChecklistItemProps) {

    return (
        <div
            key={item.id}
            className={`group flex items-start space-x-3 p-3 rounded-lg transition-all duration-200 ${
                getItemClassName(item.completed, item.warning, item.failed, isCurrent, isDisabled)
            }`}
            onClick={() => {
                if (!item.completed && !isDisabled && item.action) {
                    item.action();
                }
            }}
        >
            <div className="flex-shrink-0 mt-0.5">
                {getItemIcon(item.completed, item.warning, item.failed, isCurrent, isDisabled)}
            </div>
            <div className="flex-1 min-w-0">
                <div
                    className={`font-medium flex items-center ${
                        getItemTextColor(item.completed, item.warning, item.failed, isCurrent, isDisabled)
                    }`}
                >
                    <span>{item.title}</span>
                </div>
                {/* {!isCompleted && !isDisabled && ( */}
                <div
                    className={`text-sm ${
                        getItemTextColor(item.completed, item.warning, item.failed, isCurrent, isDisabled)
                    }`}
                >
                    {item.completed ? (
                        item.success
                    ) : item.failed || item.warning ? (
                        item.failure ? (
                            item.failure
                        ) : (
                            item.description
                        )
                    ) : (
                        item.description
                    )}
                </div>
                {/* )} */}
            </div>
            <div className="flex-shrink-0">
                {getItemBadge(item.completed, item.warning, item.failed, isCurrent, isDisabled)}
            </div>
        </div>
    );
}
