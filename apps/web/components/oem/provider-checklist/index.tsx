import { QuoteWithMessages } from "@/app/(main)/provider/(dashboard)/jobs/[id]/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { StripeConnection } from "@rvhelp/database";
import { Wrench } from "lucide-react";
import { useRouter } from "next/navigation";
import { ProviderChecklistItem } from "./checklist-item";
import { useChecklistConfig } from "./use-checklist-config";

interface ProviderChecklistProps {
	quote: QuoteWithMessages;
	warrantyRequest: ExtendedWarrantyRequest;
	stripeConnect: StripeConnection;
	onDownloadForms: () => void;
	onRequestAuthorization: () => void;
	onRequestPayment: () => void;
	onCompleteJob: () => void;
	onStartJob: () => void;
}

export function ProviderChecklist({
	quote,
	warrantyRequest,
	stripeConnect,
	onRequestAuthorization,
	onRequestPayment,
	onCompleteJob,
	onDownloadForms,
	onStartJob
}: ProviderChecklistProps) {
	const router = useRouter();

	const onConfigureStripe = () => {
		router.push("/provider/billing/dashboard");
	};

	const { checklistItems, currentItemIndex } = useChecklistConfig({
		quote,
		warrantyRequest,
		stripeConnect,
		onStartJob,
		onRequestAuthorization,
		onConfigureStripe,
		onDownloadForms,
		onRequestPayment,
		onCompleteJob
	});

	const itemCount = checklistItems.filter((item) => !item.hidden).length;
	const completedCount = checklistItems.filter((item) => item.completed).length;
	const progressPercentage = (completedCount / itemCount) * 100;

	// Determine if an item is disabled (not current and not completed)
	const isItemDisabled = (index: number) => {
		if (checklistItems[index].completed) return false;
		return index > currentItemIndex;
	};

	// Determine if an item is current (first incomplete item)
	const isItemCurrent = (index: number) => index === currentItemIndex;

	return (
		<>
			<Card>
				<CardHeader>
					<CardTitle className="text-lg font-medium flex items-center">
						<Wrench className="mr-2 h-5 w-5 text-gray-500" />
						Warranty Service Checklist
					</CardTitle>
					<div className="flex items-center justify-between text-sm text-gray-600">
						<span>
							{completedCount} of {itemCount} completed
						</span>
						<span className="font-medium">
							{Math.round(progressPercentage)}%
						</span>
					</div>
					<div className="w-full bg-gray-200 rounded-full h-2">
						<div
							className="bg-green-500 h-2 rounded-full transition-all duration-300 ease-in-out"
							style={{ width: `${progressPercentage}%` }}
						></div>
					</div>
				</CardHeader>
				<CardContent className="px-6 pb-6 -mt-2">
					<div className="space-y-3">
						{checklistItems.map((item, index) => {
							const isCurrent = isItemCurrent(index);
							const isDisabled = isItemDisabled(index);
							if (item.hidden) return null;

							return (
								<ProviderChecklistItem
									key={item.id}
									item={item}
									isCurrent={isCurrent}
									isDisabled={isDisabled}
								/>
							);
						})}
					</div>
				</CardContent>
			</Card>
		</>
	);
}
