"use client";

import type { QuoteWithMessages } from "@/app/(main)/provider/(dashboard)/jobs/[id]/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";
import type { ExtendedWarrantyRequest } from "@/types/warranty";
import { CheckCircle, FileText } from "lucide-react";
import type { UseFormReturn } from "react-hook-form";

interface InvoiceReviewStepProps {
	request: ExtendedWarrantyRequest;
	quote: QuoteWithMessages;
	form: UseFormReturn<any>;
	validationAttempted?: boolean;
}

export function InvoiceReviewStep({
	request,
	quote,
	form,
	validationAttempted = false
}: InvoiceReviewStepProps) {
	const savedInvoice = form.watch("_savedInvoice");

	if (!savedInvoice) {
		return (
			<div className="space-y-6">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<FileText className="h-5 w-5" />
							Invoice Review
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-center py-8">
							<p className="text-muted-foreground mb-4">
								No invoice has been saved yet. Please go back to the previous
								step and save your invoice.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<CheckCircle className="h-5 w-5 text-green-600" />
						Invoice Ready to Send
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="bg-green-50 border border-green-200 rounded-md p-4">
							<p className="text-green-800 font-medium">
								Invoice #{savedInvoice.invoice_number} has been saved and is
								ready to be sent to the warranty company.
							</p>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<h3 className="font-semibold mb-3">Customer Information</h3>
								<div className="space-y-2 text-sm">
									<div>
										<span className="font-medium">Name:</span>{" "}
										{savedInvoice.customer_name}
									</div>
									<div>
										<span className="font-medium">Email:</span>{" "}
										{savedInvoice.customer_email}
									</div>
									{savedInvoice.customer_phone && (
										<div>
											<span className="font-medium">Phone:</span>{" "}
											{savedInvoice.customer_phone}
										</div>
									)}
									{savedInvoice.due_date && (
										<div>
											<span className="font-medium">Due Date:</span>{" "}
											{new Date(savedInvoice.due_date).toLocaleDateString()}
										</div>
									)}
								</div>
							</div>

							<div>
								<h3 className="font-semibold mb-3">Invoice Summary</h3>
								<div className="space-y-2 text-sm">
									<div className="flex justify-between">
										<span>Invoice Number:</span>
										<span className="font-medium">
											#{savedInvoice.invoice_number}
										</span>
									</div>
									<div className="flex justify-between">
										<span>Status:</span>
										<span className="font-medium capitalize">
											{savedInvoice.status.toLowerCase()}
										</span>
									</div>
									<div className="flex justify-between">
										<span>Total Amount:</span>
										<span className="font-medium">
											{formatCurrency(savedInvoice.amount)}
										</span>
									</div>
								</div>
							</div>
						</div>

						<div>
							<h3 className="font-semibold mb-3">Invoice Items</h3>
							<div className="space-y-2">
								{savedInvoice.items?.map((item: any, index: number) => (
									<div
										key={index}
										className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0"
									>
										<div>
											<div className="font-medium">{item.description}</div>
											<div className="text-sm text-muted-foreground">
												{item.quantity} × {formatCurrency(item.unit_price)}
											</div>
										</div>
										<div className="font-medium">
											{formatCurrency(item.amount)}
										</div>
									</div>
								))}
							</div>
						</div>

						{savedInvoice.notes && (
							<div>
								<h3 className="font-semibold mb-3">Notes</h3>
								<div className="bg-gray-50 p-3 rounded-md text-sm whitespace-pre-wrap">
									{savedInvoice.notes}
								</div>
							</div>
						)}

						<div className="bg-blue-50 border border-blue-200 rounded-md p-4">
							<p className="text-blue-800 text-sm">
								<strong>Next Step:</strong> When you click "Submit" below, this
								invoice will be marked as sent and attached to the warranty
								request. The warranty company will be notified and can process
								payment.
							</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
