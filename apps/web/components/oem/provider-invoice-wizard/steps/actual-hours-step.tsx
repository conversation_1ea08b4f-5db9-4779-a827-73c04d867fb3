"use client";

import type { QuoteWithMessages } from "@/app/(main)/provider/(dashboard)/jobs/[id]/types";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import type { ExtendedWarrantyRequest } from "@/types/warranty";
import { useMemo } from "react";
import { type UseFormReturn, useWatch } from "react-hook-form";

interface ActualHoursStepProps {
	request: ExtendedWarrantyRequest;
	quote: QuoteWithMessages;
	form: UseFormReturn<any>;
	validationAttempted?: boolean;
}

export function ActualHoursStep({
	request,
	quote,
	form,
	validationAttempted = false
}: ActualHoursStepProps) {
	const { control } = form;

	const actualHours = useWatch({ control, name: "actual_hours" });
	const approvedHours = request.approved_hours;
	const estimatedHours = request.estimated_hours || request.approved_hours;
	const hourlyRate = (quote.listing.pricing_settings.warranty_rate ??
		quote.listing.pricing_settings.hourly_rate) as number;
	const dispatchFee = quote.listing.pricing_settings.dispatch_fee as number;

	// Memoize hours comparison to prevent unnecessary re-renders
	const hoursComparison = useMemo(() => {
		if (!actualHours) return null;

		const approved = approvedHours || 0;
		const estimated = estimatedHours || 0;

		if (actualHours > approved && approved > 0) {
			return {
				type: "warning" as const,
				message: `Actual hours (${actualHours}) exceed approved hours (${approved})`
			};
		} else if (actualHours > estimated && estimated > 0) {
			return {
				type: "info" as const,
				message: `Actual hours (${actualHours}) exceed estimated hours (${estimated})`
			};
		} else if (actualHours <= approved && approved > 0) {
			return {
				type: "success" as const,
				message: `Actual hours (${actualHours}) are within approved hours (${approved})`
			};
		}

		return null;
	}, [actualHours, approvedHours, estimatedHours]);

	// Memoize total amount calculation
	const totalAmount = useMemo(() => {
		if (!actualHours || actualHours <= 0) return 0;
		const totalHours = actualHours;
		return dispatchFee + hourlyRate * totalHours;
	}, [actualHours, dispatchFee, hourlyRate]);

	return (
		<Form {...form}>
			<div className="space-y-6">
				{/* Hours Information */}
				<div>
					<div className="font-semibold mb-4">Service Hours</div>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<Input
							type="number"
							label="Approved Hours"
							step="0.1"
							placeholder="e.g. 2.5"
							disabled={true}
							className="bg-gray-50"
							value={request.approved_hours}
						/>
						<Input
							type="number"
							label="Estimated Hours"
							step="0.1"
							placeholder="e.g. 2.5"
							disabled={true}
							className="bg-gray-50"
							value={request.estimated_hours || estimatedHours}
						/>
						<FormField
							control={control}
							name="actual_hours"
							render={({ field }) => (
								<FormItem>
									<FormControl>
										<Input
											type="number"
											label="Actual Hours"
											required
											step="0.1"
											placeholder="e.g. 2.5"
											name={field.name}
											value={field.value ?? ""}
											onBlur={field.onBlur}
											onChange={(e) => {
												const value =
													e.target.value === ""
														? undefined
														: parseFloat(e.target.value);
												field.onChange(value);
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					{/* Hours Comparison Alert */}
					{hoursComparison && (
						<div
							className={`mt-4 p-3 rounded-md ${
								hoursComparison.type === "warning"
									? "bg-yellow-50 border border-yellow-200"
									: hoursComparison.type === "info"
										? "bg-blue-50 border border-blue-200"
										: "bg-green-50 border border-green-200"
							}`}
						>
							<p
								className={`text-sm ${
									hoursComparison.type === "warning"
										? "text-yellow-800"
										: hoursComparison.type === "info"
											? "text-blue-800"
											: "text-green-800"
								}`}
							>
								{hoursComparison.message}
							</p>
						</div>
					)}
				</div>
			</div>
		</Form>
	);
}
