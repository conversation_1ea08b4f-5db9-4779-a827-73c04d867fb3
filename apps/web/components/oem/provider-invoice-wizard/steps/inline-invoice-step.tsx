"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { use<PERSON>allback, useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

import type { QuoteWithMessages } from "@/app/(main)/provider/(dashboard)/jobs/[id]/types";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { formatCurrency } from "@/lib/utils";
import type { ExtendedWarrantyRequest } from "@/types/warranty";
import { X } from "lucide-react";
import type { UseFormReturn } from "react-hook-form";

const invoiceItemSchema = z.object({
	description: z.string().min(1, "Description is required"),
	quantity: z.number().min(1, "Quantity must be at least 1").default(1),
	unit_price: z.number().min(0, "Unit price must be positive")
});

const invoiceFormSchema = z.object({
	customer_name: z.string().min(1, "Customer name is required"),
	customer_email: z.string().email("Invalid email address"),
	customer_phone: z.string().optional(),
	due_date: z.string().optional(),
	currency: z.string().default("usd"),
	notes: z.string().optional(),
	items: z.array(invoiceItemSchema).min(1, "At least one item is required")
});

type InvoiceFormValues = z.infer<typeof invoiceFormSchema>;

interface InlineInvoiceStepProps {
	request: ExtendedWarrantyRequest;
	quote: QuoteWithMessages;
	actualHours: number;
	form: UseFormReturn<any>;
	validationAttempted?: boolean;
	onInvoiceDataChange?: (invoiceData: InvoiceFormValues) => void;
	autoSave?: boolean;
	onSaveReady?: (saveFunction: () => Promise<boolean>) => void;
}

export function InlineInvoiceStep({
	request,
	quote,
	actualHours,
	form,
	validationAttempted = false,
	onInvoiceDataChange,
	autoSave = false,
	onSaveReady
}: InlineInvoiceStepProps) {
	const [isSaving, setIsSaving] = useState(false);
	const [hasLoadedSavedData, setHasLoadedSavedData] = useState(false);

	// Calculate default values
	const hourlyRate =
		quote.listing.pricing_settings.warranty_rate ||
		quote.listing.pricing_settings.hourly_rate;
	const dispatchFee = quote.listing.pricing_settings.dispatch_fee;

	const defaultInvoiceData = useMemo(
		(): InvoiceFormValues => ({
			customer_name: `${request.first_name} ${request.last_name}`,
			customer_email: request.email,
			customer_phone: request.phone || "",
			due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
				.toISOString()
				.split("T")[0], // 30 days from now
			currency: "usd",
			notes: `${request.company?.name || "Provider"} Warranty Service Request: ${request.id}\nRV: ${request.rv_year} ${request.rv_make} ${request.rv_model}\nCause: ${request.cause || "N/A"}\nCorrection: ${request.correction || "N/A"}`,
			items: [
				{
					description: "Dispatch Fee",
					quantity: 1,
					unit_price: dispatchFee
				},
				{
					description: "Labor",
					quantity: actualHours,
					unit_price: hourlyRate
				}
			]
		}),
		[request, dispatchFee, actualHours, hourlyRate]
	);

	const invoiceForm = useForm<InvoiceFormValues>({
		resolver: zodResolver(invoiceFormSchema),
		defaultValues: defaultInvoiceData
	});

	const { fields, append, remove } = useFieldArray({
		control: invoiceForm.control,
		name: "items"
	});

	// Simple state for total calculation
	const [calculatedTotal, setCalculatedTotal] = useState(0);

	// Function to calculate total from current form values
	const calculateTotalFromForm = useCallback(() => {
		const currentItems = invoiceForm.getValues("items") || [];

		const total = currentItems.reduce((sum, item) => {
			if (!item) return sum;
			const quantity = item.quantity || 1;
			const unitPrice = item.unit_price || 0;
			const itemTotal = quantity * unitPrice;

			return sum + itemTotal;
		}, 0);

		setCalculatedTotal(total);
		return total;
	}, [invoiceForm]);

	// Reset form when actualHours changes (but only if no saved invoice exists and we haven't loaded saved data)
	useEffect(() => {
		const savedInvoice = form.getValues("_savedInvoice");

		// Only reset with default data if:
		// 1. We haven't loaded saved data yet
		// 2. There's no saved invoice OR it has no items
		// 3. The current form is empty (to avoid overriding user changes)
		const currentItems = invoiceForm.getValues("items") || [];
		const shouldResetWithDefaults =
			!hasLoadedSavedData &&
			(!savedInvoice ||
				!savedInvoice.items ||
				savedInvoice.items.length === 0) &&
			(currentItems.length === 0 ||
				(currentItems.length === 2 &&
					currentItems[0]?.description === "Dispatch Fee" &&
					currentItems[1]?.description === "Labor"));

		if (shouldResetWithDefaults) {
			invoiceForm.reset(defaultInvoiceData);
			setTimeout(calculateTotalFromForm, 0);
		}
	}, [
		defaultInvoiceData,
		invoiceForm,
		form,
		hasLoadedSavedData,
		calculateTotalFromForm
	]);

	// Watch for form changes and notify parent + auto-save
	useEffect(() => {
		if (onInvoiceDataChange) {
			const subscription = invoiceForm.watch((value) => {
				onInvoiceDataChange(value as InvoiceFormValues);
			});
			return () => subscription.unsubscribe();
		}
	}, [invoiceForm, onInvoiceDataChange]);

	const calculateItemTotal = (item: {
		unit_price?: number;
		quantity?: number;
	}) => {
		const quantity = item.quantity || 1;
		const unitPrice = item.unit_price || 0;
		return quantity * unitPrice; // Return in dollars for display
	};

	// Calculate total on mount and when fields change
	useEffect(() => {
		calculateTotalFromForm();
	}, [calculateTotalFromForm, fields.length]);

	const handleSaveInvoice = useCallback(async (): Promise<boolean> => {
		try {
			setIsSaving(true);

			// Validate the form
			const isValid = await invoiceForm.trigger();
			if (!isValid) {
				toast.error("Please fix the form errors before continuing");
				return false;
			}

			const data = invoiceForm.getValues();
			let existingInvoice = form.getValues("_savedInvoice");

			// If no existing invoice in form, check if one exists for this warranty request
			if (!existingInvoice) {
				try {
					// Use the specific endpoint to check for existing invoice by warranty request ID
					const checkResponse = await fetch(
						`/api/warranty-requests/${request.id}/invoice`
					);
					if (checkResponse.ok) {
						const checkData = await checkResponse.json();

						if (checkData.invoice) {
							existingInvoice = checkData.invoice;
							// Store it in the form for future use
							form.setValue("_savedInvoice", existingInvoice);
							// Immediately pre-load the invoice data into the form
							preloadSavedInvoiceData(existingInvoice);
						}
					}
				} catch (e) {
					console.error("Error checking for existing invoice:", e);
				}
			}

			// Convert all dollar amounts to cents
			const itemsWithCents = data.items.map((item) => ({
				description: item.description || "",
				unit_price: Math.round((item.unit_price || 0) * 100),
				quantity: item.quantity || 1,
				amount: Math.round((item.unit_price || 0) * (item.quantity || 1) * 100)
			}));

			// Calculate total in cents
			const totalAmount = itemsWithCents.reduce(
				(total, item) => total + item.amount,
				0
			);

			const invoiceData = {
				...data,
				amount: totalAmount,
				items: itemsWithCents,
				status: "DRAFT", // Save as draft initially
				notes: data.notes,
				warranty_request_id: request.id
			};

			let response;
			if (existingInvoice?.id) {
				// Update existing invoice
				response = await fetch(`/api/invoices/${existingInvoice.id}`, {
					method: "PATCH",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify(invoiceData)
				});
			} else {
				// Create new invoice using the warranty-specific endpoint
				response = await fetch(`/api/warranty-requests/${request.id}/invoice`, {
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify(invoiceData)
				});
			}

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to save invoice");
			}

			const responseData = await response.json();

			// Only show success message for initial save, not for updates
			if (!existingInvoice?.id) {
				toast.success("Invoice saved successfully");
			}

			// Store the invoice data in the main form for the review step
			// Also update the amount to match what we calculated locally for consistency
			const updatedInvoice = {
				...responseData.invoice,
				amount: totalAmount // Ensure the stored amount matches our calculation
			};
			form.setValue("_savedInvoice", updatedInvoice);
			return true;
		} catch (error) {
			// Show more specific error if available
			const errorMessage =
				error instanceof Error
					? error.message
					: "Failed to save invoice. Please try again.";
			toast.error(errorMessage);
			return false;
		} finally {
			setIsSaving(false);
		}
	}, [invoiceForm, request.id, form]);

	// Function to pre-load saved invoice data into the form
	const preloadSavedInvoiceData = useCallback(
		(invoice: any) => {
			if (invoice && invoice.items && invoice.items.length > 0) {
				// Convert the saved invoice data back to form format (amounts in dollars)
				const formData = {
					customer_name: invoice.customer_name,
					customer_email: invoice.customer_email,
					customer_phone: invoice.customer_phone || "",
					due_date: invoice.due_date
						? new Date(invoice.due_date).toISOString().split("T")[0]
						: "",
					currency: invoice.currency || "usd",
					notes: invoice.notes || "",
					items: invoice.items.map((item: any) => ({
						description: item.description,
						quantity: item.quantity,
						unit_price: item.unit_price / 100 // Convert cents back to dollars
					}))
				};

				invoiceForm.reset(formData);
				setHasLoadedSavedData(true);
				// Recalculate total after loading saved data
				setTimeout(calculateTotalFromForm, 0);
			} else {
				setHasLoadedSavedData(false);
			}
		},
		[invoiceForm, calculateTotalFromForm]
	);

	// Watch for saved invoice changes to pre-load data
	const savedInvoice = form.watch("_savedInvoice");

	// Check for existing invoice on component mount
	useEffect(() => {
		const checkForExistingInvoice = async () => {
			const currentSavedInvoice = form.getValues("_savedInvoice");

			// If no saved invoice in form, check if one exists for this warranty request
			if (!currentSavedInvoice) {
				try {
					const response = await fetch(
						`/api/warranty-requests/${request.id}/invoice`
					);
					if (response.ok) {
						const data = await response.json();

						if (data.invoice) {
							form.setValue("_savedInvoice", data.invoice);
							// Immediately pre-load the invoice data into the form
							preloadSavedInvoiceData(data.invoice);
						}
					}
				} catch (error) {
					console.error("Error checking for existing invoice on mount:", error);
				}
			}
		};

		checkForExistingInvoice();
	}, [request.id, form, preloadSavedInvoiceData]); // Only run on mount

	// Pre-load existing invoice data if available (backup effect for watch changes)
	useEffect(() => {
		preloadSavedInvoiceData(savedInvoice);
	}, [savedInvoice, preloadSavedInvoiceData]);

	// Expose save function to parent component
	useEffect(() => {
		if (onSaveReady) {
			onSaveReady(handleSaveInvoice);
		}
	}, [onSaveReady, handleSaveInvoice]);

	return (
		<Form {...invoiceForm}>
			<div className="space-y-6" key={`invoice-form-${fields.length}`}>
				<Card>
					<CardHeader>
						<CardTitle>Customer Information</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<FormField
								control={invoiceForm.control}
								name="customer_name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Customer Name</FormLabel>
										<FormControl>
											<Input placeholder="John Doe" {...field} disabled />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={invoiceForm.control}
								name="customer_email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Customer Email</FormLabel>
										<FormControl>
											<Input
												type="email"
												placeholder="<EMAIL>"
												{...field}
												disabled
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={invoiceForm.control}
								name="customer_phone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Customer Phone (Optional)</FormLabel>
										<FormControl>
											<Input placeholder="(*************" {...field} disabled />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={invoiceForm.control}
								name="due_date"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Due Date (Optional)</FormLabel>
										<FormControl>
											<Input type="date" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Invoice Items</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						{fields.map((field, index) => (
							<div
								key={field.id}
								className="grid grid-cols-1 md:grid-cols-12 gap-4 items-end"
							>
								<div className="md:col-span-5">
									<FormField
										control={invoiceForm.control}
										name={`items.${index}.description`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Description</FormLabel>
												<FormControl>
													<Input placeholder="Item description" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
								<div className="md:col-span-2">
									<FormField
										control={invoiceForm.control}
										name={`items.${index}.quantity`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Quantity</FormLabel>
												<FormControl>
													<Input
														type="number"
														min="1"
														{...field}
														onChange={(e) => {
															const finalValue =
																Number.parseInt(e.target.value) || 1;
															field.onChange(finalValue);
															// Trigger total recalculation
															setTimeout(calculateTotalFromForm, 0);
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
								<div className="md:col-span-2">
									<FormField
										control={invoiceForm.control}
										name={`items.${index}.unit_price`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Unit Price</FormLabel>
												<FormControl>
													<Input
														type="text"
														inputMode="decimal"
														placeholder="0.00"
														defaultValue={
															field.value === 0 ? "" : field.value.toFixed(2)
														}
														onChange={(e) => {
															const value = e.target.value;
															const sanitizedValue = value.replace(
																/[^\d.]/g,
																""
															);
															const parts = sanitizedValue.split(".");
															let numericValue: number;

															if (parts.length > 1) {
																const formattedValue = `${parts[0]}.${parts[1].slice(0, 2)}`;
																numericValue =
																	Number.parseFloat(formattedValue);
															} else {
																numericValue =
																	Number.parseFloat(sanitizedValue);
															}

															const finalValue = isNaN(numericValue)
																? 0
																: numericValue;
															field.onChange(finalValue);
															// Trigger total recalculation
															setTimeout(calculateTotalFromForm, 0);
														}}
														onBlur={(e) => {
															const value = Number.parseFloat(e.target.value);
															if (!isNaN(value)) {
																e.target.value = value.toFixed(2);
																field.onChange(value);
																// Trigger total recalculation
																setTimeout(calculateTotalFromForm, 0);
															}
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
								<div className="md:col-span-2">
									<FormLabel>Amount</FormLabel>
									<div className="h-10 px-3 py-2 border border-input bg-muted rounded-md text-sm">
										{(() => {
											const currentItems = invoiceForm.getValues("items") || [];
											const item = currentItems[index];
											const itemTotal = calculateItemTotal(item || {});
											return formatCurrency(itemTotal * 100);
										})()}
									</div>
								</div>
								<div className="md:col-span-1">
									<Button
										type="button"
										variant="outline"
										onClick={() => remove(index)}
										disabled={fields.length === 1}
									>
										<X className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
						<Button
							type="button"
							variant="outline"
							onClick={() => {
								append({ description: "", unit_price: 0, quantity: 1 });
								// Trigger total recalculation after adding item
								setTimeout(calculateTotalFromForm, 0);
							}}
						>
							Add Item
						</Button>
					</CardContent>
					<CardFooter>
						<div className="text-lg font-semibold">
							Total: {formatCurrency(calculatedTotal * 100)}
						</div>
					</CardFooter>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Additional Information</CardTitle>
					</CardHeader>
					<CardContent>
						<FormField
							control={invoiceForm.control}
							name="notes"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Notes (Optional)</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Add any additional notes or terms to the invoice"
											className="min-h-[100px]"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</CardContent>
				</Card>

				<div className="bg-blue-50 border border-blue-200 rounded-md p-4">
					<p className="text-blue-800 text-sm">
						<strong>Note:</strong> Your invoice will be automatically saved when
						you click "Next" to proceed to the review step.
					</p>
				</div>
			</div>
		</Form>
	);
}
