import { z } from "zod";

export const ProviderInvoiceSchema = z.object({
	cause: z
		.string()
		.min(1, "Cause is required")
		.max(500, "Cause cannot exceed 500 characters"),
	correction: z
		.string()
		.min(1, "Suggested correction is required")
		.max(500, "Suggested correction cannot exceed 500 characters"),
	component_id: z.string().min(1, "Please select a component"),
	actual_hours: z
		.number({
			required_error: "Actual hours is required",
			invalid_type_error: "Please enter a valid number"
		})
		.min(0.1, "Actual hours must be at least 0.1")
		.max(1000, "Actual hours cannot exceed 1000"),

	update_notes: z
		.string()
		.max(1000, "Update notes cannot exceed 1000 characters")
		.optional()
		.nullable(),
	attachments: z
		.array(
			z.object({
				id: z.string().optional().nullable(),
				component_id: z.string().optional().nullable(),
				title: z.string(),
				type: z.string(),
				url: z.string(),
				required: z.boolean().optional(),
				completed: z.boolean().optional(),
				status_update: z.boolean().optional()
			})
		)
		.optional()
		.nullable(),
	skip_attachments: z.boolean().optional().default(false),
	event_type: z
		.enum([
			"PREAUTHORIZATION_APPROVED",
			"PREAUTHORIZATION_REJECTED",
			"CUSTOMER_REGISTERED",
			"TECHNICIAN_INVITED",
			"TECHNICIAN_ACCEPTED",
			"TECHNICIAN_REJECTED",
			"CUSTOMER_ACCEPTED",
			"CUSTOMER_REJECTED",
			"AUTHORIZATION_REQUESTED",
			"AUTHORIZATION_APPROVED",
			"AUTHORIZATION_REJECTED",
			"AUTHORIZATION_FEEDBACK",
			"PARTS_ORDERED",
			"JOB_STARTED",
			"JOB_COMPLETED",
			"JOB_CANCELLED",
			"JOB_PAUSED",
			"INVOICE_CREATED",
			"INVOICE_PAID"
		])
		.optional()
});
