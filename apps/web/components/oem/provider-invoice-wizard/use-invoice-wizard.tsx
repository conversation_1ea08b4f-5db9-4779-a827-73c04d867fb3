"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import type { z } from "zod";

import type { QuoteWithMessages } from "@/app/(main)/provider/(dashboard)/jobs/[id]/types";
import type {
	ExtendedCompany,
	ExtendedWarrantyRequest
} from "@/types/warranty";

import config from "../../../config";
import { ProviderInvoiceSchema } from "./provider-invoice-schema";
import { ActualHoursStep } from "./steps/actual-hours-step";
import { InlineInvoiceStep } from "./steps/inline-invoice-step";
import { InvoiceReviewStep } from "./steps/invoice-review-step";

type ProviderInvoiceFormData = z.infer<typeof ProviderInvoiceSchema>;

const STEPS = [
	{
		id: 0,
		title: "Actual Hours",
		description: "Enter the actual hours spent on this warranty service.",
		fieldsToValidate: ["actual_hours"]
	},
	{
		id: 1,
		title: "Customize Invoice",
		description:
			"Customize your invoice before sending to the warranty company.",
		fieldsToValidate: []
	},
	{
		id: 2,
		title: "Review & Send",
		description: "Review your invoice and complete the warranty request.",
		fieldsToValidate: []
	}
] as const;

export interface UseProviderInvoiceWizardArgs {
	company: ExtendedCompany;
	request?: ExtendedWarrantyRequest;
	quote?: QuoteWithMessages;
	onCancel?: () => void;
	onSuccess?: (updatedRequest?: any) => void;
}

export function useProviderInvoiceWizard({
	company,
	request,
	quote,
	onCancel,
	onSuccess
}: UseProviderInvoiceWizardArgs) {
	const router = useRouter();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [currentStepId, setCurrentStepId] = useState(0);
	const [validationAttempted, setValidationAttempted] = useState(false);
	const [invoiceSaveFunction, setInvoiceSaveFunction] = useState<
		(() => Promise<boolean>) | null
	>(null);
	const [isSavingInvoice, setIsSavingInvoice] = useState(false);

	const getDefaultValues = useCallback(() => {
		const baseValues = {
			update_notes: "",
			attachments: [],
			skip_attachments: false,
			event_type: undefined
		} as ProviderInvoiceFormData;

		return {
			...baseValues,
			component_id: request?.component?.id || "",
			cause: request?.cause || "",
			correction: request?.correction || "",
			actual_hours: (request as any)?.actual_hours ?? undefined,
			attachments: (request as any)?.attachments || []
		} as ProviderInvoiceFormData;
	}, [request]);

	const form = useForm<ProviderInvoiceFormData>({
		resolver: zodResolver(ProviderInvoiceSchema),
		mode: "onChange",
		defaultValues: getDefaultValues()
	});

	const stepArray = useMemo(() => {
		return [STEPS[0], STEPS[1], STEPS[2]];
	}, []);

	useEffect(() => {
		if (stepArray.length > 0) {
			setCurrentStepId(stepArray[0].id);
		}
	}, [stepArray]);

	const validateCurrentStep = useCallback(async () => {
		const currentStep = stepArray.find((step) => step.id === currentStepId);
		const fieldsToValidate = currentStep?.fieldsToValidate ?? [];

		fieldsToValidate.forEach((field) => {
			form.setValue(field as any, form.getValues(field as any), {
				shouldTouch: true
			});
		});

		return await form.trigger(fieldsToValidate as unknown as any[]);
	}, [currentStepId, form, stepArray]);

	const nextStep = useCallback(() => {
		const index = stepArray.findIndex((step) => step.id === currentStepId);
		if (index < stepArray.length - 1) {
			setCurrentStepId(stepArray[index + 1].id);
		}
	}, [currentStepId, stepArray]);

	const previousStep = useCallback(() => {
		const index = stepArray.findIndex((step) => step.id === currentStepId);
		if (index > 0) {
			setCurrentStepId(stepArray[index - 1].id);
		}
	}, [currentStepId, stepArray]);

	const handleNext = useCallback(async () => {
		const isValid = await validateCurrentStep();
		setValidationAttempted(true);

		if (!isValid) {
			await form.trigger();
		}

		if (isValid) {
			if (currentStepId === 1 && invoiceSaveFunction) {
				setIsSavingInvoice(true);
				try {
					const saveSuccess = await invoiceSaveFunction();
					if (!saveSuccess) {
						return;
					}
				} finally {
					setIsSavingInvoice(false);
				}
			}

			nextStep();
			setValidationAttempted(false);
		}
	}, [validateCurrentStep, nextStep, form, currentStepId, invoiceSaveFunction]);

	const handlePrevious = useCallback(() => {
		previousStep();
	}, [previousStep]);

	const handleCancel = useCallback(() => {
		if (onCancel) onCancel();
		else router.back();
	}, [onCancel, router]);

	const onSubmit = useCallback(
		async (data: ProviderInvoiceFormData) => {
			try {
				setIsSubmitting(true);

				if ("component_id" in data && (data as any)?.component_id === "") {
					(data as any).component_id = null;
				}

				const savedInvoice = (form.getValues() as any)._savedInvoice;
				if (!savedInvoice) {
					toast.error("Please save your invoice before completing the job");
					return;
				}

				const updateInvoiceResponse = await fetch(
					`/api/invoices/${savedInvoice.id}`,
					{
						method: "PATCH",
						headers: { "Content-Type": "application/json" },
						body: JSON.stringify({ status: "SENT" })
					}
				);

				if (!updateInvoiceResponse.ok) {
					toast.error("Failed to send invoice");
					return;
				}

				const invoiceAttachment = {
					id: `invoice-${savedInvoice.id}`,
					type: "document" as const,
					title: `Provider Invoice #${savedInvoice.invoice_number}`,
					url: config.appUrl + `/api/invoices/${savedInvoice.id}/pdf`,
					required: false,
					completed: true
				};

				const attachments = (data as any).attachments || [];
				const updatedSubmitData = {
					...data,
					provider_invoice_id: savedInvoice.id,
					attachments: [...attachments, invoiceAttachment]
				} as ProviderInvoiceFormData;

				const response = await fetch(
					`/api/warranty-requests/${request!.id}/complete-job`,
					{
						method: "POST",
						headers: { "Content-Type": "application/json" },
						body: JSON.stringify(updatedSubmitData)
					}
				);

				if (response.ok) {
					const updatedRequest = await response.json();
					form.reset();
					toast.success("Job completed and invoice sent successfully");
					if (onSuccess) {
						onSuccess({ ...updatedRequest, _invoiceData: savedInvoice });
					} else {
						router.push("/dashboard");
					}
					return;
				}

				if (!response.ok) throw new Error("Failed to update warranty request");

				const updatedRequest = await response.json();
				form.reset();
				toast.success("Warranty request updated successfully");
				if (onSuccess) onSuccess(updatedRequest);
				else router.push("/dashboard");
			} catch (error) {
				console.error(error);
				toast.error("Failed to update warranty request. Please try again.");
			} finally {
				setIsSubmitting(false);
			}
		},
		[request, onSuccess, router, form]
	);

	const handleInvoiceDataChange = useCallback((_invoiceData: any) => {
		// Reserved for potential real-time updates
	}, []);

	const handleInvoiceSaveReady = useCallback(
		(saveFunction: () => Promise<boolean>) => {
			setInvoiceSaveFunction(() => saveFunction);
		},
		[]
	);

	const handleSubmit = useCallback(async () => {
		setValidationAttempted(true);

		const currentStep = stepArray.find((step) => step.id === currentStepId);
		const fieldsToValidate = currentStep?.fieldsToValidate || [];

		const stepIsValid = await form.trigger(
			fieldsToValidate as unknown as any[]
		);

		const formIsValid = await form.trigger();

		if (!stepIsValid || !formIsValid) {
			toast.error("Please fix the validation errors before submitting");
			const firstErrorField = document.querySelector('[data-error="true"]');
			if (firstErrorField) {
				firstErrorField.scrollIntoView({ behavior: "smooth", block: "center" });
			}
			return;
		}

		if (currentStep) {
			const stepValidation = await validateCurrentStep();
			if (!stepValidation) {
				toast.error("Please complete all required fields for this step");
				return;
			}
		}

		form.handleSubmit(onSubmit)();
	}, [form, onSubmit, stepArray, currentStepId, validateCurrentStep]);

	const currentStep = useMemo(() => {
		return stepArray.find((step) => step.id === currentStepId);
	}, [stepArray, currentStepId]);

	const isFirstStep = currentStepId === stepArray[0]?.id;
	const isLastStep = currentStepId === stepArray[stepArray.length - 1]?.id;

	const currentStepComponent = useMemo(() => {
		switch (currentStepId) {
			case 0:
				return (
					<ActualHoursStep
						request={request!}
						quote={quote!}
						form={form as any}
						validationAttempted={validationAttempted}
					/>
				);
			case 1:
				return (
					<InlineInvoiceStep
						request={request!}
						quote={quote!}
						actualHours={(form.getValues() as any).actual_hours || 0}
						form={form as any}
						validationAttempted={validationAttempted}
						onInvoiceDataChange={handleInvoiceDataChange}
						onSaveReady={handleInvoiceSaveReady}
						autoSave={true}
					/>
				);
			case 2:
				return (
					<InvoiceReviewStep
						request={request!}
						quote={quote!}
						form={form as any}
						validationAttempted={validationAttempted}
					/>
				);
			default:
				return null;
		}
	}, [
		request,
		quote,
		form,
		currentStepId,
		validationAttempted,
		handleInvoiceDataChange,
		handleInvoiceSaveReady
	]);

	return {
		form,
		isSubmitting,
		isSavingInvoice,
		currentStep,
		currentStepComponent,
		isFirstStep,
		isLastStep,
		handleNext,
		handlePrevious,
		handleCancel,
		handleSubmit
	} as const;
}
