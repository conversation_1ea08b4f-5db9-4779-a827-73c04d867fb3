"use client";

import type { QuoteWithMessages } from "@/app/(main)/provider/(dashboard)/jobs/[id]/types";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import type {
	ExtendedCompany,
	ExtendedWarrantyRequest
} from "@/types/warranty";
import { useSession } from "next-auth/react";
import { DialogDescription, DialogHeader, DialogTitle } from "../../ui/dialog";

import { useProviderInvoiceWizard } from "./use-invoice-wizard";

const ProviderInvoiceWizardSkeleton = () => (
	<Card>
		<CardHeader>
			<Skeleton className="h-8 w-48" />
			<Skeleton className="h-4 w-64 mt-2" />
		</CardHeader>
		<CardContent>
			<div className="space-y-6">
				<Skeleton className="h-6 w-32" />
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Skeleton className="h-10 w-full" />
					<Skeleton className="h-10 w-full" />
				</div>
				<Skeleton className="h-6 w-32" />
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Skeleton className="h-10 w-full" />
					<Skeleton className="h-10 w-full" />
				</div>
			</div>
		</CardContent>
	</Card>
);

interface ProviderInvoiceWizardProps {
	company: ExtendedCompany;
	request?: ExtendedWarrantyRequest;
	quote?: QuoteWithMessages;
	onCancel?: () => void;
	onSuccess?: (updatedRequest?: any) => void;
}

export function ProviderInvoiceWizard({
	company,
	request,
	quote,
	onCancel,
	onSuccess
}: ProviderInvoiceWizardProps) {
	const { data: session } = useSession();
	const {
		form,
		isSubmitting,
		isSavingInvoice,
		currentStep,
		currentStepComponent,
		isFirstStep,
		isLastStep,
		handleNext,
		handlePrevious,
		handleCancel,
		handleSubmit
	} = useProviderInvoiceWizard({
		company,
		request,
		quote,
		onCancel,
		onSuccess
	});

	if (!session?.user || !company) {
		return <ProviderInvoiceWizardSkeleton />;
	}

	return (
		<>
			{/* Loading overlay that covers the entire dialog */}
			{(isSubmitting || isSavingInvoice) && (
				<div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center">
					<div className="bg-white rounded-lg p-6 shadow-xl">
						<div className="flex items-center space-x-3">
							<div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
							<p className="text-gray-700 font-medium">
								{isSavingInvoice ? "Saving invoice..." : "Submitting..."}
							</p>
						</div>
					</div>
				</div>
			)}

			<form
				className="space-y-4"
				onSubmit={(e) => {
					e.preventDefault();
					handleSubmit();
				}}
			>
				<DialogHeader>
					<DialogTitle>{currentStep?.title}</DialogTitle>
					<DialogDescription>{currentStep?.description}</DialogDescription>
				</DialogHeader>

				{currentStepComponent}

				{/* Navigation */}
				<div className="flex justify-between mt-6 pt-4 border-t">
					<div className="flex gap-2">
						<Button
							type="button"
							variant="outline"
							onClick={handleCancel}
							disabled={isSubmitting || isSavingInvoice}
						>
							Cancel
						</Button>
						{!isFirstStep && (
							<Button
								type="button"
								variant="outline"
								onClick={handlePrevious}
								disabled={isSubmitting || isSavingInvoice}
							>
								Previous
							</Button>
						)}
					</div>

					<div>
						{!isLastStep ? (
							<Button
								type="button"
								onClick={handleNext}
								disabled={isSubmitting || isSavingInvoice}
							>
								{isSavingInvoice ? (
									<div className="flex items-center space-x-2">
										<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
										<span>Saving...</span>
									</div>
								) : currentStep?.id === 1 ? (
									"Save & Next"
								) : (
									"Next"
								)}
							</Button>
						) : (
							<Button
								type="button"
								onClick={handleSubmit}
								disabled={isSubmitting || isSavingInvoice}
							>
								{isSubmitting ? "Submitting..." : "Submit"}
							</Button>
						)}
					</div>
				</div>
			</form>
		</>
	);
}
