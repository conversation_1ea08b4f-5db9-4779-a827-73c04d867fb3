"use client";

import { CenteredPageLoader } from "@/components/Loader";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FormField, WarrantyAttachment } from "@/types/warranty";
import { Document, WarrantyRequest } from "@rvhelp/database";
import { FormEvent, useCallback, useEffect, useState } from "react";

interface FormFillComponentProps {
	warrantyRequest: WarrantyRequest;
	attachment: WarrantyAttachment;
	formRef?: React.RefObject<HTMLFormElement>;
	setSaving?: (saving: boolean) => void;
}

export default function FormFillComponent({
	warrantyRequest,
	attachment,
	formRef,
	setSaving
}: FormFillComponentProps) {
	const [formValues, setFormValues] = useState<
		Record<string, string | boolean>
	>({});
	const [jsonForm, setJsonForm] = useState<FormField[]>([]);
	const [document, setDocument] = useState<Document | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Reset all state when attachment.id changes
	useEffect(() => {
		setFormValues({});
		setError(null);
		setJsonForm([]);
		setDocument(null);
		setLoading(true);
	}, [attachment.id]);

	// Fetch document when attachment.id changes
	useEffect(() => {
		const fetchDocument = async () => {
			setLoading(true);
			const response = await fetch(`/api/documents/${attachment.id}`);
			const data = await response.json();
			const parsedFields = JSON.parse(data.form_fields);
			setDocument(data);
			setJsonForm(parsedFields);
			setLoading(false);
		};
		fetchDocument();
	}, [attachment.id]);

	// Pre-populate formValues if jsonForm has values
	useEffect(() => {
		if (jsonForm && jsonForm.length > 0) {
			const initialValues: Record<string, string | boolean> = {};
			jsonForm.forEach((field) => {
				if (
					field.value !== undefined &&
					field.value !== null &&
					field.value !== ""
				) {
					initialValues[field.name] = field.value;
				}
			});
			if (Object.keys(initialValues).length > 0) {
				setFormValues(initialValues);
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [jsonForm]);

	const handleInputChange = useCallback(
		(fieldName: string, value: string | boolean) => {
			setFormValues((prev) => {
				if (prev[fieldName] === value) return prev;
				return {
					...prev,
					[fieldName]: value
				};
			});
		},
		[]
	);

	const handleSubmit = async (e: FormEvent) => {
		e.preventDefault();
		const formWithValues = jsonForm.map((field) => ({
			...field,
			value:
				formValues[field.name] || (field.type === "PDFCheckBox" ? false : "")
		}));
		if (setSaving) setSaving(true);
		setError(null);
		try {
			let response;
			if (document?.original_id) {
				response = await fetch(`/api/documents/${document.id}`, {
					method: "PUT",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						id: document.id,
						formFields: formWithValues,
						warrantyRequestId: warrantyRequest.id
					})
				});
			} else {
				response = await fetch("/api/documents", {
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						documentId: attachment.id,
						formFields: formWithValues,
						warrantyRequestId: warrantyRequest.id
					})
				});
			}
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to save filled PDF");
			}
			// Optionally, show a success message or reset form here
			// For now, just reset
			handleReset();
		} catch (error) {
			console.error("Error saving PDF:", error);
			setError(
				error instanceof Error ? error.message : "An unexpected error occurred"
			);
		} finally {
			if (setSaving) setSaving(false);
		}
	};

	const handleReset = useCallback(() => {
		setFormValues({});
		setError(null);
	}, []);

	// Memoize renderFormField to avoid unnecessary re-renders
	const renderFormField = useCallback(
		(field: FormField) => {
			const fieldId = `field-${field.index}`;
			if (field.type === "PDFCheckBox") {
				return (
					<div key={field.index} className="flex items-center space-x-2 mb-4">
						<Checkbox
							id={fieldId}
							checked={!!formValues[field.name]}
							onCheckedChange={(checked) =>
								handleInputChange(field.name, checked)
							}
						/>
						<Label
							htmlFor={fieldId}
							className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
						>
							{field.name}
						</Label>
					</div>
				);
			}
			return (
				<div key={field.index} className="mb-4">
					<Label htmlFor={fieldId} className="text-sm font-medium mb-2 block">
						{field.name}
					</Label>
					<Input
						id={fieldId}
						type="text"
						value={(formValues[field.name] as string) || ""}
						onChange={(e) => handleInputChange(field.name, e.target.value)}
						placeholder={`Enter ${field.name.toLowerCase()}`}
					/>
				</div>
			);
		},
		[formValues, handleInputChange]
	);

	return (
		<div className="container mx-auto py-8 px-4">
			<div className="max-w-4xl mx-auto">
				{/* Attachment Title and Link */}
				<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
					<h1 className="text-3xl font-bold mb-8">{attachment.title}</h1>
					<a
						href={attachment.url}
						target="_blank"
						rel="noopener noreferrer"
						className="text-blue-600 hover:underline text-sm"
					>
						View Original Form
					</a>
				</div>
				{loading || !document || !jsonForm ? (
					<div className="flex justify-center max-h-[175px] items-center">
						<CenteredPageLoader />
					</div>
				) : (
					<div className="w-full">
						{/* Form Section - now full width */}
						<Card>
							<CardHeader>
								<CardTitle>Form Fields</CardTitle>
							</CardHeader>
							<CardContent>
								<form
									onSubmit={handleSubmit}
									className="space-y-4"
									ref={formRef}
								>
									<div className="max-h-96 overflow-y-auto pr-2">
										{jsonForm.map(renderFormField)}
									</div>
									{error && (
										<div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
											<p className="text-sm text-red-600">{error}</p>
										</div>
									)}
									<div className="flex gap-4 pt-4 border-t">
										<Button
											type="button"
											variant="outline"
											onClick={handleReset}
										>
											Reset
										</Button>
									</div>
								</form>
							</CardContent>
						</Card>
					</div>
				)}
			</div>
		</div>
	);
}
