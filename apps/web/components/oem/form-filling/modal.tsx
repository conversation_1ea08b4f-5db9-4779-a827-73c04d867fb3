import * as React from "react";

export function Modal({
    open,
    onClose,
    children,
    noBorder = true,
    hideCloseButton = false,
}: {
    open: boolean;
    onClose: () => void;
    children: React.ReactNode;
    noBorder?: boolean;
    hideCloseButton?: boolean;
}) {
    if (!open) return null;
    return (
        <div className="fixed inset-0 z-50 h-[100dvh] w-screen overflow-y-auto">
            {/* Full screen backdrop with blur */}
            <div 
                className="fixed inset-0 bg-black/30 backdrop-blur-sm"
                onClick={onClose}
                aria-hidden="true"
                style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    height: '100dvh',
                    width: '100vw'
                }}
            />
            {/* Modal container */}
            <div className="fixed inset-0 flex items-center justify-center p-4">
                <div className={`bg-white rounded-xl shadow-2xl min-w-[320px] max-w-4xl w-full max-h-[85vh] overflow-hidden relative flex flex-col ${noBorder ? '' : 'border border-gray-200'}`}
                >
                    {!hideCloseButton && (
                        <button
                            type="button"
                            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 rounded-full p-2 z-10 transition-colors"
                            onClick={onClose}
                            aria-label="Close" 
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    )}
                    {children}
                </div>
            </div>
        </div>
    );
}