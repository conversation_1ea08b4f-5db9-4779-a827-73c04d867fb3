import { Button } from "@/components/ui/button";
import { WarrantyAttachment } from "@/types/warranty";
import { WarrantyRequest } from "@rvhelp/database";
import React from "react";
import FormFillComponent from "./form-fill-component";
import { Modal } from "./modal";

interface FormFillModalProps {
	open: boolean;
	onClose: () => void;
	attachment: WarrantyAttachment;
	warrantyRequest: WarrantyRequest;
}

export function FormFillModal({
	open,
	onClose,
	attachment,
	warrantyRequest
}: FormFillModalProps) {
	const formRef = React.useRef<HTMLFormElement>(null);
	const [saving, setSaving] = React.useState(false);

	if (!open) return null;
	return (
		<Modal open={open} onClose={onClose}>
			<div className="p-6 min-h-[500px] flex flex-col justify-center">
				<h2 className="text-xl font-bold mb-4">Fill PDF Form</h2>
				<FormFillComponent
					attachment={attachment}
					warrantyRequest={warrantyRequest}
					formRef={formRef}
					setSaving={setSaving}
				/>
				<div className="flex gap-4 justify-end mt-6">
					<Button
						variant="outline"
						type="button"
						onClick={onClose}
						disabled={saving}
					>
						Cancel
					</Button>
					<Button
						variant="default"
						type="button"
						onClick={() => formRef.current?.requestSubmit()}
						disabled={saving}
					>
						{saving ? "Saving..." : "Save Form Data"}
					</Button>
				</div>
			</div>
		</Modal>
	);
}

export default FormFillModal;
