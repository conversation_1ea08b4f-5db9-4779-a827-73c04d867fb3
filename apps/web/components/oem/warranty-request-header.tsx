"use client";

import OEMLogo from "@/components/oem/oem-logo";
import { getWarrantyRequestState } from "@/components/oem/warranty-details/warranty-utils";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { JobWithUserAndLocation } from "@/types/global";
import { Calendar, MapPin } from "lucide-react";
import { useMemo, useState } from "react";

interface WarrantyRequestHeaderProps {
	job: JobWithUserAndLocation;
	collapsable?: boolean;
	buttons?: React.ReactNode;
	content?: React.ReactNode;
}

export function WarrantyRequestHeader({
	job,
	buttons,
	content,
	collapsable = true
}: WarrantyRequestHeaderProps) {
	const [isExpanded, setIsExpanded] = useState(!collapsable);

	const requestLabel = useMemo(
		() => `${job.warranty_request.company.name || ""} Warranty Request`.trim(),
		[job.warranty_request.rv_make]
	);

	const formattedDate = useMemo(
		() =>
			new Date(job.warranty_request.created_at).toLocaleDateString("en-US", {
				year: "numeric",
				month: "long",
				day: "numeric"
			}),
		[job.warranty_request.created_at]
	);

	const state = getWarrantyRequestState(job.warranty_request);

	return (
		<div className="mb-4 md:mb-6">
			{/* Header Card */}
			<Card className="transition-shadow hover:shadow-lg overflow-hidden">
				<div className="relative flex flex-col sm:flex-row gap-3 sm:gap-6 p-3 sm:p-4 rounded-lg">
					{job.warranty_request?.company && (
						<div className="flex-shrink-0 self-start">
							<OEMLogo company={job.warranty_request.company} />
						</div>
					)}
					{/* Main content column */}
					<div className="flex-1 min-w-0">
						<div
							className={`flex flex-col sm:flex-row sm:items-center gap-2 mb-2`}
						>
							<h1 className={`text-lg sm:text-2xl font-semibold leading-tight`}>
								{requestLabel}
							</h1>
							<Badge
								className={`capitalize ${state.color} border-0 self-start sm:self-auto`}
							>
								{state.state}
							</Badge>
						</div>

						<div className="flex flex-col gap-y-2 mb-3">
							<div
								className={
									isExpanded
										? "text-sm sm:text-md"
										: "line-clamp-2 text-ellipsis overflow-hidden text-sm sm:text-md"
								}
							>
								<span>{job.message}</span>
							</div>

							<div className="flex items-center text-sm">
								<MapPin className={`h-4 w-4 mr-1.5 flex-shrink-0`} />
								<span className="truncate">{job.location.address}</span>
							</div>
						</div>

						{/* Created date - positioned normally on mobile, bottom right on desktop */}
						<div className="flex items-center text-xs sm:text-sm sm:absolute sm:bottom-4 sm:right-4">
							<Calendar className={`h-4 w-4 mr-1.5`} />
							<span>Created {formattedDate}</span>
						</div>

						{/* Request message and read more */}
						{collapsable && (
							<div className="mt-4">
								{!isExpanded && (
									<button
										onClick={() => setIsExpanded(true)}
										className="underline"
									>
										Read more
									</button>
								)}
								{isExpanded && (
									<button
										onClick={() => setIsExpanded(false)}
										className="underline"
									>
										Hide details
									</button>
								)}
							</div>
						)}

						{buttons && <div className="flex gap-2 mt-4">{buttons}</div>}
					</div>
				</div>

				{/* Desktop Content - Inside Card */}
				{isExpanded && (
					<CardContent className="hidden md:block p-4">{content}</CardContent>
				)}
			</Card>

			{/* Mobile Content - Outside Card */}
			{isExpanded && content && <div className="md:hidden mt-4">{content}</div>}
		</div>
	);
}
