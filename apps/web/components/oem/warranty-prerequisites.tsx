import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CreditCard, FileEdit } from "lucide-react";
import { useRouter } from "next/navigation";

interface WarrantyPrerequisitesProps {
	onConfigureWarrantyPricing?: () => void;
	pricingConfigured: boolean;
	stripeConfigured: boolean;
}

export function WarrantyPrerequisites({
	onConfigureWarrantyPricing,
	pricingConfigured,
	stripeConfigured
}: WarrantyPrerequisitesProps) {
	const router = useRouter();

	return (
		<>
			<Card>
				<CardHeader>
					<CardTitle className="text-xl font-semibold flex items-center">
						{/* <Wrench className="mr-2 h-5 w-5 text-gray-500" /> */}
						Warranty Prerequisites
					</CardTitle>
				</CardHeader>
				<CardContent className="px-6 pb-6 -mt-2">
					<div className="space-y-2">
						<Button
							variant="outline"
							className="w-full"
							disabled={pricingConfigured}
							onClick={onConfigureWarrantyPricing}
						>
							<FileEdit className="mr-2 h-4 w-4" />
							Configure Warranty Pricing
						</Button>
						<Button
							variant="outline"
							className="w-full"
							disabled={stripeConfigured}
							onClick={() => router.push("/provider/billing/dashboard")}
						>
							<CreditCard className="mr-2 h-4 w-4" />
							Configure Payments
						</Button>
					</div>
				</CardContent>
			</Card>
		</>
	);
}
