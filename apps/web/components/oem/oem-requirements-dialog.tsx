import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>ooter,
	<PERSON><PERSON>Header,
	DialogTitle
} from "@/components/ui/dialog";
import { ExtendedWarrantyRequest, WarrantyAttachment } from "@/types/warranty";
import { CheckCircle, Eye, FileText, Image as ImageIcon } from "lucide-react";
import { useState } from "react";
import toast from "react-hot-toast";

interface OEMRequirementsDialogProps {
	open: boolean;
	onClose: () => void;
	warrantyRequest: ExtendedWarrantyRequest;
}

export function OEMRequirementsDialog({
	open,
	onClose,
	warrantyRequest
}: OEMRequirementsDialogProps) {
	const [acknowledged, setAcknowledged] = useState(false);

	const getFileIcon = (type: string) => {
		const lowerType = type.toLowerCase();
		if (
			lowerType.includes("image") ||
			lowerType.includes("jpg") ||
			lowerType.includes("png") ||
			lowerType.includes("jpeg")
		) {
			return <ImageIcon className="w-4 h-4" />;
		}
		return <FileText className="w-4 h-4" />;
	};

	const getRequiredAttachments = (): WarrantyAttachment[] => {
		if (!warrantyRequest.attachments) return [];

		return warrantyRequest.attachments.filter(
			(attachment) => attachment.required && !attachment.completed
		);
	};

	const handlePreview = (attachment: WarrantyAttachment) => {
		if (attachment.url) {
			window.open(attachment.url, "_blank");
		}
	};

	const handleClose = async () => {
		if (acknowledged) {
			try {
				const response = await fetch(
					`/api/warranty-requests/${warrantyRequest.id}/acknowledge-attachments`,
					{
						method: "PATCH",
						headers: {
							"Content-Type": "application/json"
						},
						body: JSON.stringify({
							attachments_acknowledged: true
						})
					}
				);

				if (!response.ok) {
					throw new Error("Failed to update acknowledgment status");
				}

				toast.success("Forms acknowledgment recorded");
			} catch (error) {
				console.error("Error updating acknowledgment status:", error);
				toast.error("Failed to record acknowledgment");
			}
		}

		setAcknowledged(false);
		onClose();
	};

	const requiredAttachments = getRequiredAttachments();

	if (!open) return null;

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="px-6 py-4 min-h-[400px] flex flex-col">
				<DialogHeader>
					<DialogTitle className="text-xl font-bold text-gray-900 pb-4">
						Acknowledge OEM Requirements
					</DialogTitle>
					<div className="flex items-center gap-2">
						<p className="text-sm text-gray-600 px-6">
							Some Warranty work can require additional actions (or information)
							before service can be authorized. Please review the OEM
							requirements below and click the check box to acknowledge that you
							have read the requirements.
						</p>
					</div>
				</DialogHeader>
				{warrantyRequest.requires_return && (
					<div className="flex flex-col gap-4 pb-4">
						<div className="pl-4 flex items-center gap-2">
							<div className="rounded-full px-2">
								<CheckCircle className="w-5 h-5 text-gray-7000" />
							</div>
							<h3 className="text-md font-semibold text-gray-900">
								Requirement: Return of Original Component
							</h3>
						</div>

						<p className="text-sm text-gray-600 px-6">
							This warranty job requires the return of the original component.
							Before further authorization can be approved, you will be required
							to submit the dimensions and weight of the component to be shipped
							so that shipping can be arranged.
						</p>
					</div>
				)}
				{requiredAttachments.length > 0 && (
					<div className="flex flex-col gap-4">
						<div className="pl-4 flex items-center gap-2">
							<div className="rounded-full px-2">
								<CheckCircle className="w-5 h-5 text-gray-7000" />
							</div>
							<h3 className="text-md font-semibold text-gray-900">
								Requirement: Diagnostic Forms
							</h3>
						</div>
						<p className="text-sm text-gray-600 px-6">
							Please print out the following forms and fill them out on site
							when performing your diagnosis. They must be submitted to the OEM
							before additional authorization can be approved.
						</p>
						<div className="flex-1 space-y-4">
							<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
								<div className="bg-white rounded-lg border border-slate-200 overflow-hidden shadow-sm">
									<div className="divide-y divide-slate-100">
										{requiredAttachments.map((attachment, index) => (
											<div
												key={attachment.id || index}
												className={`p-4 ${index % 2 === 0 ? "bg-white" : "bg-slate-25"} hover:bg-slate-50 transition-colors`}
											>
												<div className="flex items-center justify-between">
													<div className="flex items-center gap-3 flex-1 min-w-0">
														<div className="flex-shrink-0 text-slate-500">
															{getFileIcon(attachment.type)}
														</div>
														<div className="flex-1 min-w-0">
															<div className="text-sm font-medium text-slate-900 truncate">
																{attachment.title}
															</div>
															<div className="text-xs text-slate-500 flex items-center gap-2 flex-wrap">
																<span className="capitalize">
																	{attachment.type.replace("_", " ")}
																</span>
																<span className="bg-red-100 text-red-800 px-2 py-0.5 rounded text-xs font-medium">
																	Required
																</span>
																{attachment.component_id && (
																	<span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs font-medium">
																		Component
																	</span>
																)}
															</div>
														</div>
													</div>
													<div className="flex items-center gap-2 flex-shrink-0">
														<button
															type="button"
															onClick={() => handlePreview(attachment)}
															className="p-1 text-slate-500 hover:text-slate-700 transition-colors"
															title="Preview"
														>
															<Eye className="w-4 h-4" />
														</button>
													</div>
												</div>
											</div>
										))}
									</div>
								</div>
							</div>
						</div>
					</div>
				)}

				{(requiredAttachments.length > 0 ||
					warrantyRequest.requires_return) && (
					<div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
						<div className="flex items-start gap-3">
							<Checkbox
								id="acknowledge-forms"
								checked={acknowledged}
								onCheckedChange={(checked) =>
									setAcknowledged(checked as boolean)
								}
								className="mt-0.5"
							/>
							<label
								htmlFor="acknowledge-forms"
								className="text-sm text-amber-800 leading-relaxed cursor-pointer"
							>
								I acknowledge that I am aware of the OEM requirements and will
								not receive payment for this job until they have been satisfied.
							</label>
						</div>
					</div>
				)}

				<DialogFooter className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
					<Button variant="outline" onClick={handleClose} className="px-6">
						Close
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
