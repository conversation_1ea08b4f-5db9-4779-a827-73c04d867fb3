"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	DialogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AlertCircle, CheckCircle, DollarSign } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface WarrantyPricingModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	listingId: string;
	currentWarrantyRate?: number | null;
	onPricingUpdated: () => void;
	isLoading?: boolean;
}

const MoneyInput = ({
	label,
	id,
	value,
	onChange,
	error,
	placeholder,
	required = false,
	onKeyDown
}: {
	label: string;
	id: string;
	value: number | string;
	onChange: (value: number) => void;
	error?: string;
	placeholder: string;
	required?: boolean;
	onKeyDown?: (e: React.KeyboardEvent) => void;
}) => (
	<div className="space-y-2">
		<Label htmlFor={id}>
			{label} {required && <span className="text-destructive">*</span>}
		</Label>
		<div className="relative">
			<Input
				id={id}
				type="number"
				placeholder={placeholder}
				value={value || ""}
				onChange={(e) => onChange(Number(e.target.value))}
				error={error}
				className="pl-6"
				icon={<DollarSign className="w-4 h-4 text-muted-foreground" />}
				onKeyDown={onKeyDown}
				step="0.01"
				min="0"
			/>
		</div>
	</div>
);

export function WarrantyPricingModal({
	open,
	onOpenChange,
	listingId,
	currentWarrantyRate,
	onPricingUpdated,
	isLoading = false
}: WarrantyPricingModalProps) {
	const [warrantyRate, setWarrantyRate] = useState<number>(
		currentWarrantyRate || 0
	);
	const [isSaving, setIsSaving] = useState(false);
	const [error, setError] = useState<string>("");

	// Reset form when modal opens with current values
	useEffect(() => {
		if (open) {
			setWarrantyRate(currentWarrantyRate || 0);
			setError("");
		}
	}, [open, currentWarrantyRate]);

	const handleSave = async () => {
		if (!warrantyRate || warrantyRate <= 0) {
			setError("Warranty rate must be greater than $0");
			return;
		}

		setError("");
		setIsSaving(true);

		try {
			const requestBody = {
				warranty_rate: warrantyRate
			};

			const response = await fetch(`/api/listings/${listingId}/warranty-rate`, {
				method: "PUT",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(requestBody)
			});

			if (!response.ok) {
				const errorData = await response.json();
				console.error("Server response error:", errorData);

				// Check if it's a validation error with details
				if (
					errorData.code === "VALIDATION_ERROR" &&
					errorData.details?.errors
				) {
					const validationErrors = Object.entries(errorData.details.errors)
						.map(([field, message]) => `${field}: ${message}`)
						.join(", ");
					throw new Error(`Validation failed: ${validationErrors}`);
				}

				throw new Error(errorData.error || "Failed to update warranty rate");
			}

			toast.success("Warranty rate updated successfully");
			onPricingUpdated();
			onOpenChange(false);
		} catch (error) {
			console.error("Error updating warranty rate:", error);
			setError(
				error instanceof Error
					? error.message
					: "Failed to update warranty rate"
			);
		} finally {
			setIsSaving(false);
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			e.preventDefault();
			handleSave();
		}
	};

	const canSave = warrantyRate > 0 && !isSaving && !isLoading;

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-lg p-0">
				{/* Header */}
				<div className="bg-gradient-to-r from-[#43806c] to-[#5a8d69] text-white p-6 text-center">
					<DialogTitle className="text-xl font-bold mb-1">
						Configure Warranty Pricing
					</DialogTitle>
					<DialogDescription className="text-white/90">
						Set your hourly rate for warranty work
					</DialogDescription>
				</div>

				<ScrollArea className="max-h-[60vh]">
					<div className="p-6 space-y-6">
						{/* Information Section */}
						<div className="border-2 border-[#43806c] rounded-lg p-5 bg-gradient-to-r from-[#43806c]/5 to-emerald-50/50">
							<div className="flex items-center gap-2 mb-4">
								<CheckCircle className="w-5 h-5 text-[#43806c]" />
								<h3 className="font-semibold text-[#43806c]">
									Warranty Hourly Rate
								</h3>
							</div>
							<p className="text-sm text-gray-700 mb-4 leading-relaxed">
								This is the hourly rate you charge for warranty work. This rate
								may be different from your standard hourly rate and will be
								shared with OEM partners for warranty job assignments.
							</p>
							<div className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded">
								<p className="text-sm text-blue-800 leading-relaxed">
									<strong>Note:</strong> Once configured, this rate will be
									visible to OEM partners when they assign warranty work to you
									through RV Help's warranty portal.
								</p>
							</div>
						</div>

						{/* Rate Input Section */}
						<div className="space-y-4">
							<MoneyInput
								label="Warranty Hourly Rate"
								id="warrantyRate"
								value={warrantyRate}
								onChange={setWarrantyRate}
								error={error}
								placeholder="0.00"
								required
								onKeyDown={handleKeyDown}
							/>

							{warrantyRate > 0 && (
								<div className="bg-green-50 border border-green-200 rounded-lg p-3">
									<div className="flex items-center gap-2">
										<CheckCircle className="w-4 h-4 text-green-600" />
										<span className="text-sm text-green-800 font-medium">
											Rate Preview: ${warrantyRate.toFixed(2)} per hour
										</span>
									</div>
								</div>
							)}
						</div>

						{/* Error Display */}
						{error && (
							<div className="bg-red-50 border border-red-200 rounded-lg p-3">
								<div className="flex items-center gap-2">
									<AlertCircle className="w-4 h-4 text-red-600" />
									<span className="text-sm text-red-800">{error}</span>
								</div>
							</div>
						)}
					</div>
				</ScrollArea>

				<DialogFooter className="p-6 pt-0">
					<div className="flex flex-col w-full gap-3">
						<Button
							onClick={handleSave}
							disabled={!canSave}
							className="w-full bg-[#43806c] hover:bg-[#2c5446] text-white text-base py-3"
						>
							{isSaving ? "Saving..." : "Save Warranty Rate"}
						</Button>
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
							disabled={isSaving}
						>
							Cancel
						</Button>
					</div>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
