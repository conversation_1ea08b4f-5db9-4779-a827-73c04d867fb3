"use client";

import { zodResolver } from "@hookform/resolvers/zod";
// ... existing code ...
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

import type { QuoteWithMessages } from "@/app/(main)/provider/(dashboard)/jobs/[id]/types";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { formatCurrency } from "@/lib/utils";
import type { ExtendedWarrantyRequest } from "@/types/warranty";
import { CheckCircle, Loader2, X } from "lucide-react";

const invoiceItemSchema = z.object({
	description: z.string().min(1, "Description is required"),
	quantity: z.number().min(1, "Quantity must be at least 1").default(1),
	unit_price: z.number().min(0, "Unit price must be positive")
});

const invoiceFormSchema = z.object({
	customer_name: z.string().min(1, "Customer name is required"),
	customer_email: z.string().email("Invalid email address"),
	customer_phone: z.string().optional(),
	due_date: z.string().optional(),
	currency: z.string().default("usd"),
	notes: z.string().optional(),
	items: z.array(invoiceItemSchema).min(1, "At least one item is required")
});

type InvoiceFormValues = z.infer<typeof invoiceFormSchema>;

interface InvoicePreviewModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	request: ExtendedWarrantyRequest;
	quote: QuoteWithMessages;
	actualHours: number;
	onInvoiceCreated: (invoiceData: any) => void;
	onCancel: () => void;
	onInvoiceDataChange?: (invoiceData: any) => void;
}

export function InvoicePreviewModal({
	open,
	onOpenChange,
	request,
	quote,
	actualHours,
	onInvoiceCreated,
	onCancel,
	onInvoiceDataChange
}: InvoicePreviewModalProps) {
	const router = useRouter();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSaving, setIsSaving] = useState(false);

	// Calculate default values
	const hourlyRate =
		quote.listing.pricing_settings.warranty_rate ||
		quote.listing.pricing_settings.hourly_rate;
	const dispatchFee = quote.listing.pricing_settings.dispatch_fee;
	const totalHours = actualHours;

	const defaultInvoiceData = useMemo(
		(): InvoiceFormValues => ({
			customer_name: `${request.first_name} ${request.last_name}`,
			customer_email: request.email,
			customer_phone: request.phone || "",
			due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
				.toISOString()
				.split("T")[0], // 30 days from now
			currency: "usd",
			notes: `${request.company?.name || "Provider"} Warranty Service Request: ${request.id}\nRV: ${request.rv_year} ${request.rv_make} ${request.rv_model}\nCause: ${request.cause || "N/A"}\nCorrection: ${request.correction || "N/A"}`,
			items: [
				{
					description: "Dispatch Fee",
					quantity: 1,
					unit_price: dispatchFee
				},
				{
					description: "Labor",
					quantity: actualHours,
					unit_price: hourlyRate
				}
			]
		}),
		[request, dispatchFee, actualHours, hourlyRate]
	);

	const form = useForm<InvoiceFormValues>({
		resolver: zodResolver(invoiceFormSchema),
		defaultValues: defaultInvoiceData
	});

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: "items"
	});

	// Reset form when modal opens
	useEffect(() => {
		if (open) {
			form.reset(defaultInvoiceData);
		}
	}, [open, form, defaultInvoiceData]);

	// Watch for form changes and notify parent
	useEffect(() => {
		if (open && onInvoiceDataChange) {
			const subscription = form.watch((value) => {
				onInvoiceDataChange(value as InvoiceFormValues);
			});
			return () => subscription.unsubscribe();
		}
	}, [open, form, onInvoiceDataChange]);

	const calculateItemTotal = (item: {
		unit_price?: number;
		quantity?: number;
	}) => {
		const quantity = item.quantity || 1;
		const unitPrice = item.unit_price || 0;
		return quantity * unitPrice; // Return in dollars for display
	};

	const watchedItems = form.watch("items");
	const calculateTotal = useMemo(() => {
		const total = watchedItems.reduce(
			(total, item) => total + calculateItemTotal(item),
			0
		);
		return total;
	}, [watchedItems]);

	const handleSaveAndContinue = async () => {
		try {
			setIsSaving(true);

			// Validate the form
			const isValid = await form.trigger();
			if (!isValid) {
				toast.error("Please fix the form errors before continuing");
				return;
			}

			const data = form.getValues();

			// Convert all dollar amounts to cents
			const itemsWithCents = data.items.map((item) => ({
				description: item.description,
				unit_price: Math.round(item.unit_price * 100),
				quantity: item.quantity || 1,
				amount: Math.round(item.unit_price * (item.quantity || 1) * 100)
			}));

			// Calculate total in cents
			const totalAmount = itemsWithCents.reduce(
				(total, item) => total + item.amount,
				0
			);

			const invoiceData = {
				...data,
				amount: totalAmount,
				items: itemsWithCents,
				status: "SENT",
				notes: data.notes,
				warranty_request_id: request.id
			};

			const response = await fetch("/api/invoices", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(invoiceData)
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to create invoice");
			}

			const responseData = await response.json();

			toast.success("Invoice created successfully");
			onInvoiceCreated(responseData.invoice);
			onOpenChange(false);
		} catch (error) {
			console.error("Error creating invoice:", error);
			toast.error("Failed to create invoice. Please try again.");
		} finally {
			setIsSaving(false);
		}
	};

	const handleCancel = () => {
		onCancel();
		onOpenChange(false);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Review & Customize Invoice</DialogTitle>
					<DialogDescription>
						Review and customize your invoice before sending it to the warranty
						company.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form className="space-y-6" onSubmit={(e) => e.preventDefault()}>
						<Card>
							<CardHeader>
								<CardTitle>Customer Information</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="customer_name"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Customer Name</FormLabel>
												<FormControl>
													<Input placeholder="John Doe" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="customer_email"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Customer Email</FormLabel>
												<FormControl>
													<Input
														type="email"
														placeholder="<EMAIL>"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="customer_phone"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Customer Phone (Optional)</FormLabel>
												<FormControl>
													<Input placeholder="(*************" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="due_date"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Due Date (Optional)</FormLabel>
												<FormControl>
													<Input type="date" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>Invoice Items</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								{fields.map((field, index) => (
									<div
										key={field.id}
										className="grid grid-cols-1 md:grid-cols-12 gap-4 items-end"
									>
										<div className="md:col-span-5">
											<FormField
												control={form.control}
												name={`items.${index}.description`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Description</FormLabel>
														<FormControl>
															<Input
																placeholder="Item description"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
										<div className="md:col-span-2">
											<FormField
												control={form.control}
												name={`items.${index}.quantity`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Quantity</FormLabel>
														<FormControl>
															<Input
																type="number"
																min="1"
																{...field}
																onChange={(e) =>
																	field.onChange(
																		Number.parseInt(e.target.value) || 1
																	)
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
										<div className="md:col-span-2">
											<FormField
												control={form.control}
												name={`items.${index}.unit_price`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Unit Price</FormLabel>
														<FormControl>
															<Input
																type="text"
																inputMode="decimal"
																placeholder="0.00"
																defaultValue={
																	field.value === 0
																		? ""
																		: field.value.toFixed(2)
																}
																onChange={(e) => {
																	const value = e.target.value;
																	const sanitizedValue = value.replace(
																		/[^\d.]/g,
																		""
																	);
																	const parts = sanitizedValue.split(".");
																	let numericValue: number;

																	if (parts.length > 1) {
																		const formattedValue = `${parts[0]}.${parts[1].slice(0, 2)}`;
																		numericValue =
																			Number.parseFloat(formattedValue);
																	} else {
																		numericValue =
																			Number.parseFloat(sanitizedValue);
																	}

																	field.onChange(
																		isNaN(numericValue) ? 0 : numericValue
																	);
																}}
																onBlur={(e) => {
																	const value = Number.parseFloat(
																		e.target.value
																	);
																	if (!isNaN(value)) {
																		e.target.value = value.toFixed(2);
																		field.onChange(value);
																	}
																}}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
										<div className="md:col-span-2">
											<FormLabel>Amount</FormLabel>
											<div className="h-10 px-3 py-2 border border-input bg-muted rounded-md text-sm">
												{formatCurrency(
													calculateItemTotal(watchedItems[index] || {}) * 100
												)}
											</div>
										</div>
										<div className="md:col-span-1">
											<Button
												type="button"
												variant="outline"
												onClick={() => remove(index)}
												disabled={fields.length === 1}
											>
												<X className="h-4 w-4" />
											</Button>
										</div>
									</div>
								))}
								<Button
									type="button"
									variant="outline"
									onClick={() =>
										append({ description: "", unit_price: 0, quantity: 1 })
									}
								>
									Add Item
								</Button>
							</CardContent>
							<CardFooter>
								<div className="text-lg font-semibold">
									Total: {formatCurrency(calculateTotal * 100)}
								</div>
							</CardFooter>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>Additional Information</CardTitle>
							</CardHeader>
							<CardContent>
								<FormField
									control={form.control}
									name="notes"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Notes (Optional)</FormLabel>
											<FormControl>
												<Textarea
													placeholder="Add any additional notes or terms to the invoice"
													className="min-h-[100px]"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</CardContent>
						</Card>
					</form>
				</Form>

				<DialogFooter className="flex justify-between">
					<Button
						type="button"
						variant="outline"
						onClick={handleCancel}
						disabled={isSaving}
					>
						Cancel
					</Button>
					<Button
						type="button"
						onClick={handleSaveAndContinue}
						disabled={isSaving}
					>
						{isSaving ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Creating Invoice...
							</>
						) : (
							<>
								<CheckCircle className="mr-2 h-4 w-4" />
								Create & Send Invoice
							</>
						)}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
