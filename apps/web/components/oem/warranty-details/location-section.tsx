import { ExtendedWarrantyRequest } from "@/types/warranty";

export function LocationSection({
	request,
	onEdit
}: {
	request: ExtendedWarrantyRequest;
	onEdit?: () => void;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
			<div className="flex items-center justify-between mb-3">
				<div className="flex items-center gap-2 font-bold text-md text-slate-700">
					{/* <div className={`bg-primary rounded-full p-1`}>
						<MapPin className="w-6 h-6" />
					</div> */}
					Service Location
				</div>
				{onEdit && (
					<button
						type="button"
						onClick={onEdit}
						className="text-xs font-medium underline"
					>
						Edit
					</button>
				)}
			</div>
			<div className="text-sm">
				<div className="text-xs font-semibold text-gray-600">Address</div>
				<div>{request.location.address}</div>
			</div>
		</div>
	);
}
