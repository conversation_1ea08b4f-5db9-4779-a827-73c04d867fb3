import { ExtendedWarrantyRequest } from "@/types/warranty";
import { TimelineEventType } from "@rvhelp/database";
import { formatDate, getEventTypeColor } from "./warranty-utils";

interface TimelineUpdateWithUser {
	id: string;
	date: Date;
	event_type: TimelineEventType;
	details: any;
	updated_by?: {
		first_name: string | null;
		last_name: string | null;
		email: string;
	};
}

export function StatusHistorySection({
	warrantyRequest,
	className
}: {
	warrantyRequest: ExtendedWarrantyRequest;
	className?: string;
}) {
	return (
		<div
			className={`bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200 ${className}`}
		>
			{warrantyRequest.timeline_updates &&
			warrantyRequest.timeline_updates.length > 0 ? (
				<div>
					<div className="flex items-center gap-2 font-bold mb-4 text-md text-slate-700">
						{/* <div className={`rounded-full p-1`}>
							<Clock className="w-6 h-6" />
						</div> */}
						History
					</div>
					<div className="bg-white rounded-lg border border-slate-200 overflow-hidden shadow-sm">
						<div className="overflow-x-auto">
							<table className="w-full">
								<thead className="bg-slate-100 border-b border-slate-200">
									<tr>
										<th
											scope="col"
											className="px-4 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider"
										>
											Date
										</th>
										<th
											scope="col"
											className="px-4 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider"
										>
											Status
										</th>
										<th
											scope="col"
											className="px-4 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider"
										>
											Updated By
										</th>
										<th
											scope="col"
											className="px-4 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider"
										>
											Notes
										</th>
									</tr>
								</thead>
								<tbody className="divide-y divide-slate-100">
									{warrantyRequest.timeline_updates
										.filter(
											(update) =>
												update.event_type !==
													TimelineEventType.TECHNICIAN_INVITED &&
												update.event_type !==
													TimelineEventType.TECHNICIAN_ACCEPTED &&
												update.event_type !==
													TimelineEventType.TECHNICIAN_REJECTED
										)
										.map((update, index) => (
											<tr
												key={update.id}
												className={`${index % 2 === 0 ? "bg-white" : "bg-slate-25"} hover:bg-slate-50 transition-colors`}
											>
												<td className="px-4 py-3 whitespace-nowrap text-sm text-slate-600 font-medium">
													{formatDate(update.date)}
												</td>
												<td className="px-4 py-3 whitespace-nowrap text-sm">
													<span
														className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full shadow-sm ${getEventTypeColor(update.event_type)}`}
													>
														{update.event_type.replace("_", " ")}
													</span>
												</td>
												<td className="px-4 py-3 whitespace-nowrap text-sm text-slate-600">
													<div className="flex items-center">
														<div className="bg-slate-200 rounded-full p-1 mr-2">
															<svg
																className="w-3 h-3 text-slate-500"
																fill="currentColor"
																viewBox="0 0 20 20"
															>
																<path
																	fillRule="evenodd"
																	d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
																	clipRule="evenodd"
																/>
															</svg>
														</div>
														{update.updated_by?.first_name &&
														update.updated_by?.last_name
															? `${update.updated_by.first_name} ${update.updated_by.last_name}`
															: "Unknown User"}
													</div>
												</td>
												<td className="px-4 py-3 text-sm text-slate-600 max-w-xs">
													<div className="break-words">
														{update.details &&
														typeof update.details === "object" &&
														"notes" in update.details &&
														update.details.notes ? (
															<span className="text-slate-700">
																{String(update.details.notes)}
															</span>
														) : (
															<span className="text-slate-400 italic">
																No notes
															</span>
														)}
													</div>
												</td>
											</tr>
										))}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			) : (
				<div className="text-center py-8 text-gray-500">
					No status updates available
				</div>
			)}
		</div>
	);
}
