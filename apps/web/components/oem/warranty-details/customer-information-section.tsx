import { JobWithUserAndLocation } from "@/types/global";
import { User } from "lucide-react";

export function CustomerInformationSection({
	job,
	onEdit
}: {
	job: JobWithUserAndLocation;
	onEdit?: () => void;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
			<div className="flex items-center justify-between mb-3">
				<div className="flex items-center gap-2 font-bold text-md text-slate-700">
					<div className={`rounded-full p-1`}>
						<User className="w-6 h-6" />
					</div>
					Customer Information
				</div>
				{onEdit && (
					<button
						type="button"
						onClick={onEdit}
						className="text-xs font-medium underline"
					>
						Edit
					</button>
				)}
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
				<div>
					<div className="text-xs font-semibold text-gray-600">Name</div>
					<div>
						{job.first_name} {job.last_name}
					</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Email</div>
					<div>{job.email}</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Phone</div>
					<div>{job.phone}</div>
				</div>

				<div>
					<div className="text-xs font-semibold text-gray-600">Location</div>
					<div>{job.location.address}</div>
				</div>
			</div>
		</div>
	);
}
