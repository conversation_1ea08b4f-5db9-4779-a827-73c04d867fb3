import { ExtendedWarrantyRequest } from "@/types/warranty";

export function IssueDescriptionSection({
	request,
	onEdit,
	isProvider = false
}: {
	request: ExtendedWarrantyRequest;
	onEdit?: () => void;
	isProvider: boolean;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200 flex-1 flex flex-col">
			<div className="flex items-center justify-between mb-3">
				<div className="flex items-center gap-1 font-bold text-md text-slate-700">
					{/* <div className={`rounded-full p-1`}>
						<Info className="w-6 h-6" />
					</div> */}
					Issue Description
				</div>
				{onEdit && (
					<button
						type="button"
						onClick={onEdit}
						className="text-xs font-medium underline"
					>
						Edit
					</button>
				)}
			</div>
			<div className="grid grid-cols-1 gap-2 text-sm">
				<div>
					<div className="text-xs font-semibold text-gray-600">Description</div>
					<div>{request.complaint}</div>
				</div>
				{isProvider && request.notes_to_provider && (
					<div>
						<div className="text-xs font-semibold text-gray-600 pt-2">
							Notes to Provider
						</div>
						<div>{request.notes_to_provider}</div>
					</div>
				)}
				<div>
					<div className="text-xs font-semibold text-gray-600 pt-2">
						Affected Component
					</div>
					{request.component?.type || request.component?.manufacturer ? (
						<div>{`${request.component.type} (${request.component.manufacturer})`}</div>
					) : (
						<div>No component information specified</div>
					)}
					{request.requires_return && (
						<div className="text-xs font-semibold text-gray-600 pt-2">
							NOTE: Requires return of original component
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
