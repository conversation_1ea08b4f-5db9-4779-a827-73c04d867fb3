import { JobWithUserAndLocation } from "@/types/global";

export function RVDetailsSection({
	job,
	onEdit
}: {
	job: JobWithUserAndLocation;
	onEdit?: () => void;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
			<div className="flex items-center justify-between mb-3">
				<div className="flex items-center gap-2 font-bold text-md text-slate-700">
					{/* <div className={`rounded-full p-1`}>
						<Caravan className="w-6 h-6" />
					</div> */}
					RV Details
				</div>
				{onEdit && (
					<button
						type="button"
						onClick={onEdit}
						className="text-xs font-medium underline"
					>
						Edit
					</button>
				)}
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
				<div>
					<div className="text-xs font-semibold text-gray-600">Make</div>
					<div>{job.rv_make}</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Model</div>
					<div>{job.rv_model}</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Year</div>
					<div>{job.rv_year}</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Type</div>
					<div>{job.rv_type}</div>
				</div>
			</div>
		</div>
	);
}
