import { Listing } from "@rvhelp/database";

export function ProviderInformationSection({
	listing
}: {
	listing: Partial<Listing>;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
			{listing ? (
				<div>
					<div className="flex items-center justify-between mb-3">
						<div className="flex items-center gap-2 font-bold text-md text-slate-700">
							{/* wrench icon  */}
							{/* <div className="rounded-full p-1 ">
								<Wrench className="w-4 h-4 text-transparent fill-white" />
							</div> */}
							Provider Information
						</div>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
						{listing.business_name && (
							<div>
								<div className="text-xs font-semibold text-gray-600">
									Business Name
								</div>
								<div>{listing.business_name}</div>
							</div>
						)}
						<div>
							<div className="text-xs font-semibold text-gray-600">Name</div>
							<div>
								{listing.first_name} {listing.last_name}
							</div>
						</div>
						{listing.email && (
							<div>
								<div className="text-xs font-semibold text-gray-600">Email</div>
								<div>{listing.email}</div>
							</div>
						)}
						{listing.phone && (
							<div>
								<div className="text-xs font-semibold text-gray-600">Phone</div>
								<div>{listing.phone}</div>
							</div>
						)}
					</div>
				</div>
			) : (
				<div className="text-center py-8 text-gray-500">
					No technician information available
				</div>
			)}
		</div>
	);
}
