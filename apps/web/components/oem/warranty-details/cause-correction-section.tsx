import { ExtendedWarrantyRequest } from "@/types/warranty";

export function CauseCorrectionSection({
	request,
	onEdit
}: {
	request: ExtendedWarrantyRequest;
	onEdit?: () => void;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
			<div className="flex items-center justify-between mb-3">
				<div className="flex items-center gap-2 font-bold text-md text-slate-700">
					{/* <div className={`rounded-full p-1`}>
						<Hammer className="w-6 h-6" />
					</div> */}
					Cause & Correction
				</div>
				{onEdit && (
					<button
						type="button"
						onClick={onEdit}
						className="text-xs font-medium underline"
					>
						Edit
					</button>
				)}
			</div>
			<div className="grid grid-cols-1 gap-4 text-sm">
				<div>
					<div className="text-xs font-semibold text-gray-600">Cause</div>
					<div>
						{request.cause || (
							<span className="text-gray-500 italic">No cause specified</span>
						)}
					</div>
				</div>
				<div>
					<div className="text-xs font-semibold text-gray-600">Correction</div>
					<div>
						{request.correction || (
							<span className="text-gray-500 italic">
								No correction specified
							</span>
						)}
					</div>
				</div>
			</div>
			<div className="grid grid-cols-1 gap-2 pt-4">
				<div>
					<div className="text-xs font-semibold text-gray-600">
						Estimated Hours
					</div>
					<div>
						{request.estimated_hours || (
							<span className="text-gray-500 italic text-sm">
								No hours specified
							</span>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
