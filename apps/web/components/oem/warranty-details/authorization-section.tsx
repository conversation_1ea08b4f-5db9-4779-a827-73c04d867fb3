import { ExtendedWarrantyRequest } from "@/types/warranty";

export function AuthorizationSection({
	request,
	onEdit
}: {
	request: ExtendedWarrantyRequest;
	onEdit?: () => void;
}) {
	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200 flex-1 flex flex-col">
			<div className="flex items-center justify-between mb-3">
				<div className="flex items-center gap-1 font-bold text-md text-slate-700">
					{/* <div className={`rounded-full p-1`}>
						<ShieldCheck className="w-6 h-6" />
					</div> */}
					Service Authorization
				</div>
				{onEdit && (
					<button
						type="button"
						onClick={onEdit}
						className="text-xs font-medium underline"
					>
						Edit
					</button>
				)}
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
				<div>
					<div className="text-xs font-semibold text-gray-600">
						Authorization Type
					</div>
					<div>
						{request.authorization_type || (
							<span className="text-gray-500 italic">No hours specified</span>
						)}
					</div>
				</div>

				<div>
					<div className="text-xs font-semibold text-gray-600">
						Approved Hours
					</div>
					<div>
						{request.approved_hours || (
							<span className="text-gray-500 italic">No hours specified</span>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
