import { useAuth } from "@/lib/hooks/useAuth";
import { ExtendedWarrantyRequest, WarrantyAttachment } from "@/types/warranty";
import { Eye, FileText, Image as ImageIcon, Paperclip } from "lucide-react";
import { useEffect, useMemo, useState } from "react";

type AttachmentWithSource = WarrantyAttachment & {
	source: "direct" | "status_update";
	status_update_id?: string;
	status_update_date?: Date;
	status_update_user?: {
		first_name: string | null;
		last_name: string | null;
	};
};

export function AttachmentSection({
	request
}: {
	request: ExtendedWarrantyRequest;
}) {
	const { user } = useAuth();
	const [allAttachments, setAllAttachments] = useState<AttachmentWithSource[]>(
		[]
	);
	const [loading, setLoading] = useState(false);

	// Memoize the base attachments so the reference is stable unless the actual data changes
	const baseAttachments = useMemo(() => {
		const attachments: AttachmentWithSource[] = [];
		if (request.attachments) {
			attachments.push(
				...request.attachments.map((attachment) => ({
					...attachment,
					source: "direct" as const
				}))
			);
		}
		if (request.timeline_updates) {
			request.timeline_updates.forEach((update) => {
				if (update.details?.attachments) {
					attachments.push(
						...update.details.attachments.map((attachment) => ({
							...attachment,
							source: "status_update" as const,
							status_update_id: update.id,
							status_update_date: update.date,
							status_update_user: update.updated_by
						}))
					);
				}
			});
		}
		return attachments;
	}, [request.attachments, request.timeline_updates]);

	// Only depend on stable, minimal values
	useEffect(() => {
		let isMounted = true;
		async function fetchAttachments() {
			setLoading(true);
			const fetches = baseAttachments.map(async (attachment) => {
				if (
					attachment.id &&
					attachment.type === "form" &&
					attachment.completed
				) {
					try {
						const res = await fetch(`/api/documents/${attachment.id}`);
						if (res.ok) {
							const doc = await res.json();
							return {
								...attachment,
								status_update_date: doc.updated_at
									? new Date(doc.updated_at)
									: attachment.status_update_date,
								status_update_user: user
									? { first_name: user.first_name, last_name: user.last_name }
									: attachment.status_update_user
							};
						}
					} catch (e) {
						console.error("Error fetching attachment:", e);
					}
				}
				return attachment;
			});
			const resolved = await Promise.all(fetches);
			if (isMounted) {
				setAllAttachments(resolved);
				setLoading(false);
			}
		}
		fetchAttachments();
		return () => {
			isMounted = false;
		};
	}, [baseAttachments, user]);

	const getFileIcon = (type: string) => {
		const lowerType = type.toLowerCase();
		if (
			lowerType.includes("image") ||
			lowerType.includes("jpg") ||
			lowerType.includes("png") ||
			lowerType.includes("jpeg")
		) {
			return <ImageIcon className="w-4 h-4" />;
		}
		return <FileText className="w-4 h-4" />;
	};

	const formatUserName = (
		user: { first_name: string | null; last_name: string | null } | undefined
	) => {
		if (!user || (!user.first_name && !user.last_name)) {
			return "Unknown User";
		}
		return `${user.first_name || ""} ${user.last_name || ""}`.trim();
	};

	const formatDate = (date: Date | undefined) => {
		if (!date) return "";
		return new Date(date).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		});
	};

	const handlePreview = (attachment: AttachmentWithSource) => {
		if (attachment.url) {
			window.open(attachment.url, "_blank");
		}
	};

	return (
		<div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-lg border border-slate-200">
			<div>
				<div className="flex items-center gap-2 font-bold mb-4 text-md text-slate-700">
					{/* <div className={"rounded-full p-1"}>
						<Paperclip className="w-4 h-4" />
					</div> */}
					Attachments
				</div>
				{allAttachments.length > 0 ? (
					<div className="bg-white rounded-lg border border-slate-200 overflow-hidden shadow-sm">
						<div className="divide-y divide-slate-100">
							{allAttachments.map((attachment, index) => (
								<div
									key={attachment.id || `${attachment.source}-${index}`}
									className={`p-4 ${index % 2 === 0 ? "bg-white" : "bg-slate-25"} hover:bg-slate-50 transition-colors`}
								>
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-3 flex-1 min-w-0">
											<div className="flex-shrink-0 text-slate-500">
												{getFileIcon(attachment.type)}
											</div>
											<div className="flex-1 min-w-0">
												<div className="text-sm font-medium text-slate-900 truncate">
													{attachment.title}
												</div>
												<div className="text-xs text-slate-500 flex items-center gap-2 flex-wrap">
													<span className="capitalize">
														{attachment.type.replace("_", " ")}
													</span>
													{attachment.required && !attachment.completed && (
														<span className="bg-red-100 text-red-800 px-2 py-0.5 rounded text-xs font-medium">
															Required
														</span>
													)}
													{attachment.completed && (
														<span className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs font-medium">
															Updated
														</span>
													)}
													{attachment.component_id && (
														<span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs font-medium">
															Component
														</span>
													)}
													{attachment.source === "status_update" && (
														<span className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs font-medium">
															Status Update
														</span>
													)}
												</div>
												{attachment.source === "status_update" ||
													(attachment.type === "form" &&
														attachment.completed && (
															<div className="text-xs text-slate-400 mt-1">
																Added by{" "}
																{formatUserName(attachment.status_update_user)}{" "}
																on {formatDate(attachment.status_update_date)}
															</div>
														))}
											</div>
										</div>
										<div className="flex items-center gap-2 flex-shrink-0">
											<button
												type="button"
												onClick={() => handlePreview(attachment)}
												className="p-1 text-slate-500 hover:text-slate-700 transition-colors"
												title="Preview"
											>
												<Eye className="w-4 h-4" />
											</button>
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				) : (
					<div className="text-center py-8 text-gray-500">
						<Paperclip className="w-8 h-8 mx-auto mb-2 text-gray-400" />
						<p>No attachments available</p>
						<p className="text-sm text-gray-400 mt-1">
							Files and images will appear here when uploaded
						</p>
					</div>
				)}
			</div>
		</div>
	);
}
