import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	DialogTitle
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ResolutionStatus } from "@prisma/client";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { JobWithQuoteAndMessages, QuoteWithMessages } from "../types";

interface CancelJobModalProps {
	open: boolean;
	onClose: () => void;
	job: JobWithQuoteAndMessages;
	quote: QuoteWithMessages;
	onJobCancelled: () => void;
}

export function CancelJobModal({
	open,
	onClose,
	job,
	quote,
	onJobCancelled
}: CancelJobModalProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [resolutionStatus, setResolutionStatus] = useState<ResolutionStatus>(
		ResolutionStatus.CANCELLED
	);
	const [notes, setNotes] = useState("");

	const handleCancelJob = async () => {
		if (!quote) {
			toast.error("No job found");
			return;
		}

		setIsSubmitting(true);
		try {
			const response = await fetch(
				`/api/provider/quotes/${quote.id}/complete`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						// Existing API structure
						sendInvoice: false,
						requestReview: false,
						reviewDelayHours: undefined,
						// Additional resolution data (will be stored in metadata or handled by updated API)
						resolutionStatus: ResolutionStatus.CANCELLED,
						resolutionNotes: notes.trim() || undefined
					})
				}
			);

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.message || "Failed to complete job");
			}

			toast.success("Job completed successfully");
			onJobCancelled();
			onClose();
		} catch (error) {
			console.error("Error completing job:", error);
			toast.error(
				error instanceof Error ? error.message : "Failed to complete job"
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		if (!isSubmitting) {
			setResolutionStatus(ResolutionStatus.CANCELLED);
			setNotes("");
			onClose();
		}
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>{"Cancel Job"}</DialogTitle>
				</DialogHeader>
				<div className="space-y-4 py-4">
					<div className="space-y-4">
						<div>
							<Label>Warranty Service Request</Label>
							<div className="mt-1 p-3 bg-gray-50 rounded-md">
								<p className="text-sm text-gray-700 whitespace-pre-wrap">
									{job.message}
								</p>
							</div>
						</div>

						<div>
							<Label htmlFor="notes">Notes (Optional)</Label>
							<Textarea
								id="notes"
								value={notes}
								onChange={(e) => setNotes(e.target.value)}
								placeholder="Add any additional notes about the job completion..."
								className="min-h-[100px] mt-1"
								aria-describedby="notes-help"
							/>
							<p className="mt-1 text-sm text-gray-500" id="notes-help">
								Add any relevant details regarding the reason for cancelling the
								job.
							</p>
						</div>

						<div className="flex justify-end gap-3">
							<Button
								variant="outline"
								onClick={handleClose}
								disabled={isSubmitting}
							>
								Close
							</Button>
							<Button
								variant="default"
								onClick={handleCancelJob}
								className="bg-emerald-600 hover:bg-emerald-700"
								disabled={isSubmitting}
							>
								Cancel Job
							</Button>
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
