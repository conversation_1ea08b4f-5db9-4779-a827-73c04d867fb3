"use client";
import { ProviderChecklist } from "@/components/oem/provider-checklist";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { JobWithUserAndLocation } from "@/types/global";
import { StripeConnection, WarrantyRequestStatus } from "@rvhelp/database";
import { useState } from "react";
import { QuoteWithMessages } from "../../app/(main)/provider/(dashboard)/jobs/[id]/types";
import { AttachmentSection } from "./warranty-details/attachment-section";
import { AuthorizationSection } from "./warranty-details/authorization-section";
import { CauseCorrectionSection } from "./warranty-details/cause-correction-section";
import { IssueDescriptionSection } from "./warranty-details/issue-description-section";
import { StatusHistorySection } from "./warranty-details/status-history-section";

interface WarrantyRequestHeaderProps {
	quote: QuoteWithMessages;
	job: JobWithUserAndLocation;
	stripeConnect: StripeConnection;
	onRequestAuthorization: () => void;
	onRequestPayment: () => void;
	onDownloadForms: () => void;
	onCompleteJob: () => void;
	onStatusUpdated?: () => void;
	onStartJob: () => void;
	collapsable?: boolean;
	buttons?: React.ReactNode;
	content?: React.ReactNode;
}

export function ProviderWarrantyTabs({
	quote,
	job,
	stripeConnect,
	onRequestAuthorization,
	onRequestPayment,
	onDownloadForms,
	onCompleteJob,
	onStartJob
}: WarrantyRequestHeaderProps) {
	const defaultTab =
		job.warranty_request.status !== WarrantyRequestStatus.JOB_CANCELLED
			? "checklist"
			: "details";

	const [activeTab, setActiveTab] = useState(defaultTab);

	// Determine available tabs based on warranty request status
	const availableTabs = [
		...(job.warranty_request.status !== WarrantyRequestStatus.JOB_CANCELLED
			? [{ value: "checklist", label: "Warranty Checklist" }]
			: []),
		{ value: "details", label: "Issue Details" },
		{ value: "messages", label: "Status History" }
	];

	return (
		<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
			{/* Desktop Tabs */}
			<TabsList className="hidden md:grid w-full grid-cols-3 bg-slate-100">
				{job.warranty_request.status !==
					WarrantyRequestStatus.JOB_CANCELLED && (
					<TabsTrigger value="checklist">Warranty Checklist</TabsTrigger>
				)}
				<TabsTrigger value="details">Issue Details</TabsTrigger>
				<TabsTrigger value="messages">Status History</TabsTrigger>
			</TabsList>

			{/* Mobile Select */}
			<div className="md:hidden mb-4">
				<select
					value={activeTab}
					onChange={(e) => setActiveTab(e.target.value)}
					className="w-full block px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-emerald-600 focus:border-transparent"
				>
					{availableTabs.map((tab) => (
						<option key={tab.value} value={tab.value}>
							{tab.label}
						</option>
					))}
				</select>
			</div>
			{job.warranty_request.status !== WarrantyRequestStatus.JOB_CANCELLED && (
				<TabsContent value="checklist">
					<div className="space-y-4 text-gray-900">
						<ProviderChecklist
							quote={quote}
							warrantyRequest={job.warranty_request}
							stripeConnect={stripeConnect}
							onStartJob={onStartJob}
							onRequestAuthorization={onRequestAuthorization}
							onRequestPayment={onRequestPayment}
							onDownloadForms={onDownloadForms}
							onCompleteJob={onCompleteJob}
						/>
					</div>
				</TabsContent>
			)}
			<TabsContent value="details">
				<div className="space-y-4 text-gray-900">
					<IssueDescriptionSection
						request={job.warranty_request}
						isProvider={true}
					/>
					<AuthorizationSection request={job.warranty_request} />
					<CauseCorrectionSection request={job.warranty_request} />
					<AttachmentSection request={job.warranty_request} />
				</div>
			</TabsContent>
			<TabsContent value="messages">
				<StatusHistorySection warrantyRequest={job.warranty_request} />
			</TabsContent>
		</Tabs>
	);
}
