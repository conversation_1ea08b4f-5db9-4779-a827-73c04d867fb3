"use client";

import { useSession } from "next-auth/react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
	DialogDescription,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";

import type { QuoteWithMessages } from "@/app/(main)/provider/(dashboard)/jobs/[id]/types";
import type {
	ExtendedCompany,
	ExtendedWarrantyRequest
} from "@/types/warranty";

import { useProviderUpdateWizard } from "./use-update-wizard";

const ProviderUpdateWizardSkeleton = () => (
	<Card>
		<CardHeader>
			<Skeleton className="h-8 w-48" />
			<Skeleton className="h-4 w-64 mt-2" />
		</CardHeader>
		<CardContent>
			<div className="space-y-6">
				<Skeleton className="h-6 w-32" />
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Skeleton className="h-10 w-full" />
					<Skeleton className="h-10 w-full" />
				</div>
				<Skeleton className="h-6 w-32" />
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Skeleton className="h-10 w-full" />
					<Skeleton className="h-10 w-full" />
				</div>
			</div>
		</CardContent>
	</Card>
);

interface ProviderUpdateWizardProps {
	company: ExtendedCompany;
	request?: ExtendedWarrantyRequest;
	quote?: QuoteWithMessages;
	onCancel?: () => void;
	onSuccess?: (updatedRequest?: any) => void;
	initialUpdateType?: "authorization";
}

export function ProviderUpdateWizard({
	company,
	request,
	quote,
	onCancel,
	onSuccess
}: ProviderUpdateWizardProps) {
	const { data: session } = useSession();

	const {
		form,
		isSubmitting,
		currentStep,
		currentStepComponent,
		isFirstStep,
		isLastStep,
		handleNext,
		handlePrevious,
		handleCancel,
		handleSubmit
	} = useProviderUpdateWizard({ company, request, quote, onCancel, onSuccess });

	if (!session?.user || !company) {
		return <ProviderUpdateWizardSkeleton />;
	}

	return (
		<>
			<form
				className="space-y-4"
				onSubmit={(e) => {
					e.preventDefault();
					handleSubmit();
				}}
			>
				<DialogHeader>
					<DialogTitle>{currentStep?.title}</DialogTitle>
					<DialogDescription>{currentStep?.description}</DialogDescription>
				</DialogHeader>

				{currentStepComponent}

				{/* Navigation */}
				<div className="flex justify-between mt-6 pt-4 border-t">
					<div className="flex gap-2">
						<Button
							type="button"
							variant="outline"
							onClick={handleCancel}
							disabled={isSubmitting}
						>
							Cancel
						</Button>
						{!isFirstStep && (
							<Button
								type="button"
								variant="outline"
								onClick={handlePrevious}
								disabled={isSubmitting}
							>
								Previous
							</Button>
						)}
					</div>

					<div>
						{!isLastStep ? (
							<Button
								type="button"
								onClick={handleNext}
								disabled={isSubmitting}
							>
								Next
							</Button>
						) : (
							<Button
								type="button"
								onClick={handleSubmit}
								disabled={isSubmitting}
							>
								{isSubmitting ? "Submitting..." : "Submit"}
							</Button>
						)}
					</div>
				</div>
			</form>
		</>
	);
}
