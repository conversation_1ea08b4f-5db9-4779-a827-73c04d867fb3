"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ExtendedWarrantyRequest, WarrantyAttachment } from "@/types/warranty";
import { Loader2, Paperclip, X } from "lucide-react";
import { useRef, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";
import { ProviderUpdateSchema } from "../provider-update-schema";

// Define the possible form data types based on update type
type WarrantyAuthorizationFormData = z.infer<typeof ProviderUpdateSchema>;

interface EstimatedHoursStepProps {
	form: UseFormReturn<WarrantyAuthorizationFormData>;
	request: ExtendedWarrantyRequest;
	updateType?: "progress" | "authorization" | "upload_attachments" | "complete";
	validationAttempted?: boolean;
}

export function EstimatedHoursStep({
	form,
	request,
	validationAttempted = false
}: EstimatedHoursStepProps) {
	const { register, formState, setValue, watch } = form;
	const [isUploading, setIsUploading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const attachments = watch("attachments") || [];

	const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const files = e.target.files;
		if (!files || files.length === 0) return;

		setIsUploading(true);
		try {
			for (let i = 0; i < files.length; i++) {
				const file = files[i];
				const timestamp = new Date().getTime();
				const fileName = `${timestamp}-${file.name}`;
				const formData = new FormData();
				formData.append("file", file);
				formData.append("fileName", fileName);
				formData.append("path", `warranty-requests/${request.id}`);

				const response = await fetch("/api/storage", {
					method: "POST",
					body: formData
				});

				if (!response.ok) {
					throw new Error("Failed to upload file");
				}

				const data = await response.json();
				const newAttachment: WarrantyAttachment = {
					id: data.key,
					type: file.type.startsWith("image/") ? "image" : "document",
					title: file.name,
					url: data.url,
					required: false,
					component_id: null,
					completed: false,
					status_update: true
				};

				setValue("attachments", [...attachments, newAttachment]);
			}
		} catch (error) {
			console.error("Error uploading file:", error);
			toast.error("Failed to upload file");
		} finally {
			setIsUploading(false);
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}
		}
	};

	const removeAttachment = (id: string) => {
		const updatedAttachments = attachments.filter(
			(attachment: WarrantyAttachment) => attachment.id !== id
		);
		setValue("attachments", updatedAttachments);
	};

	return (
		<div className="space-y-6">
			{/* Cost Estimates */}
			<div>
				<div className="font-semibold mb-4">Cost Estimates</div>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<Input
						type="number"
						step="0.1"
						value={request.approved_hours}
						label={"Pre-Approved Hours"}
						disabled={true}
						className="bg-gray-200"
					/>
					<Input
						type="number"
						step="0.1"
						{...register("estimated_hours", { valueAsNumber: true })}
						error={
							formState.errors.estimated_hours?.message
								? String(formState.errors.estimated_hours.message)
								: null
						}
						placeholder="e.g. 2.5"
						label={"Estimated Hours *"}
						required
					/>
				</div>
				{watch("estimated_hours") > (request.approved_hours || 0) && (
					<div className="text-sm text-amber-600 bg-amber-50 p-2 rounded-md mt-2">
						📝 <strong>Note:</strong> Since you're requesting additional hours
						above the pre-approved amount, please provide a reason in the Update
						Notes field below.
					</div>
				)}

				<div className="space-y-4 pt-2">
					<div className="flex flex-col gap-2">
						<Label htmlFor="update_notes">
							Update Notes
							{watch("estimated_hours") > (request.approved_hours || 0) && (
								<span className="text-red-500 ml-1">*</span>
							)}
						</Label>
						<Textarea
							id="update_notes"
							{...register("update_notes")}
							placeholder={
								watch("estimated_hours") > (request.approved_hours || 0)
									? "Please provide a reason for requesting additional hours..."
									: "Enter any notes related to this estimate..."
							}
							className={`min-h-[100px] ${
								formState.errors.update_notes
									? "border-red-500 focus:border-red-500"
									: ""
							}`}
						/>
						{formState.errors.update_notes && (
							<p className="text-sm text-red-500">
								{formState.errors.update_notes.message}
							</p>
						)}
					</div>
				</div>

				<div className="space-y-4">
					<div>
						<Label>Attachments</Label>
						<div className="mt-2">
							<Input
								type="file"
								ref={fileInputRef}
								onChange={handleFileChange}
								multiple
								className="hidden"
								accept="*"
							/>
							<Button
								type="button"
								variant="outline"
								onClick={() => fileInputRef.current?.click()}
								disabled={isUploading}
								className="w-full"
							>
								{isUploading ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										Uploading...
									</>
								) : (
									<>
										<Paperclip className="mr-2 h-4 w-4" />
										Attach Files and Photos
									</>
								)}
							</Button>
						</div>

						{attachments.length > 0 && (
							<div className="mt-4 space-y-2">
								{attachments
									.filter((a) => a.type !== "form")
									.map((attachment: WarrantyAttachment) => (
										<div
											key={attachment.id}
											className="flex items-center justify-between p-2 bg-gray-50 rounded-md"
										>
											<div className="flex items-center space-x-2">
												<Paperclip className="h-4 w-4 text-gray-500" />
												<span className="text-sm truncate max-w-[300px]">
													{attachment.title}
												</span>
											</div>
											<Button
												type="button"
												variant="ghost"
												size="sm"
												onClick={() => removeAttachment(attachment.id)}
												className="h-8 w-8 p-0"
											>
												<X className="h-4 w-4" />
											</Button>
										</div>
									))}
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
