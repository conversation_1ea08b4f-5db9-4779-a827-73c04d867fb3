"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

export const ReturnRequestedSchema = z.object({
	// Service Request Details
	return_details: z
		.object({
			height: z
				.number({
					required_error: "Height is required",
					invalid_type_error: "Please enter a valid number"
				})
				.min(1, "Height must have a non-zero value"),
			width: z
				.number({
					required_error: "Width is required",
					invalid_type_error: "Please enter a valid number"
				})
				.min(1, "Width must have a non-zero value"),
			depth: z
				.number({
					required_error: "Depth is required",
					invalid_type_error: "Please enter a valid number"
				})
				.min(1, "Depth must have a non-zero value"),
			weight: z
				.number({
					required_error: "Weight is required",
					invalid_type_error: "Please enter a valid number"
				})
				.min(1, "Weight must have a non-zero value")
		})
		.optional()
		.nullable()
});

type ReturnRequestedStepData = z.infer<typeof ReturnRequestedSchema>;

interface ReturnRequestedStepProps {
	form: UseFormReturn<ReturnRequestedStepData>;
	request: ExtendedWarrantyRequest;
	validationAttempted?: boolean;
}

export function ReturnRequestedStep({
	form,
	request,
	validationAttempted = false
}: ReturnRequestedStepProps) {
	const { register, formState, watch } = form;

	return (
		<div className="space-y-6">
			{/* Return Details */}
			<div>
				<div className="font-semibold mb-4">Return Details</div>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="flex flex-col gap-2">
						<Label htmlFor="height">Height (inches)</Label>
						<Input
							type="number"
							step="0.1"
							{...register("return_details.height", { valueAsNumber: true })}
							error={String(
								formState.errors.return_details?.height?.message ||
									(validationAttempted && !watch("return_details.height")
										? "Height is required"
										: "")
							)}
							placeholder="Enter height"
							required
						/>
					</div>

					<div className="flex flex-col gap-2">
						<Label htmlFor="width">Width (inches)</Label>
						<Input
							type="number"
							step="0.1"
							{...register("return_details.width", { valueAsNumber: true })}
							error={String(
								formState.errors.return_details?.width?.message ||
									(validationAttempted && !watch("return_details.width")
										? "Width is required"
										: "")
							)}
							placeholder="Enter width"
							required
						/>
					</div>

					<div className="flex flex-col gap-2">
						<Label htmlFor="depth">Depth (inches)</Label>
						<Input
							type="number"
							step="0.1"
							{...register("return_details.depth", { valueAsNumber: true })}
							error={String(
								formState.errors.return_details?.depth?.message ||
									(validationAttempted && !watch("return_details.depth")
										? "Depth is required"
										: "")
							)}
							placeholder="Enter depth"
							required
						/>
					</div>

					<div className="flex flex-col gap-2">
						<Label htmlFor="weight">Weight (lbs)</Label>
						<Input
							type="number"
							step="0.1"
							{...register("return_details.weight", { valueAsNumber: true })}
							error={String(
								formState.errors.return_details?.weight?.message ||
									(validationAttempted && !watch("return_details.weight")
										? "Weight is required"
										: "")
							)}
							placeholder="Enter weight"
							required
						/>
					</div>
				</div>
			</div>
		</div>
	);
}
