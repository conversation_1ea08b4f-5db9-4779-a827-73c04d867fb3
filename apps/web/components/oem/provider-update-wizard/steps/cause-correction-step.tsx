"use client";

import { Textarea } from "@/components/ui/textarea";
import { ExtendedCompany } from "@/types/warranty";
import { useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { ProviderUpdateSchema } from "../provider-update-schema";

// Define the possible form data types based on update type
type WarrantyAuthorizationFormData = z.infer<typeof ProviderUpdateSchema>;

interface CauseCorrectionStepProps {
	form: UseFormReturn<WarrantyAuthorizationFormData>;
	company: ExtendedCompany;
	validationAttempted?: boolean;
}

export function CauseCorrectionStep({
	company,
	form,
	validationAttempted = false
}: CauseCorrectionStepProps) {
	const { register, formState, setValue, watch } = form;

	// Watch the component_id field to sync with our internal dropdown state
	const selectedComponentId = watch("component_id");

	// Memoize component options to prevent unnecessary re-renders
	const componentOptions = useMemo(
		() =>
			company.components
				.slice()
				.sort((a, b) => {
					const typeCompare = a.type.localeCompare(b.type);
					if (typeCompare !== 0) return typeCompare;
					return a.manufacturer.localeCompare(b.manufacturer);
				})
				.map((component) => ({
					id: component.id,
					value: `${component.type} - ${component.manufacturer}`,
					label: `${component.type} - ${component.manufacturer}`
				})),
		[company?.components]
	);

	// Handle component selection
	const handleComponentChange = (componentId: string) => {
		setValue("component_id", componentId);
		const selectedComponent = company.components.find(
			(c) => c.id === componentId
		);
		if (selectedComponent) {
			setValue("component_id", selectedComponent.id);
		}
	};

	// Force re-render when errors change
	const hasErrors = Object.keys(formState.errors).length > 0;
	return (
		<div className="space-y-6">
			{/* Service Request Details */}
			<div>
				<div className="font-semibold mb-4">Component</div>
				<div className="space-y-2">
					<div className="flex">
						<select
							value={selectedComponentId || ""}
							onChange={(e) => handleComponentChange(e.target.value)}
							aria-label="Component"
							className={`flex-1 px-2 py-1 border rounded-md ${
								formState.errors.component_id
									? "border-red-500"
									: "focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1"
							}`}
						>
							<option value="">Select a component</option>
							{componentOptions.map((option) => (
								<option key={option.id} value={option.id}>
									{option.label}
								</option>
							))}
						</select>
					</div>
					{(formState.errors.component_id ||
						(validationAttempted && !selectedComponentId)) && (
						<p className="text-sm text-red-500">
							{formState.errors.component_id?.message ||
								"Please select a component"}
						</p>
					)}
				</div>
				<div>
					<div className="font-semibold pt-4 mb-4">Cause and Correction</div>
					{/* <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> */}
					<div>
						<div className="space-y-2">
							<Textarea
								{...register("cause")}
								error={String(
									formState.errors.cause?.message ||
										(validationAttempted && !watch("cause")
											? "Cause is required"
											: "")
								)}
								placeholder="What do you believe caused this issue?"
								rows={2}
								required
								label={"Cause"}
							/>
						</div>
						<div className="pt-2">
							<Textarea
								{...register("correction")}
								error={String(
									formState.errors.correction?.message ||
										(validationAttempted && !watch("correction")
											? "Suggested correction is required"
											: "")
								)}
								placeholder="How do you think this should be fixed?"
								rows={2}
								required
								label={"Suggested Correction"}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
