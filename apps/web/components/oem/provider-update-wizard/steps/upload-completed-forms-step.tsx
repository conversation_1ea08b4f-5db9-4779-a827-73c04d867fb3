import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ExtendedWarrantyRequest, WarrantyAttachment } from "@/types/warranty";
import { Eye, FileText, ImageIcon, Loader2, Upload } from "lucide-react";
import React, { useRef, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";
import { ProviderUpdateSchema } from "../provider-update-schema";

// Define the possible form data types based on update type
type WarrantyAuthorizationFormData = z.infer<typeof ProviderUpdateSchema>;

interface UpdateCompletedFormsStepProps {
	request: ExtendedWarrantyRequest;
	form: UseFormReturn<WarrantyAuthorizationFormData>;
	updateType?: "complete";
	validationAttempted?: boolean;
}

export function UpdateCompletedFormsStep({
	request,
	form,
	validationAttempted = false
}: UpdateCompletedFormsStepProps) {
	const [isUploading, setIsUploading] = useState(false);
	const [uploadingForAttachment, setUploadingForAttachment] = useState<
		string | null
	>(null);
	const [updatedRequestAttachments, setUpdatedRequestAttachments] = useState<
		WarrantyAttachment[]
	>(request.attachments || []);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const { register, formState, setValue, watch } = form;

	const attachments = watch("attachments") || [];
	const skipAttachments = watch("skip_attachments") || false;

	// Get all required attachments from both the form and the request
	const getRequiredAttachments = (): WarrantyAttachment[] => {
		const formAttachments = attachments.filter(
			(att: WarrantyAttachment) => att.required
		);
		const requestAttachments =
			updatedRequestAttachments.filter(
				(att: WarrantyAttachment) => att.required
			) || [];

		// Combine and deduplicate by title
		const allAttachments = [...formAttachments, ...requestAttachments];
		const uniqueAttachments = allAttachments.filter(
			(attachment, index, self) =>
				index === self.findIndex((a) => a.title === attachment.title)
		);

		return uniqueAttachments as WarrantyAttachment[];
	};

	const getFileIcon = (type: string) => {
		const lowerType = type.toLowerCase();
		if (
			lowerType.includes("image") ||
			lowerType.includes("jpg") ||
			lowerType.includes("png") ||
			lowerType.includes("jpeg")
		) {
			return <ImageIcon className="w-4 h-4" />;
		}
		return <FileText className="w-4 h-4" />;
	};

	const formatUserName = (
		user: { first_name: string | null; last_name: string | null } | undefined
	) => {
		if (!user || (!user.first_name && !user.last_name)) {
			return "Unknown User";
		}
		return `${user.first_name || ""} ${user.last_name || ""}`.trim();
	};

	const formatDate = (date: Date | undefined) => {
		if (!date) return "";
		return new Date(date).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		});
	};

	const handleDownload = (attachment: WarrantyAttachment) => {
		if (attachment.url) {
			const link = document.createElement("a");
			link.href = attachment.url;
			link.download = attachment.title || "attachment";
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		}
	};

	const handlePreview = (attachment: WarrantyAttachment) => {
		if (attachment.url) {
			window.open(attachment.url, "_blank");
		}
	};

	const handleFileChange = async (
		e: React.ChangeEvent<HTMLInputElement>,
		originalAttachment?: WarrantyAttachment
	) => {
		const files = e.target.files;
		if (!files || files.length === 0) return;

		setIsUploading(true);
		setUploadingForAttachment(originalAttachment?.title || null);

		try {
			for (let i = 0; i < files.length; i++) {
				const file = files[i];
				const timestamp = new Date().getTime();
				const fileName = `${timestamp}-${file.name}`;
				const formData = new FormData();
				formData.append("file", file);
				formData.append("fileName", fileName);
				formData.append("path", `warranty-requests/${request.id}`);

				const response = await fetch("/api/storage", {
					method: "POST",
					body: formData
				});

				if (!response.ok) {
					throw new Error("Failed to upload file");
				}

				const data = await response.json();

				if (originalAttachment) {
					// Replace the original attachment with the completed version
					const updatedAttachments = attachments.map(
						(att: WarrantyAttachment) => {
							if (att.title === originalAttachment.title) {
								return {
									...att,
									id: data.key,
									component_id: originalAttachment.component_id,
									url: data.url,
									required: true,
									completed: true,
									type: "form",
									title: originalAttachment.title
								};
							}
							return att;
						}
					);

					// Set the form value with validation to ensure the form recognizes the update
					setValue("attachments", updatedAttachments, {
						shouldValidate: true,
						shouldTouch: true,
						shouldDirty: true
					});

					// Also update the request attachments if they exist
					const newRequestAttachments = updatedRequestAttachments.map(
						(att: WarrantyAttachment) => {
							if (att.title === originalAttachment.title) {
								return {
									...att,
									id: data.key,
									component_id: originalAttachment.component_id,
									url: data.url,
									required: true,
									type: "form",
									completed: true,
									title: originalAttachment.title
								};
							}
							return att;
						}
					);
					setUpdatedRequestAttachments(newRequestAttachments);

					toast.success(
						`Completed form uploaded for ${originalAttachment.title}`
					);
				} else {
					// Add new attachment
					const newAttachment: WarrantyAttachment = {
						id: data.key,
						type: file.type.startsWith("image/") ? "image" : "document",
						title: file.name,
						url: data.url
					};
					setValue("attachments", [...attachments, newAttachment]);
				}
			}
		} catch (error) {
			console.error("Error uploading file:", error);
			toast.error("Failed to upload file");
		} finally {
			setIsUploading(false);
			setUploadingForAttachment(null);
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}
		}
	};

	const handleUploadForAttachment = (attachment: WarrantyAttachment) => {
		if (fileInputRef.current) {
			fileInputRef.current.onchange = (e) =>
				handleFileChange(
					e as unknown as React.ChangeEvent<HTMLInputElement>,
					attachment
				);
			fileInputRef.current.click();
		}
	};

	const requiredAttachments = getRequiredAttachments();

	// Check if all required attachments are completed
	const incompleteRequiredAttachments = requiredAttachments.filter(
		(attachment) => attachment.required && !attachment.completed
	);

	const hasIncompleteRequiredAttachments =
		incompleteRequiredAttachments.length > 0 && !skipAttachments;

	// const hasIncompleteRequiredAttachments = false;

	// Set form error for attachments if validation is attempted and there are incomplete required attachments
	React.useEffect(() => {
		if (validationAttempted && hasIncompleteRequiredAttachments) {
			setValue("attachments", attachments, {
				shouldValidate: true,
				shouldTouch: true,
				shouldDirty: true
			});
		}
	}, [
		validationAttempted,
		hasIncompleteRequiredAttachments,
		attachments,
		setValue
	]);

	return (
		<div className="space-y-6">
			{/* Summary of incomplete required attachments */}
			{validationAttempted && hasIncompleteRequiredAttachments && (
				<div className="p-4 bg-red-50 border border-red-200 rounded-md">
					<p className="text-sm text-red-800 font-medium mb-2">
						⚠️ Required forms must be completed before proceeding:
					</p>
					<ul className="text-sm text-red-700 space-y-1">
						{incompleteRequiredAttachments.map((attachment, index) => (
							<li key={attachment.id || `${attachment.title}-${index}`}>
								• {attachment.title}
							</li>
						))}
					</ul>
				</div>
			)}

			{/* Skip attachments checkbox */}
			<div className="flex items-start space-x-3 p-4 bg-amber-50 border border-amber-200 rounded-md">
				<div className="flex-shrink-0">
					<input
						type="checkbox"
						id="skip_attachments"
						aria-label="Skip attachment upload step"
						{...register("skip_attachments")}
						className="mt-0.5 h-4 w-4 rounded border-gray-300 text-amber-600 focus:ring-amber-500 cursor-pointer"
					/>
				</div>
				<div className="space-y-1 flex-1">
					<Label
						htmlFor="skip_attachments"
						className="text-sm font-medium text-amber-900 cursor-pointer select-none"
					>
						Skip this step
					</Label>
					<p className="text-sm text-amber-800 select-none">
						I acknowledge that by skipping this step, the OEM support team may
						require these forms to be submitted before authorization is
						approved.
					</p>
				</div>
			</div>

			{/* Only show the forms section if not skipping */}
			{!skipAttachments && (
				<div className="space-y-4">
					<div>
						<Label>Required OEM Forms</Label>
						{requiredAttachments.length > 0 ? (
							<div className="mt-4 space-y-2">
								{requiredAttachments.map(
									(attachment: WarrantyAttachment, index: number) => (
										<div
											key={attachment.id || `${attachment.title}-${index}`}
											className={`p-4 ${index % 2 === 0 ? "bg-white" : "bg-slate-25"} hover:bg-slate-50 transition-colors border rounded-lg ${
												validationAttempted &&
												attachment.required &&
												!attachment.completed
													? "border-red-300 bg-red-25"
													: "border-slate-200"
											}`}
										>
											<div className="flex items-center justify-between">
												<div className="flex items-center gap-3 flex-1 min-w-0">
													<div className="flex-shrink-0 text-slate-500">
														{getFileIcon(attachment.type)}
													</div>
													<div className="flex-1 min-w-0">
														<div className="text-sm font-medium text-slate-900 truncate">
															{attachment.title}
														</div>
														<div className="text-xs text-slate-500 flex items-center gap-2 flex-wrap">
															<span className="capitalize">
																{attachment.type.replace("_", " ")}
															</span>
															{attachment.required && !attachment.completed && (
																<span
																	className={`px-2 py-0.5 rounded text-xs font-medium ${
																		validationAttempted
																			? "bg-red-200 text-red-900 animate-pulse"
																			: "bg-red-100 text-red-800"
																	}`}
																>
																	{validationAttempted
																		? "⚠️ Required"
																		: "Required"}
																</span>
															)}
															{attachment.completed && (
																<span className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs font-medium">
																	Completed
																</span>
															)}
															{attachment.component_id && (
																<span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs font-medium">
																	Component
																</span>
															)}
														</div>
													</div>
												</div>
												<div className="flex flex-col gap-2 flex-shrink-0">
													<Button
														type="button"
														variant="outline"
														size="sm"
														onClick={() => handlePreview(attachment)}
														className="flex items-center gap-2"
													>
														<Eye className="w-4 h-4" />
														Preview
													</Button>
													<Button
														type="button"
														variant="outline"
														size="sm"
														onClick={() =>
															handleUploadForAttachment(attachment)
														}
														disabled={
															isUploading &&
															uploadingForAttachment === attachment.title
														}
														className="flex items-center gap-2"
													>
														{isUploading &&
														uploadingForAttachment === attachment.title ? (
															<Loader2 className="w-4 h-4 animate-spin" />
														) : (
															<Upload className="w-4 h-4" />
														)}
														Update
													</Button>
												</div>
											</div>
										</div>
									)
								)}
							</div>
						) : (
							<div className="mt-4 p-4 bg-slate-50 border border-slate-200 rounded-lg">
								<p className="text-sm text-slate-500 text-center">
									No required forms found for this warranty request.
								</p>
							</div>
						)}

						{/* Hidden file input for uploads */}
						<Input
							type="file"
							ref={fileInputRef}
							className="hidden"
							accept="image/*,.pdf,.doc,.docx"
						/>
					</div>
				</div>
			)}
		</div>
	);
}
