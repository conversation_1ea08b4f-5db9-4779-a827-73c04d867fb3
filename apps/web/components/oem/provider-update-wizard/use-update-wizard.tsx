"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import type { z } from "zod";

import type { QuoteWithMessages } from "@/app/(main)/provider/(dashboard)/jobs/[id]/types";
import type {
	ExtendedCompany,
	ExtendedWarrantyRequest
} from "@/types/warranty";

import { ProviderUpdateSchema } from "./provider-update-schema";
import { CauseCorrectionStep } from "./steps/cause-correction-step";
import { EstimatedHoursStep } from "./steps/estimated-hours-step";
import { ReturnRequestedStep } from "./steps/return-requested-step";
import { UpdateCompletedFormsStep } from "./steps/upload-completed-forms-step";

// Schema-derived form data type
type ProviderUpdateFormData = z.infer<typeof ProviderUpdateSchema>;

// Wizard step metadata
const STEPS = [
	{
		id: 0,
		title: "Cause and Correction",
		description: "Update (or confirm) the results of your diagnosis",
		fieldsToValidate: ["cause", "correction", "component_id"]
	},
	{
		id: 1,
		title: "Update Estimate",
		description:
			"Update the estimated hours to complete this warranty service.",
		fieldsToValidate: ["estimated_hours"]
	},
	{
		id: 2,
		title: "Upload OEM Forms",
		description:
			"Upload completed warranty diagnostic forms required by the OEM.",
		fieldsToValidate: ["attachments"]
	},
	{
		id: 3,
		title: "Return Component",
		description:
			"Please enter the dimensions and weight of the component to be returned. OEM will use this information to arrange shipping and send a return label via email.",
		fieldsToValidate: ["return_details"]
	}
] as const;

export interface UseProviderUpdateWizardArgs {
	company: ExtendedCompany;
	request?: ExtendedWarrantyRequest;
	quote?: QuoteWithMessages;
	onCancel?: () => void;
	onSuccess?: (updatedRequest?: any) => void;
}

export function useProviderUpdateWizard({
	company,
	request,
	quote,
	onCancel,
	onSuccess
}: UseProviderUpdateWizardArgs) {
	const router = useRouter();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [currentStepId, setCurrentStepId] = useState(0);
	const [validationAttempted, setValidationAttempted] = useState(false);

	// Default values built from the warranty request context
	const getDefaultValues = useCallback((): ProviderUpdateFormData => {
		const baseValues = {
			update_notes: "",
			attachments: [] as any[],
			skip_attachments: false,
			event_type: undefined as any
		} as ProviderUpdateFormData;

		// Find the latest TECHNICIAN_UPDATED event with "Return details updated" message
		const getLatestReturnDetails = () => {
			if (!request?.timeline_updates || request.timeline_updates.length === 0) {
				return { height: 0, width: 0, depth: 0, weight: 0 };
			}

			const returnUpdates = request.timeline_updates
				.filter(
					(update) =>
						update.event_type === "TECHNICIAN_UPDATED" &&
						update.details?.notes === "Return details updated"
				)
				.sort(
					(a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
				);

			if (
				returnUpdates.length > 0 &&
				returnUpdates[0].details?.return_details
			) {
				return returnUpdates[0].details.return_details;
			}

			return { height: 0, width: 0, depth: 0, weight: 0 };
		};

		return {
			...baseValues,
			component_id: request?.component?.id || "",
			cause: request?.cause || "",
			correction: request?.correction || "",
			estimated_hours: request?.estimated_hours ?? request?.approved_hours,
			return_details: getLatestReturnDetails(),
			attachments: (request?.attachments as any) || []
		} as ProviderUpdateFormData;
	}, [request]);

	const form = useForm<ProviderUpdateFormData>({
		resolver: zodResolver(ProviderUpdateSchema),
		mode: "onChange",
		defaultValues: getDefaultValues()
	});

	// Dynamic steps based on request state
	const stepArray = useMemo(() => {
		const steps: any[] = [STEPS[0], STEPS[1]];
		if (request?.attachments?.some((att) => att.required)) {
			steps.push(STEPS[2]);
		}
		if (request?.requires_return) {
			steps.push(STEPS[3]);
		}
		return steps;
	}, [request]);

	// Initialize current step
	useEffect(() => {
		if (stepArray.length > 0) {
			setCurrentStepId(stepArray[0].id);
		}
	}, [stepArray]);

	const validateCurrentStep = useCallback(async () => {
		const currentStep = stepArray.find((step) => step.id === currentStepId);
		const fieldsToValidate = currentStep?.fieldsToValidate ?? [];

		// Ensure errors display by touching fields
		fieldsToValidate.forEach((field) => {
			form.setValue(field as any, form.getValues(field as any), {
				shouldTouch: true
			});
		});

		// Extra validation for hours justification
		if (currentStepId === 1) {
			const estimatedHours = form.getValues("estimated_hours");
			const updateNotes = form.getValues("update_notes");

			if (
				estimatedHours > (request?.approved_hours ?? 0) &&
				(!updateNotes || updateNotes.trim() === "")
			) {
				form.setError("update_notes" as any, {
					type: "manual",
					message:
						"If requesting additional hours, please provide a reason for the additional time."
				});
				return false;
			} else {
				// Clear the error if validation passes
				form.clearErrors("update_notes" as any);
			}
		}

		// Extra validation for return details
		if (currentStepId === 3) {
			const returnDetails = form.getValues("return_details");
			if (
				!returnDetails ||
				returnDetails.height === undefined ||
				returnDetails.width === undefined ||
				returnDetails.depth === undefined ||
				returnDetails.weight === undefined
			) {
				form.setError("return_details" as any, {
					type: "manual",
					message: "Please enter all return details"
				});
				return false;
			}
		}

		return await form.trigger(fieldsToValidate as any[]);
	}, [currentStepId, form, stepArray, request]);

	const nextStep = useCallback(() => {
		const index = stepArray.findIndex((step) => step.id === currentStepId);
		if (index < stepArray.length - 1) {
			setCurrentStepId(stepArray[index + 1].id);
		}
	}, [currentStepId, stepArray]);

	const previousStep = useCallback(() => {
		const index = stepArray.findIndex((step) => step.id === currentStepId);
		if (index > 0) {
			setCurrentStepId(stepArray[index - 1].id);
		}
	}, [currentStepId, stepArray]);

	const handleNext = useCallback(async () => {
		const isValid = await validateCurrentStep();
		setValidationAttempted(true);

		if (!isValid) {
			await form.trigger();
		}

		if (isValid) {
			nextStep();
			setValidationAttempted(false);
		}
	}, [validateCurrentStep, nextStep, form]);

	const handlePrevious = useCallback(() => {
		previousStep();
	}, [previousStep]);

	const handleCancel = useCallback(() => {
		if (onCancel) onCancel();
		else router.back();
	}, [onCancel, router]);

	const onSubmit = useCallback(
		async (data: ProviderUpdateFormData) => {
			try {
				setIsSubmitting(true);
				let response: Response | undefined;

				if ("component_id" in data && (data as any)?.component_id === "") {
					// Normalize empty component selections to null for API
					(data as any).component_id = null;
				}

				response = await fetch(
					`/api/warranty-requests/${request!.id}/request-authorization`,
					{
						method: "POST",
						headers: { "Content-Type": "application/json" },
						body: JSON.stringify(data)
					}
				);

				if (!response.ok) throw new Error("Failed to update warranty request");

				const updatedRequest = await response.json();
				form.reset();
				toast.success("Warranty request updated successfully");
				if (onSuccess) onSuccess(updatedRequest);
				else router.push("/dashboard");
			} catch (error) {
				console.error(error);
				toast.error("Failed to update warranty request. Please try again.");
			} finally {
				setIsSubmitting(false);
			}
		},
		[request, onSuccess, router, form, quote]
	);

	const handleSubmit = useCallback(async () => {
		setValidationAttempted(true);

		const currentStep = stepArray.find((step) => step.id === currentStepId);
		const fieldsToValidate = currentStep?.fieldsToValidate || [];

		const stepIsValid = await form.trigger(fieldsToValidate as any[]);

		if (!request?.requires_return) {
			form.setValue("return_details" as any, null);
		}

		const formIsValid = await form.trigger();

		if (!stepIsValid || !formIsValid) {
			toast.error("Please fix the validation errors before submitting");
			const firstErrorField = document.querySelector('[data-error="true"]');
			if (firstErrorField) {
				firstErrorField.scrollIntoView({ behavior: "smooth", block: "center" });
			}
			return;
		}

		// Step-specific validation after general checks
		if (currentStep) {
			const stepValidation = await validateCurrentStep();
			if (!stepValidation) {
				// Check for specific validation errors and show targeted messages
				const errors = form.formState.errors;
				if (errors.update_notes) {
					toast.error(
						errors.update_notes.message ||
							"Please provide notes for additional hours"
					);
				} else if (errors.estimated_hours) {
					toast.error(
						errors.estimated_hours.message ||
							"Please enter valid estimated hours"
					);
				} else {
					toast.error("Please complete all required fields for this step");
				}
				return;
			}
		}

		form.handleSubmit(onSubmit)();
	}, [form, onSubmit, stepArray, currentStepId, validateCurrentStep, request]);

	// Derived values for the UI layer
	const currentStep = useMemo(() => {
		return stepArray.find((step) => step.id === currentStepId);
	}, [stepArray, currentStepId]);

	const isFirstStep = currentStepId === stepArray[0]?.id;
	const isLastStep = currentStepId === stepArray[stepArray.length - 1]?.id;

	// Provide the current step component ready for rendering
	const currentStepComponent = useMemo(() => {
		switch (currentStepId) {
			case 0:
				return (
					<CauseCorrectionStep
						company={company}
						form={form as any}
						validationAttempted={validationAttempted}
					/>
				);
			case 1:
				return (
					<EstimatedHoursStep
						request={request!}
						form={form as any}
						validationAttempted={validationAttempted}
					/>
				);
			case 2:
				return (
					<UpdateCompletedFormsStep
						request={request!}
						form={form as any}
						validationAttempted={validationAttempted}
					/>
				);
			case 3:
				return (
					<ReturnRequestedStep
						request={request!}
						form={form as any}
						validationAttempted={validationAttempted}
					/>
				);
			default:
				return null;
		}
	}, [company, request, form, currentStepId, validationAttempted]);

	return {
		// form and state
		form,
		isSubmitting,
		currentStepId,
		validationAttempted,

		// derived
		stepArray,
		currentStep,
		isFirstStep,
		isLastStep,
		currentStepComponent,

		// handlers
		handleNext,
		handlePrevious,
		handleCancel,
		handleSubmit
	} as const;
}
