"use client";

import { useRouter } from "next/navigation";
import { memo, useEffect } from "react";

interface StepSuccessProps {
	jobId: string;
}

export const StepSuccess = memo(function StepSuccess({
	jobId
}: StepSuccessProps) {
	const router = useRouter();

	useEffect(() => {
		// Redirect to the Work Room after a short delay
		const timer = setTimeout(() => {
			router.push(`/service-requests/${jobId}`);
		}, 250); // Redirect after 250 ms
		return () => clearTimeout(timer); // Cleanup on unmount
	}, [jobId, router]);

	return (
		<div className="w-full space-y-6 py-4">
			<div className="bg-green-50 p-6 rounded-lg text-center">
				<div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						className="h-8 w-8 text-green-600"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M5 13l4 4L19 7"
						/>
					</svg>
				</div>
				<p className="text-base text-green-700">
					You will now be redirected to the Work Room for your Service Request.
				</p>
			</div>
		</div>
	);
});
