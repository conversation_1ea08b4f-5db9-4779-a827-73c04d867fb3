"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	Di<PERSON>Header,
	DialogTitle
} from "@/components/ui/dialog";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { memo, useCallback, useState } from "react";

import { EditContactInfo } from "@/components/oem/oem-walkthrough/edit-contact-info";
import { EditLocation } from "@/components/oem/oem-walkthrough/edit-location";
import { EditRVDetails } from "@/components/oem/oem-walkthrough/edit-rv-details";
import { Checkbox } from "@/components/ui/checkbox";
import { zodResolver } from "@hookform/resolvers/zod";
import { Edit2, FileText, MapPin, Truck, User } from "lucide-react";
import { useForm } from "react-hook-form";
import * as z from "zod";

const requestDetailsSchema = z.object({
	sms_opt_in: z.boolean().default(true)
});

type RequestDetailsFormData = z.infer<typeof requestDetailsSchema>;

interface StepRequestDetailsProps {
	warrantyRequest: ExtendedWarrantyRequest;
	onUpdateWarrantyRequest?: (
		request: ExtendedWarrantyRequest,
		createJob: boolean
	) => Promise<any>;
}

export const StepRequestDetails = memo(function StepRequestDetails({
	warrantyRequest,
	onUpdateWarrantyRequest
}: StepRequestDetailsProps) {
	const [showContactModal, setShowContactModal] = useState(false);
	const [showLocationModal, setShowLocationModal] = useState(false);
	const [showRVModal, setShowRVModal] = useState(false);

	const form = useForm<RequestDetailsFormData>({
		resolver: zodResolver(requestDetailsSchema),
		defaultValues: {
			sms_opt_in: true
		}
	});

	const { watch, register } = form;


	const handleEditCustomerInfo = useCallback(() => {
		setShowContactModal(true);
	}, []);

	const handleEditServiceLocation = useCallback(() => {
		setShowLocationModal(true);
	}, []);

	const handleEditRVInfo = useCallback(() => {
		setShowRVModal(true);
	}, []);

	const handleModalClose = useCallback(
		(modalType: "contact" | "location" | "rv") => {
			switch (modalType) {
				case "contact":
					setShowContactModal(false);
					break;
				case "location":
					setShowLocationModal(false);
					break;
				case "rv":
					setShowRVModal(false);
					break;
			}
		},
		[]
	);

	const handleContactUpdate = useCallback(
		async (updatedRequest: ExtendedWarrantyRequest, createJob: boolean) => {
			try {
				await onUpdateWarrantyRequest?.(updatedRequest, createJob);
				handleModalClose("contact");
			} catch (error) {
				console.error("Failed to update contact info:", error);
			}
		},
		[onUpdateWarrantyRequest, handleModalClose]
	);

	const handleLocationUpdate = useCallback(
		async (updatedRequest: ExtendedWarrantyRequest, createJob: boolean) => {
			try {
				await onUpdateWarrantyRequest?.(updatedRequest, createJob);
				handleModalClose("location");
			} catch (error) {
				console.error("Failed to update location:", error);
			}
		},
		[onUpdateWarrantyRequest, handleModalClose]
	);

	const handleRVUpdate = useCallback(
		async (updatedRequest: ExtendedWarrantyRequest, createJob: boolean) => {
			try {
				await onUpdateWarrantyRequest?.(updatedRequest, createJob);
				handleModalClose("rv");
			} catch (error) {
				console.error("Failed to update RV details:", error);
			}
		},
		[onUpdateWarrantyRequest, handleModalClose]
	);

	return (
		<div className="w-full space-y-3 py-0 px-4">
			{/* Compact Summary Cards */}
			<div className="grid grid-cols-1 gap-3">
				{/* Customer Info Card */}
				<div className="bg-white py-2 px-3 rounded-lg border">
					<div className="flex items-center justify-between mb-3">
						<div className="flex items-center gap-2">
							<User className="w-5 h-5 text-primary" />
							<h3 className="text-base font-semibold text-gray-900">
								Customer
							</h3>
						</div>
						<Button
							variant="ghost"
							size="sm"
							onClick={handleEditCustomerInfo}
							className="h-8 px-3 text-sm text-blue-600 hover:text-blue-800"
						>
							<Edit2 className="w-4 h-4 mr-1" />
							Edit
						</Button>
					</div>
					<div className="text-sm text-gray-600 space-y-2">
						<div className="flex justify-between">
							<span className="font-medium">Name:</span>
							<span>
								{warrantyRequest.first_name} {warrantyRequest.last_name}
							</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Email:</span>
							<span className="truncate ml-2">{warrantyRequest.email}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Phone:</span>
							<span>{warrantyRequest.phone}</span>
						</div>
					</div>
				</div>

				{/* Service Location Card */}
				<div className="bg-white py-2 px-3 rounded-lg border">
					<div className="flex items-center justify-between mb-3">
						<div className="flex items-center gap-2">
							<MapPin className="w-5 h-5 text-primary" />
							<h3 className="text-base font-semibold text-gray-900">
								Location
							</h3>
						</div>
						<Button
							variant="ghost"
							size="sm"
							onClick={handleEditServiceLocation}
							className="h-8 px-3 text-sm text-blue-600 hover:text-blue-800"
						>
							<Edit2 className="w-4 h-4 mr-1" />
							Edit
						</Button>
					</div>
					<div className="text-sm text-gray-600">
						<span className="font-medium">Address:</span>
						<p className="mt-2 text-gray-900">
							{warrantyRequest.location?.address || "Not specified"}
						</p>
					</div>
				</div>

				{/* RV Information Card */}
				<div className="bg-white py-2 px-3 rounded-lg border">
					<div className="flex items-center justify-between mb-3">
						<div className="flex items-center gap-2">
							<Truck className="w-5 h-5 text-primary" />
							<h3 className="text-base font-semibold text-gray-900">
								RV Details
							</h3>
						</div>
						<Button
							variant="ghost"
							size="sm"
							onClick={handleEditRVInfo}
							className="h-8 px-3 text-sm text-blue-600 hover:text-blue-800"
						>
							<Edit2 className="w-4 h-4 mr-1" />
							Edit
						</Button>
					</div>
					<div className="text-sm text-gray-600 space-y-2">
						<div className="flex justify-between">
							<span className="font-medium">VIN:</span>
							<span className="font-mono text-gray-900">
								{warrantyRequest.rv_vin}
							</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Make/Year/Model:</span>
							<span>
								{warrantyRequest.rv_make} {warrantyRequest.rv_year}{" "}
								{warrantyRequest.rv_model}
							</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Type:</span>
							<span>{warrantyRequest.rv_type}</span>
						</div>
					</div>
				</div>

				{/* Issue Description Card */}
				<div className="bg-white p-4 rounded-lg border">
					<div className="flex items-center gap-2 mb-3">
						<FileText className="w-5 h-5 text-primary" />
						<h3 className="text-base font-semibold text-gray-900">
							Issue Description
						</h3>
					</div>
					<div className="text-sm text-gray-900 bg-gray-50 p-3 rounded border max-h-24 overflow-y-auto">
						{warrantyRequest.complaint}
					</div>
				</div>

				{/* SMS Opt-in Field */}
				<div className="bg-white p-4 rounded-lg border">
					<div className="flex items-start space-x-3">
						<Checkbox
							id="sms_opt_in"
							checked={watch("sms_opt_in")}
							onCheckedChange={(checked) => {
								// Use register to handle the field
								const { onChange } = register("sms_opt_in");
								onChange({ target: { value: checked } });
							}}
						/>
						<div className="space-y-1">
							<label
								htmlFor="sms_opt_in"
								className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
							>
								SMS Communications
							</label>
							<p className="text-xs text-gray-500">
								I agree to receive SMS messages from RVHelp and service providers regarding my warranty request. Message & data rates may apply. Reply STOP to unsubscribe.
								<a href="/terms-and-conditions" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline ml-1">Terms & Conditions</a> and
								<a href="/privacy-policy" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline ml-1">Privacy Policy</a> apply.
							</p>
						</div>
					</div>
				</div>

			</div>

			{/* Contact Info Modal */}
			<Dialog
				open={showContactModal}
				onOpenChange={() => handleModalClose("contact")}
			>
				<DialogContent className="max-w-2xl">
					<DialogHeader>
						<DialogTitle>Edit Customer Information</DialogTitle>
					</DialogHeader>
					<EditContactInfo
						warrantyRequest={warrantyRequest}
						onUpdateWarrantyRequest={handleContactUpdate}
					/>
				</DialogContent>
			</Dialog>

			{/* Location Modal */}
			<Dialog
				open={showLocationModal}
				onOpenChange={() => handleModalClose("location")}
			>
				<DialogContent className="max-w-2xl">
					<DialogHeader>
						<DialogTitle>Edit Service Location</DialogTitle>
					</DialogHeader>
					<EditLocation
						warrantyRequest={warrantyRequest}
						onUpdateWarrantyRequest={handleLocationUpdate}
					/>
				</DialogContent>
			</Dialog>

			{/* RV Details Modal */}
			<Dialog open={showRVModal} onOpenChange={() => handleModalClose("rv")}>
				<DialogContent className="max-w-2xl">
					<DialogHeader>
						<DialogTitle>Edit RV Details</DialogTitle>
					</DialogHeader>
					<EditRVDetails
						warrantyRequest={warrantyRequest}
						onUpdateWarrantyRequest={handleRVUpdate}
					/>
				</DialogContent>
			</Dialog>
		</div>
	);
});
