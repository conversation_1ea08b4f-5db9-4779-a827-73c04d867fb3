"use client";
import { useEffect, useRef, memo } from "react";
import { CenteredPageLoader } from "@/components/Loader";
import { ExtendedWarrantyRequest } from "@/types/warranty";

interface StepProcessingProps {
  warrantyRequest: ExtendedWarrantyRequest;
  onUpdateWarrantyRequest?: (request: ExtendedWarrantyRequest, createJob: boolean) => Promise<any>;
}

export const StepProcessing = memo(function StepProcessing({
  warrantyRequest,
  onUpdateWarrantyRequest,
}: StepProcessingProps) {
  const hasSubmitted = useRef(false);

  useEffect(() => {
    if (!hasSubmitted.current && onUpdateWarrantyRequest) {
      hasSubmitted.current = true;
      onUpdateWarrantyRequest(warrantyRequest, true);
    }
  }, [warrantyRequest, onUpdateWarrantyRequest]);

  return (
    <div className="w-full max-w-lg space-y-6 py-4">
      <div className="bg-green-50 p-6 rounded-lg text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CenteredPageLoader />
        </div>
        <p className="text-green-700">
          You will be automatically redirected to the Work Room for your Service Request once processing is complete.
        </p>
      </div>
    </div>
  );
});
