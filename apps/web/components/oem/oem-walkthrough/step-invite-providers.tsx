"use client";

import InviteProviders, {
	InviteProvidersRef
} from "@/components/InviteProviders";
import { Skeleton } from "@/components/ui/skeleton";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { Job, Quote } from "@rvhelp/database";
import { forwardRef, memo, useImperativeHandle, useMemo, useRef } from "react";

type JobWithQuotes = Job & {
	quotes: Quote[];
};

interface StepInviteProvidersProps {
	warrantyRequest: ExtendedWarrantyRequest;
	onInvitedProviders: (updatedJob: JobWithQuotes) => Promise<void>;
	job?: JobWithQuotes;
	onSelectionChange?: (selectedProviders: Set<string>) => void;
}

export const StepInviteProviders = memo(
	forwardRef<InviteProvidersRef, StepInviteProvidersProps>(
		function StepInviteProviders(
			{
				warrantyRequest,
				job,
				onInvitedProviders: _onInvitedProviders,
				onSelectionChange: _onSelectionChange
			},
			ref
		) {
			const inviteProvidersRef = useRef<InviteProvidersRef>(null);
			// Memoize quotes array to prevent unnecessary refetches
			const memoizedQuotes = useMemo(() => job?.quotes || [], [job?.quotes]);

			// Expose the InviteProviders ref methods to parent
			useImperativeHandle(
				ref,
				() => ({
					getSelectedProviders: () =>
						inviteProvidersRef.current?.getSelectedProviders() || new Set(),
					clearSelection: () => inviteProvidersRef.current?.clearSelection(),
					refetch: () => inviteProvidersRef.current?.refetch(),
					getProviders: () => inviteProvidersRef.current?.getProviders() || []
				}),
				[]
			);

			// Check if job is loading
			if (!job) {
				return (
					<div className="w-full max-w-4xl p-4">
						<div className="mb-6">
							<Skeleton className="h-6 w-64 mb-2" />
							<Skeleton className="h-4 w-96" />
						</div>

						<div className="mb-4 flex gap-2 flex-wrap">
							<Skeleton className="h-8 w-32" />
							<Skeleton className="h-8 w-28" />
							<Skeleton className="h-8 w-36" />
						</div>

						<div className="py-4">
							<div className="flex items-center justify-between mb-4">
								<Skeleton className="h-4 w-48" />
							</div>

							<div className="space-y-3">
								{Array.from({ length: 3 }).map((_, i) => (
									<div
										key={i}
										className="flex items-start space-x-4 p-4 border rounded-lg"
									>
										<Skeleton className="h-5 w-5 rounded" />
										<Skeleton className="h-12 w-12 rounded-full" />
										<div className="flex-1 space-y-2">
											<Skeleton className="h-4 w-48" />
											<Skeleton className="h-3 w-32" />
											<Skeleton className="h-3 w-64" />
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				);
			}

			// Extract location data from warranty request
			const latitude = warrantyRequest.location?.latitude;
			const longitude = warrantyRequest.location?.longitude;

			// Check if we have the required location data
			if (!latitude || !longitude) {
				return (
					<div className="w-full max-w-lg p-4">
						<div className="text-center py-8">
							<p className="text-gray-500 mb-4">
								Location information is required to find nearby providers.
							</p>
							<p className="text-sm text-gray-400">
								Please ensure the warranty request includes valid location data.
							</p>
						</div>
					</div>
				);
			}

			return (
				<div className="w-full max-w-4xl py-0 px-4">
					<InviteProviders
						ref={inviteProvidersRef}
						category={job.category}
						latitude={latitude}
						longitude={longitude}
						quotes={memoizedQuotes}
						maxSelections={5}
						limit={5}
						height="400px"
						autoFetch={true}
						onSelectionChange={_onSelectionChange}
					/>
				</div>
			);
		}
	)
);
