"use client";

import React, {
	use<PERSON><PERSON>back,
	useEffect,
	useMemo,
	useRef,
	useState
} from "react";

import { StepInviteProviders } from "@/components/oem/oem-walkthrough/step-invite-providers";
import { StepLoading } from "@/components/oem/oem-walkthrough/step-loading";
import { StepLogin } from "@/components/oem/oem-walkthrough/step-login";
import { StepRegistration } from "@/components/oem/oem-walkthrough/step-registration";
import { StepRequestDetails } from "@/components/oem/oem-walkthrough/step-request-details";
import { StepSuccess } from "@/components/oem/oem-walkthrough/step-success";
import { StepWelcome } from "@/components/oem/oem-walkthrough/step-welcome";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle
} from "@/components/ui/card";

import ApplicationLogo from "@/components/ApplicationLogo";
import { InviteProvidersRef } from "@/components/InviteProviders";
import OEMLogo from "@/components/oem/oem-logo";
import { useAuth } from "@/lib/hooks/useAuth";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { Job, Quote } from "@rvhelp/database";
import { signOut } from "next-auth/react";

// Define Job with quotes relation
type JobWithQuotes = Job & {
	quotes: Quote[];
};

// Define the Step interface with optional dynamic function, onNext handler, allowPrevious, allowNext, and header
interface Step {
	id: number;
	title: string;
	description: string;
	allowPrevious?: boolean;
	allowNext?: boolean;
	header?: (warrantyRequest: ExtendedWarrantyRequest) => React.ReactNode;
	dynamicStep?: (warrantyRequest: ExtendedWarrantyRequest) => {
		title?: string;
		description?: string;
	};
	onNext?: (
		warrantyRequest: ExtendedWarrantyRequest,
		handleUpdateWarrantyRequest: (
			request: ExtendedWarrantyRequest,
			createJob: boolean
		) => Promise<any>
	) => Promise<void>;
	ignoreInStepCount?: boolean;
}

// Move steps array inside component to access state

export default function OEMWalkthrough({
	initialRequest,
	initialJob
}: {
	initialRequest: ExtendedWarrantyRequest | null;
	initialJob: JobWithQuotes | null;
}) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [warrantyRequest, setWarrantyRequest] =
		useState<ExtendedWarrantyRequest | null>(initialRequest);
	const [job, setJob] = useState<JobWithQuotes | null>(initialJob);
	const [userState, setUserState] = useState<{
		userExists: boolean;
		hasLoggedIn: boolean;
		needsPasswordSetup: boolean;
	} | null>(null);
	const [checkingUser, setCheckingUser] = useState(true);

	const { user, loading } = useAuth();

	// Create ref for StepInviteProviders to access selected providers
	const stepInviteProvidersRef = useRef<InviteProvidersRef>(null);

	// Check if user exists for warranty request email
	useEffect(() => {
		const checkUserExists = async () => {
			if (!warrantyRequest?.email) {
				console.log("No email found, skipping API call");
				setCheckingUser(false);
				return;
			}

			console.log(
				"Making API call to check user exists for:",
				warrantyRequest.email
			);
			try {
				const response = await fetch("/api/users/check-user-exists", {
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						email: warrantyRequest.email
					})
				});

				if (response.ok) {
					const data = await response.json();
					console.log("API Response data:", data);
					setUserState(data);
				} else {
					// If API fails, fallback to existing logic (check customer_id)
					setUserState({
						userExists: !!warrantyRequest.customer_id,
						hasLoggedIn: false, // If API failed, assume no login since we can't verify
						needsPasswordSetup: !!warrantyRequest.customer_id // If user exists, they need password setup
					});
				}
			} catch (error) {
				console.error("Error checking if user exists:", error);
				// Fallback to existing logic if check fails
				setUserState({
					userExists: !!warrantyRequest.customer_id,
					hasLoggedIn: false, // If API failed, assume no login since we can't verify
					needsPasswordSetup: !!warrantyRequest.customer_id // If user exists, they need password setup
				});
			} finally {
				setCheckingUser(false);
			}
		};

		checkUserExists();
	}, [warrantyRequest?.email, warrantyRequest?.customer_id]);

	const handleInvitedProviders = useCallback(
		async (updatedJob: JobWithQuotes) => {
			setJob(updatedJob);
		},
		[]
	);

	// Move invite providers logic up to parent component
	const handleUpdateQuotes = useCallback(
		async (selectedProviderIds: string[]) => {
			if (!job) {
				console.error("Cannot invite providers: job is null");
				throw new Error("Job not available");
			}

			if (selectedProviderIds.length === 0) {
				// No providers selected, just continue
				return;
			}

			try {
				const response = await fetch(`/api/jobs/${job.id}/invite`, {
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						providerIds: selectedProviderIds
					})
				});

				if (!response.ok) throw new Error("Failed to invite providers");

				await response.json();
				console.log(
					`Successfully invited ${selectedProviderIds.length} providers`
				);

				// Refresh job data to get updated quotes
				if (warrantyRequest?.job_id) {
					const jobResponse = await fetch(
						`/api/jobs/${warrantyRequest.job_id}`
					);
					if (jobResponse.ok) {
						const updatedJob = await jobResponse.json();
						handleInvitedProviders(updatedJob);
					}
				}
			} catch (error) {
				console.error("Error inviting providers:", error);
				throw error; // Re-throw to prevent navigation if invite fails
			}
		},
		[job, handleInvitedProviders, warrantyRequest?.job_id]
	);

	// Define steps array with access to component state
	const steps: Step[] = useMemo(
		() => [
			{
				id: 1,
				title: `${warrantyRequest.company.name}/RV Help Warranty Portal`,
				description: `Welcome to the ${warrantyRequest.company.name} / RV Help Warranty Service Portal`,
				allowPrevious: false,
				header: (warrantyRequest: ExtendedWarrantyRequest) => (
					<div className="space-y-6">
						{/* Logo Section */}
						<div className="flex items-end justify-center gap-6">
							<div className="flex flex-col items-center">
								<ApplicationLogo className="h-12 w-auto" />
							</div>
							<div className="h-8 w-px bg-gray-300 mb-4"></div>
							<div className="flex flex-col items-center">
								<OEMLogo company={warrantyRequest.company} />
							</div>
						</div>
					</div>
				)
			},
			{
				id: 2,
				title: `${warrantyRequest.company.name}/RV Help Warranty Portal`,
				description: "Where the service will be performed",
				allowPrevious: false,
				allowNext: false,
				dynamicStep: (warrantyRequest: ExtendedWarrantyRequest) => {
					// Use userState for more accurate routing
					if (!userState?.userExists) {
						// User doesn't exist - full registration
						return {
							title: `${warrantyRequest.company.name}/RV Help Warranty Portal Registration`,
							description:
								"Create your RV Help account to find providers near you to fulfill your warranty request"
						};
					} else if (userState.hasLoggedIn) {
						// User exists and has logged in before - show login
						return {
							title: `${warrantyRequest.company.name}/RV Help Warranty Portal Login`,
							description: "Sign in to your RV Help account"
						};
					} else if (userState.needsPasswordSetup) {
						// User exists but needs password setup
						return {
							title: `${warrantyRequest.company.name}/RV Help Warranty Portal Setup`,
							description:
								"Set your password to access your account and continue with your warranty request"
						};
					} else {
						// Fallback to login
						return {
							title: `${warrantyRequest.company.name}/RV Help Warranty Portal Login`,
							description: "Sign in to your RV Help account"
						};
					}
				}
			},
			{
				id: 3,
				title: `${warrantyRequest.company.name}/RV Help Warranty Request Details`,
				description: "Review the Warranty Request and update details as needed",
				allowPrevious: false,
				onNext: async (
					warrantyRequest: ExtendedWarrantyRequest,
					handleUpdateWarrantyRequest: (
						request: ExtendedWarrantyRequest,
						createJob: boolean
					) => Promise<any>
				) => {
					// Call handleUpdateWarrantyRequest with createJob: true (same as StepProcessing did)
					await handleUpdateWarrantyRequest(warrantyRequest, true);
				}
			},
			{
				id: 4,
				title: "Invite RV Help Providers",
				description:
					"Find and invite service providers near you who can complete your warranty work. You will be add additional providers later.",
				allowPrevious: true,
				onNext: async (_warrantyRequest: ExtendedWarrantyRequest) => {
					// Get selected providers from the step component and invite them
					const selectedProviders =
						stepInviteProvidersRef.current?.getSelectedProviders();
					if (selectedProviders && selectedProviders.size > 0) {
						await handleUpdateQuotes(Array.from(selectedProviders));
					}
				}
			},
			{
				id: 5,
				title: "Success",
				description: "Request submitted successfully",
				allowPrevious: false,
				dynamicStep: (warrantyRequest: ExtendedWarrantyRequest) => {
					const jobId = warrantyRequest?.job_id;
					return {
						title: "Success",
						description: jobId
							? `Job ${jobId} created successfully`
							: "Request submitted successfully"
					};
				},
				ignoreInStepCount: true
			}
		],
		[handleUpdateQuotes, warrantyRequest.company?.name, userState]
	);

	// Calculate steps that should be counted in progress
	const countableSteps = useMemo(
		() => steps.filter((step) => !step.ignoreInStepCount),
		[steps]
	);

	// Determine initial step based on warranty request state
	const getInitialStep = useCallback(() => {
		// If user is logged in and email matches warranty request email (case-insensitive)
		if (
			user?.id &&
			user.email?.toLowerCase() === warrantyRequest.email?.toLowerCase()
		) {
			// If job state is not null and has quotes, go directly to success (step 5)
			// If onboarding is completed, go directly to success (step 5)
			if (warrantyRequest?.onboarding_completed_at) {
				return 5;
			}
			if (job && job.quotes && job.quotes.length > 0) {
				return 5;
			}
			if (job) {
				return 4;
			}
			return 3;
		}

		// If userState is null (still checking), stay on loading
		if (userState === null) {
			return 0;
		}

		// Everyone starts at welcome step - we only use userState when they reach step 2
		return 1;
	}, [warrantyRequest, user?.id, user?.email, job, userState]);

	const [currentStep, setCurrentStep] = useState(0); // Start with 0 for loading
	const currentPath = useMemo(
		() => `/oem/${warrantyRequest.uuid}`,
		[warrantyRequest.uuid]
	);

	// Helper function to get step properties (static or dynamic)
	const getStepProperties = useCallback(
		(step: Step, warrantyRequest: ExtendedWarrantyRequest) => {
			if (step.dynamicStep && warrantyRequest) {
				const dynamicProps = step.dynamicStep(warrantyRequest);
				return {
					title: dynamicProps.title || step.title,
					description: dynamicProps.description || step.description
				};
			}
			return {
				title: step.title,
				description: step.description
			};
		},
		[]
	);

	// Memoize current step properties to avoid recalculation
	const currentStepProperties = useMemo(() => {
		if (currentStep === 0)
			return {
				title: "Loading",
				description: "Loading Warranty Request Details..."
			};
		return getStepProperties(steps[currentStep - 1], warrantyRequest);
	}, [currentStep, warrantyRequest, getStepProperties, steps]);

	useEffect(() => {
		if (loading) return;
		if (
			user &&
			user.email &&
			user.email.toLowerCase() !== warrantyRequest.email?.toLowerCase()
		) {
			// Log out if logged in user does not match warranty request email
			signOut({ callbackUrl: currentPath });
		}
	}, [user, loading, warrantyRequest.email, currentPath]);

	// Update current step when user data becomes available, warranty request changes, or job changes
	useEffect(() => {
		if (!loading && !checkingUser) {
			const newStep = getInitialStep();
			setCurrentStep(newStep);
		}
	}, [loading, checkingUser, getInitialStep]);

	// Additional effect to handle user authentication state changes
	useEffect(() => {
		if (
			!loading &&
			!checkingUser &&
			user?.id &&
			user.email?.toLowerCase() === warrantyRequest.email?.toLowerCase()
		) {
			// If user is logged in and email matches, ensure we're on the correct step
			const newStep = getInitialStep();
			if (currentStep !== newStep) {
				setCurrentStep(newStep);
			}
		}
	}, [
		loading,
		checkingUser,
		user?.id,
		user?.email,
		warrantyRequest.email,
		currentStep,
		getInitialStep
	]);

	const validateCurrentStep = useCallback(async () => {
		return currentStep > 0; // Placeholder for step validation logic
	}, [currentStep]);

	const handlePrevious = useCallback(() => {
		if (currentStep > 1) {
			setCurrentStep(currentStep - 1);
		}
	}, [currentStep]);

	const handleSubmit = useCallback(async () => {
		setIsSubmitting(true);
		// TODO: redirect
	}, []);

	const updateWarrantyRequest = useCallback(
		async (data: ExtendedWarrantyRequest, createJob: boolean = true) => {
			const warrantyUpdatePayload = {
				id: data.id,
				first_name: data.first_name,
				last_name: data.last_name,
				email: data.email,
				phone: data.phone,
				sms_opt_in: data.sms_opt_in,
				// contact_preference: data.contact_preference,
				location: data.location,
				rv_type: data.rv_type,
				rv_model: data.rv_model,
				rv_make: data.rv_make,
				rv_year: data.rv_year.toString(),
				onboarding_completed_at: data.onboarding_completed_at
					? data.onboarding_completed_at.toISOString()
					: undefined
			};

			const warrantyResponse = await fetch(
				`/api/warranty-requests/${data.id}?createJob=${createJob}`,
				{
					method: "PUT",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify(warrantyUpdatePayload)
				}
			);

			if (!warrantyResponse.ok) {
				const errorData = await warrantyResponse.json();
				throw new Error(errorData.error || "Failed to update warranty request");
			}

			return warrantyResponse.json();
		},
		[]
	);

	const handleUpdateWarrantyRequest = useCallback(
		async (request: ExtendedWarrantyRequest, createJob: boolean) => {
			try {
				const result = await updateWarrantyRequest(request, createJob);

				console.log("Warranty request updated:", result, result.job_id);
				setWarrantyRequest(result);
				return result;
			} catch (error) {
				console.error("Failed to update warranty request:", error);
				throw error;
			}
		},
		[updateWarrantyRequest]
	);

	const handleNext = useCallback(async () => {
		const isValid = await validateCurrentStep();
		if (isValid && currentStep < steps.length) {
			// Check if current step has an onNext function
			const currentStepConfig = steps[currentStep - 1];
			if (currentStepConfig?.onNext) {
				try {
					setIsSubmitting(true);
					await currentStepConfig.onNext(
						warrantyRequest,
						handleUpdateWarrantyRequest
					);
				} catch (error) {
					console.error("Error in step onNext:", error);
				} finally {
					setIsSubmitting(false);
				}
			}
			setCurrentStep(currentStep + 1);
		}
	}, [
		validateCurrentStep,
		currentStep,
		warrantyRequest,
		handleUpdateWarrantyRequest,
		steps
	]);

	const fetchJob = useCallback(async (jobId: string) => {
		// if (!user || user.id !== warrantyRequest?.customer_id) {
		//     return;
		// }
		fetch(`/api/jobs/${jobId}`)
			.then((response) => response.json())
			.then((data: JobWithQuotes) => {
				setJob(data);
			})
			.catch((error) => {
				console.error("Failed to fetch job:", error);
			});
	}, []);

	const handleRegistrationComplete = useCallback(() => {
		// Auto-advance to the next step after successful registration
		setCurrentStep(3); // Move to step 3 (request details)
	}, []);

	const handleLoginComplete = useCallback(() => {
		// Auto-advance to the next step after successful login
		setCurrentStep(3); // Move to step 3 (request details)
	}, []);

	const renderCurrentStep = useCallback(() => {
		switch (currentStep) {
			case 0:
				return <StepLoading />;
			case 1:
				return <StepWelcome warrantyRequest={warrantyRequest} />;
			case 2:
				// If user is logged in and email matches warranty request email, skip to step 3
				if (
					user?.id &&
					user.email?.toLowerCase() === warrantyRequest.email?.toLowerCase()
				) {
					return (
						<StepRequestDetails
							warrantyRequest={warrantyRequest}
							onUpdateWarrantyRequest={handleUpdateWarrantyRequest}
						/>
					);
				}
				// Route based on user state
				if (!userState?.userExists) {
					// User doesn't exist - full registration
					return (
						<StepRegistration
							warrantyRequest={warrantyRequest}
							onRegistrationComplete={handleRegistrationComplete}
						/>
					);
				} else if (userState.hasLoggedIn) {
					// User exists and has logged in before - show login
					return <StepLogin warrantyRequest={warrantyRequest} />;
				} else if (userState.needsPasswordSetup) {
					// User exists but needs password setup - show password setup with newsletter tags
					return (
						<StepRegistration
							warrantyRequest={warrantyRequest}
							onRegistrationComplete={handleRegistrationComplete}
							isPasswordSetup={true}
						/>
					);
				} else {
					// Fallback - show login
					return <StepLogin warrantyRequest={warrantyRequest} />;
				}
			case 3:
				return (
					<StepRequestDetails
						warrantyRequest={warrantyRequest}
						onUpdateWarrantyRequest={handleUpdateWarrantyRequest}
					/>
				);
			case 4:
				return (
					<StepInviteProviders
						ref={stepInviteProvidersRef}
						warrantyRequest={warrantyRequest}
						job={job}
						onInvitedProviders={handleInvitedProviders}
					/>
				);
			// case 5:
			//     return <StepUpgrade warrantyRequest={warrantyRequest} />;
			case 5:
				return <StepSuccess jobId={warrantyRequest.job_id} />;
			default:
				return null;
		}
	}, [
		currentStep,
		handleUpdateWarrantyRequest,
		handleInvitedProviders,
		handleRegistrationComplete,
		job,
		warrantyRequest,
		user?.id,
		user?.email
	]);

	// Call fetchJob when jobId changes
	useEffect(() => {
		if (warrantyRequest?.job_id) {
			fetchJob(warrantyRequest.job_id);
		}
	}, [user, warrantyRequest.job_id, fetchJob]);

	return (
		<div className="bg-gray-50 flex flex-col items-center justify-center py-4 px-4 sm:px-6 lg:px-8">
			<div className="max-w-2xl w-full space-y-4">
				<Card className="flex flex-col">
					<CardHeader className="pt-4">
						{currentStep > 0 && steps[currentStep - 1]?.header ? (
							// Use custom header if available
							steps[currentStep - 1].header!(warrantyRequest)
						) : (
							<div className="text-center">
								<CardTitle className="text-center space-y-4">
									<div className="flex items-end justify-center gap-6">
										<div className="flex flex-col items-center">
											<ApplicationLogo />
											<span className="text-sm text-gray-500 mt-1">
												Service Partner{" "}
											</span>
										</div>
										<div className="h-8 w-px bg-gray-300 mb-4"></div>
										<div className="flex flex-col items-center">
											<OEMLogo company={warrantyRequest.company} />
											<span className="text-sm text-gray-500 mt-1">
												Warranty Provider
											</span>
										</div>
									</div>
									<div className="pt-4 text-lg">
										{currentStepProperties.title}
									</div>
								</CardTitle>
								{currentStepProperties.description && (
									<CardDescription className="text-bold pt-2">
										{currentStepProperties.description}
									</CardDescription>
								)}
							</div>
						)}
					</CardHeader>

					<CardContent className="flex-1 flex justify-center items-start">
						<div className="w-full">{renderCurrentStep()}</div>
					</CardContent>

					<CardFooter className="pt-4">
						{currentStep === 0 ? (
							// Loading state - show minimal footer
							<div className="flex justify-center w-full">
								<div className="text-lg text-muted-foreground">
									Initializing...
								</div>
							</div>
						) : (
							// Normal state - show full navigation
							<div className="flex items-center justify-between w-full">
								{/* Progress Indicator and Step Info - Always on far left */}
								<div className="flex flex-col items-start">
									<div className="flex items-center space-x-1">
										{countableSteps.map((step, index) => (
											<div key={step.id} className="flex items-center">
												<div
													className={`w-6 h-6 rounded-full flex items-center justify-center text-base font-medium
                                                ${currentStep >= step.id
															? "bg-primary text-primary-foreground"
															: "bg-muted text-muted-foreground"
														}`}
												>
													{step.id}
												</div>
												{index < countableSteps.length - 1 && (
													<div
														className={`w-3 h-0.5 mx-1
                                                    ${currentStep > step.id ? "bg-primary" : "bg-muted"}`}
													/>
												)}
											</div>
										))}
									</div>
									<div className="text-sm text-muted-foreground mt-3">
										{steps[currentStep - 1]?.ignoreInStepCount
											? currentStepProperties.title
											: `Step ${countableSteps.findIndex((step) => step.id === currentStep) + 1} of ${countableSteps.length}: ${currentStepProperties.title}`}
									</div>
								</div>

								{/* Navigation Buttons - On the right */}
								<div className="flex gap-2">
									{/* Previous Button */}
									{currentStep > 1 &&
										steps[currentStep - 1]?.allowPrevious !== false && (
											<Button
												type="button"
												variant="outline"
												onClick={handlePrevious}
												disabled={isSubmitting}
											>
												Previous
											</Button>
										)}

									{/* Next/Done Button */}
									{currentStep < steps.length ? (
										steps[currentStep - 1]?.allowNext !== false && (
											<Button
												type="button"
												onClick={handleNext}
												disabled={isSubmitting}
											>
												Next
											</Button>
										)
									) : (
										<Button
											type="button"
											onClick={handleSubmit}
											disabled={isSubmitting}
										>
											{isSubmitting ? "Saving..." : "Done"}
										</Button>
									)}
								</div>
							</div>
						)}
					</CardFooter>
				</Card>
			</div>
		</div>
	);
}
