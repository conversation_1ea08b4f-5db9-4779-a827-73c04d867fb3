"use client";

import { ExtendedWarrantyRequest } from "@/types/warranty";
import { <PERSON><PERSON><PERSON>cle, Clock, MapPin, Sparkles, Wrench } from "lucide-react";
import { memo } from "react";

interface StepWelcomeProps {
	warrantyRequest: ExtendedWarrantyRequest;
}

export const StepWelcome = memo(function StepWelcome({
	warrantyRequest
}: StepWelcomeProps) {
	return (
		<div className="w-full space-y-8 py-6">
			{/* Hero Section - Great News */}
			<div className="relative overflow-hidden">
				<div className="absolute inset-0 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl" />
				<div className="relative p-8 text-center">
					<div className="inline-flex items-center justify-center w-16 h-16 bg-emerald-500 rounded-full mb-4 shadow-lg">
						<Sparkles className="h-8 w-8 text-white" />
					</div>
					<h2 className="text-2xl font-bold text-emerald-900 mb-3">
						Great News!
					</h2>
					<p className="text-lg text-emerald-800 leading-relaxed max-w-2xl mx-auto">
						To make your warranty service experience as convenient as possible,{" "}
						<span className="font-semibold text-emerald-900">
							{warrantyRequest.company.name}
						</span>{" "}
						has partnered with RV Help to give you a <br />
						<span className="font-bold text-orange-600 bg-orange-100 px-2 py-1 rounded-md">
							1 year free membership
						</span>{" "}
						to their Pro program.
					</p>
				</div>
			</div>

			{/* Benefits Section */}
			<div className="relative">
				<div className="absolute inset-0 bg-gradient-to-br from-slate-50 to-white rounded-2xl shadow-sm" />
				<div className="relative p-8">
					<div className="text-center mb-8">
						<h3 className="text-xl font-bold text-gray-900 mb-2">
							Why You'll Love RV Help
						</h3>
						<p className="text-gray-600">
							Professional service, delivered to your door
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						{[
							{
								icon: Wrench,
								title: "Mobile Service",
								text: "Certified technicians come to your location",
								color: "emerald"
							},
							{
								icon: MapPin,
								title: "No Towing Required",
								text: "No need to tow your RV to a service center",
								color: "orange"
							},
							{
								icon: Clock,
								title: "Real-Time Updates",
								text: "Stay informed with live appointment tracking",
								color: "emerald"
							},
							{
								icon: CheckCircle,
								title: "Covered Fees",
								text: `${warrantyRequest?.company?.name}-covered dispatch fee`,
								color: "orange"
							}
						].map((benefit, index) => (
							<div
								key={index}
								className="group flex items-start gap-4 p-4 rounded-xl hover:bg-white hover:shadow-md transition-all duration-200"
							>
								<div
									className={`flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center ${
										benefit.color === "emerald"
											? "bg-emerald-100 text-emerald-600 group-hover:bg-emerald-200"
											: "bg-orange-100 text-orange-600 group-hover:bg-orange-200"
									} transition-colors duration-200`}
								>
									<benefit.icon className="h-6 w-6" />
								</div>
								<div className="flex-1 min-w-0">
									<h4 className="font-semibold text-gray-900 mb-1">
										{benefit.title}
									</h4>
									<p className="text-sm text-gray-600 leading-relaxed">
										{benefit.text}
									</p>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Next Steps CTA */}
			<div className="relative overflow-hidden">
				<div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl" />
				<div className="relative p-8 text-center">
					<div className="inline-flex items-center justify-center w-12 h-12 bg-orange-500 rounded-full mb-4">
						<CheckCircle className="h-6 w-6 text-white" />
					</div>
					<h3 className="text-xl font-bold text-orange-900 mb-3">
						What&apos;s Next?
					</h3>
					<p className="text-orange-800 leading-relaxed max-w-xl mx-auto">
						We&apos;ll help you create your RV Help account, confirm your
						details, and connect you with qualified technicians in your area.{" "}
						<span className="font-semibold">
							Your Pro membership will be automatically activated!
						</span>
					</p>
				</div>
			</div>
		</div>
	);
});
