"use client";

import { SearchableSelect } from "@/components/SearchableSelect";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { allRvMakes, popularRvMakes } from "@/data/rvMakes";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { zodResolver } from "@hookform/resolvers/zod";
import { memo, useMemo } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Static RV types array to avoid recreation on every render
const RV_TYPES = [
	"Class A",
	"Class B",
	"Class C",
	"Travel Trailer",
	"Fifth Wheel",
	"Toy Hauler",
	"Truck Camper"
] as const;

const rvDetailsSchema = z.object({
	rv_type: z.string().min(1, "RV type is required"),
	rv_make: z.string().min(1, "RV make is required"),
	rv_model: z.string().min(1, "RV model is required"),
	rv_year: z.string().min(4, "RV year is required")
});

type RVDetailsFormData = z.infer<typeof rvDetailsSchema>;

interface EditRVDetailsProps {
	warrantyRequest: ExtendedWarrantyRequest;
	className?: string;
	onUpdateWarrantyRequest?: (
		request: ExtendedWarrantyRequest,
		createJob: boolean
	) => Promise<any>;
}

export const EditRVDetails = memo(function EditRVDetails({
	warrantyRequest,
	onUpdateWarrantyRequest
}: EditRVDetailsProps) {
	const form = useForm<RVDetailsFormData>({
		resolver: zodResolver(rvDetailsSchema),
		defaultValues: {
			rv_type: warrantyRequest.rv_type || "",
			rv_make: warrantyRequest.rv_make || "",
			rv_model: warrantyRequest.rv_model || "",
			rv_year: warrantyRequest.rv_year || ""
		}
	});

	const onSubmit = async (data: RVDetailsFormData) => {
		try {
			const updatedRequest = {
				...warrantyRequest,
				...data
			};
			await onUpdateWarrantyRequest?.(updatedRequest, false);
		} catch (error) {
			console.error("Failed to update RV details:", error);
		}
	};

	// Create the make options in the same format as the profile form
	const makeOptions = useMemo(
		() => [
			{
				label: "Popular Makes",
				options: popularRvMakes.map((make) => ({
					value: make,
					label: make
				}))
			},
			{
				label: "All Makes",
				options: allRvMakes.map((make) => ({
					value: make,
					label: make
				}))
			}
		],
		[]
	);

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
				<div>
					<div className="flex items-center gap-2">
						<h3 className="text-lg font-medium">RV Information</h3>
					</div>
					<p className="text-sm text-gray-600 mt-1">
						Provide details about your RV to help us better assist you.
					</p>
				</div>

				<div className="space-y-2">
					<FormField
						control={form.control}
						name="rv_make"
						render={({ field }) => (
							<FormItem data-testid="rv_make">
								<FormLabel>Make</FormLabel>
								<div>
									<SearchableSelect
										name="rv_make"
										options={makeOptions}
										value={field.value as string}
										onChange={field.onChange}
										placeholder="Select RV make"
										error={form.formState.errors.rv_make}
									/>
								</div>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="space-y-2">
					<FormField
						control={form.control}
						name="rv_model"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Model</FormLabel>
								<FormControl>
									<Input placeholder="Enter RV model" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="space-y-2">
					<FormField
						control={form.control}
						name="rv_year"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Year</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter RV year"
										type="number"
										{...field}
										onChange={(e) => field.onChange(e.target.value)}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="space-y-2">
					<FormField
						control={form.control}
						name="rv_type"
						render={({ field }) => (
							<FormItem>
								<FormLabel>RV Type</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="Choose RV type" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{RV_TYPES.map((type) => (
											<SelectItem key={type} value={type}>
												{type}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="flex justify-end pt-4">
					<Button
						type="submit"
						disabled={form.formState.isSubmitting}
						className="px-6"
					>
						{form.formState.isSubmitting ? "Saving..." : "Save Changes"}
					</Button>
				</div>
			</form>
		</Form>
	);
});
