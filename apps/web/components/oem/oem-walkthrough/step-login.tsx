"use client";
import { TermsText } from "@/components/auth/TermsText";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { signIn } from "next-auth/react";
import Link from "next/link";
import { memo, useCallback, useMemo } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";

interface StepLoginFormData {
	email: string;
	password: string;
}

interface StepLoginProps {
	warrantyRequest: ExtendedWarrantyRequest;
	onLoginComplete?: () => void;
}

export const StepLogin = memo(function StepLogin({
	warrantyRequest,
	onLoginComplete
}: StepLoginProps) {
	const formDefaultValues = useMemo(
		() => ({
			email: warrantyRequest.email || "",
			password: ""
		}),
		[warrantyRequest]
	);

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting }
	} = useForm<StepLoginFormData>({
		defaultValues: formDefaultValues
	});

	const onSubmit = useCallback(
		async (data: StepLoginFormData) => {
			try {
				const result = await signIn("credentials", {
					email: data.email.toLowerCase(),
					password: data.password,
					redirect: false
				});

				if (result?.error) {
					toast.error("Invalid credentials");
					return;
				}

				await fetch("/api/auth/login", {
					method: "POST"
				});

				// Auto-advance to next step after successful login
				if (onLoginComplete) {
					onLoginComplete();
				}
			} catch (error) {
				console.error(error);
				toast.error("An error occurred. Please try again.");
			}
		},
		[onLoginComplete]
	);

	return (
		<div className="w-full max-w-lg p-4 mx-auto">
			<div className={cn(isSubmitting ? "opacity-50 pointer-events-none" : "")}>
				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="space-y-4 pt-3">
						<div className="space-y-6">
							<Input
								{...register("email", {
									required: "Email is required",
									pattern: {
										value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
										message: "Invalid email address"
									}
								})}
								type="email"
								label="Email"
								error={errors.email?.message}
								disabled={true}
							/>
							<Input
								{...register("password", {
									required: "Password is required"
								})}
								type="password"
								label="Password"
								error={errors.password?.message}
								disabled={isSubmitting}
							/>
						</div>
						<div className="pt-2">
							<Button
								type="submit"
								className="w-full"
								disabled={isSubmitting}
								data-testid="login-button"
							>
								{isSubmitting ? "Loading..." : "Sign In"}
							</Button>

							<TermsText isRegister={false} />

							<div className="text-center text-sm mt-4">
								<Link
									href="/forgot-password"
									className="text-blue-600 hover:text-blue-800"
								>
									Forgot Password?
								</Link>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
	);
});
