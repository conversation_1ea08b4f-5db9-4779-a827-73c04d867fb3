"use client";
import { TermsText } from "@/components/auth/TermsText";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { signIn } from "next-auth/react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { memo, useCallback, useMemo } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";

interface StepRegistrationFormData {
	firstName: string;
	lastName: string;
	email: string;
	phone?: string;
	contact_preference: "sms" | "email" | "phone";
	requestId: string;
	password: string;
	confirmPassword: string;
}

interface StepRegistrationProps {
	warrantyRequest: ExtendedWarrantyRequest;
	onRegistrationComplete?: () => void;
	isPasswordSetup?: boolean;
}

export const StepRegistration = memo(function StepRegistration({
	warrantyRequest,
	onRegistrationComplete,
	isPasswordSetup = false
}: StepRegistrationProps) {
	const searchParams = useSearchParams();

	const formDefaultValues = useMemo(
		() => ({
			firstName: warrantyRequest.first_name || "",
			lastName: warrantyRequest.last_name || "",
			requestId: warrantyRequest.id,
			email: warrantyRequest.email || "",
			password: "",
			confirmPassword: "",
			newsletter: true,
		}),
		[warrantyRequest]
	);

	const currentPath = useMemo(
		() => `/oem/${warrantyRequest.uuid}`,
		[warrantyRequest.uuid]
	);

	const {
		register,
		handleSubmit,
		formState: { errors, isSubmitting },
		watch
	} = useForm<StepRegistrationFormData>({
		defaultValues: formDefaultValues
	});

	const onSubmit = useCallback(
		async (data: any) => {
			try {
				const apiEndpoint = "/api/users/set-warranty-password";
				const response = await fetch(apiEndpoint, {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						...data,
						currentPath,
						emailVerified: true
					})
				});

				const responseData = await response.json();

				if (responseData.error) {
					toast.error(responseData.error);
					return;
				}

				// log the user in
				const signInResult = await signIn("credentials", {
					email: data.email,
					password: data.password,
					callbackUrl: currentPath,
					redirect: false
				});

				if (signInResult?.error) {
					toast.error(
						"Registration successful but login failed. Please try logging in."
					);
					return;
				}

				// Auto-advance to next step after successful registration and sign-in
				if (onRegistrationComplete) {
					onRegistrationComplete();
				}
			} catch (error) {
				console.log("catch error", error);
				console.log("error.details", error.details);
				toast.error(error.message);
				if (error.details) {
					toast.error(error.details.message);
				}
			}
		},
		[
			currentPath,
			onRegistrationComplete,
			isPasswordSetup,
			warrantyRequest.company.name
		]
	);

	return (
		<div className="w-full py-1 px-2">
			<div className={cn(isSubmitting ? "opacity-50 pointer-events-none" : "")}>
				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="space-y-2">
						{!isPasswordSetup && (
							<>
								<Input
									{...register("firstName", {
										required: "First name is required"
									})}
									label="First Name"
									error={errors.firstName?.message}
									disabled={true}
								/>
								<Input
									{...register("lastName", {
										required: "Last name is required"
									})}
									label="Last Name"
									error={errors.lastName?.message}
									disabled={true}
								/>
							</>
						)}
						<Input
							{...register("email", {
								required: "Email is required",
								pattern: {
									value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
									message: "Invalid email address"
								}
							})}
							label="Email"
							error={errors.email?.message}
							disabled={true}
						/>
						<Input
							{...register("password", {
								required: "Password is required",
								minLength: {
									value: 8,
									message: "Password must be at least 8 characters"
								}
							})}
							type="password"
							label="Password"
							error={errors.password?.message}
							disabled={isSubmitting}
						/>
						<Input
							{...register("confirmPassword", {
								required: "Please confirm your password",
								validate: (val: string) => {
									if (watch("password") != val) {
										return "Passwords do not match";
									}
								}
							})}
							type="password"
							label="Confirm Password"
							error={errors.confirmPassword?.message}
							disabled={isSubmitting}
						/>


					</div>
					<Button type="submit" className="w-full mt-3" disabled={isSubmitting}>
						{isSubmitting
							? isPasswordSetup
								? "Setting up..."
								: "Registering..."
							: isPasswordSetup
								? "Set Password"
								: "Register"}
					</Button>
					<TermsText isRegister={true} />
				</form>
			</div>

			{!isPasswordSetup && (
				<p className="text-center text-sm mt-2">
					Already have an account?{" "}
					<Link
						href={`/login${searchParams?.get("redirectUrl")
							? `?redirectUrl=${searchParams.get("redirectUrl")}`
							: ""
							}`}
						className="text-blue-600 hover:text-blue-800"
					>
						Sign In
					</Link>
				</p>
			)}
		</div>
	);
});
