"use client";

import { PlacesAutocomplete } from "@/components/places/PlacesAutocomplete";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { ExtendedWarrantyRequest, LocationData } from "@/types/warranty";
import { zodResolver } from "@hookform/resolvers/zod";
import { memo, useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

const locationSchema = z.object({
	location: z
		.object({
			address: z.string().min(1, "Address is required"),
			latitude: z.number(),
			longitude: z.number()
		})
		.nullable()
		.refine((val) => val !== null, {
			message: "Please select a valid address"
		})
});

type LocationFormData = z.infer<typeof locationSchema>;

interface EditLocationProps {
	warrantyRequest: ExtendedWarrantyRequest;
	className?: string;
	onUpdateWarrantyRequest?: (
		request: ExtendedWarrantyRequest,
		createJob: boolean
	) => Promise<any>;
}

export const EditLocation = memo(function EditLocation({
	warrantyRequest,
	onUpdateWarrantyRequest
}: EditLocationProps) {
	const [locationConfirmed, setLocationConfirmed] = useState(false);
	const [providerCount, setProviderCount] = useState<number | null>(null);
	const [isLoadingProviders, setIsLoadingProviders] = useState(false);

	const form = useForm<LocationFormData>({
		resolver: zodResolver(locationSchema),
		defaultValues: {
			location: warrantyRequest.location || null
		}
	});

	const fetchProviderCount = useCallback(async (lat: number, lng: number) => {
		setIsLoadingProviders(true);
		try {
			const response = await fetch(
				`/api/listings/invite-providers?page=1&limit=1&category=rv-repair&lat=${lat}&lng=${lng}`
			);
			if (response.ok) {
				const data = await response.json();
				setProviderCount(data.total || 0);
			} else {
				setProviderCount(0);
			}
		} catch (error) {
			console.error("Error fetching provider count:", error);
			setProviderCount(0);
		} finally {
			setIsLoadingProviders(false);
		}
	}, []);

	const onSubmit = async (data: LocationFormData) => {
		try {
			const updatedRequest = {
				...warrantyRequest,
				location: data.location as LocationData
			};
			await onUpdateWarrantyRequest?.(updatedRequest, false);
		} catch (error) {
			console.error("Failed to update location:", error);
		}
	};

	// Handle location selection
	const handleLocationSelect = (address: string, details: any) => {
		if (details?.geometry?.location) {
			// Create simplified location data with just address, latitude, and longitude
			const locationData = {
				address,
				latitude: details.geometry.location.lat(),
				longitude: details.geometry.location.lng()
			};

			// For now, always confirm the location (radius checking can be added later)
			form.setValue("location", locationData);
			setLocationConfirmed(true);

			// Fetch provider count for the new location
			fetchProviderCount(locationData.latitude, locationData.longitude);
		}
	};

	// Fetch provider count when component mounts if location exists
	useEffect(() => {
		const location = form.watch("location");
		if (location?.latitude && location?.longitude) {
			fetchProviderCount(location.latitude, location.longitude);
		}
	}, [fetchProviderCount, form.watch("location")]);

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
				<div>
					<div className="flex items-center gap-2">
						<h3 className="text-lg font-medium">Service Location</h3>
					</div>
					<p className="text-sm text-gray-600 mt-1">
						Enter the exact address where you need service. This helps us match
						you with the right technician.
					</p>
				</div>

				<div className="relative">
					<FormField
						control={form.control}
						name="location"
						render={({ field }) => (
							<FormItem>
								<FormControl>
									<PlacesAutocomplete
										placeholder="Enter your street address"
										value={field.value?.address || ""}
										onPlaceSelect={handleLocationSelect}
										onChange={(e) => {
											if (!e.target.value) {
												field.onChange(null);
												setLocationConfirmed(false);
												setProviderCount(null);
											}
										}}
										locationType="precise"
										className="focus:ring-primary focus:border-primary transition-all duration-200"
									/>
								</FormControl>
								{form.formState.errors.location && (
									<p className="text-sm text-red-500">
										{form.formState.errors.location?.message}
									</p>
								)}
								{locationConfirmed && field.value?.address && (
									<div className="mt-2 text-sm text-green-600">
										✓ Location confirmed
									</div>
								)}
								{/* Provider Count Display */}
								{field.value?.address && (
									<div className="mt-4 p-3 bg-slate-50 border border-slate-200 rounded">
										<div className="font-semibold text-sm mb-1 text-slate-700">
											Available Providers (within 100 mile radius)
										</div>
										{isLoadingProviders ? (
											<div className="text-xs text-slate-600">
												Loading provider count...
											</div>
										) : providerCount !== null ? (
											<div className="text-xs text-slate-600">
												Total Providers:{" "}
												<span className="font-bold">{providerCount}</span>
											</div>
										) : null}
										{providerCount === 0 && (
											<div className="text-xs text-red-500 mt-2">
												No providers found in this area. Please verify the
												address or try a different location.
											</div>
										)}
									</div>
								)}
							</FormItem>
						)}
					/>
				</div>

				<div className="flex justify-end pt-4">
					<Button
						type="submit"
						disabled={form.formState.isSubmitting}
						className="px-6"
					>
						{form.formState.isSubmitting ? "Saving..." : "Save Changes"}
					</Button>
				</div>
			</form>
		</Form>
	);
});
