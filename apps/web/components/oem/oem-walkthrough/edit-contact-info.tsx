"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel
} from "@/components/ui/form";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { zodResolver } from "@hookform/resolvers/zod";
import { memo } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

const contactInfoSchema = z.object({
	first_name: z.string().min(1, "First name is required"),
	last_name: z.string().min(1, "Last name is required"),
	email: z.string().email("Valid email is required"),
	phone: z.string().min(10, "Valid phone number is required"),
	contact_preference: z.enum(["phone", "sms", "email"]).optional()
});

type ContactInfoFormData = z.infer<typeof contactInfoSchema>;

interface EditContactInfoProps {
	warrantyRequest: ExtendedWarrantyRequest;
	className?: string;
	onUpdateWarrantyRequest?: (
		request: ExtendedWarrantyRequest,
		createJob: boolean
	) => Promise<any>;
}

export const EditContactInfo = memo(function EditContactInfo({
	warrantyRequest,
	onUpdateWarrantyRequest
}: EditContactInfoProps) {
	const form = useForm<ContactInfoFormData>({
		resolver: zodResolver(contactInfoSchema),
		defaultValues: {
			first_name: warrantyRequest.first_name || "",
			last_name: warrantyRequest.last_name || "",
			email: warrantyRequest.email || "",
			phone: warrantyRequest.phone || "",
			contact_preference: warrantyRequest.contact_preference as
				| "phone"
				| "sms"
				| "email"
				| undefined
		}
	});

	const onSubmit = async (data: ContactInfoFormData) => {
		try {
			const updatedRequest = {
				...warrantyRequest,
				...data
			};
			await onUpdateWarrantyRequest?.(updatedRequest, false);
		} catch (error) {
			console.error("Failed to update contact info:", error);
		}
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
				<div>
					<div className="flex items-center gap-2">
						<h3 className="text-lg font-medium">Contact Information</h3>
					</div>
					<p className="text-sm text-gray-600 mt-1">
						{"How would you like the service provider to contact you?"}
					</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<FormField
						control={form.control}
						name="first_name"
						render={({ field }) => (
							<FormItem>
								<FormLabel>First Name</FormLabel>
								<FormControl>
									<input
										type="text"
										className="flex h-10 w-full rounded-md border border-gray-300 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:border-primary transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50"
										placeholder="Enter your first name"
										disabled={true}
										{...field}
									/>
								</FormControl>
								{form.formState.errors.first_name && (
									<p className="text-sm text-red-500">
										{form.formState.errors.first_name.message}
									</p>
								)}
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="last_name"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Last Name</FormLabel>
								<FormControl>
									<input
										type="text"
										className="flex h-10 w-full rounded-md border border-gray-300 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:border-primary transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50"
										placeholder="Enter your last name"
										disabled={true}
										{...field}
									/>
								</FormControl>
								{form.formState.errors.last_name && (
									<p className="text-sm text-red-500">
										{form.formState.errors.last_name.message}
									</p>
								)}
							</FormItem>
						)}
					/>
				</div>

				<FormField
					control={form.control}
					name="email"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Email</FormLabel>
							<FormControl>
								<input
									type="email"
									className="flex h-10 w-full rounded-md border border-gray-300 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:border-primary transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50"
									placeholder="Enter your email"
									disabled={true}
									{...field}
								/>
							</FormControl>
							{form.formState.errors.email && (
								<p className="text-sm text-red-500">
									{form.formState.errors.email.message}
								</p>
							)}
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="phone"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Phone Number</FormLabel>
							<FormControl>
								<input
									type="tel"
									className="flex h-10 w-full rounded-md border border-gray-300 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:border-primary transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50"
									placeholder="Enter your phone number"
									{...field}
								/>
							</FormControl>
							{form.formState.errors.phone && (
								<p className="text-sm text-red-500">
									{form.formState.errors.phone.message}
								</p>
							)}
							<p className="text-sm text-gray-500">
								This will be saved to your profile
							</p>
						</FormItem>
					)}
				/>

				{/* SMS Opt-in Notice */}
				<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
					<p className="text-xs text-gray-600">
						By providing your phone number, you agree to receive SMS messages from RVHelp and service providers regarding your warranty request. Message and data rates may apply. Reply STOP to opt-out.
					</p>
				</div>

				<div className="flex justify-end pt-4">
					<Button
						type="submit"
						disabled={form.formState.isSubmitting}
						className="px-6"
					>
						{form.formState.isSubmitting ? "Saving..." : "Save Changes"}
					</Button>
				</div>
			</form>
		</Form>
	);
});
