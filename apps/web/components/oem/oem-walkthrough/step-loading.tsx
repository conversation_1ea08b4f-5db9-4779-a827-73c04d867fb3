"use client";

import { memo } from "react";
import { Skeleton } from "@/components/ui/skeleton";

export const StepLoading = memo(function StepLoading() {
    return (
        <div className="w-full max-w-lg p-4">
            <div className="space-y-6">
                {/* Title skeleton */}
                <div className="text-center">
                    <Skeleton className="h-8 w-64 mx-auto" />
                </div>
                
                {/* Content skeletons */}
                <div className="space-y-4">
                    <Skeleton className="h-4 w-48 mx-auto" />
                    <Skeleton className="h-4 w-56 mx-auto" />
                    <Skeleton className="h-4 w-40 mx-auto" />
                </div>
                
                {/* Form-like skeletons */}
                <div className="space-y-4">
                    <div>
                        <Skeleton className="h-4 w-24 mb-2" />
                        <Skeleton className="h-10 w-full" />
                    </div>
                    <div>
                        <Skeleton className="h-4 w-32 mb-2" />
                        <Skeleton className="h-10 w-full" />
                    </div>
                </div>
                
                {/* Button skeleton */}
                <div className="flex justify-center pt-4">
                    <Skeleton className="h-10 w-24" />
                </div>
            </div>
        </div>
    );
});
