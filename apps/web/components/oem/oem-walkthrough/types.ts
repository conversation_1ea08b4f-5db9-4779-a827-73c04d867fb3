import { z } from "zod";

export const messageProviderSchema = z.object({
    location: z.object({
        address: z.string().min(1, "Please enter a location"),
        latitude: z.number(),
        longitude: z.number(),
        city: z.string().optional(),
        state: z.string().optional(),
        zip: z.string().optional(),
        country: z.string().optional()
    }),
    contact_preference: z.enum(["sms", "phone"]).optional(),
    contact_phone: z.string().min(1, "Phone number is required"),
    category: z.string({
        required_error: "Please select a service type"
    }),
    first_name: z.string().min(1, "First name is required"),
    last_name: z.string().min(1, "Last name is required"),
    email: z.string().email("Invalid email format"),
    // RV Details fields
    rv_type: z.string().optional(),
    rv_make: z.string().optional(),
    rv_model: z.string().optional(),
    rv_year: z.number().optional(),
    // Booking fields
    preferred_date: z.string().optional(),
    preferred_time_slot: z.enum(["8am-12pm", "12pm-5pm", "5pm-9pm"]).optional(),
    alternative_dates: z.array(z.string()).optional(),
    alternative_time_slots: z
        .array(z.enum(["8am-12pm", "12pm-5pm", "5pm-9pm"]))
        .optional(),
    warranty_request_id: z.string().optional(),
    message: z
        .string()
        .min(25, "Message must be at least 25 characters")
        .max(1000, "Message cannot exceed 1000 characters")
});

export type MessageProviderFormData = z.infer<typeof messageProviderSchema>;
