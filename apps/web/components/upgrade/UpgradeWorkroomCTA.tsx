import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/hooks/useAuth";
import { OfferUtils } from "@/lib/utils/offer-utils";
import type { JobWithUserAndLocation } from "@/types/global";
import { AlertCircle } from "lucide-react";
import { useMemo, useState } from "react";

interface UpgradeWorkroomCTAProps {
	job: JobWithUserAndLocation;
	setIsOfferModalOpen: (isOpen: boolean) => void;
	setOfferVariant?: (variant: "invite-providers" | "pro-support") => void;
}

const UpgradeWorkroomCTA = ({
	job,
	setIsOfferModalOpen,
	setOfferVariant
}: UpgradeWorkroomCTAProps) => {
	const [showAlert, setShowAlert] = useState(true);
	const { isPaid, loading: authLoading, user } = useAuth();

	const isOfferActive =
		job && OfferUtils.isJobOfferActive(new Date(job.created_at));
	const workroomIsUpgraded = job.is_premium || isPaid;

	const offerEligibilityCheck = useMemo(() => {
		if (!user || authLoading || workroomIsUpgraded || !isOfferActive) {
			return { eligible: false, reason: "Not applicable" };
		}
		return OfferUtils.isUserEligibleForAnnualOfferClientSide(
			user,
			new Date(job.created_at)
		);
	}, [user, authLoading, workroomIsUpgraded, isOfferActive, job.created_at]);

	const isEligibleForDiscount = offerEligibilityCheck.eligible;
	const shouldShowOffer =
		!authLoading && !workroomIsUpgraded && isOfferActive && showAlert;

	if (!shouldShowOffer) return null;
	if (isPaid) return null;

	return (
		<div className="mb-6">
			<Alert variant="info" className="border-yellow-300 bg-yellow-50">
				<AlertCircle className="h-4 w-4 text-yellow-600" />
				<AlertTitle className="font-semibold text-yellow-800 mb-0">
					{isEligibleForDiscount
						? "Unlock Pro Features & Save 50%!"
						: "Unlock Pro Features!"}
				</AlertTitle>
				<AlertDescription className="flex justify-between items-center">
					<span className="text-yellow-700">
						{isEligibleForDiscount
							? "For a limited time, upgrade to a Pro membership for 50% off and unlock the ability to invite multiple providers to this job."
							: "Upgrade to a Pro membership and unlock the ability to invite multiple providers to this job."}
					</span>
					<div className="flex items-center gap-4">
						<Button
							className="bg-gradient-to-r from-yellow-400 to-amber-500 hover:from-yellow-500 hover:to-amber-600 text-white shadow-lg hover:shadow-xl"
							onClick={() => {
								setOfferVariant?.("invite-providers");
								setIsOfferModalOpen(true);
							}}
						>
							View Offer
						</Button>
						<Button
							variant="ghost"
							size="sm"
							onClick={() => setShowAlert(false)}
						>
							Dismiss
						</Button>
					</div>
				</AlertDescription>
			</Alert>
		</div>
	);
};

export default UpgradeWorkroomCTA;
