import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger
} from "@/components/ui/tooltip";
import { ListingWithLocation } from "@/types/global";
import { RVHelpVerificationLevel } from "@rvhelp/database";
import { CheckCircle } from "lucide-react";
import { getBadgeContent } from "../../lib/rvta-certifications";
import { renderStarRating } from "../../lib/utils/star-rating";

// Utility function to format distance
const formatDistance = (distance?: number): string => {
	if (!distance || !Number.isFinite(distance)) {
		return "Distance unavailable";
	}
	return `${distance.toFixed(1)} miles away`;
};

interface ProviderCardProps {
	category?: string;
	provider: ListingWithLocation;
	onSelect?: (providerId: string) => void;
	isSelected?: boolean;
	showCheckbox?: boolean;
	className?: string;
}

export default function ProviderCard({
	category,
	provider,
	onSelect,
	isSelected,
	showCheckbox = true,
	className = ""
}: ProviderCardProps) {
	const isVerified =
		provider.rv_help_verification_level === RVHelpVerificationLevel.VERIFIED ||
		provider.rv_help_verification_level ===
			RVHelpVerificationLevel.CERTIFIED_PRO;

	const inspectorBadgeContent = getBadgeContent(
		"rv-inspection",
		provider.nrvia_inspector_level ?? 0
	);
	const technicianBadgeContent = getBadgeContent(
		"rv-repair",
		provider.rvtaa_technician_level ?? 0
	);

	return (
		<div
			className={`relative cursor-pointer bg-white rounded-lg border ${isSelected ? "border-primary" : "border-gray-200"} p-4 hover:border-primary/50 transition-all duration-200 ${className}`}
			onClick={() => onSelect?.(provider.id)}
		>
			<div className="flex items-start gap-4">
				{showCheckbox && (
					<div onClick={(e) => e.stopPropagation()}>
						<Checkbox
							checked={isSelected}
							className="mt-1.5"
							onCheckedChange={() => onSelect?.(provider.id)}
						/>
					</div>
				)}

				<div className="flex-grow">
					<div className="flex items-start justify-between gap-4">
						<div className="relative">
							<Avatar className="h-12 w-12">
								{provider.profile_image && (
									<AvatarImage
										src={provider.profile_image}
										alt={provider.business_name}
									/>
								)}
								<AvatarFallback>
									{provider.business_name.substring(0, 2).toUpperCase()}
								</AvatarFallback>
							</Avatar>
							{isVerified && (
								<div className="absolute -bottom-1 -right-1 bg-white rounded-full">
									<CheckCircle className="w-5 h-5 text-green-600 fill-white" />
								</div>
							)}
						</div>
						<div className="flex-grow">
							<h3 className="text-lg font-semibold text-gray-900">
								{provider.business_name}
							</h3>
							<div className="flex items-center gap-2 mt-1">
								<div className="flex items-center">
									{renderStarRating(provider.rating || 0, provider.num_reviews)}
								</div>
							</div>
							<div className="flex items-center gap-2 mt-1">
								<span className="text-sm text-gray-600">
									{provider.location.city}, {provider.location.state}
								</span>
							</div>
						</div>
					</div>

					{provider.short_description && (
						<p className="text-sm text-gray-600 mt-2 line-clamp-2">
							{provider.short_description}
						</p>
					)}

					<div className="flex items-center gap-4 mt-3 text-sm text-gray-600">
						<div className="flex items-center gap-1">
							<svg
								className="w-4 h-4"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
								/>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
								/>
							</svg>
							<span>{formatDistance(provider.distance)}</span>
						</div>
						{provider.year_established && (
							<div className="flex items-center gap-1">
								<svg
									className="w-4 h-4"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
									/>
								</svg>
								<span>Est. {provider.year_established}</span>
							</div>
						)}
					</div>

					<div className="flex items-center gap-2 mt-3">
						{isVerified && (
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger>
										<Badge
											variant="secondary"
											className="mt-3 border-green-500 bg-green-50 text-green-700 hover:bg-green-100"
										>
											Verified Pro
										</Badge>
									</TooltipTrigger>
									<TooltipContent className="flex flex-col gap-1 max-w-[300px]">
										<p className="font-semibold">RV Help Verified Pro</p>
										<p className="text-sm text-gray-600">
											In-person verified member of RVTAA/NRVIA who has completed
											their listing profile, verified their contact information,
											and agreed to the RV Help Code of Conduct.
										</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						)}
						{category === "rv-inspection" && inspectorBadgeContent && (
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger>
										<Badge
											variant="secondary"
											className="mt-3 border-green-500 bg-green-50 text-green-700 hover:bg-green-100"
										>
											{inspectorBadgeContent.label}
										</Badge>
									</TooltipTrigger>
									<TooltipContent className="flex flex-col gap-1 max-w-[300px]">
										<p className="font-semibold">
											{inspectorBadgeContent.label}
										</p>
										<p className="text-sm text-gray-600">
											{inspectorBadgeContent.description}
										</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						)}
						{category === "rv-repair" && technicianBadgeContent && (
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger>
										<Badge variant="secondary" className="mt-3">
											{technicianBadgeContent.label}
										</Badge>
									</TooltipTrigger>
									<TooltipContent className="flex flex-col gap-1 max-w-[300px]">
										<p className="font-semibold">
											{technicianBadgeContent.label}
										</p>
										<p className="text-sm text-gray-600">
											{technicianBadgeContent.description}
										</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
