# Email Template Styling Guide

This guide explains how to use the shared styling system for consistent email templates across the RV Help platform.

## Overview

We've created a centralized styling system to ensure consistency across all email templates. This eliminates the problems of:
- Inconsistent margins and spacing
- Different color schemes 
- Duplicate style definitions
- Hard-to-maintain styling code

## Usage

### 1. Import the Shared Styles

```tsx
import { emailStyles, emailUtils } from "../shared-styles";
// or for nested templates:
import { emailStyles, emailUtils } from "../../shared-styles";
```

### 2. Use Predefined Styles

Replace local style objects with the shared ones:

```tsx
// Before (inconsistent local styles)
const h1 = {
  color: "#333",
  fontSize: "24px",
  margin: "40px 0", // Huge margin!
};

// After (consistent shared styles)
<Heading style={emailStyles.heading}>My Title</Heading>
```

## Available Styles

### Typography
- `emailStyles.heading` - Main headings (24px, brand green, centered)
- `emailStyles.subheading` - Secondary headings (20px, dark gray)
- `emailStyles.text` - Body text (16px, consistent gray)
- `emailStyles.smallText` - Small text (14px, lighter gray)

### Layout
- `emailStyles.container` - Main container (560px max width, consistent padding)
- `emailStyles.section` - Content sections (light gray background)
- `emailStyles.sectionWhite` - White background sections
- `emailStyles.centered` - Centered content wrapper

### Interactive Elements
- `emailStyles.button` - Primary buttons (brand green, inline-block)
- `emailStyles.buttonBlock` - Block-level buttons (full width)
- `emailStyles.buttonSecondary` - Secondary buttons (gray)

### Message Components
- `emailStyles.messageBox` - Message containers with left border
- `emailStyles.messageText` - Styled message text (italic)
- `emailStyles.messageTitle` - Message titles

### Other Elements
- `emailStyles.hr` - Horizontal dividers
- `emailStyles.footer` - Footer text styling
- `emailStyles.list` - List containers
- `emailStyles.listItem` - List items

### Alert/Status Styles
- `emailStyles.alertInfo` - Info alerts (blue theme)
- `emailStyles.alertWarning` - Warning alerts (yellow theme)
- `emailStyles.alertSuccess` - Success alerts (green theme)

## Utility Functions

### `emailUtils.buttonWithAlignment()`
Create buttons with custom alignment:
```tsx
<Link style={emailUtils.buttonWithAlignment("left")}>
  Left Aligned Button
</Link>
```

### `emailUtils.sectionWithBackground()`
Create sections with custom background colors:
```tsx
<Section style={emailUtils.sectionWithBackground("#f0f9ff")}>
  Custom background section
</Section>
```

## Migration Example

Here's how to migrate an existing template:

### Before (inconsistent):
```tsx
const h1 = {
  color: "#333",
  fontSize: "24px",
  fontWeight: "bold",
  margin: "40px 0", // Too much margin
};

const text = {
  color: "#333", // Different from other templates
  fontSize: "16px",
  lineHeight: "24px",
  margin: "16px 0",
};

const button = {
  backgroundColor: "#2563eb", // Wrong brand color
  color: "#fff",
  borderRadius: "3px",
  fontSize: "16px",
  padding: "12px",
};

// In JSX:
<Heading style={h1}>Title</Heading>
<Text style={text}>Content</Text>
<Link style={button}>Click me</Link>
```

### After (consistent):
```tsx
import { emailStyles } from "../shared-styles";

// In JSX:
<Heading style={emailStyles.heading}>Title</Heading>
<Text style={emailStyles.text}>Content</Text>
<Link style={emailStyles.button}>Click me</Link>
```

## Benefits

✅ **Consistent Design**: All templates use the same colors, spacing, and typography  
✅ **Easier Maintenance**: Change styles in one place to update all templates  
✅ **Reduced Code**: No more duplicate style definitions  
✅ **Brand Compliance**: Ensures correct brand colors are used  
✅ **Better UX**: Consistent spacing and margins for better readability  

## Best Practices

1. **Always use shared styles** instead of creating local ones
2. **Test thoroughly** when migrating existing templates
3. **Use semantic style names** (e.g., `messageBox` instead of generic styles)
4. **Document any custom styles** if absolutely necessary
5. **Keep the shared styles file organized** with clear comments

## Color Palette

Our consistent color scheme:
- **Brand Green**: `#437F6B` (headings, primary buttons)
- **Text Dark**: `#374151` (body text, subheadings)
- **Text Light**: `#6b7280` (footer, small text)
- **Border**: `#e5e7eb` (dividers, borders)
- **Background Light**: `#f8f9fa` (sections)

## Need Help?

If you need to create styles that aren't covered by the shared system, consider:
1. Adding them to the shared styles file if they'll be reused
2. Creating one-off inline styles for truly unique cases
3. Discussing with the team if new patterns are needed

Remember: The goal is consistency and maintainability! 