import { Button, Section, Text } from "@react-email/components";
import { BaseEmail } from "./BaseEmail";

interface PayoutNotificationEmailProps {
	providerName: string;
	amount: number;
	currency: string;
	invoiceNumber?: string;
	workDescription?: string;
}

export function PayoutNotificationEmail({
	providerName,
	amount,
	currency,
	invoiceNumber,
	workDescription
}: PayoutNotificationEmailProps) {
	const previewText = `Your payment of $${(amount / 100).toFixed(2)} is on the way!`;
	const formattedAmount = new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: currency.toUpperCase()
	}).format(amount / 100);

	return (
		<BaseEmail previewText={previewText}>
			<Section className="flex flex-col gap-4">
				<Text className="text-2xl font-bold text-center text-[#43806c]">
					Payment On The Way! 💰
				</Text>

				<Text>Hi {providerName},</Text>

				<Text>
					Great news! RVHelp has processed your payment and the money is on its
					way to your account.
				</Text>

				<Section className="bg-green-50 border border-green-200 p-4 rounded-lg">
					<Text className="text-lg font-bold text-green-800 m-0 mb-2">
						Payment Amount: {formattedAmount}
					</Text>
					{invoiceNumber && (
						<Text className="text-sm text-green-700 m-0">
							Invoice: #{invoiceNumber}
						</Text>
					)}
					{workDescription && (
						<Text className="text-sm text-green-700 m-0">
							For: {workDescription}
						</Text>
					)}
				</Section>

				<Text>
					This payment has been transferred to your connected Stripe account.
					Depending on your payout schedule, you should see the funds in your
					bank account within 1-2 business days.
				</Text>

				<Text>
					You can view your payout history and manage your payment settings in
					your Stripe Dashboard:
				</Text>

				<Button
					className="bg-[#437F6B] text-white py-3 px-6 rounded-lg"
					href="https://dashboard.stripe.com/dashboard"
				>
					View Stripe Dashboard
				</Button>

				<Text className="text-sm text-gray-600">
					Questions about your payment? Contact us at{" "}
					<a href="mailto:<EMAIL>" className="text-[#437F6B]">
						<EMAIL>
					</a>
				</Text>

				<Text className="text-xs text-gray-400 text-center mt-4 mb-0">
					This is an automated notification from RVHelp. Please do not reply to
					this email.
				</Text>
			</Section>
		</BaseEmail>
	);
}
