import { Button, Section, Text } from "@react-email/components";
import { BaseEmail } from "./BaseEmail";

interface ReviewRequestEmailProps {
    customerFirstName: string;
    providerName: string;
    providerSlug: string;
    serviceType?: string;
}

export function ReviewRequestEmail({
    customerFirstName,
    providerName,
    serviceType = "service",
    providerSlug,
}: ReviewRequestEmailProps) {
    const reviewUrl = `${process.env.NEXT_PUBLIC_APP_URL}/providers/${providerSlug}/review`;

    return (
        <BaseEmail previewText={`How was your experience with ${providerName}?`}>
            <Section>
                <Text className="text-2xl font-bold">
                    How was your {serviceType} experience?
                </Text>

                <Text className="mt-4">
                    Hi {customerFirstName},
                </Text>

                <Text className="mt-4">
                    We hope your recent {serviceType} session with {providerName} was helpful.
                    Your feedback helps other RV owners find quality service providers and helps
                    providers improve their services.
                </Text>

                <Section className="mt-6">
                    <Text className="font-semibold">Would you take a moment to share your experience?</Text>
                    <Text>
                        Your review will help other RV owners make informed decisions and recognize
                        providers who deliver excellent service.
                    </Text>
                </Section>

                <Section className="mt-6 text-center">
                    <Button
                        href={reviewUrl}
                        className="bg-emerald-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-emerald-700"
                    >
                        Write a Review
                    </Button>
                </Section>

                <Section className="mt-6">
                    <Text className="font-semibold">What to include in your review:</Text>
                    <Text>• How responsive was the provider?</Text>
                    <Text>• Did they clearly explain the issue and solution?</Text>
                    <Text>• Was the {serviceType} session helpful?</Text>
                    <Text>• Would you recommend them to other RV owners?</Text>
                </Section>

                <Section className="mt-6 text-sm text-gray-600">
                    <Text>
                        Thank you for being part of the RV Help community. Your feedback makes a difference!
                    </Text>
                    <Text className="mt-2">
                        If you'd prefer not to receive review requests, you can update your
                        notification preferences in your account settings.
                    </Text>
                </Section>
            </Section>
        </BaseEmail>
    );
} 