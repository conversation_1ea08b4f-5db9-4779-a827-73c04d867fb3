import { formatCurrency } from "@/lib/utils";
import { But<PERSON>, Heading, Section, Text } from "@react-email/components";
import { format } from "date-fns";
import { BaseEmail } from "./BaseEmail";
import { emailStyles } from "./shared-styles";

interface KeystoneInvoiceNotificationEmailProps {
	platformInvoice: {
		id: string;
		invoice_number: number;
		amount: number;
		currency: string;
		items: Array<{
			description: string;
			quantity: number;
			unit_price: number;
			amount: number;
		}>;
		warranty_request_id?: string;
		notes?: string;
	};
	originalInvoice: {
		invoice_number: number;
		amount: number;
		customer_name: string;
		customer_email: string;
	};
	warrantyRequest?: {
		id: string;
		rv_model?: string;
		rv_year?: string;
	};
	provider: {
		business_name: string;
		first_name: string;
		last_name: string;
		email: string;
	};
	platformInvoiceUrl: string;
}

export function KeystoneInvoiceNotificationEmail({
	platformInvoice,
	originalInvoice,
	warrantyRequest,
	provider,
	platformInvoiceUrl
}: KeystoneInvoiceNotificationEmailProps) {
	const previewText = `RV Help - Warranty Portal Invoice #${platformInvoice.invoice_number} - ${formatCurrency(platformInvoice.amount)}`;
	const providerName =
		provider.business_name || `${provider.first_name} ${provider.last_name}`;
	const platformFeeItem = platformInvoice.items.find((item) =>
		item.description.includes("Platform Fee")
	);

	return (
		<BaseEmail previewText={previewText}>
			<Section style={emailStyles.container}>
				<Heading style={emailStyles.heading}>
					RV Help - Warranty Portal Invoice
				</Heading>

				<Text style={emailStyles.text}>
					A new warranty service request has been paid by RV Help and an invoice
					has been generated.
				</Text>

				<Section style={emailStyles.section}>
					<Text style={emailStyles.subheading}>
						Platform Fee Invoice Details
					</Text>
					<Text style={emailStyles.text}>
						<strong>Invoice #:</strong> {platformInvoice.invoice_number}
						<br />
						<strong>Amount:</strong> {formatCurrency(platformInvoice.amount)}
						<br />
						<strong>Date:</strong> {format(new Date(), "MMMM d, yyyy")}
						<br />
						{platformFeeItem && (
							<>
								<strong>Platform Fee:</strong>{" "}
								{formatCurrency(platformFeeItem.amount)}
								<br />
							</>
						)}
					</Text>
				</Section>

				<Section style={emailStyles.section}>
					<Text style={emailStyles.subheading}>
						Original Invoice Information
					</Text>
					<Text style={emailStyles.text}>
						<strong>Original Invoice #:</strong>{" "}
						{originalInvoice.invoice_number}
						<br />
						<strong>Original Amount:</strong>{" "}
						{formatCurrency(originalInvoice.amount)}
						<br />
						<strong>Customer:</strong> {originalInvoice.customer_name}
						<br />
						<strong>Customer Email:</strong> {originalInvoice.customer_email}
						<br />
					</Text>
				</Section>

				<Section style={emailStyles.section}>
					<Text style={emailStyles.subheading}>Service Provider</Text>
					<Text style={emailStyles.text}>
						<strong>Provider:</strong> {providerName}
						<br />
						<strong>Email:</strong> {provider.email}
						<br />
					</Text>
				</Section>

				{warrantyRequest && (
					<Section style={emailStyles.section}>
						<Text style={emailStyles.subheading}>Warranty Claim Details</Text>
						<Text style={emailStyles.text}>
							<strong>Warranty Request ID:</strong> {warrantyRequest.id}
							<br />
							{warrantyRequest.rv_model && (
								<>
									<strong>Model:</strong> {warrantyRequest.rv_model}
									<br />
								</>
							)}
							{warrantyRequest.rv_year && (
								<>
									<strong>Year:</strong> {warrantyRequest.rv_year}
									<br />
								</>
							)}
						</Text>
					</Section>
				)}

				<Section style={emailStyles.section}>
					<Text style={emailStyles.subheading}>Invoice Breakdown</Text>
					{platformInvoice.items.map((item, index) => (
						<Text key={index} style={emailStyles.text}>
							<strong>{item.description}</strong>
							<br />
							Quantity: {item.quantity} × {formatCurrency(item.unit_price)} ={" "}
							{formatCurrency(item.amount)}
						</Text>
					))}
					<Text style={{ ...emailStyles.text, ...emailStyles.subheading }}>
						<strong>Total: {formatCurrency(platformInvoice.amount)}</strong>
					</Text>
				</Section>

				<Section style={emailStyles.centered}>
					<Button href={platformInvoiceUrl} style={emailStyles.button}>
						View Platform Invoice
					</Button>
				</Section>

				<Text style={emailStyles.smallText}>
					This is an automated notification from the RV Help platform. The
					platform fee invoice has been created and is ready for review.
				</Text>

				{platformInvoice.notes && (
					<Section style={emailStyles.section}>
						<Text style={emailStyles.subheading}>Notes</Text>
						<Text style={emailStyles.text}>{platformInvoice.notes}</Text>
					</Section>
				)}
			</Section>
		</BaseEmail>
	);
}
