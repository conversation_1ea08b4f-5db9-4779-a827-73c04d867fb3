import { Button, Container, Section, Text } from "@react-email/components";
import { BaseEmail } from "./BaseEmail";

interface SmsVerificationRemovedEmailProps {
	providerName: string;
	businessName?: string;
	phone: string;
	listingId: string;
}

export function SmsVerificationRemovedEmail({
	providerName,
	businessName,
	phone,
	listingId
}: SmsVerificationRemovedEmailProps) {
	const displayName = businessName || providerName;

	return (
		<BaseEmail previewText="SMS Verification Removed - Action Required">
			<Container>
				<Section className="bg-red-50 border-l-4 border-red-400 rounded-lg p-6 mb-6">
					<Text className="text-xl text-red-800 mb-4 font-semibold">
						⚠️ SMS Verification Removed
					</Text>
					<Text className="text-red-700 mb-4">Hello {providerName},</Text>
					<Text className="text-red-700 mb-4">
						We've been unable to deliver SMS notifications to your phone number{" "}
						<strong>{phone}</strong> for your listing &quot;{displayName}&quot;
						after multiple attempts over the past 24 hours.
					</Text>
					<Text className="text-red-700 mb-4">
						To protect our SMS delivery reputation and ensure reliable service
						for all providers, we've temporarily removed SMS verification from
						your listing.
					</Text>
				</Section>

				<Section className="bg-gray-50 rounded-lg p-6 mb-6">
					<Text className="text-lg text-gray-900 mb-4 font-semibold">
						What This Means
					</Text>
					<Text className="text-gray-600 mb-2">
						• You will no longer receive SMS notifications for new leads
					</Text>
					<Text className="text-gray-600 mb-2">
						• Email notifications will continue to work normally
					</Text>
					<Text className="text-gray-600 mb-4">
						• Your listing remains active and visible to customers
					</Text>
				</Section>

				<Section className="bg-blue-50 rounded-lg p-6 mb-6">
					<Text className="text-lg text-blue-900 mb-4 font-semibold">
						How to Fix This
					</Text>
					<Text className="text-blue-700 mb-4">
						To restore SMS notifications, please:
					</Text>
					<Text className="text-blue-700 mb-2">
						1. Log in to your RV Help account and verify your phone number is
						correct and can receive SMS messages
					</Text>
					<Text className="text-blue-700 mb-2">
						2. Update your phone number if needed
					</Text>
					<Text className="text-blue-700 mb-4">
						3. Re-verify your SMS notifications in your listing settings
					</Text>

					<Section className="text-center my-6">
						<Button
							href={`${process.env.NEXT_PUBLIC_APP_URL}/provider/business/business-info?id=${listingId}`}
							className="bg-[#437F6B] text-sm text-white py-3 px-6 rounded-lg text-center"
						>
							Update SMS Settings
						</Button>
					</Section>
				</Section>

				<Section className="bg-gray-50 rounded-lg p-6">
					<Text className="text-sm text-gray-600 mb-2">
						<strong>Common causes for SMS delivery failures:</strong>
					</Text>
					<Text className="text-sm text-gray-600 mb-1">
						• Phone number was disconnected or changed
					</Text>
					<Text className="text-sm text-gray-600 mb-1">
						• SMS messages blocked by carrier
					</Text>
					<Text className="text-sm text-gray-600 mb-1">
						• Phone number format issues
					</Text>
					<Text className="text-sm text-gray-600">
						• Phone service provider restrictions
					</Text>
				</Section>

				<Text className="text-xs text-gray-400 text-center mt-6 mb-0">
					If you continue to have issues with SMS verification, please contact
					our support team.
				</Text>
			</Container>
		</BaseEmail>
	);
}
