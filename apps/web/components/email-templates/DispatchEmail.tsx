import config from "@/config";
import { Button, Heading, Section, Text } from "@react-email/components";
import React from "react";
import { BaseEmail } from "./BaseEmail";
import { emailStyles } from "./shared-styles";

interface DispatchEmailProps {
	title: string;
	subject: string;
	body: string;
	providerName?: string;
	businessName?: string;
	cityName?: string;
}

export const DispatchEmail: React.FC<DispatchEmailProps> = ({
	title,
	subject,
	body,
	providerName,
	businessName
}) => {
	const greeting = providerName || businessName || "RV Service Provider";

	return (
		<BaseEmail previewText={subject}>
			<Section style={emailStyles.container}>
				<Heading style={emailStyles.heading}>{title}</Heading>

				<Text style={emailStyles.subheading}>Hello {greeting},</Text>

				<div
					style={emailStyles.text}
					dangerouslySetInnerHTML={{ __html: body }}
				/>

				<Section style={emailStyles.alertInfo}>
					<Text style={emailStyles.smallText}>
						You&apos;re receiving this email because one or more of the RV
						owners above have not been able to find a provider near them for an
						emergency service.
					</Text>

					<Text style={emailStyles.smallText}>
						You can opt out of receiving these emails by clicking the button
						below.
					</Text>
					<Button
						href={`${config.appUrl}/provider/business/settings?setting=dispatch-notifications`}
						style={emailStyles.buttonSmall}
					>
						Update Notification Preferences
					</Button>
				</Section>

				<Text style={emailStyles.footer}>
					Best regards,
					<br />
					The RV Help Team
				</Text>
			</Section>
		</BaseEmail>
	);
};

export default DispatchEmail;
