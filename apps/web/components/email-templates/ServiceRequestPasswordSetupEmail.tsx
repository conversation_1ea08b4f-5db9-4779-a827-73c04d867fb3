import { But<PERSON>, Heading, Hr, Section, Text } from "@react-email/components";
import { BaseEmail } from "./BaseEmail";

interface ServiceRequestPasswordSetupEmailProps {
	firstName: string;
	lastName: string;
	serviceRequestId: string;
	passwordSetupLink: string;
	rvDetails?: {
		year?: string;
		make?: string;
		model?: string;
		type?: string;
	};
	category?: string;
	message?: string;
}

export const serviceRequestPasswordSetupText = (
	firstName: string,
	lastName: string,
	serviceRequestId: string,
	passwordSetupLink: string,
	rvDetails?: any,
	category?: string
) => `Hi ${firstName},

You have a service request for your RV that needs your attention!

${
	rvDetails
		? `RV Details:
${rvDetails.year || ""} ${rvDetails.make || ""} ${rvDetails.model || ""}
Service Category: ${category || "RV Service"}`
		: ""
}

To access your service request workroom and manage your repair request, you'll need to set up your password first.

Set up your password here: ${passwordSetupLink}

From your service request workroom, you'll be able to:
- Message providers directly about your repair needs
- Invite additional providers to quote your job
- View and compare detailed quotes
- Track your repair progress in real-time
- Schedule service appointments
- Access your complete service history

This link will expire in 24 hours. If you need a new password setup link, please contact support.

Best regards,
The RV Help Team`;

export default function ServiceRequestPasswordSetupEmail({
	firstName,
	lastName,
	serviceRequestId,
	passwordSetupLink,
	rvDetails,
	category,
	message
}: ServiceRequestPasswordSetupEmailProps) {
	return (
		<BaseEmail
			previewText={`Set up your password to access your RV service request workroom`}
		>
			<Section>
				<Heading className="text-xl font-bold text-gray-900 mb-4">
					Your RV Service Request Workroom is Ready
				</Heading>

				<Text className="text-gray-700 mb-4">Hi {firstName},</Text>

				<Text className="text-gray-700 mb-4">
					You have a service request for your RV that needs your attention! Your
					service request workroom is ready for you to manage your repair needs.
				</Text>

				{rvDetails && (
					<div className="bg-gray-50 p-4 rounded-lg mb-4">
						<Text className="text-sm font-medium text-gray-900 mb-2">
							Your RV Details:
						</Text>
						<Text className="text-sm text-gray-700 mb-1">
							{[rvDetails.year, rvDetails.make, rvDetails.model]
								.filter(Boolean)
								.join(" ")}
						</Text>
						{category && (
							<Text className="text-sm text-gray-700">
								Service Category: {category}
							</Text>
						)}
					</div>
				)}

				{message && (
					<div className="bg-blue-50 p-4 rounded-lg mb-4">
						<Text className="text-sm font-medium text-blue-900 mb-2">
							Your Service Request:
						</Text>
						<Text className="text-sm text-blue-800">
							"{message.substring(0, 200)}
							{message.length > 200 ? "..." : ""}"
						</Text>
					</div>
				)}

				<Text className="text-gray-700 mb-4">
					To access your service request workroom and manage your repair
					request, you'll need to set up your password first.
				</Text>

				<Button
					className="bg-[#437F6B] text-white py-3 px-6 text-base rounded-lg mb-4"
					href={passwordSetupLink}
				>
					Set Up Your Password
				</Button>

				<Hr className="my-4" />

				<Text className="text-sm text-gray-600 mb-2">
					<strong>
						From your service request workroom, you'll be able to:
					</strong>
				</Text>
				<Text className="text-sm text-gray-600 mb-1">
					✓ Message providers directly about your repair needs
				</Text>
				<Text className="text-sm text-gray-600 mb-1">
					✓ Invite additional providers to quote your job
				</Text>
				<Text className="text-sm text-gray-600 mb-1">
					✓ View and compare detailed quotes
				</Text>
				<Text className="text-sm text-gray-600 mb-1">
					✓ Track your repair progress in real-time
				</Text>
				<Text className="text-sm text-gray-600 mb-1">
					✓ Schedule service appointments
				</Text>
				<Text className="text-sm text-gray-600 mb-4">
					✓ Access your complete service history
				</Text>

				<Text className="text-xs text-gray-500">
					This link will expire in 24 hours. If you need a new password setup
					link, please contact our support team.
				</Text>

				<Text className="text-xs text-gray-500 mt-4">
					Service Request ID: {serviceRequestId}
				</Text>
			</Section>
		</BaseEmail>
	);
}
