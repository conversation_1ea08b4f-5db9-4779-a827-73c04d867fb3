import { Heading, Section, Text } from "@react-email/components";
import { BaseEmail } from "./BaseEmail";

interface EmergencyDispatchEmailProps {
	requestId: string;
	firstName: string;
	lastName: string;
	email: string;
	phone: string;
	location: {
		address: string;
		latitude: number;
		longitude: number;
	};
	currentLocation?: string;
	emergencyDescription: string;
	uploadedFiles?: Array<{
		name: string;
		size: string;
		type: string;
		url: string;
	}>;
}

export const EmergencyDispatchEmail: React.FC<EmergencyDispatchEmailProps> = ({
	requestId,
	firstName,
	lastName,
	email,
	phone,
	location,
	currentLocation,
	emergencyDescription,
	uploadedFiles
}) => {
	return (
		<BaseEmail previewText={`EMERGENCY DISPATCH: ${firstName} ${lastName} - ${location.address}`}>
			<Section className="flex flex-col gap-4">
				<Heading className="text-2xl font-bold text-center text-red-600">
					🚨 EMERGENCY RV DISPATCH REQUEST
				</Heading>

				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
					<Text className="text-lg font-bold text-red-800 mb-2">
						Customer Information:
					</Text>
					<Text className="text-gray-700 mb-1">
						<strong>Name:</strong> {firstName} {lastName}
					</Text>
					<Text className="text-gray-700 mb-1">
						<strong>Email:</strong> {email}
					</Text>
					<Text className="text-gray-700 mb-1">
						<strong>Phone:</strong> {phone}
					</Text>
					<Text className="text-gray-700 mb-1">
						<strong>Location:</strong> {location.address}
					</Text>
					{currentLocation && (
						<Text className="text-gray-700 mb-1">
							<strong>Current Location:</strong> {currentLocation}
						</Text>
					)}
				</div>

				<div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
					<Text className="text-lg font-bold text-orange-800 mb-2">
						Emergency Description:
					</Text>
					<Text className="text-gray-700 whitespace-pre-wrap">
						{emergencyDescription}
					</Text>
				</div>

				{uploadedFiles && uploadedFiles.length > 0 && (
					<div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
						<Text className="text-lg font-bold text-blue-800 mb-2">
							Uploaded Files:
						</Text>
						{uploadedFiles.map((file, index) => (
							<Text key={index} className="text-gray-700 mb-1">
								📎 {file.name} ({file.size})
							</Text>
						))}
					</div>
				)}

				<div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
					<Text className="text-lg font-bold text-green-800 mb-2">
						Next Steps:
					</Text>
					<Text className="text-gray-700 mb-1">1. Review the emergency details above</Text>
					<Text className="text-gray-700 mb-1">2. Contact available RV technicians within 50 miles of {location.address}</Text>
					<Text className="text-gray-700 mb-1">3. Coordinate with responding technicians</Text>
					<Text className="text-gray-700 mb-1">4. Follow up with customer within 1 business day</Text>
					<Text className="text-gray-700 mb-1">
						<strong>Request ID:</strong> {requestId}
					</Text>
				</div>

				<hr className="my-6" />
				<Text className="text-sm text-gray-600">
					This emergency dispatch request was submitted by a Pro member. Please prioritize and respond within 1 business day.
				</Text>
			</Section>
		</BaseEmail>
	);
}; 