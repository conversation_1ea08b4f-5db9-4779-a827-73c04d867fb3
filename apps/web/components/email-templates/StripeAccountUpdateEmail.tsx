import {
	<PERSON>,
	<PERSON><PERSON>,
	Container,
	Head,
	<PERSON><PERSON>,
	Html,
	Link,
	Preview,
	Section,
	Text
} from "@react-email/components";

interface StripeAccountUpdateEmailProps {
	providerName: string;
	updateLink: string;
}

export const StripeAccountUpdateEmail = ({
	providerName = "Provider",
	updateLink = "https://rvhelp.com/provider/billing/settings"
}: StripeAccountUpdateEmailProps) => (
	<Html>
		<Head />
		<Preview>Action Required: Update Your Stripe Account Information</Preview>
		<Body style={main}>
			<Container style={container}>
				<Heading style={h1}>
					Action Required: Update Your Stripe Account
				</Heading>

				<Text style={text}>Hi {providerName},</Text>

				<Text style={text}>
					We've detected that your Stripe account needs some additional
					information to continue processing payments and payouts without
					delays.
				</Text>

				<Text style={text}>
					<strong>What this means:</strong>
				</Text>
				<Text style={text}>
					• Your payouts may be delayed until this is resolved
					<br />
					• You may not be able to receive payments for completed jobs
					<br />• This is a routine requirement from <PERSON><PERSON> to ensure account
					security
				</Text>

				<Section style={buttonContainer}>
					<Button style={button} href={updateLink}>
						Update Stripe Account
					</Button>
				</Section>

				<Text style={text}>
					Clicking the button above will take you directly to Stripe where you
					can provide the required information. This usually takes just a few
					minutes to complete.
				</Text>

				<Text style={text}>
					<strong>Need help?</strong> If you have any questions about this
					process, please contact our support team.
				</Text>

				<Text style={text}>
					Best regards,
					<br />
					The RVHelp Team
				</Text>

				<Text style={footer}>
					If the button above doesn't work, you can also copy and paste this
					link into your browser: <Link href={updateLink}>{updateLink}</Link>
				</Text>
			</Container>
		</Body>
	</Html>
);

const main = {
	backgroundColor: "#f6f9fc",
	fontFamily:
		'-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif'
};

const container = {
	backgroundColor: "#ffffff",
	margin: "0 auto",
	padding: "20px 0 48px",
	marginBottom: "64px"
};

const h1 = {
	color: "#333",
	fontSize: "24px",
	fontWeight: "bold",
	margin: "40px 0",
	padding: "0",
	textAlign: "center" as const
};

const text = {
	color: "#333",
	fontSize: "16px",
	lineHeight: "24px",
	margin: "16px 0"
};

const buttonContainer = {
	textAlign: "center" as const,
	margin: "32px 0"
};

const button = {
	backgroundColor: "#059669", // Deep green brand color
	borderRadius: "8px",
	color: "#fff",
	fontSize: "16px",
	fontWeight: "bold",
	textDecoration: "none",
	textAlign: "center" as const,
	display: "inline-block",
	padding: "12px 24px"
};

const footer = {
	color: "#666",
	fontSize: "14px",
	lineHeight: "20px",
	margin: "32px 0 0 0",
	textAlign: "center" as const
};

export default StripeAccountUpdateEmail;
