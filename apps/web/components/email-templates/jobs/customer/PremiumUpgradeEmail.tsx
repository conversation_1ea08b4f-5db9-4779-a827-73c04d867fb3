import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Text,
  Hr,
  Button
} from "@react-email/components";

interface PremiumUpgradeEmailProps {
  customerName: string;
  adminMessage?: string;
  jobId: string;
  workroomUrl?: string;
}

export const PremiumUpgradeEmail = ({
  customerName,
  adminMessage,
  jobId,
  workroomUrl
}: PremiumUpgradeEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>Your RV service request has been upgraded to premium status!</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Header */}
          <Section style={header}>
            <Heading style={title}>
              🎉 Your Service Request Has Been Upgraded to Premium!
            </Heading>
          </Section>

          {/* Main Content */}
          <Section style={content}>
            <Text style={text}>Hi {customerName},</Text>
            
            <Text style={text}>
              Great news! Your RV service request has been upgraded to premium status, 
              which means you now have access to enhanced features and priority support.
            </Text>

            {/* Premium Benefits Box */}
            <Section style={benefitsBox}>
              <Heading as="h3" style={benefitsTitle}>
                Premium Benefits Include:
              </Heading>
              <ul style={benefitsList}>
                <li style={benefitItem}>Priority placement with providers</li>
                <li style={benefitItem}>Enhanced visibility in the marketplace</li>
                <li style={benefitItem}>Faster response times</li>
                <li style={benefitItem}>Dedicated support assistance</li>
              </ul>
            </Section>

            {/* Admin Message (if provided) */}
            {adminMessage && (
              <Section style={adminMessageBox}>
                <Heading as="h4" style={adminMessageTitle}>
                  Message from RV Help Team:
                </Heading>
                <Text style={adminMessageText}>
                  {adminMessage}
                </Text>
              </Section>
            )}

            {/* Call to Action */}
            <Section style={ctaBox}>
              <Heading as="h3" style={ctaTitle}>
                Ready to Find More Providers?
              </Heading>
              <Text style={ctaText}>
                With your premium upgrade, you now have access to invite additional providers 
                and get even faster responses for your RV service needs.
              </Text>
              <Button style={ctaButton} href={workroomUrl || `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/jobs/${jobId}`}>
                Visit Your Workroom
              </Button>
            </Section>

            <Text style={text}>
              Your service request will continue to be processed with these premium 
              enhancements. The workroom is where you can track progress, communicate 
              with providers, and invite additional technicians if needed.
            </Text>

            <Text style={text}>
              If you have any questions, please don't hesitate to reach out to our 
              support team.
            </Text>

            <Hr style={divider} />

            <Text style={signature}>
              Best regards,<br />
              The RV Help Team
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: "#ffffff",
  fontFamily: 'system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "600px",
};

const header = {
  marginBottom: "32px",
};

const title = {
  fontSize: "24px",
  lineHeight: "1.3",
  fontWeight: "700",
  color: "#2563eb",
  margin: "0 0 16px",
  textAlign: "center" as const,
};

const content = {
  padding: "0 20px",
};

const text = {
  fontSize: "16px",
  lineHeight: "1.6",
  color: "#374151",
  margin: "0 0 16px",
};

const benefitsBox = {
  backgroundColor: "#fef3c7",
  borderLeft: "4px solid #f59e0b",
  padding: "16px",
  margin: "20px 0",
  borderRadius: "4px",
};

const benefitsTitle = {
  fontSize: "18px",
  fontWeight: "600",
  color: "#92400e",
  margin: "0 0 12px",
};

const benefitsList = {
  margin: "0",
  padding: "0 0 0 20px",
  color: "#92400e",
};

const benefitItem = {
  fontSize: "15px",
  lineHeight: "1.5",
  margin: "0 0 8px",
};

const adminMessageBox = {
  backgroundColor: "#f3f4f6",
  borderRadius: "8px",
  padding: "16px",
  margin: "20px 0",
};

const adminMessageTitle = {
  fontSize: "16px",
  fontWeight: "600",
  color: "#374151",
  margin: "0 0 8px",
};

const adminMessageText = {
  fontSize: "15px",
  lineHeight: "1.5",
  color: "#374151",
  margin: "0",
};

const divider = {
  borderColor: "#e5e7eb",
  margin: "32px 0",
};

const signature = {
  fontSize: "16px",
  lineHeight: "1.6",
  color: "#374151",
  margin: "0",
};

const ctaBox = {
  backgroundColor: "#2563eb",
  borderRadius: "8px",
  padding: "24px",
  margin: "32px 0",
  textAlign: "center" as const,
};

const ctaTitle = {
  fontSize: "20px",
  fontWeight: "700",
  color: "#ffffff",
  margin: "0 0 12px",
};

const ctaText = {
  fontSize: "16px",
  lineHeight: "1.5",
  color: "#e5e7eb",
  margin: "0 0 20px",
};

const ctaButton = {
  backgroundColor: "#ffffff",
  color: "#2563eb",
  fontSize: "16px",
  fontWeight: "600",
  padding: "12px 24px",
  borderRadius: "6px",
  textDecoration: "none",
  display: "inline-block",
  border: "none",
}; 