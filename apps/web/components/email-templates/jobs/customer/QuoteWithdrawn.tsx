import {
  Container,
  Heading,
  Link,
  Section,
  Text,
} from "@react-email/components";
import config from "@/config";
import { BaseEmail } from "../../BaseEmail";
import { emailStyles } from "../../shared-styles";

interface QuoteWithdrawnEmailProps {
  customerName: string;
  providerName: string;
  message: string;
  jobId: string;
  rejectionReason?: "too_busy" | "schedule_conflict" | "outside_travel_area" | "not_a_good_fit" | "other" | null;
}

const rejectionReasonText = {
  too_busy: "they are currently too busy to take on new work.",
  schedule_conflict: "they have a scheduling conflict.",
  outside_travel_area: "this request is outside of their travel area.",
  not_a_good_fit: "this is not the type of work they perform.",
  other: "of another reason.",
}

export const QuoteWithdrawnEmail = ({
  customerName,
  providerName,
  message,
  jobId,
  rejectionReason
}: QuoteWithdrawnEmailProps) => {
  const isWithdrawal = !rejectionReason;

  return (
    <BaseEmail previewText={`RV Help: An update on your service request`}>
      <Container style={emailStyles.container}>
        <Heading style={emailStyles.heading}>
          {isWithdrawal ? "Proposal Withdrawn" : "Proposal Declined"}
        </Heading>

        <Section style={emailStyles.section}>
          <Text style={emailStyles.text}>
            Hi {customerName},
          </Text>
          
          <Text style={emailStyles.text}>
            We wanted to let you know that <strong>{providerName}</strong> has {isWithdrawal ? "withdrawn their proposal" : "declined to submit a proposal"} for your recent service request.
            {rejectionReason && (
              <>
                <br /><br />
                The reason provided was that {rejectionReasonText[rejectionReason]}
              </>
            )}
          </Text>
          
          {message && (
            <Text style={emailStyles.messageText}>
              {message}
            </Text>
          )}
        </Section>

        <Text style={emailStyles.text}>
          We will continue to search for other qualified technicians in your area. You can view the status of your request and any other proposals you receive by clicking the button below.
        </Text>

        <Section style={emailStyles.centered}>
          <Link
            href={`${config.appUrl}/service-requests/${jobId}#quotes`}
            style={emailStyles.button}
          >
            View Service Request
          </Link>
        </Section>

        <Text style={emailStyles.text}>
          If you need help finding another service provider, our team is here to assist you.
        </Text>

        <Text style={emailStyles.footer}>
          Best regards,<br />
          The RV Help Team
        </Text>
      </Container>
    </BaseEmail>
  );
};