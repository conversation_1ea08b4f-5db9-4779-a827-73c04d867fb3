import config from "@/config";
import { JobWithUserAndLocation } from "@/types/global";
import {
	Button,
	Container,
	Heading,
	Hr,
	Section,
	Text
} from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";

export const NoResponseEmail = ({
	customerName,
	job,
	membershipLevel = "FREE"
}: {
	customerName: string;
	job: JobWithUserAndLocation;
	membershipLevel?: "FREE" | "STANDARD" | "PREMIUM";
}) => {
	const isPro = membershipLevel === "STANDARD" || membershipLevel === "PREMIUM";
	const isPremium = membershipLevel === "PREMIUM";

	return (
		<BaseEmail previewText="We're sorry no one has responded to your service request yet - but we have options for you!">
			<Container className="mx-auto">
				<Heading className="text-xl font-bold text-gray-900 my-4 text-center">
					💡 No one to help with your service request? We Have Options for You!
				</Heading>

				<Text className="text-base text-gray-700 m-0 mb-4">
					Hi {customerName},
				</Text>

				<Text className="text-base text-gray-700 m-0 mb-4">
					We wanted to reach out because it doesn't look like anyone has
					accepted your service request yet. Don't worry - this happens
					sometimes, and we have{" "}
					{isPro ? " several great options" : " great options"} to help you get
					the service you need. Here's what you can do:
				</Text>

				{/* Options Section */}
				<Section className="bg-blue-50 p-6 rounded-lg mt-6 mb-6">
					<Heading className="text-lg font-semibold text-blue-900 mb-3 m-0">
						Here's what you can do:
					</Heading>

					<div className="space-y-3">
						{isPro ? (
							<>
								{/* PRO USER OPTIONS */}
								<ul className="space-y-2 text-sm text-gray-700">
									<li className="flex items-start">
										<span className="text-blue-600 mr-2 mt-1">•</span>
										<div>
											<span className="font-medium text-gray-900">
												Withdraw & Invite More Providers
											</span>
											<br />
											Return to your workroom to withdraw current invitations
											and invite up to 5 new providers
										</div>
									</li>
									<li className="flex items-start">
										<span className="text-blue-600 mr-2 mt-1">•</span>
										<div>
											<span className="font-medium text-gray-900">
												Contact Our Support Team
											</span>
											<br />
											Get personalized help from our concierge team to find the
											right provider for your needs
										</div>
									</li>
									{isPremium ? (
										<li className="flex items-start">
											<span className="text-blue-600 mr-2 mt-1">•</span>
											<div>
												<span className="font-medium text-gray-900">
													Request Virtual Diagnosis
												</span>
												<br />
												Get expert remote diagnosis to understand your issue
												before booking service
											</div>
										</li>
									) : (
										<li className="flex items-start">
											<span className="text-blue-600 mr-2 mt-1">•</span>
											<div>
												<span className="font-medium text-gray-900">
													Request Pre-Service Troubleshooting Call
												</span>
												<br />
												Get a quick troubleshooting call to help determine the
												best next steps
											</div>
										</li>
									)}
									<li className="flex items-start">
										<span className="text-blue-600 mr-2 mt-1">•</span>
										<div>
											<span className="font-medium text-gray-900">
												Emergency Dispatch
											</span>
											<br />
											For urgent situations, contact our emergency dispatch team
											for immediate assistance
										</div>
									</li>
									<li className="flex items-start">
										<span className="text-blue-600 mr-2 mt-1">•</span>
										<div>
											<span className="font-medium text-gray-900">
												Search More Providers
											</span>
											<br />
											Browse our directory and contact providers directly
										</div>
									</li>
								</ul>
							</>
						) : (
							<>
								{/* FREE USER OPTIONS */}
								<ul className="space-y-2 text-sm text-gray-700">
									<li className="flex items-start">
										<span className="text-blue-600 mr-2 mt-1">•</span>
										<div>
											<span className="font-medium text-gray-900">
												Search for Another Provider
											</span>
											<br />
											Browse our directory and contact providers directly - it's
											completely free
										</div>
									</li>
									<li className="flex items-start">
										<span className="text-blue-600 mr-2 mt-1">•</span>
										<div>
											<span className="font-medium text-gray-900">
												Upgrade to Pro for Better Results
											</span>
											<br />
											Get faster responses by inviting up to 5 providers, plus
											access to unlimited pre-service diagnostic calls and
											nationwide provider discounts
										</div>
									</li>
								</ul>
							</>
						)}
					</div>
				</Section>

				{/* Pro Benefits for Free Users */}
				{!isPro && (
					<Section className="bg-green-50 p-6 rounded-lg mt-6 mb-6">
						<Heading className="text-lg font-semibold text-green-900 mb-3 m-0">
							🚀 Pro Members Get Better Results:
						</Heading>
						<ul className="space-y-2 text-sm text-gray-700">
							<li className="flex items-start">
								<span className="text-green-600 mr-2 mt-1">•</span>
								<div>
									<span className="font-medium text-gray-900">
										Invite Up to 5 Providers
									</span>
									<br />
									Automatically request availability from multiple providers to
									get more responses
								</div>
							</li>
							<li className="flex items-start">
								<span className="text-green-600 mr-2 mt-1">•</span>
								<div>
									<span className="font-medium text-gray-900">
										Unlimited Pre-Service Diagnostic Calls
									</span>
									<br />
									10-15 minute troubleshooting calls with local techs before
									scheduling service
								</div>
							</li>
							<li className="flex items-start">
								<span className="text-green-600 mr-2 mt-1">•</span>
								<div>
									<span className="font-medium text-gray-900">
										Nationwide Provider Discounts
									</span>
									<br />
									Save up to $80 per service call with 25% off dispatch fees and
									10% off hourly rates
								</div>
							</li>
							<li className="flex items-start">
								<span className="text-green-600 mr-2 mt-1">•</span>
								<div>
									<span className="font-medium text-gray-900">
										Maintenance Tracker & Premium Resources
									</span>
									<br />
									Track your RV maintenance and access exclusive guides and
									troubleshooting resources
								</div>
							</li>
						</ul>
					</Section>
				)}

				{/* Call-to-Action Buttons */}
				<Section className="text-center mb-6">
					{isPro ? (
						<>
							<Button
								href={`${config.appUrl}/service-requests/${job.id}`}
								className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold mr-4 mb-3 inline-block text-decoration-none"
							>
								Return to Workroom
							</Button>

							<Button
								href={`${config.appUrl}/support`}
								className="bg-green-600 text-white px-8 py-3 rounded-lg font-semibold mr-4 mb-3 inline-block text-decoration-none"
							>
								Contact Support
							</Button>

							<Button
								href={`${config.appUrl}/${job.category === "rv-inspection" ? "rv-inspection" : "mobile-rv-repair"}`}
								className="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold inline-block text-decoration-none"
							>
								Search Providers
							</Button>
						</>
					) : (
						<>
							<Button
								href={`${config.appUrl}/pro-membership`}
								className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold mr-4 mb-3 inline-block text-decoration-none"
							>
								Learn About Pro Membership
							</Button>

							<Button
								href={`${config.appUrl}/${job.category === "rv-inspection" ? "rv-inspection" : "mobile-rv-repair"}`}
								className="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold inline-block text-decoration-none"
							>
								Find Another Provider
							</Button>
						</>
					)}
				</Section>

				<Hr className="border-gray-200 my-6" />

				<Text className="text-sm text-gray-500 text-center m-0">
					Best regards,
					<br />
					The RV Help Team
				</Text>
			</Container>
		</BaseEmail>
	);
};
