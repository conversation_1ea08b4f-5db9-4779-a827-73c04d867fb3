import config from "@/config";
import { getCategoryName } from "@/lib/categories";
import { Button, Container, Section, Text } from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";

interface BatchInvitationConfirmationEmailProps {
	firstName: string;
	lastName: string;
	providers: Array<{
		name: string;
		location?: {
			city?: string;
			state?: string;
		};
	}>;
	message: string;
	contactPreference: "sms" | "phone" | "email";
	email: string;
	phone?: string;
	category: string;
	jobId: string;
}

export const BatchInvitationConfirmationEmail = ({
	firstName,
	lastName,
	providers,
	message,
	contactPreference,
	email,
	phone,
	category,
	jobId
}: BatchInvitationConfirmationEmailProps) => {
	const fullName = `${firstName} ${lastName}`;
	const contactMethodText =
		contactPreference === "sms"
			? "Text Message"
			: contactPreference === "phone"
				? "Phone Call"
				: "Email";

	const providerCount = providers.length;
	const providerText = providerCount === 1 ? "provider" : "providers";

	return (
		<BaseEmail
			previewText={`Your message has been sent to ${providerCount} ${providerText}`}
		>
			<Container className="mx-auto">
				<Text className="text-lg font-bold text-gray-900 mb-2">
					Messages Sent Successfully
				</Text>

				<Text className="text-sm text-gray-700 m-0">Hi {firstName},</Text>
				<Text className="text-sm text-gray-700 m-0 mt-2">
					Your message has been sent to{" "}
					<span className="font-semibold">
						{providerCount} {providerText}
					</span>
				</Text>

				<Text className="font-semibold text-gray-900 m-0 mt-4 mb-1">
					Providers Contacted
				</Text>
				<Section className="bg-gray-100 p-4 rounded-lg mt-0">
					{providers.map((provider, index) => {
						const locationText =
							provider.location?.city && provider.location?.state
								? ` (${provider.location.city}, ${provider.location.state})`
								: "";
						return (
							<Text key={index} className="text-sm text-gray-700 m-0 mt-1">
								• {provider.name}
								{locationText}
							</Text>
						);
					})}
				</Section>

				<Text className="font-semibold text-gray-900 m-0 mt-4 mb-1">
					Your Contact Information
				</Text>
				<Section className="bg-gray-100 p-4 rounded-lg mt-0">
					<Text className="text-sm text-gray-700 m-0">
						<span className="font-medium">Name:</span> {fullName}
					</Text>
					<Text className="text-sm text-gray-700 m-0 mt-1">
						<span className="font-medium">Email:</span> {email}
					</Text>
					{phone && (
						<Text className="text-sm text-gray-700 m-0 mt-1">
							<span className="font-medium">Phone:</span> {phone}
						</Text>
					)}
					<Text className="text-sm text-gray-700 m-0 mt-1">
						<span className="font-medium">Preferred Contact Method:</span>{" "}
						{contactMethodText}
					</Text>
					<Text className="text-sm text-gray-700 m-0 mt-1">
						<span className="font-medium">Service Type:</span>{" "}
						{getCategoryName(category)}
					</Text>
				</Section>

				<Text className="font-semibold text-gray-900 m-0 mt-4 mb-1">
					Your Message
				</Text>
				<Section className="bg-gray-100 p-4 rounded-lg mt-0">
					<Text className="text-sm whitespace-pre-wrap m-0 text-gray-700">
						{message}
					</Text>
				</Section>

				{/* link to the job */}
				<Text className="text-sm text-gray-700 m-0 mt-4">
					You can view the details of this service request here:
				</Text>
				<Button
					className="bg-[#437F6B] text-sm text-white py-2 px-6 rounded-lg text-center mt-4"
					href={`${config.appUrl}/service-requests/${jobId}`}
				>
					View Service Request Details
				</Button>

				<Text className="text-xs text-gray-400 text-center mt-2 mb-0">
					This is an automated email. Please do not reply to this message as it
					is not monitored.
				</Text>
			</Container>
		</BaseEmail>
	);
};
