import {
  Container,
  Heading,
  Link,
  Text,
  Section,
  Hr,
} from "@react-email/components";
import * as React from "react";
import { BaseEmail } from "../../BaseEmail";
import { formatSchedulingTimeframe } from "@/lib/utils";
import { emailStyles } from "../../shared-styles";

interface QuoteReceivedEmailProps {
  customerName: string;
  providerName: string;
  providerLocation?: string;
  jobId: string;
  schedulingTimeframe?: string;
  message?: string;
}

export const QuoteReceivedEmail = ({
  customerName,
  providerName,
  providerLocation,
  jobId,
  schedulingTimeframe,
  message,
}: QuoteReceivedEmailProps) => {
  const previewText = `${providerName} has responded to your service request`;

  return (
    <BaseEmail previewText={previewText}>
      <Container style={emailStyles.container}>
        <Heading style={emailStyles.heading}>New Response Received!</Heading>
        
        <Text style={emailStyles.text}>Hi {customerName},</Text>
        
        <Text style={emailStyles.text}>
          Great news! <strong>{providerName}</strong>{providerLocation && ` from ${providerLocation}`} has responded to your service request and is available to help.
        </Text>

        {schedulingTimeframe && schedulingTimeframe !== "need_more_info" && (
          <Text style={emailStyles.text}>
            <strong>Availability:</strong> They can schedule your service {formatSchedulingTimeframe(schedulingTimeframe)}.
          </Text>
        )}

        {schedulingTimeframe === "need_more_info" && (
          <Text style={emailStyles.text}>
            <strong>Availability:</strong> Provider needs more information to schedule your service.
          </Text>
        )}

        {message && (
          <Section style={emailStyles.messageBox}>
            <Text style={emailStyles.messageTitle}>Message from {providerName}:</Text>
            <Text style={emailStyles.messageText}>"{message}"</Text>
          </Section>
        )}

        <Hr style={emailStyles.hr} />

        <Text style={emailStyles.text}>
          You can now review their profile, compare with other providers, and decide if you'd like to move forward.
        </Text>

        <Link
          href={`${process.env.NEXT_PUBLIC_APP_URL}/service-requests/${jobId}`}
          style={emailStyles.buttonBlock}
        >
          View Response & Respond
        </Link>

        <Text style={emailStyles.footer}>
          Best regards,
          <br />
          The RV Help Team
        </Text>
      </Container>
    </BaseEmail>
  );
};

export default QuoteReceivedEmail; 