import { getCategoryName } from "@/lib/categories";
import { RejectionReasonType } from "@prisma/client";
import { Container, Section, Text } from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";
import { emailStyles } from "../../shared-styles";

interface LeadRejectionEmailProps {
	firstName: string;
	lastName: string;
	providerName: string;
	providerLocation?: {
		city?: string;
		state?: string;
	};
	category: string;
	rejectionReason: {
		type: RejectionReasonType;
		details?: string;
	};
}

export const LeadRejectionEmail = ({
	firstName,
	providerName,
	providerLocation,
	category,
	rejectionReason
}: LeadRejectionEmailProps) => {
	const providerLocationText =
		providerLocation?.city && providerLocation?.state
			? ` in ${providerLocation.city}, ${providerLocation.state}`
			: "";

	// Format the rejection reason for display
	const getRejectionReasonText = () => {
		switch (rejectionReason.type) {
			case RejectionReasonType.TOO_BUSY:
				return "The provider is currently too busy and unable to take on additional work at this time.";
			case RejectionReasonType.NOT_A_GOOD_FIT:
				return "The provider is not a good fit or does not offer the type of service you are looking for.";
			case RejectionReasonType.SCHEDULE_CONFLICT:
				return "The provider has a scheduling conflict and is unable to help with your request.";
			case RejectionReasonType.OUTSIDE_TRAVEL_AREA:
				return "Your location is outside of the provider's service area.";
			case RejectionReasonType.OTHER:
				return (
					rejectionReason.details ||
					"The provider is unable to help with your request at this time."
				);
			default:
				return "The provider is unable to help with your request at this time.";
		}
	};

	return (
		<BaseEmail
			previewText={`Update on your service request to ${providerName}`}
		>
			<Container style={emailStyles.container}>
				<Text style={emailStyles.subheading}>Service Request Update</Text>

				<Text style={emailStyles.text}>Hi {firstName},</Text>
				<Text style={emailStyles.text}>
					We wanted to let you know that <strong>{providerName}</strong>
					{providerLocationText} is unable to help with your service request at
					this time.
				</Text>

				<Text style={emailStyles.text}>
					<strong>Reason</strong>
				</Text>
				<Section style={emailStyles.section}>
					<Text style={emailStyles.text}>{getRejectionReasonText()}</Text>
					{rejectionReason.details && (
						<Text style={emailStyles.text}>
							<strong>Provider Comments:</strong> {rejectionReason.details}
						</Text>
					)}
				</Section>

				<Text style={emailStyles.text}>
					You can visit our website to search for other providers in your area
					who may be able to assist you with your {getCategoryName(category)}{" "}
					needs.
				</Text>

				<Section style={emailStyles.alertInfo}>
					<Text style={emailStyles.text}>
						<strong>RV Help Can Help Get You Connected</strong>
					</Text>
					<Text style={emailStyles.text}>
						As part of our RV Help Pro Membership, we offer a concierge service
						where our team will personally match you with a qualified technician
						in your area. If you're interested in this service, please visit our
						pro membership page at{" "}
						<a
							href="https://rvhelp.com/pro-membership"
							style={{ color: "#437F6B", textDecoration: "underline" }}
						>
							https://rvhelp.com/pro-membership
						</a>
						.
					</Text>
				</Section>

				<Text style={emailStyles.text}>
					Thank you for using RV Help. We're committed to connecting you with
					the right service provider for your needs.
				</Text>

				<Text style={emailStyles.smallText}>
					This is an automated email. Please do not reply to this message as it
					is not monitored.
				</Text>
			</Container>
		</BaseEmail>
	);
};
