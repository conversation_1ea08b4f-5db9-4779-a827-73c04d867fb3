import { getCategoryName } from "@/lib/categories";
import { But<PERSON>, Container, Section, Text } from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";

interface LeadFollowUpEmailProps {
	firstName: string;
	lastName: string;
	email: string;
	phone?: string;
	contactPreference: "sms" | "phone" | "email";
	message: string;
	category: string;
	// New smart follow-up props
	followUpType:
		| "select_provider"
		| "all_declined"
		| "awaiting_responses"
		| "mixed_responses";
	actionMessage: string;
	jobId: string;
	acceptedQuotesCount: number;
	declinedQuotesCount: number;
	pendingQuotesCount: number;
	membershipLevel: "FREE" | "STANDARD" | "PREMIUM";
	acceptedProviders?: Array<{
		name: string;
		location?: { city?: string; state?: string };
	}>;
}

export const LeadFollowUpEmail = ({
	firstName,
	lastName,
	email,
	phone,
	contactPreference,
	message,
	category,
	followUpType,
	actionMessage,
	jobId,
	acceptedQuotesCount,
	declinedQuotesCount,
	pendingQuotesCount,
	membershipLevel,
	acceptedProviders = []
}: LeadFollowUpEmailProps) => {
	const fullName = `${firstName} ${lastName}`;
	const contactMethodText =
		contactPreference === "sms"
			? "Text Message"
			: contactPreference === "phone"
				? "Phone Call"
				: "Email";

	const getSubjectText = () => {
		switch (followUpType) {
			case "select_provider":
				return "Action Required: Select Your Provider";
			case "all_declined":
				return "Let's Find You Another Provider";
			case "awaiting_responses":
				return "Update on Your Service Request";
			case "mixed_responses":
				return "Partial Response Update";
			default:
				return "Update on Your Service Request";
		}
	};

	const getCallToAction = () => {
		switch (followUpType) {
			case "select_provider":
				return {
					text: "Review and Select Provider",
					href: `${process.env.NEXT_PUBLIC_APP_URL}/service-requests/${jobId}`,
					color: "#437F6B"
				};
			case "all_declined":
				return {
					text:
						membershipLevel === "FREE"
							? "Find More Providers"
							: "Get Help Finding Providers",
					href:
						membershipLevel === "FREE"
							? `${process.env.NEXT_PUBLIC_APP_URL}/search?category=${category}`
							: `${process.env.NEXT_PUBLIC_APP_URL}/service-requests/${jobId}`,
					color: "#437F6B"
				};
			case "awaiting_responses":
				return {
					text: "View Request Status",
					href: `${process.env.NEXT_PUBLIC_APP_URL}/service-requests/${jobId}`,
					color: "#6B7280"
				};
			case "mixed_responses":
				return {
					text: "View All Responses",
					href: `${process.env.NEXT_PUBLIC_APP_URL}/service-requests/${jobId}`,
					color: "#437F6B"
				};
			default:
				return {
					text: "View Request",
					href: `${process.env.NEXT_PUBLIC_APP_URL}/service-requests/${jobId}`,
					color: "#437F6B"
				};
		}
	};

	const getAdditionalTips = () => {
		switch (followUpType) {
			case "select_provider":
				return (
					<Section className="bg-green-50 p-4 rounded-lg mt-4 mb-4">
						<Text className="text-sm text-green-800 m-0 mb-2 font-medium">
							Great news! You have provider responses to review:
						</Text>
						<Text className="text-sm text-green-700 m-0">
							• Review each provider's details and response
							<br />
							• Ask any questions through the messaging system
							<br />• Select the provider that best fits your needs
						</Text>
						{acceptedProviders.length > 0 && (
							<Text className="text-sm text-green-700 m-0 mt-2 font-medium">
								Providers who responded:
							</Text>
						)}
						{acceptedProviders.map((provider, index) => (
							<Text key={index} className="text-sm text-green-700 m-0">
								• {provider.name}
								{provider.location?.city &&
									provider.location?.state &&
									` - ${provider.location.city}, ${provider.location.state}`}
							</Text>
						))}
					</Section>
				);
			case "all_declined":
				return (
					<Section className="bg-blue-50 p-4 rounded-lg mt-4 mb-4">
						<Text className="text-sm text-blue-800 m-0 mb-2 font-medium">
							Don't worry - this happens sometimes. Here's what you can do:
						</Text>
						<Text className="text-sm text-blue-700 m-0">
							{membershipLevel === "FREE" ? (
								<>
									• Search for more providers in your area
									<br />
									• Consider expanding your search radius
									<br />• Upgrade to Pro for priority placement and more
									invitations
								</>
							) : (
								<>
									• Contact support for personalized help finding providers
									<br />
									• We'll reach out to additional providers on your behalf
									<br />• Your Pro membership ensures priority placement
								</>
							)}
						</Text>
					</Section>
				);
			case "awaiting_responses":
				return (
					<Section className="bg-yellow-50 p-4 rounded-lg mt-4 mb-4">
						<Text className="text-sm text-yellow-800 m-0 mb-2 font-medium">
							We're actively working on your request:
						</Text>
						<Text className="text-sm text-yellow-700 m-0">
							• {pendingQuotesCount} provider{pendingQuotesCount > 1 ? "s" : ""}{" "}
							{pendingQuotesCount > 1 ? "are" : "is"} reviewing your request
							<br />
							• You'll receive notifications as soon as they respond
							<br />• Most providers respond within 24-48 hours
						</Text>
					</Section>
				);
			case "mixed_responses":
				return (
					<Section className="bg-orange-50 p-4 rounded-lg mt-4 mb-4">
						<Text className="text-sm text-orange-800 m-0 mb-2 font-medium">
							Current response status:
						</Text>
						<Text className="text-sm text-orange-700 m-0">
							• {declinedQuotesCount} provider
							{declinedQuotesCount > 1 ? "s" : ""} unable to help
							<br />• {pendingQuotesCount} provider
							{pendingQuotesCount > 1 ? "s" : ""} still reviewing
							<br />• We'll notify you of additional responses
						</Text>
					</Section>
				);
			default:
				return null;
		}
	};

	const callToAction = getCallToAction();

	return (
		<BaseEmail
			previewText={`Update on your ${getCategoryName(category)} request`}
		>
			<Container className="mx-auto">
				<Text className="text-lg font-bold text-gray-900 mb-2">
					{getSubjectText()}
				</Text>

				<Text className="text-sm text-gray-700 m-0">Hi {firstName},</Text>
				<Text className="text-sm text-gray-700 m-0 mt-2">{actionMessage}</Text>

				{getAdditionalTips()}

				<Section className="text-center my-6">
					<Button
						href={callToAction.href}
						className={`text-sm text-white py-2 px-6 rounded-lg text-center`}
						style={{ backgroundColor: callToAction.color }}
					>
						{callToAction.text}
					</Button>
				</Section>

				<Text className="font-semibold text-gray-900 m-0 mt-4 mb-1">
					Your Original Request Details
				</Text>
				<Section className="bg-gray-100 p-4 rounded-lg mt-0">
					<Text className="text-sm text-gray-700 m-0">
						<span className="font-medium">Name:</span> {fullName}
					</Text>
					<Text className="text-sm text-gray-700 m-0 mt-1">
						<span className="font-medium">Email:</span> {email}
					</Text>
					{phone && (
						<Text className="text-sm text-gray-700 m-0 mt-1">
							<span className="font-medium">Phone:</span> {phone}
						</Text>
					)}
					<Text className="text-sm text-gray-700 m-0 mt-1">
						<span className="font-medium">Preferred Contact Method:</span>{" "}
						{contactMethodText}
					</Text>
					<Text className="text-sm text-gray-700 m-0 mt-1">
						<span className="font-medium">Service Type:</span>{" "}
						{getCategoryName(category)}
					</Text>
				</Section>

				<Text className="font-semibold text-gray-900 m-0 mt-4 mb-1">
					Your Original Message
				</Text>
				<Section className="bg-gray-100 p-4 rounded-lg mt-0">
					<Text className="text-sm whitespace-pre-wrap m-0 text-gray-700">
						{message}
					</Text>
				</Section>

				<Text className="text-xs text-gray-400 text-center mt-2 mb-0">
					This is an automated email. Please feel free to reply to this message
					at <EMAIL> if you have any questions or need further
					assistance.
				</Text>
			</Container>
		</BaseEmail>
	);
};
