import { getCategoryName } from "@/lib/categories";
import { Job, RVHelpMembershipLevel } from "@prisma/client";
import {
	Button,
	Heading,
	Hr,
	Link,
	Section,
	Text
} from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";
import { emailStyles } from "../../shared-styles";

interface ServiceRequestConfirmationEmailProps {
	job: Job;
	customerName: string;
	customerEmail: string;
	customerPhone?: string;
	contactPreference: "sms" | "phone" | "email";
	message: string;
	category: string;
	membershipLevel?: RVHelpMembershipLevel;
	providerName?: string;
	providerLocation?: {
		city?: string;
		state?: string;
	};
}

const APP_URL = process.env.NEXT_PUBLIC_APP_URL;

export const ServiceRequestConfirmationEmail = ({
	job,
	customerName,
	customerEmail,
	customerPhone,
	contactPreference,
	message,
	category,
	membershipLevel = "FREE",
	providerName,
	providerLocation
}: ServiceRequestConfirmationEmailProps) => {
	const previewText = providerName
		? `Your message to ${providerName} has been sent`
		: `Your Service Request has been Submitted!`;

	const contactMethodText =
		contactPreference === "sms"
			? "Text Message"
			: contactPreference === "phone"
				? "Phone Call"
				: "Email";

	const providerLocationText =
		providerLocation?.city && providerLocation?.state
			? ` in ${providerLocation.city}, ${providerLocation.state}`
			: "";

	const isProviderMessage = !!providerName;
	const showOffer = membershipLevel === "FREE";

	return (
		<BaseEmail previewText={previewText}>
			<Heading style={emailStyles.heading}>
				{isProviderMessage
					? "Message Sent Successfully"
					: "Your Service Request has been Submitted!"}
			</Heading>

			<Text style={emailStyles.text}>Hello {customerName},</Text>

			<Text style={emailStyles.text}>
				{isProviderMessage
					? `Your message has been sent to ${providerName}${providerLocationText}. They will contact you shortly.`
					: "Thank you for submitting your service request. We've received it and are working on finding you the best mobile RV technicians in your area. You can view your request details and status at any time by clicking the button below."}
			</Text>

			<Section style={emailStyles.centered}>
				<Button
					style={emailStyles.button}
					href={`${APP_URL}/service-requests/${job.id}`}
				>
					View Your Service Request
				</Button>
			</Section>

			{/* Service Request Details */}
			<Section style={emailStyles.section}>
				<Heading as="h2" style={emailStyles.subheading}>
					Your{" "}
					{isProviderMessage
						? "Contact Information"
						: "Service Request Details"}
				</Heading>

				<Section
					style={{
						backgroundColor: "#f9fafb",
						padding: "16px",
						borderRadius: "8px",
						marginBottom: "16px"
					}}
				>
					<Text style={{ ...emailStyles.text, margin: "0 0 8px 0" }}>
						<span style={{ fontWeight: "600" }}>Service Type:</span>{" "}
						{getCategoryName(category)}
					</Text>
					<Text style={{ ...emailStyles.text, margin: "0 0 8px 0" }}>
						<span style={{ fontWeight: "600" }}>Contact Method:</span>{" "}
						{contactMethodText}
					</Text>
					<Text style={{ ...emailStyles.text, margin: "0 0 8px 0" }}>
						<span style={{ fontWeight: "600" }}>Email:</span> {customerEmail}
					</Text>
					{customerPhone && (
						<Text style={{ ...emailStyles.text, margin: "0 0 8px 0" }}>
							<span style={{ fontWeight: "600" }}>Phone:</span> {customerPhone}
						</Text>
					)}
				</Section>

				<Text
					style={{
						...emailStyles.text,
						fontWeight: "600",
						marginBottom: "8px"
					}}
				>
					Your Message:
				</Text>
				<Section
					style={{
						backgroundColor: "#f9fafb",
						padding: "16px",
						borderRadius: "8px",
						marginBottom: "16px"
					}}
				>
					<Text
						style={{ ...emailStyles.text, margin: "0", whiteSpace: "pre-wrap" }}
					>
						{message}
					</Text>
				</Section>
			</Section>

			{/* Show offer only for FREE users */}
			{showOffer && (
				<>
					<Hr style={emailStyles.hr} />
					<Section style={emailStyles.section}>
						<Heading as="h2" style={emailStyles.subheading}>
							Your One-Time Pro Offer
						</Heading>
						<Text
							style={{
								...emailStyles.text,
								textAlign: "center",
								color: "#dc2626",
								fontWeight: "600"
							}}
						>
							Expires in 72 hours!
						</Text>
						<Text style={emailStyles.text}>
							To help you get your issue resolved faster, we're giving you a
							special one-time offer. For the next <strong>72 hours</strong>,
							you can upgrade to a Pro membership for <strong>50% off</strong>{" "}
							the first year.
						</Text>
						<Text style={{ ...emailStyles.text, fontWeight: "600" }}>
							Pro members can:
						</Text>
						<Text style={{ ...emailStyles.text, paddingLeft: "16px" }}>
							&bull; Invite up to 5 providers to a job to get faster responses.
							<br />
							&bull; Access unlimited pre-service diagnostic calls.
							<br />
							&bull; Receive nationwide discounts from providers.
						</Text>
						<Section style={emailStyles.centered}>
							<Button
								style={{ ...emailStyles.button, backgroundColor: "#16a34a" }}
								href={`${APP_URL}/service-requests/${job.id}/success`}
							>
								Claim 50% Off Now
							</Button>
						</Section>
					</Section>
				</>
			)}

			<Text style={emailStyles.smallText}>
				Or copy and paste this URL into your browser:{" "}
				<Link
					href={`${APP_URL}/service-requests/${job.id}`}
					style={{ color: "#2563eb", textDecoration: "none" }}
				>
					{`${APP_URL}/service-requests/${job.id}`}
				</Link>
			</Text>
		</BaseEmail>
	);
};

export default ServiceRequestConfirmationEmail;
