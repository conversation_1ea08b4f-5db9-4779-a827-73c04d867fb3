import { getCategoryName } from "@/lib/categories";
import { Job } from "@prisma/client";
import {
	Button,
	Heading,
	Hr,
	Link,
	Section,
	Text
} from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";
import { emailStyles } from "../../shared-styles";

interface OfferReminderEmailProps {
	job: Job;
	customerName: string;
	customerEmail: string;
	customerPhone?: string;
	contactPreference: "sms" | "phone" | "email";
	message: string;
	category: string;
}

const APP_URL = process.env.NEXT_PUBLIC_APP_URL;

export const OfferReminderEmail = ({
	job,
	customerName,
	customerEmail,
	customerPhone,
	contactPreference,
	message,
	category
}: OfferReminderEmailProps) => {
	const previewText = `Your 50% off Pro offer expires in 24 hours!`;
	const contactMethodText =
		contactPreference === "sms"
			? "Text Message"
			: contactPreference === "phone"
				? "Phone Call"
				: "Email";

	return (
		<BaseEmail previewText={previewText}>
			<Heading style={emailStyles.heading}>
				⏰ Your 50% off Pro offer expires in 24 hours!
			</Heading>

			<Text style={emailStyles.text}>Hello {customerName},</Text>

			<Text style={emailStyles.text}>
				This is a friendly reminder that your special one-time Pro membership
				offer expires in just 24 hours. Don't miss out on this limited-time
				opportunity to upgrade and get faster responses to your service request.
			</Text>

			{/* Offer Section */}
			<Section style={emailStyles.section}>
				<Heading as="h2" style={emailStyles.subheading}>
					Your One-Time Pro Offer
				</Heading>
				<Text
					style={{
						...emailStyles.text,
						textAlign: "center",
						color: "#dc2626",
						fontWeight: "600",
						fontSize: "18px"
					}}
				>
					⏰ Expires in 24 hours!
				</Text>
				<Text style={emailStyles.text}>
					Upgrade to Pro membership now and get <strong>50% off</strong> your
					first year. This offer is only available for your current service
					request and expires soon.
				</Text>
				<Text style={{ ...emailStyles.text, fontWeight: "600" }}>
					Pro members get:
				</Text>
				<Text style={{ ...emailStyles.text, paddingLeft: "16px" }}>
					&bull; Invite up to 5 providers to get faster responses
					<br />
					&bull; Access unlimited pre-service diagnostic calls
					<br />
					&bull; Receive nationwide discounts from providers
					<br />
					&bull; Priority customer support
				</Text>
				<Section style={emailStyles.centered}>
					<Button
						style={{
							...emailStyles.button,
							backgroundColor: "#dc2626",
							fontSize: "16px"
						}}
						href={`${APP_URL}/service-requests/${job.id}/success`}
					>
						🚀 Claim 50% Off Now - Limited Time!
					</Button>
				</Section>
			</Section>

			<Hr style={emailStyles.hr} />

			{/* Service Request Reminder */}
			<Section style={emailStyles.section}>
				<Heading as="h2" style={emailStyles.subheading}>
					Your Service Request Summary
				</Heading>
				<Text style={emailStyles.text}>
					As a reminder, here's your current service request:
				</Text>

				<Section
					style={{
						backgroundColor: "#f9fafb",
						padding: "16px",
						borderRadius: "8px",
						marginBottom: "16px"
					}}
				>
					<Text style={{ ...emailStyles.text, margin: "0 0 8px 0" }}>
						<span style={{ fontWeight: "600" }}>Service Type:</span>{" "}
						{getCategoryName(category)}
					</Text>
					<Text style={{ ...emailStyles.text, margin: "0 0 8px 0" }}>
						<span style={{ fontWeight: "600" }}>Contact Method:</span>{" "}
						{contactMethodText}
					</Text>
					<Text style={{ ...emailStyles.text, margin: "0 0 8px 0" }}>
						<span style={{ fontWeight: "600" }}>Email:</span> {customerEmail}
					</Text>
					{customerPhone && (
						<Text style={{ ...emailStyles.text, margin: "0 0 8px 0" }}>
							<span style={{ fontWeight: "600" }}>Phone:</span> {customerPhone}
						</Text>
					)}
				</Section>

				<Section style={emailStyles.centered}>
					<Button
						style={emailStyles.button}
						href={`${APP_URL}/service-requests/${job.id}`}
					>
						View Your Service Request
					</Button>
				</Section>
			</Section>

			<Text style={emailStyles.smallText}>
				Or copy and paste this URL into your browser:{" "}
				<Link
					href={`${APP_URL}/service-requests/${job.id}`}
					style={{ color: "#2563eb", textDecoration: "none" }}
				>
					{`${APP_URL}/service-requests/${job.id}`}
				</Link>
			</Text>

			<Text
				style={{
					...emailStyles.smallText,
					color: "#6b7280",
					marginTop: "16px"
				}}
			>
				This offer expires in 24 hours and is only available for your current
				service request. Regular pricing will apply after the offer expires.
			</Text>
		</BaseEmail>
	);
};
