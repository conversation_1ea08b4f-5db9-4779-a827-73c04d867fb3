import config from "@/config";
import {
	Container,
	Heading,
	Link,
	Section,
	Text
} from "@react-email/components";
import { BaseEmail } from "../BaseEmail";
import { emailStyles } from "../shared-styles";

interface NewQuoteMessageEmailProps {
	recipientName: string;
	senderName: string;
	messagePreview: string;
	jobId: string;
	quoteId: string;
	isProvider: boolean;
}

export const NewQuoteMessageEmail = ({
	recipientName,
	senderName,
	messagePreview,
	jobId,
	quoteId,
	isProvider
}: NewQuoteMessageEmailProps) => {
	const viewUrl = isProvider
		? `${config.appUrl}/provider/leads/${quoteId}`
		: `${config.appUrl}/service-requests/${jobId}`;

	return (
		<BaseEmail previewText={`New message from ${senderName}`}>
			<Container style={emailStyles.container}>
				<Heading style={emailStyles.heading}>New Message Received</Heading>

				<Section style={emailStyles.section}>
					<Text style={emailStyles.text}>Hi {recipientName},</Text>

					<Text style={emailStyles.text}>
						You have received a new message from {senderName} regarding your
						service request:
					</Text>

					<Text style={emailStyles.messageText}>"{messagePreview}"</Text>
				</Section>

				<Section style={emailStyles.centered}>
					<Link href={viewUrl} style={emailStyles.button}>
						View and Reply
					</Link>
				</Section>

				<Text style={emailStyles.footer}>
					You can reply directly to this email or use the RV Help platform for
					better tracking and features.
				</Text>
			</Container>
		</BaseEmail>
	);
};
