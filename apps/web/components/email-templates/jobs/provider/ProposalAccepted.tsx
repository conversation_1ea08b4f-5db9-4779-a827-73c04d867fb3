import config from "@/config";
import {
	Container,
	Heading,
	Link,
	Section,
	Text
} from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";
import { emailStyles } from "../../shared-styles";

interface ProposalAcceptedEmailProps {
	customerName: string;
	quoteId: string;
	reviewRequestScheduled?: boolean;
	reviewRequestDelay?: number;
}

export const ProposalAcceptedEmail = ({
	customerName,
	quoteId,
	reviewRequestScheduled,
	reviewRequestDelay
}: ProposalAcceptedEmailProps) => {
	const previewText = `You've got a job!`;

	return (
		<BaseEmail previewText={previewText}>
			<Container style={emailStyles.container}>
				<Heading style={emailStyles.heading}>You've got a job!</Heading>

				<Section style={emailStyles.section}>
					<Text style={emailStyles.text}>
						Great news! {customerName} has accepted your proposal.
					</Text>

					<Text style={emailStyles.text}>Please ensure to:</Text>
					<Text
						style={{
							...emailStyles.text,
							paddingLeft: "20px",
							marginTop: "8px"
						}}
					>
						• Contact the customer to confirm details
						<br />
						• Update the job status as you progress
						<br />• Mark the job as complete when finished
					</Text>
				</Section>

				<Section style={emailStyles.centered}>
					<Link
						href={`${config.appUrl}/provider/leads/${quoteId}`}
						style={emailStyles.button}
					>
						View Details
					</Link>
				</Section>

				<Section style={emailStyles.section}>
					<Text style={emailStyles.text}>
						<strong>About Reviews:</strong>
					</Text>
					{reviewRequestScheduled ? (
						<Text style={{ ...emailStyles.text, marginTop: "8px" }}>
							An automated review request will be sent to the customer{" "}
							{reviewRequestDelay} hours after you mark the job as complete.
						</Text>
					) : (
						<Text style={{ ...emailStyles.text, marginTop: "8px" }}>
							Once you complete the job, you can request a review from the
							customer through the job details page.
						</Text>
					)}
				</Section>

				<Text style={emailStyles.footer}>
					Thank you for providing your services through RV Help!
				</Text>
			</Container>
		</BaseEmail>
	);
};

export default ProposalAcceptedEmail;
