import config from "@/config";
import {
	Container,
	Heading,
	Link,
	Section,
	Text
} from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";
import { emailStyles } from "../../shared-styles";

interface ProposalRejectedEmailProps {
	customerName: string;
	providerName: string;
	jobId: string;
}

export const ProposalRejectedEmail = ({
	customerName,
	providerName,
	jobId
}: ProposalRejectedEmailProps) => {
	return (
		<BaseEmail previewText="RV Help: Service Request Update">
			<Container style={emailStyles.container}>
				<Heading style={emailStyles.heading}>Service Request Update</Heading>

				<Section style={emailStyles.section}>
					<Text style={emailStyles.text}>Hi {providerName},</Text>

					<Text style={emailStyles.text}>
						We wanted to let you know that {customerName} has decided to go in a
						different direction with their service request. While your proposal
						wasn't selected this time, we appreciate your timeliness in
						responding.
					</Text>
				</Section>

				<Text style={emailStyles.text}>
					There are many more opportunities available in your area. We encourage
					you to continue submitting proposals for other service requests that
					match your expertise.
				</Text>

				<Section style={emailStyles.centered}>
					<Link
						href={`${config.appUrl}/provider/leads?leadId=${jobId}`}
						style={emailStyles.button}
					>
						View Service Request
					</Link>
				</Section>

				<Text style={emailStyles.footer}>
					Keep checking your dashboard for new service requests in your area.
				</Text>
			</Container>
		</BaseEmail>
	);
};

export default ProposalRejectedEmail;
