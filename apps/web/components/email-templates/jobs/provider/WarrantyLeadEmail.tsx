import { getCategoryName } from "@/lib/categories";
import { Button, Container, Section, Text } from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";

interface WarrantyLeadEmailProps {
	firstName: string;
	lastName: string;
	email: string;
	phone?: string;
	contactPreference: "sms" | "phone" | "email";
	location?: {
		address?: string;
		city?: string;
		state?: string;
		zip?: string;
		country?: string;
		latitude?: number;
		longitude?: number;
	};
	message: string;
	category: "rv-repair" | "rv-inspection";
	leadId: string;
	distance?: number | null;
	manufacturerName: string;
}

export function WarrantyLeadEmail({
	firstName,
	lastName,
	location,
	message,
	category,
	leadId,
	manufacturerName
}: WarrantyLeadEmailProps) {
	const fullName = `${firstName} ${lastName}`;
	const locationText = location
		? location.city && location.state
			? `${location.city}, ${location.state}`
			: location.address
				? location.address
				: "Location not provided"
		: "Location not provided";

	const categoryText = getCategoryName(category);

	return (
		<BaseEmail
			previewText={`Pre-Approved ${manufacturerName} Warranty Lead from ${fullName}`}
		>
			<Container className="mt-4">
				<Section className="bg-orange-50 border-l-4 border-orange-400 rounded-lg p-6 mb-6">
					<Text className="text-xl text-gray-900 mb-2 font-semibold">
						🚨 Pre-Approved {manufacturerName} Warranty Lead
					</Text>
					<Text className="text-orange-800 mb-4 font-medium">
						This is a pre-approved warranty job from {manufacturerName}. Click the button below to view the lead details:
					</Text>
				</Section>

				<Section className="bg-gray-50 rounded-lg p-6 mb-6">
					<Text className="text-xl text-gray-900 mb-4">
						New Warranty Lead Received
					</Text>
					<Text className="text-gray-600 mb-4">
						You have received a new {manufacturerName} warranty lead for{" "}
						{categoryText} in {locationText}.
					</Text>
					<Text className="text-gray-600 mb-4">
						This is pre-approved warranty work from {manufacturerName}. Click
						the button below to view the lead details:
					</Text>
					<Section className="text-center my-6">
						<Button
							href={`${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${leadId}`}
							className="bg-[#437F6B] text-sm text-white py-2 px-6 rounded-lg text-center"
						>
							View Warranty Lead
						</Button>
					</Section>
				</Section>


			</Container>
		</BaseEmail>
	);
}
