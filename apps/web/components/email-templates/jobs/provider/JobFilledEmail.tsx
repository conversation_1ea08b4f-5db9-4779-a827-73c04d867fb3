import config from "@/config";
import { JobWithUserAndLocation } from "@/types/global";
import {
	Container,
	Heading,
	Link,
	Section,
	Text
} from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";

export const JobFilledEmail = ({
	customerName,
	job
}: {
	customerName: string;
	job: JobWithUserAndLocation;
}) => {
	return (
		<BaseEmail previewText="RV Help: Service Request Update">
			<Container>
				<Heading
					as="h1"
					style={{
						color: "#437F6B",
						fontSize: "24px",
						textAlign: "center",
						margin: "20px 0"
					}}
				>
					Service Request Update
				</Heading>

				<Section
					style={{
						backgroundColor: "#f3f4f6",
						padding: "20px",
						borderRadius: "8px",
						margin: "20px 0"
					}}
				>
					<Text
						style={{ fontSize: "16px", color: "#374151", lineHeight: "24px" }}
					>
						We wanted to let you know that a service request you responded to
						has been filled. {customerName} has selected another service
						provider for this job.
					</Text>

					<Text
						style={{
							fontSize: "16px",
							color: "#374151",
							lineHeight: "24px",
							marginTop: "16px"
						}}
					>
						<strong>Job Details:</strong>
					</Text>
					<Text
						style={{
							fontSize: "16px",
							color: "#374151",
							lineHeight: "24px",
							marginTop: "8px"
						}}
					>
						• Type: {job.category}
						<br />• Vehicle: {job.rv_year} {job.rv_make} {job.rv_model}
						<br />
						{job.location && `• Location: ${job.location.address}`}
					</Text>
				</Section>

				<Text
					style={{
						fontSize: "16px",
						color: "#374151",
						lineHeight: "24px",
						marginTop: "20px"
					}}
				>
					Thank you for responding to the service request. We encourage you to
					continue responding to other service requests in your area.
				</Text>

				<Section style={{ textAlign: "center", margin: "30px 0" }}>
					<Link
						href={`${config.appUrl}/provider/leads?leadId=${job.id}`}
						style={{
							backgroundColor: "#437F6B",
							color: "#ffffff",
							padding: "12px 24px",
							borderRadius: "6px",
							textDecoration: "none",
							display: "inline-block"
						}}
					>
						View Lead Details
					</Link>
				</Section>

				<Text
					style={{
						fontSize: "14px",
						color: "#6b7280",
						textAlign: "center",
						marginTop: "30px"
					}}
				>
					Keep an eye on your dashboard for new opportunities in your area.
				</Text>
			</Container>
		</BaseEmail>
	);
};
