import { getCategoryName } from "@/lib/categories";
import { Button, Container, Section, Text } from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";

interface LeadEmailProps {
	firstName: string;
	lastName: string;
	email: string;
	phone?: string;
	contactPreference: "sms" | "phone" | "email";
	location?: {
		address?: string;
		city?: string;
		state?: string;
		zip?: string;
		country?: string;
		latitude?: number;
		longitude?: number;
	};
	message: string;
	category: "rv-repair" | "rv-inspection";
	leadId: string;
	distance?: number | null;
}

export function NewLeadEmail({
	firstName,
	lastName,
	location,
	message,
	category,
	leadId
}: LeadEmailProps) {
	const fullName = `${firstName} ${lastName}`;
	const locationText = location
		? location.city && location.state
			? `${location.city}, ${location.state}`
			: location.address
				? location.address
				: "Location not provided"
		: "Location not provided";

	const categoryText = getCategoryName(category);

	return (
		<BaseEmail previewText={`New Lead from ${fullName}`}>
			<Container>
				<Section className="bg-gray-50 rounded-lg p-6 mb-6">
					<Text className="text-xl text-gray-900 mb-4">New Lead Received</Text>
					<Text className="text-gray-600 mb-4">
						You have received a new lead for {categoryText} in {locationText}.
					</Text>
					<Text className="text-gray-600 mb-4">
						Click the button below to respond to this lead. You can accept,
						decline, or message the customer directly:
					</Text>
					<Section className="text-center my-6">
						<Button
							href={`${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${leadId}`}
							className="bg-[#437F6B] text-sm text-white py-2 px-6 rounded-lg text-center"
						>
							Respond to Lead
						</Button>
					</Section>
				</Section>

				<Section className="bg-gray-50 rounded-lg p-6">
					<Text className="text-lg text-gray-900 mb-4">Message Preview</Text>
					<Text className="text-gray-600 whitespace-pre-wrap">{message}</Text>
				</Section>
			</Container>
		</BaseEmail>
	);
}
