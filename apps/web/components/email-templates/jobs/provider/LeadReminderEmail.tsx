import { getCategoryName } from "@/lib/categories";
import { Button, Container, Section, Text } from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";

interface ProviderReminderEmailProps {
	firstName: string;
	lastName: string;
	email: string;
	phone: string;
	contactPreference: "sms" | "phone" | "email";
	message: string;
	category: string;
	providerName: string;
	location: {
		address: string;
		latitude: number;
		longitude: number;
	};
	leadId: string;
}

export function ProviderReminderEmail({
	firstName,
	lastName,
	email,
	phone,
	contactPreference,
	message,
	category,
	providerName,
	location,
	leadId
}: ProviderReminderEmailProps) {
	const fullName = `${firstName} ${lastName}`;
	const categoryText = getCategoryName(category);
	const contactMethodText =
		contactPreference === "sms"
			? "Text Message"
			: contactPreference === "phone"
				? "Phone Call"
				: "Email";

	return (
		<BaseEmail previewText={`Reminder: Pending Lead from ${fullName}`}>
			<Container>
				<Section className="bg-gray-50 rounded-lg p-6 mb-6">
					<Text className="text-xl text-gray-900 mb-4">
						Reminder: Pending Lead Needs Your Attention
					</Text>
					<Text className="text-gray-600 mb-4">Hello {providerName},</Text>
					<Text className="text-gray-600 mb-4">
						This is a friendly reminder that you have a pending lead from{" "}
						{fullName} that was submitted more than 24 hours ago and is still
						waiting for your response.
					</Text>
					<Text className="text-gray-600 mb-4">
						To view the full lead details and take action, please click the
						button below:
					</Text>
					<Section className="text-center my-6">
						<Button
							href={`${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${leadId}`}
							className="bg-[#437F6B] text-sm text-white py-2 px-6 rounded-lg text-center"
						>
							View Lead Details
						</Button>
					</Section>
				</Section>

				<Section className="bg-gray-50 rounded-lg p-6">
					<Text className="text-lg text-gray-900 mb-4">Lead Details</Text>
					<Text className="text-gray-600 m-0">
						<span className="font-medium">Name:</span> {fullName}
					</Text>
					<Text className="text-gray-600 m-0 mt-1">
						<span className="font-medium">Email:</span> {email}
					</Text>
					<Text className="text-gray-600 m-0 mt-1">
						<span className="font-medium">Phone:</span> {phone}
					</Text>
					<Text className="text-gray-600 m-0 mt-1">
						<span className="font-medium">Preferred Contact Method:</span>{" "}
						{contactMethodText}
					</Text>
					<Text className="text-gray-600 m-0 mt-1">
						<span className="font-medium">Service Type:</span> {categoryText}
					</Text>
					<Text className="text-gray-600 m-0 mt-1">
						<span className="font-medium">Location:</span> {location.address}
					</Text>
					<Text className="text-gray-600 m-0 mt-4">
						<span className="font-medium">Message:</span>
					</Text>
					<Text className="text-gray-600 whitespace-pre-wrap mt-1">
						{message}
					</Text>
				</Section>

				<Text className="text-xs text-gray-400 text-center mt-4 mb-0">
					This is an automated email. Please do not reply to this message as it
					is not monitored.
				</Text>
			</Container>
		</BaseEmail>
	);
}
