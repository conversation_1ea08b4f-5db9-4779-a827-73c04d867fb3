import { Container, Heading, Section, Text } from "@react-email/components";
import { BaseEmail } from "../../BaseEmail";
import { emailStyles } from "../../shared-styles";

interface JobCompletedEmailProps {
	customerName: string;
	resolutionNotes?: string;
}

export const JobCompletedEmail = ({
	customerName,
	resolutionNotes
}: JobCompletedEmailProps) => {
	return (
		<BaseEmail previewText={`Congratulations! Your job has been completed`}>
			<Container style={emailStyles.container}>
				<Heading style={emailStyles.heading}>
					Job Completed Successfully! 🎉
				</Heading>

				<Section style={emailStyles.section}>
					<Text style={emailStyles.text}>
						Great work! You've successfully completed the service request for{" "}
						{customerName}.
					</Text>

					{resolutionNotes && (
						<>
							<Text style={emailStyles.text}>
								<strong>Resolution Notes:</strong>
							</Text>
							<Text style={{ ...emailStyles.text, marginTop: "8px" }}>
								{resolutionNotes}
							</Text>
						</>
					)}
				</Section>

				<Text style={emailStyles.text}>
					Thank you for providing excellent service through RV Help. We look
					forward to connecting you with more customers in need of your
					expertise.
				</Text>

				<Text style={emailStyles.footer}>
					Keep checking your dashboard for new service opportunities in your
					area.
				</Text>
			</Container>
		</BaseEmail>
	);
};
