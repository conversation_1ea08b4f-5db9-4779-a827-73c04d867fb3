import { Heading, Section, Text } from "@react-email/components";
import { BaseEmail } from "./BaseEmail";

interface EmergencyDispatchConfirmationEmailProps {
	requestId: string;
	firstName: string;
	location: {
		address: string;
		latitude: number;
		longitude: number;
	};
	emergencyDescription: string;
	phone: string;
}

export const EmergencyDispatchConfirmationEmail: React.FC<EmergencyDispatchConfirmationEmailProps> = ({
	requestId,
	firstName,
	location,
	emergencyDescription,
	phone
}) => {
	return (
		<BaseEmail previewText="Emergency Dispatch Request Submitted - RV Help">
			<Section className="flex flex-col gap-4">
				<Heading className="text-2xl font-bold text-center text-red-600">
					Emergency Dispatch Request Received
				</Heading>

				<div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
					<Text className="text-gray-700 mb-2">
						Hello {firstName},
					</Text>
					<Text className="text-gray-700">
						We've received your emergency dispatch request and are immediately notifying all available RV technicians within 50 miles of your location.
					</Text>
				</div>

				<div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
					<Text className="text-lg font-bold text-gray-800 mb-2">
						Request Details:
					</Text>
					<Text className="text-gray-700 mb-1">
						<strong>Request ID:</strong> {requestId}
					</Text>
					<Text className="text-gray-700 mb-1">
						<strong>Location:</strong> {location.address}
					</Text>
					<Text className="text-gray-700 mb-1">
						<strong>Emergency:</strong> {emergencyDescription}
					</Text>
					<Text className="text-gray-700 mb-1">
						<strong>Contact:</strong> {phone}
					</Text>
				</div>

				<div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
					<Text className="text-lg font-bold text-orange-800 mb-2">
						What Happens Next:
					</Text>
					<Text className="text-gray-700 mb-1">
						✅ We'll broadcast your emergency to all available RV technicians within 50 miles
					</Text>
					<Text className="text-gray-700 mb-1">
						📞 Responding technicians will contact you directly with availability and pricing
					</Text>
					<Text className="text-gray-700 mb-1">
						⏰ You should receive responses within 1 business day
					</Text>
					<Text className="text-gray-700 mb-1">
						💰 Service fees and repair costs will be handled directly with the technician
					</Text>
				</div>

				<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
					<Text className="text-lg font-bold text-yellow-800 mb-2">
						Important Reminder:
					</Text>
					<Text className="text-gray-700">
						Emergency Dispatch is NOT a roadside assistance or towing service. This feature helps you quickly connect with multiple RV technicians when time is critical. You will still need to pay the responding technician for their service call and repairs.
					</Text>
				</div>

				<Text className="text-gray-700 mb-2">
					If you need immediate assistance or have questions, please contact our support <NAME_EMAIL> or reply to this email.
				</Text>

				<Text className="text-gray-700 mb-2">
					Thank you for being a Pro member!
				</Text>
				<Text className="text-gray-700 font-bold">
					The RV Help Team
				</Text>
			</Section>
		</BaseEmail>
	);
}; 