import { But<PERSON>, <PERSON><PERSON>, <PERSON>r, <PERSON>, Text } from "@react-email/components";
import * as React from "react";
import { BaseEmail } from "./BaseEmail";
import { emailStyles } from "./shared-styles";

interface FraudNotificationEmailProps {
	providerName?: string;
	providerEmail: string;
	jobId?: string;
	jobMessage?: string;
	customerName?: string;
	customerEmail?: string;
}

export const FraudNotificationEmail: React.FC<
	Readonly<FraudNotificationEmailProps>
> = ({
	providerName,
	providerEmail,
	jobId,
	jobMessage,
	customerName,
	customerEmail
}) => {
	const previewText = "Important: Lead Flagged as Potentially Fraudulent";

	return (
		<BaseEmail previewText={previewText}>
			<Heading style={emailStyles.heading}>
				⚠️ Fraud Alert - Lead Suspended
			</Heading>

			<Text style={emailStyles.text}>Dear {providerName || "Provider"},</Text>

			<Text style={emailStyles.text}>
				We wanted to inform you that a lead you received has been flagged as
				potentially fraudulent activity and has been suspended from our
				platform.
			</Text>

			<Section style={emailStyles.alertWarning}>
				<Text style={emailStyles.messageTitle}>Lead Details:</Text>
				{jobId && (
					<Text style={emailStyles.messageText}>
						<strong>Job ID:</strong> {jobId}
					</Text>
				)}
				{customerName && (
					<Text style={emailStyles.messageText}>
						<strong>Customer:</strong> {customerName}
					</Text>
				)}
				{customerEmail && (
					<Text style={emailStyles.messageText}>
						<strong>Customer Email:</strong> {customerEmail}
					</Text>
				)}
				{jobMessage && (
					<Text style={emailStyles.messageText}>
						<strong>Message:</strong> "{jobMessage.substring(0, 100)}
						{jobMessage.length > 100 ? "..." : ""}"
					</Text>
				)}
			</Section>

			<Text style={emailStyles.text}>
				The customer and their associated leads have been suspended from our
				platform to protect our community of providers and customers.
			</Text>

			<Text style={emailStyles.text}>
				<strong>Important:</strong> If you have already started work on this
				request or would like additional information, please contact our support
				team immediately.
			</Text>

			<Section style={emailStyles.centered}>
				<Button
					href="mailto:<EMAIL>?subject=Fraud Alert - Job ID: {jobId}"
					style={emailStyles.button}
				>
					Contact Support
				</Button>
			</Section>

			<Text style={emailStyles.text}>
				We appreciate your understanding and commitment to maintaining a safe
				and trustworthy platform for all users.
			</Text>

			<Hr style={emailStyles.hr} />

			<Text style={emailStyles.footer}>
				Thank you for being part of the RV Help community.
			</Text>

			<Text style={emailStyles.footer}>
				<strong>RV Help Team</strong>
			</Text>
		</BaseEmail>
	);
};

export default FraudNotificationEmail;
