import {
	Container,
	Heading,
	Link,
	Section,
	Text
} from "@react-email/components";
import { BaseEmail } from "./BaseEmail";

interface MarketingCampaignCouponEmailProps {
	firstName?: string;
	lastName?: string;
	email: string;
	couponCode: string;
	campaignTitle: string;
	discountType: "PERCENTAGE" | "FIXED_AMOUNT";
	discountValue: number;
	expiresAt?: Date;
	customMessage?: string;
}

const MarketingCampaignCouponEmail = ({
	firstName,
	lastName,
	email,
	couponCode,
	campaignTitle,
	discountType,
	discountValue,
	expiresAt,
	customMessage
}: MarketingCampaignCouponEmailProps) => {
	const discountText =
		discountType === "PERCENTAGE"
			? `${discountValue}% off`
			: `$${discountValue} off`;

	const previewText = `Your ${discountText} discount code is here!`;

	return (
		<BaseEmail previewText={previewText}>
			<Container style={container}>
				<Section style={content}>
					<Heading style={heading}>
						🎉 Your {discountText} discount code is here!
					</Heading>

					<Text style={text}>Hi {firstName || "there"},</Text>

					<Text style={text}>
						Thanks for your interest in RV Help Pro! Here's your exclusive
						discount code:
					</Text>

					<Section style={codeSection}>
						<Text style={codeText}>{couponCode}</Text>
					</Section>

					<Text style={text}>
						This code gives you <strong>{discountText}</strong> on your RV Help
						Pro membership.
					</Text>

					{customMessage && <Text style={text}>{customMessage}</Text>}

					<Section style={buttonSection}>
						<Link
							href={`${process.env.NEXT_PUBLIC_APP_URL}/pro-membership?coupon=${couponCode}`}
							style={button}
						>
							Claim Your Discount
						</Link>
					</Section>

					<Text style={text}>With RV Help Pro, you'll get:</Text>

					<ul style={benefitsList}>
						<li style={benefitItem}>
							✅ Unlimited pre-service diagnostic calls
						</li>
						<li style={benefitItem}>✅ Nationwide provider discounts</li>
						<li style={benefitItem}>✅ Priority support access</li>
						<li style={benefitItem}>✅ Maintenance tracking tools</li>
						<li style={benefitItem}>✅ Premium guides and resources</li>
					</ul>

					{expiresAt && (
						<Text style={expirationText}>
							⏰ This offer expires on {expiresAt.toLocaleDateString()}. Don't
							wait!
						</Text>
					)}

					<Text style={text}>
						Questions? Reply to this email or contact us at{" "}
						<Link href="mailto:<EMAIL>" style={link}>
							<EMAIL>
						</Link>
					</Text>

					<Text style={text}>
						Best regards,
						<br />
						The RV Help Team
					</Text>
				</Section>

				<Section style={footer}>
					<Text style={footerText}>
						RV Help - Your trusted partner for RV service and support
					</Text>
					<Text style={footerText}>
						<Link
							href={`${process.env.NEXT_PUBLIC_APP_URL}/unsubscribe`}
							style={link}
						>
							Unsubscribe
						</Link>
					</Text>
				</Section>
			</Container>
		</BaseEmail>
	);
};

export default MarketingCampaignCouponEmail;

// Styles
const main = {
	backgroundColor: "#f6f9fc",
	fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif'
};

const container = {
	margin: "0 auto",
	padding: "20px 0 48px",
	maxWidth: "600px"
};

const header = {
	padding: "0 48px",
	textAlign: "center" as const
};

const logo = {
	margin: "0 auto"
};

const content = {
	padding: "0 48px"
};

const heading = {
	fontSize: "32px",
	lineHeight: "1.3",
	fontWeight: "700",
	color: "#484848",
	textAlign: "center" as const,
	margin: "40px 0 20px"
};

const text = {
	fontSize: "16px",
	lineHeight: "26px",
	color: "#484848",
	margin: "16px 0"
};

const codeSection = {
	textAlign: "center" as const,
	padding: "20px",
	backgroundColor: "#f8f9fa",
	border: "2px dashed #28a745",
	borderRadius: "8px",
	margin: "24px 0"
};

const codeText = {
	fontSize: "32px",
	fontWeight: "bold",
	color: "#28a745",
	fontFamily: "monospace",
	letterSpacing: "2px",
	lineHeight: "1.2",
	margin: "0"
};

const buttonSection = {
	textAlign: "center" as const,
	margin: "32px 0"
};

const button = {
	backgroundColor: "#28a745",
	borderRadius: "8px",
	color: "#ffffff",
	fontSize: "16px",
	fontWeight: "600",
	textDecoration: "none",
	textAlign: "center" as const,
	display: "inline-block",
	padding: "16px 32px",
	border: "none",
	cursor: "pointer"
};

const benefitsList = {
	paddingLeft: "0",
	listStyle: "none",
	margin: "16px 0"
};

const benefitItem = {
	fontSize: "16px",
	lineHeight: "26px",
	color: "#484848",
	margin: "8px 0"
};

const expirationText = {
	fontSize: "16px",
	lineHeight: "26px",
	color: "#dc3545",
	fontWeight: "600",
	textAlign: "center" as const,
	padding: "16px",
	backgroundColor: "#f8d7da",
	borderRadius: "8px",
	margin: "24px 0"
};

const link = {
	color: "#067df7",
	textDecoration: "underline"
};

const footer = {
	padding: "0 48px",
	textAlign: "center" as const,
	marginTop: "48px",
	borderTop: "1px solid #e6ebf1",
	paddingTop: "24px"
};

const footerText = {
	fontSize: "14px",
	lineHeight: "20px",
	color: "#8898aa",
	margin: "4px 0"
};

export const marketingCampaignCouponText = (
	props: MarketingCampaignCouponEmailProps
) => {
	const {
		firstName,
		couponCode,
		discountType,
		discountValue,
		expiresAt,
		customMessage
	} = props;

	const discountText =
		discountType === "PERCENTAGE"
			? `${discountValue}% off`
			: `$${discountValue} off`;

	return `
Hi ${firstName || "there"},

Thanks for your interest in RV Help Pro! Here's your exclusive discount code:

${couponCode}

This code gives you ${discountText} on your RV Help Pro membership.

${customMessage ? customMessage + "\n\n" : ""}

Use this code when you upgrade to Pro membership at: ${process.env.NEXT_PUBLIC_APP_URL}/pro-membership?coupon=${couponCode}

With RV Help Pro, you'll get:
✅ Unlimited pre-service diagnostic calls
✅ Nationwide provider discounts
✅ Priority support access
✅ Maintenance tracking tools
✅ Premium guides and resources

${expiresAt ? `⏰ This offer expires on ${expiresAt.toLocaleDateString()}. Don't wait!` : ""}

Questions? Reply to this email or contact <NAME_EMAIL>

Best regards,
The RV Help Team

---
RV Help - Your trusted partner for RV service and support
Unsubscribe: ${process.env.NEXT_PUBLIC_APP_URL}/unsubscribe
  `.trim();
};
