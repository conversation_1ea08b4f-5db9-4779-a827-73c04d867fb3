// Shared styles for email templates to ensure consistency
export const emailStyles = {
  // Container styles
  container: {
    margin: "0 auto",
    padding: "20px 0 24px",
    maxWidth: "560px",
  },

  // Typography styles
  heading: {
    color: "#437F6B", // Brand green
    fontSize: "24px",
    fontWeight: "bold",
    margin: "20px 0", // Consistent smaller margin
    padding: "0",
    textAlign: "center" as const,
  },

  subheading: {
    color: "#374151",
    fontSize: "20px",
    fontWeight: "600",
    margin: "16px 0",
    padding: "0",
  },

  text: {
    color: "#374151", // Consistent dark gray
    fontSize: "16px",
    lineHeight: "24px",
    margin: "16px 0",
  },

  smallText: {
    color: "#6b7280",
    fontSize: "14px",
    lineHeight: "20px",
    margin: "12px 0",
  },

  // Button styles
  button: {
    backgroundColor: "#437F6B", // Brand green
    color: "#ffffff",
    borderRadius: "6px",
    fontSize: "16px",
    padding: "12px 24px",
    margin: "24px 0",
    textDecoration: "none",
    textAlign: "center" as const,
    display: "inline-block",
  },

  buttonBlock: {
    backgroundColor: "#437F6B", // Brand green
    color: "#ffffff",
    borderRadius: "6px",
    fontSize: "16px",
    padding: "12px 24px",
    margin: "24px 0",
    textDecoration: "none",
    textAlign: "center" as const,
    display: "block",
  },

  // Secondary button (for less prominent actions)
  buttonSecondary: {
    backgroundColor: "#f3f4f6",
    color: "#374151",
    border: "1px solid #d1d5db",
    borderRadius: "6px",
    fontSize: "14px",
    padding: "8px 16px",
    margin: "16px 0",
    textDecoration: "none",
    textAlign: "center" as const,
    display: "inline-block",
  },

  // Small button (for unsubscribe and other minor actions)
  buttonSmall: {
    backgroundColor: "#f3f4f6",
    color: "#6b7280",
    border: "1px solid #d1d5db",
    borderRadius: "4px",
    fontSize: "12px",
    padding: "6px 12px",
    margin: "12px 0",
    textDecoration: "none",
    textAlign: "center" as const,
    display: "inline-block",
  },

  // Section styles
  section: {
    backgroundColor: "#f8f9fa",
    padding: "20px",
    borderRadius: "8px",
    margin: "20px 0",
  },

  sectionWhite: {
    backgroundColor: "#ffffff",
    padding: "20px",
    borderRadius: "8px",
    margin: "20px 0",
  },

  // Message/quote styles
  messageBox: {
    backgroundColor: "#ffffff",
    border: "1px solid #e5e7eb",
    borderLeft: "4px solid #437F6B",
    padding: "16px",
    borderRadius: "8px",
    margin: "16px 0",
  },

  messageText: {
    color: "#374151",
    fontSize: "16px",
    lineHeight: "24px",
    margin: "0",
    fontStyle: "italic",
  },

  messageTitle: {
    color: "#374151",
    fontSize: "14px",
    fontWeight: "600",
    margin: "0 0 8px 0",
  },

  // Divider
  hr: {
    borderColor: "#e5e7eb",
    margin: "24px 0",
  },

  // Footer
  footer: {
    color: "#6b7280",
    fontSize: "14px",
    margin: "24px 0 0 0",
  },

  // Centered content
  centered: {
    textAlign: "center" as const,
    margin: "24px 0",
  },

  // List styles
  list: {
    margin: "16px 0",
    paddingLeft: "20px",
  },

  listItem: {
    color: "#374151",
    fontSize: "16px",
    lineHeight: "24px",
    margin: "8px 0",
  },

  // Highlight/accent styles
  highlight: {
    backgroundColor: "#fef3c7",
    padding: "2px 6px",
    borderRadius: "4px",
    fontWeight: "500",
  },

  // Alert/warning styles
  alertInfo: {
    backgroundColor: "#eff6ff",
    border: "1px solid #bfdbfe",
    borderRadius: "8px",
    padding: "16px",
    margin: "16px 0",
  },

  alertWarning: {
    backgroundColor: "#fffbeb",
    border: "1px solid #fed7aa",
    borderRadius: "8px",
    padding: "16px",
    margin: "16px 0",
  },

  alertSuccess: {
    backgroundColor: "#f0fdf4",
    border: "1px solid #bbf7d0",
    borderRadius: "8px",
    padding: "16px",
    margin: "16px 0",
  },
};

// Utility functions for common patterns
export const emailUtils = {
  // Create a greeting text style
  greetingText: {
    ...emailStyles.text,
    marginBottom: "20px",
  },

  // Create a signature/footer text
  signatureText: {
    ...emailStyles.text,
    marginTop: "32px",
    marginBottom: "8px",
  },

  // Create a button with custom text alignment
  buttonWithAlignment: (alignment: "left" | "center" | "right") => ({
    ...emailStyles.button,
    textAlign: alignment,
  }),

  // Create a section with custom background
  sectionWithBackground: (backgroundColor: string) => ({
    ...emailStyles.section,
    backgroundColor,
  }),
}; 