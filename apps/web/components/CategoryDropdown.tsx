"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { url } from "@/lib/url";
import { MagnifyingGlassIcon } from "@heroicons/react/20/solid";
import { ChevronDown, Search, Wrench } from "lucide-react";
import { useRouter } from "next/navigation";

interface CategoryDropdownProps {
	variant?: "default" | "outline";
	size?: "default" | "sm" | "lg";
	className?: string;
}

export default function CategoryDropdown({
	variant = "outline",
	size = "lg",
	className = ""
}: CategoryDropdownProps) {
	const router = useRouter();

	const handleCategorySelect = (category: "rv-repair" | "rv-inspection") => {
		router.push(url(category));
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant={variant} size={size} className={`group ${className}`}>
					<MagnifyingGlassIcon className="w-6 h-6 mr-3" />
					<span>Search Directory</span>
					<ChevronDown className="w-4 h-4 ml-2 transition-transform group-data-[state=open]:rotate-180" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="center" className="w-64">
				<DropdownMenuItem
					onClick={() => handleCategorySelect("rv-repair")}
					className="flex items-center space-x-3 p-3 cursor-pointer hover:bg-gray-50"
				>
					<div className="flex items-center justify-center w-10 h-10 bg-[#43806c]/10 rounded-lg">
						<Wrench className="w-5 h-5 text-[#43806c]" />
					</div>
					<div>
						<div className="font-medium text-gray-900">Mobile RV Repair</div>
						<div className="text-sm text-gray-500">
							Find certified mobile RV technicians
						</div>
					</div>
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={() => handleCategorySelect("rv-inspection")}
					className="flex items-center space-x-3 p-3 cursor-pointer hover:bg-gray-50"
				>
					<div className="flex items-center justify-center w-10 h-10 bg-[#43806c]/10 rounded-lg">
						<Search className="w-5 h-5 text-[#43806c]" />
					</div>
					<div>
						<div className="font-medium text-gray-900">RV Inspection</div>
						<div className="text-sm text-gray-500">
							Connect with certified NRVIA inspectors
						</div>
					</div>
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
