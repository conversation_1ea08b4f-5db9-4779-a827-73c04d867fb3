import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface VacationModeDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    listingId: string | number | undefined;
    initialEnabled?: boolean;
    initialMessage?: string;
    onSuccess?: () => void;
}

export default function VacationModeDialog({
    open,
    onOpenChange,
    listingId,
    initialEnabled = false,
    initialMessage = "",
    onSuccess,
}: VacationModeDialogProps) {
    const [vacationModeEnabled, setVacationModeEnabled] = useState(initialEnabled);
    const [vacationMessage, setVacationMessage] = useState(initialMessage);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Update state when props change
    useEffect(() => {
        setVacationModeEnabled(initialEnabled);
        setVacationMessage(initialMessage);
    }, [initialEnabled, initialMessage]);

    const handleVacationModeSave = async () => {
        if (!listingId) return;

        setIsSubmitting(true);

        try {
            const response = await fetch(`/api/listings/${listingId}/vacation-mode`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    enabled: vacationModeEnabled,
                    message: vacationModeEnabled ? vacationMessage : null
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update vacation mode settings');
            }

            const data = await response.json();

            if (data.success) {
                toast.success(
                    vacationModeEnabled
                        ? "Vacation mode enabled successfully"
                        : "Vacation mode disabled successfully"
                );
                onOpenChange(false);

                // Call success callback if provided
                if (onSuccess) {
                    onSuccess();
                }
            }
        } catch (error) {
            console.error('Error updating vacation mode:', error);
            toast.error('Failed to update vacation mode settings');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Vacation Mode</DialogTitle>
                    <DialogDescription>
                        Enable vacation mode to temporarily pause your listing and let clients know you're unavailable.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="flex items-center justify-between">
                        <Label htmlFor="vacation-mode" className="font-medium text-gray-700">
                            Enable Vacation Mode
                        </Label>
                        <Switch
                            id="vacation-mode"
                            checked={vacationModeEnabled}
                            onCheckedChange={setVacationModeEnabled}
                        />
                    </div>

                    {vacationModeEnabled && (
                        <div className="grid gap-2">
                            <Label htmlFor="vacation-message" className="font-medium text-gray-700">
                                Away message (optional)
                            </Label>
                            <Input
                                id="vacation-message"
                                placeholder="Let clients know when you'll return..."
                                value={vacationMessage}
                                onChange={(e) => setVacationMessage(e.target.value.slice(0, 150))}
                                maxLength={150}
                            />
                            <p className="text-xs text-gray-500 text-right">
                                {vacationMessage.length}/150 characters
                            </p>
                        </div>
                    )}
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleVacationModeSave}
                        className="bg-[#4d7c6f] hover:bg-[#3d635a]"
                        disabled={isSubmitting}
                    >
                        {isSubmitting ? "Saving..." : "Save Changes"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
