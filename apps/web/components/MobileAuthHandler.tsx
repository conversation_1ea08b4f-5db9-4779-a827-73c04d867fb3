"use client";

import { Loader } from "@/components/Loader";
import { useMobileAuth } from "@/hooks/useMobileAuth";
import { useMobileWebView } from "@/hooks/useMobileWebView";
import { useAuth } from "@/lib/hooks/useAuth";
import { useEffect, useRef, useState } from "react";

interface MobileAuthHandlerProps {
	children: React.ReactNode;
	onAuthSuccess?: () => void;
	onAuthFailure?: (error: string) => void;
	showLoading?: boolean;
}

// Global state to prevent multiple authentication attempts
const globalAuthState = {
	hasAttempted: false,
	isAuthenticating: false
};

export function MobileAuthHandler({
	children,
	onAuthSuccess,
	onAuthFailure,
	showLoading = true
}: MobileAuthHandlerProps) {
	const { authenticateFromUrl, getMobileTokenFromUrl } = useMobileAuth();
	const { setMobileWebView } = useMobileWebView();
	const { user } = useAuth();
	const [localState, setLocalState] = useState({
		isAuthenticating: false,
		hasAttempted: false
	});
	const effectRanRef = useRef(false);

	useEffect(() => {
		// Prevent multiple effect runs
		if (effectRanRef.current) {
			return;
		}

		// Add a small delay to ensure URL is fully available
		const handleMobileAuth = async () => {
			// If we already have a user, no need for mobile auth
			if (user) {
				globalAuthState.hasAttempted = true;
				setLocalState((prev) => ({ ...prev, hasAttempted: true }));
				return;
			}

			// If we've already attempted mobile auth globally, don't try again
			if (globalAuthState.hasAttempted) {
				setLocalState((prev) => ({ ...prev, hasAttempted: true }));
				return;
			}

			const mobileToken = getMobileTokenFromUrl();

			// Only attempt mobile auth if we have a token and no user is logged in
			if (mobileToken && !user) {
				// Set mobile web view flag immediately when we detect a mobile token
				setMobileWebView(true);

				// Set global state to prevent multiple attempts
				globalAuthState.hasAttempted = true;
				globalAuthState.isAuthenticating = true;
				setLocalState({ isAuthenticating: true, hasAttempted: false });

				try {
					const success = await authenticateFromUrl();

					if (success) {
						onAuthSuccess?.();
					} else {
						onAuthFailure?.("Failed to authenticate from mobile app");
					}
				} catch (error) {
					onAuthFailure?.("Failed to authenticate from mobile app");
				} finally {
					globalAuthState.isAuthenticating = false;
					setLocalState({ isAuthenticating: false, hasAttempted: true });
				}
			} else {
				// No mobile token or user already exists
				globalAuthState.hasAttempted = true;
				setLocalState({ isAuthenticating: false, hasAttempted: true });
			}
		};

		effectRanRef.current = true;
		handleMobileAuth();
	}, []); // Empty dependency array to run only once

	// Show loading while attempting mobile authentication
	if (
		showLoading &&
		(localState.isAuthenticating || (!localState.hasAttempted && !user))
	) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-center">
					<Loader />
					<p className="mt-4 text-gray-600">
						Authenticating from mobile app...
					</p>
				</div>
			</div>
		);
	}

	return <>{children}</>;
}
