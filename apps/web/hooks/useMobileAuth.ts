import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface MobileAuthResult {
	success: boolean;
	message: string;
	email: string;
	signInToken: string;
	redirectUrl: string;
}

export function useMobileAuth() {
	const [loading, setLoading] = useState(false);
	const router = useRouter();

	const authenticateFromMobile = async (
		mobileToken: string
	): Promise<boolean> => {
		setLoading(true);

		try {
			// Call the mobile-to-web session endpoint
			const response = await fetch("/api/auth/mobile/mobile-to-web-session", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({ token: mobileToken })
			});

			const data: MobileAuthResult = await response.json();

			if (!response.ok) {
				console.error("API call failed:", data);
				throw new Error(data.message || "Authentication failed");
			}

			// Use NextAuth to sign in with the auto sign-in token
			const signInResult = await signIn("credentials", {
				email: data.email,
				signInToken: data.signInToken,
				redirect: false
			});

			if (signInResult?.error) {
				console.error("🔐 authenticateFromMobile: NextAuth signIn failed:", signInResult.error);
				throw new Error("Failed to create web session");
			}

			toast.success("Successfully authenticated from mobile app");

			// Get the current URL and remove the mobileToken parameter
			const currentUrl = new URL(window.location.href);
			currentUrl.searchParams.delete("mobileToken");

			// Always redirect to the current page (without the mobile token) to stay on the requested page
			const redirectUrl = currentUrl.pathname + currentUrl.search;
			router.push(redirectUrl);
			return true;
		} catch (error) {
			console.error("Mobile authentication error:", error);
			toast.error(error.message || "Authentication failed");
			return false;
		} finally {
			setLoading(false);
		}
	};

	const getMobileTokenFromUrl = (): string | null => {
		if (typeof window === "undefined") {
			return null;
		}

		const urlParams = new URLSearchParams(window.location.search);

		// First, try to get mobileToken directly from the current URL
		let mobileToken = urlParams.get("mobileToken");

		// If not found, check if it's nested in a redirectUrl parameter
		if (!mobileToken) {
			const redirectUrl = urlParams.get("redirectUrl");
			if (redirectUrl) {
				try {
					const redirectUrlObj = new URL(redirectUrl, window.location.origin);
					const redirectParams = new URLSearchParams(redirectUrlObj.search);
					mobileToken = redirectParams.get("mobileToken");
				} catch (error) {
					console.error("Error parsing redirectUrl:", error);
				}
			}
		}

		return mobileToken;
	};

	const authenticateFromUrl = async (): Promise<boolean> => {
		const mobileToken = getMobileTokenFromUrl();

		if (!mobileToken) {
			return false;
		}

		const result = await authenticateFromMobile(mobileToken);
		return result;
	};

	return {
		authenticateFromMobile,
		authenticateFromUrl,
		getMobileTokenFromUrl,
		loading
	};
}
