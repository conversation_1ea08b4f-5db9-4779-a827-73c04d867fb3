import { useEffect, useState } from "react";

export function useMobileWebView() {
	const [isMobileWebView, setIsMobileWebView] = useState(false);

	useEffect(() => {
		// Check if we're in a mobile web view session
		const checkMobileWebView = () => {
			if (typeof window === "undefined") return false;

			// Check for mobile token in URL (current or previous)
			const urlParams = new URLSearchParams(window.location.search);
			const hasMobileToken = urlParams.has("mobileToken");

			// Check for mobile web view flag in sessionStorage
			const mobileWebViewFlag = sessionStorage.getItem("mobileWebView");

			return hasMobileToken || mobileWebViewFlag === "true";
		};

		const isMobile = checkMobileWebView();
		setIsMobileWebView(isMobile);

		// If we have a mobile token, set the session flag
		const urlParams = new URLSearchParams(window.location.search);
		if (urlParams.has("mobileToken")) {
			sessionStorage.setItem("mobileWebView", "true");
		}
	}, []);

	return {
		isMobileWebView,
		setMobileWebView: (value: boolean) => {
			setIsMobileWebView(value);
			if (typeof window !== "undefined") {
				if (value) {
					sessionStorage.setItem("mobileWebView", "true");
				} else {
					sessionStorage.removeItem("mobileWebView");
				}
			}
		}
	};
}
