import { POST } from "@/app/api/jobs/[id]/customer-status/route";
import { prisma as mockPrisma } from "@/tests/mocks/prisma-mock";
import { createMockRequest, mockBaseHandler } from "@/tests/utils/api-test-utils";
import { JobStatus } from "@rvhelp/database";

// Mock the QuoteStatusService
jest.mock("@/lib/services/quote-status.service", () => ({
    QuoteStatusService: {
        customerCompleteJob: jest.fn(),
        customerCancelJob: jest.fn(),
    }
}));

describe("/api/jobs/[id]/customer-status", () => {
    const mockUserId = "user-123";
    const mockJobId = "job-456";

    const mockJob = {
        id: mockJobId,
        user_id: mockUserId,
        status: JobStatus.OPEN,
        accepted_quote_id: null,
        updated_at: new Date(),
        quotes: [],
        user: {
            id: mockUserId,
            first_name: "Test",
            last_name: "User",
            email: "<EMAIL>"
        }
    };

    const mockCompletedJob = {
        ...mockJob,
        status: JobStatus.COMPLETED,
        updated_at: new Date(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = { id: mockUserId, role: "USER" };
        mockPrisma.job.findFirst.mockImplementation(async (params) => {
            // Only return job if the user_id in the query matches the current user
            if (params?.where?.user_id === mockBaseHandler.user?.id) {
                return mockJob;
            }
            return null;
        });
    });

    describe("POST - Complete Job", () => {
        it("should successfully complete a job with selected provider", async () => {
            const { QuoteStatusService } = await import("@/lib/services/quote-status.service");
            (QuoteStatusService.customerCompleteJob as jest.Mock).mockResolvedValue(mockCompletedJob);

            const request = createMockRequest({
                method: "POST",
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully",
                    selectedProviderId: "provider-123",
                }
            });

            const response = await POST(request, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);
            expect(data.message).toBe("Job successfully marked as completed");
            expect(data.job.status).toBe(JobStatus.COMPLETED);

            // Verify QuoteStatusService was called correctly
            expect(QuoteStatusService.customerCompleteJob).toHaveBeenCalledWith({
                jobId: mockJobId,
                userId: mockUserId,
                reason: "Work finished successfully",
                selectedProviderId: "provider-123",
            });
        });

        it("should successfully complete a job without selected provider", async () => {
            const { QuoteStatusService } = await import("@/lib/services/quote-status.service");
            (QuoteStatusService.customerCompleteJob as jest.Mock).mockResolvedValue(mockCompletedJob);

            const request = createMockRequest({
                method: "POST",
                body: {
                    status: "COMPLETED",
                    reason: "Completed work myself",
                }
            });

            const response = await POST(request, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);

            expect(QuoteStatusService.customerCompleteJob).toHaveBeenCalledWith({
                jobId: mockJobId,
                userId: mockUserId,
                reason: "Completed work myself",
                selectedProviderId: undefined,
            });
        });

        it("should return 404 if job not found", async () => {
            mockPrisma.job.findFirst.mockResolvedValue(null);

            const request = createMockRequest({
                method: "POST",
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully",
                }
            });

            const response = await POST(request, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data.error).toBe("Job not found or access denied");
        });

        it("should return 400 if job already completed", async () => {
            mockPrisma.job.findFirst.mockResolvedValue({
                ...mockJob,
                status: JobStatus.COMPLETED,
            });

            const request = createMockRequest({
                method: "POST",
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully",
                }
            });

            const response = await POST(request, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Job is already in a final state");
        });

        it("should return 400 if reason is missing", async () => {
            const request = createMockRequest({
                method: "POST",
                body: {
                    status: "COMPLETED",
                    // Missing reason
                }
            });

            const response = await POST(request, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toContain("Reason is required");
        });

        it("should handle QuoteStatusService errors", async () => {
            const { QuoteStatusService } = await import("@/lib/services/quote-status.service");
            (QuoteStatusService.customerCompleteJob as jest.Mock).mockRejectedValue(
                new Error("Selected provider not found")
            );

            const request = createMockRequest({
                method: "POST",
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully",
                    selectedProviderId: "non-existent-provider",
                }
            });

            const response = await POST(request, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Selected provider not found");
        });
    });

    describe("POST - Cancel Job", () => {
        it("should successfully cancel a job", async () => {
            const { QuoteStatusService } = await import("@/lib/services/quote-status.service");
            const mockCancelledJob = { ...mockJob, status: JobStatus.CANCELLED };
            (QuoteStatusService.customerCancelJob as jest.Mock).mockResolvedValue(mockCancelledJob);

            const request = createMockRequest({
                method: "POST",
                body: {
                    status: "CANCELLED",
                    reason: "No longer needed",
                }
            });

            const response = await POST(request, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);
            expect(data.message).toBe("Job successfully marked as cancelled");
            expect(data.job.status).toBe(JobStatus.CANCELLED);

            expect(QuoteStatusService.customerCancelJob).toHaveBeenCalledWith({
                jobId: mockJobId,
                userId: mockUserId,
                reason: "No longer needed",
            });
        });

        it("should handle cancellation errors", async () => {
            const { QuoteStatusService } = await import("@/lib/services/quote-status.service");
            (QuoteStatusService.customerCancelJob as jest.Mock).mockRejectedValue(
                new Error("Database connection failed")
            );

            const request = createMockRequest({
                method: "POST",
                body: {
                    status: "CANCELLED",
                    reason: "No longer needed",
                }
            });

            const response = await POST(request, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Database connection failed");
        });
    });

    describe("Authentication", () => {
        it("should require authentication", async () => {
            mockBaseHandler.user = null; // Not authenticated

            const request = createMockRequest({
                method: "POST",
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully",
                }
            });

            const response = await POST(request, { params: { id: mockJobId } });

            expect(response.status).toBe(401);
        });

        it("should only allow job owner to update status", async () => {
            mockBaseHandler.user = { id: "different-user", role: "USER" };

            // Explicitly mock that no job is found for a different user
            mockPrisma.job.findFirst.mockResolvedValue(null);

            const request = createMockRequest({
                method: "POST",
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully",
                }
            });

            const response = await POST(request, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data.error).toBe("Job not found or access denied");
        });
    });
}); 