import { beforeEach, describe, expect, it, jest } from '@jest/globals';

// Mock Next.js router
const mockPush = jest.fn();

jest.mock('next/navigation', () => ({
    useRouter: () => ({
        push: mockPush,
    }),
}));

describe('Warranty Lead Success Dialog Functionality', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should show warranty success dialog for warranty jobs', () => {
        // Simulate the logic that determines if a job is a warranty job
        const job = {
            warranty_request_id: 'warranty-123',
            first_name: '<PERSON>',
            last_name: '<PERSON><PERSON>'
        };

        const isWarrantyJob = Boolean(job.warranty_request_id);

        expect(isWarrantyJob).toBe(true);
    });

    it('should not show warranty success dialog for regular jobs', () => {
        // Simulate the logic that determines if a job is a warranty job
        const job = {
            warranty_request_id: null,
            first_name: '<PERSON>',
            last_name: '<PERSON><PERSON>'
        };

        const isWarrantyJob = Boolean(job.warranty_request_id);

        expect(isWarrantyJob).toBe(false);
    });

    it('should navigate to jobs page when warranty success dialog button is clicked', () => {
        // Simulate the navigation logic
        const leadId = 'lead-123';
        const targetUrl = `/provider/jobs/${leadId}`;

        mockPush(targetUrl);

        expect(mockPush).toHaveBeenCalledWith(targetUrl);
        expect(mockPush).toHaveBeenCalledTimes(1);
    });

    it('should handle the complete flow from accepting warranty lead to showing success dialog', () => {
        // Simulate the complete flow
        const leadId = 'lead-123';
        const job = {
            warranty_request_id: 'warranty-123',
            first_name: 'John',
            last_name: 'Doe'
        };

        // Step 1: Check if it's a warranty job
        const isWarrantyJob = Boolean(job.warranty_request_id);
        expect(isWarrantyJob).toBe(true);

        // Step 2: Simulate showing the success dialog
        const showWarrantySuccessDialog = true;
        expect(showWarrantySuccessDialog).toBe(true);

        // Step 3: Simulate clicking the navigation button
        const targetUrl = `/provider/jobs/${leadId}`;
        mockPush(targetUrl);

        expect(mockPush).toHaveBeenCalledWith(targetUrl);
    });

    it('should handle regular lead acceptance flow (no warranty success dialog)', () => {
        // Simulate the complete flow for regular leads
        const leadId = 'lead-123';
        const job = {
            warranty_request_id: null,
            first_name: 'John',
            last_name: 'Doe'
        };

        // Step 1: Check if it's a warranty job
        const isWarrantyJob = Boolean(job.warranty_request_id);
        expect(isWarrantyJob).toBe(false);

        // Step 2: For regular leads, should not show warranty success dialog
        const showWarrantySuccessDialog = false;
        expect(showWarrantySuccessDialog).toBe(false);

        // Step 3: Should stay on the same page and refresh data
        const shouldRefreshData = true;
        expect(shouldRefreshData).toBe(true);
    });

    it('should properly handle the AcceptLeadModal state transitions', () => {
        // Simulate the state transitions in AcceptLeadModal
        const states = ['idle', 'success', 'error'];

        // Test state transitions for warranty jobs
        const isWarrantyJob = true;
        const finalState = isWarrantyJob ? 'idle' : 'success'; // For warranty jobs, we call onResponseSent immediately

        expect(finalState).toBe('idle');

        // Test state transitions for regular jobs
        const isRegularJob = false;
        const regularJobFinalState = isRegularJob ? 'idle' : 'success';

        expect(regularJobFinalState).toBe('success');
    });
}); 