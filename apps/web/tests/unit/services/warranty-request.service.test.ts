import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";
import { FirstPromoterService } from "@/lib/services/first-promoter.service";
import { smsService } from "@/lib/services/sms.service";
import { WarrantyRequestService } from "@/lib/services/warranty-request.service";
import { mockPrisma } from "@/tests/mocks/prisma-mock";
import bcrypt from "bcryptjs";

// Mock external services
jest.mock("@/lib/services/emailNewsletter.service");
jest.mock("@/lib/services/first-promoter.service");
jest.mock("@/lib/services/sms.service", () => ({
    smsService: {
        send: jest.fn().mockResolvedValue(undefined),
        sendToProvider: jest.fn().mockResolvedValue(undefined),
        sendToUser: jest.fn().mockResolvedValue(undefined)
    }
}));
jest.mock("bcryptjs");

const mockEmailNewsletterService = EmailNewsletterService as jest.Mocked<typeof EmailNewsletterService>;
const mockFirstPromoterService = FirstPromoterService as jest.Mocked<typeof FirstPromoterService>;
const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;
const mockSmsService = smsService as jest.Mocked<typeof smsService>;

describe("WarrantyRequestService", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockBcrypt.hash.mockResolvedValue("hashed-password");
    });

    describe("checkUserState", () => {
        it("should return correct state for non-existent user", async () => {
            mockPrisma.user.findFirst.mockResolvedValue(null);

            const result = await WarrantyRequestService.checkUserState("<EMAIL>");

            expect(result).toEqual({
                userExists: false,
                hasLoggedIn: false,
                needsPasswordSetup: false
            });

            expect(mockPrisma.user.findFirst).toHaveBeenCalledWith({
                where: {
                    email: {
                        equals: "<EMAIL>",
                        mode: "insensitive"
                    }
                },
                select: {
                    id: true,
                    last_login: true,
                    password: true
                }
            });
        });

        it("should return correct state for user who has logged in", async () => {
            const mockUser = {
                id: "user-123",
                last_login: new Date(),
                password: "hashed-password"
            };

            mockPrisma.user.findFirst.mockResolvedValue(mockUser);

            const result = await WarrantyRequestService.checkUserState("<EMAIL>");

            expect(result).toEqual({
                userExists: true,
                hasLoggedIn: true,
                needsPasswordSetup: false
            });
        });

        it("should return needsPasswordSetup for user without password and no login", async () => {
            const mockUser = {
                id: "user-123",
                last_login: null,
                password: null
            };

            mockPrisma.user.findFirst.mockResolvedValue(mockUser);

            const result = await WarrantyRequestService.checkUserState("<EMAIL>");

            expect(result).toEqual({
                userExists: true,
                hasLoggedIn: false,
                needsPasswordSetup: true
            });
        });

        it("should return needsPasswordSetup for user with password but no login", async () => {
            const mockUser = {
                id: "user-123",
                last_login: null,
                password: "hashed-password"
            };

            mockPrisma.user.findFirst.mockResolvedValue(mockUser);

            const result = await WarrantyRequestService.checkUserState("<EMAIL>");

            expect(result).toEqual({
                userExists: true,
                hasLoggedIn: false,
                needsPasswordSetup: true
            });
        });
    });

    describe("setWarrantyPassword", () => {
        const validParams = {
            firstName: "John",
            lastName: "Doe",
            email: "<EMAIL>",
            password: "password123",
            newsletterSourceTag: "Added by Test Company",
            companyName: "Test Company"
        };

        it("should successfully set password and subscribe to newsletter", async () => {
            const existingUser = {
                id: "user-123",
                email: "<EMAIL>",
                password: null,
                first_name: "John",
                last_name: "Doe"
            };

            const updatedUser = {
                id: "user-123",
                email: "<EMAIL>",
                first_name: "John",
                last_name: "Doe",
                password: "hashed-password"
            };

            mockPrisma.user.findFirst.mockResolvedValue(existingUser);
            mockPrisma.user.update.mockResolvedValue(updatedUser);
            mockEmailNewsletterService.syncNewsletterSubscriber.mockResolvedValue(true);
            mockFirstPromoterService.trackNewsletterSignup.mockResolvedValue();

            const result = await WarrantyRequestService.setWarrantyPassword(validParams);

            expect(result).toEqual({
                success: true,
                message: "Password set successfully",
                user: {
                    id: "user-123",
                    email: "<EMAIL>",
                    first_name: "John",
                    last_name: "Doe"
                }
            });

            // Verify password was hashed
            expect(mockBcrypt.hash).toHaveBeenCalledWith("password123", 12);

            // Verify user was updated
            expect(mockPrisma.user.update).toHaveBeenCalledWith({
                where: { id: "user-123" },
                data: {
                    password: "hashed-password",
                    first_name: "John",
                    last_name: "Doe",
                    email_verified_at: expect.any(Date),
                    last_login: expect.any(Date),
                    newsletter_subscribed: true
                }
            });

            // Verify newsletter subscription
            expect(mockEmailNewsletterService.syncNewsletterSubscriber).toHaveBeenCalledWith({
                email: "<EMAIL>",
                first_name: "John",
                last_name: "Doe",
                user: updatedUser,
                tags: [
                    "Consumer Action: Logged In",
                    "Added by Test Company"
                ]
            });

            // Verify FirstPromoter tracking
            expect(mockFirstPromoterService.trackNewsletterSignup).toHaveBeenCalledWith({
                email: "<EMAIL>",
                userId: "user-123"
            });
        });

        it("should throw error for non-existent user", async () => {
            mockPrisma.user.findFirst.mockResolvedValue(null);

            await expect(WarrantyRequestService.setWarrantyPassword(validParams))
                .rejects.toThrow("User not found");
        });

        it("should throw error for user who already has password", async () => {
            const existingUser = {
                id: "user-123",
                email: "<EMAIL>",
                password: "existing-password",
                first_name: "John",
                last_name: "Doe"
            };

            mockPrisma.user.findFirst.mockResolvedValue(existingUser);

            await expect(WarrantyRequestService.setWarrantyPassword(validParams))
                .rejects.toThrow("User already has a password set. Please use login instead.");
        });

        it("should continue if newsletter service fails", async () => {
            const existingUser = {
                id: "user-123",
                email: "<EMAIL>",
                password: null,
                first_name: "John",
                last_name: "Doe"
            };

            const updatedUser = {
                id: "user-123",
                email: "<EMAIL>",
                first_name: "John",
                last_name: "Doe"
            };

            mockPrisma.user.findFirst.mockResolvedValue(existingUser);
            mockPrisma.user.update.mockResolvedValue(updatedUser);
            mockEmailNewsletterService.syncNewsletterSubscriber.mockRejectedValue(new Error("Newsletter failed"));

            // Should not throw despite newsletter failure
            const result = await WarrantyRequestService.setWarrantyPassword(validParams);

            expect(result.success).toBe(true);
        });
    });

    describe("createWarrantyUser", () => {
        const createParams = {
            email: "<EMAIL>",
            firstName: "Jane",
            lastName: "Smith",
            phone: "555-1234",
            companyName: "Test Company",
            source: "Warranty Request"
        };

        it("should create new user and subscribe to newsletter", async () => {
            const newUser = {
                id: "user-456",
                email: "<EMAIL>",
                first_name: "Jane",
                last_name: "Smith",
                phone: "555-1234"
            };

            mockPrisma.user.findFirst.mockResolvedValue(null); // User doesn't exist
            mockPrisma.user.create.mockResolvedValue(newUser);

            const result = await WarrantyRequestService.createWarrantyUser(createParams);

            expect(result).toEqual(newUser);

            expect(mockPrisma.user.create).toHaveBeenCalledWith({
                data: {
                    email: "<EMAIL>",
                    first_name: "Jane",
                    last_name: "Smith",
                    phone: "555-1234",
                    newsletter_subscribed: true,
                    email_verified_at: expect.any(Date)
                }
            });

            // Newsletter service should not be called during user creation
            expect(mockEmailNewsletterService.syncNewsletterSubscriber).not.toHaveBeenCalled();
        });

        it("should return existing user if already exists", async () => {
            const existingUser = {
                id: "user-123",
                email: "<EMAIL>",
                first_name: "John",
                last_name: "Doe"
            };

            mockPrisma.user.findFirst.mockResolvedValue(existingUser);

            const result = await WarrantyRequestService.createWarrantyUser({
                ...createParams,
                email: "<EMAIL>"
            });

            expect(result).toEqual(existingUser);
            expect(mockPrisma.user.create).not.toHaveBeenCalled();
        });
    });

    describe("sendWarrantySmsNotification", () => {
        beforeEach(() => {
            // Reset SMS service mock
            jest.clearAllMocks();
        });

        it("should send warranty SMS notification when customer selects provider for warranty work", async () => {
            const mockQuote = {
                id: "quote123",
                job: {
                    warranty_request: {
                        company: {
                            name: "Test Company"
                        },
                        approved_hours: 4
                    }
                },
                listing: {
                    notification_sms: "+**********",
                    business_name: "Test Business",
                    first_name: "John",
                    last_name: "Doe"
                }
            };

            mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
            (mockSmsService.sendToProvider as jest.Mock).mockResolvedValue({} as any);

            await WarrantyRequestService.sendWarrantySmsNotification("quote123");

            expect(mockSmsService.sendToProvider).toHaveBeenCalledWith(
                "+**********",
                expect.stringContaining("Congratulations! You've accepted a Test Company warranty lead!")
            );
            expect(mockSmsService.sendToProvider).toHaveBeenCalledWith(
                "+**********",
                expect.stringContaining("4 hours")
            );
        });

        it("should send warranty SMS notification when provider starts job immediately", async () => {
            const mockQuote = {
                id: "quote456",
                job: {
                    warranty_request: {
                        company: {
                            name: "Another Company"
                        },
                        approved_hours: 6
                    }
                },
                listing: {
                    notification_sms: "+**********",
                    business_name: "Another Business",
                    first_name: "Jane",
                    last_name: "Smith"
                }
            };

            mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
            (mockSmsService.sendToProvider as jest.Mock).mockResolvedValue({} as any);

            await WarrantyRequestService.sendWarrantySmsNotification("quote456");

            expect(mockSmsService.sendToProvider).toHaveBeenCalledWith(
                "+**********",
                expect.stringContaining("Congratulations! You've accepted a Another Company warranty lead!")
            );
            expect(mockSmsService.sendToProvider).toHaveBeenCalledWith(
                "+**********",
                expect.stringContaining("6 hours")
            );
        });

        it("should not send SMS when quote does not exist", async () => {
            mockPrisma.quote.findUnique.mockResolvedValue(null);

            await WarrantyRequestService.sendWarrantySmsNotification("nonexistent");

            expect(mockSmsService.sendToProvider).not.toHaveBeenCalled();
        });

        it("should not send SMS when warranty request does not exist", async () => {
            const mockQuote = {
                id: "quote123",
                job: {
                    warranty_request: null
                }
            };

            mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);

            await WarrantyRequestService.sendWarrantySmsNotification("quote123");

            expect(mockSmsService.sendToProvider).not.toHaveBeenCalled();
        });

        it("should not send SMS when provider has no phone number", async () => {
            const mockQuote = {
                id: "quote123",
                job: {
                    warranty_request: {
                        company: {
                            name: "Test Company"
                        }
                    }
                },
                listing: {
                    notification_sms: null,
                    phone: null,
                    business_name: "Test Business"
                }
            };

            mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);

            await WarrantyRequestService.sendWarrantySmsNotification("quote123");

            expect(mockSmsService.sendToProvider).not.toHaveBeenCalled();
        });

        it("should fallback to listing.phone when notification_sms is not available", async () => {
            const mockQuote = {
                id: "quote123",
                job: {
                    warranty_request: {
                        company: {
                            name: "Test Company"
                        }
                    }
                },
                listing: {
                    notification_sms: null,
                    phone: "+**********",
                    business_name: "Test Business"
                }
            };

            mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
            (mockSmsService.sendToProvider as jest.Mock).mockResolvedValue({} as any);

            await WarrantyRequestService.sendWarrantySmsNotification("quote123");

            expect(mockSmsService.sendToProvider).toHaveBeenCalledWith(
                "+**********",
                expect.any(String)
            );
        });

        it("should handle SMS sending errors gracefully", async () => {
            const mockQuote = {
                id: "quote123",
                job: {
                    warranty_request: {
                        company: {
                            name: "Test Company"
                        }
                    }
                },
                listing: {
                    notification_sms: "+**********",
                    business_name: "Test Business"
                }
            };

            mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
            (mockSmsService.sendToProvider as jest.Mock).mockRejectedValue(new Error("SMS failed"));

            // Should not throw error
            await expect(WarrantyRequestService.sendWarrantySmsNotification("quote123")).resolves.toBeUndefined();
        });

        it("should handle database errors gracefully", async () => {
            mockPrisma.quote.findUnique.mockRejectedValue(new Error("Database error"));

            // Should not throw error
            await expect(WarrantyRequestService.sendWarrantySmsNotification("quote123")).resolves.toBeUndefined();
        });
    });
});