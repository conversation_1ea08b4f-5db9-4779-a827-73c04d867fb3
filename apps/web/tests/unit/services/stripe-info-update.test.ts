// Mock Stripe
jest.mock("stripe", () => {
    return jest.fn().mockImplementation(() => ({
        accounts: {
            retrieve: jest.fn()
        }
    }));
});

// Mock prisma
jest.mock("@/lib/prisma", () => ({
    __esModule: true,
    default: {
        stripeConnection: {
            findUnique: jest.fn(),
            update: jest.fn()
        }
    }
}));

import prisma from "@/lib/prisma";
import { StripeService } from "@/lib/services/stripe.service";
import { stripe } from "@/lib/stripe";

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockStripeAccounts = (stripe as any).accounts;

describe("StripeService Info Update Flag", () => {
    const testUserId = "test-user-123";
    const testAccountId = "acct_test123";

    beforeEach(() => {
        jest.clearAllMocks();

        // Mock default stripe connection
        mockPrisma.stripeConnection.findUnique.mockResolvedValue({
            id: "conn-123",
            user_id: testUserId,
            stripe_account_id: testAccountId,
            stripe_account_status: "active",
            payments_enabled: true,
            details_submitted: true,
            payouts_enabled: true,
            is_verified: true,
            needs_info_update: false,
            created_at: new Date(),
            updated_at: new Date()
        });
    });

    describe("accountNeedsInfoUpdate", () => {
        it("should return false when account does not need update", async () => {
            const needsUpdate = await StripeService.accountNeedsInfoUpdate(testUserId);
            expect(needsUpdate).toBe(false);
            expect(mockPrisma.stripeConnection.findUnique).toHaveBeenCalledWith({
                where: { user_id: testUserId }
            });
        });

        it("should return true when account needs update", async () => {
            mockPrisma.stripeConnection.findUnique.mockResolvedValue({
                id: "conn-123",
                user_id: testUserId,
                stripe_account_id: testAccountId,
                stripe_account_status: "active",
                payments_enabled: true,
                details_submitted: true,
                payouts_enabled: true,
                is_verified: true,
                needs_info_update: true,
                created_at: new Date(),
                updated_at: new Date()
            });

            const needsUpdate = await StripeService.accountNeedsInfoUpdate(testUserId);
            expect(needsUpdate).toBe(true);
        });

        it("should return false when no stripe connection exists", async () => {
            mockPrisma.stripeConnection.findUnique.mockResolvedValue(null);

            const needsUpdate = await StripeService.accountNeedsInfoUpdate("non-existent-user");
            expect(needsUpdate).toBe(false);
        });
    });

    describe("markAccountNeedsUpdate", () => {
        it("should set needs_info_update to true", async () => {
            mockPrisma.stripeConnection.update.mockResolvedValue({
                id: "conn-123",
                user_id: testUserId,
                stripe_account_id: testAccountId,
                stripe_account_status: "active",
                payments_enabled: true,
                details_submitted: true,
                payouts_enabled: true,
                is_verified: true,
                needs_info_update: true,
                created_at: new Date(),
                updated_at: new Date()
            });

            await StripeService.markAccountNeedsUpdate(testUserId);

            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { user_id: testUserId },
                data: { needs_info_update: true }
            });
        });
    });

    describe("clearAccountUpdateFlag", () => {
        it("should set needs_info_update to false", async () => {
            mockPrisma.stripeConnection.update.mockResolvedValue({
                id: "conn-123",
                user_id: testUserId,
                stripe_account_id: testAccountId,
                stripe_account_status: "active",
                payments_enabled: true,
                details_submitted: true,
                payouts_enabled: true,
                is_verified: true,
                needs_info_update: false,
                created_at: new Date(),
                updated_at: new Date()
            });

            await StripeService.clearAccountUpdateFlag(testUserId);

            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { user_id: testUserId },
                data: { needs_info_update: false }
            });
        });
    });

    describe("syncAccountRequirements", () => {
        it("should set needs_info_update to true when account has requirements", async () => {
            const mockAccount = {
                id: testAccountId,
                details_submitted: true,
                charges_enabled: true,
                payouts_enabled: true,
                requirements: {
                    currently_due: ["individual.verification.document"],
                    eventually_due: [],
                    past_due: []
                }
            };

            mockStripeAccounts.retrieve.mockResolvedValue(mockAccount);
            mockPrisma.stripeConnection.update.mockResolvedValue({
                id: "conn-123",
                user_id: testUserId,
                stripe_account_id: testAccountId,
                stripe_account_status: "active",
                payments_enabled: true,
                details_submitted: true,
                payouts_enabled: true,
                is_verified: true,
                needs_info_update: true,
                created_at: new Date(),
                updated_at: new Date()
            });

            await StripeService.syncAccountRequirements(testAccountId);

            expect(mockStripeAccounts.retrieve).toHaveBeenCalledWith(testAccountId);
            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { stripe_account_id: testAccountId },
                data: {
                    needs_info_update: true,
                    stripe_account_status: "active",
                    payments_enabled: true,
                    details_submitted: true,
                    payouts_enabled: true,
                    is_verified: true
                }
            });
        });

        it("should set needs_info_update to false when account has no requirements", async () => {
            const mockAccount = {
                id: testAccountId,
                details_submitted: true,
                charges_enabled: true,
                payouts_enabled: true,
                requirements: {
                    currently_due: [],
                    eventually_due: [],
                    past_due: []
                }
            };

            mockStripeAccounts.retrieve.mockResolvedValue(mockAccount);
            mockPrisma.stripeConnection.update.mockResolvedValue({
                id: "conn-123",
                user_id: testUserId,
                stripe_account_id: testAccountId,
                stripe_account_status: "active",
                payments_enabled: true,
                details_submitted: true,
                payouts_enabled: true,
                is_verified: true,
                needs_info_update: false,
                created_at: new Date(),
                updated_at: new Date()
            });

            await StripeService.syncAccountRequirements(testAccountId);

            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { stripe_account_id: testAccountId },
                data: {
                    needs_info_update: false,
                    stripe_account_status: "active",
                    payments_enabled: true,
                    details_submitted: true,
                    payouts_enabled: true,
                    is_verified: true
                }
            });
        });

        it("should handle eventually_due requirements", async () => {
            const mockAccount = {
                id: testAccountId,
                details_submitted: true,
                charges_enabled: true,
                payouts_enabled: true,
                requirements: {
                    currently_due: [],
                    eventually_due: ["individual.address.line1"],
                    past_due: []
                }
            };

            mockStripeAccounts.retrieve.mockResolvedValue(mockAccount);

            await StripeService.syncAccountRequirements(testAccountId);

            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { stripe_account_id: testAccountId },
                data: expect.objectContaining({
                    needs_info_update: true
                })
            });
        });

        it("should handle past_due requirements", async () => {
            const mockAccount = {
                id: testAccountId,
                details_submitted: false,
                charges_enabled: false,
                payouts_enabled: false,
                requirements: {
                    currently_due: [],
                    eventually_due: [],
                    past_due: ["individual.email"]
                }
            };

            mockStripeAccounts.retrieve.mockResolvedValue(mockAccount);

            await StripeService.syncAccountRequirements(testAccountId);

            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { stripe_account_id: testAccountId },
                data: {
                    needs_info_update: true,
                    stripe_account_status: "pending",
                    payments_enabled: false,
                    details_submitted: false,
                    payouts_enabled: false,
                    is_verified: false
                }
            });
        });
    });
});
