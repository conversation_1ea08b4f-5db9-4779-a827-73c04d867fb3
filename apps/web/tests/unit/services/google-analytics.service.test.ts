// Mock config before importing the service
jest.mock('@/config', () => ({
    __esModule: true,
    default: {
        isDevelopment: false
    }
}));

import { GoogleAnalyticsService } from '../../../lib/services/google-analytics.service';

// Mock window object for testing
const mockWindow = {
    dataLayer: [],
    gtag: jest.fn(),
    location: {
        hostname: 'rvhelp.com'
    }
};

Object.defineProperty(global, 'window', {
    value: mockWindow,
    writable: true
});

// Mock fetch for server-side tracking
global.fetch = jest.fn();
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('GoogleAnalyticsService', () => {
    beforeEach(() => {
        // Clear mocks before each test
        jest.clearAllMocks();
        mockWindow.dataLayer = [];
        mockWindow.gtag = jest.fn();
        mockFetch.mockClear();

        // Reset config mock to production
        jest.resetModules();
        jest.doMock('@/config', () => ({
            __esModule: true,
            default: {
                isDevelopment: false
            }
        }));
    });

    describe('production tracking', () => {
        beforeEach(() => {
            // Ensure we're in production mode
            jest.doMock('@/config', () => ({
                __esModule: true,
                default: {
                    isDevelopment: false
                }
            }));
        });

        describe('trackLeadFormSubmission', () => {
            it('should track lead form submission with correct parameters', () => {
                const mockData = {
                    listingId: 'test-listing-id',
                    listingName: 'Test Provider',
                    category: 'rv-repair',
                    userId: 'test-user-id',
                    isAnonymous: false
                };

                GoogleAnalyticsService.trackLeadFormSubmission(mockData);

                expect(mockWindow.gtag).toHaveBeenCalledWith('event', 'lead_form_submission', {
                    event_label: 'lead_form_submission',
                    listing_id: 'test-listing-id',
                    listing_name: 'Test Provider',
                    category: 'rv-repair',
                    user_id: 'test-user-id',
                    is_anonymous: false,
                    value: 1,
                    event_category: 'conversion'
                });

                expect(mockWindow.gtag).toHaveBeenCalledWith('event', 'conversion', {
                    'send_to': 'AW-***********/pyNgCJyai-EaEJ_4hZNA',
                    'value': 1,
                    'currency': 'USD'
                });
            });
        });

        describe('trackProviderContact', () => {
            it('should track provider contact click with correct parameters', () => {
                const mockData = {
                    listingId: 'test-listing-id',
                    listingName: 'Test Provider',
                    contactType: 'phone' as const,
                    contactValue: '************',
                    userId: 'test-user-id',
                    isAnonymous: false
                };

                GoogleAnalyticsService.trackProviderContact(mockData);

                expect(mockWindow.gtag).toHaveBeenCalledWith('event', 'provider_contact_click', {
                    event_label: 'provider_contact_phone',
                    listing_id: 'test-listing-id',
                    listing_name: 'Test Provider',
                    contact_type: 'phone',
                    contact_value: '************',
                    user_id: 'test-user-id',
                    is_anonymous: false,
                    value: 1,
                    event_category: 'conversion'
                });

                expect(mockWindow.gtag).toHaveBeenCalledWith('event', 'conversion', {
                    'send_to': 'AW-***********/ZAUdCKO-sPEaEJ_4hZNA',
                    'value': 1,
                    'currency': 'USD'
                });
            });
        });

        describe('trackAccountCreation', () => {
            it('should track account creation with correct parameters', () => {
                const mockData = {
                    userId: 'test-user-id',
                    email: '<EMAIL>',
                    source: 'website',
                    referrer: 'https://google.com'
                };

                GoogleAnalyticsService.trackAccountCreation(mockData);

                expect(mockWindow.gtag).toHaveBeenCalledWith('event', 'account_creation', {
                    event_label: 'account_creation',
                    user_id: 'test-user-id',
                    email: '<EMAIL>',
                    source: 'website',
                    referrer: 'https://google.com',
                    value: 1,
                    event_category: 'conversion'
                });
            });
        });

        describe('trackEvent', () => {
            it('should track custom event with correct parameters', () => {
                const eventName = 'custom_event';
                const parameters = {
                    custom_param: 'test_value',
                    event_label: 'custom_label'
                };

                GoogleAnalyticsService.trackEvent(eventName, parameters);

                expect(mockWindow.gtag).toHaveBeenCalledWith('event', 'custom_event', {
                    custom_param: 'test_value',
                    event_label: 'custom_label',
                    event_category: 'conversion',
                    value: 1
                });
            });
        });

        describe('Server-side tracking methods', () => {
            beforeEach(() => {
                // Mock environment variables
                process.env.GA_API_SECRET = 'test-secret';
            });

            afterEach(() => {
                delete process.env.GA_API_SECRET;
            });

            describe('trackServerEvent', () => {
                it('should track server event with correct parameters', async () => {
                    mockFetch.mockResolvedValueOnce({
                        ok: true,
                        status: 200
                    } as Response);

                    const eventName = 'test_event';
                    const parameters = {
                        userId: 'test-user-id',
                        custom_param: 'test_value'
                    };

                    await GoogleAnalyticsService.trackServerEvent(eventName, parameters);

                    expect(mockFetch).toHaveBeenCalledWith(
                        'https://www.google-analytics.com/mp/collect?measurement_id=G-RJ4TCPN4LS&api_secret=test-secret',
                        {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                client_id: 'test-user-id',
                                events: [{
                                    name: 'test_event',
                                    params: {
                                        userId: 'test-user-id',
                                        custom_param: 'test_value',
                                        event_category: 'conversion',
                                        event_label: 'test_event',
                                        value: 1
                                    }
                                }]
                            })
                        }
                    );
                });

                it('should handle missing API secret gracefully', async () => {
                    delete process.env.GA_API_SECRET;

                    const eventName = 'test_event';
                    const parameters = { userId: 'test-user-id' };

                    await GoogleAnalyticsService.trackServerEvent(eventName, parameters);

                    expect(mockFetch).not.toHaveBeenCalled();
                });

                it('should handle fetch errors gracefully', async () => {
                    process.env.GA_API_SECRET = 'test-secret';
                    mockFetch.mockRejectedValueOnce(new Error('Network error'));

                    const eventName = 'test_event';
                    const parameters = { userId: 'test-user-id' };

                    await GoogleAnalyticsService.trackServerEvent(eventName, parameters);

                    expect(mockFetch).toHaveBeenCalled();
                });
            });

            describe('trackServerLeadFormSubmission', () => {
                it('should track server lead form submission', async () => {
                    mockFetch.mockResolvedValueOnce({
                        ok: true,
                        status: 200
                    } as Response);

                    const data = {
                        listingId: 'test-listing-id',
                        listingName: 'Test Provider',
                        category: 'rv-repair',
                        userId: 'test-user-id',
                        isAnonymous: false
                    };

                    await GoogleAnalyticsService.trackServerLeadFormSubmission(data);

                    expect(mockFetch).toHaveBeenCalledWith(
                        'https://www.google-analytics.com/mp/collect?measurement_id=G-RJ4TCPN4LS&api_secret=test-secret',
                        {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                client_id: 'anonymous',
                                events: [{
                                    name: 'lead_form_submission',
                                    params: {
                                        event_label: 'lead_form_submission',
                                        listing_id: 'test-listing-id',
                                        listing_name: 'Test Provider',
                                        category: 'rv-repair',
                                        user_id: 'test-user-id',
                                        is_anonymous: false,
                                        value: 1,
                                        event_category: 'conversion'
                                    }
                                }]
                            })
                        }
                    );
                });
            });

            describe('trackServerProviderContact', () => {
                it('should track server provider contact', async () => {
                    mockFetch.mockResolvedValueOnce({
                        ok: true,
                        status: 200
                    } as Response);

                    const data = {
                        listingId: 'test-listing-id',
                        listingName: 'Test Provider',
                        contactType: 'phone' as const,
                        contactValue: '************',
                        userId: 'test-user-id',
                        isAnonymous: false
                    };

                    await GoogleAnalyticsService.trackServerProviderContact(data);

                    expect(mockFetch).toHaveBeenCalledWith(
                        'https://www.google-analytics.com/mp/collect?measurement_id=G-RJ4TCPN4LS&api_secret=test-secret',
                        {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                client_id: 'anonymous',
                                events: [{
                                    name: 'provider_contact_click',
                                    params: {
                                        event_label: 'provider_contact_phone',
                                        listing_id: 'test-listing-id',
                                        listing_name: 'Test Provider',
                                        contact_type: 'phone',
                                        contact_value: '************',
                                        user_id: 'test-user-id',
                                        is_anonymous: false,
                                        value: 1,
                                        event_category: 'conversion'
                                    }
                                }]
                            })
                        }
                    );
                });
            });

            describe('trackServerAccountCreation', () => {
                it('should track server account creation', async () => {
                    mockFetch.mockResolvedValueOnce({
                        ok: true,
                        status: 200
                    } as Response);

                    const data = {
                        userId: 'test-user-id',
                        email: '<EMAIL>',
                        source: 'website',
                        referrer: 'https://google.com'
                    };

                    await GoogleAnalyticsService.trackServerAccountCreation(data);

                    expect(mockFetch).toHaveBeenCalledWith(
                        'https://www.google-analytics.com/mp/collect?measurement_id=G-RJ4TCPN4LS&api_secret=test-secret',
                        {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                client_id: 'anonymous',
                                events: [{
                                    name: 'account_creation',
                                    params: {
                                        event_label: 'account_creation',
                                        user_id: 'test-user-id',
                                        email: '<EMAIL>',
                                        source: 'website',
                                        referrer: 'https://google.com',
                                        value: 1,
                                        event_category: 'conversion'
                                    }
                                }]
                            })
                        }
                    );
                });
            });
        });
    });

    // Note: Development mode testing is handled in a separate test file
    // to avoid module loading conflicts with the config mock
});