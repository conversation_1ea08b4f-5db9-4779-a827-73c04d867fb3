// Mock axios for HTTP requests
jest.mock("axios");
const mockedAxios = require("axios") as jest.Mocked<typeof axios>;

// Mock the config module
jest.mock("@/config", () => ({
    __esModule: true,
    default: {
        isDevelopment: true, // Set to true for development tests
        appUrl: "http://localhost:3000",
        email: {
            from: "<EMAIL>",
            allowedDomains: ["rvhelp.com"],
            allowedEmails: ["<EMAIL>"]
        }
    }
}));

// Mock nodemailer
jest.mock("nodemailer", () => ({
    createTransport: jest.fn().mockReturnValue({
        sendMail: jest.fn().mockResolvedValue({ messageId: "test-message-id" })
    })
}));

// Mock Resend
jest.mock("resend", () => ({
    Resend: jest.fn().mockImplementation(() => ({
        emails: {
            send: jest.fn().mockResolvedValue({ id: "test-email-id" })
        },
        batch: {
            send: jest.fn().mockResolvedValue({ data: [{ id: "test-batch-id" }] })
        }
    }))
}));

// Mock @react-email/render
jest.mock("@react-email/render", () => ({
    renderAsync: jest.fn().mockResolvedValue("<html>test email</html>")
}));

// Mock prisma
jest.mock("@/lib/prisma", () => ({
    user: {
        findFirst: jest.fn(),
        update: jest.fn(),
        create: jest.fn()
    },
    emailOutbox: {
        create: jest.fn()
    },
    $transaction: jest.fn()
}));

// Mock the shared services
jest.mock("@rvhelp/services", () => ({
    ...jest.requireActual("@rvhelp/services"),
    SafelistService: jest.fn().mockImplementation(() => ({
        isAllowed: jest.fn().mockResolvedValue(true)
    })),
    DisposableEmailService: {
        validateEmail: jest.fn().mockReturnValue({ isValid: true })
    }
}));



// Mock the admin log service
jest.mock("@/lib/services/admin-log.service", () => ({
    adminLogger: {
        log: jest.fn()
    }
}));

// Import after mocks
import { emailService } from "@/lib/services";
import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";
import axios from "axios";
import React from "react";

describe("Email Development Safety Tests", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Reset environment variables
        Object.defineProperty(process.env, 'NODE_ENV', {
            value: 'development',
            writable: true
        });
        Object.defineProperty(process.env, 'NEXT_PUBLIC_VERCEL_ENV', {
            value: 'development',
            writable: true
        });
    });

    describe("EmailNewsletterService", () => {
        describe("syncNewsletterSubscriber", () => {
            it("should skip newsletter sync in development mode", async () => {
                const consoleSpy = jest.spyOn(console, "log").mockImplementation();

                const result = await EmailNewsletterService.syncNewsletterSubscriber({
                    email: "<EMAIL>",
                    first_name: "Test",
                    last_name: "User"
                });

                expect(result).toBe(true);
                expect(consoleSpy).toHaveBeenCalledWith("Skipping sync in development");
                expect(mockedAxios.get).not.toHaveBeenCalled();
                expect(mockedAxios.post).not.toHaveBeenCalled();

                consoleSpy.mockRestore();
            });

            it("should not make any HTTP requests to Mailcoach API in development", async () => {
                await EmailNewsletterService.syncNewsletterSubscriber({
                    email: "<EMAIL>",
                    first_name: "Test",
                    last_name: "User",
                    tags: ["test-tag"]
                });

                // Verify no HTTP requests were made
                expect(mockedAxios.get).not.toHaveBeenCalled();
                expect(mockedAxios.post).not.toHaveBeenCalled();
            });

            it("should not update user newsletter subscription in database in development", async () => {
                const mockUser = {
                    id: "user123",
                    email: "<EMAIL>"
                };

                await EmailNewsletterService.syncNewsletterSubscriber({
                    email: "<EMAIL>",
                    first_name: "Test",
                    last_name: "User",
                    user: mockUser as any
                });

                // Verify no database updates were made
                const prisma = require("@/lib/prisma");
                expect(prisma.user.update).not.toHaveBeenCalled();
            });
        });

        describe("sendProviderWelcomeEmail", () => {
            it("should skip Mailcoach subscription in development mode", async () => {
                const mockListing = {
                    email: "<EMAIL>",
                    first_name: "Provider",
                    last_name: "User"
                };

                const mockUser = {
                    id: "user123",
                    email: "<EMAIL>"
                };

                const prisma = require("@/lib/prisma");
                prisma.user.findFirst.mockResolvedValue(mockUser as any);

                await EmailNewsletterService.sendProviderWelcomeEmail(mockListing as any);

                // Verify no Mailcoach API calls were made
                expect(mockedAxios.post).not.toHaveBeenCalledWith(
                    expect.stringContaining("/email-lists/"),
                    expect.any(Object),
                    expect.any(Object)
                );
            });

            it("should still send welcome email via email service in development", async () => {
                const mockListing = {
                    email: "<EMAIL>",
                    first_name: "Provider",
                    last_name: "User"
                };

                const mockUser = {
                    id: "user123",
                    email: "<EMAIL>"
                };

                const prisma = require("@/lib/prisma");
                prisma.user.findFirst.mockResolvedValue(mockUser as any);

                // Mock the email service send method
                const emailServiceSpy = jest.spyOn(emailService, "send").mockResolvedValue({ success: true });

                await EmailNewsletterService.sendProviderWelcomeEmail(mockListing as any);

                // Verify email service was called
                expect(emailServiceSpy).toHaveBeenCalledWith({
                    to: "<EMAIL>",
                    subject: "Welcome to RV Help - Your Provider Account",
                    react: expect.any(Object),
                    replyTo: "<EMAIL>",
                    emailType: "provider_welcome"
                });

                emailServiceSpy.mockRestore();
            });
        });

        describe("sendCampaign", () => {
            it("should skip campaign sending in development mode", async () => {
                const consoleSpy = jest.spyOn(console, "log").mockImplementation();

                const result = await EmailNewsletterService.sendCampaign(
                    "Test Campaign",
                    "Test Subject",
                    "Test Body"
                );

                expect(result.success).toBe(true);
                expect(result.campaignUuid).toBe("dev-mode-skipped");
                expect(consoleSpy).toHaveBeenCalledWith("Skipping campaign send in development mode");
                expect(mockedAxios.post).not.toHaveBeenCalled();

                consoleSpy.mockRestore();
            });
        });

        describe("sendTestCampaign", () => {
            it("should skip test campaign sending in development mode", async () => {
                const consoleSpy = jest.spyOn(console, "log").mockImplementation();

                const result = await EmailNewsletterService.sendTestCampaign(
                    "Test Campaign",
                    "Test Subject",
                    "Test Body"
                );

                expect(result.success).toBe(true);
                expect(result.campaignUuid).toBe("dev-mode-skipped");
                expect(consoleSpy).toHaveBeenCalledWith("Skipping test campaign send in development mode");
                expect(mockedAxios.post).not.toHaveBeenCalled();

                consoleSpy.mockRestore();
            });
        });
    });

    describe("Email Service Development Mode", () => {
        describe("emailService instance", () => {
            it("should use mock email service in test environment", () => {
                // In test environment, the emailService should be the mock service
                expect(emailService).toBeDefined();
                expect(typeof emailService.send).toBe('function');
            });

            it("should not make real HTTP requests when sending emails", async () => {
                const emailOptions = {
                    to: "<EMAIL>",
                    subject: "Test Email",
                    react: React.createElement("div", null, "Test content")
                };

                // Mock the email service send method to return a proper response
                const emailServiceSpy = jest.spyOn(emailService, "send").mockResolvedValue({ success: true });

                const result = await emailService.send(emailOptions);

                // Should use mock service and not make real requests
                expect(result.success).toBe(true);
                expect(mockedAxios.post).not.toHaveBeenCalled();

                emailServiceSpy.mockRestore();
            });
        });
    });

    describe("Production Mode Tests", () => {
        it("should document that development protection is working", () => {
            // This test documents that our development protection is working correctly
            // The main tests above verify that:
            // 1. EmailNewsletterService.syncNewsletterSubscriber skips operations in development
            // 2. EmailNewsletterService.sendProviderWelcomeEmail skips Mailcoach in development
            // 3. EmailNewsletterService.sendCampaign skips campaign sending in development
            // 4. EmailNewsletterService.sendTestCampaign skips test campaign sending in development
            // 5. Email service uses mock service in test environment

            expect(true).toBe(true); // Placeholder test to document the behavior
        });
    });
}); 