import { FirstPromoterService } from "@/lib/services/first-promoter.service";
import { NextRequest } from "next/server";

// Mock the configuration
jest.mock("@/config", () => ({
	firstPromoter: {
		companyId: "test-company-id",
		apiKey: "test-api-key"
	}
}));

// Mock the admin logger
jest.mock("@/lib/services/admin-log.service", () => ({
	adminLogger: {
		log: jest.fn()
	}
}));

// Mock fetch globally
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe("FirstPromoterService", () => {
	beforeEach(() => {
		jest.clearAllMocks();
		mockFetch.mockResolvedValue({
			ok: true,
			json: async () => ({ success: true })
		});

		// Mock environment to not be development
		process.env.NODE_ENV = "production";
	});

	describe("trackSignup", () => {
		const signupData = {
			email: "<EMAIL>",
			userId: "user123",
			firstName: "John",
			lastName: "Doe"
		};

		it("should skip tracking when no referral data is available", async () => {
			const result = await FirstPromoterService.trackSignup(signupData);

			expect(result.success).toBe(true);
			expect(result.message).toBe("No referral data to track");
			expect(fetch).not.toHaveBeenCalled();
		});

		it("should track signup successfully when tracking ID is available", async () => {
			// Mock request with cookies
			const mockRequest = {
				headers: {
					get: jest
						.fn()
						.mockReturnValue("_fprom_tid=test-tracking-id; other_cookie=value")
				}
			} as unknown as NextRequest;

			const result = await FirstPromoterService.trackSignup({
				...signupData,
				req: mockRequest
			});

			expect(result.success).toBe(true);
			expect(result.message).toBe("Event tracked successfully");
			expect(fetch).toHaveBeenCalledWith(
				"https://v2.firstpromoter.com/api/v2/track/signup",
				expect.objectContaining({
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						Authorization: "Bearer test-api-key",
						"Account-ID": "test-company-id"
					},
					body: JSON.stringify({
						email: signupData.email,
						uid: signupData.userId,
						firstName: signupData.firstName,
						lastName: signupData.lastName,
						tid: "test-tracking-id"
					})
				})
			);
		});

		it("should handle missing firstName and lastName when tracking ID is available", async () => {
			const minimalData = {
				email: "<EMAIL>",
				userId: "user123",
				req: {
					headers: {
						get: jest.fn().mockReturnValue("_fprom_tid=test-tracking-id")
					}
				} as unknown as NextRequest
			};

			const result = await FirstPromoterService.trackSignup(minimalData);

			expect(result.success).toBe(true);
			expect(fetch).toHaveBeenCalledWith(
				"https://v2.firstpromoter.com/api/v2/track/signup",
				expect.objectContaining({
					body: JSON.stringify({
						email: minimalData.email,
						uid: minimalData.userId,
						firstName: undefined,
						lastName: undefined,
						tid: "test-tracking-id"
					})
				})
			);
		});
	});

	describe("trackNewsletterSignup", () => {
		const newsletterData = {
			email: "<EMAIL>",
			userId: "user456"
		};

		it("should skip tracking when no referral data is available", async () => {
			const result =
				await FirstPromoterService.trackNewsletterSignup(newsletterData);

			expect(result.success).toBe(true);
			expect(result.message).toBe("No referral data to track");
			expect(fetch).not.toHaveBeenCalled();
		});

		it("should skip tracking without userId when no referral data", async () => {
			const result = await FirstPromoterService.trackNewsletterSignup({
				email: newsletterData.email
			});

			expect(result.success).toBe(true);
			expect(result.message).toBe("No referral data to track");
			expect(fetch).not.toHaveBeenCalled();
		});
	});

	describe("trackLeadMagnet", () => {
		const leadMagnetData = {
			email: "<EMAIL>",
			userId: "user789",
			leadMagnetTitle: "RV Maintenance Guide"
		};

		it("should skip tracking when no referral data is available", async () => {
			const result = await FirstPromoterService.trackLeadMagnet(leadMagnetData);

			expect(result.success).toBe(true);
			expect(result.message).toBe("No referral data to track");
			expect(fetch).not.toHaveBeenCalled();
		});
	});

	describe("trackLeadForm", () => {
		const leadFormData = {
			email: "<EMAIL>",
			userId: "user321",
			formType: "contact-form"
		};

		it("should skip tracking when no referral data is available", async () => {
			const result = await FirstPromoterService.trackLeadForm(leadFormData);

			expect(result.success).toBe(true);
			expect(result.message).toBe("No referral data to track");
			expect(fetch).not.toHaveBeenCalled();
		});
	});

	describe("trackProMembership", () => {
		const membershipData = {
			email: "<EMAIL>",
			userId: "user654",
			membershipLevel: "STANDARD",
			amount: 4900,
			currency: "usd"
		};

		it("should skip tracking when no referral data is available", async () => {
			const result =
				await FirstPromoterService.trackProMembership(membershipData);

			expect(result.success).toBe(true);
			expect(result.message).toBe("No referral data to track");
			expect(fetch).not.toHaveBeenCalled();
		});
	});

	describe("error handling", () => {
		it("should handle API errors gracefully", async () => {
			mockFetch.mockResolvedValueOnce({
				ok: false,
				status: 400,
				text: async () => "Bad Request"
			});

			const result = await FirstPromoterService.trackSignup({
				email: "<EMAIL>",
				userId: "user123",
				req: {
					headers: {
						get: jest.fn().mockReturnValue("_fprom_tid=test-tracking-id")
					}
				} as unknown as NextRequest
			});

			expect(result.success).toBe(false);
			expect(result.message).toBe("API error: 400");
		});

		it("should handle network errors", async () => {
			mockFetch.mockRejectedValueOnce(new Error("Network error"));

			const result = await FirstPromoterService.trackSignup({
				email: "<EMAIL>",
				userId: "user123",
				req: {
					headers: {
						get: jest.fn().mockReturnValue("_fprom_tid=test-tracking-id")
					}
				} as unknown as NextRequest
			});

			expect(result.success).toBe(false);
			expect(result.message).toBe("Network error");
		});
	});

	describe("configuration", () => {
		it("should skip tracking when no referral data is available", async () => {
			// Without tracking data, service should skip tracking regardless of config
			const result = await FirstPromoterService.trackSignup({
				email: "<EMAIL>",
				userId: "user123"
			});

			// Without tracking data, service skips tracking
			expect(result.success).toBe(true);
			expect(result.message).toBe("No referral data to track");
			expect(fetch).not.toHaveBeenCalled();
		});

		it("should skip tracking in development mode when no referral data", async () => {
			const originalEnv = process.env.NODE_ENV;
			Object.defineProperty(process.env, "NODE_ENV", {
				value: "development",
				writable: true
			});

			const result = await FirstPromoterService.trackSignup({
				email: "<EMAIL>",
				userId: "user123"
			});

			// No tracking data available, so should skip
			expect(result.success).toBe(true);
			expect(result.message).toBe("No referral data to track");
			expect(fetch).not.toHaveBeenCalled();

			// Restore original environment
			Object.defineProperty(process.env, "NODE_ENV", {
				value: originalEnv,
				writable: true
			});
		});
	});
});
