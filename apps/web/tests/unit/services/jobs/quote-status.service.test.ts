import { emailService } from "@/lib/services";
import { ProviderStatsService } from "@/lib/services/provider-stats.service";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { timelineService } from "@/lib/services/timeline.service";
import { QuoteStatus } from "@rvhelp/database";
import { mockPrisma } from "../../../mocks/prisma-mock";

jest.mock("@/lib/services/provider-stats.service", () => ({
	ProviderStatsService: {
		onProviderResponse: jest.fn(),
		onJobAccepted: jest.fn(),
		onJobCompleted: jest.fn(),
		updateProviderStats: jest.fn(),
		onReviewSubmitted: jest.fn()
	}
}));

jest.mock("@/lib/services", () => ({
	emailService: {
		send: jest.fn()
	}
}));

jest.mock("@/lib/services/sms.service", () => ({
	smsService: {
		send: jest.fn()
	}
}));

jest.mock("@/lib/services/timeline.service", () => ({
	timelineService: {
		createTimelineUpdate: jest.fn()
	}
}));

jest.mock("@/lib/services/emailNewsletter.service", () => ({
	EmailNewsletterService: {
		syncNewsletterSubscriber: jest.fn()
	}
}));

const { EmailNewsletterService } = require("@/lib/services/emailNewsletter.service");

describe("QuoteStatusService", () => {
	const mockQuoteId = "quote123";
	const mockListingId = "listing123";
	const mockJobId = "job123";
	const mockUserId = "user123";

	const mockQuote = {
		id: mockQuoteId,
		listing_id: mockListingId,
		job_id: mockJobId,
		status: QuoteStatus.PENDING,
		message: "Test message",
		created_at: new Date(),
		job: {
			id: mockJobId,
			user_id: mockUserId,
			user: {
				id: mockUserId,
				first_name: "John",
				last_name: "Doe",
				email: "<EMAIL>"
			},
			warranty_request_id: null
		},
		listing: {
			id: mockListingId,
			business_name: "Test Business",
			first_name: "Jane",
			last_name: "Smith",
			email: "<EMAIL>",
			phone: "+1234567890",
			locations: [
				{
					id: "location123",
					city: "Test City",
					state: "TX",
					is_active: true
				}
			]
		},
		messages: []
	};

	const mockListing = {
		id: mockListingId,
		business_name: "Test Business",
		first_name: "Jane",
		last_name: "Smith",
		email: "<EMAIL>",
		locations: [
			{
				id: "location123",
				city: "Test City",
				state: "TX",
				is_active: true
			}
		]
	};

	beforeEach(() => {
		jest.clearAllMocks();

		// Mock the getQuoteWithDetails method to return the mock quote
		jest.spyOn(QuoteStatusService, 'getQuoteWithDetails').mockResolvedValue(mockQuote);
	});

	describe("changeQuoteStatus", () => {
		it("should update quote status with timestamps", async () => {
			const result = await QuoteStatusService.changeQuoteStatus({
				quoteId: mockQuoteId,
				listingId: mockListingId,
				newStatus: QuoteStatus.ACCEPTED,
				message: "I can help with this"
			});

			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: {
					status: QuoteStatus.ACCEPTED,
					provider_notes: "I can help with this",
					responded_at: expect.any(Date)
				},
				include: {
					job: {
						include: {
							user: true
						}
					},
					listing: {
						include: {
							locations: true
						}
					},
					messages: {
						orderBy: {
							created_at: "desc"
						}
					}
				}
			});
		});

		it("should set accepted_at timestamp for customer acceptance", async () => {
			const result = await QuoteStatusService.changeQuoteStatus({
				quoteId: mockQuoteId,
				listingId: mockListingId,
				newStatus: QuoteStatus.ACCEPTED
			});

			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: {
					status: QuoteStatus.ACCEPTED,
					responded_at: expect.any(Date)
				},
				include: {
					job: {
						include: {
							user: true
						}
					},
					listing: {
						include: {
							locations: true
						}
					},
					messages: {
						orderBy: {
							created_at: "desc"
						}
					}
				}
			});
		});
	});

	describe("canProviderPerformAction", () => {
		it("should allow responding to invited quotes", async () => {
			(mockPrisma.quote.findUnique as jest.Mock).mockResolvedValue({
				status: QuoteStatus.PENDING,
				listing_id: mockListingId
			});

			const result = await QuoteStatusService.canProviderPerformAction(
				mockQuoteId,
				mockListingId,
				"respond"
			);

			expect(result).toEqual({ allowed: true });
		});

		it("should prevent responding to non-pending quotes", async () => {
			(mockPrisma.quote.findUnique as jest.Mock).mockResolvedValue({
				status: QuoteStatus.ACCEPTED,
				listing_id: mockListingId
			});

			const result = await QuoteStatusService.canProviderPerformAction(
				mockQuoteId,
				mockListingId,
				"respond"
			);

			expect(result).toEqual({
				allowed: false,
				reason: "Can only respond to pending quotes"
			});
		});

		it("should prevent unauthorized access to quotes", async () => {
			(mockPrisma.quote.findUnique as jest.Mock).mockResolvedValue({
				status: QuoteStatus.PENDING,
				listing_id: "other-listing"
			});

			const result = await QuoteStatusService.canProviderPerformAction(
				mockQuoteId,
				mockListingId,
				"respond"
			);

			expect(result).toEqual({
				allowed: false,
				reason: "Unauthorized"
			});
		});

		it("should allow completing accepted jobs", async () => {
			(mockPrisma.quote.findUnique as jest.Mock).mockResolvedValue({
				status: QuoteStatus.ACCEPTED,
				listing_id: mockListingId
			});

			const result = await QuoteStatusService.canProviderPerformAction(
				mockQuoteId,
				mockListingId,
				"complete"
			);

			expect(result).toEqual({ allowed: true });
		});

		it("should prevent completing non-accepted jobs", async () => {
			(mockPrisma.quote.findUnique as jest.Mock).mockResolvedValue({
				status: QuoteStatus.PENDING,
				listing_id: mockListingId
			});

			const result = await QuoteStatusService.canProviderPerformAction(
				mockQuoteId,
				mockListingId,
				"complete"
			);

			expect(result).toEqual({
				allowed: false,
				reason: "Can only complete accepted jobs"
			});
		});


	});

	describe("providerAcceptJob", () => {
		beforeEach(() => {
			(mockPrisma.quote.findUnique as jest.Mock)
				.mockResolvedValueOnce({
					// canProviderPerformAction call
					status: QuoteStatus.PENDING,
					listing_id: mockListingId
				})
				.mockResolvedValueOnce(mockQuote); // getQuoteWithDetails call

			(mockPrisma.listing.findUnique as jest.Mock).mockResolvedValue(
				mockListing
			);
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue(mockQuote);
		});

		it("should accept a job with proper workflow", async () => {
			const params = {
				quote: mockQuote,
				userId: mockUserId,
				message: "I can help with this",
				schedulingTimeframe: "Next week"
			};

			const result = await QuoteStatusService.providerAcceptJob(params);

			// Verify quote was updated
			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: {
					status: QuoteStatus.ACCEPTED,
					provider_notes: "I can help with this",
					responded_at: expect.any(Date)
				},
				include: {
					job: {
						include: {
							user: true
						}
					},
					listing: {
						include: {
							locations: true
						}
					},
					messages: {
						orderBy: {
							created_at: "desc"
						}
					}
				}
			});

			expect(result).toBeDefined();
		});

		it("should throw error if provider cannot respond", async () => {
			// Spy on the canProviderPerformAction method to force it to return false
			const canPerformActionSpy = jest
				.spyOn(QuoteStatusService, "canProviderPerformAction")
				.mockResolvedValue({
					allowed: false,
					reason: "Can only respond to invited quotes"
				});

			try {
				const params = {
					quote: mockQuote,
					userId: mockUserId,
					message: "Test message"
				};

				await expect(
					QuoteStatusService.providerAcceptJob(params)
				).rejects.toThrow("Can only respond to invited quotes");

				// Verify the permission check was called
				expect(canPerformActionSpy).toHaveBeenCalledWith(
					mockQuoteId,
					mockListingId,
					"respond"
				);
			} finally {
				// Always restore the spy
				canPerformActionSpy.mockRestore();
			}
		});
	});

	describe("providerCompleteJob", () => {
		beforeEach(() => {
			// Reset specific mocks without clearing the main database mocks
			(timelineService.createTimelineUpdate as jest.Mock)
				.mockReset()
				.mockResolvedValue(undefined);
			(emailService.send as jest.Mock).mockReset().mockResolvedValue(undefined);
			(ProviderStatsService.onJobCompleted as jest.Mock)
				.mockReset()
				.mockResolvedValue(undefined);

			// Reset only the implementation of the prisma mocks, not the mock itself
			(mockPrisma.quote.findUnique as jest.Mock).mockReset();
			(mockPrisma.quote.update as jest.Mock).mockReset();
		});

		it("should complete a job with full workflow", async () => {
			// Mock the canProviderPerformAction check
			(mockPrisma.quote.findUnique as jest.Mock)
				.mockResolvedValueOnce({
					status: QuoteStatus.ACCEPTED,
					listing_id: mockListingId
				})
				.mockResolvedValueOnce({
					...mockQuote,
					status: QuoteStatus.ACCEPTED,
					job_id: mockJobId
				});

			// Mock the quote update
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue({
				...mockQuote,
				status: QuoteStatus.COMPLETED,
				job_id: mockJobId
			});

			// Mock the job update
			(mockPrisma.job.update as jest.Mock).mockResolvedValue({
				...mockQuote.job,
				id: mockJobId
			});

			const params = {
				quoteId: mockQuoteId,
				listingId: mockListingId,
				userId: mockUserId,
				resolutionStatus: "completed" as any,
				resolutionNotes: "Job completed successfully",
				requestReview: true,
				reviewDelayHours: 24
			};

			const result = await QuoteStatusService.providerCompleteJob(params);

			// Verify quote was updated with completion data
			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: {
					status: QuoteStatus.COMPLETED,
					completed_at: expect.any(Date),
					resolution_notes: "Job completed successfully",
					resolution_status: "completed",
					review_delay_hours: 24,
					review_requested: true,
					review_requested_at: expect.any(Date)
				},
				include: {
					job: {
						include: {
							user: true,
							warranty_request: true
						}
					},
					listing: {
						include: {
							owner: true
						}
					}
				}
			});

			expect(result).toBeDefined();
		});

		it("should throw error if job cannot be completed", async () => {
			// Mock canProviderPerformAction to return not-allowed
			(mockPrisma.quote.findUnique as jest.Mock).mockResolvedValue({
				status: QuoteStatus.PENDING,
				listing_id: mockListingId
			});

			const params = {
				quoteId: mockQuoteId,
				listingId: mockListingId,
				userId: mockUserId
			};

			await expect(
				QuoteStatusService.providerCompleteJob(params)
			).rejects.toThrow("Can only complete accepted jobs");
		});

		it("should send job completion email to provider, not customer", async () => {
			// Mock the canProviderPerformAction check
			(mockPrisma.quote.findUnique as jest.Mock)
				.mockResolvedValueOnce({
					status: QuoteStatus.ACCEPTED,
					listing_id: mockListingId
				})
				.mockResolvedValueOnce({
					...mockQuote,
					status: QuoteStatus.ACCEPTED,
					job_id: mockJobId
				});

			// Mock the quote update with full details including listing email
			const mockUpdatedQuote = {
				...mockQuote,
				status: QuoteStatus.COMPLETED,
				job_id: mockJobId,
				job: {
					...mockQuote.job,
					user: {
						...mockQuote.job.user,
						email: "<EMAIL>"
					}
				},
				listing: {
					...mockQuote.listing,
					email: "<EMAIL>"
				}
			};

			(mockPrisma.quote.update as jest.Mock).mockResolvedValue(mockUpdatedQuote);

			// Mock the job update
			(mockPrisma.job.update as jest.Mock).mockResolvedValue({
				...mockQuote.job,
				id: mockJobId
			});

			const params = {
				quoteId: mockQuoteId,
				listingId: mockListingId,
				userId: mockUserId,
				resolutionStatus: "completed" as any,
				resolutionNotes: "Job completed successfully"
			};

			await QuoteStatusService.providerCompleteJob(params);

			// Verify email was sent to provider, not customer
			expect(emailService.send).toHaveBeenCalledWith({
				to: "<EMAIL>",
				subject: expect.stringContaining("Service Complete"),
				react: expect.any(Object)
			});

			// Verify email was NOT sent to customer
			expect(emailService.send).not.toHaveBeenCalledWith({
				to: "<EMAIL>",
				subject: expect.stringContaining("Service Complete"),
				react: expect.any(Object)
			});
		});

		it("should add newsletter tag to customer when job is completed", async () => {
			// Mock the canProviderPerformAction check
			(mockPrisma.quote.findUnique as jest.Mock)
				.mockResolvedValueOnce({
					status: QuoteStatus.ACCEPTED,
					listing_id: mockListingId
				})
				.mockResolvedValueOnce({
					...mockQuote,
					status: QuoteStatus.ACCEPTED,
					job_id: mockJobId
				});

			// Mock the quote update with customer details
			const mockUpdatedQuote = {
				...mockQuote,
				status: QuoteStatus.COMPLETED,
				job_id: mockJobId,
				job: {
					...mockQuote.job,
					user: {
						id: mockUserId,
						first_name: "John",
						last_name: "Doe",
						email: "<EMAIL>"
					}
				},
				listing: {
					...mockQuote.listing,
					email: "<EMAIL>"
				}
			};

			(mockPrisma.quote.update as jest.Mock).mockResolvedValue(mockUpdatedQuote);

			// Mock the job update
			(mockPrisma.job.update as jest.Mock).mockResolvedValue({
				...mockQuote.job,
				id: mockJobId
			});

			// Mock the EmailNewsletterService
			(EmailNewsletterService.syncNewsletterSubscriber as jest.Mock).mockResolvedValue(true);

			const params = {
				quoteId: mockQuoteId,
				listingId: mockListingId,
				userId: mockUserId,
				resolutionStatus: "completed" as any,
				resolutionNotes: "Job completed successfully"
			};

			await QuoteStatusService.providerCompleteJob(params);

			// Verify newsletter tag was added to customer
			expect(EmailNewsletterService.syncNewsletterSubscriber).toHaveBeenCalledWith({
				email: "<EMAIL>",
				first_name: "John",
				last_name: "Doe",
				user: {
					id: mockUserId,
					first_name: "John",
					last_name: "Doe",
					email: "<EMAIL>"
				},
				tags: ["job complete"]
			});
		});

		it("should not fail job completion if newsletter tagging fails", async () => {
			// Mock the canProviderPerformAction check
			(mockPrisma.quote.findUnique as jest.Mock)
				.mockResolvedValueOnce({
					status: QuoteStatus.ACCEPTED,
					listing_id: mockListingId
				})
				.mockResolvedValueOnce({
					...mockQuote,
					status: QuoteStatus.ACCEPTED,
					job_id: mockJobId
				});

			// Mock the quote update
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue({
				...mockQuote,
				status: QuoteStatus.COMPLETED,
				job_id: mockJobId
			});

			// Mock the job update
			(mockPrisma.job.update as jest.Mock).mockResolvedValue({
				...mockQuote.job,
				id: mockJobId
			});

			// Mock the EmailNewsletterService to fail
			(EmailNewsletterService.syncNewsletterSubscriber as jest.Mock).mockRejectedValue(
				new Error("Newsletter service error")
			);

			const params = {
				quoteId: mockQuoteId,
				listingId: mockListingId,
				userId: mockUserId,
				resolutionStatus: "completed" as any,
				resolutionNotes: "Job completed successfully"
			};

			// Should not throw error
			const result = await QuoteStatusService.providerCompleteJob(params);

			// Verify the job completion still succeeded
			expect(result).toBeDefined();
			expect(mockPrisma.quote.update).toHaveBeenCalled();
			expect(mockPrisma.job.update).toHaveBeenCalled();
		});
	});

	describe("customerAcceptQuote", () => {
		const mockJob = {
			id: mockJobId,
			user_id: mockUserId,
			user: mockQuote.job.user,
			quotes: [
				{ id: mockQuoteId, status: QuoteStatus.ACCEPTED },
				{ id: "quote456", status: QuoteStatus.ACCEPTED }
			],
			warranty_request_id: null
		};

		const mockQuoteWithQuotedStatus = {
			...mockQuote,
			status: QuoteStatus.ACCEPTED,
			job_id: mockJobId // Ensure job_id matches
		};

		beforeEach(() => {
			jest.clearAllMocks();

			// Reset other mocks that might be needed
			(timelineService.createTimelineUpdate as jest.Mock).mockResolvedValue(
				undefined
			);
			(emailService.send as jest.Mock).mockResolvedValue(undefined);

			(mockPrisma.job.findFirst as jest.Mock).mockResolvedValue(mockJob);
			(mockPrisma.quote.findUnique as jest.Mock).mockResolvedValue(
				mockQuoteWithQuotedStatus
			);
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue(
				mockQuoteWithQuotedStatus
			);
			(mockPrisma.job.update as jest.Mock).mockResolvedValue(mockJob);
		});

		it("should accept quote and reject others", async () => {
			// Reset the global mock for this test specifically
			jest.spyOn(QuoteStatusService, 'getQuoteWithDetails').mockResolvedValue(mockQuoteWithQuotedStatus);

			const result = await QuoteStatusService.customerAcceptQuote({
				quote: mockQuote,
				userId: mockUserId
			});

			// Verify quote was accepted (service sets to IN_PROGRESS)
			expect(mockPrisma.quote.update).toHaveBeenCalledWith(
				expect.objectContaining({
					where: { id: mockQuoteId },
					data: expect.objectContaining({
						status: QuoteStatus.IN_PROGRESS
					})
				})
			);

			// Verify other quotes were rejected
			expect(mockPrisma.quote.update).toHaveBeenCalledWith(
				expect.objectContaining({
					where: { id: "quote456" },
					data: expect.objectContaining({
						status: QuoteStatus.EXPIRED
					})
				})
			);

			expect(result).toBeDefined();
		});

		it("should throw error if quote already accepted", async () => {
			const jobWithAcceptedQuote = {
				...mockJob,
				accepted_quote_id: mockQuoteId,
				quotes: [
					{ id: mockQuoteId, status: QuoteStatus.ACCEPTED },
					{ id: "quote456", status: QuoteStatus.ACCEPTED }
				]
			};

			(mockPrisma.job.findFirst as jest.Mock).mockResolvedValue(
				jobWithAcceptedQuote
			);

			const params = {
				quote: mockQuote,
				userId: mockUserId
			};

			await expect(
				QuoteStatusService.customerAcceptQuote(params)
			).rejects.toThrow("Another quote has already been accepted for this job");
		});

		it("should throw error if quote is not in quotable state", async () => {
			const quoteNotQuoted = {
				...mockQuote,
				status: QuoteStatus.WITHDRAWN
			};

			// Reset the getQuoteWithDetails mock for this test
			jest.spyOn(QuoteStatusService, 'getQuoteWithDetails').mockResolvedValue(quoteNotQuoted);

			const params = {
				quote: quoteNotQuoted,
				userId: mockUserId
			};

			await expect(
				QuoteStatusService.customerAcceptQuote(params)
			).rejects.toThrow("Quote cannot be accepted in its current state");
		});
	});

	describe("customerRejectQuote", () => {
		const mockJob = {
			id: mockJobId,
			user_id: mockUserId,
			user: mockQuote.job.user,
			quotes: [{ id: mockQuoteId, status: QuoteStatus.ACCEPTED }],
			warranty_request_id: null
		};

		beforeEach(() => {
			jest.clearAllMocks();

			// Reset other mocks that might be needed
			(timelineService.createTimelineUpdate as jest.Mock).mockResolvedValue(
				undefined
			);

			(mockPrisma.job.findFirst as jest.Mock).mockResolvedValue(mockJob);
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue(mockQuote);
		});

		it("should reject a quote in ACCEPTED status", async () => {
			const quotedQuote = {
				...mockQuote,
				status: QuoteStatus.ACCEPTED,
				job_id: mockJobId
			};

			// Reset the getQuoteWithDetails mock for this test
			jest.spyOn(QuoteStatusService, 'getQuoteWithDetails').mockResolvedValue(quotedQuote);

			const params = {
				quote: quotedQuote,
				jobId: mockJobId,
				userId: mockUserId
			};

			const result = await QuoteStatusService.customerRejectQuote(params);

			// Verify quote was rejected
			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: {
					status: QuoteStatus.CUSTOMER_REJECTED
				},
				include: expect.any(Object)
			});

			// Verify timeline event was created
			expect(timelineService.createTimelineUpdate).toHaveBeenCalledWith({
				job_id: mockJobId,
				event_type: "CUSTOMER_REJECTED",
				updated_by_id: mockUserId,
				notes: expect.stringContaining("Proposal rejected by customer")
			});

			expect(result).toEqual(mockQuote);
		});

		it("should reject a quote in ACCEPTED status", async () => {
			const acceptedQuote = {
				...mockQuote,
				status: QuoteStatus.ACCEPTED,
				job_id: mockJobId
			};

			// Reset the getQuoteWithDetails mock for this test
			jest.spyOn(QuoteStatusService, 'getQuoteWithDetails').mockResolvedValue(acceptedQuote);

			const params = {
				quote: acceptedQuote,
				jobId: mockJobId,
				userId: mockUserId
			};

			const result = await QuoteStatusService.customerRejectQuote(params);

			// Verify quote was rejected
			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: {
					status: QuoteStatus.CUSTOMER_REJECTED
				},
				include: expect.any(Object)
			});

			// Verify timeline event was created
			expect(timelineService.createTimelineUpdate).toHaveBeenCalledWith({
				job_id: mockJobId,
				event_type: "CUSTOMER_REJECTED",
				updated_by_id: mockUserId,
				notes: expect.stringContaining("Proposal rejected by customer")
			});

			expect(result).toEqual(mockQuote);
		});

		it("should throw error if quote is not in rejectable state", async () => {
			const withdrawnQuote = {
				...mockQuote,
				status: QuoteStatus.WITHDRAWN,
				job_id: mockJobId
			};

			(mockPrisma.quote.findUnique as jest.Mock).mockResolvedValue(
				withdrawnQuote
			);

			const params = {
				quote: mockQuote,
				jobId: mockJobId,
				userId: mockUserId
			};

			await expect(
				QuoteStatusService.customerRejectQuote(params)
			).rejects.toThrow("Quote cannot be rejected in its current state");
		});

		it("should throw error if job not found", async () => {
			(mockPrisma.job.findFirst as jest.Mock).mockResolvedValue(null);

			const params = {
				quote: mockQuote,
				jobId: mockJobId,
				userId: mockUserId
			};

			await expect(
				QuoteStatusService.customerRejectQuote(params)
			).rejects.toThrow("Job not found");
		});

		it("should throw error if quote does not belong to job", async () => {
			const quoteFromDifferentJob = {
				...mockQuote,
				status: QuoteStatus.ACCEPTED,
				job_id: "different-job-id" // Different job ID
			};

			// Reset the getQuoteWithDetails mock for this test
			jest.spyOn(QuoteStatusService, 'getQuoteWithDetails').mockResolvedValue(quoteFromDifferentJob);

			const params = {
				quote: quoteFromDifferentJob,
				jobId: mockJobId,
				userId: mockUserId
			};

			await expect(
				QuoteStatusService.customerRejectQuote(params)
			).rejects.toThrow("Quote does not belong to this job");
		});

		it("should send proposal rejection email to provider", async () => {
			const quotedQuote = {
				...mockQuote,
				status: QuoteStatus.ACCEPTED,
				job_id: mockJobId,
				listing: {
					...mockQuote.listing,
					email: "<EMAIL>"
				}
			};

			// Reset the getQuoteWithDetails mock for this test
			jest.spyOn(QuoteStatusService, 'getQuoteWithDetails').mockResolvedValue(quotedQuote);

			const params = {
				quote: quotedQuote,
				jobId: mockJobId,
				userId: mockUserId
			};

			await QuoteStatusService.customerRejectQuote(params);

			// Verify email was sent to provider
			expect(emailService.send).toHaveBeenCalledWith({
				to: "<EMAIL>",
				subject: "RV Help: Service Request Update",
				react: expect.any(Object)
			});

			// Verify the email template was called with correct parameters
			const emailCall = (emailService.send as jest.Mock).mock.calls[0][0];
			expect(emailCall.to).toBe("<EMAIL>");
			expect(emailCall.subject).toBe("RV Help: Service Request Update");
			expect(emailCall.react).toBeDefined();
		});
	});

	describe("markQuoteAsReviewed", () => {
		it("should mark quote as reviewed and update stats", async () => {
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue(mockQuote);

			await QuoteStatusService.markQuoteAsReviewed(mockQuoteId, mockListingId);

			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: {
					reviewed_at: expect.any(Date)
				}
			});

			expect(ProviderStatsService.onReviewSubmitted).toHaveBeenCalledWith(
				mockListingId
			);
		});
	});

	describe("providerRespond", () => {
		beforeEach(() => {
			jest.clearAllMocks();
		});

		it("should respond to quote without updating job status", async () => {
			// Mock the quote update
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue({
				...mockQuote,
				status: QuoteStatus.ACCEPTED,
				provider_notes: "I can help with this",
				responded_at: new Date()
			});

			const result = await QuoteStatusService.providerRespond({
				quoteId: mockQuoteId,
				listingId: mockListingId,
				responseType: "accepted",
				message: "I can help with this",
				schedulingTimeframe: "Tomorrow"
			});

			// Verify quote was updated
			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: {
					status: QuoteStatus.ACCEPTED,
					provider_notes: "I can help with this",
					responded_at: expect.any(Date)
				},
				include: {
					job: {
						include: {
							user: true
						}
					},
					listing: {
						include: {
							locations: true
						}
					},
					messages: {
						orderBy: {
							created_at: "desc"
						}
					}
				}
			});

			expect(result).toBeDefined();
		});

		it("should handle decline responses correctly", async () => {
			// Mock the quote update
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue({
				...mockQuote,
				status: QuoteStatus.REJECTED,
				provider_notes: "Unable to help at this time",
				rejection_reason: "too_busy"
			});

			const result = await QuoteStatusService.providerRespond({
				quoteId: mockQuoteId,
				listingId: mockListingId,
				responseType: "declined",
				message: "Unable to help at this time",
				rejectionReason: "too_busy"
			});

			// Verify quote was updated with decline info
			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: {
					status: QuoteStatus.REJECTED,
					provider_notes: "Unable to help at this time",
					rejection_reason: "too_busy"
				},
				include: {
					job: {
						include: {
							user: true
						}
					},
					listing: {
						include: {
							locations: true
						}
					},
					messages: {
						orderBy: {
							created_at: "desc"
						}
					}
				}
			});

			expect(result).toBeDefined();
		});
	});

	describe("providerWithdrawFromJob", () => {
		beforeEach(() => {
			(mockPrisma.quote.findUnique as jest.Mock)
				.mockResolvedValueOnce({
					// canProviderPerformAction call
					status: QuoteStatus.ACCEPTED,
					listing_id: mockListingId
				})
				.mockResolvedValueOnce(mockQuote); // getQuoteWithDetails call

			(mockPrisma.listing.findUnique as jest.Mock).mockResolvedValue(
				mockListing
			);
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue(mockQuote);
		});

		it("should withdraw from job with notifications", async () => {
			const result = await QuoteStatusService.providerWithdrawFromJob({
				quoteId: mockQuoteId,
				listingId: mockListingId,
				userId: mockUserId,
				message: "Unexpected scheduling conflict",
				rejectionReason: "scheduling_conflict"
			});

			// Verify quote status was updated (WITHDRAWN status sets responded_at)
			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: {
					status: QuoteStatus.WITHDRAWN,
					provider_notes: "Unexpected scheduling conflict",
					rejection_reason: "scheduling_conflict",
					responded_at: expect.any(Date)
				},
				include: {
					job: {
						include: {
							user: true
						}
					},
					listing: {
						include: {
							locations: true
						}
					},
					messages: {
						orderBy: {
							created_at: "desc"
						}
					}
				}
			});

			expect(result).toBeDefined();
		});
	});

	describe("customerCompleteJob", () => {
		const mockSelectedProviderId = "provider-789";
		const mockReason = "Work completed successfully";

		const mockUser = {
			id: mockUserId,
			first_name: "John",
			last_name: "Doe",
			email: "<EMAIL>",
		};

		const mockSelectedListing = {
			id: mockSelectedProviderId,
			business_name: "Best RV Service",
			first_name: "Jane",
			last_name: "Provider",
			email: "<EMAIL>",
			notification_email: "<EMAIL>",
		};

		const mockOtherListing = {
			id: "other-provider",
			business_name: "Other RV Service",
			first_name: "Bob",
			last_name: "Smith",
			email: "<EMAIL>",
			notification_email: null,
		};

		const mockSelectedQuote = {
			id: "quote-123",
			listing_id: mockSelectedProviderId,
			status: QuoteStatus.PENDING,
			listing: mockSelectedListing,
		};

		const mockOtherQuote = {
			id: "quote-456",
			listing_id: "other-provider",
			status: QuoteStatus.PENDING,
			listing: mockOtherListing,
		};

		const mockJobForCompletion = {
			id: mockJobId,
			user_id: mockUserId,
			status: "OPEN" as any,
			accepted_quote_id: null,
			quotes: [mockSelectedQuote, mockOtherQuote],
			user: mockUser,
		};

		beforeEach(() => {
			jest.clearAllMocks();
			(mockPrisma.job.findFirst as jest.Mock).mockResolvedValue(mockJobForCompletion);
			(mockPrisma.job.update as jest.Mock).mockResolvedValue({ ...mockJobForCompletion, accepted_quote_id: mockSelectedQuote.id });
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue({ ...mockSelectedQuote, status: QuoteStatus.COMPLETED });
		});

		it("should complete job with selected provider", async () => {
			// Mock the service methods that will be called
			const changeQuoteStatusSpy = jest.spyOn(QuoteStatusService, 'changeQuoteStatus')
				.mockResolvedValue(mockSelectedQuote as any);
			const expireQuotesSpy = jest.spyOn(QuoteStatusService as any, 'expireQuotes')
				.mockImplementation(jest.fn());

			await QuoteStatusService.customerCompleteJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: mockReason,
				selectedProviderId: mockSelectedProviderId,
			});

			// Verify job was fetched with correct relationships
			expect(mockPrisma.job.findFirst).toHaveBeenCalledWith({
				where: { id: mockJobId, user_id: mockUserId },
				include: {
					quotes: {
						include: {
							listing: {
								select: {
									id: true,
									business_name: true,
									first_name: true,
									last_name: true,
									email: true,
									notification_email: true,
								}
							}
						}
					},
					user: true,
				}
			});

			// Verify selected quote was marked as completed
			expect(changeQuoteStatusSpy).toHaveBeenCalledWith({
				quoteId: mockSelectedQuote.id,
				listingId: mockSelectedProviderId,
				newStatus: QuoteStatus.COMPLETED,
				userId: mockUserId,
			});

			// Verify job was updated with accepted quote
			expect(mockPrisma.job.update).toHaveBeenCalledWith({
				where: { id: mockJobId },
				data: { accepted_quote_id: mockSelectedQuote.id },
			});

			// Verify other quotes were expired
			expect(expireQuotesSpy).toHaveBeenCalledWith([mockOtherQuote], mockUserId);

			// Verify completion email was sent to selected provider
			expect(emailService.send).toHaveBeenCalledWith({
				to: "<EMAIL>", // Uses notification_email
				subject: "Customer Completed Service Request",
				react: expect.any(Object),
			});

			// Verify "no longer available" email was sent to other providers
			expect(emailService.send).toHaveBeenCalledWith({
				to: "<EMAIL>", // Falls back to email since no notification_email
				subject: "Service Request Completed by Customer",
				react: expect.any(Object),
			});

			changeQuoteStatusSpy.mockRestore();
			expireQuotesSpy.mockRestore();
		});

		it("should complete job without selected provider", async () => {
			const expireQuotesSpy = jest.spyOn(QuoteStatusService as any, 'expireQuotes')
				.mockImplementation(jest.fn());

			await QuoteStatusService.customerCompleteJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: mockReason,
				// No selectedProviderId
			});

			// Verify all quotes were expired
			expect(expireQuotesSpy).toHaveBeenCalledWith(mockJobForCompletion.quotes, mockUserId);

			// Verify "no longer available" emails were sent to all providers
			expect(emailService.send).toHaveBeenCalledTimes(2);
			expect(emailService.send).toHaveBeenCalledWith(
				expect.objectContaining({
					to: "<EMAIL>",
					subject: "Service Request Completed by Customer",
				})
			);
			expect(emailService.send).toHaveBeenCalledWith(
				expect.objectContaining({
					to: "<EMAIL>",
					subject: "Service Request Completed by Customer",
				})
			);

			expireQuotesSpy.mockRestore();
		});

		it("should throw error if job not found", async () => {
			(mockPrisma.job.findFirst as jest.Mock).mockResolvedValue(null);

			await expect(QuoteStatusService.customerCompleteJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: mockReason,
			})).rejects.toThrow("Job not found or access denied");
		});

		it("should throw error if job has accepted quote", async () => {
			(mockPrisma.job.findFirst as jest.Mock).mockResolvedValue({
				...mockJobForCompletion,
				accepted_quote_id: "existing-quote-id",
			});

			await expect(QuoteStatusService.customerCompleteJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: mockReason,
			})).rejects.toThrow("Job already has a selected provider - use standard completion flow");
		});

		it("should throw error if selected provider not found in quotes", async () => {
			await expect(QuoteStatusService.customerCompleteJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: mockReason,
				selectedProviderId: "non-existent-provider",
			})).rejects.toThrow("Selected provider was not invited to this job");
		});

		it("should handle email failures gracefully", async () => {
			const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

			// Mock email service to fail
			(emailService.send as jest.Mock).mockRejectedValue(new Error("Email service down"));

			const changeQuoteStatusSpy = jest.spyOn(QuoteStatusService, 'changeQuoteStatus')
				.mockResolvedValue(mockSelectedQuote as any);
			const expireQuotesSpy = jest.spyOn(QuoteStatusService as any, 'expireQuotes')
				.mockImplementation(jest.fn());

			// Should not throw error even if emails fail
			await expect(QuoteStatusService.customerCompleteJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: mockReason,
				selectedProviderId: mockSelectedProviderId,
			})).resolves.not.toThrow();

			// Should still update quotes and job status
			expect(changeQuoteStatusSpy).toHaveBeenCalled();
			expect(mockPrisma.job.update).toHaveBeenCalled();

			// Should log errors but not fail
			expect(consoleSpy).toHaveBeenCalledWith(
				expect.stringContaining("Failed to send completion notification"),
				expect.any(Error)
			);

			changeQuoteStatusSpy.mockRestore();
			expireQuotesSpy.mockRestore();
			consoleSpy.mockRestore();
		});

		it("should add newsletter tag to customer when job is completed", async () => {
			// Mock the service methods that will be called
			const changeQuoteStatusSpy = jest.spyOn(QuoteStatusService, 'changeQuoteStatus')
				.mockResolvedValue(mockSelectedQuote as any);
			const expireQuotesSpy = jest.spyOn(QuoteStatusService as any, 'expireQuotes')
				.mockImplementation(jest.fn());

			// Mock the EmailNewsletterService
			(EmailNewsletterService.syncNewsletterSubscriber as jest.Mock).mockResolvedValue(true);

			await QuoteStatusService.customerCompleteJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: mockReason,
				selectedProviderId: mockSelectedProviderId,
			});

			// Verify newsletter tag was added to customer
			expect(EmailNewsletterService.syncNewsletterSubscriber).toHaveBeenCalledWith({
				email: "<EMAIL>",
				first_name: "John",
				last_name: "Doe",
				user: mockUser,
				tags: ["job complete"]
			});

			changeQuoteStatusSpy.mockRestore();
			expireQuotesSpy.mockRestore();
		});

		it("should not fail job completion if newsletter tagging fails", async () => {
			// Mock the service methods that will be called
			const changeQuoteStatusSpy = jest.spyOn(QuoteStatusService, 'changeQuoteStatus')
				.mockResolvedValue(mockSelectedQuote as any);
			const expireQuotesSpy = jest.spyOn(QuoteStatusService as any, 'expireQuotes')
				.mockImplementation(jest.fn());

			// Mock the EmailNewsletterService to fail
			(EmailNewsletterService.syncNewsletterSubscriber as jest.Mock).mockRejectedValue(
				new Error("Newsletter service error")
			);

			// Should not throw error
			await expect(QuoteStatusService.customerCompleteJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: mockReason,
				selectedProviderId: mockSelectedProviderId,
			})).resolves.not.toThrow();

			// Verify the job completion still succeeded
			expect(changeQuoteStatusSpy).toHaveBeenCalled();
			expect(mockPrisma.job.update).toHaveBeenCalled();

			changeQuoteStatusSpy.mockRestore();
			expireQuotesSpy.mockRestore();
		});
	});

	describe("customerCancelJob", () => {
		const mockUser = {
			id: mockUserId,
			first_name: "John",
			last_name: "Doe",
			email: "<EMAIL>",
		};

		const mockSelectedListing = {
			id: "provider-789",
			business_name: "Best RV Service",
			first_name: "Jane",
			last_name: "Provider",
			email: "<EMAIL>",
			notification_email: "<EMAIL>",
		};

		const mockOtherListing = {
			id: "other-provider",
			business_name: "Other RV Service",
			first_name: "Bob",
			last_name: "Smith",
			email: "<EMAIL>",
			notification_email: null,
		};

		const mockSelectedQuote = {
			id: "quote-123",
			listing_id: "provider-789",
			status: QuoteStatus.PENDING,
			listing: mockSelectedListing,
		};

		const mockOtherQuote = {
			id: "quote-456",
			listing_id: "other-provider",
			status: QuoteStatus.PENDING,
			listing: mockOtherListing,
		};

		const mockJobForCancellation = {
			id: mockJobId,
			user_id: mockUserId,
			status: "OPEN" as any,
			accepted_quote_id: null,
			quotes: [mockSelectedQuote, mockOtherQuote],
			user: mockUser,
		};

		beforeEach(() => {
			jest.clearAllMocks();
			(mockPrisma.job.findFirst as jest.Mock).mockResolvedValue(mockJobForCancellation);
			(mockPrisma.job.update as jest.Mock).mockResolvedValue({ ...mockJobForCancellation, accepted_quote_id: mockSelectedQuote.id });
			(mockPrisma.quote.update as jest.Mock).mockResolvedValue({ ...mockSelectedQuote, status: QuoteStatus.COMPLETED });
		});

		it("should cancel job and notify all providers", async () => {
			const expireQuotesSpy = jest.spyOn(QuoteStatusService as any, 'expireQuotes')
				.mockImplementation(jest.fn());

			await QuoteStatusService.customerCancelJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: "No longer needed",
			});

			// Verify job was fetched
			expect(mockPrisma.job.findFirst).toHaveBeenCalledWith({
				where: { id: mockJobId, user_id: mockUserId },
				include: {
					quotes: {
						include: {
							listing: {
								select: {
									id: true,
									business_name: true,
									first_name: true,
									last_name: true,
									email: true,
								}
							}
						}
					},
					user: true,
				}
			});

			// Verify all quotes were expired
			expect(expireQuotesSpy).toHaveBeenCalledWith(mockJobForCancellation.quotes, mockUserId);

			// Verify cancellation emails were sent
			expect(emailService.send).toHaveBeenCalledTimes(2);
			expect(emailService.send).toHaveBeenCalledWith(
				expect.objectContaining({
					subject: "Service Request Cancelled by Customer",
				})
			);

			expireQuotesSpy.mockRestore();
		});

		it("should throw error if job not found", async () => {
			(mockPrisma.job.findFirst as jest.Mock).mockResolvedValue(null);

			await expect(QuoteStatusService.customerCancelJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: "No longer needed",
			})).rejects.toThrow("Job not found or access denied");
		});

		it("should only notify providers with pending or accepted quotes", async () => {
			// Mock job with quotes in different statuses
			const jobWithMixedQuotes = {
				...mockJobForCancellation,
				quotes: [
					{ ...mockSelectedQuote, status: QuoteStatus.PENDING },
					{ ...mockOtherQuote, status: QuoteStatus.REJECTED },
					{
						id: "quote-789",
						listing_id: "accepted-provider",
						status: QuoteStatus.ACCEPTED,
						listing: {
							id: "accepted-provider",
							email: "<EMAIL>",
							business_name: "Accepted Provider",
						}
					}
				]
			};

			(mockPrisma.job.findFirst as jest.Mock).mockResolvedValue(jobWithMixedQuotes);

			const expireQuotesSpy = jest.spyOn(QuoteStatusService as any, 'expireQuotes')
				.mockImplementation(jest.fn());

			await QuoteStatusService.customerCancelJob({
				jobId: mockJobId,
				userId: mockUserId,
				reason: "No longer needed",
			});

			// Should only send 2 emails (PENDING and ACCEPTED, not REJECTED)
			expect(emailService.send).toHaveBeenCalledTimes(2);

			expireQuotesSpy.mockRestore();
		});
	});
});
