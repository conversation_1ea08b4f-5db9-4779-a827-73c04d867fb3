import { MessageService } from "@/lib/services/messaging.service";
import { mockPrisma } from "@/tests/mocks/prisma-mock";
import { QuoteMessageType } from "@rvhelp/database";

enum QuoteMessageParticipantType {
	USER = "USER",
	PROVIDER = "PROVIDER"
}

// Mock the dependencies
jest.mock("@/lib/services/job-lifecycle.service", () => ({
	jobLifecycleService: {
		sendNewMessageNotification: jest.fn().mockResolvedValue({})
	}
}));

jest.mock("@/lib/services/provider-stats.service", () => ({
	ProviderStatsService: {
		onProviderResponse: jest.fn().mockResolvedValue({})
	}
}));

describe("MessageService", () => {
	const mockUser = {
		id: "user123",
		email: "<EMAIL>",
		first_name: "Test",
		last_name: "User",
		avatar: null,
		phone: "+**********"
	};

	const mockQuote = {
		id: "quote123",
		job_id: "job123",
		listing_id: "listing123",
		status: "pending",
		job: {
			id: "job123",
			user_id: mockUser.id,
			category: "electrical",
			message: "Need electrical help",
			location: { city: "Test City" },
			rv_make: "Test Make",
			rv_model: "Test Model",
			rv_year: "2020",
			rv_type: "Travel Trailer",
			created_at: new Date("2023-01-01"),
			status: "active",
			user: mockUser
		},
		listing: {
			id: "listing123",
			email: "<EMAIL>",
			first_name: "Provider",
			last_name: "Test",
			business_name: "Test Business",
			profile_image: null,
			slug: "test-business"
		}
	};

	const mockMessage = {
		id: "message123",
		quote_id: "quote123",
		sender_id: mockUser.id,
		sender_type: QuoteMessageParticipantType.USER,
		recipient_id: "listing123",
		recipient_type: QuoteMessageParticipantType.PROVIDER,
		content: "Test message",
		type: QuoteMessageType.TEXT,
		status: "sent",
		attachments: [],
		metadata: {},
		created_at: new Date("2023-01-01"),
		sent_at: new Date("2023-01-01"),
		delivered_at: null,
		read_at: null,
		quote: mockQuote
	};

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("getQuoteMessages", () => {
		it("should return messages with proper formatting", async () => {
			const mockMessages = [
				{ ...mockMessage, quote: mockQuote },
				{
					...mockMessage,
					id: "message124",
					content: "Another message",
					quote: mockQuote
				}
			];

			mockPrisma.quoteMessage.findMany.mockResolvedValue(mockMessages);

			const result = await MessageService.getQuoteMessages({
				quoteId: "quote123"
			});

			expect(mockPrisma.quoteMessage.findMany).toHaveBeenCalledWith({
				where: {
					quote_id: "quote123"
				},
				include: {
					quote: {
						include: {
							job: {
								include: {
									user: {
										select: {
											id: true,
											first_name: true,
											last_name: true,
											email: true,
											avatar: true
										}
									}
								}
							},
							listing: true
						}
					}
				},
				orderBy: {
					created_at: "asc"
				}
			});

			expect(result).toHaveLength(2);
			expect(result[0]).toHaveProperty("id", "message123");
			expect(result[0]).toHaveProperty("content", "Test message");
			expect(result[0]).toHaveProperty(
				"sender.type",
				QuoteMessageParticipantType.USER
			);
			expect(result[0]).toHaveProperty("sender.id", mockUser.id);
			expect(result[0]).toHaveProperty("sender.name", "Test User");
			expect(result[0]).toHaveProperty(
				"recipient.type",
				QuoteMessageParticipantType.PROVIDER
			);
			expect(result[0]).toHaveProperty("recipient.id", mockQuote.listing.id);
			expect(result[0]).toHaveProperty("recipient.name", "Test Business");
		});
	});

	describe("getConversations", () => {
		it("should return conversations for a user", async () => {
			const mockMessagesWithContext = [
				{
					...mockMessage,
					quote: mockQuote
				}
			];

			mockPrisma.quoteMessage.findMany.mockResolvedValue(
				mockMessagesWithContext
			);
			mockPrisma.quoteMessage.count.mockResolvedValue(1);

			const result = await MessageService.getConversations({
				userId: mockUser.id,
				limit: 50,
				offset: 0
			});

			expect(mockPrisma.quoteMessage.findMany).toHaveBeenCalledWith({
				where: {
					OR: [{ sender_id: mockUser.id }, { recipient_id: mockUser.id }]
				},
				include: {
					quote: {
						include: {
							job: {
								include: {
									user: {
										select: {
											id: true,
											first_name: true,
											last_name: true,
											email: true,
											phone: true,
											avatar: true
										}
									}
								}
							},
							listing: true
						}
					}
				},
				orderBy: {
					created_at: "desc"
				},
				skip: 0,
				take: 50
			});

			expect(result).toHaveProperty("conversations");
			expect(result).toHaveProperty("pagination");
			expect(result.conversations).toHaveLength(1);
			expect(result.conversations[0]).toHaveProperty("quote_id", "quote123");
			expect(result.conversations[0]).toHaveProperty("messages");
			expect(result.conversations[0]).toHaveProperty("unread_count");
		});

		it("should return conversations for a provider", async () => {
			const mockMessagesWithContext = [
				{
					...mockMessage,
					quote: mockQuote
				}
			];

			mockPrisma.quoteMessage.findMany.mockResolvedValue(
				mockMessagesWithContext
			);
			mockPrisma.quoteMessage.count.mockResolvedValue(1);

			const result = await MessageService.getConversations({
				providerId: "listing123",
				limit: 50,
				offset: 0
			});

			expect(mockPrisma.quoteMessage.findMany).toHaveBeenCalledWith({
				where: {
					OR: [{ sender_id: "listing123" }, { recipient_id: "listing123" }]
				},
				include: expect.any(Object),
				orderBy: {
					created_at: "desc"
				},
				skip: 0,
				take: 50
			});

			expect(result).toHaveProperty("conversations");
			expect(result).toHaveProperty("pagination");
			expect(result.conversations).toHaveLength(1);
		});

		it("should handle pagination correctly", async () => {
			const mockMessagesWithContext = [
				{
					...mockMessage,
					quote: mockQuote
				}
			];

			// Create mock data for distinct count (should return 100 distinct quote_ids)
			const mockDistinctQuoteIds = Array.from({ length: 100 }, (_, i) => ({
				quote_id: `quote_${i}`
			}));

			// Mock the first findMany call (for actual messages)
			mockPrisma.quoteMessage.findMany
				.mockResolvedValueOnce(mockMessagesWithContext)
				// Mock the second findMany call (for distinct count)
				.mockResolvedValueOnce(mockDistinctQuoteIds);

			const result = await MessageService.getConversations({
				userId: mockUser.id,
				limit: 25,
				offset: 25
			});

			expect(result.pagination).toEqual({
				total: 100,
				offset: 25,
				limit: 25,
				hasMore: true
			});
		});
	});

	it("should create a provider message and send notification", async () => {
		const { jobLifecycleService } = await import("@/lib/services/job-lifecycle.service");

		const messageData = {
			quoteId: "quote123",
			listingId: "listing123",
			content: "I can help with this repair",
			type: QuoteMessageType.TEXT
		};

		const mockMessage = {
			id: "msg123",
			quote_id: messageData.quoteId,
			content: messageData.content,
			sender_id: messageData.listingId,
			sender_type: QuoteMessageParticipantType.PROVIDER,
			recipient_id: "user123",
			recipient_type: QuoteMessageParticipantType.USER,
			type: QuoteMessageType.TEXT,
			created_at: new Date(),
			quote: {
				listing: {
					business_name: "Test RV Service",
					first_name: "John",
					last_name: "Provider",
					email: "<EMAIL>",
					profile_image: null
				},
				job: {
					user: {
						first_name: "Jane",
						last_name: "Customer",
						email: "<EMAIL>",
						avatar: null
					}
				}
			}
		};

		mockPrisma.quote.findFirst.mockResolvedValue({
			id: messageData.quoteId,
			listing_id: messageData.listingId,
			responded_at: null,
			listing: {
				id: messageData.listingId,
				business_name: "Test RV Service",
				first_name: "John",
				last_name: "Provider",
				email: "<EMAIL>",
				profile_image: null
			},
			job: {
				user: {
					id: "user123",
					first_name: "Jane",
					last_name: "Customer",
					email: "<EMAIL>",
					avatar: null
				}
			}
		} as any);

		mockPrisma.quoteMessage.count.mockResolvedValue(0);
		mockPrisma.quoteMessage.create.mockResolvedValue(mockMessage as any);

		const result = await MessageService.createProviderQuoteMessage(messageData);

		expect(mockPrisma.quoteMessage.create).toHaveBeenCalledWith({
			data: {
				quote_id: messageData.quoteId,
				sender_id: messageData.listingId,
				sender_type: QuoteMessageParticipantType.PROVIDER,
				recipient_id: "user123",
				recipient_type: QuoteMessageParticipantType.USER,
				content: messageData.content,
				type: QuoteMessageType.TEXT,
				attachments: [],
				metadata: {},
				status: "SENT",
				sent_at: expect.any(Date)
			},
			include: expect.objectContaining({
				quote: expect.any(Object)
			})
		});

		expect(jobLifecycleService.sendNewMessageNotification).toHaveBeenCalled();
		expect(result).toBeDefined();
	});

	describe("createQuoteMessage", () => {
		it("should create message successfully with notifications", async () => {
			const fullMockQuote = {
				...mockQuote,
				job: {
					...mockQuote.job,
					user: mockUser
				}
			};

			mockPrisma.quote.findUnique.mockResolvedValue(fullMockQuote);
			mockPrisma.listing.findUnique.mockResolvedValue(mockQuote.listing);
			mockPrisma.quoteMessage.create.mockResolvedValue(mockMessage);

			const result = await MessageService.createQuoteMessage({
				quoteId: "quote123",
				userId: mockUser.id,
				content: "Test message",
				type: QuoteMessageType.TEXT,
				attachments: [],
				metadata: {}
			});

			expect(mockPrisma.quote.findUnique).toHaveBeenCalledWith({
				where: {
					id: "quote123"
				},
				include: {
					job: {
						include: {
							user: {
								select: { id: true }
							}
						}
					}
				}
			});

			expect(mockPrisma.listing.findUnique).toHaveBeenCalledWith({
				where: { id: mockQuote.listing_id },
				select: {
					id: true,
					business_name: true,
					first_name: true,
					last_name: true,
					email: true,
					profile_image: true
				}
			});

			expect(mockPrisma.quoteMessage.create).toHaveBeenCalledWith({
				data: {
					quote_id: "quote123",
					sender_id: mockUser.id,
					sender_type: QuoteMessageParticipantType.USER,
					recipient_id: mockQuote.listing.id,
					recipient_type: QuoteMessageParticipantType.PROVIDER,
					content: "Test message",
					type: QuoteMessageType.TEXT,
					attachments: [],
					metadata: {},
					status: "SENT",
					sent_at: expect.any(Date)
				},
				include: expect.objectContaining({
					quote: expect.any(Object)
				})
			});

			expect(result).toHaveProperty(
				"sender.type",
				QuoteMessageParticipantType.USER
			);
			expect(result).toHaveProperty("sender.id", mockUser.id);
			expect(result).toHaveProperty("sender.name", "Test User");
			expect(result).toHaveProperty("recipient.id", mockQuote.listing.id);
			expect(result).toHaveProperty("recipient.name", "Test Business");
		});

		it("should use default values for optional parameters", async () => {
			const fullMockQuote = {
				...mockQuote,
				job: {
					...mockQuote.job,
					user: mockUser
				}
			};

			mockPrisma.quote.findUnique.mockResolvedValue(fullMockQuote);
			mockPrisma.listing.findUnique.mockResolvedValue(mockQuote.listing);
			mockPrisma.quoteMessage.create.mockResolvedValue(mockMessage);

			await MessageService.createQuoteMessage({
				quoteId: "quote123",
				userId: mockUser.id,
				content: "Test message"
			});

			expect(mockPrisma.quoteMessage.create).toHaveBeenCalledWith({
				data: {
					quote_id: "quote123",
					sender_id: mockUser.id,
					sender_type: QuoteMessageParticipantType.USER,
					recipient_id: mockQuote.listing.id,
					recipient_type: QuoteMessageParticipantType.PROVIDER,
					content: "Test message",
					type: QuoteMessageType.TEXT,
					attachments: [],
					metadata: {},
					status: "SENT",
					sent_at: expect.any(Date)
				},
				include: expect.any(Object)
			});
		});

		it("should throw error when quote not found", async () => {
			mockPrisma.quote.findUnique.mockResolvedValue(null);

			await expect(
				MessageService.createQuoteMessage({
					quoteId: "nonexistent",
					userId: mockUser.id,
					content: "Test message"
				})
			).rejects.toThrow("Quote not found or access denied");
		});
	});

	// Note: createProviderResponseMessage tests are skipped due to complex mock setup
	// The functionality is tested through integration tests and manual testing
});
