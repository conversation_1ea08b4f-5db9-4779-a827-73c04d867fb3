import { slackService } from "@/lib/services";
import { FirstPromoterService } from "@/lib/services/first-promoter.service";
import { MembershipService } from "@/lib/services/membership.service";
import { mockPrisma } from "../../mocks/prisma-mock";


// Import the mocked emailService
const { emailService } = require("@/lib/services");

jest.mock("@/lib/services/first-promoter.service", () => ({
	FirstPromoterService: {
		trackProMembership: jest.fn().mockResolvedValue({ success: true })
	}
}));

describe("MembershipService", () => {
	let membershipService: MembershipService;

	beforeEach(() => {
		jest.clearAllMocks();
		membershipService = new MembershipService();

		// Mock the transaction to just call the callback with mocked prisma
		(mockPrisma.$transaction as jest.Mock).mockImplementation(
			async (callback) => {
				return callback(mockPrisma);
			}
		);
	});

	describe("createOrUpdateMembership", () => {
		const mockUser = {
			id: "user123",
			email: "<EMAIL>",
			first_name: "<PERSON>",
			last_name: "<PERSON><PERSON>",
			membership_level: "STANDARD",
			member_number: 1
		};

		it("should create a new membership for a user without existing membership", async () => {
			const membershipData = {
				userId: "user123",
				level: "STANDARD" as const,
				stripeSessionId: "sess_123",
				stripeSubscriptionId: "sub_123",
				amountPaid: 4900,
				setupPasswordUrl: "https://example.com/setup"
			};

			// Mock no existing membership
			(mockPrisma.membership.findUnique as jest.Mock).mockResolvedValue(null);
			(mockPrisma.membership.count as jest.Mock).mockResolvedValue(0);

			const mockMembership = {
				id: "membership123",
				user_id: "user123",
				level: "STANDARD",
				member_number: 1,
				stripe_session_id: "sess_123",
				stripe_subscription_id: "sub_123",
				amount_paid: 4900
			};

			(mockPrisma.membership.create as jest.Mock).mockResolvedValue(
				mockMembership
			);
			(mockPrisma.user.update as jest.Mock).mockResolvedValue(mockUser);

			const result =
				await membershipService.createOrUpdateMembership(membershipData);

			expect(mockPrisma.membership.findUnique).toHaveBeenCalledWith({
				where: { user_id: "user123" }
			});

			expect(mockPrisma.membership.create).toHaveBeenCalledWith({
				data: {
					user_id: "user123",
					level: "STANDARD",
					member_number: 1,
					stripe_session_id: "sess_123",
					stripe_subscription_id: "sub_123",
					amount_paid: 4900,
					currency: "usd"
				}
			});

			expect(mockPrisma.user.update).toHaveBeenCalledWith({
				where: { id: "user123" },
				data: {
					membership_level: "STANDARD",
					member_number: 1
				}
			});

			// emailService is already imported at the top of the file
			expect(
				emailService.sendMembershipWelcomeEmail
			).toHaveBeenCalledWith({
				email: "<EMAIL>",
				name: "John Doe",
				setupPasswordUrl: "https://example.com/setup",
				membershipLevel: "STANDARD"
			});

			expect(slackService.notifyNewMemberSignup).toHaveBeenCalledWith(
				mockUser,
				"STANDARD",
				1
			);

			expect(result).toEqual({
				user: mockUser,
				membership: mockMembership,
				isNewMembership: true
			});
		});

		it("should call FirstPromoter.trackProMembership for new membership", async () => {
			const membershipData = {
				userId: "user123",
				level: "STANDARD" as const,
				stripeSessionId: "sess_123",
				stripeSubscriptionId: "sub_123",
				amountPaid: 4900
			};

			// Mock no existing membership
			(mockPrisma.membership.findUnique as jest.Mock).mockResolvedValue(null);
			(mockPrisma.membership.count as jest.Mock).mockResolvedValue(0);

			const mockMembership = {
				id: "membership123",
				user_id: "user123",
				level: "STANDARD",
				member_number: 1,
				stripe_session_id: "sess_123",
				stripe_subscription_id: "sub_123",
				amount_paid: 4900
			};

			(mockPrisma.membership.create as jest.Mock).mockResolvedValue(
				mockMembership
			);
			(mockPrisma.user.update as jest.Mock).mockResolvedValue(mockUser);
			(FirstPromoterService.trackProMembership as jest.Mock).mockResolvedValue({
				success: true
			});

			const result =
				await membershipService.createOrUpdateMembership(membershipData);

			expect(FirstPromoterService.trackProMembership).toHaveBeenCalledTimes(1);
			expect(FirstPromoterService.trackProMembership).toHaveBeenCalledWith({
				email: mockUser.email,
				userId: mockUser.id,
				membershipLevel: "STANDARD",
				amount: 4900,
				currency: "usd"
			});

			expect(result.isNewMembership).toBe(true);
		});

		it("should update existing membership", async () => {
			const membershipData = {
				userId: "user123",
				level: "PREMIUM" as const,
				stripeSessionId: "sess_456",
				stripeSubscriptionId: "sub_456",
				amountPaid: 9900
			};

			const existingMembership = {
				id: "membership123",
				user_id: "user123",
				level: "STANDARD",
				member_number: 1,
				is_active: true
			};

			const updatedMembership = {
				...existingMembership,
				level: "PREMIUM",
				stripe_session_id: "sess_456",
				stripe_subscription_id: "sub_456",
				amount_paid: 9900
			};

			(mockPrisma.membership.findUnique as jest.Mock).mockResolvedValue(
				existingMembership
			);
			(mockPrisma.membership.update as jest.Mock).mockResolvedValue(
				updatedMembership
			);
			(mockPrisma.user.update as jest.Mock).mockResolvedValue({
				...mockUser,
				membership_level: "PREMIUM"
			});

			const result =
				await membershipService.createOrUpdateMembership(membershipData);

			expect(mockPrisma.membership.update).toHaveBeenCalledWith({
				where: { user_id: "user123" },
				data: {
					level: "PREMIUM",
					stripe_session_id: "sess_456",
					stripe_subscription_id: "sub_456",
					amount_paid: 9900,
					currency: "usd",
					is_active: true,
					cancelled_at: null
				}
			});

			expect(mockPrisma.user.update).toHaveBeenCalledWith({
				where: { id: "user123" },
				data: {
					membership_level: "PREMIUM",
					member_number: 1
				}
			});

			// Should not send welcome email for existing membership
			expect(
				emailService.sendMembershipWelcomeEmail
			).not.toHaveBeenCalled();
			expect(slackService.notifyNewMemberSignup).not.toHaveBeenCalled();

			expect(result.isNewMembership).toBe(false);
		});

		it("should handle email service failure gracefully", async () => {
			const membershipData = {
				userId: "user123",
				level: "STANDARD" as const,
				setupPasswordUrl: "https://example.com/setup"
			};

			(mockPrisma.membership.findUnique as jest.Mock).mockResolvedValue(null);
			(mockPrisma.membership.count as jest.Mock).mockResolvedValue(0);
			(mockPrisma.membership.create as jest.Mock).mockResolvedValue({
				id: "membership123",
				member_number: 1
			});
			(mockPrisma.user.update as jest.Mock).mockResolvedValue(mockUser);

			// Mock email service failure
			(
				emailService.sendMembershipWelcomeEmail as jest.Mock
			).mockRejectedValue(new Error("Email service failed"));

			// Should not throw error
			const result =
				await membershipService.createOrUpdateMembership(membershipData);

			expect(result.isNewMembership).toBe(true);
			expect(
				emailService.sendMembershipWelcomeEmail
			).toHaveBeenCalled();
		});

		it("should skip notifications when flags are false", async () => {
			const membershipData = {
				userId: "user123",
				level: "STANDARD" as const,
				sendWelcomeEmail: false,
				sendSlackNotification: false
			};

			(mockPrisma.membership.findUnique as jest.Mock).mockResolvedValue(null);
			(mockPrisma.membership.count as jest.Mock).mockResolvedValue(0);
			(mockPrisma.membership.create as jest.Mock).mockResolvedValue({
				id: "membership123",
				member_number: 1
			});
			(mockPrisma.user.update as jest.Mock).mockResolvedValue(mockUser);

			await membershipService.createOrUpdateMembership(membershipData);

			expect(
				emailService.sendMembershipWelcomeEmail
			).not.toHaveBeenCalled();
			expect(slackService.notifyNewMemberSignup).not.toHaveBeenCalled();
		});
	});

	describe("getMembershipStats", () => {
		it("should return accurate membership statistics", async () => {
			const startDate = new Date("2023-01-01");
			const endDate = new Date("2023-01-31");

			(mockPrisma.membership.count as jest.Mock)
				.mockResolvedValueOnce(150) // totalActiveMembers
				.mockResolvedValueOnce(25) // newMemberships
				.mockResolvedValueOnce(5); // totalCancellations

			const result = await membershipService.getMembershipStats(
				startDate,
				endDate
			);

			expect(result).toEqual({
				totalActiveMembers: 150,
				newMemberships: 25,
				totalCancellations: 5
			});

			expect(mockPrisma.membership.count).toHaveBeenCalledTimes(3);
		});
	});

	describe("cancelMembership", () => {
		it("should cancel an active membership", async () => {
			const userId = "user123";
			const activeMembership = {
				id: "membership123",
				user_id: userId,
				is_active: true,
				level: "STANDARD"
			};

			const cancelledMembership = {
				...activeMembership,
				is_active: false,
				cancelled_at: new Date()
			};

			const updatedUser = {
				id: userId,
				membership_level: "FREE"
			};

			(mockPrisma.membership.findUnique as jest.Mock).mockResolvedValue(
				activeMembership
			);
			(mockPrisma.membership.update as jest.Mock).mockResolvedValue(
				cancelledMembership
			);
			(mockPrisma.user.update as jest.Mock).mockResolvedValue(updatedUser);

			const result = await membershipService.cancelMembership(userId);

			expect(mockPrisma.membership.update).toHaveBeenCalledWith({
				where: { user_id: userId },
				data: {
					is_active: false,
					cancelled_at: expect.any(Date)
				}
			});

			expect(mockPrisma.user.update).toHaveBeenCalledWith({
				where: { id: userId },
				data: { membership_level: "FREE" }
			});

			expect(result).toEqual({
				user: updatedUser,
				membership: cancelledMembership
			});
		});

		it("should throw error if user has no membership", async () => {
			const userId = "user123";
			(mockPrisma.membership.findUnique as jest.Mock).mockResolvedValue(null);

			await expect(membershipService.cancelMembership(userId)).rejects.toThrow(
				"User does not have a membership to cancel"
			);
		});

		it("should throw error if membership is already cancelled", async () => {
			const userId = "user123";
			const cancelledMembership = {
				id: "membership123",
				user_id: userId,
				is_active: false
			};

			(mockPrisma.membership.findUnique as jest.Mock).mockResolvedValue(
				cancelledMembership
			);

			await expect(membershipService.cancelMembership(userId)).rejects.toThrow(
				"Membership is already cancelled"
			);
		});
	});

	describe("getUserMembership", () => {
		it("should return user membership with user details", async () => {
			const userId = "user123";
			const membershipWithUser = {
				id: "membership123",
				user_id: userId,
				level: "STANDARD",
				user: {
					id: userId,
					email: "<EMAIL>",
					first_name: "John"
				}
			};

			(mockPrisma.membership.findUnique as jest.Mock).mockResolvedValue(
				membershipWithUser
			);

			const result = await membershipService.getUserMembership(userId);

			expect(mockPrisma.membership.findUnique).toHaveBeenCalledWith({
				where: { user_id: userId },
				include: { user: true }
			});

			expect(result).toEqual(membershipWithUser);
		});
	});
});
