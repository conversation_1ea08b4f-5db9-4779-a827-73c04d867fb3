import prisma from "@/lib/prisma";
import { BlacklistService } from "@/lib/services/blacklist.service";

// unmock the blacklist service
jest.unmock("@/lib/services/blacklist.service");

describe("BlacklistService", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Default to no entry found
        (prisma.blacklistEntry.findFirst as jest.Mock).mockResolvedValue(null);
    });

    describe("isBlacklisted", () => {
        it("should return false for empty value", async () => {
            const result = await BlacklistService.isBlacklisted("EMAIL", "");
            expect(result.isBlacklisted).toBe(false);
            expect(prisma.blacklistEntry.findFirst).not.toHaveBeenCalled();
        });

        it("should check domain for email addresses", async () => {
            // Mock domain check to return blacklisted
            (prisma.blacklistEntry.findFirst as jest.Mock).mockResolvedValueOnce({
                id: "test",
                value: "spamdomain.com",
                message: "Domain is blacklisted"
            });

            const result = await BlacklistService.isBlacklisted("EMAIL", "<EMAIL>");

            expect(prisma.blacklistEntry.findFirst).toHaveBeenCalledWith(
                expect.objectContaining({
                    where: expect.objectContaining({
                        type: "DOMAIN",
                        value: "spamdomain.com",
                        is_active: true
                    })
                })
            );
            expect(result.isBlacklisted).toBe(true);
            expect(result.message).toBe("Domain is blacklisted");
        });

        it("should check domain for email addresses with mixed case", async () => {
            // Mock domain check to return blacklisted
            (prisma.blacklistEntry.findFirst as jest.Mock).mockResolvedValueOnce({
                id: "test",
                value: "zipfixx.com",
                message: "Domain is blacklisted"
            });

            const result = await BlacklistService.isBlacklisted("EMAIL", "<EMAIL>");

            expect(prisma.blacklistEntry.findFirst).toHaveBeenCalledWith(
                expect.objectContaining({
                    where: expect.objectContaining({
                        type: "DOMAIN",
                        value: "zipfixx.com",
                        is_active: true
                    })
                })
            );
            expect(result.isBlacklisted).toBe(true);
            expect(result.message).toBe("Domain is blacklisted");
        });

        it("should check full email if domain is not blacklisted", async () => {
            // Domain check returns null, email check returns blacklisted
            (prisma.blacklistEntry.findFirst as jest.Mock)
                .mockResolvedValueOnce(null) // Domain check
                .mockResolvedValueOnce({
                    id: "test",
                    value: "<EMAIL>",
                    message: "Email is blacklisted"
                }); // Email check

            const result = await BlacklistService.isBlacklisted("EMAIL", "<EMAIL>");

            expect(prisma.blacklistEntry.findFirst).toHaveBeenCalledTimes(2);
            expect(result.isBlacklisted).toBe(true);
            expect(result.message).toBe("Email is blacklisted");
        });

        it("should return false if entry not found", async () => {
            const result = await BlacklistService.isBlacklisted("EMAIL", "<EMAIL>");
            expect(prisma.blacklistEntry.findFirst).toHaveBeenCalled();
            expect(result.isBlacklisted).toBe(false);
        });

        it("should normalize values to lowercase", async () => {
            // Mock domain check to return null, then email check to return found
            (prisma.blacklistEntry.findFirst as jest.Mock)
                .mockResolvedValueOnce(null) // Domain check returns null
                .mockResolvedValueOnce({
                    id: "test",
                    value: "<EMAIL>"
                }); // Email check returns found

            await BlacklistService.isBlacklisted("EMAIL", "<EMAIL>");

            expect(prisma.blacklistEntry.findFirst).toHaveBeenCalledTimes(2);
            // First call checks domain (normalized)
            expect(prisma.blacklistEntry.findFirst).toHaveBeenNthCalledWith(
                1,
                expect.objectContaining({
                    where: expect.objectContaining({
                        type: "DOMAIN",
                        value: "example.com"
                    })
                })
            );
            // Second call checks full email (normalized)
            expect(prisma.blacklistEntry.findFirst).toHaveBeenNthCalledWith(
                2,
                expect.objectContaining({
                    where: expect.objectContaining({
                        type: "EMAIL",
                        value: "<EMAIL>"
                    })
                })
            );
        });
    });

    describe("addEntry", () => {
        it("should create a blacklist entry", async () => {
            const mockEntry = {
                id: "test-id",
                type: "EMAIL",
                value: "<EMAIL>",
                reason: "Spam",
                message: "Custom message",
                created_at: new Date(),
                created_by: "admin",
                expires_at: null,
                is_active: true
            };

            (prisma.blacklistEntry.create as jest.Mock).mockResolvedValue(mockEntry);

            const result = await BlacklistService.addEntry({
                type: "EMAIL",
                value: "<EMAIL>",
                reason: "Spam",
                message: "Custom message",
                createdBy: "admin"
            });

            expect(prisma.blacklistEntry.create).toHaveBeenCalledWith({
                data: {
                    type: "EMAIL",
                    value: "<EMAIL>",
                    reason: "Spam",
                    message: "Custom message",
                    created_by: "admin",
                    expires_at: null
                }
            });
            expect(result).toEqual(mockEntry);
        });

        it("should normalize value to lowercase", async () => {
            await BlacklistService.addEntry({
                type: "EMAIL",
                value: "<EMAIL>",
                reason: "Spam"
            });

            expect(prisma.blacklistEntry.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    value: "<EMAIL>"
                })
            });
        });
    });

    describe("removeEntry", () => {
        it("should delete a blacklist entry", async () => {
            await BlacklistService.removeEntry("test-id");

            expect(prisma.blacklistEntry.delete).toHaveBeenCalledWith({
                where: { id: "test-id" }
            });
        });
    });

    describe("listEntries", () => {
        it("should list all entries when no type specified", async () => {
            const mockEntries = [
                { id: "1", type: "EMAIL", value: "<EMAIL>" },
                { id: "2", type: "DOMAIN", value: "spam.com" }
            ];

            (prisma.blacklistEntry.findMany as jest.Mock).mockResolvedValue(mockEntries);

            const result = await BlacklistService.listEntries();

            expect(prisma.blacklistEntry.findMany).toHaveBeenCalledWith({
                where: undefined,
                orderBy: { created_at: "desc" }
            });
            expect(result).toEqual(mockEntries);
        });

        it("should filter by type when specified", async () => {
            await BlacklistService.listEntries("EMAIL");

            expect(prisma.blacklistEntry.findMany).toHaveBeenCalledWith({
                where: { type: "EMAIL" },
                orderBy: { created_at: "desc" }
            });
        });
    });
}); 