import prisma from "@/lib/prisma";
import { OfferService } from "@/lib/services/offer.service";
import { OfferUtils } from "@/lib/utils/offer-utils";

describe("OfferService", () => {
    beforeEach(() => {
        jest.clearAllMocks();

        // Mock the transaction to just call the callback with mocked prisma
        (prisma.$transaction as jest.Mock).mockImplementation(async (callback) => {
            return callback(prisma);
        });
    });

    describe("isUserEligibleForAnnualOffer", () => {
        it("should return eligible when user has active offer", async () => {
            const mockOffer = {
                id: "offer123",
                user_id: "user123",
                email: "<EMAIL>",
                offer_type: "ANNUAL_50_OFF",
                discount_percentage: 50,
                description: "Annual 50% off Pro membership",
                is_active: true,
                used_at: null,
                expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
            };

            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(mockOffer);

            const result = await OfferService.isUserEligibleForAnnualOffer("user123");

            expect(result).toEqual({
                eligible: true,
                offer: mockOffer,
            });
        });

        it("should return ineligible when user has no active offers", async () => {
            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(null);

            const result = await OfferService.isUserEligibleForAnnualOffer("user123");

            expect(result).toEqual({
                eligible: false,
                reason: "No active offers found",
            });
        });

        it("should return ineligible for expired offer", async () => {
            const mockOffer = {
                id: "offer123",
                user_id: "user123",
                email: "<EMAIL>",
                offer_type: "ANNUAL_50_OFF",
                discount_percentage: 50,
                description: "Annual 50% off Pro membership",
                is_active: true,
                used_at: null,
                expires_at: new Date(Date.now() - 24 * 60 * 60 * 1000), // expired
            };

            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(mockOffer);

            const result = await OfferService.isUserEligibleForAnnualOffer("user123");

            expect(result).toEqual({
                eligible: true,
                offer: mockOffer,
            });
        });
    });

    describe("isEmailEligibleForAnnualOffer", () => {
        it("should return eligible for email with active offer", async () => {
            const mockOffer = {
                id: "offer123",
                user_id: "user123",
                email: "<EMAIL>",
                offer_type: "ANNUAL_50_OFF",
                discount_percentage: 50,
                description: "Annual 50% off Pro membership",
                is_active: true,
                used_at: null,
                expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
            };

            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(mockOffer);

            const result = await OfferService.isEmailEligibleForAnnualOffer("<EMAIL>");

            expect(result).toEqual({
                eligible: true,
                userId: "user123",
                offer: mockOffer,
            });
        });

        it("should return ineligible for email with no active offers", async () => {
            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(null);

            const result = await OfferService.isEmailEligibleForAnnualOffer("<EMAIL>");

            expect(result).toEqual({
                eligible: false,
                reason: "No active offers found for this email",
            });
        });
    });

    describe("validateMarketingCampaignCoupon", () => {
        it("should validate active marketing campaign coupon", async () => {
            const mockCampaignLead = {
                id: "lead123",
                coupon_code: "SUMMER25",
                coupon_used_at: null,
                campaign: {
                    id: "campaign123",
                    title: "Summer Sale",
                    status: "ACTIVE",
                    discount_type: "PERCENTAGE",
                    discount_value: 25,
                    expires_at: null,
                },
            };

            (prisma.marketingCampaignLead.findFirst as jest.Mock).mockResolvedValue(mockCampaignLead);

            const result = await OfferService.validateMarketingCampaignCoupon("SUMMER25");

            expect(result).toEqual({
                valid: true,
                campaign: mockCampaignLead.campaign,
            });
        });

        it("should return invalid for non-existent coupon", async () => {
            (prisma.marketingCampaignLead.findFirst as jest.Mock).mockResolvedValue(null);

            const result = await OfferService.validateMarketingCampaignCoupon("INVALID");

            expect(result).toEqual({
                valid: false,
                reason: "Invalid or expired coupon code",
            });
        });

        it("should return invalid for used coupon", async () => {
            // Mock to return null since the query filters for coupon_used_at: null
            (prisma.marketingCampaignLead.findFirst as jest.Mock).mockResolvedValue(null);

            const result = await OfferService.validateMarketingCampaignCoupon("USED25");

            expect(result).toEqual({
                valid: false,
                reason: "Invalid or expired coupon code",
            });
        });

        it("should return invalid for expired campaign", async () => {
            const mockCampaignLead = {
                id: "lead123",
                coupon_code: "EXPIRED25",
                coupon_used_at: null,
                campaign: {
                    id: "campaign123",
                    title: "Expired Sale",
                    status: "ACTIVE",
                    discount_type: "PERCENTAGE",
                    discount_value: 25,
                    expires_at: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
                },
            };

            (prisma.marketingCampaignLead.findFirst as jest.Mock).mockResolvedValue(mockCampaignLead);

            const result = await OfferService.validateMarketingCampaignCoupon("EXPIRED25");

            expect(result).toEqual({
                valid: false,
                reason: "Campaign is no longer active",
            });
        });
    });

    describe("markOfferUsed", () => {
        it("should successfully mark offer as used", async () => {
            (prisma.membershipOffer.update as jest.Mock).mockResolvedValue({ id: "offer123" });

            const result = await OfferService.markOfferUsed("offer123");

            expect(result).toBe(true);
            expect(prisma.membershipOffer.update).toHaveBeenCalledWith({
                where: { id: "offer123" },
                data: {
                    used_at: expect.any(Date),
                    is_active: false,
                },
            });
        });

        it("should handle database errors", async () => {
            (prisma.membershipOffer.update as jest.Mock).mockRejectedValue(new Error("Database error"));

            const result = await OfferService.markOfferUsed("offer123");

            expect(result).toBe(false);
        });
    });

    describe("markUserOfferAsUsed", () => {
        it("should successfully mark user offer as used", async () => {
            const mockOffer = {
                id: "offer123",
                user_id: "user123",
                email: "<EMAIL>",
                offer_type: "ANNUAL_50_OFF",
                is_active: true,
                used_at: null,
            };

            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(mockOffer);
            (prisma.membershipOffer.update as jest.Mock).mockResolvedValue({ id: "offer123" });

            const result = await OfferService.markUserOfferAsUsed("user123");

            expect(result).toBe(true);
            expect(prisma.membershipOffer.findFirst).toHaveBeenCalledWith({
                where: {
                    user_id: "user123",
                    is_active: true,
                    used_at: null,
                },
                orderBy: {
                    created_at: "desc"
                }
            });
            expect(prisma.membershipOffer.update).toHaveBeenCalledWith({
                where: { id: "offer123" },
                data: {
                    used_at: expect.any(Date),
                    is_active: false,
                },
            });
        });

        it("should return false when no active offer found for user", async () => {
            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(null);

            const result = await OfferService.markUserOfferAsUsed("user123");

            expect(result).toBe(false);
            expect(prisma.membershipOffer.update).not.toHaveBeenCalled();
        });

        it("should handle database errors when finding offer", async () => {
            (prisma.membershipOffer.findFirst as jest.Mock).mockRejectedValue(new Error("Database error"));

            const result = await OfferService.markUserOfferAsUsed("user123");

            expect(result).toBe(false);
        });

        it("should handle database errors when updating offer", async () => {
            const mockOffer = {
                id: "offer123",
                user_id: "user123",
                email: "<EMAIL>",
                offer_type: "ANNUAL_50_OFF",
                is_active: true,
                used_at: null,
            };

            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(mockOffer);
            (prisma.membershipOffer.update as jest.Mock).mockRejectedValue(new Error("Update error"));

            const result = await OfferService.markUserOfferAsUsed("user123");

            expect(result).toBe(false);
        });
    });

    describe("markEmailOfferAsUsed", () => {
        it("should successfully mark email offer as used", async () => {
            const mockOffer = {
                id: "offer123",
                user_id: "user123",
                email: "<EMAIL>",
                offer_type: "ANNUAL_50_OFF",
                is_active: true,
                used_at: null,
            };

            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(mockOffer);
            (prisma.membershipOffer.update as jest.Mock).mockResolvedValue({ id: "offer123" });

            const result = await OfferService.markEmailOfferAsUsed("<EMAIL>");

            expect(result).toBe(true);
            expect(prisma.membershipOffer.findFirst).toHaveBeenCalledWith({
                where: {
                    email: {
                        equals: "<EMAIL>",
                        mode: "insensitive"
                    },
                    is_active: true,
                    used_at: null,
                },
                orderBy: {
                    created_at: "desc"
                }
            });
            expect(prisma.membershipOffer.update).toHaveBeenCalledWith({
                where: { id: "offer123" },
                data: {
                    used_at: expect.any(Date),
                    is_active: false,
                },
            });
        });

        it("should return false when no active offer found for email", async () => {
            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(null);

            const result = await OfferService.markEmailOfferAsUsed("<EMAIL>");

            expect(result).toBe(false);
            expect(prisma.membershipOffer.update).not.toHaveBeenCalled();
        });

        it("should handle database errors when finding offer", async () => {
            (prisma.membershipOffer.findFirst as jest.Mock).mockRejectedValue(new Error("Database error"));

            const result = await OfferService.markEmailOfferAsUsed("<EMAIL>");

            expect(result).toBe(false);
        });

        it("should handle database errors when updating offer", async () => {
            const mockOffer = {
                id: "offer123",
                user_id: "user123",
                email: "<EMAIL>",
                offer_type: "ANNUAL_50_OFF",
                is_active: true,
                used_at: null,
            };

            (prisma.membershipOffer.findFirst as jest.Mock).mockResolvedValue(mockOffer);
            (prisma.membershipOffer.update as jest.Mock).mockRejectedValue(new Error("Update error"));

            const result = await OfferService.markEmailOfferAsUsed("<EMAIL>");

            expect(result).toBe(false);
        });
    });
});

describe("OfferUtils", () => {
    describe("isJobOfferActive", () => {
        it("should return true for jobs within 72 hours", () => {
            const jobCreatedAt = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
            const result = OfferUtils.isJobOfferActive(jobCreatedAt);
            expect(result).toBe(true);
        });

        it("should return false for jobs older than 72 hours", () => {
            const jobCreatedAt = new Date(Date.now() - 73 * 60 * 60 * 1000); // 73 hours ago
            const result = OfferUtils.isJobOfferActive(jobCreatedAt);
            expect(result).toBe(false);
        });

        it("should return false for jobs exactly 72 hours old", () => {
            const jobCreatedAt = new Date(Date.now() - 72 * 60 * 60 * 1000); // exactly 72 hours ago
            const result = OfferUtils.isJobOfferActive(jobCreatedAt);
            expect(result).toBe(false);
        });
    });

    describe("isUserEligibleForAnnualOfferClientSide", () => {
        it("should return ineligible for paid users", () => {
            const user = {
                membership_level: "STANDARD" as const,
                last_offer_shown_at: null,
            };
            const jobCreatedAt = new Date(Date.now() - 24 * 60 * 60 * 1000);

            const result = OfferUtils.isUserEligibleForAnnualOfferClientSide(user, jobCreatedAt);
            expect(result.eligible).toBe(false);
        });

        it("should return eligible for free users without recent offers", () => {
            const user = {
                membership_level: "FREE" as const,
                last_offer_shown_at: null,
            };
            const jobCreatedAt = new Date(Date.now() - 24 * 60 * 60 * 1000);

            const result = OfferUtils.isUserEligibleForAnnualOfferClientSide(user, jobCreatedAt);
            expect(result.eligible).toBe(true);
        });

        it("should return eligible for free users with recent offers", () => {
            const user = {
                membership_level: "FREE" as const,
                last_offer_shown_at: new Date(Date.now() - 24 * 60 * 60 * 1000),
            };
            const jobCreatedAt = new Date(Date.now() - 24 * 60 * 60 * 1000);

            const result = OfferUtils.isUserEligibleForAnnualOfferClientSide(user, jobCreatedAt);
            expect(result.eligible).toBe(true);
        });

        it("should return ineligible if jobCreatedAt is missing", () => {
            const user = {
                membership_level: "FREE" as const,
                last_offer_shown_at: null,
            };

            const result = OfferUtils.isUserEligibleForAnnualOfferClientSide(user, null);
            expect(result.eligible).toBe(false);
        });

        it("should return ineligible if offer window expired", () => {
            const user = {
                membership_level: "FREE" as const,
                last_offer_shown_at: null,
            };
            const jobCreatedAt = new Date(Date.now() - 73 * 60 * 60 * 1000); // 73 hours ago

            const result = OfferUtils.isUserEligibleForAnnualOfferClientSide(user, jobCreatedAt);
            expect(result.eligible).toBe(false);
        });
    });
}); 