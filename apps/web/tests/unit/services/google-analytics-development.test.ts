// Mock config for development mode before importing the service
jest.mock('@/config', () => ({
    __esModule: true,
    default: {
        isDevelopment: true
    }
}));

import { GoogleAnalyticsService } from '../../../lib/services/google-analytics.service';

// Mock window object for testing
const mockWindow = {
    dataLayer: [],
    gtag: jest.fn(),
    location: {
        hostname: 'localhost'
    }
};

Object.defineProperty(global, 'window', {
    value: mockWindow,
    writable: true
});

describe('GoogleAnalyticsService - Development Mode', () => {
    beforeEach(() => {
        // Clear mocks before each test
        jest.clearAllMocks();
        mockWindow.dataLayer = [];
        mockWindow.gtag = jest.fn();
    });

    describe('development mode tracking', () => {
        it('should skip client-side tracking in development mode', () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

            const mockData = {
                listingId: 'test-listing-id',
                listingName: 'Test Provider',
                category: 'rv-repair',
                userId: 'test-user-id',
                isAnonymous: false
            };

            GoogleAnalyticsService.trackLeadFormSubmission(mockData);

            expect(consoleSpy).toHaveBeenCalledWith(
                '[GA] Skipping event tracking in non-production: lead_form_submission',
                expect.any(Object)
            );
            expect(mockWindow.gtag).not.toHaveBeenCalled();

            consoleSpy.mockRestore();
        });

        it('should skip server-side tracking in development mode', async () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

            const mockData = {
                listingId: 'test-listing-id',
                listingName: 'Test Provider',
                category: 'rv-repair',
                userId: 'test-user-id',
                isAnonymous: false
            };

            await GoogleAnalyticsService.trackServerLeadFormSubmission(mockData);

            expect(consoleSpy).toHaveBeenCalledWith(
                '[GA] Skipping server-side event tracking in non-production: lead_form_submission',
                expect.any(Object)
            );

            consoleSpy.mockRestore();
        });
    });
}); 