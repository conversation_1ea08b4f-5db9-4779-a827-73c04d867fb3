import { processReferral } from "@/lib/queue/workers/referral-processing";
import { emailService } from "@/lib/services";
import { AuthService } from "@/lib/services/auth.service";
import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";
import { FirstPromoterService } from "@/lib/services/first-promoter.service";
import { UserDeviceService } from "@/lib/services/user-device.service";
import { mockPrisma } from "@/tests/mocks/prisma-mock";
import bcrypt from "bcryptjs";
import { cookies } from "next/headers";
import { NextRequest } from "next/server";

jest.mock("@/lib/services/emailNewsletter.service", () => ({
	EmailNewsletterService: {
		syncNewsletterSubscriber: jest.fn()
	}
}));

jest.mock("@/lib/services/first-promoter.service", () => ({
	FirstPromoterService: {
		trackSignup: jest.fn()
	}
}));

jest.mock("@/lib/services/user-device.service", () => ({
	UserDeviceService: {
		connectDeviceToUser: jest.fn()
	}
}));

jest.mock("@/lib/services", () => ({
	emailService: {
		sendVerificationEmail: jest.fn()
	}
}));

jest.mock("@/lib/queue/workers/referral-processing", () => ({
	processReferral: jest.fn()
}));

jest.mock("bcryptjs", () => ({
	hash: jest.fn(),
	compare: jest.fn()
}));

jest.mock("next/headers", () => ({
	cookies: jest.fn()
}));

jest.mock("@/lib/services/admin-log.service", () => ({
	adminLogger: {
		log: jest.fn()
	}
}));

describe("AuthService", () => {
	let authService: AuthService;
	let mockRequest: NextRequest;

	const mockUser = {
		id: "user123",
		email: "<EMAIL>",
		first_name: "John",
		last_name: "Doe",
		password: "hashedPassword",
		role: "USER",
		newsletter_subscribed: true,
		created_at: new Date(),
		updated_at: new Date(),
		email_verified_at: null,
		last_login: null
	};

	const mockVerificationToken = {
		id: "token123",
		token: "verification-token",
		expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
		user_id: "user123",
		type: "email_verification"
	};

	beforeEach(() => {
		authService = new AuthService();
		jest.clearAllMocks();

		// Setup default mocks
		(bcrypt.hash as jest.Mock).mockResolvedValue("hashedPassword");
		(mockPrisma.$transaction as jest.Mock).mockImplementation((callback) =>
			callback(mockPrisma)
		);
		(mockPrisma.user.findFirst as jest.Mock).mockResolvedValue(null);
		(mockPrisma.user.create as jest.Mock).mockResolvedValue(mockUser);
		(mockPrisma.userTermsAcceptance.create as jest.Mock).mockResolvedValue({});
		(mockPrisma.verificationToken.create as jest.Mock).mockResolvedValue(
			mockVerificationToken
		);
		(mockPrisma.userNotificationPreferences.create as jest.Mock).mockResolvedValue(
			{}
		);
		(emailService.sendVerificationEmail as jest.Mock).mockResolvedValue(true);
		(
			EmailNewsletterService.syncNewsletterSubscriber as jest.Mock
		).mockResolvedValue(true);
		(FirstPromoterService.trackSignup as jest.Mock).mockResolvedValue({
			success: true
		});
		(UserDeviceService.connectDeviceToUser as jest.Mock).mockResolvedValue(
			true
		);
		(processReferral as jest.Mock).mockResolvedValue(true);
		(cookies as jest.Mock).mockReturnValue({
			get: jest.fn().mockReturnValue(null),
			delete: jest.fn()
		});

		// Mock request object
		mockRequest = {
			headers: new Map([
				["x-real-ip", "***********"],
				["user-agent", "test-agent"]
			]),
			method: "POST",
			url: "https://example.com/api/auth/register"
		} as any;
	});

	describe("register", () => {
		const registerData = {
			email: "<EMAIL>",
			password: "password123",
			firstName: "Jane",
			lastName: "Smith",
			redirectUrl: "/dashboard"
		};

		it("should register a new user successfully", async () => {
			const result = await authService.register(registerData, mockRequest);

			expect(bcrypt.hash).toHaveBeenCalledWith(registerData.password, 10);
			expect(mockPrisma.user.findFirst).toHaveBeenCalledWith({
				where: {
					email: {
						equals: registerData.email,
						mode: "insensitive"
					}
				}
			});
			expect(mockPrisma.user.create).toHaveBeenCalledWith({
				data: {
					email: registerData.email,
					password: "hashedPassword",
					first_name: registerData.firstName,
					last_name: registerData.lastName,
					role: "USER",
					newsletter_subscribed: undefined,
					email_verified_at: null
				}
			});
			expect(result).toEqual(mockUser);
		});

		it("should throw error if user already exists", async () => {
			(mockPrisma.user.findFirst as jest.Mock).mockResolvedValue(mockUser);

			await expect(
				authService.register(registerData, mockRequest)
			).rejects.toThrow("Email already registered");
		});

		it("should sync newsletter subscriber with correct tags", async () => {
			await authService.register(registerData, mockRequest);

			expect(
				EmailNewsletterService.syncNewsletterSubscriber
			).toHaveBeenCalledWith({
				email: mockUser.email,
				first_name: mockUser.first_name,
				last_name: mockUser.last_name,
				user: mockUser,
				tags: [
					"Consumer Action: Created Account",
					"Source: Created Account via Website"
				]
			});
		});

		it("should track signup with FirstPromoter", async () => {
			await authService.register(registerData, mockRequest);

			expect(FirstPromoterService.trackSignup).toHaveBeenCalledWith({
				email: mockUser.email,
				userId: mockUser.id,
				firstName: mockUser.first_name,
				lastName: mockUser.last_name,
				req: mockRequest
			});
		});

		it("should still register user even if FirstPromoter tracking fails", async () => {
			(FirstPromoterService.trackSignup as jest.Mock).mockRejectedValue(
				new Error("FirstPromoter failed")
			);

			const result = await authService.register(registerData, mockRequest);

			expect(result).toEqual(mockUser);
			expect(FirstPromoterService.trackSignup).toHaveBeenCalled();
		});

		it("should still register user even if newsletter sync fails", async () => {
			(
				EmailNewsletterService.syncNewsletterSubscriber as jest.Mock
			).mockRejectedValue(new Error("Newsletter sync failed"));

			const result = await authService.register(registerData, mockRequest);

			expect(result).toEqual(mockUser);
			expect(
				EmailNewsletterService.syncNewsletterSubscriber
			).toHaveBeenCalled();
		});

		it("should handle device token connection for mobile users", async () => {
			const mobileData = {
				...registerData,
				deviceToken: "device-token-123",
				platform: "ios" as const
			};

			await authService.register(mobileData, mockRequest);

			expect(UserDeviceService.connectDeviceToUser).toHaveBeenCalledWith({
				deviceToken: "device-token-123",
				userId: mockUser.id,
				platform: "ios"
			});
		});

		it("should process referral code if present in cookies", async () => {
			const mockCookies = {
				get: jest.fn().mockReturnValue({ value: "referral-code-123" }),
				delete: jest.fn()
			};
			(cookies as jest.Mock).mockReturnValue(mockCookies);

			await authService.register(registerData, mockRequest);

			expect(processReferral).toHaveBeenCalledWith({
				referralCode: "referral-code-123",
				userId: mockUser.id
			});
			expect(mockCookies.delete).toHaveBeenCalledWith("referral_code");
		});

		it("should send verification email", async () => {
			await authService.register(registerData, mockRequest);

			expect(emailService.sendVerificationEmail).toHaveBeenCalledWith(
				mockUser.email,
				mockVerificationToken,
				false
			);
		});

		it("should handle mobile registration", async () => {
			await authService.register(registerData, mockRequest, true);

			expect(emailService.sendVerificationEmail).toHaveBeenCalledWith(
				mockUser.email,
				mockVerificationToken,
				true
			);
		});

		it("should create user terms acceptance and verification token", async () => {
			await authService.register(registerData, mockRequest);

			expect(mockPrisma.userTermsAcceptance.create).toHaveBeenCalledWith({
				data: {
					user_id: mockUser.id,
					document: "terms_and_conditions",
					version: "1.0.0",
					ip_address: "***********"
				}
			});

			expect(mockPrisma.verificationToken.create).toHaveBeenCalledWith({
				data: {
					token: expect.any(String),
					expires: expect.any(Date),
					user_id: mockUser.id,
					type: "email_verification",
					redirect_url: registerData.redirectUrl
				}
			});
		});

		it("should create notification preferences asynchronously", async () => {
			await authService.register(registerData, mockRequest);

			expect(mockPrisma.userNotificationPreferences.create).toHaveBeenCalledWith({
				data: {
					user_id: mockUser.id,
					messages: { email: true, sms: false },
					requests: { email: true, sms: false },
					quotes: { email: true, sms: false }
				}
			});
		});

		it("should sync newsletter with mobile tags for mobile registration", async () => {
			const mobileData = {
				...registerData,
				deviceToken: "test-token",
				platform: "ios" as const
			};

			await authService.register(mobileData, mockRequest, true);

			expect(
				EmailNewsletterService.syncNewsletterSubscriber
			).toHaveBeenCalledWith({
				email: mockUser.email,
				first_name: mockUser.first_name,
				last_name: mockUser.last_name,
				user: mockUser,
				tags: [
					"Consumer Action: Created Account",
					"Consumer Action: Logged in to Mobile App",
					"Source: Created Account via Mobile App"
				]
			});
		});

		it("should sync newsletter with custom source tags for OEM registration", async () => {
			const oemData = {
				...registerData,
				newsletterSourceTag: "Added by Keystone",
				emailVerified: true
			};

			await authService.register(oemData, mockRequest, false);

			expect(
				EmailNewsletterService.syncNewsletterSubscriber
			).toHaveBeenCalledWith({
				email: mockUser.email,
				first_name: mockUser.first_name,
				last_name: mockUser.last_name,
				user: mockUser,
				tags: [
					"Consumer Action: Created Account",
					"Source: Added by Keystone"
				]
			});
		});
	});
});
