import prisma from "@/lib/prisma";
import { FraudDetectionService } from "@/lib/services/fraud-detection.service";

// Mock the prisma client
jest.mock("@/lib/prisma", () => ({
    job: {
        count: jest.fn(),
        update: jest.fn(),
        updateMany: jest.fn(),
        findMany: jest.fn(),
        groupBy: jest.fn()
    },
    user: {
        findUnique: jest.fn()
    },
    blacklistEntry: {
        create: jest.fn()
    }
}));

// Mock the slack service
jest.mock("@/lib/services", () => ({
    slackService: {
        sendToJosiahMann: jest.fn()
    },
    emailService: {
        send: jest.fn()
    }
}));


describe("FraudDetectionService", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("checkJobSubmission", () => {
        it("should detect suspicious activity when user submits multiple jobs", async () => {
            const mockPrisma = prisma as jest.Mocked<typeof prisma>;

            // Mock that user has submitted 5 jobs today
            mockPrisma.job.count
                .mockResolvedValueOnce(5) // jobsToday
                .mockResolvedValueOnce(2); // similarMessages

            const result = await FraudDetectionService.checkJobSubmission(
                "user123",
                "<EMAIL>",
                "I need urgent RV repair"
            );

            expect(result.isSuspicious).toBe(true);
            expect(result.riskLevel).toBe("MEDIUM");
            expect(result.details.jobsToday).toBe(5);
            expect(result.reason).toContain("User submitted 5 jobs today");
        });

        it("should detect high risk when user submits 6 or more jobs", async () => {
            const mockPrisma = prisma as jest.Mocked<typeof prisma>;

            // Mock that user has submitted 6 jobs today
            mockPrisma.job.count
                .mockResolvedValueOnce(6) // jobsToday
                .mockResolvedValueOnce(1); // similarMessages

            const result = await FraudDetectionService.checkJobSubmission(
                "user123",
                "<EMAIL>",
                "I need urgent RV repair"
            );

            expect(result.isSuspicious).toBe(true);
            expect(result.riskLevel).toBe("HIGH");
            expect(result.details.jobsToday).toBe(6);
            expect(result.reason).toContain("User submitted 6 jobs today");
        });

        it("should detect suspicious keywords in message", async () => {
            const mockPrisma = prisma as jest.Mocked<typeof prisma>;

            mockPrisma.job.count
                .mockResolvedValueOnce(1) // jobsToday
                .mockResolvedValueOnce(1); // similarMessages

            const result = await FraudDetectionService.checkJobSubmission(
                "user123",
                "<EMAIL>",
                "I need urgent cash only repair under the table no receipt"
            );

            expect(result.isSuspicious).toBe(true);
            expect(result.riskLevel).toBe("MEDIUM");
            expect(result.reason).toContain("Message contains suspicious keywords");
        });

        it("should not flag legitimate single job submission", async () => {
            const mockPrisma = prisma as jest.Mocked<typeof prisma>;

            mockPrisma.job.count
                .mockResolvedValueOnce(1) // jobsToday
                .mockResolvedValueOnce(1); // similarMessages

            const result = await FraudDetectionService.checkJobSubmission(
                "user123",
                "<EMAIL>",
                "I need help with my RV electrical system. The lights are not working properly."
            );

            expect(result.isSuspicious).toBe(false);
            expect(result.riskLevel).toBe("LOW");
        });

        it("should detect very suspicious phrases and set HIGH risk", async () => {
            const mockPrisma = prisma as jest.Mocked<typeof prisma>;

            mockPrisma.job.count
                .mockResolvedValueOnce(1) // jobsToday
                .mockResolvedValueOnce(1); // similarMessages

            const result = await FraudDetectionService.checkJobSubmission(
                "user123",
                "<EMAIL>",
                "I understand you're a Master RVTI-certified tech and I need help"
            );

            expect(result.isSuspicious).toBe(true);
            expect(result.riskLevel).toBe("HIGH");
            expect(result.reason).toContain("Message contains very suspicious phrases");
            expect(result.details.suspiciousPatterns).toContain("Message contains very suspicious phrases: master rvti");
        });
    });

    describe("getFraudStats", () => {
        it("should return fraud statistics", async () => {
            const mockPrisma = prisma as jest.Mocked<typeof prisma>;

            mockPrisma.job.count
                .mockResolvedValueOnce(3) // flaggedJobsToday
                .mockResolvedValueOnce(15); // totalFlaggedJobs

            mockPrisma.job.groupBy.mockResolvedValueOnce([
                { user_id: "user1", _count: { id: 5 } },
                { user_id: "user2", _count: { id: 4 } }
            ]);

            const stats = await FraudDetectionService.getFraudStats();

            expect(stats.flaggedJobsToday).toBe(3);
            expect(stats.totalFlaggedJobs).toBe(15);
            expect(stats.suspiciousUsersToday).toBe(2);
        });
    });
}); 