import { QuoteStatus } from "@rvhelp/database";

import { filterLeadsByStatus } from "../../../lib/utils/quote";

describe("Provider Leads Filter Logic", () => {
    const mockLeads = [
        {
            id: "1",
            status: QuoteStatus.PENDING,
            type: "quote"
        },
        {
            id: "2",
            status: QuoteStatus.ACCEPTED,
            type: "quote"
        },
        {
            id: "3",
            status: QuoteStatus.REJECTED,
            type: "quote"
        },
        {
            id: "4",
            status: QuoteStatus.CUSTOMER_REJECTED,
            type: "quote"
        },
        {
            id: "5",
            status: QuoteStatus.COMPLETED,
            type: "quote"
        },
        {
            id: "6",
            status: "pending",
            type: "troubleshooting"
        },
        {
            id: "7",
            status: "accepted",
            type: "troubleshooting"
        }
    ];

    describe("needs_action filter", () => {
        it("should include pending quotes and troubleshooting requests", () => {
            const filtered = filterLeadsByStatus(mockLeads, "needs_action");
            const ids = filtered.map(lead => lead.id);

            expect(ids).toContain("1"); // PENDING quote
            expect(ids).toContain("6"); // pending troubleshooting
            expect(ids).toContain("7"); // accepted troubleshooting
        });

        it("should exclude completed, rejected, and customer rejected leads", () => {
            const filtered = filterLeadsByStatus(mockLeads, "needs_action");
            const ids = filtered.map(lead => lead.id);

            expect(ids).not.toContain("3"); // REJECTED quote
            expect(ids).not.toContain("4"); // CUSTOMER_REJECTED quote
            expect(ids).not.toContain("5"); // COMPLETED quote
        });

        it("DEBUG: should show exactly what's in needs_action", () => {
            const filtered = filterLeadsByStatus(mockLeads, "needs_action");
            const ids = filtered.map(lead => lead.id);
            console.log("needs_action filtered IDs:", ids);
            console.log("needs_action filtered leads:", filtered.map(lead => ({ id: lead.id, status: lead.status, type: lead.type })));
        });
    });

    describe("proposal_declined filter", () => {
        it("should only include customer rejected quotes", () => {
            const filtered = filterLeadsByStatus(mockLeads, "proposal_declined");
            const ids = filtered.map(lead => lead.id);

            expect(ids).toContain("4"); // CUSTOMER_REJECTED quote
            expect(ids).toHaveLength(1);
        });

        it("should exclude all other statuses", () => {
            const filtered = filterLeadsByStatus(mockLeads, "proposal_declined");
            const ids = filtered.map(lead => lead.id);

            expect(ids).not.toContain("1"); // PENDING
            expect(ids).not.toContain("2"); // ACCEPTED
            expect(ids).not.toContain("3"); // REJECTED
            expect(ids).not.toContain("5"); // COMPLETED
            expect(ids).not.toContain("6"); // pending troubleshooting
            expect(ids).not.toContain("7"); // accepted troubleshooting
        });
    });

    describe("rejected filter", () => {
        it("should only include provider rejected quotes", () => {
            const filtered = filterLeadsByStatus(mockLeads, "rejected");
            const ids = filtered.map(lead => lead.id);

            expect(ids).toContain("3"); // REJECTED quote
            expect(ids).toHaveLength(1);
        });

        it("should not include customer rejected quotes", () => {
            const filtered = filterLeadsByStatus(mockLeads, "rejected");
            const ids = filtered.map(lead => lead.id);

            expect(ids).not.toContain("4"); // CUSTOMER_REJECTED quote
        });
    });

    describe("all filter", () => {
        it("should include all leads", () => {
            const filtered = filterLeadsByStatus(mockLeads, "all");
            expect(filtered).toHaveLength(mockLeads.length);
        });
    });
}); 