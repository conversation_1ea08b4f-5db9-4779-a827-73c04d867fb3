import { smsService } from "@/lib/services/sms.service";

// Mock the SMS service
jest.mock("@/lib/services/sms.service", () => ({
    smsService: {
        send: jest.fn().mockResolvedValue({ sid: "test_message_sid", status: "sent" }),
        sendToProvider: jest.fn().mockResolvedValue({ sid: "test_message_sid", status: "sent" }),
        sendToUser: jest.fn().mockResolvedValue({ sid: "test_message_sid", status: "sent" })
    }
}));

describe("SMSService", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("send", () => {
        it("should send SMS successfully", async () => {
            const result = await smsService.send("+**********", "Test message");

            expect(smsService.send).toHaveBeenCalledWith("+**********", "Test message");
            expect(result).toEqual({
                sid: "test_message_sid",
                status: "sent"
            });
        });
    });

    describe("sendToProvider", () => {
        it("should send SMS using provider phone number", async () => {
            const result = await smsService.sendToProvider("+**********", "Test provider message");

            expect(smsService.sendToProvider).toHaveBeenCalledWith("+**********", "Test provider message");
            expect(result).toEqual({
                sid: "test_message_sid",
                status: "sent"
            });
        });
    });

    describe("sendToUser", () => {
        it("should send SMS using user phone number", async () => {
            const result = await smsService.sendToUser("+**********", "Test user message");

            expect(smsService.sendToUser).toHaveBeenCalledWith("+**********", "Test user message");
            expect(result).toEqual({
                sid: "test_message_sid",
                status: "sent"
            });
        });
    });

    describe("error handling", () => {
        it("should handle SMS service errors gracefully", async () => {
            // Mock a failure
            (smsService.sendToProvider as jest.Mock).mockRejectedValueOnce(new Error("SMS service not configured"));

            await expect(smsService.sendToProvider("+**********", "Test")).rejects.toThrow("SMS service not configured");
        });
    });
});
