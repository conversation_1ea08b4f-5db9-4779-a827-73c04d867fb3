import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { smsService } from "@/lib/services/sms.service";
import { processSmsNotification } from "../../../lib/queue/workers/sms-notification";

// Mock the services
jest.mock("@/lib/services/sms.service", () => ({
    smsService: {
        send: jest.fn().mockResolvedValue(undefined),
        sendToProvider: jest.fn().mockResolvedValue(undefined),
        sendToUser: jest.fn().mockResolvedValue(undefined)
    }
}));
jest.mock("@/lib/services");
jest.mock("@/lib/prisma", () => ({
    listing: {
        findUnique: jest.fn(),
        update: jest.fn(),
    },
}));
jest.mock("@/config", () => ({
    isDevelopment: false,
}));

const mockSmsService = smsService as jest.Mocked<typeof smsService>;
const mockEmailService = emailService as jest.Mocked<typeof emailService>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe("SMS Notification Worker", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    const mockPayload = {
        listingId: "listing-123",
        phoneNumber: "+**********",
        message: "Test SMS message",
        jobId: "job-123",
        retryCount: 0,
    };

    const mockListing = {
        id: "listing-123",
        business_name: "Test Business",
        first_name: "John",
        last_name: "Doe",
        phone: "+**********",
        email: "<EMAIL>",
        notification_email: "<EMAIL>",
        owner: {
            first_name: "John",
            last_name: "Doe",
        },
    };

    describe("processSmsNotification", () => {
        it("should send SMS successfully", async () => {
            mockSmsService.sendToProvider.mockResolvedValue({ success: true } as any);

            const result = await processSmsNotification(mockPayload);

            expect(mockSmsService.sendToProvider).toHaveBeenCalledWith(
                mockPayload.phoneNumber,
                mockPayload.message
            );
            expect(result).toEqual({ success: true });
        });

        it("should handle permanent SMS failures and remove verification", async () => {
            const permanentError = new Error("Invalid phone number");
            (permanentError as any).code = 21606; // Invalid phone number error
            mockSmsService.sendToProvider.mockRejectedValue(permanentError);
            mockPrisma.listing.findUnique.mockResolvedValue(mockListing as any);
            mockPrisma.listing.update.mockResolvedValue(mockListing as any);
            mockEmailService.send.mockResolvedValue({ success: true } as any);

            const result = await processSmsNotification(mockPayload);

            expect(mockPrisma.listing.update).toHaveBeenCalledWith({
                where: { id: mockPayload.listingId },
                data: { sms_verified_at: null },
            });

            expect(mockEmailService.send).toHaveBeenCalledWith({
                to: mockListing.notification_email,
                subject: "SMS Verification Removed - Action Required",
                react: expect.any(Object),
            });

            expect(result).toEqual({ success: false, permanent: true });
        });

        it("should handle temporary failures and retry", async () => {
            const temporaryError = new Error("Temporary failure");
            (temporaryError as any).code = 30000; // Some temporary error code
            mockSmsService.sendToProvider.mockRejectedValue(temporaryError);

            await expect(processSmsNotification(mockPayload)).rejects.toThrow("Temporary failure");

            expect(mockPrisma.listing.update).not.toHaveBeenCalled();
            expect(mockEmailService.send).not.toHaveBeenCalled();
        });

        it("should handle max retries reached", async () => {
            const temporaryError = new Error("Temporary failure");
            (temporaryError as any).code = 30000;
            mockSmsService.sendToProvider.mockRejectedValue(temporaryError);
            mockPrisma.listing.findUnique.mockResolvedValue(mockListing as any);
            mockPrisma.listing.update.mockResolvedValue(mockListing as any);
            mockEmailService.send.mockResolvedValue({ success: true } as any);

            const payloadWithMaxRetries = { ...mockPayload, retryCount: 2 };

            const result = await processSmsNotification(payloadWithMaxRetries);

            expect(mockPrisma.listing.update).toHaveBeenCalledWith({
                where: { id: mockPayload.listingId },
                data: { sms_verified_at: null },
            });

            expect(result).toEqual({ success: false, permanent: true });
        });

        it("should handle missing listing gracefully", async () => {
            const permanentError = new Error("Invalid phone number");
            (permanentError as any).code = 21606;
            mockSmsService.sendToProvider.mockRejectedValue(permanentError);
            mockPrisma.listing.findUnique.mockResolvedValue(null);

            const result = await processSmsNotification(mockPayload);

            expect(mockPrisma.listing.update).not.toHaveBeenCalled();
            expect(mockEmailService.send).not.toHaveBeenCalled();
            expect(result).toEqual({ success: false, permanent: true });
        });
    });
});
