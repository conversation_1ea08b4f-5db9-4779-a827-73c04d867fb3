import { queueMessage } from "@/lib/queue/qstash";
import { emailService } from "@/lib/services";
import { JobLifecycleService, jobLifecycleService } from "@/lib/services/job-lifecycle.service";
import { smsService } from "@/lib/services/sms.service";

// Mock dependencies
jest.mock("@/lib/services", () => ({
	emailService: {
		send: jest.fn(),
		batchSend: jest.fn(),
	},
}));

jest.mock("@/lib/services/sms.service", () => ({
	smsService: {
		send: jest.fn(),
	},
}));

jest.mock("@/lib/queue/qstash", () => ({
	queueMessage: jest.fn(),
}));

jest.mock("@/lib/prisma", () => ({
	__esModule: true,
	default: {
		quote: {
			findMany: jest.fn(),
			update: jest.fn(),
		},
		job: {
			findMany: jest.fn(),
			update: jest.fn(),
			updateMany: jest.fn(),
		},
	},
}));

jest.mock("@/lib/utils/business-days", () => ({
	isBusinessDay: jest.fn(() => true),
}));

describe("JobLifecycleService", () => {
	const mockQuote = {
		id: "quote123",
		listing_id: "listing123",
		status: "CUSTOMER_ACCEPTED",
		listing: {
			id: "listing123",
			business_name: "Test Business",
			first_name: "Jane",
			last_name: "Smith",
			email: "<EMAIL>",
			phone: "+**********",
		},
	};

	const mockJob = {
		id: "job123",
		user_id: "user123",
		category: "rv-repair",
		message: "Need help with my RV",
		user: {
			id: "user123",
			first_name: "John",
			last_name: "Doe",
			email: "<EMAIL>",
			phone: "+**********",
		},
	};

	const mockMessage = {
		id: "message123",
		quote_id: "quote123",
		user_id: "user123",
		content: "Thanks for your response!",
		sender_type: "USER",
		type: "TEXT",
		quote: {
			...mockQuote,
			job: mockJob,
		},
	};

	beforeEach(() => {
		jest.clearAllMocks();
		// Reset queueMessage to default success behavior
		(queueMessage as jest.Mock).mockResolvedValue(true);
	});


	describe("sendNewMessageNotification", () => {
		it("should queue new message notification", async () => {
			await jobLifecycleService.sendNewMessageNotification(mockMessage as any);

			expect(queueMessage).toHaveBeenCalledWith({
				type: "new-message",
				payload: {
					message: mockMessage,
				},
			});
		});

		it("should handle complex message types", async () => {
			const complexMessage = {
				...mockMessage,
				type: "IMAGE",
				attachments: ["image1.jpg", "image2.png"],
				metadata: { size: "large", quality: "high" },
			};

			await jobLifecycleService.sendNewMessageNotification(complexMessage as any);

			expect(queueMessage).toHaveBeenCalledWith({
				type: "new-message",
				payload: {
					message: complexMessage,
				},
			});
		});
	});

	describe("JobLifecycleService class instantiation", () => {
		it("should create instance with injected dependencies", () => {
			const service = new JobLifecycleService(emailService, smsService);

			expect(service).toBeDefined();
			expect(service.sendNewMessageNotification).toBeDefined();
		});

		it("should use provided email and SMS services", async () => {
			const customEmailService = { send: jest.fn() };
			const customSmsService = { send: jest.fn() };

			const service = new JobLifecycleService(
				customEmailService as any,
				customSmsService as any
			);

			// Test that the service uses the injected dependencies
			expect(service).toBeDefined();
		});
	});

	describe("exported singleton instance", () => {
		it("should export a configured singleton instance", () => {
			expect(jobLifecycleService).toBeDefined();
			expect(jobLifecycleService).toBeInstanceOf(JobLifecycleService);
		});

		it("should use the global email and SMS services", () => {
			// Test that the exported instance is properly configured
			expect(jobLifecycleService.sendNewMessageNotification).toBeDefined();
		});
	});

	describe("integration scenarios", () => {
		it("should handle multiple rapid notifications", async () => {
			const promises = [
				jobLifecycleService.sendNewMessageNotification(mockMessage as any),
				jobLifecycleService.sendNewMessageNotification(mockMessage as any),
				jobLifecycleService.sendNewMessageNotification(mockMessage as any),
			];

			await Promise.all(promises);

			expect(queueMessage).toHaveBeenNthCalledWith(1, {
				type: "new-message",
				payload: { message: mockMessage },
			});


		});

		it("should handle partial queue failures", async () => {
			(queueMessage as jest.Mock)
				.mockResolvedValueOnce(true) // First call succeeds
				.mockRejectedValueOnce(new Error("Queue full")) // Second call fails
				.mockResolvedValueOnce(true); // Third call succeeds

			// Third notification should succeed
			await expect(
				jobLifecycleService.sendNewMessageNotification(mockMessage as any)
			).resolves.not.toThrow();
		});
	});

	describe("send24HourProviderReminders", () => {
		// it("should handle batch email timeout by falling back to individual emails", async () => {
		// 	// Mock data for quotes that need reminders
		// 	const mockQuotes = [
		// 		{
		// 			id: "quote1",
		// 			status: QuoteStatus.PENDING,
		// 			reminder_sent_at: null,
		// 			invited_at: new Date(Date.now() - 36 * 60 * 60 * 1000), // 36 hours ago
		// 			messages: [],
		// 			listing: {
		// 				id: "listing1",
		// 				business_name: "Test Business",
		// 				first_name: "John",
		// 				last_name: "Provider",
		// 				email: "<EMAIL>",
		// 				phone: "+**********",
		// 			},
		// 			job: {
		// 				id: "job1",
		// 				first_name: "Customer",
		// 				last_name: "User",
		// 				email: "<EMAIL>",
		// 				phone: "+**********",
		// 				contact_preference: "email",
		// 				message: "Need RV repair",
		// 				category: "rv-repair",
		// 				location: {
		// 					address: "123 Main St",
		// 					latitude: 40.7128,
		// 					longitude: -74.0060,
		// 				},
		// 				user: {
		// 					id: "user1",
		// 					first_name: "Customer",
		// 					last_name: "User",
		// 					email: "<EMAIL>",
		// 				},
		// 			},
		// 		},
		// 		{
		// 			id: "quote2",
		// 			status: QuoteStatus.PENDING,
		// 			reminder_sent_at: null,
		// 			invited_at: new Date(Date.now() - 30 * 60 * 60 * 1000), // 30 hours ago
		// 			messages: [],
		// 			listing: {
		// 				id: "listing2",
		// 				business_name: "Another Business",
		// 				first_name: "Jane",
		// 				last_name: "Provider",
		// 				email: "<EMAIL>",
		// 				phone: "+**********",
		// 			},
		// 			job: {
		// 				id: "job2",
		// 				first_name: "Customer",
		// 				last_name: "User",
		// 				email: "<EMAIL>",
		// 				phone: "+**********",
		// 				contact_preference: "email",
		// 				message: "Need RV repair",
		// 				category: "rv-repair",
		// 				location: {
		// 					address: "456 Oak St",
		// 					latitude: 40.7128,
		// 					longitude: -74.0060,
		// 				},
		// 				user: {
		// 					id: "user1",
		// 					first_name: "Customer",
		// 					last_name: "User",
		// 					email: "<EMAIL>",
		// 				},
		// 			},
		// 		},
		// 	];

		// 	// Mock prisma to return quotes
		// 	(prisma.quote.findMany as jest.Mock).mockResolvedValue(mockQuotes);

		// 	// Mock batchSend to timeout
		// 	(emailService.batchSend as jest.Mock).mockImplementation(() => {
		// 		return new Promise((resolve) => {
		// 			// Simulate a timeout by never resolving
		// 			setTimeout(() => {
		// 				resolve({
		// 					success: false,
		// 					results: [],
		// 					errors: ["Batch email timeout"]
		// 				});
		// 			}, 30000); // 30 second timeout
		// 		});
		// 	});

		// 	// Mock individual email send to succeed
		// 	(emailService.send as jest.Mock).mockResolvedValue({ success: true });

		// 	// Mock SMS service
		// 	(smsService.sendToProvider as jest.Mock).mockResolvedValue(true);

		// 	// Mock quote update
		// 	(prisma.quote.update as jest.Mock).mockResolvedValue({});

		// 	// This should now work by falling back to individual emails
		// 	await jobLifecycleService.send24HourProviderReminders();

		// 	// Verify that batchSend was called
		// 	expect(emailService.batchSend).toHaveBeenCalled();

		// 	// Verify that individual email sends were called as fallback
		// 	expect(emailService.send).toHaveBeenCalledTimes(2);

		// 	// Verify that quote updates were called for both quotes
		// 	expect(prisma.quote.update).toHaveBeenCalledTimes(2);
		// 	expect(prisma.quote.update).toHaveBeenCalledWith({
		// 		where: { id: "quote1" },
		// 		data: {
		// 			reminder_sent_at: expect.any(Date)
		// 		}
		// 	});
		// 	expect(prisma.quote.update).toHaveBeenCalledWith({
		// 		where: { id: "quote2" },
		// 		data: {
		// 			reminder_sent_at: expect.any(Date)
		// 		}
		// 	});

		// 	// This demonstrates the fix: reminder_sent_at is now set even when batch fails
		// }, 35000); // Increase timeout for this test

		// it("should handle batch email partial failures correctly", async () => {
		// 	// Mock data for quotes
		// 	const mockQuotes = [
		// 		{
		// 			id: "quote1",
		// 			status: QuoteStatus.PENDING,
		// 			reminder_sent_at: null,
		// 			invited_at: new Date(Date.now() - 36 * 60 * 60 * 1000),
		// 			messages: [],
		// 			listing: {
		// 				id: "listing1",
		// 				business_name: "Test Business",
		// 				first_name: "John",
		// 				last_name: "Provider",
		// 				email: "<EMAIL>",
		// 				phone: "+**********",
		// 			},
		// 			job: {
		// 				id: "job1",
		// 				first_name: "Customer",
		// 				last_name: "User",
		// 				email: "<EMAIL>",
		// 				phone: "+**********",
		// 				contact_preference: "email",
		// 				message: "Need RV repair",
		// 				category: "rv-repair",
		// 				location: {
		// 					address: "123 Main St",
		// 					latitude: 40.7128,
		// 					longitude: -74.0060,
		// 				},
		// 				user: {
		// 					id: "user1",
		// 					first_name: "Customer",
		// 					last_name: "User",
		// 					email: "<EMAIL>",
		// 				},
		// 			},
		// 		},
		// 	];

		// 	// Mock prisma to return quotes
		// 	(prisma.quote.findMany as jest.Mock).mockResolvedValue(mockQuotes);

		// 	// Mock batchSend to return partial success
		// 	(emailService.batchSend as jest.Mock).mockResolvedValue({
		// 		success: false,
		// 		results: [{}], // One successful result
		// 		errors: ["Failed to send <NAME_EMAIL>"] // One error
		// 	});

		// 	// Mock SMS service
		// 	(smsService.sendToProvider as jest.Mock).mockResolvedValue(true);

		// 	// Mock quote update
		// 	(prisma.quote.update as jest.Mock).mockResolvedValue({});

		// 	// This should work but only update the successful quote
		// 	await jobLifecycleService.send24HourProviderReminders();

		// 	// Verify that quote update was called for the successful email
		// 	expect(prisma.quote.update).toHaveBeenCalledWith({
		// 		where: { id: "quote1" },
		// 		data: {
		// 			reminder_sent_at: expect.any(Date)
		// 		}
		// 	});
		// });

		// it("should handle individual email failures gracefully", async () => {
		// 	// Mock data for quotes
		// 	const mockQuotes = [
		// 		{
		// 			id: "quote1",
		// 			status: QuoteStatus.PENDING,
		// 			reminder_sent_at: null,
		// 			invited_at: new Date(Date.now() - 36 * 60 * 60 * 1000),
		// 			messages: [],
		// 			listing: {
		// 				id: "listing1",
		// 				business_name: "Test Business",
		// 				first_name: "John",
		// 				last_name: "Provider",
		// 				email: "<EMAIL>",
		// 				phone: "+**********",
		// 			},
		// 			job: {
		// 				id: "job1",
		// 				first_name: "Customer",
		// 				last_name: "User",
		// 				email: "<EMAIL>",
		// 				phone: "+**********",
		// 				contact_preference: "email",
		// 				message: "Need RV repair",
		// 				category: "rv-repair",
		// 				location: {
		// 					address: "123 Main St",
		// 					latitude: 40.7128,
		// 					longitude: -74.0060,
		// 				},
		// 				user: {
		// 					id: "user1",
		// 					first_name: "Customer",
		// 					last_name: "User",
		// 					email: "<EMAIL>",
		// 				},
		// 			},
		// 		},
		// 	];

		// 	// Mock prisma to return quotes
		// 	(prisma.quote.findMany as jest.Mock).mockResolvedValue(mockQuotes);

		// 	// Mock batchSend to timeout
		// 	(emailService.batchSend as jest.Mock).mockImplementation(() => {
		// 		return new Promise((resolve) => {
		// 			setTimeout(() => {
		// 				resolve({
		// 					success: false,
		// 					results: [],
		// 					errors: ["Batch email timeout"]
		// 				});
		// 			}, 30000);
		// 		});
		// 	});

		// 	// Mock individual email send to fail
		// 	(emailService.send as jest.Mock).mockRejectedValue(new Error("Email service error"));

		// 	// Mock SMS service
		// 	(smsService.sendToProvider as jest.Mock).mockResolvedValue(true);

		// 	// Mock quote update
		// 	(prisma.quote.update as jest.Mock).mockResolvedValue({});

		// 	// This should handle the failure gracefully
		// 	await jobLifecycleService.send24HourProviderReminders();

		// 	// Verify that batchSend was called
		// 	expect(emailService.batchSend).toHaveBeenCalled();

		// 	// Verify that individual email send was attempted
		// 	expect(emailService.send).toHaveBeenCalled();

		// 	// Verify that quote update was NOT called because email failed
		// 	expect(prisma.quote.update).not.toHaveBeenCalled();
		// }, 35000);
	});
}); 