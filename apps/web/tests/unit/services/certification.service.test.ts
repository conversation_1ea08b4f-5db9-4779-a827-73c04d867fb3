
import { CertificationService } from "@/lib/services/certification.service";
import { mockPrisma } from "../../mocks/prisma-mock";


describe("CertificationService", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("getActiveCertifications", () => {
        it("should return active certifications", async () => {
            const mockCertifications = [
                {
                    id: "1",
                    name: "keystone-warranty",
                    display_name: "Keystone Warranty Certification",
                    is_active: true,
                },
            ];

            (mockPrisma.providerCertification.findMany as jest.Mock).mockResolvedValue(mockCertifications);

            const result = await CertificationService.getActiveCertifications();

            expect(result).toEqual(mockCertifications);
            expect(mockPrisma.providerCertification.findMany).toHaveBeenCalledWith({
                where: { is_active: true },
                orderBy: { created_at: "desc" },
            });
        });
    });

    describe("getCertificationByName", () => {
        it("should return certification by name", async () => {
            const mockCertification = {
                id: "1",
                name: "keystone-warranty",
                display_name: "Keystone Warranty Certification",
            };

            (mockPrisma.providerCertification.findUnique as jest.Mock).mockResolvedValue(mockCertification);

            const result = await CertificationService.getCertificationByName("keystone-warranty");

            expect(result).toEqual(mockCertification);
            expect(mockPrisma.providerCertification.findUnique).toHaveBeenCalledWith({
                where: { name: "keystone-warranty" },
            });
        });

        it("should return null if certification not found", async () => {
            (mockPrisma.providerCertification.findUnique as jest.Mock).mockResolvedValue(null);

            const result = await CertificationService.getCertificationByName("non-existent");

            expect(result).toBeNull();
        });
    });

    describe("getProviderCertificationStatus", () => {
        it("should return provider certification status", async () => {
            const mockCertification = {
                id: "1",
                name: "keystone-warranty",
            };

            const mockRecord = {
                id: "record-1",
                status: "COMPLETED",
                certification: mockCertification,
            };

            (mockPrisma.providerCertification.findUnique as jest.Mock).mockResolvedValue(mockCertification);
            (mockPrisma.providerCertificationRecord.findFirst as jest.Mock).mockResolvedValue(mockRecord);

            const result = await CertificationService.getProviderCertificationStatus(
                "listing-1",
                "keystone-warranty"
            );

            expect(result).toEqual(mockRecord);
        });

        it("should return null if certification not found", async () => {
            (mockPrisma.providerCertification.findUnique as jest.Mock).mockResolvedValue(null);

            const result = await CertificationService.getProviderCertificationStatus(
                "listing-1",
                "non-existent"
            );

            expect(result).toBeNull();
        });
    });

    describe("startCertification", () => {
        it("should start certification process", async () => {
            const mockCertification = {
                id: "1",
                name: "keystone-warranty",
            };

            const mockRecord = {
                id: "record-1",
                status: "IN_PROGRESS",
                certification: mockCertification,
            };

            (mockPrisma.providerCertification.findUnique as jest.Mock).mockResolvedValue(mockCertification);
            (mockPrisma.providerCertificationRecord.findFirst as jest.Mock).mockResolvedValue(null);
            (mockPrisma.providerCertificationRecord.create as jest.Mock).mockResolvedValue(mockRecord);

            const result = await CertificationService.startCertification(
                "listing-1",
                "keystone-warranty",
                "user-1"
            );

            expect(result).toEqual(mockRecord);
            expect(mockPrisma.providerCertificationRecord.create).toHaveBeenCalledWith({
                data: {
                    listing_id: "listing-1",
                    certification_id: "1",
                    user_id: "user-1",
                    status: "IN_PROGRESS",
                },
                include: {
                    certification: true,
                    user: {
                        select: {
                            id: true,
                            first_name: true,
                            last_name: true,
                            email: true,
                        },
                    },
                },
            });
        });

        it("should throw error if certification not found", async () => {
            (mockPrisma.providerCertification.findUnique as jest.Mock).mockResolvedValue(null);

            await expect(
                CertificationService.startCertification("listing-1", "non-existent", "user-1")
            ).rejects.toThrow("Certification not found");
        });
    });

    describe("completeCertification", () => {
        it("should complete certification", async () => {
            const mockCertification = {
                id: "1",
                name: "keystone-warranty",
            };

            const mockRecord = {
                id: "record-1",
                status: "IN_PROGRESS",
                certification: mockCertification,
            };

            const mockCompletedRecord = {
                ...mockRecord,
                status: "COMPLETED",
                training_completed_at: new Date(),
                terms_accepted_at: new Date(),
                terms_accepted_by: "user-1",
            };

            (mockPrisma.providerCertification.findUnique as jest.Mock).mockResolvedValue(mockCertification);
            (mockPrisma.providerCertificationRecord.findFirst as jest.Mock).mockResolvedValue(mockRecord);
            (mockPrisma.providerCertificationRecord.update as jest.Mock).mockResolvedValue(mockCompletedRecord);

            const result = await CertificationService.completeCertification(
                "listing-1",
                "keystone-warranty",
                "user-1"
            );

            expect(result).toEqual(mockCompletedRecord);
        });

        it("should throw error if certification record not found", async () => {
            const mockCertification = {
                id: "1",
                name: "keystone-warranty",
            };

            (mockPrisma.providerCertification.findUnique as jest.Mock).mockResolvedValue(mockCertification);
            (mockPrisma.providerCertificationRecord.findFirst as jest.Mock).mockResolvedValue(null);

            await expect(
                CertificationService.completeCertification("listing-1", "keystone-warranty", "user-1")
            ).rejects.toThrow("Certification record not found. Please start the certification first.");
        });
    });

    describe("updateTrainingProgress", () => {
        it("should update training progress", async () => {
            const mockCertification = {
                id: "1",
                name: "keystone-warranty",
            };

            const mockRecord = {
                id: "record-1",
                status: "PENDING",
                certification: mockCertification,
            };

            const mockUpdatedRecord = {
                ...mockRecord,
                status: "IN_PROGRESS",
                training_progress: { module: 2, completed: true },
            };

            (mockPrisma.providerCertification.findUnique as jest.Mock).mockResolvedValue(mockCertification);
            (mockPrisma.providerCertificationRecord.findFirst as jest.Mock).mockResolvedValue(mockRecord);
            (mockPrisma.providerCertificationRecord.update as jest.Mock).mockResolvedValue(mockUpdatedRecord);

            const result = await CertificationService.updateTrainingProgress(
                "listing-1",
                "keystone-warranty",
                { module: 2, completed: true }
            );

            expect(result).toEqual(mockUpdatedRecord);
        });
    });

    describe("optOutOfCertification", () => {
        it("should opt out of certification", async () => {
            const mockCertification = {
                id: "1",
                name: "keystone-warranty",
            };

            const mockRecord = {
                id: "record-1",
                status: "PENDING",
                certification: mockCertification,
            };

            const mockOptedOutRecord = {
                ...mockRecord,
                opted_out: true,
                opted_out_at: new Date(),
                opted_out_reason: "Not interested",
                status: "PENDING",
            };

            (mockPrisma.providerCertification.findUnique as jest.Mock).mockResolvedValue(mockCertification);
            (mockPrisma.providerCertificationRecord.findFirst as jest.Mock).mockResolvedValue(mockRecord);
            (mockPrisma.providerCertificationRecord.update as jest.Mock).mockResolvedValue(mockOptedOutRecord);

            const result = await CertificationService.optOutOfCertification(
                "listing-1",
                "keystone-warranty",
                "Not interested"
            );

            expect(result).toEqual(mockOptedOutRecord);
        });
    });
}); 