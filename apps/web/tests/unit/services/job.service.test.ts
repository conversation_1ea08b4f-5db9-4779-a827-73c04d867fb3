import { queueMessage } from "@/lib/queue/qstash";
import { BlacklistService } from "@/lib/services/blacklist.service";
import { CreateJobParams, JobService } from "@/lib/services/job.service";
import { getDrivingDistance } from "@/lib/utils/distance";
import { prisma as mockPrisma } from "@/tests/mocks/prisma-mock";

// Mock dependencies
jest.mock("@/lib/services/blacklist.service");
jest.mock("@/lib/queue/qstash");
jest.mock("@/lib/utils/distance");

const mockBlacklistService = BlacklistService as jest.Mocked<typeof BlacklistService>;
const mockQueueMessage = queueMessage as jest.MockedFunction<typeof queueMessage>;
const mockGetDrivingDistance = getDrivingDistance as jest.MockedFunction<typeof getDrivingDistance>;

describe("JobService", () => {
    const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        phone: "555-1234",
        role: "USER",
        password: null,
        created_at: new Date(),
        updated_at: new Date()
    };

    const mockListing = {
        id: "listing-123",
        business_name: "Test RV Service",
        first_name: "Jane",
        last_name: "Smith",
        email: "<EMAIL>",
        locations: [
            {
                id: "location-123",
                latitude: 40.7128,
                longitude: -74.0060,
                city: "New York",
                state: "NY",
                is_active: true,
                default: true
            }
        ]
    };

    const mockJob = {
        id: "job-123",
        user_id: "user-123",
        status: "OPEN",
        first_name: "John",
        last_name: "Doe",
        email: "<EMAIL>",
        category: "rv-repair",
        message: "Need help with RV repair",
        quotes: []
    };

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup default mocks
        mockBlacklistService.checkEmailAccess.mockResolvedValue({
            isBlacklisted: false,
            message: null
        });
        mockQueueMessage.mockResolvedValue(undefined);
        mockGetDrivingDistance.mockResolvedValue(5.2);
    });

    describe("createJob", () => {
        const validJobParams: CreateJobParams = {
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
            category: "rv-repair",
            message: "Need help with my RV",
            listing_id: "listing-123",
            location: {
                latitude: 41.0000,
                longitude: -75.0000
            },
            rv_type: "Motorhome",
            rv_make: "Forest River",
            rv_model: "Georgetown",
            rv_year: 2020
        };

        it("should successfully create a job for non-warranty request", async () => {
            // Mock user upsert
            mockPrisma.user.upsert.mockResolvedValue(mockUser);

            // Mock listing lookup
            mockPrisma.listing.findUnique.mockResolvedValue(mockListing);

            // Mock job creation
            const mockCreatedJob = {
                ...mockJob,
                quotes: [{
                    id: "quote-123",
                    listing_id: "listing-123",
                    listing: mockListing,
                    status: "PENDING"
                }]
            };
            mockPrisma.job.create.mockResolvedValue(mockCreatedJob);

            const result = await JobService.createJob(validJobParams);

            // Verify blacklist check
            expect(mockBlacklistService.checkEmailAccess).toHaveBeenCalledWith("<EMAIL>");

            // Verify user upsert
            expect(mockPrisma.user.upsert).toHaveBeenCalledWith({
                where: { email: "<EMAIL>" },
                update: {
                    phone: null,
                    rv_details: {
                        type: "Motorhome",
                        make: "Forest River",
                        model: "Georgetown",
                        year: "2020"
                    }
                },
                create: {
                    email: "<EMAIL>",
                    first_name: "John",
                    last_name: "Doe",
                    phone: null,
                    role: "USER",
                    password: null
                }
            });

            // Verify job creation
            expect(mockPrisma.job.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    first_name: "John",
                    last_name: "Doe",
                    email: "<EMAIL>",
                    category: "rv-repair",
                    message: "Need help with my RV",
                    status: "OPEN",
                    quotes: {
                        create: expect.objectContaining({
                            listing: { connect: { id: "listing-123" } },
                            status: "PENDING",
                            distance_miles: 5.2
                        })
                    }
                }),
                include: expect.any(Object)
            });

            // Verify job processing queue
            expect(mockQueueMessage).toHaveBeenCalledWith({
                type: "job-processing",
                payload: expect.objectContaining({
                    job: mockCreatedJob,
                    listing: mockListing,
                    validatedData: validJobParams,
                    user: mockUser,
                    isWarranty: false
                })
            });

            // Verify analytics tracking
            expect(mockQueueMessage).toHaveBeenCalledWith({
                type: "analytics-tracking",
                payload: {
                    type: "lead-form-submission",
                    data: {
                        listingId: "listing-123",
                        listingName: "Test RV Service",
                        category: "rv-repair",
                        userId: "user-123",
                        isAnonymous: false
                    }
                }
            });

            expect(result.success).toBe(true);
            expect(result.job).toEqual(mockCreatedJob);
        });

        it("should successfully create a warranty job", async () => {
            const warrantyParams = {
                ...validJobParams,
                isWarranty: true,
                warranty_request_id: "warranty-123",
                listing_id: undefined // Not required for warranty jobs
            };

            mockPrisma.user.upsert.mockResolvedValue(mockUser);
            mockPrisma.job.create.mockResolvedValue(mockJob);

            const result = await JobService.createJob(warrantyParams);

            expect(mockPrisma.job.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    warranty_request: {
                        connect: { id: "warranty-123" }
                    }
                }),
                include: expect.any(Object)
            });

            expect(result.success).toBe(true);
        });

        it("should reject job if email is blacklisted", async () => {
            mockBlacklistService.checkEmailAccess.mockResolvedValue({
                isBlacklisted: true,
                message: "Email is blacklisted"
            });

            const result = await JobService.createJob(validJobParams);

            expect(result.success).toBe(false);
            expect(result.error).toBe("Lead submission blocked");
            expect(result.message).toBe("Email is blacklisted");
            expect(mockPrisma.user.upsert).not.toHaveBeenCalled();
        });

        it("should reject non-warranty job without listing_id", async () => {
            const invalidParams = {
                ...validJobParams,
                listing_id: undefined
            };

            const result = await JobService.createJob(invalidParams);

            expect(result.success).toBe(false);
            expect(result.error).toBe("Validation error");
            expect(result.message).toBe("listing_id is required for non-warranty jobs");
        });

        it("should handle job creation errors gracefully", async () => {
            mockPrisma.user.upsert.mockRejectedValue(new Error("Database error"));

            const result = await JobService.createJob(validJobParams);

            expect(result.success).toBe(false);
            expect(result.error).toBe("Internal server error");
            expect(result.message).toBe("Failed to create job");
        });
    });

    describe("getJobsForUser", () => {
        it("should return jobs with unread message counts", async () => {
            const mockJobsWithQuotes = [{
                ...mockJob,
                quotes: [{
                    id: "quote-123",
                    listing: mockListing,
                    messages: [{
                        id: "msg-1",
                        sender_type: "PROVIDER",
                        read_at: null
                    }, {
                        id: "msg-2",
                        sender_type: "PROVIDER",
                        read_at: null
                    }]
                }],
                user: mockUser
            }];

            mockPrisma.job.findMany.mockResolvedValue(mockJobsWithQuotes);

            const result = await JobService.getJobsForUser("user-123");

            expect(mockPrisma.job.findMany).toHaveBeenCalledWith({
                where: { user_id: "user-123" },
                include: expect.objectContaining({
                    quotes: expect.objectContaining({
                        include: expect.objectContaining({
                            messages: {
                                where: {
                                    read_at: null,
                                    sender_type: 'PROVIDER'
                                }
                            }
                        })
                    })
                })
            });

            expect(result[0].quotes[0].unread_messages_count).toBe(2);
        });
    });

    describe("getJobsForProvider", () => {
        it("should return quotes for a provider", async () => {
            const mockQuotes = [{
                id: "quote-123",
                listing_id: "listing-123",
                status: "ACCEPTED",
                job: {
                    id: "job-123",
                    user: {
                        first_name: "John",
                        last_name: "Doe",
                        email: "<EMAIL>"
                    },
                    category: "rv-repair",
                    warranty_request: { id: "warranty-123" }
                },
                messages: []
            }];

            mockPrisma.quote.findMany.mockResolvedValue(mockQuotes);

            const result = await JobService.getJobsForProvider("listing-123", "ACCEPTED");

            expect(mockPrisma.quote.findMany).toHaveBeenCalledWith({
                where: {
                    listing_id: "listing-123",
                    status: { in: ["ACCEPTED"] },
                    job: {
                        warranty_request: { isNot: null }
                    }
                },
                include: expect.any(Object),
                orderBy: { created_at: "desc" }
            });

            expect(result).toEqual(mockQuotes);
        });

        it("should return all statuses when no specific status provided", async () => {
            mockPrisma.quote.findMany.mockResolvedValue([]);

            await JobService.getJobsForProvider("listing-123");

            expect(mockPrisma.quote.findMany).toHaveBeenCalledWith({
                where: {
                    listing_id: "listing-123",
                    status: { in: expect.arrayContaining(["ACCEPTED", "WITHDRAWN", "COMPLETED"]) },
                    job: {
                        warranty_request: { isNot: null }
                    }
                },
                include: expect.any(Object),
                orderBy: { created_at: "desc" }
            });
        });
    });
}); 