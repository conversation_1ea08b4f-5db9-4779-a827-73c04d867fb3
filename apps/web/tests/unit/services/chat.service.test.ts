import { AIChatMessageRequest, AIChatService } from "@/lib/services/ai-chat.service";
import { mockPrisma } from "../../mocks/prisma-mock";

// Mock OpenAI
jest.mock("@ai-sdk/openai", () => ({
    openai: jest.fn(() => "mock-model")
}));

// Mock AI SDK
jest.mock("ai", () => ({
    streamText: jest.fn()
}));

// Mock data imports
jest.mock("@/data/rv-systems", () => ({
    RV_SYSTEMS: { systems: ["electrical", "plumbing"] }
}));

jest.mock("@/data/rvMakes", () => ({
    allRvMakes: ["Keystone", "Airstream"]
}));

describe("AIChatService", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("createConversation", () => {
        it("should create a new conversation for a user", async () => {
            const userId = "user-123";
            const mockConversation = {
                id: "conv-123",
                user_id: userId,
                status: "ACTIVE",
                created_at: new Date(),
                updated_at: new Date()
            };

            (mockPrisma.aIChatConversation.create as jest.Mock).mockResolvedValue(mockConversation);

            const result = await AIChatService.createConversation(userId);

            expect(mockPrisma.aIChatConversation.create).toHaveBeenCalledWith({
                data: {
                    user_id: userId,
                    status: "ACTIVE"
                }
            });

            expect(result).toEqual({
                conversationId: "conv-123",
                status: "created"
            });
        });
    });

    describe("sendMessage", () => {
        it("should send a message and return AI response", async () => {
            const userId = "user-123";
            const conversationId = "conv-123";

            const mockConversation = {
                id: conversationId,
                user_id: userId,
                status: "ACTIVE",
                rv_make: "Keystone",
                rv_model: "Montana",
                rv_year: 2026,
                rv_type: "fifth-wheel",
                messages: []
            };

            const mockUserMessage = {
                id: "msg-1",
                conversation_id: conversationId,
                content: "How do I winterize my RV?",
                is_user_message: true,
                created_at: new Date()
            };

            const mockAiMessage = {
                id: "msg-2",
                conversation_id: conversationId,
                content: "To winterize your 2026 Keystone Montana...",
                is_user_message: false,
                suggestions: ["What supplies do I need?"],
                related_resources: [{ title: "Winterization Guide", url: "/resources/winterization", type: "manual" }],
                created_at: new Date()
            };

            const mockStreamText = require("ai").streamText;
            const mockTextStream = (async function* () {
                yield "To winterize your 2026 Keystone Montana...";
            })();

            mockStreamText.mockResolvedValue({
                textStream: mockTextStream
            });

            (mockPrisma.aIChatConversation.findFirst as jest.Mock).mockResolvedValue(mockConversation);
            (mockPrisma.aIChatMessage.create as jest.Mock)
                .mockResolvedValueOnce(mockUserMessage)
                .mockResolvedValueOnce(mockAiMessage);
            (mockPrisma.aIChatConversation.update as jest.Mock).mockResolvedValue({});

            const request: AIChatMessageRequest = {
                message: "How do I winterize my RV?",
                conversationId,
                context: {
                    userRvDetails: {
                        make: "Keystone",
                        model: "Montana",
                        year: 2026,
                        type: "fifth-wheel"
                    }
                }
            };

            const result = await AIChatService.sendMessage(userId, request);

            expect(mockPrisma.aIChatConversation.findFirst).toHaveBeenCalledWith({
                where: {
                    id: conversationId,
                    user_id: userId,
                    status: "ACTIVE"
                },
                include: {
                    messages: {
                        orderBy: { created_at: "asc" }
                    }
                }
            });

            expect(mockPrisma.aIChatMessage.create).toHaveBeenCalledTimes(2);
            expect(mockPrisma.aIChatConversation.update).toHaveBeenCalledWith({
                where: { id: conversationId },
                data: { last_message_at: expect.any(Date) }
            });

            expect(result).toEqual({
                message: "To winterize your 2026 Keystone Montana...",
                conversationId,
                suggestions: expect.any(Array),
                relatedResources: expect.any(Array)
            });
        });

        it("should throw error if conversation not found", async () => {
            const userId = "user-123";
            const conversationId = "conv-123";

            (mockPrisma.aIChatConversation.findFirst as jest.Mock).mockResolvedValue(null);

            const request: AIChatMessageRequest = {
                message: "Hello",
                conversationId
            };

            await expect(AIChatService.sendMessage(userId, request)).rejects.toThrow(
                "Conversation not found or access denied"
            );
        });
    });

    describe("getConversationHistory", () => {
        it("should return conversation history", async () => {
            const userId = "user-123";
            const conversationId = "conv-123";

            const mockConversation = {
                id: conversationId,
                user_id: userId,
                status: "ACTIVE",
                messages: [
                    {
                        id: "msg-1",
                        content: "Hello",
                        is_user_message: true,
                        created_at: new Date("2024-01-15T10:30:00Z")
                    },
                    {
                        id: "msg-2",
                        content: "Hi! How can I help you?",
                        is_user_message: false,
                        created_at: new Date("2024-01-15T10:30:05Z")
                    }
                ]
            };

            (mockPrisma.aIChatConversation.findFirst as jest.Mock).mockResolvedValue(mockConversation);

            const result = await AIChatService.getConversationHistory(userId, conversationId);

            expect(mockPrisma.aIChatConversation.findFirst).toHaveBeenCalledWith({
                where: {
                    id: conversationId,
                    user_id: userId,
                    status: "ACTIVE"
                },
                include: {
                    messages: {
                        orderBy: { created_at: "asc" }
                    }
                }
            });

            expect(result).toEqual({
                conversationId,
                messages: [
                    {
                        id: "msg-1",
                        text: "Hello",
                        isUser: true,
                        timestamp: "2024-01-15T10:30:00.000Z"
                    },
                    {
                        id: "msg-2",
                        text: "Hi! How can I help you?",
                        isUser: false,
                        timestamp: "2024-01-15T10:30:05.000Z"
                    }
                ]
            });
        });

        it("should throw error if conversation not found", async () => {
            const userId = "user-123";
            const conversationId = "conv-123";

            (mockPrisma.aIChatConversation.findFirst as jest.Mock).mockResolvedValue(null);

            await expect(AIChatService.getConversationHistory(userId, conversationId)).rejects.toThrow(
                "Conversation not found or access denied"
            );
        });
    });

    describe("getSuggestedQuestions", () => {
        it("should return base questions when no RV context", async () => {
            const userId = "user-123";

            (mockPrisma.aIChatConversation.findFirst as jest.Mock).mockResolvedValue(null);

            const result = await AIChatService.getSuggestedQuestions(userId);

            expect(result).toEqual({
                questions: [
                    "How do I check my tire pressure?",
                    "What's the proper way to level my RV?",
                    "How often should I service my generator?",
                    "What maintenance should I do before a trip?"
                ]
            });
        });

        it("should return customized questions when RV context exists", async () => {
            const userId = "user-123";
            const mockConversation = {
                rv_make: "Keystone",
                rv_model: "Montana",
                rv_year: 2026
            };

            (mockPrisma.aIChatConversation.findFirst as jest.Mock).mockResolvedValue(mockConversation);

            const result = await AIChatService.getSuggestedQuestions(userId);

            expect(result.questions).toContain("How do I winterize my 2026 Keystone Montana?");
            expect(result.questions).toContain("What are common issues with Keystone Montana?");
            expect(result.questions).toContain("How do I check the battery system in my Keystone?");
            expect(result.questions).toContain("What's the recommended maintenance schedule for my Keystone?");
        });
    });
});
