import { prisma } from "@/lib/prisma";
import { adminLogger } from "@/lib/services/admin-log.service";
import { UserDeviceService } from "@/lib/services/user-device.service";

// Mock prisma
jest.mock("@/lib/prisma", () => ({
	prisma: require("../../mocks/prisma-mock").prisma
}));

jest.mock("@/lib/services/admin-log.service", () => ({
	adminLogger: {
		log: jest.fn()
	}
}));

// Mock console.error
global.console = {
	...console,
	error: jest.fn()
};

describe("UserDeviceService", () => {
	beforeEach(() => {
		jest.clearAllMocks();
		jest.useFakeTimers();
	});

	afterEach(() => {
		jest.useRealTimers();
	});

	describe("registerAnonymousDevice", () => {
		it("should create a new anonymous device if none exists", async () => {
			const mockDate = new Date("2023-01-01T12:00:00Z");
			jest.setSystemTime(mockDate);

			const deviceData = {
				deviceToken: "test-token-123",
				platform: "ios" as const
			};

			const mockDevice = {
				id: "device-123",
				device_token: deviceData.deviceToken,
				platform: deviceData.platform,
				user_id: null,
				created_at: mockDate,
				updated_at: mockDate,
				last_used_at: mockDate
			};

			(prisma.userDevice.findUnique as jest.Mock).mockResolvedValue(null);
			(prisma.userDevice.create as jest.Mock).mockResolvedValue(mockDevice);

			const result =
				await UserDeviceService.registerAnonymousDevice(deviceData);

			expect(prisma.userDevice.findUnique).toHaveBeenCalledWith({
				where: { device_token: deviceData.deviceToken }
			});

			expect(prisma.userDevice.create).toHaveBeenCalledWith({
				data: {
					device_token: deviceData.deviceToken,
					platform: deviceData.platform,
					user_id: null,
					last_used_at: mockDate
				}
			});

			expect(adminLogger.log).toHaveBeenCalledWith(
				"Anonymous device registered",
				{
					deviceId: mockDevice.id,
					platform: deviceData.platform
				}
			);

			expect(result).toEqual(mockDevice);
		});

		it("should update last_used_at if device already exists", async () => {
			const mockDate = new Date("2023-01-01T12:00:00Z");
			jest.setSystemTime(mockDate);

			const deviceData = {
				deviceToken: "existing-token-123",
				platform: "android" as const
			};

			const existingDevice = {
				id: "device-456",
				device_token: deviceData.deviceToken,
				platform: deviceData.platform,
				user_id: null,
				created_at: new Date("2022-12-30T12:00:00Z"),
				updated_at: new Date("2022-12-31T12:00:00Z"),
				last_used_at: new Date("2022-12-31T12:00:00Z")
			};

			const updatedDevice = {
				...existingDevice,
				last_used_at: mockDate,
				updated_at: mockDate
			};

			(prisma.userDevice.findUnique as jest.Mock).mockResolvedValue(
				existingDevice
			);
			(prisma.userDevice.update as jest.Mock).mockResolvedValue(updatedDevice);

			const result =
				await UserDeviceService.registerAnonymousDevice(deviceData);

			expect(prisma.userDevice.update).toHaveBeenCalledWith({
				where: { id: existingDevice.id },
				data: { last_used_at: mockDate }
			});

			expect(prisma.userDevice.create).not.toHaveBeenCalled();
			expect(result).toEqual(updatedDevice);
		});

		it("should handle errors and throw appropriate message", async () => {
			const deviceData = {
				deviceToken: "error-token",
				platform: "ios" as const
			};

			(prisma.userDevice.findUnique as jest.Mock).mockRejectedValue(
				new Error("Database error")
			);

			await expect(
				UserDeviceService.registerAnonymousDevice(deviceData)
			).rejects.toThrow("Failed to register device");

			expect(console.error).toHaveBeenCalledWith(
				"Error registering anonymous device:",
				expect.any(Error)
			);
		});
	});

	describe("connectDeviceToUser", () => {
		it("should update existing device with user info", async () => {
			const mockDate = new Date("2023-01-01T12:00:00Z");
			jest.setSystemTime(mockDate);

			const connectionData = {
				deviceToken: "existing-device-token",
				userId: "user-123",
				platform: "ios" as const
			};

			const existingDevice = {
				id: "device-789",
				device_token: connectionData.deviceToken,
				platform: "android",
				user_id: null,
				created_at: new Date("2022-12-30T12:00:00Z"),
				updated_at: new Date("2022-12-31T12:00:00Z"),
				last_used_at: new Date("2022-12-31T12:00:00Z")
			};

			const updatedDevice = {
				...existingDevice,
				user_id: connectionData.userId,
				platform: connectionData.platform,
				last_used_at: mockDate,
				updated_at: mockDate
			};

			(prisma.userDevice.findUnique as jest.Mock).mockResolvedValue(
				existingDevice
			);
			(prisma.userDevice.update as jest.Mock).mockResolvedValue(updatedDevice);

			const result =
				await UserDeviceService.connectDeviceToUser(connectionData);

			expect(prisma.userDevice.update).toHaveBeenCalledWith({
				where: { id: existingDevice.id },
				data: {
					user_id: connectionData.userId,
					last_used_at: mockDate,
					platform: connectionData.platform
				}
			});

			expect(adminLogger.log).toHaveBeenCalledWith("Device connected to user", {
				deviceId: updatedDevice.id,
				userId: connectionData.userId,
				previouslyAnonymous: true
			});

			expect(result).toEqual(updatedDevice);
		});

		it("should create new device if none exists", async () => {
			const mockDate = new Date("2023-01-01T12:00:00Z");
			jest.setSystemTime(mockDate);

			const connectionData = {
				deviceToken: "new-device-token",
				userId: "user-456",
				platform: "android" as const
			};

			const newDevice = {
				id: "device-new",
				device_token: connectionData.deviceToken,
				platform: connectionData.platform,
				user_id: connectionData.userId,
				created_at: mockDate,
				updated_at: mockDate,
				last_used_at: mockDate
			};

			(prisma.userDevice.findUnique as jest.Mock).mockResolvedValue(null);
			(prisma.userDevice.create as jest.Mock).mockResolvedValue(newDevice);

			const result =
				await UserDeviceService.connectDeviceToUser(connectionData);

			expect(prisma.userDevice.create).toHaveBeenCalledWith({
				data: {
					device_token: connectionData.deviceToken,
					platform: connectionData.platform,
					user_id: connectionData.userId,
					last_used_at: mockDate
				}
			});

			expect(adminLogger.log).toHaveBeenCalledWith(
				"New device created for user",
				{
					deviceId: newDevice.id,
					userId: connectionData.userId
				}
			);

			expect(result).toEqual(newDevice);
		});

		it("should default to ios platform if none provided", async () => {
			const mockDate = new Date("2023-01-01T12:00:00Z");
			jest.setSystemTime(mockDate);

			const connectionData = {
				deviceToken: "no-platform-token",
				userId: "user-789"
			};

			const newDevice = {
				id: "device-default",
				device_token: connectionData.deviceToken,
				platform: "ios",
				user_id: connectionData.userId,
				created_at: mockDate,
				updated_at: mockDate,
				last_used_at: mockDate
			};

			(prisma.userDevice.findUnique as jest.Mock).mockResolvedValue(null);
			(prisma.userDevice.create as jest.Mock).mockResolvedValue(newDevice);

			const result =
				await UserDeviceService.connectDeviceToUser(connectionData);

			expect(prisma.userDevice.create).toHaveBeenCalledWith({
				data: {
					device_token: connectionData.deviceToken,
					platform: "ios",
					user_id: connectionData.userId,
					last_used_at: mockDate
				}
			});

			expect(result).toEqual(newDevice);
		});
	});

	describe("updateDeviceToken", () => {
		it("should update device token successfully", async () => {
			const mockDate = new Date("2023-01-01T12:00:00Z");
			jest.setSystemTime(mockDate);

			const updateData = {
				oldToken: "old-token-123",
				newToken: "new-token-456",
				userId: "user-123"
			};

			const existingDevice = {
				id: "device-update",
				device_token: updateData.oldToken,
				platform: "ios",
				user_id: "existing-user",
				created_at: new Date("2022-12-30T12:00:00Z"),
				updated_at: new Date("2022-12-31T12:00:00Z"),
				last_used_at: new Date("2022-12-31T12:00:00Z")
			};

			const updatedDevice = {
				...existingDevice,
				device_token: updateData.newToken,
				user_id: updateData.userId,
				last_used_at: mockDate,
				updated_at: mockDate
			};

			(prisma.userDevice.findUnique as jest.Mock).mockResolvedValue(
				existingDevice
			);
			(prisma.userDevice.update as jest.Mock).mockResolvedValue(updatedDevice);

			const result = await UserDeviceService.updateDeviceToken(updateData);

			expect(prisma.userDevice.update).toHaveBeenCalledWith({
				where: { id: existingDevice.id },
				data: {
					device_token: updateData.newToken,
					last_used_at: mockDate,
					user_id: updateData.userId
				}
			});

			expect(adminLogger.log).toHaveBeenCalledWith("Device token updated", {
				deviceId: updatedDevice.id,
				userId: updateData.userId
			});

			expect(result).toEqual(updatedDevice);
		});

		it("should throw error if device not found", async () => {
			const updateData = {
				oldToken: "non-existent-token",
				newToken: "new-token-456"
			};

			(prisma.userDevice.findUnique as jest.Mock).mockResolvedValue(null);

			await expect(
				UserDeviceService.updateDeviceToken(updateData)
			).rejects.toThrow("Device not found");
		});
	});

	describe("cleanupOldAnonymousDevices", () => {
		it("should delete old anonymous devices and return count", async () => {
			const mockDate = new Date("2023-01-01T12:00:00Z");
			jest.setSystemTime(mockDate);

			const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
			const mockResult = { count: 5 };

			(prisma.userDevice.deleteMany as jest.Mock).mockResolvedValue(mockResult);

			const result = await UserDeviceService.cleanupOldAnonymousDevices();

			expect(prisma.userDevice.deleteMany).toHaveBeenCalledWith({
				where: {
					user_id: null,
					last_used_at: { lt: thirtyDaysAgo }
				}
			});

			expect(adminLogger.log).toHaveBeenCalledWith(
				"Old anonymous devices cleaned up",
				{
					count: mockResult.count
				}
			);

			expect(result).toBe(5);
		});
	});

	describe("getDeviceStats", () => {
		it("should return device statistics", async () => {
			const mockTotal = 100;
			const mockAnonymous = 30;
			const mockPlatformStats = [
				{ platform: "ios", _count: 60 },
				{ platform: "android", _count: 40 }
			];

			(prisma.userDevice.count as jest.Mock)
				.mockResolvedValueOnce(mockTotal)
				.mockResolvedValueOnce(mockAnonymous);
			(prisma.userDevice.groupBy as jest.Mock).mockResolvedValue(
				mockPlatformStats
			);

			const result = await UserDeviceService.getDeviceStats();

			expect(prisma.userDevice.count).toHaveBeenCalledTimes(2);
			expect(prisma.userDevice.count).toHaveBeenNthCalledWith(1);
			expect(prisma.userDevice.count).toHaveBeenNthCalledWith(2, {
				where: { user_id: null }
			});

			expect(prisma.userDevice.groupBy).toHaveBeenCalledWith({
				by: ["platform"],
				_count: true
			});

			expect(result).toEqual({
				totalDevices: 100,
				anonymousDevices: 30,
				connectedDevices: 70,
				platformBreakdown: [
					{ platform: "ios", count: 60 },
					{ platform: "android", count: 40 }
				]
			});
		});
	});

	describe("connectMultipleDevicesToUser", () => {
		it("should connect multiple anonymous devices to a user", async () => {
			const mockDate = new Date("2023-01-01T12:00:00Z");
			jest.setSystemTime(mockDate);

			const connectionData = {
				deviceTokens: ["token1", "token2", "token3"],
				userId: "user-multi"
			};

			const mockDevices = [
				{
					id: "device1",
					device_token: "token1",
					platform: "ios",
					user_id: null,
					created_at: new Date("2022-12-30T12:00:00Z"),
					updated_at: new Date("2022-12-31T12:00:00Z"),
					last_used_at: new Date("2022-12-31T12:00:00Z")
				},
				{
					id: "device2",
					device_token: "token2",
					platform: "android",
					user_id: null,
					created_at: new Date("2022-12-30T12:00:00Z"),
					updated_at: new Date("2022-12-31T12:00:00Z"),
					last_used_at: new Date("2022-12-31T12:00:00Z")
				}
			];

			const updatedDevices = mockDevices.map((device) => ({
				...device,
				user_id: connectionData.userId,
				last_used_at: mockDate,
				updated_at: mockDate
			}));

			(prisma.userDevice.findMany as jest.Mock).mockResolvedValue(mockDevices);
			(prisma.$transaction as jest.Mock).mockResolvedValue(updatedDevices);

			const result =
				await UserDeviceService.connectMultipleDevicesToUser(connectionData);

			expect(prisma.userDevice.findMany).toHaveBeenCalledWith({
				where: {
					device_token: { in: connectionData.deviceTokens },
					user_id: null
				}
			});

			expect(prisma.$transaction).toHaveBeenCalledWith(expect.any(Array));

			expect(adminLogger.log).toHaveBeenCalledWith(
				"Multiple devices connected to user",
				{
					userId: connectionData.userId,
					deviceCount: 2,
					deviceIds: ["device1", "device2"]
				}
			);

			expect(result).toEqual(updatedDevices);
		});

		it("should return empty array if no anonymous devices found", async () => {
			const connectionData = {
				deviceTokens: ["non-existent-token"],
				userId: "user-no-devices"
			};

			(prisma.userDevice.findMany as jest.Mock).mockResolvedValue([]);

			const result =
				await UserDeviceService.connectMultipleDevicesToUser(connectionData);

			expect(result).toEqual([]);
			expect(prisma.$transaction).not.toHaveBeenCalled();
		});
	});

	describe("getUserDevices", () => {
		it("should return user devices ordered by last_used_at", async () => {
			const userId = "user-123";
			const mockDevices = [
				{
					id: "device1",
					device_token: "token1",
					platform: "ios",
					user_id: userId,
					last_used_at: new Date("2023-01-01T12:00:00Z")
				},
				{
					id: "device2",
					device_token: "token2",
					platform: "android",
					user_id: userId,
					last_used_at: new Date("2022-12-31T12:00:00Z")
				}
			];

			(prisma.userDevice.findMany as jest.Mock).mockResolvedValue(mockDevices);

			const result = await UserDeviceService.getUserDevices(userId);

			expect(prisma.userDevice.findMany).toHaveBeenCalledWith({
				where: { user_id: userId },
				orderBy: { last_used_at: "desc" }
			});

			expect(result).toEqual(mockDevices);
		});
	});

	describe("removeDevice", () => {
		it("should remove device and log the action", async () => {
			const deviceToken = "token-to-remove";
			const mockDevice = {
				id: "device-remove",
				device_token: deviceToken,
				platform: "ios",
				user_id: "user-123"
			};

			(prisma.userDevice.findUnique as jest.Mock).mockResolvedValue(mockDevice);
			(prisma.userDevice.delete as jest.Mock).mockResolvedValue(mockDevice);

			await UserDeviceService.removeDevice(deviceToken);

			expect(prisma.userDevice.delete).toHaveBeenCalledWith({
				where: { device_token: deviceToken }
			});

			expect(adminLogger.log).toHaveBeenCalledWith("Device removed", {
				deviceId: mockDevice.id,
				userId: mockDevice.user_id
			});
		});

		it("should handle case where device does not exist", async () => {
			const deviceToken = "non-existent-token";

			(prisma.userDevice.findUnique as jest.Mock).mockResolvedValue(null);

			await UserDeviceService.removeDevice(deviceToken);

			expect(prisma.userDevice.delete).not.toHaveBeenCalled();
			expect(adminLogger.log).not.toHaveBeenCalled();
		});
	});
});
