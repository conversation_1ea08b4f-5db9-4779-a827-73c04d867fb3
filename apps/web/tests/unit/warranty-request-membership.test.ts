/**
 * Test to verify the warranty request creation logic properly handles
 * user creation and membership assignment in the OEM portal
 */

import { membershipService } from '@/lib/services/membership.service';
import { mockPrisma } from '@/tests/mocks/prisma-mock';

// Mock the membership service
jest.mock('@/lib/services/membership.service', () => ({
    membershipService: {
        createOrUpdateMembership: jest.fn(),
    },
}));

describe('Warranty Request User and Membership Logic', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('New User Creation Scenarios', () => {
        it('should create user with STANDARD membership when user does not exist', async () => {
            // Mock no existing user
            mockPrisma.user.findUnique.mockResolvedValueOnce(null);

            const newUserId = 'new-user-123';
            const mockCreatedUser = {
                id: newUserId,
                email: '<EMAIL>',
                first_name: '<PERSON>',
                last_name: '<PERSON><PERSON>',
                role: '<PERSON><PERSON>',
                membership_level: 'STANDARD',
            };

            mockPrisma.user.create.mockResolvedValue(mockCreatedUser);
            mockPrisma.membership.count.mockResolvedValue(100); // Next member number = 101
            mockPrisma.membership.create.mockResolvedValue({
                id: 'membership-123',
                user_id: newUserId,
                level: 'STANDARD',
                member_number: 101,
            });

            // Simulate the logic from warranty request creation
            const warrantyRequestData = {
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
            };

            // Check if user exists
            let existingCustomer = await mockPrisma.user.findUnique({
                where: { email: warrantyRequestData.email },
                include: { membership: true }
            });

            expect(existingCustomer).toBeNull();

            if (!existingCustomer) {
                // Create user
                existingCustomer = await mockPrisma.user.create({
                    data: {
                        email: warrantyRequestData.email,
                        first_name: warrantyRequestData.first_name,
                        last_name: warrantyRequestData.last_name,
                        role: "USER",
                        membership_level: "STANDARD",
                        newsletter_subscribed: true
                    }
                });

                // Get next member number
                const memberCount = await mockPrisma.membership.count({
                    where: { is_active: true }
                });
                const memberNumber = memberCount + 1;

                // Create membership
                await mockPrisma.membership.create({
                    data: {
                        user_id: existingCustomer.id,
                        level: "STANDARD",
                        member_number: memberNumber,
                        amount_paid: 0,
                        currency: "usd"
                    }
                });
            }

            // Verify the calls
            expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
                where: { email: '<EMAIL>' },
                include: { membership: true }
            });

            expect(mockPrisma.user.create).toHaveBeenCalledWith({
                data: {
                    email: '<EMAIL>',
                    first_name: 'John',
                    last_name: 'Doe',
                    role: 'USER',
                    membership_level: 'STANDARD',
                    newsletter_subscribed: true,
                }
            });

            expect(mockPrisma.membership.create).toHaveBeenCalledWith({
                data: {
                    user_id: newUserId,
                    level: 'STANDARD',
                    member_number: 101,
                    amount_paid: 0,
                    currency: 'usd',
                }
            });
        });

        it('should upgrade existing user with FREE membership to STANDARD', async () => {
            const existingUserWithFreeMembership = {
                id: 'existing-user-123',
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
                membership: {
                    id: 'membership-123',
                    level: 'FREE',
                    user_id: 'existing-user-123',
                },
            };

            mockPrisma.user.findUnique.mockResolvedValue(existingUserWithFreeMembership);
            mockPrisma.user.update.mockResolvedValue({
                ...existingUserWithFreeMembership,
                membership_level: 'STANDARD',
            });
            mockPrisma.membership.update.mockResolvedValue({
                ...existingUserWithFreeMembership.membership,
                level: 'STANDARD',
            });

            // Simulate the warranty request logic
            const warrantyRequestData = {
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
            };

            let existingCustomer = await mockPrisma.user.findUnique({
                where: { email: warrantyRequestData.email },
                include: { membership: true }
            });

            expect(existingCustomer).toBeDefined();
            expect(existingCustomer!.membership.level).toBe('FREE');

            // User exists with FREE membership - upgrade them
            if (existingCustomer && (!existingCustomer.membership || existingCustomer.membership.level === "FREE")) {
                // Update user's membership level
                await mockPrisma.user.update({
                    where: { id: existingCustomer.id },
                    data: { membership_level: "STANDARD" }
                });

                if (existingCustomer.membership) {
                    // Update existing membership
                    await mockPrisma.membership.update({
                        where: { user_id: existingCustomer.id },
                        data: {
                            level: "STANDARD",
                            is_active: true,
                            cancelled_at: null
                        }
                    });
                }
            }

            expect(mockPrisma.user.update).toHaveBeenCalledWith({
                where: { id: 'existing-user-123' },
                data: { membership_level: 'STANDARD' },
            });

            expect(mockPrisma.membership.update).toHaveBeenCalledWith({
                where: { user_id: 'existing-user-123' },
                data: {
                    level: 'STANDARD',
                    is_active: true,
                    cancelled_at: null,
                },
            });
        });

        it('should not modify existing user with STANDARD or PREMIUM membership', async () => {
            const existingUserWithStandardMembership = {
                id: 'existing-user-123',
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
                membership: {
                    id: 'membership-123',
                    level: 'STANDARD',
                    user_id: 'existing-user-123',
                },
            };

            mockPrisma.user.findUnique.mockResolvedValue(existingUserWithStandardMembership);

            const warrantyRequestData = {
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
            };

            let existingCustomer = await mockPrisma.user.findUnique({
                where: { email: warrantyRequestData.email },
                include: { membership: true }
            });

            expect(existingCustomer).toBeDefined();
            expect(existingCustomer!.membership.level).toBe('STANDARD');

            // User exists with STANDARD membership - don't modify
            const shouldUpgrade = existingCustomer && (!existingCustomer.membership || existingCustomer.membership.level === "FREE");
            expect(shouldUpgrade).toBe(false);

            // Should not call any update methods
            expect(mockPrisma.user.create).not.toHaveBeenCalled();
            expect(mockPrisma.user.update).not.toHaveBeenCalled();
            expect(mockPrisma.membership.create).not.toHaveBeenCalled();
            expect(mockPrisma.membership.update).not.toHaveBeenCalled();
        });
    });

    describe('Integration with Membership Service', () => {
        it('should use membership service for creating memberships with proper parameters', async () => {
            const userId = 'user-123';
            const req = {} as any; // Mock request

            (membershipService.createOrUpdateMembership as jest.Mock).mockResolvedValue({
                user: { id: userId, membership_level: 'STANDARD' },
                membership: { id: 'membership-123', level: 'STANDARD' },
                isNewMembership: true
            });

            // Simulate calling the membership service as would be done in production
            const result = await membershipService.createOrUpdateMembership({
                userId,
                level: 'STANDARD',
                amountPaid: 0,
                currency: 'usd',
                sendWelcomeEmail: true,
                sendSlackNotification: false,
                req
            });

            expect(membershipService.createOrUpdateMembership).toHaveBeenCalledWith({
                userId,
                level: 'STANDARD',
                amountPaid: 0,
                currency: 'usd',
                sendWelcomeEmail: true,
                sendSlackNotification: false,
                req
            });

            expect(result.user.membership_level).toBe('STANDARD');
            expect(result.membership.level).toBe('STANDARD');
            expect(result.isNewMembership).toBe(true);
        });
    });
});