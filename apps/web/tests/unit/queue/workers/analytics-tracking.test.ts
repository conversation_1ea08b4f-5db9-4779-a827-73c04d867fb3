import { processAnalyticsTracking } from "@/lib/queue/workers/analytics-tracking";
import { GoogleAnalyticsService } from "@/lib/services/google-analytics.service";

// Mock the GoogleAnalyticsService
jest.mock("@/lib/services/google-analytics.service", () => ({
    GoogleAnalyticsService: {
        trackServerLeadFormSubmission: jest.fn(),
        trackServerProviderContact: jest.fn(),
        trackServerAccountCreation: jest.fn()
    }
}));

describe("Analytics Tracking Worker", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("processAnalyticsTracking", () => {
        it("should process lead form submission tracking", async () => {
            const payload = {
                type: "lead-form-submission" as const,
                data: {
                    listingId: "listing123",
                    listingName: "Test Provider",
                    category: "repair",
                    userId: "user123",
                    isAnonymous: false
                }
            };

            await processAnalyticsTracking(payload);

            expect(GoogleAnalyticsService.trackServerLeadFormSubmission).toHaveBeenCalledWith(payload.data);
        });

        it("should process provider contact tracking", async () => {
            const payload = {
                type: "provider-contact" as const,
                data: {
                    listingId: "listing123",
                    listingName: "Test Provider",
                    contactType: "phone" as const,
                    contactValue: "+**********",
                    userId: "user123",
                    isAnonymous: false
                }
            };

            await processAnalyticsTracking(payload);

            expect(GoogleAnalyticsService.trackServerProviderContact).toHaveBeenCalledWith(payload.data);
        });

        it("should process account creation tracking", async () => {
            const payload = {
                type: "account-creation" as const,
                data: {
                    userId: "user123",
                    email: "<EMAIL>",
                    source: "website",
                    referrer: "https://google.com"
                }
            };

            await processAnalyticsTracking(payload);

            expect(GoogleAnalyticsService.trackServerAccountCreation).toHaveBeenCalledWith(payload.data);
        });

        it("should handle unknown tracking type gracefully", async () => {
            const consoleSpy = jest.spyOn(console, "error").mockImplementation();

            const payload = {
                type: "unknown-type" as any,
                data: {}
            };

            await processAnalyticsTracking(payload);

            expect(consoleSpy).toHaveBeenCalledWith("[Analytics] Unknown tracking type: unknown-type");
            expect(GoogleAnalyticsService.trackServerLeadFormSubmission).not.toHaveBeenCalled();
            expect(GoogleAnalyticsService.trackServerProviderContact).not.toHaveBeenCalled();
            expect(GoogleAnalyticsService.trackServerAccountCreation).not.toHaveBeenCalled();

            consoleSpy.mockRestore();
        });

        it("should handle tracking errors gracefully without throwing", async () => {
            const consoleSpy = jest.spyOn(console, "error").mockImplementation();

            (GoogleAnalyticsService.trackServerLeadFormSubmission as jest.Mock).mockRejectedValue(new Error("Tracking failed"));

            const payload = {
                type: "lead-form-submission" as const,
                data: {
                    listingId: "listing123",
                    listingName: "Test Provider",
                    category: "repair",
                    userId: "user123",
                    isAnonymous: false
                }
            };

            // Should not throw
            await expect(processAnalyticsTracking(payload)).resolves.not.toThrow();

            expect(consoleSpy).toHaveBeenCalledWith("[Analytics] Failed to track lead-form-submission event:", expect.any(Error));

            consoleSpy.mockRestore();
        });

        it("should call the appropriate tracking method", async () => {
            const payload = {
                type: "lead-form-submission" as const,
                data: {
                    listingId: "listing123",
                    listingName: "Test Provider",
                    category: "repair",
                    userId: "user123",
                    isAnonymous: false
                }
            };

            await processAnalyticsTracking(payload);

            expect(GoogleAnalyticsService.trackServerLeadFormSubmission).toHaveBeenCalledWith(payload.data);
        });
    });
}); 