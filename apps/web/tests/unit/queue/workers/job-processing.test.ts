import prisma from "@/lib/prisma";
import { processJob } from "@/lib/queue/workers/job-processing";
import { membershipService } from "@/lib/services/membership.service";

// Mock dependencies
jest.mock("@/lib/prisma", () => ({
    job: {
        update: jest.fn()
    },
    membership: {
        findUnique: jest.fn()
    },
    membershipOffer: {
        findFirst: jest.fn(),
        create: jest.fn()
    },
    user: {
        update: jest.fn()
    },
    warrantyRequest: {
        update: jest.fn()
    },
    listing: {
        findUnique: jest.fn()
    }
}));

jest.mock("@/lib/services/membership.service", () => ({
    membershipService: {
        createOrUpdateMembership: jest.fn()
    }
}));

jest.mock("@/lib/services/fraud-detection.service", () => ({
    FraudDetectionService: {
        checkJobSubmission: jest.fn().mockResolvedValue({ isSuspicious: false }),
        flagJobAsFraudulent: jest.fn()
    }
}));

jest.mock("@/lib/services/emailNewsletter.service", () => ({
    EmailNewsletterService: {
        syncNewsletterSubscriber: jest.fn().mockResolvedValue(true)
    }
}));

jest.mock("@/lib/services", () => ({
    emailService: {
        send: jest.fn().mockResolvedValue(true)
    }
}));

jest.mock("@/lib/services/timeline.service", () => ({
    timelineService: {
        createTimelineUpdate: jest.fn().mockResolvedValue(true)
    }
}));

jest.mock("@/lib/services/job-invitation.service", () => ({
    JobInvitationService: {
        sendLeadNotification: jest.fn().mockResolvedValue(true)
    }
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockMembershipService = membershipService as jest.Mocked<typeof membershipService>;

describe("Job Processing - Warranty Membership Granting", () => {
    const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        first_name: "John",
        last_name: "Doe",
        membership_level: "FREE" as const
    };

    const mockJob = {
        id: "job123",
        user_id: "user123",
        warranty_request_id: "warranty123",
        quotes: []
    };

    const mockPayload = {
        job: mockJob,
        listing: null,
        validatedData: {
            warranty_request_id: "warranty123",
            email: "<EMAIL>",
            rv_year: "2023",
            rv_make: "Keystone",
            rv_model: "Cougar"
        },
        user: mockUser,
        isWarranty: true
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Warranty membership granting", () => {
        it("should grant STANDARD membership to warranty customer without existing membership", async () => {
            // Mock no existing membership
            mockPrisma.membership.findUnique.mockResolvedValue(null);
            mockPrisma.user.update.mockResolvedValue(mockUser);
            mockPrisma.warrantyRequest.update.mockResolvedValue({} as any);
            mockMembershipService.createOrUpdateMembership.mockResolvedValue({} as any);

            await processJob(mockPayload);

            expect(mockMembershipService.createOrUpdateMembership).toHaveBeenCalledWith({
                userId: "user123",
                level: "STANDARD",
                amountPaid: 0,
                currency: "usd",
                sendWelcomeEmail: false,
                sendSlackNotification: false,
                req: undefined
            });
        });

        it("should grant STANDARD membership to warranty customer with FREE membership", async () => {
            // Mock existing FREE membership
            mockPrisma.membership.findUnique.mockResolvedValue({
                id: "membership123",
                level: "FREE"
            } as any);
            mockPrisma.user.update.mockResolvedValue(mockUser);
            mockPrisma.warrantyRequest.update.mockResolvedValue({} as any);
            mockMembershipService.createOrUpdateMembership.mockResolvedValue({} as any);

            await processJob(mockPayload);

            expect(mockMembershipService.createOrUpdateMembership).toHaveBeenCalledWith({
                userId: "user123",
                level: "STANDARD",
                amountPaid: 0,
                currency: "usd",
                sendWelcomeEmail: false,
                sendSlackNotification: false,
                req: undefined
            });
        });

        it("should not grant membership to warranty customer with existing STANDARD membership", async () => {
            // Mock existing STANDARD membership
            mockPrisma.membership.findUnique.mockResolvedValue({
                id: "membership123",
                level: "STANDARD"
            } as any);
            mockPrisma.warrantyRequest.update.mockResolvedValue({} as any);

            await processJob(mockPayload);

            expect(mockMembershipService.createOrUpdateMembership).not.toHaveBeenCalled();
        });

        it("should not grant membership to warranty customer with existing PREMIUM membership", async () => {
            // Mock existing PREMIUM membership
            mockPrisma.membership.findUnique.mockResolvedValue({
                id: "membership123",
                level: "PREMIUM"
            } as any);
            mockPrisma.warrantyRequest.update.mockResolvedValue({} as any);

            await processJob(mockPayload);

            expect(mockMembershipService.createOrUpdateMembership).not.toHaveBeenCalled();
        });

        it("should handle membership service errors gracefully", async () => {
            mockPrisma.membership.findUnique.mockResolvedValue(null);
            mockPrisma.warrantyRequest.update.mockResolvedValue({} as any);
            mockMembershipService.createOrUpdateMembership.mockRejectedValue(new Error("Membership service error"));

            await processJob(mockPayload);

            // Should not throw error, just log it
            expect(mockMembershipService.createOrUpdateMembership).toHaveBeenCalled();
        });

        it("should not grant membership for non-warranty jobs", async () => {
            const nonWarrantyPayload = {
                ...mockPayload,
                isWarranty: false,
                validatedData: {
                    ...mockPayload.validatedData,
                    warranty_request_id: undefined
                }
            };

            await processJob(nonWarrantyPayload);

            expect(mockMembershipService.createOrUpdateMembership).not.toHaveBeenCalled();
        });
    });

    describe("Membership offer creation", () => {
        const mockFreeUser = {
            id: "user123",
            email: "<EMAIL>",
            first_name: "John",
            last_name: "Doe",
            membership_level: "FREE" as const
        };

        const mockNonWarrantyPayload = {
            job: {
                id: "job123",
                user_id: "user123",
                quotes: []
            },
            listing: null,
            validatedData: {
                email: "<EMAIL>",
                rv_year: "2023",
                rv_make: "Keystone",
                rv_model: "Cougar"
            },
            user: mockFreeUser,
            isWarranty: false
        };

        it("should create membership offer for FREE user without existing offer", async () => {
            // Mock no existing offer
            mockPrisma.membershipOffer.findFirst.mockResolvedValue(null);
            mockPrisma.membershipOffer.create.mockResolvedValue({
                id: "offer123",
                user_id: "user123",
                offer_type: "JOB_SUBMISSION_50_OFF"
            } as any);

            await processJob(mockNonWarrantyPayload);

            expect(mockPrisma.membershipOffer.findFirst).toHaveBeenCalledWith({
                where: {
                    user_id: "user123",
                    is_active: true,
                    used_at: null,
                    expires_at: {
                        gt: expect.any(Date)
                    }
                }
            });

            expect(mockPrisma.membershipOffer.create).toHaveBeenCalledWith({
                data: {
                    user_id: "user123",
                    email: "<EMAIL>",
                    offer_type: "JOB_SUBMISSION_50_OFF",
                    discount_percentage: 50,
                    discount_amount: null,
                    description: "50% off Pro membership - Limited time offer for new service requests",
                    is_active: true,
                    expires_at: expect.any(Date)
                }
            });
        });

        it("should not create membership offer for FREE user with existing offer", async () => {
            // Mock existing offer
            mockPrisma.membershipOffer.findFirst.mockResolvedValue({
                id: "existing-offer123",
                user_id: "user123"
            } as any);

            await processJob(mockNonWarrantyPayload);

            expect(mockPrisma.membershipOffer.findFirst).toHaveBeenCalled();
            expect(mockPrisma.membershipOffer.create).not.toHaveBeenCalled();
        });

        it("should not create membership offer for paid users", async () => {
            const paidUserPayload = {
                ...mockNonWarrantyPayload,
                user: {
                    ...mockFreeUser,
                    membership_level: "STANDARD" as const
                }
            };

            await processJob(paidUserPayload);

            expect(mockPrisma.membershipOffer.findFirst).not.toHaveBeenCalled();
            expect(mockPrisma.membershipOffer.create).not.toHaveBeenCalled();
        });

        it("should handle membership offer creation errors gracefully", async () => {
            mockPrisma.membershipOffer.findFirst.mockResolvedValue(null);
            mockPrisma.membershipOffer.create.mockRejectedValue(new Error("Database error"));

            await processJob(mockNonWarrantyPayload);

            // Should not throw error, just log it
            expect(mockPrisma.membershipOffer.create).toHaveBeenCalled();
        });
    });
}); 