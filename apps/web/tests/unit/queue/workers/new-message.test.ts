import { mockPrisma } from "@/tests/mocks/prisma-mock";


jest.mock("@/lib/services", () => ({
	emailService: {
		send: jest.fn().mockResolvedValue(true)
	}
}));

import {
	NewMessagePayload,
	processNewMessage
} from "@/lib/queue/workers/new-message";
import { emailService } from "@/lib/services";

describe("New Message Worker", () => {
	const mockJob = {
		id: "job_123",
		user: {
			id: "user_123",
			email: "<EMAIL>",
			first_name: "<PERSON>",
			last_name: "<PERSON><PERSON>"
		}
	};

	const mockListing = {
		id: "listing_123",
		email: "<EMAIL>",
		first_name: "<PERSON>",
		last_name: "<PERSON>",
		business_name: "<PERSON>'s RV Repair"
	};

	const mockQuote = {
		id: "quote_123",
		listing: mockListing,
		job: mockJob,
		listing_id: "listing_123"
	};

	const mockMessage = {
		id: "message_123",
		content: "Test message content",
		sender_type: "PROVIDER" as "PROVIDER" | "USER",
		quote: mockQuote
	};

	beforeEach(() => {
		jest.clearAllMocks();
		// Mock the job fetch to return the job with user data
		mockPrisma.job.findUnique.mockResolvedValue({
			id: "job_123",
			sms_opt_in: true,
			user: {
				id: "user_123",
				email: "<EMAIL>",
				first_name: "John",
				last_name: "Doe"
			}
		});
		// Mock the message refetch to return the complete message structure
		mockPrisma.quoteMessage.findUnique.mockResolvedValue({
			id: "message_123",
			content: "Test message content",
			sender_type: "PROVIDER",
			quote: {
				id: "quote_123",
				listing_id: "listing_123",
				job: {
					id: "job_123",
					sms_opt_in: true,
					user: {
						id: "user_123",
						email: "<EMAIL>",
						first_name: "John",
						last_name: "Doe",
						avatar: null,
						phone: "+**********"
					}
				},
				listing: {
					id: "listing_123",
					email: "<EMAIL>",
					first_name: "Jane",
					last_name: "Smith",
					business_name: "Jane's RV Repair",
					phone: "+**********",
					sms_verified_at: new Date(),
					profile_image: null
				}
			}
		});
	});

	it("should send email to customer when message is from provider", async () => {
		const payload: NewMessagePayload = {
			message: {
				...mockMessage,
				sender_type: "PROVIDER"
			}
		};

		const result = await processNewMessage(payload);

		expect(emailService.send).toHaveBeenCalledWith({
			to: "<EMAIL>",
			replyTo: "<EMAIL>",
			subject: "New message from Jane's RV Repair",
			react: expect.any(Object)
		});
		expect(result).toEqual({ success: true });
	});

	it("should send email to provider when message is from customer", async () => {
		// Mock for user message
		mockPrisma.quoteMessage.findUnique.mockResolvedValueOnce({
			id: "message_123",
			content: "Test message content",
			sender_type: "USER",
			quote: {
				id: "quote_123",
				listing_id: "listing_123",
				job: {
					id: "job_123",
					sms_opt_in: true,
					user: {
						id: "user_123",
						email: "<EMAIL>",
						first_name: "John",
						last_name: "Doe",
						avatar: null,
						phone: "+**********"
					}
				},
				listing: {
					id: "listing_123",
					email: "<EMAIL>",
					first_name: "Jane",
					last_name: "Smith",
					business_name: "Jane's RV Repair",
					phone: "+**********",
					sms_verified_at: new Date(),
					profile_image: null
				}
			}
		});

		const payload: NewMessagePayload = {
			message: {
				...mockMessage,
				sender_type: "USER"
			}
		};

		const result = await processNewMessage(payload);

		expect(emailService.send).toHaveBeenCalledWith({
			to: "<EMAIL>",
			replyTo: "<EMAIL>",
			subject: "New message from John",
			react: expect.any(Object)
		});
		expect(result).toEqual({ success: true });
	});

	it("should use provider full name when no business name", async () => {
		// Mock for provider message without business name
		mockPrisma.quoteMessage.findUnique.mockResolvedValueOnce({
			id: "message_123",
			content: "Test message content",
			sender_type: "PROVIDER",
			quote: {
				id: "quote_123",
				listing_id: "listing_123",
				job: {
					id: "job_123",
					sms_opt_in: true,
					user: {
						id: "user_123",
						email: "<EMAIL>",
						first_name: "John",
						last_name: "Doe",
						avatar: null,
						phone: "+**********"
					}
				},
				listing: {
					id: "listing_123",
					email: "<EMAIL>",
					first_name: "Jane",
					last_name: "Smith",
					business_name: null, // No business name
					phone: "+**********",
					sms_verified_at: new Date(),
					profile_image: null
				}
			}
		});

		const payloadWithNoBusinessName: NewMessagePayload = {
			message: {
				...mockMessage,
				sender_type: "PROVIDER",
				quote: {
					...mockQuote,
					listing: {
						...mockListing,
						business_name: null
					}
				}
			}
		};

		await processNewMessage(payloadWithNoBusinessName);

		expect(emailService.send).toHaveBeenCalledWith({
			to: "<EMAIL>",
			replyTo: "<EMAIL>",
			subject: "New message from Jane Smith",
			react: expect.any(Object)
		});
	});

	it("should handle email sending errors", async () => {
		const payload: NewMessagePayload = {
			message: {
				...mockMessage,
				sender_type: "PROVIDER"
			}
		};

		const error = new Error("Email sending failed");
		(emailService.send as jest.Mock).mockRejectedValueOnce(error);

		await expect(processNewMessage(payload)).rejects.toThrow("Email sending failed");
	});


}); 