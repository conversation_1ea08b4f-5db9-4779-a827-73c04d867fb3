import { createMockRequest, mockBase<PERSON><PERSON><PERSON>, mockUser } from "@/tests/utils/api-test-utils";
import { prisma as mockPrisma } from "../../../mocks/prisma-mock";


// Import the handler after setting up mocks
import { POST as submitReview } from "@/app/api/reviews/submit/route";

describe("POST /api/reviews/submit", () => {
    const mockVerifiedUser = {
        ...mockUser,
        email_verified_at: new Date()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = null; // Reset user for each test
    });

    it("should return 401 if user is not authenticated", async () => {
        mockBaseHandler.user = null;

        const req = createMockRequest({
            method: "POST",
            url: "/api/reviews/submit",
            body: {
                title: "Great service!",
                content: "This was an excellent experience with the service provider.",
                ratings: {
                    overall: 5,
                    service: 5,
                    responsiveness: 5,
                    expertise: 5,
                    results: 5,
                    communication: 5
                },
                service_category: "repair",
                listingId: "listing-123"
            }
        });

        await submitReview(req);

        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "Authentication required" },
            401
        );
    });

    it("should create review with in_moderation status for verified users with FREE membership", async () => {
        mockBaseHandler.user = mockVerifiedUser;

        // Mock finding no existing review
        (mockPrisma.review.findFirst as jest.Mock).mockResolvedValue(null);

        // Mock creating the review
        const mockCreatedReview = {
            id: "review-123",
            status: "in_moderation",
            user_id: mockVerifiedUser.id
        };
        (mockPrisma.review.create as jest.Mock).mockResolvedValue(mockCreatedReview);

        const req = createMockRequest({
            method: "POST",
            url: "/api/reviews/submit",
            body: {
                title: "Great service!",
                content: "This was an excellent experience with the service provider.",
                ratings: {
                    overall: 5,
                    service: 5,
                    responsiveness: 5,
                    expertise: 5,
                    results: 5,
                    communication: 5
                },
                service_category: "repair",
                listingId: "listing-123"
            }
        });

        await submitReview(req);

        expect(mockPrisma.review.create).toHaveBeenCalledWith({
            data: {
                listing_id: "listing-123",
                user_id: mockVerifiedUser.id,
                title: "Great service!",
                content: "This was an excellent experience with the service provider.",
                overall: 5,
                service: 5,
                responsiveness: 5,
                expertise: 5,
                results: 5,
                communication: 5,
                service_category: "repair",
                first_name: mockVerifiedUser.first_name,
                last_name: mockVerifiedUser.last_name,
                email: mockVerifiedUser.email,
                status: "in_moderation"
            }
        });

        expect(mockBaseHandler.respond).toHaveBeenCalledWith({
            success: true,
            redirect: `/verify-review?reviewId=review-123&rating=5`,
            message: "Review submitted successfully and is now under moderation."
        });
    });

    it("should create review with in_moderation status for verified users with PAID membership", async () => {
        const mockPaidUser = {
            ...mockVerifiedUser,
            membership_level: "PAID"
        };
        mockBaseHandler.user = mockPaidUser;

        // Mock finding no existing review
        (mockPrisma.review.findFirst as jest.Mock).mockResolvedValue(null);

        // Mock creating the review
        const mockCreatedReview = {
            id: "review-123",
            status: "in_moderation",
            user_id: mockPaidUser.id
        };
        (mockPrisma.review.create as jest.Mock).mockResolvedValue(mockCreatedReview);

        const req = createMockRequest({
            method: "POST",
            url: "/api/reviews/submit",
            body: {
                title: "Great service!",
                content: "This was an excellent experience with the service provider.",
                ratings: {
                    overall: 5,
                    service: 5,
                    responsiveness: 5,
                    expertise: 5,
                    results: 5,
                    communication: 5
                },
                service_category: "repair",
                listingId: "listing-123"
            }
        });

        await submitReview(req);

        expect(mockPrisma.review.create).toHaveBeenCalledWith({
            data: {
                listing_id: "listing-123",
                user_id: mockPaidUser.id,
                title: "Great service!",
                content: "This was an excellent experience with the service provider.",
                overall: 5,
                service: 5,
                responsiveness: 5,
                expertise: 5,
                results: 5,
                communication: 5,
                service_category: "repair",
                first_name: mockPaidUser.first_name,
                last_name: mockPaidUser.last_name,
                email: mockPaidUser.email,
                status: "in_moderation"
            }
        });

        expect(mockBaseHandler.respond).toHaveBeenCalledWith({
            success: true,
            redirect: `/review-submitted?email=${encodeURIComponent(mockPaidUser.email)}&verified=true`,
            message: "Review submitted successfully and is now under moderation."
        });
    });

    it("should create review with pending_verification status for unverified users", async () => {
        const mockUnverifiedUser = {
            ...mockUser,
            email_verified_at: null
        };
        mockBaseHandler.user = mockUnverifiedUser;

        // Mock finding no existing review
        (mockPrisma.review.findFirst as jest.Mock).mockResolvedValue(null);

        // Mock creating the review
        const mockCreatedReview = {
            id: "review-123",
            status: "pending_verification",
            user_id: mockUnverifiedUser.id
        };
        (mockPrisma.review.create as jest.Mock).mockResolvedValue(mockCreatedReview);

        const req = createMockRequest({
            method: "POST",
            url: "/api/reviews/submit",
            body: {
                title: "Great service!",
                content: "This was an excellent experience with the service provider.",
                ratings: {
                    overall: 5,
                    service: 5,
                    responsiveness: 5,
                    expertise: 5,
                    results: 5,
                    communication: 5
                },
                service_category: "repair",
                listingId: "listing-123"
            }
        });

        await submitReview(req);

        expect(mockPrisma.review.create).toHaveBeenCalledWith({
            data: {
                listing_id: "listing-123",
                user_id: mockUnverifiedUser.id,
                title: "Great service!",
                content: "This was an excellent experience with the service provider.",
                overall: 5,
                service: 5,
                responsiveness: 5,
                expertise: 5,
                results: 5,
                communication: 5,
                service_category: "repair",
                first_name: mockUnverifiedUser.first_name,
                last_name: mockUnverifiedUser.last_name,
                email: mockUnverifiedUser.email,
                status: "pending_verification"
            }
        });

        expect(mockBaseHandler.respond).toHaveBeenCalledWith({
            success: true,
            redirect: `/review-submitted?email=${encodeURIComponent(mockUnverifiedUser.email)}&verified=false`,
            message: "Review submitted successfully. Please check your email to verify your review."
        });
    });

    it("should return error if user has already reviewed this listing", async () => {
        mockBaseHandler.user = mockVerifiedUser;

        // Mock finding an existing review
        (mockPrisma.review.findFirst as jest.Mock).mockResolvedValue({
            id: "existing-review-123"
        });

        const req = createMockRequest({
            method: "POST",
            url: "/api/reviews/submit",
            body: {
                title: "Great service!",
                content: "This was an excellent experience with the service provider.",
                ratings: {
                    overall: 5,
                    service: 5,
                    responsiveness: 5,
                    expertise: 5,
                    results: 5,
                    communication: 5
                },
                service_category: "repair",
                listingId: "listing-123"
            }
        });

        await submitReview(req);

        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "You have already reviewed this listing" },
            400
        );
    });
}); 