import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseH<PERSON>ler,
    mockUser
} from "@/tests/utils/api-test-utils";



// Import handler after setting up mocks
import { DELETE } from "@/app/api/reviews/[id]/route";
import { z } from "zod";

const deleteReviewSchema = z.object({
    reason: z.string().min(1, "Deletion reason is required"),
    admin_notes: z.string().optional()
});

describe("Review Deletion Handler", () => {
    const mockAdminUser = {
        ...mockUser,
        role: "ADMIN"
    };

    const mockReview = {
        id: "review123",
        title: "Test Review Title",
        content: "Test review content",
        source: "rvhelp",
        google_review_id: null,
        google_place_id: null,
        mrr_review_id: null,
        profile_photo: null,
        rating: 4,
        overall: 5,
        status: "active",
        user_id: "user123",
        listing_id: "listing123",
        email: "<EMAIL>",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        created_at: "2025-03-22T16:00:50.585Z",
        updated_at: "2025-03-22T16:00:50.585Z",
        moderated_by_id: null,
        moderated_at: null,
        moderation_notes: null,
        listing: {
            id: "listing123",
            business_name: "Test Business",
            email: "<EMAIL>",
            slug: "test-business"
        }
    };

    const deleteData = {
        reason: "spam",
        admin_notes: "Fake review detected"
    };

    const validatedData = deleteReviewSchema.parse(deleteData);

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockAdminUser;
        mockBaseHandler.isAdmin = true;
        // Reset mock implementations
        mockPrisma.review.findUnique.mockReset();
        mockPrisma.review.delete.mockReset();
        mockPrisma.auditLog.create.mockReset();
    });

    it("should delete review successfully", async () => {
        // Mock finding the review
        mockPrisma.review.findUnique.mockResolvedValue(mockReview);

        // Mock successful deletion
        mockPrisma.review.delete.mockResolvedValue(mockReview);

        // Mock audit log creation
        mockPrisma.auditLog.create.mockResolvedValue({
            id: "audit123",
            action: "REVIEW_DELETED",
            entity_type: "REVIEW",
            entity_id: mockReview.id,
            user_id: mockAdminUser.id,
            changes: {
                review_id: mockReview.id,
                business_name: mockReview.listing.business_name,
                reason: validatedData.reason,
                admin_notes: `Deleted by admin: ${mockAdminUser.email}\n\n${validatedData.admin_notes}`
            },
            created_at: new Date()
        });

        const req = createMockRequest({
            method: "DELETE",
            url: `/api/reviews/${mockReview.id}`,
            params: { id: mockReview.id },
            validatedData
        });

        const handler = DELETE.bind({
            ...mockBaseHandler,
            user: mockAdminUser,
            validatedData
        });

        await handler(req, {
            validatedData,
            params: { id: mockReview.id }
        });

        // Verify review was found
        expect(mockPrisma.review.findUnique).toHaveBeenCalledWith({
            where: { id: mockReview.id },
            include: {
                listing: {
                    select: {
                        business_name: true
                    }
                }
            }
        });

        // Verify review was deleted
        expect(mockPrisma.review.delete).toHaveBeenCalledWith({
            where: { id: mockReview.id }
        });

        // Verify audit log was created
        expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
            data: {
                action: "REVIEW_DELETED",
                entity_type: "REVIEW",
                entity_id: mockReview.id,
                user_id: mockAdminUser.id,
                changes: {
                    review_id: mockReview.id,
                    business_name: mockReview.listing.business_name,
                    reason: validatedData.reason,
                    admin_notes: `Deleted by admin: ${mockAdminUser.email}\n\n${validatedData.admin_notes}`
                }
            }
        });

        // Verify success response
        expect(mockBaseHandler.respond).toHaveBeenCalledWith({
            success: true,
            message: "Review deleted successfully"
        });
    });

    it("should delete review without admin notes", async () => {
        const deleteDataWithoutNotes = {
            reason: "inappropriate"
        };

        // Mock finding the review
        mockPrisma.review.findUnique.mockResolvedValue(mockReview);

        // Mock successful deletion
        mockPrisma.review.delete.mockResolvedValue(mockReview);

        // Mock audit log creation
        mockPrisma.auditLog.create.mockResolvedValue({
            id: "audit123",
            action: "REVIEW_DELETED",
            entity_type: "REVIEW",
            entity_id: mockReview.id,
            user_id: mockAdminUser.id,
            changes: {
                review_id: mockReview.id,
                business_name: mockReview.listing.business_name,
                reason: deleteDataWithoutNotes.reason,
                admin_notes: `Deleted by admin: ${mockAdminUser.email}`
            },
            created_at: new Date()
        });

        const req = createMockRequest({
            method: "DELETE",
            url: `/api/reviews/${mockReview.id}`,
            params: { id: mockReview.id },
            validatedData: deleteDataWithoutNotes
        });

        const handler = DELETE.bind({
            ...mockBaseHandler,
            user: mockAdminUser,
            validatedData: deleteDataWithoutNotes
        });

        await handler(req, {
            validatedData: deleteDataWithoutNotes,
            params: { id: mockReview.id }
        });

        // Verify audit log was created without admin notes
        expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
            data: {
                action: "REVIEW_DELETED",
                entity_type: "REVIEW",
                entity_id: mockReview.id,
                user_id: mockAdminUser.id,
                changes: {
                    review_id: mockReview.id,
                    business_name: mockReview.listing.business_name,
                    reason: deleteDataWithoutNotes.reason,
                    admin_notes: `Deleted by admin: ${mockAdminUser.email}`
                }
            }
        });
    });

    it("should return 404 when review not found", async () => {
        // Mock review not found
        mockPrisma.review.findUnique.mockResolvedValue(null);

        const req = createMockRequest({
            method: "DELETE",
            url: `/api/reviews/${mockReview.id}`,
            params: { id: mockReview.id },
            validatedData
        });

        const handler = DELETE.bind({
            ...mockBaseHandler,
            user: mockAdminUser,
            validatedData
        });

        await handler(req, {
            validatedData,
            params: { id: mockReview.id }
        });

        // Verify error response
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "Review not found" },
            404
        );

        // Verify review was not deleted
        expect(mockPrisma.review.delete).not.toHaveBeenCalled();
        expect(mockPrisma.auditLog.create).not.toHaveBeenCalled();
    });

    it("should handle database errors gracefully", async () => {
        // Mock finding the review
        mockPrisma.review.findUnique.mockResolvedValue(mockReview);

        // Mock database error during deletion
        mockPrisma.review.delete.mockRejectedValue(new Error("Database connection failed"));

        const req = createMockRequest({
            method: "DELETE",
            url: `/api/reviews/${mockReview.id}`,
            params: { id: mockReview.id },
            validatedData
        });

        const handler = DELETE.bind({
            ...mockBaseHandler,
            user: mockAdminUser,
            validatedData
        });

        await handler(req, {
            validatedData,
            params: { id: mockReview.id }
        });

        // Verify error response
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "Failed to delete review" },
            500
        );
    });

    it("should handle audit log creation errors gracefully", async () => {
        // Mock finding the review
        mockPrisma.review.findUnique.mockResolvedValue(mockReview);

        // Mock successful deletion
        mockPrisma.review.delete.mockResolvedValue(mockReview);

        // Mock audit log creation error
        mockPrisma.auditLog.create.mockRejectedValue(new Error("Audit log creation failed"));

        const req = createMockRequest({
            method: "DELETE",
            url: `/api/reviews/${mockReview.id}`,
            params: { id: mockReview.id },
            validatedData
        });

        const handler = DELETE.bind({
            ...mockBaseHandler,
            user: mockAdminUser,
            validatedData
        });

        await handler(req, {
            validatedData,
            params: { id: mockReview.id }
        });

        // Verify error response
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "Failed to delete review" },
            500
        );
    });

    it("should validate required reason field", async () => {
        const invalidData = {
            reason: "",
            admin_notes: "Some notes"
        };

        // This should throw a validation error
        expect(() => deleteReviewSchema.parse(invalidData)).toThrow("Deletion reason is required");
    });

    it("should allow optional admin notes", async () => {
        const validDataWithoutNotes = {
            reason: "spam"
        };

        // This should not throw an error
        expect(() => deleteReviewSchema.parse(validDataWithoutNotes)).not.toThrow();
    });
}); 