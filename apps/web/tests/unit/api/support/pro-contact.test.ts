import { mockPrisma } from "@/tests/mocks/prisma-mock";

// Mock email service
jest.mock("@/lib/services", () => ({
	emailService: {
		send: jest.fn().mockResolvedValue({ success: true })
	}
}));

// Import the email service to test directly
import { emailService } from "@/lib/services";

const mockEmailService = emailService as jest.Mocked<typeof emailService>;

describe("Pro Contact Email Service", () => {
	const mockValidData = {
		name: "<PERSON>",
		email: "<EMAIL>",
		phone: "************",
		location: {
			address: "123 Main St, Anytown, CA 12345",
			latitude: 37.7749,
			longitude: -122.4194,
			city: "Anytown",
			state: "CA",
			zip: "12345",
			country: "US"
		},
		subject: "Need help with my RV",
		message: "I'm having trouble with my RV's electrical system."
	};

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("Email Content Generation", () => {
		it("should generate premium user email content correctly", async () => {
			const membershipLevel = "PREMIUM";
			const isPaid = true;
			const isPremium = true;

			// Test the email content generation logic
			const enhancedMessage = `
${mockValidData.message}

--- Pro Member Location Information ---
Address: ${mockValidData.location.address}
City/State: ${mockValidData.location.city}, ${mockValidData.location.state}
ZIP: ${mockValidData.location.zip}
Coordinates: ${mockValidData.location.latitude}, ${mockValidData.location.longitude}

--- Support Priority ---
Membership Level: ${membershipLevel}
Priority Response: Premium (2-hour)
			`.trim();

			const emailContent = `
New PRO MEMBER support request from ${mockValidData.name}
Email: ${mockValidData.email}
Phone: ${mockValidData.phone}
Membership: ${membershipLevel}
Location: ${mockValidData.location.address}
Subject: ${mockValidData.subject}

Message:
${mockValidData.message}

Location Details:
- Address: ${mockValidData.location.address}
- City/State: ${mockValidData.location.city}, ${mockValidData.location.state}
- ZIP: ${mockValidData.location.zip}
- Coordinates: ${mockValidData.location.latitude}, ${mockValidData.location.longitude}

Expected Response Time: 2 hours
			`;

			// Verify the content is generated correctly
			expect(enhancedMessage).toContain("Premium (2-hour)");
			expect(enhancedMessage).toContain("Address: 123 Main St, Anytown, CA 12345");
			expect(enhancedMessage).toContain("City/State: Anytown, CA");
			expect(enhancedMessage).toContain("ZIP: 12345");
			expect(enhancedMessage).toContain("Coordinates: 37.7749, -122.4194");

			expect(emailContent).toContain("PRO MEMBER");
			expect(emailContent).toContain("Expected Response Time: 2 hours");
			expect(emailContent).toContain("Membership: PREMIUM");
		});

		it("should generate standard user email content correctly", async () => {
			const membershipLevel = "STANDARD";
			const isPaid = true;
			const isPremium = false;

			const emailContent = `
New PRO MEMBER support request from ${mockValidData.name}
Email: ${mockValidData.email}
Phone: ${mockValidData.phone}
Membership: ${membershipLevel}
Location: ${mockValidData.location.address}
Subject: ${mockValidData.subject}

Message:
${mockValidData.message}

Location Details:
- Address: ${mockValidData.location.address}
- City/State: ${mockValidData.location.city}, ${mockValidData.location.state}
- ZIP: ${mockValidData.location.zip}
- Coordinates: ${mockValidData.location.latitude}, ${mockValidData.location.longitude}

Expected Response Time: 4 hours
			`;

			expect(emailContent).toContain("PRO MEMBER");
			expect(emailContent).toContain("Expected Response Time: 4 hours");
			expect(emailContent).toContain("Membership: STANDARD");
		});

		it("should generate free user email content correctly", async () => {
			const membershipLevel = "FREE";
			const isPaid = false;
			const isPremium = false;

			const emailContent = `
New support request from ${mockValidData.name}
Email: ${mockValidData.email}
Phone: ${mockValidData.phone}
Membership: ${membershipLevel}
Location: ${mockValidData.location.address}
Subject: ${mockValidData.subject}

Message:
${mockValidData.message}

Location Details:
- Address: ${mockValidData.location.address}
- City/State: ${mockValidData.location.city}, ${mockValidData.location.state}
- ZIP: ${mockValidData.location.zip}
- Coordinates: ${mockValidData.location.latitude}, ${mockValidData.location.longitude}

Expected Response Time: 24 hours
			`;

			expect(emailContent).not.toContain("PRO MEMBER");
			expect(emailContent).toContain("Expected Response Time: 24 hours");
			expect(emailContent).toContain("Membership: FREE");
		});
	});

	describe("Email Subject Generation", () => {
		it("should generate correct subject for premium users", () => {
			const isPaid = true;
			const subject = `${isPaid ? "[PRO MEMBER] " : ""}Support Request: ${mockValidData.subject}`;

			expect(subject).toBe("[PRO MEMBER] Support Request: Need help with my RV");
		});

		it("should generate correct subject for standard users", () => {
			const isPaid = true;
			const subject = `${isPaid ? "[PRO MEMBER] " : ""}Support Request: ${mockValidData.subject}`;

			expect(subject).toBe("[PRO MEMBER] Support Request: Need help with my RV");
		});

		it("should generate correct subject for free users", () => {
			const isPaid = false;
			const subject = `${isPaid ? "[PRO MEMBER] " : ""}Support Request: ${mockValidData.subject}`;

			expect(subject).toBe("Support Request: Need help with my RV");
		});
	});

	describe("Location Information Handling", () => {
		it("should handle complete location information", () => {
			const location = mockValidData.location;

			const locationDetails = `
- Address: ${location.address}
- City/State: ${location.city ? `${location.city}, ${location.state}` : "Not provided"}
- ZIP: ${location.zip || "Not provided"}
- Coordinates: ${location.latitude}, ${location.longitude}
			`;

			expect(locationDetails).toContain("Address: 123 Main St, Anytown, CA 12345");
			expect(locationDetails).toContain("City/State: Anytown, CA");
			expect(locationDetails).toContain("ZIP: 12345");
			expect(locationDetails).toContain("Coordinates: 37.7749, -122.4194");
		});

		it("should handle missing location fields gracefully", () => {
			const minimalLocation = {
				address: "123 Main St",
				latitude: 37.7749,
				longitude: -122.4194
				// Missing city, state, zip, country
			};

			const locationDetails = `
- Address: ${minimalLocation.address}
- City/State: ${minimalLocation.city ? `${minimalLocation.city}, ${minimalLocation.state}` : "Not provided"}
- ZIP: ${minimalLocation.zip || "Not provided"}
- Coordinates: ${minimalLocation.latitude}, ${minimalLocation.longitude}
			`;

			expect(locationDetails).toContain("Address: 123 Main St");
			expect(locationDetails).toContain("City/State: Not provided");
			expect(locationDetails).toContain("ZIP: Not provided");
			expect(locationDetails).toContain("Coordinates: 37.7749, -122.4194");
		});
	});

	describe("Membership Level Logic", () => {
		it("should correctly identify paid memberships", () => {
			const premiumMembership = "PREMIUM";
			const standardMembership = "STANDARD";
			const freeMembership = "FREE";

			const isPaidPremium = premiumMembership === "STANDARD" || premiumMembership === "PREMIUM";
			const isPaidStandard = standardMembership === "STANDARD" || standardMembership === "PREMIUM";
			const isPaidFree = freeMembership === "STANDARD" || freeMembership === "PREMIUM";

			expect(isPaidPremium).toBe(true);
			expect(isPaidStandard).toBe(true);
			expect(isPaidFree).toBe(false);
		});

		it("should correctly identify premium membership", () => {
			const premiumMembership = "PREMIUM";
			const standardMembership = "STANDARD";
			const freeMembership = "FREE";

			const isPremiumPremium = premiumMembership === "PREMIUM";
			const isPremiumStandard = standardMembership === "PREMIUM";
			const isPremiumFree = freeMembership === "PREMIUM";

			expect(isPremiumPremium).toBe(true);
			expect(isPremiumStandard).toBe(false);
			expect(isPremiumFree).toBe(false);
		});

		it("should calculate correct response times", () => {
			const getResponseTime = (membershipLevel: string) => {
				const isPaid = membershipLevel === "STANDARD" || membershipLevel === "PREMIUM";
				const isPremium = membershipLevel === "PREMIUM";

				if (isPaid) {
					return isPremium ? "2 hours" : "4 hours";
				}
				return "24 hours";
			};

			expect(getResponseTime("PREMIUM")).toBe("2 hours");
			expect(getResponseTime("STANDARD")).toBe("4 hours");
			expect(getResponseTime("FREE")).toBe("24 hours");
		});
	});

	describe("User Phone Update Logic", () => {
		it("should update user phone when not present", async () => {
			const user = {
				id: "user123",
				phone: null
			};
			const phone = "************";

			// Simulate the logic that would update the phone
			if (user && !user.phone) {
				await mockPrisma.user.update({
					where: { id: user.id },
					data: { phone }
				});
			}

			expect(mockPrisma.user.update).toHaveBeenCalledWith({
				where: { id: "user123" },
				data: { phone: "************" }
			});
		});

		it("should not update user phone when already present", async () => {
			const user = {
				id: "user123",
				phone: "existing-phone"
			};
			const phone = "************";

			// Simulate the logic that would update the phone
			if (user && !user.phone) {
				await mockPrisma.user.update({
					where: { id: user.id },
					data: { phone }
				});
			}

			expect(mockPrisma.user.update).not.toHaveBeenCalled();
		});
	});

	describe("Email Service Integration", () => {
		it("should call email service with correct parameters", async () => {
			const membershipLevel = "PREMIUM";
			const isPaid = true;
			const isPremium = true;

			await emailService.send({
				from: "RVHelp Website <<EMAIL>>",
				to: "<EMAIL>",
				cc: mockValidData.email,
				subject: `${isPaid ? "[PRO MEMBER] " : ""}Support Request: ${mockValidData.subject}`,
				text: `Test email content`
			});

			expect(mockEmailService.send).toHaveBeenCalledWith({
				from: "RVHelp Website <<EMAIL>>",
				to: "<EMAIL>",
				cc: mockValidData.email,
				subject: "[PRO MEMBER] Support Request: Need help with my RV",
				text: "Test email content"
			});
		});
	});
});
