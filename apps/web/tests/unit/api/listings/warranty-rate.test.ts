import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "@/tests/utils/api-test-utils";

jest.mock("@/lib/services/listing-access.service", () => ({
    hasListingAccess: jest.fn().mockResolvedValue(true)
}));

// Import after mocks are set up
import { PUT } from "@/app/api/listings/[id]/warranty-rate/route";
import { hasListingAccess } from "@/lib/services/listing-access.service";

describe("PUT /api/listings/[id]/warranty-rate", () => {
    const mockListing = {
        id: "listing123",
        owner_id: "owner123",
        business_name: "Test Business",
        pricing_settings: {
            hourly_rate: 100,
            dispatch_fee: 50,
            warranty_rate: null
        }
    };

    const updatedListing = {
        id: "listing123",
        pricing_settings: {
            hourly_rate: 100,
            dispatch_fee: 50,
            warranty_rate: 151
        }
    };

    beforeEach(() => {
        jest.clearAllMocks();
        // Set up authentication
        mockBaseHandler.user = mockUser;
        mockBaseHandler.session = { user: mockUser };
        mockBaseHandler.isAuthenticated = true;
        mockBaseHandler.isAdmin = false;

        // Set up default mocks
        mockPrisma.listing.findUnique.mockResolvedValue(mockListing);
        mockPrisma.listing.update.mockResolvedValue(updatedListing);
        (hasListingAccess as jest.Mock).mockResolvedValue(true);
    });

    it("should successfully update warranty rate with valid data", async () => {
        const req = createMockRequest({
            method: "PUT",
            url: `/api/listings/${mockListing.id}/warranty-rate`,
            params: { id: mockListing.id },
            body: { warranty_rate: 151 }
        });

        const response = await PUT.bind({
            ...mockBaseHandler,
            params: { id: mockListing.id },
            validatedData: { warranty_rate: 151 }
        })(req, { params: { id: mockListing.id } });

        expect(response.status).toBe(200);
        expect(await response.json()).toEqual({
            data: updatedListing,
            message: "Warranty rate updated successfully"
        });

        expect(mockPrisma.listing.update).toHaveBeenCalledWith({
            where: { id: mockListing.id },
            data: {
                pricing_settings: {
                    hourly_rate: 100,
                    dispatch_fee: 50,
                    warranty_rate: 151
                }
            },
            select: {
                id: true,
                pricing_settings: true
            }
        });
    });

    it("should merge warranty rate with existing pricing settings", async () => {
        const listingWithComplexPricing = {
            ...mockListing,
            pricing_settings: {
                hourly_rate: 125,
                dispatch_fee: 75,
                travel_rate_per_mile: 2.5,
                emergency_hourly_rate: 200,
                warranty_rate: 140
            }
        };

        mockPrisma.listing.findUnique.mockResolvedValue(listingWithComplexPricing);

        const req = createMockRequest({
            method: "PUT",
            url: `/api/listings/${mockListing.id}/warranty-rate`,
            params: { id: mockListing.id },
            body: { warranty_rate: 175 }
        });

        await PUT.bind({
            ...mockBaseHandler,
            params: { id: mockListing.id },
            validatedData: { warranty_rate: 175 }
        })(req, { params: { id: mockListing.id } });

        expect(mockPrisma.listing.update).toHaveBeenCalledWith({
            where: { id: mockListing.id },
            data: {
                pricing_settings: {
                    hourly_rate: 125,
                    dispatch_fee: 75,
                    travel_rate_per_mile: 2.5,
                    emergency_hourly_rate: 200,
                    warranty_rate: 175
                }
            },
            select: {
                id: true,
                pricing_settings: true
            }
        });
    });

    it("should handle listings with null pricing_settings", async () => {
        const listingWithNullPricing = {
            ...mockListing,
            pricing_settings: null
        };

        mockPrisma.listing.findUnique.mockResolvedValue(listingWithNullPricing);

        const req = createMockRequest({
            method: "PUT",
            url: `/api/listings/${mockListing.id}/warranty-rate`,
            params: { id: mockListing.id },
            body: { warranty_rate: 160 }
        });

        await PUT.bind({
            ...mockBaseHandler,
            params: { id: mockListing.id },
            validatedData: { warranty_rate: 160 }
        })(req, { params: { id: mockListing.id } });

        expect(mockPrisma.listing.update).toHaveBeenCalledWith({
            where: { id: mockListing.id },
            data: {
                pricing_settings: {
                    warranty_rate: 160
                }
            },
            select: {
                id: true,
                pricing_settings: true
            }
        });
    });

    it("should return 404 when listing not found", async () => {
        mockPrisma.listing.findUnique.mockResolvedValue(null);

        const req = createMockRequest({
            method: "PUT",
            url: `/api/listings/nonexistent/warranty-rate`,
            params: { id: "nonexistent" },
            body: { warranty_rate: 151 }
        });

        const response = await PUT.bind({
            ...mockBaseHandler,
            params: { id: "nonexistent" },
            validatedData: { warranty_rate: 151 }
        })(req, { params: { id: "nonexistent" } });

        expect(response.status).toBe(404);
        expect(await response.json()).toEqual({
            error: "Listing not found"
        });

        expect(mockPrisma.listing.update).not.toHaveBeenCalled();
    });

    it("should return 401 when user lacks access (not owner/manager)", async () => {
        (hasListingAccess as jest.Mock).mockResolvedValue(false);
        mockBaseHandler.isAdmin = false;

        const req = createMockRequest({
            method: "PUT",
            url: `/api/listings/${mockListing.id}/warranty-rate`,
            params: { id: mockListing.id },
            body: { warranty_rate: 151 }
        });

        const response = await PUT.bind({
            ...mockBaseHandler,
            params: { id: mockListing.id },
            validatedData: { warranty_rate: 151 }
        })(req, { params: { id: mockListing.id } });

        expect(response.status).toBe(401);
        expect(await response.json()).toEqual({
            error: "Unauthorized"
        });

        expect(hasListingAccess).toHaveBeenCalledWith({
            user: mockUser,
            listingId: mockListing.id,
            requiredRole: "MANAGER"
        });

        expect(mockPrisma.listing.update).not.toHaveBeenCalled();
    });

    it("should allow admin access even without listing access", async () => {
        (hasListingAccess as jest.Mock).mockResolvedValue(false);
        mockBaseHandler.isAdmin = true;

        const req = createMockRequest({
            method: "PUT",
            url: `/api/listings/${mockListing.id}/warranty-rate`,
            params: { id: mockListing.id },
            body: { warranty_rate: 180 }
        });

        const response = await PUT.bind({
            ...mockBaseHandler,
            params: { id: mockListing.id },
            validatedData: { warranty_rate: 180 }
        })(req, { params: { id: mockListing.id } });

        expect(response.status).toBe(200);
        expect(mockPrisma.listing.update).toHaveBeenCalled();
    });

    it("should return 401 when user is not authenticated", async () => {
        mockBaseHandler.user = null;
        mockBaseHandler.isAuthenticated = false;

        const req = createMockRequest({
            method: "PUT",
            url: `/api/listings/${mockListing.id}/warranty-rate`,
            params: { id: mockListing.id },
            body: { warranty_rate: 151 }
        });

        const response = await PUT.bind({
            ...mockBaseHandler,
            user: null,
            params: { id: mockListing.id },
            validatedData: { warranty_rate: 151 }
        })(req, { params: { id: mockListing.id } });

        expect(response.status).toBe(401);
        expect(await response.json()).toEqual({
            error: "Unauthorized"
        });

        expect(mockPrisma.listing.findUnique).not.toHaveBeenCalled();
        expect(mockPrisma.listing.update).not.toHaveBeenCalled();
    });


    it("should check for MANAGER role requirement", async () => {
        // Verify that hasListingAccess is called with MANAGER role requirement
        const req = createMockRequest({
            method: "PUT",
            url: `/api/listings/${mockListing.id}/warranty-rate`,
            params: { id: mockListing.id },
            body: { warranty_rate: 151 }
        });

        await PUT.bind({
            ...mockBaseHandler,
            params: { id: mockListing.id },
            validatedData: { warranty_rate: 151 }
        })(req, { params: { id: mockListing.id } });

        expect(hasListingAccess).toHaveBeenCalledWith({
            user: mockUser,
            listingId: mockListing.id,
            requiredRole: "MANAGER"
        });
    });

    it("should handle database errors gracefully", async () => {
        mockPrisma.listing.update.mockRejectedValue(new Error("Database error"));

        const req = createMockRequest({
            method: "PUT",
            url: `/api/listings/${mockListing.id}/warranty-rate`,
            params: { id: mockListing.id },
            body: { warranty_rate: 151 }
        });

        // The baseHandler should catch and handle database errors
        // In a real scenario, this would return a 500 error
        await expect(
            PUT.bind({
                ...mockBaseHandler,
                params: { id: mockListing.id },
                validatedData: { warranty_rate: 151 }
            })(req, { params: { id: mockListing.id } })
        ).rejects.toThrow("Database error");

        expect(mockPrisma.listing.findUnique).toHaveBeenCalled();
        expect(mockPrisma.listing.update).toHaveBeenCalled();
    });
});