import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
	createMockRequest,
	mockBase<PERSON><PERSON>ler,
	mockUser
} from "@/tests/utils/api-test-utils";


// Mock the EmailNewsletterService
jest.mock("@/lib/services/emailNewsletter.service", () => ({
	EmailNewsletterService: {
		syncNewsletterSubscriber: jest.fn()
	}
}));

// Mock the FirstPromoterService
jest.mock("@/lib/services/first-promoter.service", () => ({
	FirstPromoterService: {
		trackLeadMagnet: jest.fn()
	}
}));

// Now import handlers after mocks are set up
import { POST } from "@/app/api/newsletter/subscribe-lead-magnet/route";
import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";
import { FirstPromoterService } from "@/lib/services/first-promoter.service";

// Get the mocked service
const mockEmailNewsletterService = EmailNewsletterService as jest.Mocked<
	typeof EmailNewsletterService
>;
const mockFirstPromoterService = FirstPromoterService as jest.Mocked<
	typeof FirstPromoterService
>;

describe("Newsletter Subscribe Lead Magnet API", () => {
	const mockLeadMagnet = {
		id: "lm123",
		title: "Ultimate RV Maintenance Guide",
		description: "A comprehensive guide to RV maintenance",
		image: "https://example.com/images/guide.jpg",
		newsletter_tags: ["RV Maintenance", "Guide", "Maintenance Tips"],
		status: "active",
		created_at: new Date(),
		updated_at: new Date()
	};

	const validSubscriptionData = {
		email: "<EMAIL>",
		first_name: "John",
		last_name: "Doe",
		lead_magnet_id: mockLeadMagnet.id
	};

	beforeEach(() => {
		jest.clearAllMocks();
		mockPrisma.leadMagnet.findUnique.mockResolvedValue(mockLeadMagnet);
		mockEmailNewsletterService.syncNewsletterSubscriber.mockResolvedValue({
			success: true
		});
		mockFirstPromoterService.trackLeadMagnet.mockResolvedValue({
			success: true
		});
		mockBaseHandler.user = null; // Default to unauthenticated user
		mockBaseHandler.isAuthenticated = false;
	});

	describe("POST /api/newsletter/subscribe-lead-magnet", () => {
		it("should call FirstPromoter.trackLeadMagnet for unauthenticated user", async () => {
			const req = createMockRequest({
				method: "POST",
				url: "/api/newsletter/subscribe-lead-magnet",
				validatedData: validSubscriptionData
			});

			const response = await POST(req);

			expect(mockFirstPromoterService.trackLeadMagnet).toHaveBeenCalledTimes(1);
			expect(mockFirstPromoterService.trackLeadMagnet).toHaveBeenCalledWith({
				email: validSubscriptionData.email,
				userId: undefined, // No user ID for unauthenticated user
				leadMagnetTitle: mockLeadMagnet.title,
				req: expect.any(Object) // Request object is now passed
			});

			expect(response.status).toBe(200);
		});

		it("should call FirstPromoter.trackLeadMagnet for authenticated user", async () => {
			mockBaseHandler.user = mockUser;
			mockBaseHandler.isAuthenticated = true;
			mockPrisma.user.update.mockResolvedValue({
				...mockUser,
				newsletter_subscribed: true
			});

			const req = createMockRequest({
				method: "POST",
				url: "/api/newsletter/subscribe-lead-magnet",
				validatedData: validSubscriptionData
			});

			const response = await POST(req);

			expect(mockFirstPromoterService.trackLeadMagnet).toHaveBeenCalledTimes(1);
			expect(mockFirstPromoterService.trackLeadMagnet).toHaveBeenCalledWith({
				email: validSubscriptionData.email,
				userId: mockUser.id,
				leadMagnetTitle: mockLeadMagnet.title,
				req: expect.any(Object) // Request object is now passed
			});

			expect(response.status).toBe(200);
		});

		it("should subscribe user and return lead magnet info for unauthenticated user", async () => {
			const req = createMockRequest({
				method: "POST",
				url: "/api/newsletter/subscribe-lead-magnet",
				validatedData: validSubscriptionData
			});

			const response = await POST(req);
			const data = await response.json();

			expect(mockPrisma.leadMagnet.findUnique).toHaveBeenCalledWith({
				where: { id: mockLeadMagnet.id }
			});

			expect(
				mockEmailNewsletterService.syncNewsletterSubscriber
			).toHaveBeenCalledWith({
				email: validSubscriptionData.email,
				first_name: validSubscriptionData.first_name,
				last_name: validSubscriptionData.last_name,
				user: null,
				tags: [
					"source: lead magnet",
					"consumer action: downloaded lead magnet",
					...mockLeadMagnet.newsletter_tags
				]
			});

			expect(response.status).toBe(200);
			expect(data).toEqual({
				success: true,
				lead_magnet: {
					title: mockLeadMagnet.title
				}
			});
		});

		it("should subscribe authenticated user and update their newsletter subscription", async () => {
			mockBaseHandler.user = mockUser;
			mockBaseHandler.isAuthenticated = true;
			mockPrisma.user.update.mockResolvedValue({
				...mockUser,
				newsletter_subscribed: true
			});

			const req = createMockRequest({
				method: "POST",
				url: "/api/newsletter/subscribe-lead-magnet",
				validatedData: validSubscriptionData
			});

			const response = await POST(req);
			const data = await response.json();

			expect(mockPrisma.user.update).toHaveBeenCalledWith({
				where: { id: mockUser.id },
				data: {
					newsletter_subscribed: true
				}
			});

			expect(
				mockEmailNewsletterService.syncNewsletterSubscriber
			).toHaveBeenCalledWith({
				email: validSubscriptionData.email,
				first_name: validSubscriptionData.first_name,
				last_name: validSubscriptionData.last_name,
				user: mockUser,
				tags: [
					"source: lead magnet",
					"consumer action: downloaded lead magnet",
					...mockLeadMagnet.newsletter_tags
				]
			});

			expect(response.status).toBe(200);
			expect(data).toEqual({
				success: true,
				lead_magnet: {
					title: mockLeadMagnet.title
				}
			});
		});

		it("should return 404 if lead magnet not found", async () => {
			mockPrisma.leadMagnet.findUnique.mockResolvedValue(null);

			const req = createMockRequest({
				method: "POST",
				url: "/api/newsletter/subscribe-lead-magnet",
				validatedData: validSubscriptionData
			});

			const response = await POST(req);
			const data = await response.json();

			expect(response.status).toBe(404);
			expect(data).toEqual({ error: "Lead magnet not found" });
			expect(
				mockEmailNewsletterService.syncNewsletterSubscriber
			).not.toHaveBeenCalled();
		});

		it("should return 400 if lead magnet is inactive", async () => {
			const inactiveLeadMagnet = { ...mockLeadMagnet, status: "inactive" };
			mockPrisma.leadMagnet.findUnique.mockResolvedValue(inactiveLeadMagnet);

			const req = createMockRequest({
				method: "POST",
				url: "/api/newsletter/subscribe-lead-magnet",
				validatedData: validSubscriptionData
			});

			const response = await POST(req);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data).toEqual({ error: "Lead magnet is not active" });
			expect(
				mockEmailNewsletterService.syncNewsletterSubscriber
			).not.toHaveBeenCalled();
		});

		it("should handle newsletter service errors", async () => {
			mockEmailNewsletterService.syncNewsletterSubscriber.mockRejectedValue(
				new Error("Newsletter service error")
			);

			const req = createMockRequest({
				method: "POST",
				url: "/api/newsletter/subscribe-lead-magnet",
				validatedData: validSubscriptionData
			});

			const response = await POST(req);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data).toEqual({ error: "Failed to sync with newsletter service" });
		});

		it("should handle general subscription errors", async () => {
			mockPrisma.leadMagnet.findUnique.mockRejectedValue(
				new Error("Database error")
			);

			const req = createMockRequest({
				method: "POST",
				url: "/api/newsletter/subscribe-lead-magnet",
				validatedData: validSubscriptionData
			});

			const response = await POST(req);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data).toEqual({ error: "Failed to process subscription" });
		});

		it("should work with minimal data (only email and lead_magnet_id)", async () => {
			const minimalData = {
				email: "<EMAIL>",
				lead_magnet_id: mockLeadMagnet.id
			};

			const req = createMockRequest({
				method: "POST",
				url: "/api/newsletter/subscribe-lead-magnet",
				validatedData: minimalData
			});

			const response = await POST(req);
			const data = await response.json();

			expect(
				mockEmailNewsletterService.syncNewsletterSubscriber
			).toHaveBeenCalledWith({
				email: minimalData.email,
				first_name: undefined,
				last_name: undefined,
				user: null,
				tags: [
					"source: lead magnet",
					"consumer action: downloaded lead magnet",
					...mockLeadMagnet.newsletter_tags
				]
			});

			expect(response.status).toBe(200);
			expect(data).toEqual({
				success: true,
				lead_magnet: {
					title: mockLeadMagnet.title
				}
			});
		});

		it("should include custom newsletter tags from lead magnet", async () => {
			const leadMagnetWithCustomTags = {
				...mockLeadMagnet,
				newsletter_tags: ["Custom Tag 1", "Custom Tag 2", "Special Category"]
			};

			mockPrisma.leadMagnet.findUnique.mockResolvedValue(
				leadMagnetWithCustomTags
			);

			const req = createMockRequest({
				method: "POST",
				url: "/api/newsletter/subscribe-lead-magnet",
				validatedData: validSubscriptionData
			});

			const response = await POST(req);

			expect(
				mockEmailNewsletterService.syncNewsletterSubscriber
			).toHaveBeenCalledWith({
				email: validSubscriptionData.email,
				first_name: validSubscriptionData.first_name,
				last_name: validSubscriptionData.last_name,
				user: null,
				tags: [
					"source: lead magnet",
					"consumer action: downloaded lead magnet",
					"Custom Tag 1",
					"Custom Tag 2",
					"Special Category"
				]
			});

			expect(response.status).toBe(200);
		});
	});
});
