import { EmailNewsletterService } from '@/lib/services/emailNewsletter.service';
import { createMockRequest, mockBaseHandler } from '@/tests/utils/api-test-utils';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';

// Mock the EmailNewsletterService
jest.mock('@/lib/services/emailNewsletter.service');
const mockEmailNewsletterService = EmailNewsletterService as jest.Mocked<typeof EmailNewsletterService>;

// Mock the baseHandler
jest.mock('@/lib/api/baseHandler', () => ({
    createHandler: jest.fn((handler, options) => {
        return async (req: any) => {
            // Mock the validatedData property
            req.validatedData = req.body;

            // Call the handler with mocked context
            const context = {
                respond: mockBaseHandler.respond,
                validatedData: req.validatedData,
                user: null
            };

            return handler.call(context, req);
        };
    })
}));

// Import after mocking
import { POST } from '@/app/api/webhooks/facebook-leads/route';

describe('Zapier Facebook Leads Webhook', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Mock the syncNewsletterSubscriber method
        mockEmailNewsletterService.syncNewsletterSubscriber = jest.fn().mockResolvedValue(undefined);
    });

    it('should process a valid Zapier Facebook lead', async () => {
        const leadData = {
            email: '<EMAIL>',
            first_name: 'John',
            last_name: 'Doe',
            form_name: 'RV Service Request',
            form_id: '12345'
        };

        const request = createMockRequest({
            method: 'POST',
            url: '/api/webhooks/facebook-leads',
            headers: {
                'Content-Type': 'application/json',
                'x-forwarded-proto': 'https',
                'x-api-key': 'test-api-key'
            },
            body: leadData
        });

        // Mock environment variables
        process.env.ZAPIER_WEBHOOK_API_KEY = 'test-api-key';

        await POST(request);

        expect(mockBaseHandler.respond).toHaveBeenCalledWith({ received: true }, 200);

        // Verify that the newsletter service was called with correct data
        expect(mockEmailNewsletterService.syncNewsletterSubscriber).toHaveBeenCalledWith({
            email: '<EMAIL>',
            first_name: 'John',
            last_name: 'Doe',
            user: null,
            tags: expect.arrayContaining([
                'source: facebook lead ads',
                'consumer action: submitted facebook form',
                'facebook form id: 12345',
                'facebook form name: RV Service Request'
            ])
        });
    });

    it('should reject non-HTTPS requests', async () => {
        const request = createMockRequest({
            method: 'POST',
            url: '/api/webhooks/facebook-leads',
            headers: {
                'Content-Type': 'application/json',
                'x-forwarded-proto': 'http'
            },
            body: { email: '<EMAIL>' }
        });

        await POST(request);

        expect(mockBaseHandler.respond).toHaveBeenCalledWith({ error: 'HTTPS required' }, 400);
    });

    it('should reject requests without proper authentication', async () => {
        const request = createMockRequest({
            method: 'POST',
            url: '/api/webhooks/facebook-leads',
            headers: {
                'Content-Type': 'application/json',
                'x-forwarded-proto': 'https'
            },
            body: { email: '<EMAIL>' }
        });

        // Set an API key but don't provide it in the request
        process.env.ZAPIER_WEBHOOK_API_KEY = 'test-api-key';

        await POST(request);

        expect(mockBaseHandler.respond).toHaveBeenCalledWith({ error: 'Unauthorized' }, 401);
    });

    it('should handle missing email gracefully', async () => {
        const leadData = {
            first_name: 'John',
            last_name: 'Doe',
            form_id: '12345',
            form_name: 'Test Form'
        };

        const request = createMockRequest({
            method: 'POST',
            url: '/api/webhooks/facebook-leads',
            headers: {
                'Content-Type': 'application/json',
                'x-forwarded-proto': 'https',
                'x-api-key': 'test-api-key'
            },
            body: leadData
        });

        process.env.ZAPIER_WEBHOOK_API_KEY = 'test-api-key';

        await POST(request);

        expect(mockBaseHandler.respond).toHaveBeenCalledWith({ received: true }, 200);

        // Should not call the newsletter service when email is missing
        expect(mockEmailNewsletterService.syncNewsletterSubscriber).not.toHaveBeenCalled();
    });

    it('should handle additional fields from Zapier', async () => {
        const leadData = {
            email: '<EMAIL>',
            first_name: 'John',
            last_name: 'Doe',
            form_id: '12345',
            form_name: 'RV Service Request',
            phone: '555-1234',
            message: 'Need RV service',
            custom_field: 'custom value'
        };

        const request = createMockRequest({
            method: 'POST',
            url: '/api/webhooks/facebook-leads',
            headers: {
                'Content-Type': 'application/json',
                'x-forwarded-proto': 'https',
                'x-api-key': 'test-api-key'
            },
            body: leadData
        });

        process.env.ZAPIER_WEBHOOK_API_KEY = 'test-api-key';

        await POST(request);

        expect(mockBaseHandler.respond).toHaveBeenCalledWith({ received: true }, 200);

        // Verify that the newsletter service was called with correct data including additional fields
        expect(mockEmailNewsletterService.syncNewsletterSubscriber).toHaveBeenCalledWith({
            email: '<EMAIL>',
            first_name: 'John',
            last_name: 'Doe',
            user: null,
            tags: expect.arrayContaining([
                'source: facebook lead ads',
                'consumer action: submitted facebook form',
                'facebook form id: 12345',
                'facebook form name: RV Service Request'
            ])
        });
    });

    it('should work without authentication in development mode', async () => {
        const leadData = {
            email: '<EMAIL>',
            first_name: 'John',
            form_id: '12345'
        };

        const request = createMockRequest({
            method: 'POST',
            url: '/api/webhooks/facebook-leads',
            headers: {
                'Content-Type': 'application/json',
                'x-forwarded-proto': 'https'
            },
            body: leadData
        });

        // Don't set any authentication environment variables
        delete process.env.ZAPIER_WEBHOOK_API_KEY;
        delete process.env.ZAPIER_WEBHOOK_TOKEN;

        await POST(request);

        expect(mockBaseHandler.respond).toHaveBeenCalledWith({ received: true }, 200);
        expect(mockEmailNewsletterService.syncNewsletterSubscriber).toHaveBeenCalled();
    });
}); 