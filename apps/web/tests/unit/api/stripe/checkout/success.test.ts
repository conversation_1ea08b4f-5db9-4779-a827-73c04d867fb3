// Mock config first before any imports
jest.mock("@/config", () => ({
	__esModule: true,
	default: {
		stripe: {
			membership: {
				standard: {
					priceId: "price_standard_123"
				},
				premium: {
					priceId: "price_premium_123"
				}
			}
		}
	}
}));

import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
	createMockRequest,
	mockBaseHandler
} from "@/tests/utils/api-test-utils";


jest.mock("@/lib/stripe", () => ({
	stripe: {
		checkout: {
			sessions: {
				retrieve: jest.fn()
			}
		}
	}
}));

jest.mock("@/lib/services/membership.service", () => ({
	membershipService: {
		createOrUpdateMembership: jest.fn()
	}
}));

jest.mock("@/lib/services/emailNewsletter.service", () => ({
	EmailNewsletterService: {
		syncNewsletterSubscriber: jest.fn()
	}
}));

jest.mock("crypto", () => ({
	randomBytes: jest.fn(() => ({
		toString: jest.fn(() => "mock_token_123")
	}))
}));

// Import the route handler after mocking dependencies
import { POST } from "@/app/api/stripe/checkout/success/route";
import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";
import { membershipService } from "@/lib/services/membership.service";
import { stripe } from "@/lib/stripe";

describe("POST /api/stripe/checkout/success", () => {
	const mockSession = {
		id: "sess_123",
		metadata: {
			priceId: "price_standard_123"
		},
		customer_details: {
			email: "<EMAIL>",
			name: "John Doe"
		},
		subscription: {
			id: "sub_123"
		},
		amount_total: 4900
	};

	const mockUpdatedUser = {
		id: "user123",
		email: "<EMAIL>",
		first_name: "John",
		last_name: "Doe",
		membership_level: "STANDARD",
		member_number: 1
	};

	const mockMembership = {
		id: "membership123",
		user_id: "user123",
		level: "STANDARD",
		member_number: 1
	};

	beforeEach(() => {
		jest.clearAllMocks();
	});

	it("should process checkout success for new user", async () => {
		// Mock Stripe session retrieval
		(stripe.checkout.sessions.retrieve as jest.Mock).mockResolvedValue(
			mockSession
		);

		// Mock no existing user
		mockPrisma.user.findUnique.mockResolvedValue(null);

		// Mock user creation
		const newUser = {
			id: "user123",
			email: "<EMAIL>",
			first_name: "John",
			last_name: "Doe"
		};
		mockPrisma.user.create.mockResolvedValue(newUser);

		// Mock membership service
		(membershipService.createOrUpdateMembership as jest.Mock).mockResolvedValue(
			{
				user: mockUpdatedUser,
				membership: mockMembership,
				isNewMembership: true
			}
		);

		// Mock newsletter service
		(
			EmailNewsletterService.syncNewsletterSubscriber as jest.Mock
		).mockResolvedValue(true);

		const req = createMockRequest({
			method: "POST",
			url: "/api/stripe/checkout/success",
			validatedData: {
				sessionId: "sess_123",
				setupEmail: true
			}
		});

		await POST(req);

		expect(stripe.checkout.sessions.retrieve).toHaveBeenCalledWith("sess_123", {
			expand: ["subscription"]
		});

		expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
			where: { email: "<EMAIL>" }
		});

		expect(membershipService.createOrUpdateMembership).toHaveBeenCalledWith({
			userId: "user123",
			level: "STANDARD",
			stripeSessionId: "sess_123",
			stripeSubscriptionId: "sub_123",
			amountPaid: 4900,
			setupPasswordUrl: expect.stringContaining(
				"setup-password?token=mock_token_123"
			),
			req: expect.any(Object) // Request object is now passed for referral tracking
		});

		expect(mockBaseHandler.respond).toHaveBeenCalledWith({
			success: true,
			setupPasswordUrl: expect.stringContaining(
				"setup-password?token=mock_token_123"
			),
			memberNumber: 1,
			membershipLevel: "STANDARD",
			isNewMembership: true
		});
	});

	it("should return 404 if session not found", async () => {
		(stripe.checkout.sessions.retrieve as jest.Mock).mockResolvedValue(null);

		const req = createMockRequest({
			method: "POST",
			url: "/api/stripe/checkout/success",
			validatedData: {
				sessionId: "sess_nonexistent",
				setupEmail: false
			}
		});

		await POST(req);

		expect(mockBaseHandler.respond).toHaveBeenCalledWith(
			{ error: "Session not found" },
			404
		);
	});

	it("should return 400 if no price ID found", async () => {
		const sessionWithoutPrice = {
			...mockSession,
			metadata: {}
		};

		(stripe.checkout.sessions.retrieve as jest.Mock).mockResolvedValue(
			sessionWithoutPrice
		);

		const req = createMockRequest({
			method: "POST",
			url: "/api/stripe/checkout/success",
			validatedData: {
				sessionId: "sess_123",
				setupEmail: false
			}
		});

		await POST(req);

		expect(mockBaseHandler.respond).toHaveBeenCalledWith(
			{ error: "No price ID found" },
			400
		);
	});

	it("should handle newsletter service failure gracefully", async () => {
		(stripe.checkout.sessions.retrieve as jest.Mock).mockResolvedValue(
			mockSession
		);
		mockPrisma.user.findUnique.mockResolvedValue({
			id: "user123",
			email: "<EMAIL>",
			first_name: "John"
		});

		(membershipService.createOrUpdateMembership as jest.Mock).mockResolvedValue(
			{
				user: mockUpdatedUser,
				membership: mockMembership,
				isNewMembership: true
			}
		);

		// Mock newsletter service failure
		(
			EmailNewsletterService.syncNewsletterSubscriber as jest.Mock
		).mockRejectedValue(new Error("Newsletter service failed"));

		const req = createMockRequest({
			method: "POST",
			url: "/api/stripe/checkout/success",
			validatedData: {
				sessionId: "sess_123",
				setupEmail: false
			}
		});

		await POST(req);

		// Should still return success despite newsletter failure
		expect(mockBaseHandler.respond).toHaveBeenCalledWith({
			success: true,
			setupPasswordUrl: undefined,
			memberNumber: 1,
			membershipLevel: "STANDARD",
			isNewMembership: true
		});
	});
});
