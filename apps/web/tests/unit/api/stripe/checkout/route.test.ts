// Mock config first before any imports
jest.mock("@/config", () => ({
    __esModule: true,
    default: {
        stripe: {
            membership: {
                standard: {
                    priceId: "price_standard_123"
                },
                premium: {
                    priceId: "price_premium_123"
                }
            }
        }
    }
}));

import config from "@/config";
import { OfferService } from "@/lib/services/offer.service";
import { UserService } from "@/lib/services/user.service";
import { createMockRequest } from "@/tests/utils/api-test-utils";

// Mock the services
jest.mock("@/lib/services/user.service");
jest.mock("@/lib/services/offer.service");
jest.mock("@/lib/stripe", () => ({
    stripe: {
        checkout: {
            sessions: {
                create: jest.fn()
            }
        },
        coupons: {
            create: jest.fn()
        }
    }
}));

// Import the route handler after mocking dependencies
import { POST } from "@/app/api/stripe/checkout/route";
import { stripe } from "@/lib/stripe";

const mockUserService = UserService as jest.Mocked<typeof UserService>;
const mockOfferService = OfferService as jest.Mocked<typeof OfferService>;

describe("Stripe Checkout Route", () => {
    beforeEach(() => {
        jest.clearAllMocks();

        // Mock UserService.user() to return a test user
        mockUserService.user = jest.fn().mockResolvedValue({
            id: "user-123",
            email: "<EMAIL>",
            role: "USER"
        });

        // Mock OfferService.isUserEligibleForAnnualOffer to return not eligible
        mockOfferService.isUserEligibleForAnnualOffer = jest.fn().mockResolvedValue({
            eligible: false,
            reason: "Already used"
        });

        // Mock successful Stripe session creation
        (stripe.checkout.sessions.create as jest.Mock).mockResolvedValue({
            url: "https://checkout.stripe.com/test"
        });

        // Mock successful coupon creation
        (stripe.coupons.create as jest.Mock).mockResolvedValue({
            id: "coupon_test123"
        });
    });

    describe("Marketing Campaign Coupon Code Handling", () => {
        it("should truncate long campaign titles in coupon names", async () => {
            // Mock a marketing campaign
            const mockCampaign = {
                id: "campaign-123",
                title: "Marketing Campaign for State Parks and Recreation Areas",
                status: "ACTIVE",
                expires_at: null,
                discount_type: "PERCENTAGE",
                discount_value: 25
            };

            // Mock the OfferService to return a valid campaign
            mockOfferService.validateMarketingCampaignCoupon = jest.fn().mockResolvedValue({
                valid: true,
                campaign: mockCampaign
            });

            // Create a mock request using the utility
            const request = createMockRequest({
                method: "POST",
                url: "/api/stripe/checkout",
                validatedData: {
                    priceId: config.stripe.membership.standard.priceId,
                    couponCode: "TESTCOUPON123"
                }
            });

            // Call the handler
            await POST(request);

            // Verify that the coupon was created with a truncated name
            expect(stripe.coupons.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: "Campaign: Marketing Campaign for Stat...",
                    duration: "once",
                    currency: "usd",
                    percent_off: 25
                })
            );
        });

        it("should handle short campaign titles without truncation", async () => {
            // Mock a marketing campaign
            const mockCampaign = {
                id: "campaign-123",
                title: "Summer Sale",
                status: "ACTIVE",
                expires_at: null,
                discount_type: "FIXED_AMOUNT",
                discount_value: 50
            };

            // Mock the OfferService to return a valid campaign
            mockOfferService.validateMarketingCampaignCoupon = jest.fn().mockResolvedValue({
                valid: true,
                campaign: mockCampaign
            });

            // Create a mock request using the utility
            const request = createMockRequest({
                method: "POST",
                url: "/api/stripe/checkout",
                validatedData: {
                    priceId: config.stripe.membership.standard.priceId,
                    couponCode: "SUMMER50"
                }
            });

            // Call the handler
            await POST(request);

            // Verify that the coupon was created with the full name (no truncation needed)
            expect(stripe.coupons.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: "Campaign: Summer Sale",
                    duration: "once",
                    currency: "usd",
                    amount_off: 5000 // $50 in cents
                })
            );
        });

        it("should handle exactly 40-character campaign titles", async () => {
            // Mock a marketing campaign
            const mockCampaign = {
                id: "campaign-123",
                title: "State Parks Campaign",
                status: "ACTIVE",
                expires_at: null,
                discount_type: "PERCENTAGE",
                discount_value: 10
            };

            // Mock the OfferService to return a valid campaign
            mockOfferService.validateMarketingCampaignCoupon = jest.fn().mockResolvedValue({
                valid: true,
                campaign: mockCampaign
            });

            // Create a mock request using the utility
            const request = createMockRequest({
                method: "POST",
                url: "/api/stripe/checkout",
                validatedData: {
                    priceId: config.stripe.membership.standard.priceId,
                    couponCode: "PARKS10"
                }
            });

            // Call the handler
            await POST(request);

            // Verify that the coupon was created with the exact name (no truncation needed)
            expect(stripe.coupons.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: "Campaign: State Parks Campaign",
                    duration: "once",
                    currency: "usd",
                    percent_off: 10
                })
            );
        });
    });

    describe("Email-based Eligibility", () => {
        it("should check email-based eligibility when email is provided", async () => {
            // Mock email-based eligibility
            mockOfferService.isEmailEligibleForAnnualOffer = jest.fn().mockResolvedValue({
                eligible: true,
                offer: {
                    id: "offer123",
                    discount_percentage: 50
                },
                userId: "user-456"
            });

            // Mock no logged-in user
            mockUserService.user = jest.fn().mockResolvedValue(null);

            // Create a mock request with email
            const request = createMockRequest({
                method: "POST",
                url: "/api/stripe/checkout",
                validatedData: {
                    priceId: config.stripe.membership.standard.priceId,
                    email: "<EMAIL>",
                    serviceRequestId: "job-123"
                }
            });

            // Call the handler
            await POST(request);

            // Verify that email-based eligibility was checked
            expect(mockOfferService.isEmailEligibleForAnnualOffer).toHaveBeenCalledWith("<EMAIL>");

            // Verify that a discount was applied
            expect(stripe.checkout.sessions.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    discounts: [{ coupon: "coupon_test123" }],
                    metadata: expect.objectContaining({
                        offerId: "offer123",
                        userId: "user-456"
                    })
                })
            );
        });

        it("should not check email-based eligibility when no email is provided", async () => {
            // Mock no logged-in user
            mockUserService.user = jest.fn().mockResolvedValue(null);

            // Create a mock request without email
            const request = createMockRequest({
                method: "POST",
                url: "/api/stripe/checkout",
                validatedData: {
                    priceId: config.stripe.membership.standard.priceId,
                    serviceRequestId: "job-123"
                }
            });

            // Call the handler
            await POST(request);

            // Verify that email-based eligibility was not checked
            expect(mockOfferService.isEmailEligibleForAnnualOffer).not.toHaveBeenCalled();

            // Verify that promotion codes are enabled instead
            expect(stripe.checkout.sessions.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    allow_promotion_codes: true
                })
            );
        });
    });

    describe("Promotion Codes", () => {
        it("should enable allow_promotion_codes when no coupon code is provided", async () => {
            // Create a mock request without a coupon code
            const request = createMockRequest({
                method: "POST",
                url: "/api/stripe/checkout",
                validatedData: {
                    priceId: config.stripe.membership.standard.priceId
                }
            });

            // Call the handler
            await POST(request);

            // Verify that allow_promotion_codes is set to true
            expect(stripe.checkout.sessions.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    allow_promotion_codes: true
                })
            );
        });

        it("should not enable allow_promotion_codes when a coupon code is provided", async () => {
            // Mock a marketing campaign
            const mockCampaign = {
                id: "campaign-123",
                title: "Test Campaign",
                status: "ACTIVE",
                expires_at: null,
                discount_type: "PERCENTAGE",
                discount_value: 10
            };

            // Mock the OfferService to return a valid campaign
            mockOfferService.validateMarketingCampaignCoupon = jest.fn().mockResolvedValue({
                valid: true,
                campaign: mockCampaign
            });

            // Create a mock request with a coupon code
            const request = createMockRequest({
                method: "POST",
                url: "/api/stripe/checkout",
                validatedData: {
                    priceId: config.stripe.membership.standard.priceId,
                    couponCode: "TESTCOUPON"
                }
            });

            // Call the handler
            await POST(request);

            // Verify that allow_promotion_codes is not set
            expect(stripe.checkout.sessions.create).toHaveBeenCalledWith(
                expect.not.objectContaining({
                    allow_promotion_codes: true
                })
            );
        });

        it("should not have both allow_promotion_codes and discounts in session data", async () => {
            // Mock user eligibility for discount
            mockUserService.user.mockResolvedValue({
                id: "user-123",
                email: "<EMAIL>"
            });

            mockOfferService.isUserEligibleForAnnualOffer.mockResolvedValue({
                eligible: true,
                offer: {
                    id: "offer123",
                    discount_percentage: 50
                }
            });

            // Mock Stripe coupon creation
            (stripe.coupons.create as jest.Mock).mockResolvedValue({
                id: "coupon_123",
                amount_off: 5000,
                duration: "once",
                currency: "usd",
                name: "Annual Discount Offer"
            });

            // Create a mock request with applyDiscount: true
            const request = createMockRequest({
                method: "POST",
                url: "/api/stripe/checkout",
                validatedData: {
                    priceId: config.stripe.membership.standard.priceId,
                    applyDiscount: true
                }
            });

            // Call the handler
            await POST(request);

            // Get the session data that was passed to Stripe
            const sessionData = stripe.checkout.sessions.create.mock.calls[0][0];

            // Verify that both allow_promotion_codes and discounts are not present simultaneously
            const hasAllowPromotionCodes = sessionData.hasOwnProperty('allow_promotion_codes');
            const hasDiscounts = sessionData.hasOwnProperty('discounts');

            // Either allow_promotion_codes should be present OR discounts should be present, but not both
            expect(hasAllowPromotionCodes && hasDiscounts).toBe(false);
        });
    });
}); 