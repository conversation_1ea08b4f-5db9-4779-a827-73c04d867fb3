// Mock Next.js headers
const mockHeadersGet = jest.fn();
jest.mock("next/headers", () => ({
    headers: jest.fn(() => ({
        get: mockHeadersGet
    }))
}));

// Mock the stripe client from @/lib/stripe
jest.mock("@/lib/stripe", () => ({
    stripe: {
        webhooks: {
            constructEvent: jest.fn()
        }
    }
}));

// Mock prisma
jest.mock("@/lib/prisma", () => ({
    __esModule: true,
    default: {
        stripeConnection: {
            findUnique: jest.fn(),
            update: jest.fn()
        },
        payout: {
            update: jest.fn()
        },
        adminLog: {
            create: jest.fn()
        }
    }
}));

import { POST } from "@/app/api/stripe/connect/webhook/route";
import prisma from "@/lib/prisma";
import { stripe } from "@/lib/stripe";
import { NextRequest } from "next/server";

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockStripeWebhooks = stripe.webhooks as jest.Mocked<typeof stripe.webhooks>;

describe("Stripe Connect Webhook Info Update Handling", () => {
    const testUserId = "test-user-123";
    const testAccountId = "acct_webhook_test";

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup headers mock to return the stripe signature
        mockHeadersGet.mockImplementation((name: string) => {
            if (name === "stripe-signature") {
                return "valid_signature";
            }
            return null;
        });
    });

    describe("account.updated webhook", () => {
        it("should set needs_info_update to true when account has requirements", async () => {
            const webhookEvent = {
                type: "account.updated",
                data: {
                    object: {
                        id: testAccountId,
                        details_submitted: true,
                        charges_enabled: true,
                        payouts_enabled: true,
                        requirements: {
                            currently_due: ["individual.verification.document"],
                            eventually_due: [],
                            past_due: []
                        }
                    }
                }
            };

            mockStripeWebhooks.constructEvent.mockReturnValue(webhookEvent);
            mockPrisma.stripeConnection.update.mockResolvedValue({
                id: "conn-123",
                user_id: testUserId,
                stripe_account_id: testAccountId,
                stripe_account_status: "active",
                payments_enabled: true,
                details_submitted: true,
                payouts_enabled: true,
                is_verified: true,
                needs_info_update: true,
                created_at: new Date(),
                updated_at: new Date()
            });

            const request = new NextRequest("http://localhost:3000/api/stripe/connect/webhook", {
                method: "POST",
                body: JSON.stringify(webhookEvent),
                headers: {
                    "stripe-signature": "valid_signature"
                }
            });

            const response = await POST(request);
            expect(response.status).toBe(200);

            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { stripe_account_id: testAccountId },
                data: expect.objectContaining({
                    needs_info_update: true
                })
            });
        });

        it("should set needs_info_update to false when account has no requirements", async () => {
            const webhookEvent = {
                type: "account.updated",
                data: {
                    object: {
                        id: testAccountId,
                        details_submitted: true,
                        charges_enabled: true,
                        payouts_enabled: true,
                        requirements: {
                            currently_due: [],
                            eventually_due: [],
                            past_due: []
                        }
                    }
                }
            };

            mockStripeWebhooks.constructEvent.mockReturnValue(webhookEvent);

            const request = new NextRequest("http://localhost:3000/api/stripe/connect/webhook", {
                method: "POST",
                body: JSON.stringify(webhookEvent),
                headers: {
                    "stripe-signature": "valid_signature"
                }
            });

            const response = await POST(request);
            expect(response.status).toBe(200);

            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { stripe_account_id: testAccountId },
                data: expect.objectContaining({
                    needs_info_update: false
                })
            });
        });

        it("should handle eventually_due requirements", async () => {
            const webhookEvent = {
                type: "account.updated",
                data: {
                    object: {
                        id: testAccountId,
                        details_submitted: true,
                        charges_enabled: true,
                        payouts_enabled: true,
                        requirements: {
                            currently_due: [],
                            eventually_due: ["individual.address.city"],
                            past_due: []
                        }
                    }
                }
            };

            mockStripeWebhooks.constructEvent.mockReturnValue(webhookEvent);

            const request = new NextRequest("http://localhost:3000/api/stripe/connect/webhook", {
                method: "POST",
                body: JSON.stringify(webhookEvent),
                headers: {
                    "stripe-signature": "valid_signature"
                }
            });

            const response = await POST(request);
            expect(response.status).toBe(200);

            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { stripe_account_id: testAccountId },
                data: expect.objectContaining({
                    needs_info_update: true
                })
            });
        });

        it("should handle past_due requirements", async () => {
            const webhookEvent = {
                type: "account.updated",
                data: {
                    object: {
                        id: testAccountId,
                        details_submitted: false,
                        charges_enabled: false,
                        payouts_enabled: false,
                        requirements: {
                            currently_due: [],
                            eventually_due: [],
                            past_due: ["business.tax_id"]
                        }
                    }
                }
            };

            mockStripeWebhooks.constructEvent.mockReturnValue(webhookEvent);

            const request = new NextRequest("http://localhost:3000/api/stripe/connect/webhook", {
                method: "POST",
                body: JSON.stringify(webhookEvent),
                headers: {
                    "stripe-signature": "valid_signature"
                }
            });

            const response = await POST(request);
            expect(response.status).toBe(200);

            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { stripe_account_id: testAccountId },
                data: expect.objectContaining({
                    needs_info_update: true
                })
            });
        });

        it("should handle P2025 error gracefully when StripeConnection record not found", async () => {
            const webhookEvent = {
                type: "account.updated",
                data: {
                    object: {
                        id: "acct_nonexistent",
                        details_submitted: true,
                        charges_enabled: true,
                        payouts_enabled: true,
                        requirements: {
                            currently_due: [],
                            eventually_due: [],
                            past_due: []
                        }
                    }
                }
            };

            mockStripeWebhooks.constructEvent.mockReturnValue(webhookEvent);

            // Mock findUnique to return null (record not found)
            mockPrisma.stripeConnection.findUnique.mockResolvedValue(null);

            // Mock update to throw P2025 error
            const p2025Error = {
                name: "PrismaClientKnownRequestError",
                code: "P2025",
                meta: { cause: "No record was found for an update." },
                message: "No record was found for an update."
            };
            mockPrisma.stripeConnection.update.mockRejectedValue(p2025Error);

            const request = new NextRequest("http://localhost:3000/api/stripe/connect/webhook", {
                method: "POST",
                body: JSON.stringify(webhookEvent),
                headers: {
                    "stripe-signature": "valid_signature"
                }
            });

            const response = await POST(request);

            // Should return 200 to acknowledge the webhook, not 500
            expect(response.status).toBe(200);

            const responseData = await response.json();
            expect(responseData).toEqual({ received: true });
        });
    });

    describe("payout.failed webhook", () => {
        it("should set needs_info_update to true when payout fails", async () => {
            const webhookEvent = {
                type: "payout.failed",
                data: {
                    object: {
                        id: "po_test_failed",
                        status: "failed",
                        destination: testAccountId,
                        failure_code: "account_closed"
                    }
                }
            };

            mockStripeWebhooks.constructEvent.mockReturnValue(webhookEvent);

            // Mock payout update
            mockPrisma.payout.update.mockResolvedValue({
                id: "payout-123",
                stripe_payout_id: "po_test_failed",
                user_id: testUserId,
                amount: 10000,
                currency: "usd",
                status: "failed",
                failure_reason: "account_closed",
                created_at: new Date(),
                updated_at: new Date()
            });

            // Mock stripe connection update
            mockPrisma.stripeConnection.update.mockResolvedValue({
                id: "conn-123",
                user_id: testUserId,
                stripe_account_id: testAccountId,
                stripe_account_status: "active",
                payments_enabled: true,
                details_submitted: true,
                payouts_enabled: true,
                is_verified: true,
                needs_info_update: true,
                created_at: new Date(),
                updated_at: new Date()
            });

            const request = new NextRequest("http://localhost:3000/api/stripe/connect/webhook", {
                method: "POST",
                body: JSON.stringify(webhookEvent),
                headers: {
                    "stripe-signature": "valid_signature"
                }
            });

            const response = await POST(request);
            expect(response.status).toBe(200);

            // Check that payout was updated
            expect(mockPrisma.payout.update).toHaveBeenCalledWith({
                where: { stripe_payout_id: "po_test_failed" },
                data: {
                    status: "failed",
                    failure_reason: "account_closed"
                }
            });

            // Check that stripe connection was updated to need info update
            expect(mockPrisma.stripeConnection.update).toHaveBeenCalledWith({
                where: { stripe_account_id: testAccountId },
                data: {
                    needs_info_update: true
                }
            });
        });

        it("should not set needs_info_update when payout succeeds", async () => {
            const webhookEvent = {
                type: "payout.paid",
                data: {
                    object: {
                        id: "po_test_success",
                        status: "paid",
                        destination: testAccountId
                    }
                }
            };

            mockStripeWebhooks.constructEvent.mockReturnValue(webhookEvent);

            // Mock payout update
            mockPrisma.payout.update.mockResolvedValue({
                id: "payout-123",
                stripe_payout_id: "po_test_success",
                user_id: testUserId,
                amount: 10000,
                currency: "usd",
                status: "paid",
                failure_reason: null,
                created_at: new Date(),
                updated_at: new Date()
            });

            const request = new NextRequest("http://localhost:3000/api/stripe/connect/webhook", {
                method: "POST",
                body: JSON.stringify(webhookEvent),
                headers: {
                    "stripe-signature": "valid_signature"
                }
            });

            const response = await POST(request);
            expect(response.status).toBe(200);

            // Check that payout was updated
            expect(mockPrisma.payout.update).toHaveBeenCalledWith({
                where: { stripe_payout_id: "po_test_success" },
                data: {
                    status: "paid",
                    failure_reason: null
                }
            });

            // Check that stripe connection update was NOT called for successful payouts
            expect(mockPrisma.stripeConnection.update).not.toHaveBeenCalledWith({
                where: { stripe_account_id: testAccountId },
                data: {
                    needs_info_update: true
                }
            });
        });
    });
});
