import { POST } from "@/app/api/provider/quotes/[id]/accept/route";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { mockPrisma } from "@/tests/mocks/prisma-mock";
import { createMockRequest, mockBaseHandler, mockProviderUser } from "@/tests/utils/api-test-utils";

// Mock the services
jest.mock("@/lib/services/quote-status.service", () => ({
	QuoteStatusService: {
		providerAcceptJob: jest.fn(),
		providerStartJob: jest.fn(),
	},
}));

describe("POST /api/provider/quotes/[id]/accept", () => {
	const mockQuoteId = "quote123";
	const mockJobId = "job456";
	const mockUserId = mockProviderUser.id; // Use the provider user ID for provider tests

	beforeEach(() => {
		jest.clearAllMocks();
		// Set up provider authentication
		mockBaseHandler.user = mockProviderUser;
		mockBaseHandler.session = { user: mockProviderUser };
	});

	const mockQuote = {
		id: mockQuoteId,
		job_id: mockJobId,
		listing_id: "listing456",
		status: "PROVIDER_INVITED",
	};

	const mockAcceptedQuote = {
		id: mockQuoteId,
		listing_id: "listing456",
		status: "PROVIDER_ACCEPTED",
		responded_at: expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/), // ISO date string
		message: "I can help with this",
		job: {
			user: {
				first_name: "John",
				last_name: "Doe",
				email: "<EMAIL>",
			},
		},
	};

	const mockStartedQuote = {
		...mockAcceptedQuote,
		status: "IN_PROGRESS",
	};

	it("should accept a job successfully", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.providerAcceptJob as jest.Mock).mockResolvedValue(
			mockAcceptedQuote
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: {
				message: "I can help with this",
			},
		});

		const response = await POST(req);

		const responseData = await response.json();

		expect(mockPrisma.quote.findUnique).toHaveBeenCalledWith({
			where: { id: mockQuoteId },
		});
		expect(QuoteStatusService.providerAcceptJob).toHaveBeenCalledWith({
			quote: mockQuote,
			userId: mockUserId,
			message: "I can help with this",
		});

		expect(response.status).toBe(200);
		expect(responseData).toEqual(expect.objectContaining({
			id: mockQuoteId,
			listing_id: "listing456",
			status: "PROVIDER_ACCEPTED",
			message: "I can help with this",
			job: {
				user: {
					first_name: "John",
					last_name: "Doe",
					email: "<EMAIL>",
				},
			},
		}));
		expect(responseData.responded_at).toBeDefined(); // Just check it exists
	});

	it("should accept and start a job when shouldStartJob is true", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.providerAcceptJob as jest.Mock).mockResolvedValue(
			mockAcceptedQuote
		);
		(QuoteStatusService.providerStartJob as jest.Mock).mockResolvedValue(
			mockStartedQuote
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: {
				message: "Ready to start immediately",
				shouldStartJob: true,
			},
		});

		const response = await POST(req);
		const responseData = await response.json();

		expect(QuoteStatusService.providerAcceptJob).toHaveBeenCalledWith({
			quote: mockQuote,
			userId: mockUserId,
			message: "Ready to start immediately",
		});

		expect(QuoteStatusService.providerStartJob).toHaveBeenCalledWith({
			quote: mockAcceptedQuote,
			userId: mockUserId,
		});

		expect(response.status).toBe(200);
		expect(responseData.status).toEqual(mockStartedQuote.status);
		expect(responseData.listing_id).toEqual(mockStartedQuote.listing_id);
		expect(responseData.message).toEqual(mockStartedQuote.message);
	});

	it("should accept job without starting when shouldStartJob is false", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.providerAcceptJob as jest.Mock).mockResolvedValue(
			mockAcceptedQuote
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: {
				message: "I can help with this",
				shouldStartJob: false,
			},
		});

		const response = await POST(req);
		const responseData = await response.json();

		expect(QuoteStatusService.providerAcceptJob).toHaveBeenCalledWith({
			quote: mockQuote,
			userId: mockUserId,
			message: "I can help with this",
		});

		expect(QuoteStatusService.providerStartJob).not.toHaveBeenCalled();

		expect(response.status).toBe(200);
		expect(responseData.status).toEqual("PROVIDER_ACCEPTED");
	});

	it("should accept job without starting when shouldStartJob is not provided", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.providerAcceptJob as jest.Mock).mockResolvedValue(
			mockAcceptedQuote
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: {
				message: "I can help with this",
			},
		});

		const response = await POST(req);
		const responseData = await response.json();

		expect(QuoteStatusService.providerAcceptJob).toHaveBeenCalledWith({
			quote: mockQuote,
			userId: mockUserId,
			message: "I can help with this",
		});

		expect(QuoteStatusService.providerStartJob).not.toHaveBeenCalled();

		expect(response.status).toBe(200);
		expect(responseData.status).toEqual("PROVIDER_ACCEPTED");
	});

	it("should return 404 when quote not found", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(null);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: {
				message: "I can help with this",
			},
		});

		const response = await POST(req);
		const responseData = await response.json();

		expect(response.status).toBe(404);
		expect(responseData).toBe("Quote not found");
		expect(QuoteStatusService.providerAcceptJob).not.toHaveBeenCalled();
	});

	it("should handle quote status service errors", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.providerAcceptJob as jest.Mock).mockRejectedValue(
			new Error("Cannot respond to this quote")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: {
				message: "I can help with this",
			},
		});

		await expect(POST(req)).rejects.toThrow("Cannot respond to this quote");
	});

	it("should handle unauthorized quote access", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.providerAcceptJob as jest.Mock).mockRejectedValue(
			new Error("Unauthorized")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: {
				message: "I can help with this",
			},
		});

		await expect(POST(req)).rejects.toThrow("Unauthorized");
	});

	it("should validate required message field", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: {
				// message is missing
			},
		});

		// This should fail validation at the handler level
		await expect(POST(req)).rejects.toThrow();
	});

	it("should handle quote already responded to", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.providerAcceptJob as jest.Mock).mockRejectedValue(
			new Error("Can only respond to invited quotes")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: {
				message: "I can help with this",
			},
		});

		await expect(POST(req)).rejects.toThrow("Can only respond to invited quotes");
	});

	it("should handle start job service errors", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.providerAcceptJob as jest.Mock).mockResolvedValue(
			mockAcceptedQuote
		);
		(QuoteStatusService.providerStartJob as jest.Mock).mockRejectedValue(
			new Error("Cannot start job at this time")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: {
				message: "Ready to start immediately",
				shouldStartJob: true,
			},
		});

		await expect(POST(req)).rejects.toThrow("Cannot start job at this time");
	});
}); 