import { POST } from "@/app/api/provider/quotes/[id]/complete/route";
import { ListingService } from "@/lib/services/listing.service";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { createMockRequest, mockBaseHandler, mockProviderUser } from "@/tests/utils/api-test-utils";

// Mock the services
jest.mock("@/lib/services/quote-status.service", () => ({
	QuoteStatusService: {
		providerCompleteJob: jest.fn(),
	},
}));

jest.mock("@/lib/services/listing.service", () => ({
	ListingService: {
		getListingByUserId: jest.fn(),
	},
}));

describe("POST /api/provider/quotes/[id]/complete", () => {
	const mockQuoteId = "quote123";
	const mockListingId = "listing456";
	const mockUserId = mockProviderUser.id; // Use the provider user ID for provider tests

	beforeEach(() => {
		jest.clearAllMocks();
		// Set up provider authentication
		mockBaseHandler.user = mockProviderUser;
		mockBaseHandler.session = { user: mockProviderUser };
	});

	const mockListing = {
		id: mockListingId,
		business_name: "Test RV Service",
		first_name: "Jane",
		last_name: "Smith",
		email: "<EMAIL>",
	};

	const mockCompletedQuote = {
		id: mockQuoteId,
		listing_id: mockListingId,
		status: "PROVIDER_COMPLETED",
		completed_at: new Date(),
		review_requested: true,
		review_delay_hours: 24,
		message: "Job completed successfully",
		job: {
			user: {
				first_name: "John",
				last_name: "Doe",
				email: "<EMAIL>",
			},
		},
	};

	beforeEach(() => {
		jest.clearAllMocks();
	});

	it("should complete a job successfully with review request", async () => {
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(mockListing);
		(QuoteStatusService.providerCompleteJob as jest.Mock).mockResolvedValue(
			mockCompletedQuote
		);

		const req = createMockRequest({
			method: "POST",
			url: `/api/provider/quotes/${mockQuoteId}/complete`,
			params: { id: mockQuoteId },
			session: { user: { id: mockUserId } },
			body: {
				sendInvoice: true,
				requestReview: true,
				reviewDelayHours: 24,
				resolutionNotes: "Job completed successfully",
			},
		});

		const response = await POST(req, {
			validatedData: {
				sendInvoice: true,
				requestReview: true,
				reviewDelayHours: 24,
				resolutionNotes: "Job completed successfully",
			},
			session: { user: { id: mockUserId } },
			params: { id: mockQuoteId },
		});

		const responseData = await response.json();

		expect(ListingService.getListingByUserId).toHaveBeenCalledWith(mockUserId);
		expect(QuoteStatusService.providerCompleteJob).toHaveBeenCalledWith({
			quoteId: mockQuoteId,
			listingId: mockListingId,
			userId: mockUserId,
			resolutionNotes: "Job completed successfully",
			sendInvoice: true,
			requestReview: true,
			reviewDelayHours: 24,
		});

		expect(responseData.success).toBe(true);
		expect(responseData.quote).toEqual(mockCompletedQuote);
	});

	it("should complete a job without review request", async () => {
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(mockListing);
		(QuoteStatusService.providerCompleteJob as jest.Mock).mockResolvedValue({
			...mockCompletedQuote,
			review_requested: false,
			review_delay_hours: null,
		});

		const req = createMockRequest({
			method: "POST",
			url: `/api/provider/quotes/${mockQuoteId}/complete`,
			params: { id: mockQuoteId },
			session: { user: { id: mockUserId } },
			body: {
				sendInvoice: false,
				requestReview: false,
			},
		});

		const response = await POST(req, {
			validatedData: {
				sendInvoice: false,
				requestReview: false,
			},
			session: { user: { id: mockUserId } },
			params: { id: mockQuoteId },
		});

		expect(QuoteStatusService.providerCompleteJob).toHaveBeenCalledWith({
			quoteId: mockQuoteId,
			listingId: mockListingId,
			userId: mockUserId,
			resolutionNotes: undefined,
			sendInvoice: false,
			requestReview: false,
			reviewDelayHours: undefined,
		});

		const responseData = await response.json();
		expect(responseData.success).toBe(true);
	});

	it("should handle missing listing", async () => {
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(null);

		const req = createMockRequest({
			method: "POST",
			url: `/api/provider/quotes/${mockQuoteId}/complete`,
			params: { id: mockQuoteId },
			session: { user: { id: mockUserId } },
			body: {
				sendInvoice: true,
				requestReview: false,
			},
		});

		const response = await POST(req, {
			validatedData: {
				sendInvoice: true,
				requestReview: false,
			},
			session: { user: { id: mockUserId } },
			params: { id: mockQuoteId },
		});

		const responseData = await response.json();
		expect(response.status).toBe(404);
		expect(responseData.error).toBe("Listing not found");
	});

	it("should handle quote completion service errors", async () => {
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(mockListing);
		(QuoteStatusService.providerCompleteJob as jest.Mock).mockRejectedValue(
			new Error("Cannot complete this quote")
		);

		const req = createMockRequest({
			method: "POST",
			url: `/api/provider/quotes/${mockQuoteId}/complete`,
			params: { id: mockQuoteId },
			session: { user: { id: mockUserId } },
			body: {
				sendInvoice: true,
				requestReview: true,
			},
		});

		const response = await POST(req, {
			validatedData: {
				sendInvoice: true,
				requestReview: true,
			},
			session: { user: { id: mockUserId } },
			params: { id: mockQuoteId },
		});

		expect(response.status).toBe(500);
		const responseData = await response.json();
		expect(responseData.error).toBe("Failed to complete job");
	});

	it("should handle unauthorized completion attempt", async () => {
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(mockListing);
		(QuoteStatusService.providerCompleteJob as jest.Mock).mockRejectedValue(
			new Error("Can only complete accepted jobs")
		);

		const req = createMockRequest({
			method: "POST",
			url: `/api/provider/quotes/${mockQuoteId}/complete`,
			params: { id: mockQuoteId },
			session: { user: { id: mockUserId } },
			body: {
				sendInvoice: false,
				requestReview: false,
			},
		});

		const response = await POST(req, {
			validatedData: {
				sendInvoice: false,
				requestReview: false,
			},
			session: { user: { id: mockUserId } },
			params: { id: mockQuoteId },
		});

		expect(response.status).toBe(500);
		const responseData = await response.json();
		expect(responseData.error).toBe("Failed to complete job");
	});

	it("should handle resolution status and notes", async () => {
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(mockListing);
		(QuoteStatusService.providerCompleteJob as jest.Mock).mockResolvedValue(
			mockCompletedQuote
		);

		const req = createMockRequest({
			method: "POST",
			url: `/api/provider/quotes/${mockQuoteId}/complete`,
			params: { id: mockQuoteId },
			session: { user: { id: mockUserId } },
			body: {
				sendInvoice: true,
				requestReview: true,
				reviewDelayHours: 48,
				resolutionStatus: "COMPLETED",
				resolutionNotes: "Replaced faulty component and tested system",
			},
		});

		await POST(req, {
			validatedData: {
				sendInvoice: true,
				requestReview: true,
				reviewDelayHours: 48,
				resolutionStatus: "COMPLETED",
				resolutionNotes: "Replaced faulty component and tested system",
			},
			session: { user: { id: mockUserId } },
			params: { id: mockQuoteId },
		});

		expect(QuoteStatusService.providerCompleteJob).toHaveBeenCalledWith({
			quoteId: mockQuoteId,
			listingId: mockListingId,
			userId: mockUserId,
			resolutionStatus: "COMPLETED",
			resolutionNotes: "Replaced faulty component and tested system",
			sendInvoice: true,
			requestReview: true,
			reviewDelayHours: 48,
		});
	});

	it("should handle custom review delay hours", async () => {
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(mockListing);
		(QuoteStatusService.providerCompleteJob as jest.Mock).mockResolvedValue({
			...mockCompletedQuote,
			review_delay_hours: 72,
		});

		const req = createMockRequest({
			method: "POST",
			url: `/api/provider/quotes/${mockQuoteId}/complete`,
			params: { id: mockQuoteId },
			session: { user: { id: mockUserId } },
			body: {
				sendInvoice: false,
				requestReview: true,
				reviewDelayHours: 72,
			},
		});

		const response = await POST(req, {
			validatedData: {
				sendInvoice: false,
				requestReview: true,
				reviewDelayHours: 72,
			},
			session: { user: { id: mockUserId } },
			params: { id: mockQuoteId },
		});

		expect(QuoteStatusService.providerCompleteJob).toHaveBeenCalledWith({
			quoteId: mockQuoteId,
			listingId: mockListingId,
			userId: mockUserId,
			resolutionNotes: undefined,
			sendInvoice: false,
			requestReview: true,
			reviewDelayHours: 72,
		});

		const responseData = await response.json();
		expect(responseData.success).toBe(true);
		expect(responseData.quote.review_delay_hours).toBe(72);
	});

	it("should handle missing review delay when review requested", async () => {
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(mockListing);
		(QuoteStatusService.providerCompleteJob as jest.Mock).mockResolvedValue(
			mockCompletedQuote
		);

		const req = createMockRequest({
			method: "POST",
			url: `/api/provider/quotes/${mockQuoteId}/complete`,
			params: { id: mockQuoteId },
			session: { user: { id: mockUserId } },
			body: {
				sendInvoice: true,
				requestReview: true,
				// reviewDelayHours not provided
			},
		});

		await POST(req, {
			validatedData: {
				sendInvoice: true,
				requestReview: true,
				// reviewDelayHours not provided
			},
			session: { user: { id: mockUserId } },
			params: { id: mockQuoteId },
		});

		expect(QuoteStatusService.providerCompleteJob).toHaveBeenCalledWith({
			quoteId: mockQuoteId,
			listingId: mockListingId,
			userId: mockUserId,
			resolutionNotes: undefined,
			sendInvoice: true,
			requestReview: true,
			reviewDelayHours: undefined,
		});
	});
}); 