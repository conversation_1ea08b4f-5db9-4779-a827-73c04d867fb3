import { POST as acceptPost } from "@/app/api/provider/quotes/[id]/accept/route";
import { POST as completePost } from "@/app/api/provider/quotes/[id]/complete/route";
import { POST as rejectPost } from "@/app/api/provider/quotes/[id]/reject/route";
import { GET } from "@/app/api/provider/quotes/[id]/route";
import { POST as withdrawPost } from "@/app/api/provider/quotes/[id]/withdraw/route";
import { ListingService } from "@/lib/services/listing.service";
import { MessageService } from "@/lib/services/messaging.service";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { QuoteMessageParticipantType } from "@prisma/client";
import { mockPrisma } from "../../../../../mocks/prisma-mock";
import {
	createMockRequest,
	mockBaseHandler,
	mockProviderUser
} from "../../../../../utils/api-test-utils";

// Mock the services
jest.mock("@/lib/services/quote-status.service", () => ({
	QuoteStatusService: {
		providerAcceptJob: jest.fn(),
		providerDeclineJob: jest.fn(),
		providerCompleteJob: jest.fn(),
		providerWithdrawFromJob: jest.fn()
	}
}));

jest.mock("@/lib/services/listing.service", () => ({
	ListingService: {
		getListingByUserId: jest.fn()
	}
}));

jest.mock("@/lib/services/messaging.service", () => ({
	MessageService: {
		getQuoteMessages: jest.fn()
	}
}));

jest.mock("@/lib/utils/impersonation", () => ({
	isUserImpersonated: jest.fn().mockResolvedValue(false)
}));

jest.mock("@/lib/location-utils", () => ({
	setActiveListingLocation: jest.fn().mockReturnValue({
		id: "location123",
		city: "Denver",
		state: "CO",
		latitude: 39.7392,
		longitude: -104.9903
	})
}));

describe("Provider Quote Routes", () => {
	const mockQuoteId = "quote123";
	const mockListingId = "listing123";
	const mockJobId = "job456";

	const mockQuote = {
		id: mockQuoteId,
		listing_id: mockListingId,
		job_id: mockJobId,
		status: "PROVIDER_INVITED"
	};

	const mockListing = {
		id: mockListingId,
		business_name: "Test RV Service",
		first_name: "John",
		last_name: "Provider"
	};

	beforeEach(() => {
		jest.clearAllMocks();
		// Set up provider authentication
		mockBaseHandler.user = mockProviderUser;
		mockBaseHandler.session = { user: mockProviderUser };
		// Default mock setup for prisma (for accept route)
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		// Default mock setup for ListingService (for other routes)
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(
			mockListing
		);
	});

	describe("GET /api/provider/quotes/[id]", () => {
		const mockFullQuote = {
			id: mockQuoteId,
			listing_id: mockListingId,
			job_id: mockJobId,
			status: "PROVIDER_INVITED",
			warranty_terms_accepted_at: null,
			provider_viewed_at: null,
			job: {
				user: {
					id: "user123",
					first_name: "John",
					last_name: "Doe",
					email: "<EMAIL>"
				},
				warranty_request_id: null,
				warranty_request: null
			},
			listing: {
				id: mockListingId,
				business_name: "Test RV Service",
				locations: [
					{
						id: "location123",
						city: "Denver",
						state: "CO",
						latitude: 39.7392,
						longitude: -104.9903,
						default: true
					}
				]
			}
		};

		const mockMessages = [
			{
				id: "message1",
				quote_id: mockQuoteId,
				content: "Hello, I need help",
				sender: {
					id: "user123",
					type: QuoteMessageParticipantType.USER,
					name: "John Doe",
					email: "<EMAIL>"
				},
				recipient: {
					id: mockListingId,
					type: QuoteMessageParticipantType.PROVIDER,
					name: "Test RV Service",
					email: "<EMAIL>"
				},
				read_at: null,
				created_at: new Date()
			},
			{
				id: "message2",
				quote_id: mockQuoteId,
				content: "I can help you",
				sender: {
					id: mockListingId,
					type: QuoteMessageParticipantType.PROVIDER,
					name: "Test RV Service",
					email: "<EMAIL>"
				},
				recipient: {
					id: "user123",
					type: QuoteMessageParticipantType.USER,
					name: "John Doe",
					email: "<EMAIL>"
				},
				read_at: new Date(),
				created_at: new Date()
			}
		];

		beforeEach(() => {
			jest.clearAllMocks();
			// Set up provider authentication
			mockBaseHandler.user = mockProviderUser;
			mockBaseHandler.session = { user: mockProviderUser };

			// Mock the quote with all relations
			mockPrisma.quote.findUnique.mockResolvedValue(mockFullQuote as any);
			mockPrisma.quote.update.mockResolvedValue(mockFullQuote as any);

			// Mock listing service
			(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(mockListing);

			// Mock message service
			(MessageService.getQuoteMessages as jest.Mock).mockResolvedValue(mockMessages);
		});

		it("should return quote with all fields and relations", async () => {
			const req = createMockRequest({
				method: "GET",
				params: { id: mockQuoteId }
			});

			const response = await GET(req, { params: { id: mockQuoteId } });
			const data = await response.json();

			expect(response.status).toBe(200);

			// Verify quote structure
			expect(data).toHaveProperty("id", mockQuoteId);
			expect(data).toHaveProperty("listing_id", mockListingId);
			expect(data).toHaveProperty("job_id", mockJobId);
			expect(data).toHaveProperty("status", "PROVIDER_INVITED");

			// Verify job relation with user
			expect(data.job).toHaveProperty("user");
			expect(data.job.user).toHaveProperty("id", "user123");
			expect(data.job.user).toHaveProperty("first_name", "John");
			expect(data.job.user).toHaveProperty("last_name", "Doe");
			expect(data.job.user).toHaveProperty("email", "<EMAIL>");

			// Verify listing relation with locations
			expect(data.listing).toHaveProperty("locations");
			expect(data.listing.locations).toHaveLength(1);
			expect(data.listing.locations[0]).toHaveProperty("city", "Denver");
			expect(data.listing.locations[0]).toHaveProperty("state", "CO");

			// Verify messages
			expect(data.messages).toHaveLength(2);
			expect(data.messages[0]).toHaveProperty("sender");
			expect(data.messages[0]).toHaveProperty("recipient");

			// Verify unread messages count (1 unread from user)
			expect(data.unread_messages_count).toBe(1);
			expect(data.unread_message).toHaveProperty("id", "message1");

			// Verify warranty access
			expect(data.warranty_access).toHaveProperty("hasAccess", true);
			expect(data.warranty_access).toHaveProperty("termsAccepted", false);

			// Verify location transformation was called
			expect(data).toHaveProperty("location");
			expect(data.location).toHaveProperty("city", "Denver");

			// Verify provider_viewed_at was updated
			expect(mockPrisma.quote.update).toHaveBeenCalledWith({
				where: { id: mockQuoteId },
				data: { provider_viewed_at: expect.any(Date) }
			});
		});

		it("should handle warranty request with company properly", async () => {
			const warrantyQuote = {
				...mockFullQuote,
				warranty_terms_accepted_at: new Date("2024-01-15"),
				job: {
					...mockFullQuote.job,
					warranty_request_id: "warranty123",
					warranty_request: {
						company: { name: "Winnebago" },
						component: { type: "Engine" },
						listing: { pricing_settings: {} },
						oem_user: { first_name: "OEM", last_name: "User" },
						timeline_updates: []
					}
				}
			};

			mockPrisma.quote.findUnique.mockResolvedValue(warrantyQuote as any);

			const req = createMockRequest({
				method: "GET",
				params: { id: mockQuoteId }
			});

			const response = await GET(req, { params: { id: mockQuoteId } });
			const data = await response.json();

			expect(response.status).toBe(200);

			// Verify warranty access with accepted terms
			expect(data.warranty_access).toHaveProperty("hasAccess", true);
			expect(data.warranty_access).toHaveProperty("termsAccepted", true);
			expect(data.warranty_access).toHaveProperty("companyName", "Winnebago");

			// Verify warranty request relations are included
			expect(data.job.warranty_request).toHaveProperty("company");
			expect(data.job.warranty_request).toHaveProperty("component");
			expect(data.job.warranty_request).toHaveProperty("listing");
			expect(data.job.warranty_request).toHaveProperty("oem_user");
			expect(data.job.warranty_request).toHaveProperty("timeline_updates");
		});

		it("should return 404 when quote not found", async () => {
			mockPrisma.quote.findUnique.mockResolvedValue(null);

			const req = createMockRequest({
				method: "GET",
				params: { id: "nonexistent" }
			});

			const response = await GET(req, { params: { id: "nonexistent" } });
			const data = await response.json();

			expect(response.status).toBe(404);
			expect(data).toHaveProperty("error", "Service request not found");
		});

		it("should return 404 when provider doesn't own the listing", async () => {
			const unauthorizedListing = { id: "different-listing", business_name: "Other Service" };
			(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(unauthorizedListing);

			const req = createMockRequest({
				method: "GET",
				params: { id: mockQuoteId }
			});

			const response = await GET(req, { params: { id: mockQuoteId } });
			const data = await response.json();

			expect(response.status).toBe(404);
			expect(data).toHaveProperty("error", "User does not have a listing");
		});

		it("should not update provider_viewed_at when user is impersonated", async () => {
			const { isUserImpersonated } = require("@/lib/utils/impersonation");
			isUserImpersonated.mockResolvedValue(true);

			const req = createMockRequest({
				method: "GET",
				params: { id: mockQuoteId }
			});

			const response = await GET(req, { params: { id: mockQuoteId } });

			expect(response.status).toBe(200);
			expect(mockPrisma.quote.update).not.toHaveBeenCalled();
		});
	});

	describe("POST /api/provider/quotes/[id]/accept", () => {
		const acceptData = {
			message: "I can help with this job",
			schedulingTimeframe: "next week",
			customer_confirmed: false
		};

		const mockAcceptedQuote = {
			id: mockQuoteId,
			status: "PROVIDER_ACCEPTED",
			message: acceptData.message
		};

		it("should call QuoteStatusService.providerAcceptJob and return quote", async () => {
			(QuoteStatusService.providerAcceptJob as jest.Mock).mockResolvedValue(
				mockAcceptedQuote
			);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId },
				body: acceptData
			});

			const response = await acceptPost(req);
			const data = await response.json();

			expect(mockPrisma.quote.findUnique).toHaveBeenCalledWith({
				where: { id: mockQuoteId }
			});
			expect(QuoteStatusService.providerAcceptJob).toHaveBeenCalledWith({
				quote: mockQuote,
				userId: mockProviderUser.id,
				message: acceptData.message,
			});

			expect(response.status).toBe(200);
			expect(data).toEqual(mockAcceptedQuote);
		});

		it("should handle missing quote", async () => {
			mockPrisma.quote.findUnique.mockResolvedValue(null);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId },
				body: acceptData
			});

			const response = await acceptPost(req);

			expect(response.status).toBe(404);
			expect(QuoteStatusService.providerAcceptJob).not.toHaveBeenCalled();
		});
	});

	describe("POST /api/provider/quotes/[id]/reject", () => {
		const rejectData = {
			rejection_reason: "too_busy" as const,
			rejection_reason_details: "Fully booked this month"
		};

		const mockRejectedQuote = {
			id: mockQuoteId,
			status: "PROVIDER_DECLINED",
			rejection_reason: rejectData.rejection_reason
		};

		it("should call QuoteStatusService.providerDeclineJob and return quote", async () => {
			(QuoteStatusService.providerDeclineJob as jest.Mock).mockResolvedValue(
				mockRejectedQuote
			);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId },
				body: rejectData
			});

			const response = await rejectPost(req);
			const data = await response.json();

			expect(QuoteStatusService.providerDeclineJob).toHaveBeenCalledWith({
				quoteId: mockQuoteId,
				listingId: mockListingId,
				userId: mockProviderUser.id,
				rejectionReason: rejectData.rejection_reason,
				rejectionReasonDetails: rejectData.rejection_reason_details
			});

			expect(response.status).toBe(200);
			expect(data).toEqual(mockRejectedQuote);
		});
	});

	describe("POST /api/provider/quotes/[id]/complete", () => {
		const completeData = {
			sendInvoice: true,
			requestReview: true,
			reviewDelayHours: 24,
			resolutionNotes: "Job completed successfully"
		};

		const mockCompletedQuote = {
			id: mockQuoteId,
			status: "PROVIDER_COMPLETED",
			completed_at: new Date()
		};

		it("should call QuoteStatusService.providerCompleteJob and return response", async () => {
			(QuoteStatusService.providerCompleteJob as jest.Mock).mockResolvedValue(
				mockCompletedQuote
			);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId },
				body: completeData
			});

			const response = await completePost(req);
			const data = await response.json();

			expect(QuoteStatusService.providerCompleteJob).toHaveBeenCalledWith({
				quoteId: mockQuoteId,
				listingId: mockListingId,
				userId: mockProviderUser.id,
				resolutionNotes: completeData.resolutionNotes,
				sendInvoice: completeData.sendInvoice,
				requestReview: completeData.requestReview,
				reviewDelayHours: completeData.reviewDelayHours
			});

			expect(response.status).toBe(200);
			expect(data).toEqual({ success: true, quote: mockCompletedQuote });
		});
	});

	describe("POST /api/provider/quotes/[id]/withdraw", () => {
		const withdrawData = {
			message: "Unable to complete due to schedule conflict",
			rejection_reason: "schedule_conflict" as const,
			rejection_reason_details: "Emergency job came up"
		};

		const mockWithdrawnQuote = {
			id: mockQuoteId,
			status: "PROVIDER_WITHDRAWN",
			message: withdrawData.message
		};

		it("should call QuoteStatusService.providerWithdrawFromJob and return quote", async () => {
			(
				QuoteStatusService.providerWithdrawFromJob as jest.Mock
			).mockResolvedValue(mockWithdrawnQuote);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId },
				body: withdrawData
			});

			const response = await withdrawPost(req);
			const data = await response.json();

			expect(QuoteStatusService.providerWithdrawFromJob).toHaveBeenCalledWith({
				quoteId: mockQuoteId,
				listingId: mockListingId,
				userId: mockProviderUser.id,
				message: withdrawData.message,
				rejectionReason: withdrawData.rejection_reason,
				rejectionReasonDetails: withdrawData.rejection_reason_details
			});

			expect(response.status).toBe(200);
			expect(data).toEqual(mockWithdrawnQuote);
		});
	});

	describe("Error handling", () => {
		it("should handle service errors consistently", async () => {
			(QuoteStatusService.providerAcceptJob as jest.Mock).mockRejectedValue(
				new Error("Quote not found")
			);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId },
				body: { message: "test" }
			});

			await expect(acceptPost(req)).rejects.toThrow("Quote not found");
		});

		it("should handle quote not found from database", async () => {
			mockPrisma.quote.findUnique.mockResolvedValue(null);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId },
				body: { message: "test" }
			});

			const response = await acceptPost(req);

			expect(response.status).toBe(404);
			expect(QuoteStatusService.providerAcceptJob).not.toHaveBeenCalled();
		});
	});
});
