import { createMockRequest, mockBase<PERSON>andler, mockUser } from "@/tests/utils/api-test-utils";
import { prisma as mockPrisma } from "../../../../mocks/prisma-mock";

// Set up mocks before importing the handler
jest.mock("@/lib/api/baseHandler", () => ({
    createHandler: jest.fn((handler, options) => {
        return async (req: any) => {
            mockBaseHandler.user = mockUser;
            return handler.call(mockBaseHandler, req);
        };
    })
}));

// Import the handler after setting up mocks
import { GET as getReviews } from "@/app/api/provider/reviews/route";

describe("Provider Reviews API", () => {
    const mockListing = {
        id: "listing123",
        business_name: "Test Business",
        slug: "test-business"
    };

    const mockActiveReview = {
        id: "review1",
        title: "Great Service",
        content: "Excellent work!",
        overall: 5,
        service: 5,
        responsiveness: 5,
        expertise: 5,
        results: 5,
        communication: 5,
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        author_name: null,
        created_at: new Date(),
        verified: true,
        reply: null,
        status: "active",
        listing: mockListing
    };

    const mockPendingReview = {
        id: "review2",
        title: "Good Service",
        content: "Pretty good work",
        overall: 4,
        service: 4,
        responsiveness: 4,
        expertise: 4,
        results: 4,
        communication: 4,
        first_name: "Jane",
        last_name: "Smith",
        author_name: null,
        created_at: new Date(),
        verified: false,
        reply: null,
        status: "pending_verification",
        listing: mockListing
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockUser;
    });

    it("should return all reviews (active and pending) when status filter is 'all'", async () => {
        // Mock finding user listings
        (mockPrisma.listing.findMany as jest.Mock).mockResolvedValue([
            { id: "listing123" }
        ]);

        // Mock finding reviews
        (mockPrisma.review.findMany as jest.Mock).mockResolvedValue([
            mockActiveReview,
            mockPendingReview
        ]);

        const req = createMockRequest({
            method: "GET",
            url: "/api/provider/reviews?status=all"
        });

        await getReviews(req);

        expect(mockPrisma.review.findMany).toHaveBeenCalledWith({
            where: {
                listing_id: {
                    in: ["listing123"]
                },
                status: { in: ["active", "pending_verification"] }
            },
            include: {
                listing: {
                    select: {
                        business_name: true,
                        slug: true
                    }
                }
            },
            orderBy: {
                created_at: "desc"
            }
        });

        expect(mockBaseHandler.respond).toHaveBeenCalledWith([
            mockActiveReview,
            mockPendingReview
        ]);
    });

    it("should return only active reviews when status filter is 'active'", async () => {
        // Mock finding user listings
        (mockPrisma.listing.findMany as jest.Mock).mockResolvedValue([
            { id: "listing123" }
        ]);

        // Mock finding reviews
        (mockPrisma.review.findMany as jest.Mock).mockResolvedValue([
            mockActiveReview
        ]);

        const req = createMockRequest({
            method: "GET",
            url: "/api/provider/reviews?status=active"
        });

        await getReviews(req);

        expect(mockPrisma.review.findMany).toHaveBeenCalledWith({
            where: {
                listing_id: {
                    in: ["listing123"]
                },
                status: "active"
            },
            include: {
                listing: {
                    select: {
                        business_name: true,
                        slug: true
                    }
                }
            },
            orderBy: {
                created_at: "desc"
            }
        });

        expect(mockBaseHandler.respond).toHaveBeenCalledWith([mockActiveReview]);
    });

    it("should return only pending reviews when status filter is 'pending'", async () => {
        // Mock finding user listings
        (mockPrisma.listing.findMany as jest.Mock).mockResolvedValue([
            { id: "listing123" }
        ]);

        // Mock finding reviews
        (mockPrisma.review.findMany as jest.Mock).mockResolvedValue([
            mockPendingReview
        ]);

        const req = createMockRequest({
            method: "GET",
            url: "/api/provider/reviews?status=pending"
        });

        await getReviews(req);

        expect(mockPrisma.review.findMany).toHaveBeenCalledWith({
            where: {
                listing_id: {
                    in: ["listing123"]
                },
                status: "pending_verification"
            },
            include: {
                listing: {
                    select: {
                        business_name: true,
                        slug: true
                    }
                }
            },
            orderBy: {
                created_at: "desc"
            }
        });

        expect(mockBaseHandler.respond).toHaveBeenCalledWith([mockPendingReview]);
    });

    it("should apply rating filter along with status filter", async () => {
        // Mock finding user listings
        (mockPrisma.listing.findMany as jest.Mock).mockResolvedValue([
            { id: "listing123" }
        ]);

        // Mock finding reviews
        (mockPrisma.review.findMany as jest.Mock).mockResolvedValue([
            mockActiveReview
        ]);

        const req = createMockRequest({
            method: "GET",
            url: "/api/provider/reviews?status=active&rating=5"
        });

        await getReviews(req);

        expect(mockPrisma.review.findMany).toHaveBeenCalledWith({
            where: {
                listing_id: {
                    in: ["listing123"]
                },
                status: "active",
                overall: {
                    gte: 5
                }
            },
            include: {
                listing: {
                    select: {
                        business_name: true,
                        slug: true
                    }
                }
            },
            orderBy: {
                created_at: "desc"
            }
        });
    });

    it("should default to 'all' status when no status filter is provided", async () => {
        // Mock finding user listings
        (mockPrisma.listing.findMany as jest.Mock).mockResolvedValue([
            { id: "listing123" }
        ]);

        // Mock finding reviews
        (mockPrisma.review.findMany as jest.Mock).mockResolvedValue([
            mockActiveReview,
            mockPendingReview
        ]);

        const req = createMockRequest({
            method: "GET",
            url: "/api/provider/reviews"
        });

        await getReviews(req);

        expect(mockPrisma.review.findMany).toHaveBeenCalledWith({
            where: {
                listing_id: {
                    in: ["listing123"]
                },
                status: { in: ["active", "pending_verification"] }
            },
            include: {
                listing: {
                    select: {
                        business_name: true,
                        slug: true
                    }
                }
            },
            orderBy: {
                created_at: "desc"
            }
        });
    });
}); 