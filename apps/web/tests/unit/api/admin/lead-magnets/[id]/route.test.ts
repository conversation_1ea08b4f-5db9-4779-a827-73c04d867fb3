import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "@/tests/utils/api-test-utils";



// Now import handlers after mocks are set up
import { DELETE, GET, PUT } from "@/app/api/admin/lead-magnets/[id]/route";

describe("Individual Lead Magnet Admin API", () => {
    const mockLeadMagnet = {
        id: "lm123",
        title: "Ultimate RV Maintenance Guide",
        description: "A comprehensive guide to RV maintenance",
        image: "https://example.com/images/guide.jpg",
        newsletter_tags: ["RV Maintenance", "Guide"],
        status: "active",
        created_at: new Date("2023-01-01T00:00:00.000Z"),
        updated_at: new Date("2023-01-01T00:00:00.000Z"),
        articles: [
            {
                id: "article1",
                title: "RV Tips Article",
                slug: "rv-tips",
                type: "blog-post"
            }
        ]
    };

    const mockAdminUser = {
        ...mockUser,
        role: "ADMIN"
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockAdminUser;
        mockBaseHandler.isAdmin = true;
        mockBaseHandler.isAuthenticated = true;
    });

    describe("GET /api/admin/lead-magnets/[id]", () => {
        it("should return lead magnet with associated articles", async () => {
            mockPrisma.leadMagnet.findUnique.mockResolvedValue(mockLeadMagnet);

            const req = createMockRequest({
                method: "GET",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id }
            });

            const response = await GET(req);
            const data = await response.json();

            expect(mockPrisma.leadMagnet.findUnique).toHaveBeenCalledWith({
                where: { id: mockLeadMagnet.id },
                include: {
                    articles: {
                        select: {
                            id: true,
                            title: true,
                            slug: true,
                            type: true
                        }
                    }
                }
            });

            expect(response.status).toBe(200);
            // Dates get serialized to strings in JSON responses
            expect(data).toEqual({
                ...mockLeadMagnet,
                created_at: mockLeadMagnet.created_at.toISOString(),
                updated_at: mockLeadMagnet.updated_at.toISOString()
            });
        });

        it("should return 404 if lead magnet not found", async () => {
            mockPrisma.leadMagnet.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/lead-magnets/nonexistent",
                params: { id: "nonexistent" }
            });

            const response = await GET(req);
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data).toEqual({ error: "Lead magnet not found" });
        });

        it("should return 401 if user is not admin", async () => {
            mockBaseHandler.user = mockUser;
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "GET",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id }
            });

            const response = await GET(req);
            expect(response.status).toBe(401);
        });

        it("should return 401 if user is not authenticated", async () => {
            mockBaseHandler.user = null;
            mockBaseHandler.isAuthenticated = false;

            const req = createMockRequest({
                method: "GET",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id }
            });

            const response = await GET(req);
            expect(response.status).toBe(401);
        });
    });

    describe("PUT /api/admin/lead-magnets/[id]", () => {
        const validUpdateData = {
            title: "Updated RV Guide",
            description: "An updated guide for RV owners",
            image: "https://example.com/updated-guide.jpg",
            newsletter_tags: ["Updated Guide", "RV Tips"],
            status: "inactive"
        };

        it("should update lead magnet successfully", async () => {
            const updatedLeadMagnet = {
                ...mockLeadMagnet,
                ...validUpdateData,
                updated_at: new Date('2025-06-14T17:59:46.681Z')
            };

            mockPrisma.leadMagnet.update.mockResolvedValue(updatedLeadMagnet);

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id },
                validatedData: validUpdateData
            });

            const response = await PUT(req);
            const data = await response.json();

            expect(mockPrisma.leadMagnet.update).toHaveBeenCalledWith({
                where: { id: mockLeadMagnet.id },
                data: validUpdateData
            });

            expect(response.status).toBe(200);
            // Dates get serialized to strings in JSON responses  
            expect(data).toEqual({
                ...updatedLeadMagnet,
                created_at: updatedLeadMagnet.created_at.toISOString(),
                updated_at: updatedLeadMagnet.updated_at.toISOString()
            });
        });



        it("should handle database errors", async () => {
            mockPrisma.leadMagnet.update.mockRejectedValue(new Error("Database error"));

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id },
                validatedData: validUpdateData
            });

            const response = await PUT(req);
            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data).toEqual({ error: "Failed to update lead magnet" });
        });

        it("should return 401 if user is not admin", async () => {
            mockBaseHandler.user = mockUser;
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id },
                validatedData: validUpdateData
            });

            const response = await PUT(req);
            expect(response.status).toBe(401);
        });

        it("should return 401 if user is not authenticated", async () => {
            mockBaseHandler.user = null;
            mockBaseHandler.isAuthenticated = false;

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id },
                validatedData: validUpdateData
            });

            const response = await PUT(req);
            expect(response.status).toBe(401);
        });
    });

    describe("DELETE /api/admin/lead-magnets/[id]", () => {
        it("should delete lead magnet successfully when not in use", async () => {
            mockPrisma.article.count.mockResolvedValue(0);
            mockPrisma.leadMagnet.delete.mockResolvedValue(mockLeadMagnet);

            const req = createMockRequest({
                method: "DELETE",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id }
            });

            const response = await DELETE(req);
            const data = await response.json();

            expect(mockPrisma.article.count).toHaveBeenCalledWith({
                where: { lead_magnet_id: mockLeadMagnet.id }
            });

            expect(mockPrisma.leadMagnet.delete).toHaveBeenCalledWith({
                where: { id: mockLeadMagnet.id }
            });

            expect(response.status).toBe(200);
            expect(data).toEqual({ success: true });
        });

        it("should return 400 if lead magnet is being used by articles", async () => {
            mockPrisma.article.count.mockResolvedValue(2);

            const req = createMockRequest({
                method: "DELETE",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id }
            });

            const response = await DELETE(req);
            const data = await response.json();

            expect(mockPrisma.article.count).toHaveBeenCalledWith({
                where: { lead_magnet_id: mockLeadMagnet.id }
            });

            expect(mockPrisma.leadMagnet.delete).not.toHaveBeenCalled();

            expect(response.status).toBe(400);
            expect(data).toEqual({
                error: "Cannot delete lead magnet that is being used by articles"
            });
        });

        it("should handle database errors", async () => {
            mockPrisma.article.count.mockResolvedValue(0);
            mockPrisma.leadMagnet.delete.mockRejectedValue(new Error("Database error"));

            const req = createMockRequest({
                method: "DELETE",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id }
            });

            const response = await DELETE(req);
            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data).toEqual({ error: "Failed to delete lead magnet" });
        });

        it("should return 401 if user is not admin", async () => {
            mockBaseHandler.user = mockUser;
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "DELETE",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id }
            });

            const response = await DELETE(req);
            expect(response.status).toBe(401);
        });

        it("should return 401 if user is not authenticated", async () => {
            mockBaseHandler.user = null;
            mockBaseHandler.isAuthenticated = false;

            const req = createMockRequest({
                method: "DELETE",
                url: `/api/admin/lead-magnets/${mockLeadMagnet.id}`,
                params: { id: mockLeadMagnet.id }
            });

            const response = await DELETE(req);
            expect(response.status).toBe(401);
        });
    });
}); 