import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "@/tests/utils/api-test-utils";



// Now import handlers after mocks are set up
import { GET, POST } from "@/app/api/admin/lead-magnets/route";

describe("Lead Magnets Admin API", () => {
    const mockLeadMagnet = {
        id: "lm123",
        title: "Ultimate RV Maintenance Guide",
        description: "A comprehensive guide to RV maintenance",
        url: "https://example.com/download/guide.pdf",
        image: "https://example.com/images/guide.jpg",
        newsletter_tags: ["RV Maintenance", "Guide"],
        status: "active",
        created_at: new Date("2023-01-01T00:00:00.000Z"),
        updated_at: new Date("2023-01-01T00:00:00.000Z"),
        _count: {
            articles: 2
        }
    };

    const mockAdminUser = {
        ...mockUser,
        role: "ADMIN"
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockAdminUser;
        mockBaseHandler.isAdmin = true;
        mockBaseHandler.isAuthenticated = true;
    });

    describe("GET /api/admin/lead-magnets", () => {
        it("should return all lead magnets with article count", async () => {
            const mockLeadMagnets = [mockLeadMagnet];
            mockPrisma.leadMagnet.findMany.mockResolvedValue(mockLeadMagnets);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/lead-magnets"
            });

            const response = await GET(req);
            const data = await response.json();

            expect(mockPrisma.leadMagnet.findMany).toHaveBeenCalledWith({
                orderBy: {
                    created_at: "desc"
                },
                include: {
                    _count: {
                        select: {
                            articles: true
                        }
                    }
                }
            });

            expect(response.status).toBe(200);
            // Dates get serialized to strings in JSON responses
            expect(data).toEqual([{
                ...mockLeadMagnet,
                created_at: mockLeadMagnet.created_at.toISOString(),
                updated_at: mockLeadMagnet.updated_at.toISOString()
            }]);
        });

        it("should return 401 if user is not admin", async () => {
            mockBaseHandler.user = mockUser;
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/lead-magnets"
            });

            const response = await GET(req);
            expect(response.status).toBe(401);
        });

        it("should return 401 if user is not authenticated", async () => {
            mockBaseHandler.user = null;
            mockBaseHandler.isAuthenticated = false;

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/lead-magnets"
            });

            const response = await GET(req);
            expect(response.status).toBe(401);
        });
    });

    describe("POST /api/admin/lead-magnets", () => {
        const validLeadMagnetData = {
            title: "New RV Guide",
            description: "A new guide for RV owners",
            image: "https://example.com/new-guide.jpg",
            newsletter_tags: ["New Guide", "RV Tips"],
            status: "active"
        };

        it("should create a new lead magnet", async () => {
            const createdLeadMagnet = {
                id: "lm456",
                ...validLeadMagnetData,
                created_at: new Date("2023-01-01T00:00:00.000Z"),
                updated_at: new Date("2023-01-01T00:00:00.000Z")
            };

            mockPrisma.leadMagnet.create.mockResolvedValue(createdLeadMagnet);

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/lead-magnets",
                validatedData: validLeadMagnetData
            });

            const response = await POST(req);
            const data = await response.json();

            expect(mockPrisma.leadMagnet.create).toHaveBeenCalledWith({
                data: validLeadMagnetData
            });

            expect(response.status).toBe(200);
            // Dates get serialized to strings in JSON responses
            expect(data).toEqual({
                ...createdLeadMagnet,
                created_at: createdLeadMagnet.created_at.toISOString(),
                updated_at: createdLeadMagnet.updated_at.toISOString()
            });
        });



        it("should handle database errors", async () => {
            mockPrisma.leadMagnet.create.mockRejectedValue(new Error("Database error"));

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/lead-magnets",
                validatedData: validLeadMagnetData
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data).toEqual({ error: "Failed to create lead magnet" });
        });

        it("should return 401 if user is not admin", async () => {
            mockBaseHandler.user = mockUser;
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/lead-magnets",
                validatedData: validLeadMagnetData
            });

            const response = await POST(req);
            expect(response.status).toBe(401);
        });

        it("should return 401 if user is not authenticated", async () => {
            mockBaseHandler.user = null;
            mockBaseHandler.isAuthenticated = false;

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/lead-magnets",
                validatedData: validLeadMagnetData
            });

            const response = await POST(req);
            expect(response.status).toBe(401);
        });
    });
}); 