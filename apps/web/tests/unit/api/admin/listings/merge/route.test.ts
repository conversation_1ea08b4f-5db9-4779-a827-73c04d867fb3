import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "@/tests/utils/api-test-utils";

// Import handler after setting up mocks
import { POST } from "@/app/api/admin/listings/merge/route";

describe("Admin Listings Merge API", () => {
    const mockPrimaryListing = {
        id: "listing1",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        email: "<EMAIL>",
        business_name: "John's RV Service",
        phone: "************",
        owner_id: "owner1",
        rvtaa_member_ids: ["rvtaa1", "rvtaa2"],
        location: {
            id: "loc1",
            city: "Denver",
            state: "CO"
        }
    };

    const mockMergingListings = [
        {
            id: "listing2",
            first_name: "<PERSON>",
            last_name: "<PERSON>",
            email: "<EMAIL>",
            business_name: "<PERSON>'s RV Repair",
            phone: "************",
            owner_id: "owner2",
            rvtaa_member_id: "rvtaa3",
            location: {
                id: "loc2",
                city: "Boulder",
                state: "CO"
            },
            owner: {
                id: "owner2",
                email: "<EMAIL>"
            }
        },
        {
            id: "listing3",
            first_name: "Bob",
            last_name: "Johnson",
            email: "<EMAIL>",
            business_name: "Bob's Mobile RV",
            phone: "************",
            owner_id: "owner3",
            rvtaa_member_id: "rvtaa4",
            location: {
                id: "loc3",
                city: "Fort Collins",
                state: "CO"
            },
            owner: {
                id: "owner3",
                email: "<EMAIL>"
            }
        }
    ];

    const mockAdminUser = {
        ...mockUser,
        role: "ADMIN"
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockAdminUser;
        mockBaseHandler.isAdmin = true;
        mockBaseHandler.isAuthenticated = true;

        // Setup default mock responses
        mockPrisma.nonDuplicatePair.findMany.mockResolvedValue([]);
        mockPrisma.listing.findMany.mockResolvedValue(mockMergingListings);
        mockPrisma.listing.findUnique.mockResolvedValue(mockPrimaryListing);
        mockPrisma.userListing.create.mockResolvedValue({ user_id: "owner2", listing_id: "listing1" } as any);
        mockPrisma.listingInteraction.updateMany.mockResolvedValue({ count: 5 } as any);
        mockPrisma.listingInteractionSession.updateMany.mockResolvedValue({ count: 3 } as any);
        mockPrisma.userListing.deleteMany.mockResolvedValue({ count: 2 } as any);
        mockPrisma.review.updateMany.mockResolvedValue({ count: 4 } as any);
        mockPrisma.location.deleteMany.mockResolvedValue({ count: 2 } as any);
        mockPrisma.verificationCelebration.updateMany.mockResolvedValue({ count: 1 } as any);
        mockPrisma.quote.updateMany.mockResolvedValue({ count: 3 } as any);
        mockPrisma.qRCodeScan.updateMany.mockResolvedValue({ count: 2 } as any);
        mockPrisma.nonDuplicatePair.deleteMany.mockResolvedValue({ count: 1 } as any);
        mockPrisma.listing.update.mockResolvedValue(mockPrimaryListing as any);
        mockPrisma.listing.deleteMany.mockResolvedValue({ count: 2 } as any);
    });

    describe("POST /api/admin/listings/merge", () => {
        it("should successfully merge listings", async () => {
            const mergeData = {
                primaryListingId: "listing1",
                listingsToMerge: ["listing2", "listing3"]
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: mergeData
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);

            // Verify the merge process was executed correctly
            expect(mockPrisma.nonDuplicatePair.findMany).toHaveBeenCalledWith({
                where: {
                    OR: [
                        { listing1_id: { in: ["listing2", "listing3"] } },
                        { listing2_id: { in: ["listing2", "listing3"] } }
                    ]
                }
            });

            expect(mockPrisma.listing.findMany).toHaveBeenCalledWith({
                where: { id: { in: ["listing2", "listing3"] } },
                include: {
                    locations: true,
                    owner: true
                }
            });

            expect(mockPrisma.listing.findUnique).toHaveBeenCalledWith({
                where: { id: "listing1" },
                include: { locations: true }
            });
        });

        it("should create user listings for different owners", async () => {
            const mergeData = {
                primaryListingId: "listing1",
                listingsToMerge: ["listing2", "listing3"]
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: mergeData
            });

            await POST(req);

            // Should create UserListing entries for owner2 and owner3
            expect(mockPrisma.userListing.create).toHaveBeenCalledTimes(2);
            expect(mockPrisma.userListing.create).toHaveBeenCalledWith({
                data: {
                    user_id: "owner2",
                    listing_id: "listing1",
                    role: "MANAGER",
                    permissions: {}
                }
            });
            expect(mockPrisma.userListing.create).toHaveBeenCalledWith({
                data: {
                    user_id: "owner3",
                    listing_id: "listing1",
                    role: "MANAGER",
                    permissions: {}
                }
            });
        });

        it("should update all related entities to point to primary listing", async () => {
            const mergeData = {
                primaryListingId: "listing1",
                listingsToMerge: ["listing2", "listing3"]
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: mergeData
            });

            await POST(req);

            // Verify all related entities are updated
            expect(mockPrisma.listingInteraction.updateMany).toHaveBeenCalledWith({
                where: { listing_id: { in: ["listing2", "listing3"] } },
                data: { listing_id: "listing1" }
            });

            expect(mockPrisma.listingInteractionSession.updateMany).toHaveBeenCalledWith({
                where: { listing_id: { in: ["listing2", "listing3"] } },
                data: { listing_id: "listing1" }
            });

            expect(mockPrisma.review.updateMany).toHaveBeenCalledWith({
                where: { listing_id: { in: ["listing2", "listing3"] } },
                data: { listing_id: "listing1" }
            });

            expect(mockPrisma.verificationCelebration.updateMany).toHaveBeenCalledWith({
                where: { listing_id: { in: ["listing2", "listing3"] } },
                data: { listing_id: "listing1" }
            });

            expect(mockPrisma.quote.updateMany).toHaveBeenCalledWith({
                where: { listing_id: { in: ["listing2", "listing3"] } },
                data: { listing_id: "listing1" }
            });

            expect(mockPrisma.qRCodeScan.updateMany).toHaveBeenCalledWith({
                where: { listing_id: { in: ["listing2", "listing3"] } },
                data: { listing_id: "listing1" }
            });
        });

        it("should merge RVTAA member IDs correctly", async () => {
            const mergeData = {
                primaryListingId: "listing1",
                listingsToMerge: ["listing2", "listing3"]
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: mergeData
            });

            await POST(req);

            // Verify RVTAA member IDs are merged
            expect(mockPrisma.listing.update).toHaveBeenCalledWith({
                where: { id: "listing1" },
                data: {
                    rvtaa_member_ids: ["rvtaa1", "rvtaa2", "rvtaa3", "rvtaa4"]
                }
            });
        });

        it("should clean up entities and delete merged listings", async () => {
            const mergeData = {
                primaryListingId: "listing1",
                listingsToMerge: ["listing2", "listing3"]
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: mergeData
            });

            await POST(req);

            // Verify cleanup operations
            expect(mockPrisma.userListing.deleteMany).toHaveBeenCalledWith({
                where: { listing_id: { in: ["listing2", "listing3"] } }
            });

            expect(mockPrisma.location.deleteMany).toHaveBeenCalledWith({
                where: { listing_id: { in: ["listing2", "listing3"] } }
            });

            expect(mockPrisma.nonDuplicatePair.deleteMany).toHaveBeenCalledWith({
                where: {
                    OR: [
                        { listing1_id: { in: ["listing2", "listing3"] } },
                        { listing2_id: { in: ["listing2", "listing3"] } }
                    ]
                }
            });

            // Finally delete the merged listings
            expect(mockPrisma.listing.deleteMany).toHaveBeenCalledWith({
                where: { id: { in: ["listing2", "listing3"] } }
            });
        });

        it("should reject merge if listings are protected by non-duplicate pairs", async () => {
            // Mock non-duplicate pairs exist
            mockPrisma.nonDuplicatePair.findMany.mockResolvedValue([
                { id: "pair1", listing1_id: "listing2", listing2_id: "listing3" }
            ] as any);

            const mergeData = {
                primaryListingId: "listing1",
                listingsToMerge: ["listing2", "listing3"]
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: mergeData
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Listing is protected from merging");

            // Should not proceed with merge operations
            expect(mockPrisma.listing.findMany).not.toHaveBeenCalled();
        });

        it("should return error if primary listing not found", async () => {
            mockPrisma.listing.findUnique.mockResolvedValue(null);

            const mergeData = {
                primaryListingId: "nonexistent",
                listingsToMerge: ["listing2", "listing3"]
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: mergeData
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Failed to merge listings");
        });

        it("should handle same owner scenario correctly", async () => {
            // Mock scenario where one of the merging listings has the same owner as primary
            const mockMergingListingsWithSameOwner = [
                {
                    ...mockMergingListings[0],
                    owner_id: "owner1" // Same as primary listing
                },
                mockMergingListings[1]
            ];

            mockPrisma.listing.findMany.mockResolvedValue(mockMergingListingsWithSameOwner);

            const mergeData = {
                primaryListingId: "listing1",
                listingsToMerge: ["listing2", "listing3"]
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: mergeData
            });

            await POST(req);

            // Should only create UserListing for the different owner (owner3)
            expect(mockPrisma.userListing.create).toHaveBeenCalledTimes(1);
            expect(mockPrisma.userListing.create).toHaveBeenCalledWith({
                data: {
                    user_id: "owner3",
                    listing_id: "listing1",
                    role: "MANAGER",
                    permissions: {}
                }
            });
        });

        it("should require admin role", async () => {
            mockBaseHandler.user = mockUser; // Regular user
            mockBaseHandler.isAdmin = false;

            const mergeData = {
                primaryListingId: "listing1",
                listingsToMerge: ["listing2", "listing3"]
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: mergeData
            });

            const response = await POST(req);

            expect(response.status).toBe(401);
        });

        it("should validate request body", async () => {
            // Mock the baseHandler to return validation error
            mockBaseHandler.respond.mockReturnValueOnce({
                json: () => ({ error: "Validation failed", code: "VALIDATION_ERROR" }),
                status: 422
            });

            const invalidData = {
                primaryListingId: "listing1"
                // Missing listingsToMerge
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: invalidData
            });

            const response = await POST(req);

            expect(response.status).toBe(422);
        });

        it("should handle transaction rollback on error", async () => {
            // Mock an error during the transaction
            mockPrisma.listing.findUnique.mockRejectedValue(new Error("Database error"));

            const mergeData = {
                primaryListingId: "listing1",
                listingsToMerge: ["listing2", "listing3"]
            };

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/listings/merge",
                body: mergeData
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Failed to merge listings");
        });
    });
});