import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { GET } from '../../../../../app/api/admin/listings/export/route';
import prisma from '../../../../../lib/prisma';
import { createMockRequest } from '../../../../utils/api-test-utils';

// Mock Prisma
jest.mock('../../../../../lib/prisma', () => ({
    listing: {
        findMany: jest.fn()
    }
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

// Helper function to create mock listing data
const createMockListing = (overrides: any = {}) => ({
    id: 'mock-id',
    slug: 'mock-slug',
    first_name: 'Mock',
    last_name: 'User',
    business_name: 'Mock Business',
    email: '<EMAIL>',
    phone: '555-0000',
    rv_help_verification_level: 'NONE',
    rvtaa_technician_level: null,
    nrvia_inspector_level: null,
    status: 'ACTIVE',
    is_active: true,
    created_at: new Date(),
    updated_at: new Date(),
    email_verified_at: null,
    owner: { email: '<EMAIL>' },
    users: [],
    ...overrides
});

describe('Listings Export Route', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should export unverified listings with duplicate detection', async () => {
        // Mock data
        const mockUnverifiedListings = [
            createMockListing({
                id: 'unverified-1',
                first_name: 'John',
                last_name: 'Doe',
                business_name: 'John Doe RV Service',
                email: '<EMAIL>',
                phone: '555-1234',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: 2,
                nrvia_inspector_level: null,
                owner: { email: '<EMAIL>' },
                users: []
            })
        ] as any;

        const mockVerifiedListings = [
            {
                id: 'verified-1',
                slug: 'john-doe-rv-service',
                email: '<EMAIL>',
                phone: '555-1234',
                business_name: 'John Doe RV Service',
                first_name: 'John',
                last_name: 'Doe'
            }
        ] as any;

        // Mock the first call (unverified listings)
        mockPrisma.listing.findMany
            .mockResolvedValueOnce(mockUnverifiedListings)
            // Mock the second call (verified listings for duplicate detection)
            .mockResolvedValueOnce(mockVerifiedListings);

        // Create mock request
        const mockRequest = createMockRequest({
            url: '/api/admin/listings/export?type=unverified'
        });

        // Call the handler
        const response = await GET(mockRequest);

        // Verify the response
        expect(response).toBeDefined();

        // The mock baseHandler returns an object with json method, not a Response
        if (response && typeof response === 'object' && 'json' in response) {
            const responseData = response.json();
            expect(responseData).toBeDefined();
            return;
        }

        // If it's a real Response object
        if (response instanceof Response) {
            expect(response.headers.get('Content-Type')).toBe('text/csv');
            expect(response.headers.get('Content-Disposition')).toContain('attachment');
            const csvContent = await response.text();

            // Verify CSV headers include duplicate detection columns
            expect(csvContent).toContain('Duplicate Listing');
            expect(csvContent).toContain('Potential Duplicate');

            // Verify the duplicate detection worked (email match should be "Yes")
            expect(csvContent).toContain('Yes'); // Duplicate Listing column
            expect(csvContent).toContain('/admin/listings/verified-1/edit'); // Potential Duplicate link
        }
    });

    it('should handle verified listings without duplicate detection', async () => {
        // Mock data for verified listings
        const mockVerifiedListings = [
            createMockListing({
                id: 'verified-1',
                first_name: 'Jane',
                last_name: 'Smith',
                business_name: 'Jane Smith RV Service',
                email: '<EMAIL>',
                phone: '555-5678',
                rv_help_verification_level: 'VERIFIED',
                rvtaa_technician_level: null,
                nrvia_inspector_level: null,
                owner: { email: '<EMAIL>' },
                users: []
            })
        ] as any;

        mockPrisma.listing.findMany.mockResolvedValueOnce(mockVerifiedListings);

        // Create mock request
        const mockRequest = createMockRequest({
            url: '/api/admin/listings/export?type=verified'
        });

        // Call the handler
        const response = await GET(mockRequest);

        // Verify the response
        expect(response).toBeDefined();

        // The mock baseHandler returns an object with json method, not a Response
        if (response && typeof response === 'object' && 'json' in response) {
            const responseData = response.json();
            expect(responseData).toBeDefined();
            return;
        }

        // If it's a real Response object
        if (response instanceof Response) {
            const csvContent = await response.text();

            // Verify CSV headers include duplicate detection columns
            expect(csvContent).toContain('Duplicate Listing');
            expect(csvContent).toContain('Potential Duplicate');

            // Verify verified listings show "No" for duplicate detection
            expect(csvContent).toContain('No'); // Duplicate Listing column
        }
    });

    it('should detect phone number matches as "Maybe" duplicates', async () => {
        // Mock data
        const mockUnverifiedListings = [
            createMockListing({
                id: 'unverified-1',
                first_name: 'Bob',
                last_name: 'Wilson',
                business_name: 'Bob Wilson RV Repair',
                email: '<EMAIL>',
                phone: '555-9999',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: 1,
                nrvia_inspector_level: null,
                owner: { email: '<EMAIL>' },
                users: []
            })
        ] as any;

        const mockVerifiedListings = [
            {
                id: 'verified-1',
                slug: 'different-business',
                email: '<EMAIL>',
                phone: '555-9999',
                business_name: 'Different Business Name',
                first_name: 'Different',
                last_name: 'Person'
            }
        ] as any;

        // Mock the calls
        mockPrisma.listing.findMany
            .mockResolvedValueOnce(mockUnverifiedListings)
            .mockResolvedValueOnce(mockVerifiedListings);

        // Create mock request
        const mockRequest = createMockRequest({
            url: '/api/admin/listings/export?type=unverified'
        });

        // Call the handler
        const response = await GET(mockRequest);

        // Verify the response
        expect(response).toBeDefined();

        // The mock baseHandler returns an object with json method, not a Response
        if (response && typeof response === 'object' && 'json' in response) {
            const responseData = response.json();
            expect(responseData).toBeDefined();
            return;
        }

        // If it's a real Response object
        if (response instanceof Response) {
            const csvContent = await response.text();

            // Verify phone match is detected as "Maybe"
            expect(csvContent).toContain('Maybe'); // Duplicate Listing column
            expect(csvContent).toContain('/admin/listings/verified-1/edit'); // Potential Duplicate link
        }
    });

    it('should detect business name matches as "Maybe" duplicates', async () => {
        // Mock data
        const mockUnverifiedListings = [
            createMockListing({
                id: 'unverified-1',
                first_name: 'Alice',
                last_name: 'Johnson',
                business_name: 'Exact Same Business Name',
                email: '<EMAIL>',
                phone: '555-1111',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: null,
                nrvia_inspector_level: 2,
                owner: { email: '<EMAIL>' },
                users: []
            })
        ] as any;

        const mockVerifiedListings = [
            {
                id: 'verified-1',
                slug: 'exact-same-business-name',
                email: '<EMAIL>',
                phone: '555-2222',
                business_name: 'Exact Same Business Name',
                first_name: 'Different',
                last_name: 'Person'
            }
        ] as any;

        // Mock the calls
        mockPrisma.listing.findMany
            .mockResolvedValueOnce(mockUnverifiedListings)
            .mockResolvedValueOnce(mockVerifiedListings);

        // Create mock request
        const mockRequest = createMockRequest({
            url: '/api/admin/listings/export?type=unverified'
        });

        // Call the handler
        const response = await GET(mockRequest);

        // Verify the response
        expect(response).toBeDefined();

        // The mock baseHandler returns an object with json method, not a Response
        if (response && typeof response === 'object' && 'json' in response) {
            const responseData = response.json();
            expect(responseData).toBeDefined();
            return;
        }

        // If it's a real Response object
        if (response instanceof Response) {
            const csvContent = await response.text();

            // Verify business name match is detected as "Maybe"
            expect(csvContent).toContain('Maybe'); // Duplicate Listing column
            expect(csvContent).toContain('/admin/listings/verified-1/edit'); // Potential Duplicate link
        }
    });

    it('should correctly identify tech status based on rvtaa_technician_level and nrvia_inspector_level', async () => {
        // Mock data with different tech levels
        const mockListings = [
            createMockListing({
                id: 'tech-1',
                first_name: 'Tech',
                last_name: 'One',
                business_name: 'Tech One RV Service',
                email: '<EMAIL>',
                phone: '555-0001',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: 1,
                nrvia_inspector_level: null,
                owner: { email: '<EMAIL>' },
                users: []
            }),
            createMockListing({
                id: 'tech-2',
                first_name: 'Tech',
                last_name: 'Two',
                business_name: 'Tech Two RV Service',
                email: '<EMAIL>',
                phone: '555-0002',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: 3,
                nrvia_inspector_level: null,
                owner: { email: '<EMAIL>' },
                users: []
            }),
            createMockListing({
                id: 'inspector-1',
                first_name: 'Inspector',
                last_name: 'One',
                business_name: 'Inspector One RV Service',
                email: '<EMAIL>',
                phone: '555-0004',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: null,
                nrvia_inspector_level: 1,
                owner: { email: '<EMAIL>' },
                users: []
            }),
            createMockListing({
                id: 'both-1',
                first_name: 'Both',
                last_name: 'One',
                business_name: 'Both One RV Service',
                email: '<EMAIL>',
                phone: '555-0005',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: 2,
                nrvia_inspector_level: 2,
                owner: { email: '<EMAIL>' },
                users: []
            }),
            createMockListing({
                id: 'non-eligible',
                first_name: 'Non',
                last_name: 'Eligible',
                business_name: 'Non Eligible RV Service',
                email: '<EMAIL>',
                phone: '555-0006',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: null,
                nrvia_inspector_level: null,
                owner: { email: '<EMAIL>' },
                users: []
            })
        ] as any;

        mockPrisma.listing.findMany
            .mockResolvedValueOnce(mockListings)
            .mockResolvedValueOnce([]); // No verified listings for duplicate detection

        // Create mock request
        const mockRequest = createMockRequest({
            url: '/api/admin/listings/export?type=unverified'
        });

        // Call the handler
        const response = await GET(mockRequest);

        // Verify the response
        expect(response).toBeDefined();

        // The mock baseHandler returns an object with json method, not a Response
        if (response && typeof response === 'object' && 'json' in response) {
            const responseData = response.json();
            expect(responseData).toBeDefined();
            return;
        }

        // If it's a real Response object
        if (response instanceof Response) {
            const csvContent = await response.text();

            // Verify CSV headers include the new Is Tech? column
            expect(csvContent).toContain('Is Tech?');

            // Verify tech levels are correctly identified
            expect(csvContent).toContain('Yes'); // For rvtaa_technician_level 1, 3 and nrvia_inspector_level 1, 2
            expect(csvContent).toContain('No'); // For both levels null (non-eligible)
        }
    });

    it('should only export eligible unverified listings (with rvtaa_technician_level > 0 OR nrvia_inspector_level > 0)', async () => {
        // Mock data with mixed eligibility
        const mockListings = [
            {
                id: 'eligible-tech',
                first_name: 'Eligible',
                last_name: 'Tech',
                business_name: 'Eligible Tech RV Service',
                email: '<EMAIL>',
                phone: '555-0007',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: 1,
                nrvia_inspector_level: null,
                owner: { email: '<EMAIL>' },
                users: []
            },
            {
                id: 'eligible-inspector',
                first_name: 'Eligible',
                last_name: 'Inspector',
                business_name: 'Eligible Inspector RV Service',
                email: '<EMAIL>',
                phone: '555-0008',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: null,
                nrvia_inspector_level: 1,
                owner: { email: '<EMAIL>' },
                users: []
            },
            {
                id: 'ineligible',
                first_name: 'Ineligible',
                last_name: 'User',
                business_name: 'Ineligible RV Service',
                email: '<EMAIL>',
                phone: '555-0009',
                rv_help_verification_level: 'NONE',
                rvtaa_technician_level: null,
                nrvia_inspector_level: null,
                owner: { email: '<EMAIL>' },
                users: []
            }
        ];

        mockPrisma.listing.findMany
            .mockResolvedValueOnce(mockListings)
            .mockResolvedValueOnce([]); // No verified listings for duplicate detection

        // Create mock request
        const mockRequest = createMockRequest({
            url: '/api/admin/listings/export?type=unverified'
        });

        // Call the handler
        const response = await GET(mockRequest);

        // Verify the response
        expect(response).toBeDefined();

        // The mock baseHandler returns an object with json method, not a Response
        if (response && typeof response === 'object' && 'json' in response) {
            const responseData = response.json();
            expect(responseData).toBeDefined();
            return;
        }

        // If it's a real Response object
        if (response instanceof Response) {
            const csvContent = await response.text();

            // Verify only eligible listings are included
            expect(csvContent).toContain('Eligible Tech');
            expect(csvContent).toContain('Eligible Inspector');
            expect(csvContent).not.toContain('Ineligible'); // Should not be included

            // Verify the query was called with the correct eligibility filter
            expect(mockPrisma.listing.findMany).toHaveBeenCalledWith(
                expect.objectContaining({
                    where: expect.objectContaining({
                        OR: [
                            {
                                rvtaa_technician_level: {
                                    gt: 0
                                }
                            },
                            {
                                nrvia_inspector_level: {
                                    gt: 0
                                }
                            }
                        ]
                    })
                })
            );
        }
    });
}); 