import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "@/tests/utils/api-test-utils";

// Import handler after setting up mocks
import { GET } from "@/app/api/admin/listings/[id]/notifications/route";

describe("Admin Listings Notifications API", () => {
    const mockListing = {
        id: "listing1",
        business_name: "Test RV Service",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        phone: "+1234567890",
        notification_sms: "+1234567890",
        email: "<EMAIL>",
        notification_email: "<EMAIL>"
    };

    const mockEmail = {
        id: "email1",
        to_email: "<EMAIL>",
        subject: "Test Email",
        text: "Test email content",
        html: "<p>Test email content</p>",
        status: "sent",
        response: JSON.stringify({ accepted: ["<EMAIL>"] }),
        send_date: new Date()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = { ...mockUser, role: "ADMIN" };
        mockBaseHandler.isAdmin = true;
        mockBaseHandler.params = { id: "listing1" };
        mockPrisma.listing.findUnique.mockResolvedValue(mockListing);
        mockPrisma.emailOutbox.findMany.mockResolvedValue([mockEmail]);
        mockPrisma.emailOutbox.count.mockResolvedValue(1);
    });

    it("should return notifications for a listing", async () => {
        const req = createMockRequest({
            method: "GET",
            url: "/api/admin/listings/listing1/notifications?type=all&limit=50",
            params: { id: "listing1" }
        });

        await GET(req);

        expect(mockPrisma.listing.findUnique).toHaveBeenCalledWith({
            where: { id: "listing1" },
            select: {
                id: true,
                business_name: true,
                first_name: true,
                last_name: true,
                phone: true,
                notification_sms: true,
                email: true,
                notification_email: true
            }
        });

        expect(mockPrisma.emailOutbox.findMany).toHaveBeenCalledWith({
            where: {
                to_email: {
                    in: ["<EMAIL>", "<EMAIL>"]
                }
            },
            orderBy: {
                send_date: "desc"
            },
            skip: 0,
            take: 50
        });

        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            expect.objectContaining({
                notifications: expect.arrayContaining([
                    expect.objectContaining({
                        id: "email1",
                        type: "email",
                        to: "<EMAIL>",
                        subject: "Test Email",
                        status: "delivered"
                    })
                ]),
                listing: mockListing
            })
        );
    });

    it("should return 404 for non-existent listing", async () => {
        mockPrisma.listing.findUnique.mockResolvedValue(null);

        const req = createMockRequest({
            method: "GET",
            url: "/api/admin/listings/nonexistent/notifications",
            params: { id: "nonexistent" }
        });

        await GET(req);

        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "Listing not found" },
            404
        );
    });

    it("should filter by email type only", async () => {
        const req = createMockRequest({
            method: "GET",
            url: "/api/admin/listings/listing1/notifications?type=email",
            params: { id: "listing1" }
        });

        await GET(req);

        expect(mockPrisma.emailOutbox.findMany).toHaveBeenCalled();
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            expect.objectContaining({
                notifications: expect.arrayContaining([
                    expect.objectContaining({
                        type: "email"
                    })
                ])
            })
        );
    });
}); 