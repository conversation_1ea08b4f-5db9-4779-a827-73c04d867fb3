import { mockPrisma } from "@/tests/mocks/prisma-mock";
import { createMockRequest, mockBaseHandler, mockUser } from "@/tests/utils/api-test-utils";


jest.mock("@/lib/services", () => ({
    emailService: {
        send: jest.fn()
    }
}));

// Mock the DispatchEmail component
jest.mock("@/components/email-templates/DispatchEmail", () => ({
    DispatchEmail: jest.fn(() => ({ type: "DispatchEmail" }))
}));

// Mock React.createElement for the email template
jest.mock("react", () => ({
    createElement: jest.fn((component, props) => ({ component, props }))
}));

// Import after mocks are set up
import { POST } from "@/app/api/admin/dispatch-emails/test/route";
import { emailService } from "@/lib/services";
import React from "react";

describe("Test Dispatch Email API Handler", () => {
    const mockProviders = [
        {
            id: "provider1",
            first_name: "<PERSON>",
            last_name: "<PERSON><PERSON>",
            business_name: "John's RV Service",
            notification_email: "<EMAIL>",
            categories: {
                "rv-repair": {
                    selected: true,
                    isPrimary: true,
                    subcategories: {}
                }
            },
            location: {
                id: "loc1",
                latitude: 40.7128,
                longitude: -74.006
            }
        },
        {
            id: "provider2",
            first_name: "Jane",
            last_name: "Smith",
            business_name: "Jane's Mobile RV",
            notification_email: "<EMAIL>",
            categories: {
                "rv-inspection": {
                    selected: true,
                    isPrimary: true,
                    subcategories: {}
                }
            },
            location: {
                id: "loc2",
                latitude: 41.8781,
                longitude: -87.6298
            }
        }
    ];

    const mockListing = {
        id: "specific-listing",
        first_name: "Bob",
        last_name: "Wilson",
        business_name: "Bob's RV Repair",
        notification_email: "<EMAIL>",
        settings_dispatch_emails_opt_out: false,
        categories: {
            "rv-repair": {
                selected: true,
                isPrimary: true,
                subcategories: {}
            }
        },
        location: {
            id: "loc3",
            latitude: 39.9526,
            longitude: -75.1652
        }
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockPrisma.listing.findMany.mockResolvedValue(mockProviders);
        mockPrisma.listing.findUnique.mockResolvedValue(mockListing);
        mockPrisma.dispatchEmail.create.mockResolvedValue({
            id: "test-email-123",
            title: "Test Email",
            subject: "Test Subject",
            body: "Test Body",
            status: "SENT",
            sent_at: new Date(),
            scheduled_for: new Date()
        });
        (emailService.send as jest.Mock).mockResolvedValue({ success: true });

        // Set up admin user for all tests
        mockBaseHandler.user = { ...mockUser, role: "ADMIN" };
        mockBaseHandler.isAdmin = true;
    });

    describe("POST /api/admin/dispatch-emails/test", () => {
        const mockValidatedData = {
            title: "Test Email",
            subject: "Test Subject",
            body: "Test Body",
            category: "rv-repair"
        };

        it("should send test email to specific listing when listingId is provided", async () => {
            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: {
                    ...mockValidatedData,
                    listingId: "specific-listing"
                }
            });

            const response = await POST(req);
            const responseData = await response.json();

            // Verify listing was found
            expect(mockPrisma.listing.findUnique).toHaveBeenCalledWith({
                where: { id: "specific-listing" },
                include: { locations: true }
            });

            // Verify email was sent
            expect(emailService.send).toHaveBeenCalledWith({
                to: "<EMAIL>",
                subject: "Test Subject",
                react: expect.any(Object),
                emailType: "dispatch_email_test"
            });

            // Verify React.createElement was called with correct props
            expect(React.createElement).toHaveBeenCalledWith(
                expect.any(Function), // DispatchEmail component
                {
                    title: "Test Email",
                    subject: "Test Subject",
                    body: "Test Body",
                    providerName: "Bob Wilson",
                    businessName: "Bob's RV Repair"
                }
            );

            // Verify dispatch email record was created with recipient tracking
            expect(mockPrisma.dispatchEmail.create).toHaveBeenCalledWith({
                data: {
                    title: "Test Email",
                    subject: "Test Subject",
                    body: "Test Body",
                    status: "SENT",
                    sent_at: expect.any(Date),
                    scheduled_for: expect.any(Date),
                    recipients: [{
                        listingId: "specific-listing",
                        email: "<EMAIL>",
                        sentAt: expect.any(String),
                        status: "sent"
                    }]
                }
            });

            expect(responseData).toEqual({
                success: true,
                message: "Test dispatch email sent successfully",
                sentTo: "<EMAIL>"
            });
        });

        it("should send test emails to sample providers when no listingId is provided", async () => {
            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: mockValidatedData
            });

            const response = await POST(req);
            const responseData = await response.json();

            // Verify providers were found
            expect(mockPrisma.listing.findMany).toHaveBeenCalledWith({
                where: {
                    is_active: true,
                    status: "ACTIVE",
                    notification_email: {
                        not: null
                    },
                    settings_dispatch_emails_opt_out: false,
                    categories: {
                        path: ["rv-repair", "selected"],
                        equals: true
                    }
                },
                take: 5,
                include: {
                    locations: true
                }
            });

            // Verify emails were sent to each provider
            expect(emailService.send).toHaveBeenCalledTimes(2);
            expect(emailService.send).toHaveBeenCalledWith({
                to: "<EMAIL>",
                subject: "Test Subject",
                react: expect.any(Object),
                emailType: "dispatch_email_test"
            });
            expect(emailService.send).toHaveBeenCalledWith({
                to: "<EMAIL>",
                subject: "Test Subject",
                react: expect.any(Object),
                emailType: "dispatch_email_test"
            });

            // Verify dispatch email record was created with recipient tracking
            expect(mockPrisma.dispatchEmail.create).toHaveBeenCalledWith({
                data: {
                    title: "Test Email",
                    subject: "Test Subject",
                    body: "Test Body",
                    status: "SENT",
                    sent_at: expect.any(Date),
                    scheduled_for: expect.any(Date),
                    recipients: expect.any(Array)
                }
            });

            expect(responseData).toEqual({
                success: true,
                message: "Test dispatch email sent to 2 providers",
                sentTo: ["<EMAIL>", "<EMAIL>"],
                totalProviders: 2
            });
        });

        it("should return 404 if specific listing is not found", async () => {
            mockPrisma.listing.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: {
                    ...mockValidatedData,
                    listingId: "nonexistent"
                }
            });

            const response = await POST(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "Listing not found"
            });
            expect(response.status).toBe(404);
        });

        it("should return 400 if specific listing has no notification email", async () => {
            mockPrisma.listing.findUnique.mockResolvedValue({
                ...mockListing,
                notification_email: null
            });

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: {
                    ...mockValidatedData,
                    listingId: "specific-listing"
                }
            });

            const response = await POST(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "Listing has no notification email"
            });
            expect(response.status).toBe(400);
        });

        it("should return 404 if no active providers are found", async () => {
            mockPrisma.listing.findMany.mockResolvedValue([]);

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: mockValidatedData
            });

            const response = await POST(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "No active providers found"
            });
            expect(response.status).toBe(404);
        });

        it("should handle email sending failures gracefully", async () => {
            (emailService.send as jest.Mock)
                .mockResolvedValueOnce({ success: true }) // First email succeeds
                .mockRejectedValueOnce(new Error("Email failed")); // Second email fails

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: mockValidatedData
            });

            const response = await POST(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                success: true,
                message: "Test dispatch email sent to 1 providers",
                sentTo: ["<EMAIL>"],
                totalProviders: 2
            });
        });

        it("should handle database errors", async () => {
            mockPrisma.listing.findMany.mockRejectedValue(new Error("Database error"));

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: mockValidatedData
            });

            const response = await POST(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "Failed to send test dispatch email"
            });
            expect(response.status).toBe(500);
        });

        it("should return 400 if specific listing has opted out of dispatch emails", async () => {
            mockPrisma.listing.findUnique.mockResolvedValue({
                ...mockListing,
                settings_dispatch_emails_opt_out: true
            });

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: {
                    ...mockValidatedData,
                    listingId: "specific-listing"
                }
            });

            const response = await POST(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "This provider has opted out of dispatch emails"
            });
            expect(response.status).toBe(400);
        });

        it("should return 400 if specific listing doesn't have the selected category", async () => {
            mockPrisma.listing.findUnique.mockResolvedValue({
                ...mockListing,
                categories: {
                    "rv-inspection": {
                        selected: true,
                        isPrimary: true,
                        subcategories: {}
                    }
                }
            });

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: {
                    ...mockValidatedData,
                    listingId: "specific-listing"
                }
            });

            const response = await POST(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "This provider does not offer the selected service category"
            });
            expect(response.status).toBe(400);
        });

        it("should filter providers by category when category is provided", async () => {
            // Mock that only one provider has the rv-repair category
            mockPrisma.listing.findMany.mockResolvedValue([mockProviders[0]]);

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: mockValidatedData
            });

            const response = await POST(req);
            const responseData = await response.json();

            // Verify the query includes category filtering
            expect(mockPrisma.listing.findMany).toHaveBeenCalledWith({
                where: {
                    is_active: true,
                    status: "ACTIVE",
                    notification_email: {
                        not: null
                    },
                    settings_dispatch_emails_opt_out: false,
                    categories: {
                        path: ["rv-repair", "selected"],
                        equals: true
                    }
                },
                take: 5,
                include: {
                    locations: true
                }
            });

            expect(responseData).toEqual({
                success: true,
                message: "Test dispatch email sent to 1 providers",
                sentTo: ["<EMAIL>"],
                totalProviders: 1
            });
        });

        it("should handle 'all' category by not filtering providers", async () => {
            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails/test",
                validatedData: {
                    ...mockValidatedData,
                    category: "all"
                }
            });

            const response = await POST(req);
            const responseData = await response.json();

            // Verify the query does NOT include category filtering when category is "all"
            expect(mockPrisma.listing.findMany).toHaveBeenCalledWith({
                where: {
                    is_active: true,
                    status: "ACTIVE",
                    notification_email: {
                        not: null
                    },
                    settings_dispatch_emails_opt_out: false
                },
                take: 5,
                include: {
                    locations: true
                }
            });

            expect(responseData).toEqual({
                success: true,
                message: "Test dispatch email sent to 2 providers",
                sentTo: ["<EMAIL>", "<EMAIL>"],
                totalProviders: 2
            });
        });
    });
}); 