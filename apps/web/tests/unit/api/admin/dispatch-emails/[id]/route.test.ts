import { mockPrisma } from "@/tests/mocks/prisma-mock";
import { createMockRequest, mockBaseHandler, mockUser } from "@/tests/utils/api-test-utils";

// Import after mocks are set up
import { GET, PUT } from "@/app/api/admin/dispatch-emails/[id]/route";

describe("Individual Dispatch Email API Handler", () => {
    const mockDate = new Date("2025-06-11T13:33:26.356Z");
    const mockDispatchEmail = {
        id: "email123",
        title: "Test Email",
        subject: "Test Subject",
        body: "Test Body",
        status: "DRAFT",
        scheduled_for: null,
        sent_at: null,
        created_at: mockDate,
        updated_at: mockDate
    };

    // Helper function to convert dates to strings for comparison
    const serializeDates = (obj: any) => {
        return JSON.parse(JSON.stringify(obj));
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockPrisma.dispatchEmail.findUnique.mockResolvedValue(mockDispatchEmail);
        mockPrisma.dispatchEmail.update.mockResolvedValue(mockDispatchEmail);

        // Set up admin user for all tests
        mockBaseHandler.user = { ...mockUser, role: "ADMIN" };
        mockBaseHandler.isAdmin = true;
    });

    describe("GET /api/admin/dispatch-emails/[id]", () => {
        it("should return a dispatch email by id", async () => {
            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/dispatch-emails/email123",
                params: { id: "email123" }
            });

            const response = await GET(req);
            const responseData = await response.json();

            expect(mockPrisma.dispatchEmail.findUnique).toHaveBeenCalledWith({
                where: { id: "email123" }
            });

            expect(responseData).toEqual(serializeDates(mockDispatchEmail));
        });

        it("should return 404 if email not found", async () => {
            mockPrisma.dispatchEmail.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/dispatch-emails/nonexistent",
                params: { id: "nonexistent" }
            });

            const response = await GET(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "Email not found"
            });
            expect(response.status).toBe(404);
        });
    });

    describe("PUT /api/admin/dispatch-emails/[id]", () => {
        const mockValidatedData = {
            title: "Updated Email",
            subject: "Updated Subject",
            body: "Updated Body"
        };

        it("should update a draft email", async () => {
            const req = createMockRequest({
                method: "PUT",
                url: "/api/admin/dispatch-emails/email123",
                params: { id: "email123" },
                validatedData: mockValidatedData
            });

            const response = await PUT(req);
            const responseData = await response.json();

            expect(mockPrisma.dispatchEmail.update).toHaveBeenCalledWith({
                where: { id: "email123" },
                data: mockValidatedData
            });

            expect(responseData).toEqual(serializeDates(mockDispatchEmail));
        });

        it("should not allow updating sent emails", async () => {
            mockPrisma.dispatchEmail.findUnique.mockResolvedValue({
                ...mockDispatchEmail,
                status: "SENT"
            });

            const req = createMockRequest({
                method: "PUT",
                url: "/api/admin/dispatch-emails/email123",
                params: { id: "email123" },
                validatedData: mockValidatedData
            });

            const response = await PUT(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "Only draft emails can be edited"
            });
            expect(response.status).toBe(400);
        });

        it("should return 404 if email not found", async () => {
            mockPrisma.dispatchEmail.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "PUT",
                url: "/api/admin/dispatch-emails/nonexistent",
                params: { id: "nonexistent" },
                validatedData: mockValidatedData
            });

            const response = await PUT(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "Email not found"
            });
            expect(response.status).toBe(404);
        });
    });
}); 