import { mockPrisma } from "@/tests/mocks/prisma-mock";
import { createMockRequest } from "@/tests/utils/api-test-utils";

jest.mock("@/lib/services", () => ({
    emailService: {
        send: jest.fn()
    }
}));

// Import after mocks are set up
import { GET, POST } from "@/app/api/admin/dispatch-emails/route";

describe("Dispatch Emails API Handler", () => {
    const mockDate = new Date("2025-06-11T13:33:26.356Z");
    const mockDispatchEmail = {
        id: "email123",
        title: "Test Email",
        subject: "Test Subject",
        body: "Test Body",
        status: "DRAFT",
        scheduled_for: null,
        sent_at: null,
        created_at: mockDate,
        updated_at: mockDate
    };

    // Helper function to convert dates to strings for comparison
    const serializeDates = (obj: any) => {
        return JSON.parse(JSON.stringify(obj));
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockPrisma.dispatchEmail.findMany.mockResolvedValue([mockDispatchEmail]);
        mockPrisma.dispatchEmail.create.mockResolvedValue(mockDispatchEmail);
    });

    describe("GET /api/admin/dispatch-emails", () => {
        it("should return all dispatch emails", async () => {
            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/dispatch-emails"
            });

            const response = await GET(req);
            const responseData = await response.json();

            expect(mockPrisma.dispatchEmail.findMany).toHaveBeenCalledWith({
                orderBy: {
                    created_at: "desc"
                }
            });

            expect(responseData).toEqual(serializeDates([mockDispatchEmail]));
        });

        it("should handle database errors", async () => {
            mockPrisma.dispatchEmail.findMany.mockRejectedValue(new Error("Database error"));

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/dispatch-emails"
            });

            const response = await GET(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "Failed to fetch dispatch emails"
            });
            expect(response.status).toBe(500);
        });
    });

    describe("POST /api/admin/dispatch-emails", () => {
        const mockValidatedData = {
            title: "New Email",
            subject: "New Subject",
            body: "New Body",
            category: "rv-repair"
        };

        it("should create a new dispatch email as draft", async () => {
            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails",
                validatedData: mockValidatedData
            });

            const response = await POST(req);
            const responseData = await response.json();

            expect(mockPrisma.dispatchEmail.create).toHaveBeenCalledWith({
                data: {
                    title: mockValidatedData.title,
                    subject: mockValidatedData.title, // subject is now set to title
                    body: mockValidatedData.body,
                    status: "DRAFT",
                    scheduled_for: null,
                    category: mockValidatedData.category,
                    campaign_url: null // location targeting info
                }
            });

            expect(responseData).toEqual(serializeDates(mockDispatchEmail));
        });

        it("should store category as provided (removed 'all' special case)", async () => {
            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails",
                validatedData: {
                    ...mockValidatedData,
                    category: "rv-inspection"
                }
            });

            const response = await POST(req);
            const responseData = await response.json();

            expect(mockPrisma.dispatchEmail.create).toHaveBeenCalledWith({
                data: {
                    title: mockValidatedData.title,
                    subject: mockValidatedData.title, // subject is now set to title
                    body: mockValidatedData.body,
                    status: "DRAFT",
                    scheduled_for: null,
                    category: "rv-inspection",
                    campaign_url: null // location targeting info
                }
            });

            expect(responseData).toEqual(serializeDates(mockDispatchEmail));
        });

        it("should handle database errors", async () => {
            mockPrisma.dispatchEmail.create.mockRejectedValue(new Error("Database error"));

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/dispatch-emails",
                validatedData: mockValidatedData
            });

            const response = await POST(req);
            const responseData = await response.json();

            expect(responseData).toEqual({
                error: "Failed to create dispatch email"
            });
            expect(response.status).toBe(500);
        });
    });
}); 