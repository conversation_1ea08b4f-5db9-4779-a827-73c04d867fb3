import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "@/tests/utils/api-test-utils";



// Mock EmailNewsletterService
jest.mock("@/lib/services/emailNewsletter.service", () => ({
    EmailNewsletterService: {
        syncNewsletterSubscriber: jest.fn()
    }
}));

// Import handler after setting up mocks
import { POST } from "@/app/api/admin/reviews/[id]/approve/route";
import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";

describe("Review Approve Handler", () => {
    const mockAdminUser = {
        ...mockUser,
        role: "ADMIN"
    };

    const mockReview = {
        id: "review123",
        title: "Test Review",
        content: "Great service!",
        overall: 5,
        status: "in_moderation",
        user_id: "user123",
        listing_id: "listing123",
        email: "<EMAIL>",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        created_at: "2025-03-22T16:00:50.585Z",
        updated_at: "2025-03-22T16:00:50.585Z",
        user: {
            id: "user123",
            email: "<EMAIL>",
            first_name: "John",
            last_name: "Doe"
        }
    };

    const approvedReview = {
        ...mockReview,
        status: "active"
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockAdminUser;
        mockBaseHandler.isAdmin = true;
        // Reset mock implementations
        mockPrisma.review.update.mockResolvedValue(approvedReview);
        (EmailNewsletterService.syncNewsletterSubscriber as jest.Mock).mockResolvedValue(true);
    });

    it("should approve review successfully", async () => {
        const req = createMockRequest({
            method: "POST",
            url: `/api/admin/reviews/${mockReview.id}/approve`,
            params: { id: mockReview.id }
        });

        const handler = POST.bind({
            ...mockBaseHandler,
            user: mockAdminUser,
            params: { id: mockReview.id }
        });

        await handler(req, {
            params: { id: mockReview.id }
        });

        expect(mockPrisma.review.update).toHaveBeenCalledWith({
            where: { id: mockReview.id },
            data: {
                status: "active"
            },
            include: {
                user: true
            }
        });

        expect(mockBaseHandler.respond).toHaveBeenCalledWith(approvedReview);
    });

    it("should sync newsletter when review is approved", async () => {
        const req = createMockRequest({
            method: "POST",
            url: `/api/admin/reviews/${mockReview.id}/approve`,
            params: { id: mockReview.id }
        });

        const handler = POST.bind({
            ...mockBaseHandler,
            user: mockAdminUser,
            params: { id: mockReview.id }
        });

        await handler(req, {
            params: { id: mockReview.id }
        });

        // Verify newsletter sync was called with correct tags
        expect(EmailNewsletterService.syncNewsletterSubscriber).toHaveBeenCalledWith({
            email: mockReview.email,
            first_name: mockReview.first_name,
            last_name: mockReview.last_name,
            user: mockReview.user,
            tags: [
                "consumer action: reviewed provider",
                "consumer action: five star review"
            ]
        });
    });

    it("should sync newsletter without five star tag for non-5-star reviews", async () => {
        const fourStarReview = {
            ...mockReview,
            overall: 4
        };

        const approvedFourStarReview = {
            ...fourStarReview,
            status: "active"
        };

        mockPrisma.review.update.mockResolvedValue(approvedFourStarReview);

        const req = createMockRequest({
            method: "POST",
            url: `/api/admin/reviews/${mockReview.id}/approve`,
            params: { id: mockReview.id }
        });

        const handler = POST.bind({
            ...mockBaseHandler,
            user: mockAdminUser,
            params: { id: mockReview.id }
        });

        await handler(req, {
            params: { id: mockReview.id }
        });

        // Verify newsletter sync was called with only the basic tag
        expect(EmailNewsletterService.syncNewsletterSubscriber).toHaveBeenCalledWith({
            email: mockReview.email,
            first_name: mockReview.first_name,
            last_name: mockReview.last_name,
            user: mockReview.user,
            tags: [
                "consumer action: reviewed provider"
            ]
        });
    });

    it("should handle newsletter sync errors gracefully", async () => {
        // Mock newsletter sync to fail
        (EmailNewsletterService.syncNewsletterSubscriber as jest.Mock).mockRejectedValue(
            new Error("Newsletter sync failed")
        );

        const req = createMockRequest({
            method: "POST",
            url: `/api/admin/reviews/${mockReview.id}/approve`,
            params: { id: mockReview.id }
        });

        const handler = POST.bind({
            ...mockBaseHandler,
            user: mockAdminUser,
            params: { id: mockReview.id }
        });

        // Should not throw error even if newsletter sync fails
        await expect(handler(req, {
            params: { id: mockReview.id }
        })).resolves.not.toThrow();

        // Verify the review was still updated successfully
        expect(mockPrisma.review.update).toHaveBeenCalled();
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(approvedReview);
    });

    it("should not sync newsletter if review lacks required fields", async () => {
        const incompleteReview = {
            ...mockReview,
            email: null,
            first_name: null,
            last_name: null
        };

        const approvedIncompleteReview = {
            ...incompleteReview,
            status: "active"
        };

        mockPrisma.review.update.mockResolvedValue(approvedIncompleteReview);

        const req = createMockRequest({
            method: "POST",
            url: `/api/admin/reviews/${mockReview.id}/approve`,
            params: { id: mockReview.id }
        });

        const handler = POST.bind({
            ...mockBaseHandler,
            user: mockAdminUser,
            params: { id: mockReview.id }
        });

        await handler(req, {
            params: { id: mockReview.id }
        });

        // Verify newsletter sync was NOT called due to missing fields
        expect(EmailNewsletterService.syncNewsletterSubscriber).not.toHaveBeenCalled();

        // Verify the review was still updated successfully
        expect(mockPrisma.review.update).toHaveBeenCalled();
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(approvedIncompleteReview);
    });
}); 