import { afterEach, beforeEach, describe, expect, it } from '@jest/globals';
import { JobStatus } from '@rvhelp/database';
import { POST } from '../../../../../app/api/admin/jobs/merge/route';
import prisma from '../../../../../lib/prisma';

// Mock the baseHandler
jest.mock('../../../../../lib/api/baseHandler', () => ({
    createHandler: (handler: any, options: any) => {
        return handler;
    }
}));

// Mock prisma
jest.mock('../../../../../lib/prisma', () => ({
    $transaction: jest.fn(),
    job: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
        deleteMany: jest.fn(),
    },
    quote: {
        findMany: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
    },
    quoteMessage: {
        deleteMany: jest.fn(),
    },
    timelineUpdate: {
        updateMany: jest.fn(),
    },
}));

describe('POST /api/admin/jobs/merge', () => {
    const mockRequest = {
        json: jest.fn(),
    };

    const mockContext = {
        validatedData: {
            primaryJobId: 'primary-job-1',
            jobsToMerge: ['job-2', 'job-3']
        },
        respond: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should merge jobs successfully', async () => {
        const mockPrimaryJob = {
            id: 'primary-job-1',
            status: JobStatus.OPEN,
            is_premium: false,
            flagged_for_fraud: false,
            warranty_request_id: null,
            accepted_quote_id: null,
            transaction_id: null,
            viewed_at: null,
            sent_at: null,
            quotes: [],
            timeline_updates: [],
        };

        const mockMergingJobs = [
            {
                id: 'job-2',
                status: JobStatus.IN_PROGRESS,
                is_premium: true,
                flagged_for_fraud: false,
                warranty_request_id: 'warranty-1',
                accepted_quote_id: 'quote-1',
                transaction_id: 'transaction-1',
                viewed_at: new Date('2024-01-01'),
                sent_at: new Date('2024-01-01'),
                quotes: [],
                timeline_updates: [],
            },
            {
                id: 'job-3',
                status: JobStatus.COMPLETED,
                is_premium: false,
                flagged_for_fraud: true,
                warranty_request_id: null,
                accepted_quote_id: null,
                transaction_id: null,
                viewed_at: null,
                sent_at: null,
                quotes: [],
                timeline_updates: [],
            }
        ];

        (prisma.$transaction as jest.Mock).mockImplementation(async (callback) => {
            return await callback({
                job: {
                    findMany: jest.fn().mockResolvedValue(mockMergingJobs),
                    findUnique: jest.fn().mockResolvedValue(mockPrimaryJob),
                    update: jest.fn(),
                    deleteMany: jest.fn(),
                },
                quote: {
                    findMany: jest.fn().mockResolvedValue([]),
                    update: jest.fn(),
                    delete: jest.fn(),
                },
                quoteMessage: {
                    deleteMany: jest.fn(),
                },
                timelineUpdate: {
                    updateMany: jest.fn(),
                },
            });
        });

        await POST.call(mockContext, mockRequest as any);

        expect(prisma.$transaction).toHaveBeenCalled();
        expect(mockContext.respond).toHaveBeenCalledWith({ success: true });
    });

    it('should handle primary job not found', async () => {
        (prisma.$transaction as jest.Mock).mockImplementation(async (callback) => {
            return await callback({
                job: {
                    findMany: jest.fn().mockResolvedValue([]),
                    findUnique: jest.fn().mockResolvedValue(null),
                    update: jest.fn(),
                    deleteMany: jest.fn(),
                },
                quote: {
                    findMany: jest.fn().mockResolvedValue([]),
                    update: jest.fn(),
                    delete: jest.fn(),
                },
                quoteMessage: {
                    deleteMany: jest.fn(),
                },
                timelineUpdate: {
                    updateMany: jest.fn(),
                },
            });
        });

        await POST.call(mockContext, mockRequest as any);

        expect(mockContext.respond).toHaveBeenCalledWith(
            { error: "Failed to merge jobs" },
            500
        );
    });
}); 