import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler
} from "@/tests/utils/api-test-utils";

// Set up mocks before importing the handlers
jest.mock("@/lib/prisma", () => ({
    prisma: mockPrisma,
    __esModule: true,
    default: mockPrisma
}));

import { GET } from "@/app/api/admin/leads/analytics/route";

describe("Admin Leads Analytics API", () => {
    const mockJobsData = [
        {
            city: "Tucson",
            state: "AZ",
            category: "rv-repair",
            job_status: "pending"
        },
        {
            city: "Ocala",
            state: "FL",
            category: "rv-inspection",
            job_status: "active"
        },
        {
            city: "Tucson",
            state: "AZ",
            category: "rv-repair",
            job_status: "pending"
        },
        {
            city: "Phoenix",
            state: "AZ",
            category: "rv-repair",
            job_status: "completed"
        },
        {
            city: "Ocala",
            state: "FL",
            category: "rv-inspection",
            job_status: "pending"
        }
    ];

    const mockQuotesStats = [
        {
            job_id: "job1",
            category: "rv-repair",
            total_quotes: "3",
            responded_quotes: "2",
            accepted_quotes: "1",
            completed_quotes: "0"
        },
        {
            job_id: "job2",
            category: "rv-inspection",
            total_quotes: "2",
            responded_quotes: "1",
            accepted_quotes: "1",
            completed_quotes: "1"
        }
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        // Set up admin user authentication
        mockBaseHandler.user = {
            id: "admin123",
            email: "<EMAIL>",
            role: "ADMIN",
            first_name: "Admin",
            last_name: "User"
        };
        mockBaseHandler.isAdmin = true;
        mockBaseHandler.isAuthenticated = true;
    });

    describe("GET /api/admin/leads/analytics", () => {
        it("should return analytics data for all time by default", async () => {
            mockPrisma.job.count.mockResolvedValue(5);
            mockPrisma.$queryRawUnsafe
                .mockResolvedValueOnce(mockJobsData) // First call for jobs with location
                .mockResolvedValueOnce(mockQuotesStats); // Second call for quotes stats

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/leads/analytics",
                validatedData: { timeframe: "all" }
            });

            const response = await GET(req);
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toHaveProperty("totalJobs", 5);
            expect(data).toHaveProperty("totalQuotes");
            expect(data).toHaveProperty("responseRate");
            expect(data).toHaveProperty("acceptanceRate");
            expect(data).toHaveProperty("completionRate");
            expect(data).toHaveProperty("topCities");
            expect(data).toHaveProperty("topStates");
            expect(data).toHaveProperty("categoriesBreakdown");
            expect(data).toHaveProperty("statusBreakdown");

            // Verify top cities includes both city and state
            expect(data.topCities[0]).toHaveProperty("location");
            expect(data.topCities[0].location).toContain(","); // Should be "City, State" format

            // Verify categories are properly formatted
            expect(data.categoriesBreakdown).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        category: expect.any(String),
                        count: expect.any(Number),
                        percentage: expect.any(Number)
                    })
                ])
            );
        });

        it("should filter data for 30 days timeframe", async () => {
            mockPrisma.job.count.mockResolvedValue(3);
            mockPrisma.$queryRawUnsafe
                .mockResolvedValueOnce(mockJobsData.slice(0, 3))
                .mockResolvedValueOnce(mockQuotesStats.slice(0, 1));

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/leads/analytics?timeframe=30d",
                validatedData: { timeframe: "30d" }
            });

            const response = await GET(req);
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.totalJobs).toBe(3);

            // Verify the SQL query includes date filtering
            expect(mockPrisma.$queryRawUnsafe).toHaveBeenCalledWith(
                expect.stringContaining("AND created_at >= NOW() - INTERVAL '30 days'")
            );
        });

        it("should filter data for 3 months timeframe", async () => {
            mockPrisma.job.count.mockResolvedValue(4);
            mockPrisma.$queryRawUnsafe
                .mockResolvedValueOnce(mockJobsData.slice(0, 4))
                .mockResolvedValueOnce(mockQuotesStats);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/leads/analytics?timeframe=3m",
                validatedData: { timeframe: "3m" }
            });

            const response = await GET(req);
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.totalJobs).toBe(4);

            // Verify the SQL query includes date filtering
            expect(mockPrisma.$queryRawUnsafe).toHaveBeenCalledWith(
                expect.stringContaining("AND created_at >= NOW() - INTERVAL '3 months'")
            );
        });

        it("should properly aggregate city data with state information", async () => {
            mockPrisma.job.count.mockResolvedValue(5);
            mockPrisma.$queryRawUnsafe
                .mockResolvedValueOnce(mockJobsData)
                .mockResolvedValueOnce(mockQuotesStats);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/leads/analytics",
                validatedData: { timeframe: "all" }
            });

            const response = await GET(req);
            const data = await response.json();

            // Verify cities are aggregated correctly
            const tucsonCity = data.topCities.find((city: any) =>
                city.location.includes("Tucson")
            );
            expect(tucsonCity).toBeDefined();
            expect(tucsonCity.count).toBe(2); // Tucson appears twice in mock data
            expect(tucsonCity.location).toBe("Tucson, AZ");
        });

        it("should properly aggregate state data", async () => {
            mockPrisma.job.count.mockResolvedValue(5);
            mockPrisma.$queryRawUnsafe
                .mockResolvedValueOnce(mockJobsData)
                .mockResolvedValueOnce(mockQuotesStats);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/leads/analytics",
                validatedData: { timeframe: "all" }
            });

            const response = await GET(req);
            const data = await response.json();

            // Verify states are aggregated correctly
            const azState = data.topStates.find((state: any) =>
                state.location === "AZ"
            );
            expect(azState).toBeDefined();
            expect(azState.count).toBe(3); // AZ appears 3 times in mock data
        });

        it("should clean up category names properly", async () => {
            mockPrisma.job.count.mockResolvedValue(5);
            mockPrisma.$queryRawUnsafe
                .mockResolvedValueOnce(mockJobsData)
                .mockResolvedValueOnce(mockQuotesStats);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/leads/analytics",
                validatedData: { timeframe: "all" }
            });

            const response = await GET(req);
            const data = await response.json();

            // Verify category names are cleaned up
            const categories = data.categoriesBreakdown.map((cat: any) => cat.category);
            expect(categories).toContain("Repair"); // rv-repair should become "Repair"
            expect(categories).toContain("Inspection"); // rv-inspection should become "Inspection"
        });

        it("should calculate percentages correctly", async () => {
            mockPrisma.job.count.mockResolvedValue(5);
            mockPrisma.$queryRawUnsafe
                .mockResolvedValueOnce(mockJobsData)
                .mockResolvedValueOnce(mockQuotesStats);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/leads/analytics",
                validatedData: { timeframe: "all" }
            });

            const response = await GET(req);
            const data = await response.json();

            // Verify percentages add up correctly for states
            const totalPercentage = data.topStates.reduce(
                (sum: number, state: any) => sum + state.percentage,
                0
            );
            expect(totalPercentage).toBeCloseTo(100, 1); // Should be close to 100%
        });

        it("should require admin authentication", async () => {
            // Set up non-admin user
            mockBaseHandler.user = {
                id: "user123",
                email: "<EMAIL>",
                role: "USER",
                first_name: "Regular",
                last_name: "User"
            };
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/leads/analytics",
                validatedData: { timeframe: "all" }
            });

            const response = await GET(req);

            expect(response.status).toBe(401);
        });

        it("should handle empty data gracefully", async () => {
            mockPrisma.job.count.mockResolvedValue(0);
            mockPrisma.$queryRawUnsafe
                .mockResolvedValueOnce([])
                .mockResolvedValueOnce([]);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/leads/analytics",
                validatedData: { timeframe: "all" }
            });

            const response = await GET(req);
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.totalJobs).toBe(0);
            expect(data.totalQuotes).toBe(0);
            expect(data.responseRate).toBe(0);
            expect(data.acceptanceRate).toBe(0);
            expect(data.completionRate).toBe(0);
            expect(data.topCities).toEqual([]);
            expect(data.topStates).toEqual([]);
            expect(data.categoriesBreakdown).toEqual([]);
            expect(data.statusBreakdown).toEqual([]);
        });
    });
}); 