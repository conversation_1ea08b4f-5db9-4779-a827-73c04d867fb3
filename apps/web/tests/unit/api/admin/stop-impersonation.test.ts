import { NextRequest } from "next/server";

// Mock the entire module
jest.mock("@/lib/prisma", () => ({
    user: {
        findUnique: jest.fn(),
    },
    verificationToken: {
        deleteMany: jest.fn(),
        create: jest.fn(),
        findFirst: jest.fn(),
        delete: jest.fn(),
    },
}));

jest.mock("@/lib/api/baseHandler", () => ({
    createHandler: jest.fn((handler) => {
        return async (req: NextRequest) => {
            // Create a mock context that can be customized
            const mockContext = {
                user: {
                    id: "user123",
                    email: "<EMAIL>",
                    isImpersonating: false,
                    originalAdminId: null,
                },
                respond: (data: any, status = 200) => {
                    return new Response(JSON.stringify(data), { status });
                },
            };

            // Allow the test to modify the context by setting a global variable
            if ((global as any).mockUserContext) {
                Object.assign(mockContext.user, (global as any).mockUserContext);
            }

            return handler.call(mockContext, req);
        };
    }),
}));

// Import the mocked modules
const mockPrisma = require("@/lib/prisma");
const { POST } = require("@/app/api/admin/stop-impersonation/route");

describe("/api/admin/stop-impersonation", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (global as any).mockUserContext = null;
    });

    it("should return error if user is not impersonating", async () => {
        const req = new NextRequest("http://localhost:3000/api/admin/stop-impersonation", {
            method: "POST",
        });

        const response = await POST(req);
        const data = await response.json();

        expect(data.error).toBe("Not currently impersonating");
        expect(response.status).toBe(400);
    });

    it("should return error if original admin user not found", async () => {
        const req = new NextRequest("http://localhost:3000/api/admin/stop-impersonation", {
            method: "POST",
        });

        // Set up mock context for impersonating user
        (global as any).mockUserContext = {
            isImpersonating: true,
            originalAdminId: "admin123",
        };

        // Mock prisma to return null for admin user
        mockPrisma.user.findUnique.mockResolvedValue(null);

        const response = await POST(req);
        const data = await response.json();

        expect(data.error).toBe("Original admin user not found");
        expect(response.status).toBe(404);
    });

    it("should successfully stop impersonation and return admin user info", async () => {
        const req = new NextRequest("http://localhost:3000/api/admin/stop-impersonation", {
            method: "POST",
        });

        // Set up mock context for impersonating user
        (global as any).mockUserContext = {
            isImpersonating: true,
            originalAdminId: "admin123",
        };

        // Mock admin user
        const mockAdminUser = {
            id: "admin123",
            email: "<EMAIL>",
            role: "ADMIN",
        };

        // Mock return token
        const mockReturnToken = {
            id: "token123",
            token: "admin_return_abc123",
            expires: new Date(Date.now() + 1000 * 60 * 5),
            user_id: "admin123",
            type: "admin_return",
        };

        mockPrisma.user.findUnique.mockResolvedValue(mockAdminUser);
        mockPrisma.verificationToken.deleteMany.mockResolvedValue({ count: 1 });
        mockPrisma.verificationToken.create.mockResolvedValue(mockReturnToken);

        const response = await POST(req);
        const data = await response.json();

        expect(data.success).toBe(true);
        expect(data.adminUser).toEqual({
            id: "admin123",
            email: "<EMAIL>",
            role: "ADMIN",
        });
        expect(data.returnToken).toBe("admin_return_abc123");
        expect(response.status).toBe(200);

        // Verify that impersonation tokens were deleted
        expect(mockPrisma.verificationToken.deleteMany).toHaveBeenCalledWith({
            where: {
                admin_id: "admin123",
                type: "impersonation",
            },
        });

        // Verify that return token was created
        expect(mockPrisma.verificationToken.create).toHaveBeenCalledWith({
            data: {
                token: expect.stringMatching(/^admin_return_/),
                expires: expect.any(Date),
                user_id: "admin123",
                type: "admin_return",
            },
        });
    });


}); 