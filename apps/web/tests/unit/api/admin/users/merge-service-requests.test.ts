import { POST } from "../../../../../app/api/admin/users/merge-service-requests/route";
import prisma from "../../../../../lib/prisma";
import { createMockRequest, mockBaseHandler } from "../../../../utils/api-test-utils";

// Mock prisma
jest.mock('../../../../../lib/prisma', () => ({
    $transaction: jest.fn(),
    job: {
        findMany: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
    },
    quote: {
        findFirst: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
    },
    quoteMessage: {
        deleteMany: jest.fn(),
    },
    timelineUpdate: {
        updateMany: jest.fn(),
    },
}));

describe('/api/admin/users/merge-service-requests', () => {
    const mockRequest = createMockRequest({
        method: 'POST',
        body: { userId: 'user123' },
        validatedData: { userId: 'user123' }
    });

    beforeEach(() => {
        jest.clearAllMocks();
        // Set up admin user for authentication
        mockBaseHandler.user = { id: 'admin123', role: 'ADMIN' };
        mockBaseHandler.isAdmin = true;
    });

    it('should successfully merge service requests', async () => {
        const mockUserJobs = [
            {
                id: 'job1',
                user_id: 'user123',
                created_at: new Date('2024-01-01T10:00:00Z'),
                category: 'RV_REPAIR',
                rv_make: 'Winnebago',
                rv_model: 'Travato',
                status: 'OPEN',
                quotes: [],
                timeline_updates: [],
                warranty_request: null,
                accepted_quote: null,
                warranty_request_id: null,
                accepted_quote_id: null,
                transaction_id: null,
                is_premium: false,
                flagged_for_fraud: false,
                viewed_at: null,
                sent_at: null,
                offer_reminder_sent_at: null,
                offer_reminder_48h_sent_at: null,
                follow_up_sent_at: null,
                reminder_48h_sent_at: null,
                reminder_1w_sent_at: null,
                expired_at: null
            },
            {
                id: 'job2',
                user_id: 'user123',
                created_at: new Date('2024-01-01T11:00:00Z'),
                category: 'RV_REPAIR',
                rv_make: 'Winnebago',
                rv_model: 'Travato',
                status: 'IN_PROGRESS',
                quotes: [],
                timeline_updates: [],
                warranty_request: null,
                accepted_quote: null,
                warranty_request_id: null,
                accepted_quote_id: null,
                transaction_id: null,
                is_premium: false,
                flagged_for_fraud: false,
                viewed_at: null,
                sent_at: null,
                offer_reminder_sent_at: null,
                offer_reminder_48h_sent_at: null,
                follow_up_sent_at: null,
                reminder_48h_sent_at: null,
                reminder_1w_sent_at: null,
                expired_at: null
            }
        ];

        (prisma.job.findMany as jest.Mock).mockResolvedValue(mockUserJobs);

        (prisma.$transaction as jest.Mock).mockImplementation(async (callback) => {
            return await callback({
                job: {
                    update: jest.fn(),
                    delete: jest.fn(),
                },
                quote: {
                    findFirst: jest.fn().mockResolvedValue(null),
                    update: jest.fn(),
                    delete: jest.fn(),
                },
                quoteMessage: {
                    deleteMany: jest.fn(),
                },
                timelineUpdate: {
                    updateMany: jest.fn(),
                },
            });
        });

        const response = await POST(mockRequest);

        expect(response.status).toBe(200);
        const responseData = await response.json();
        expect(responseData).toEqual({
            success: true,
            message: 'Successfully merged 1 service requests',
            totalMerged: 1,
            totalGroups: 1
        });
    });

    it('should handle no service requests found', async () => {
        (prisma.job.findMany as jest.Mock).mockResolvedValue([]);

        const response = await POST(mockRequest);

        expect(response.status).toBe(404);
        const responseData = await response.json();
        expect(responseData).toEqual({
            error: 'No service requests found for this user'
        });
    });

    it('should handle single service request (no merging needed)', async () => {
        const mockUserJobs = [
            {
                id: 'job1',
                user_id: 'user123',
                created_at: new Date('2024-01-01T10:00:00Z'),
                category: 'RV_REPAIR',
                rv_make: 'Winnebago',
                rv_model: 'Travato',
                status: 'OPEN',
                quotes: [],
                timeline_updates: [],
                warranty_request: null,
                accepted_quote: null,
                warranty_request_id: null,
                accepted_quote_id: null,
                transaction_id: null,
                is_premium: false,
                flagged_for_fraud: false,
                viewed_at: null,
                sent_at: null,
                offer_reminder_sent_at: null,
                offer_reminder_48h_sent_at: null,
                follow_up_sent_at: null,
                reminder_48h_sent_at: null,
                reminder_1w_sent_at: null,
                expired_at: null
            }
        ];

        (prisma.job.findMany as jest.Mock).mockResolvedValue(mockUserJobs);

        const response = await POST(mockRequest);

        expect(response.status).toBe(200);
        const responseData = await response.json();
        expect(responseData).toEqual({
            success: true,
            message: 'Successfully merged 0 service requests',
            totalMerged: 0,
            totalGroups: 1
        });
    });
}); 