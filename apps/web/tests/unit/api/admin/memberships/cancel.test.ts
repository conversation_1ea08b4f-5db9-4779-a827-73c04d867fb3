import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "@/tests/utils/api-test-utils";



jest.mock("@/lib/services/membership.service", () => ({
    membershipService: {
        cancelMembership: jest.fn(),
    },
}));

// Now import handlers after mocks are set up
import { POST } from "@/app/api/admin/memberships/[userId]/cancel/route";
import { membershipService } from "@/lib/services/membership.service";

describe("POST /api/admin/memberships/[userId]/cancel", () => {
    const mockCancelledUser = {
        id: "user456",
        email: "<EMAIL>",
        membership_level: "FREE",
    };

    const mockCancelledMembership = {
        id: "membership456",
        user_id: "user456",
        is_active: false,
        cancelled_at: new Date(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = { ...mockUser, role: "ADMIN" };
        mockBaseHandler.isAdmin = true;
    });

    it("should cancel membership successfully when user is admin", async () => {
        // Mock successful cancellation
        (membershipService.cancelMembership as jest.Mock).mockResolvedValue({
            user: mockCancelledUser,
            membership: mockCancelledMembership,
        });

        const req = createMockRequest({
            method: "POST",
            url: "/api/admin/memberships/user456/cancel",
            params: { userId: "user456" },
        });

        await POST(req);

        expect(membershipService.cancelMembership).toHaveBeenCalledWith("user456");
        expect(mockBaseHandler.respond).toHaveBeenCalledWith({
            success: true,
            message: "Membership cancelled <NAME_EMAIL>",
            user: {
                id: "user456",
                email: "<EMAIL>",
                membership_level: "FREE",
            },
        });
    });

    it("should return 401 if user is not admin", async () => {
        // Set user as non-admin
        mockBaseHandler.user = { ...mockUser, role: "USER" };
        mockBaseHandler.isAdmin = false;

        const req = createMockRequest({
            method: "POST",
            url: "/api/admin/memberships/user456/cancel",
            params: { userId: "user456" },
        });

        await POST(req);

        expect(membershipService.cancelMembership).not.toHaveBeenCalled();
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "Unauthorized" },
            401
        );
    });

    it("should return 401 if no user is found", async () => {
        // Set no user
        mockBaseHandler.user = null;
        mockBaseHandler.isAdmin = false;

        const req = createMockRequest({
            method: "POST",
            url: "/api/admin/memberships/user456/cancel",
            params: { userId: "user456" },
        });

        await POST(req);

        expect(membershipService.cancelMembership).not.toHaveBeenCalled();
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "Unauthorized" },
            401
        );
    });

    it("should return 400 if userId is missing", async () => {
        const req = createMockRequest({
            method: "POST",
            url: "/api/admin/memberships//cancel",
            params: { userId: undefined },
        });

        await POST(req);

        expect(membershipService.cancelMembership).not.toHaveBeenCalled();
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "User ID is required" },
            400
        );
    });

    it("should return 400 if membership service throws error", async () => {
        // Mock service error
        (membershipService.cancelMembership as jest.Mock).mockRejectedValue(
            new Error("User does not have a membership to cancel")
        );

        const req = createMockRequest({
            method: "POST",
            url: "/api/admin/memberships/user456/cancel",
            params: { userId: "user456" },
        });

        await POST(req);

        expect(membershipService.cancelMembership).toHaveBeenCalledWith("user456");
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "User does not have a membership to cancel" },
            400
        );
    });

    it("should return 500 for unexpected errors", async () => {
        // Mock unexpected error (non-Error object)
        (membershipService.cancelMembership as jest.Mock).mockRejectedValue("String error");

        const req = createMockRequest({
            method: "POST",
            url: "/api/admin/memberships/user456/cancel",
            params: { userId: "user456" },
        });

        await POST(req);

        expect(membershipService.cancelMembership).toHaveBeenCalledWith("user456");
        expect(mockBaseHandler.respond).toHaveBeenCalledWith(
            { error: "Failed to cancel membership" },
            500
        );
    });
}); 