import { prisma as mockPrisma } from "../../../../../tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "../../../../../tests/utils/api-test-utils";

// Import after mocks are set up
import { GET, POST } from "../../../../../app/api/admin/marketing-campaigns/route";

describe("Admin Marketing Campaigns API Routes", () => {
    const mockCampaign = {
        id: "campaign123",
        title: "Test Campaign",
        description: "Test campaign description",
        slug: "test-campaign",
        discount_type: "PERCENTAGE",
        discount_value: 25,
        coupon_code: "SUMMER2024",
        status: "DRAFT",
        expires_at: new Date("2024-12-31T23:59:59Z"),
        page_title: "Special Offer",
        page_subtitle: "Limited Time Deal",
        page_description: "Get 25% off RV Help Pro membership",
        button_text: "Get Discount",
        success_message: "Check your email for the discount code!",
        background_image: "https://example.com/background.jpg",
        logo: "https://example.com/logo.png",
        views_count: 0,
        leads_count: 0,
        conversions_count: 0,
        created_at: new Date("2024-01-01T00:00:00Z"),
        updated_at: new Date("2024-01-01T00:00:00Z"),
    };

    const mockAdminUser = {
        ...mockUser,
        role: "ADMIN"
    };

    const validCampaignData = {
        title: "New Campaign",
        description: "New campaign description",
        slug: "new-campaign",
        discount_type: "PERCENTAGE",
        discount_value: 30,
        coupon_code: "WINTER2024",
        expires_at: "2024-12-31T23:59:59Z",
        page_title: "New Offer",
        page_subtitle: "Limited Time",
        page_description: "Get 30% off",
        button_text: "Claim Discount",
        success_message: "Success!",
        background_image: "https://example.com/new-background.jpg",
        logo: "https://example.com/new-logo.png",
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockAdminUser;
        mockBaseHandler.isAdmin = true;
        mockBaseHandler.isAuthenticated = true;
    });

    describe("GET /api/admin/marketing-campaigns", () => {
        it("should return all campaigns for admin users", async () => {
            mockPrisma.marketingCampaign.findMany.mockResolvedValue([mockCampaign]);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/marketing-campaigns"
            });

            const response = await GET(req);
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ campaigns: [mockCampaign] });
            expect(mockPrisma.marketingCampaign.findMany).toHaveBeenCalledWith({
                orderBy: {
                    created_at: "desc",
                },
            });
        });

        it("should return empty array when no campaigns exist", async () => {
            mockPrisma.marketingCampaign.findMany.mockResolvedValue([]);

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/marketing-campaigns"
            });

            const response = await GET(req);
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ campaigns: [] });
        });

        it("should require admin authentication", async () => {
            mockBaseHandler.user = { ...mockUser, role: "USER" };
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "GET",
                url: "/api/admin/marketing-campaigns"
            });

            const response = await GET(req);

            expect(response.status).toBe(401);
        });
    });

    describe("POST /api/admin/marketing-campaigns", () => {
        it("should create a new campaign with valid data", async () => {
            const createdCampaign = {
                ...mockCampaign,
                ...validCampaignData,
                expires_at: new Date(validCampaignData.expires_at),
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);
            mockPrisma.marketingCampaign.create.mockResolvedValue(createdCampaign);

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/marketing-campaigns",
                validatedData: validCampaignData
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(201);
            expect(data).toEqual({ campaign: createdCampaign });
            expect(mockPrisma.marketingCampaign.findUnique).toHaveBeenCalledWith({
                where: { slug: validCampaignData.slug },
            });
            expect(mockPrisma.marketingCampaign.create).toHaveBeenCalledWith({
                data: {
                    ...validCampaignData,
                    expires_at: new Date(validCampaignData.expires_at),
                },
            });
        });

        it("should create campaign with FIXED_AMOUNT discount type", async () => {
            const fixedAmountData = {
                ...validCampaignData,
                discount_type: "FIXED_AMOUNT",
                discount_value: 50,
            };

            const createdCampaign = {
                ...mockCampaign,
                ...fixedAmountData,
                expires_at: new Date(fixedAmountData.expires_at),
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);
            mockPrisma.marketingCampaign.create.mockResolvedValue(createdCampaign);

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/marketing-campaigns",
                validatedData: fixedAmountData
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(201);
            expect(data.campaign.discount_type).toBe("FIXED_AMOUNT");
            expect(data.campaign.discount_value).toBe(50);
        });

        it("should create campaign without expiration date", async () => {
            const { expires_at, ...dataWithoutExpiration } = validCampaignData;

            const createdCampaign = {
                ...mockCampaign,
                ...dataWithoutExpiration,
                expires_at: null,
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);
            mockPrisma.marketingCampaign.create.mockResolvedValue(createdCampaign);

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/marketing-campaigns",
                validatedData: dataWithoutExpiration
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(201);
            expect(data.campaign.expires_at).toBe(null);
        });

        it("should reject duplicate slug", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/marketing-campaigns",
                validatedData: validCampaignData
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data).toEqual({ error: "Campaign with this slug already exists" });
        });



        it("should create campaign with custom coupon code", async () => {
            const campaignWithCoupon = {
                ...validCampaignData,
                coupon_code: "CUSTOM2024",
            };

            const createdCampaign = {
                ...mockCampaign,
                ...campaignWithCoupon,
                expires_at: new Date(campaignWithCoupon.expires_at),
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);
            mockPrisma.marketingCampaign.create.mockResolvedValue(createdCampaign);

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/marketing-campaigns",
                validatedData: campaignWithCoupon
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(201);
            expect(data.campaign.coupon_code).toBe("CUSTOM2024");
        });

        it("should create campaign without coupon code (auto-generate)", async () => {
            const { coupon_code, ...dataWithoutCoupon } = validCampaignData;

            const createdCampaign = {
                ...mockCampaign,
                ...dataWithoutCoupon,
                coupon_code: null,
                expires_at: new Date(dataWithoutCoupon.expires_at),
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);
            mockPrisma.marketingCampaign.create.mockResolvedValue(createdCampaign);

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/marketing-campaigns",
                validatedData: dataWithoutCoupon
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(201);
            expect(data.campaign.coupon_code).toBe(null);
        });

        it("should require admin authentication", async () => {
            mockBaseHandler.user = { ...mockUser, role: "USER" };
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "POST",
                url: "/api/admin/marketing-campaigns",
                validatedData: validCampaignData
            });

            const response = await POST(req);

            expect(response.status).toBe(401);
        });
    });
}); 