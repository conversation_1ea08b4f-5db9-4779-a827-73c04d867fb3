import { prisma as mockPrisma } from "../../../../../../tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "../../../../../../tests/utils/api-test-utils";

// Import after mocks are set up
import { DELETE, GET, PUT } from "../../../../../../app/api/admin/marketing-campaigns/[id]/route";

describe("Admin Marketing Campaigns ID API Routes", () => {
    const mockCampaignId = "campaign123";
    const mockCampaign = {
        id: mockCampaignId,
        title: "Test Campaign",
        description: "Test campaign description",
        slug: "test-campaign",
        discount_type: "PERCENTAGE",
        discount_value: 25,
        coupon_code: "SUMMER2024",
        status: "ACTIVE",
        expires_at: new Date("2024-12-31T23:59:59Z"),
        page_title: "Special Offer",
        page_subtitle: "Limited Time Deal",
        page_description: "Get 25% off RV Help Pro membership",
        button_text: "Get Discount",
        success_message: "Check your email for the discount code!",
        background_image: "https://example.com/background.jpg",
        logo: "https://example.com/logo.png",
        views_count: 100,
        leads_count: 50,
        conversions_count: 10,
        created_at: new Date("2024-01-01T00:00:00Z"),
        updated_at: new Date("2024-01-01T00:00:00Z"),
        leads: [
            {
                id: "lead1",
                email: "<EMAIL>",
                first_name: "John",
                last_name: "Doe",
                coupon_code: "TESTCAMP-ABC123",
                created_at: new Date("2024-01-02T00:00:00Z"),
            },
            {
                id: "lead2",
                email: "<EMAIL>",
                first_name: "Jane",
                last_name: "Smith",
                coupon_code: "TESTCAMP-DEF456",
                created_at: new Date("2024-01-03T00:00:00Z"),
            },
        ],
    };

    const mockAdminUser = {
        ...mockUser,
        role: "ADMIN"
    };

    const validUpdateData = {
        title: "Updated Campaign",
        description: "Updated campaign description",
        slug: "updated-campaign",
        discount_type: "FIXED_AMOUNT",
        discount_value: 50,
        coupon_code: "WINTER2024",
        expires_at: "2024-12-31T23:59:59Z",
        page_title: "Updated Offer",
        page_subtitle: "New Limited Time",
        page_description: "Get $50 off",
        button_text: "Claim Now",
        success_message: "Updated Success!",
        background_image: "https://example.com/updated-background.jpg",
        logo: "https://example.com/updated-logo.png",
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockAdminUser;
        mockBaseHandler.isAdmin = true;
        mockBaseHandler.isAuthenticated = true;
    });

    describe("GET /api/admin/marketing-campaigns/[id]", () => {
        it("should return campaign with leads for admin users", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);

            const req = createMockRequest({
                method: "GET",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId }
            });

            const response = await GET(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ campaign: mockCampaign });
            expect(mockPrisma.marketingCampaign.findUnique).toHaveBeenCalledWith({
                where: { id: mockCampaignId },
                include: {
                    leads: {
                        orderBy: {
                            created_at: "desc",
                        },
                    },
                },
            });
        });

        it("should return 404 if campaign not found", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "GET",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId }
            });

            const response = await GET(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data).toEqual({ error: "Campaign not found" });
        });

        it("should require admin authentication", async () => {
            mockBaseHandler.user = { ...mockUser, role: "USER" };
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "GET",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId }
            });

            const response = await GET(req, { params: { id: mockCampaignId } });

            expect(response.status).toBe(401);
        });
    });

    describe("PUT /api/admin/marketing-campaigns/[id]", () => {
        it("should update campaign with valid data", async () => {
            const updatedCampaign = {
                ...mockCampaign,
                ...validUpdateData,
                expires_at: new Date(validUpdateData.expires_at),
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaign.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaign.update.mockResolvedValue(updatedCampaign);

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId },
                validatedData: validUpdateData
            });

            const response = await PUT(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ campaign: updatedCampaign });
            expect(mockPrisma.marketingCampaign.update).toHaveBeenCalledWith({
                where: { id: mockCampaignId },
                data: {
                    ...validUpdateData,
                    expires_at: new Date(validUpdateData.expires_at),
                },
            });
        });

        it("should return 404 if campaign not found", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId },
                validatedData: validUpdateData
            });

            const response = await PUT(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data).toEqual({ error: "Campaign not found" });
        });

        it("should reject duplicate slug when updating", async () => {
            const duplicateSlugData = {
                ...validUpdateData,
                slug: "existing-campaign-slug",
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaign.findFirst.mockResolvedValue({
                id: "different-campaign-id",
                slug: "existing-campaign-slug",
            });

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId },
                validatedData: duplicateSlugData
            });

            const response = await PUT(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data).toEqual({ error: "Campaign with this slug already exists" });
        });

        it("should allow updating with the same slug", async () => {
            const sameSlugData = {
                ...validUpdateData,
                slug: mockCampaign.slug,
            };

            const updatedCampaign = {
                ...mockCampaign,
                ...sameSlugData,
                expires_at: new Date(sameSlugData.expires_at),
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaign.update.mockResolvedValue(updatedCampaign);

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId },
                validatedData: sameSlugData
            });

            const response = await PUT(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ campaign: updatedCampaign });
            expect(mockPrisma.marketingCampaign.findFirst).not.toHaveBeenCalled();
        });

        it("should handle campaigns without expiration date", async () => {
            const { expires_at, ...dataWithoutExpiration } = validUpdateData;

            const updatedCampaign = {
                ...mockCampaign,
                ...dataWithoutExpiration,
                expires_at: null,
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaign.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaign.update.mockResolvedValue(updatedCampaign);

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId },
                validatedData: dataWithoutExpiration
            });

            const response = await PUT(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.campaign.expires_at).toBe(null);
        });

        it("should update campaign with custom coupon code", async () => {
            const updateWithCoupon = {
                ...validUpdateData,
                coupon_code: "CUSTOM2024",
            };

            const updatedCampaign = {
                ...mockCampaign,
                ...updateWithCoupon,
                expires_at: new Date(updateWithCoupon.expires_at),
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaign.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaign.update.mockResolvedValue(updatedCampaign);

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId },
                validatedData: updateWithCoupon
            });

            const response = await PUT(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.campaign.coupon_code).toBe("CUSTOM2024");
        });

        it("should update campaign to remove coupon code", async () => {
            const { coupon_code, ...dataWithoutCoupon } = validUpdateData;

            const updatedCampaign = {
                ...mockCampaign,
                ...dataWithoutCoupon,
                coupon_code: null,
                expires_at: new Date(dataWithoutCoupon.expires_at),
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaign.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaign.update.mockResolvedValue(updatedCampaign);

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId },
                validatedData: dataWithoutCoupon
            });

            const response = await PUT(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.campaign.coupon_code).toBe(null);
        });

        it("should require admin authentication", async () => {
            mockBaseHandler.user = { ...mockUser, role: "USER" };
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "PUT",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId },
                validatedData: validUpdateData
            });

            const response = await PUT(req, { params: { id: mockCampaignId } });

            expect(response.status).toBe(401);
        });
    });

    describe("DELETE /api/admin/marketing-campaigns/[id]", () => {
        it("should delete campaign when no leads exist", async () => {
            const campaignWithoutLeads = {
                ...mockCampaign,
                leads: [],
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(campaignWithoutLeads);
            mockPrisma.marketingCampaign.delete.mockResolvedValue(campaignWithoutLeads);

            const req = createMockRequest({
                method: "DELETE",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId }
            });

            const response = await DELETE(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ message: "Campaign deleted successfully" });
            expect(mockPrisma.marketingCampaign.delete).toHaveBeenCalledWith({
                where: { id: mockCampaignId },
            });
        });

        it("should return 404 if campaign not found", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "DELETE",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId }
            });

            const response = await DELETE(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data).toEqual({ error: "Campaign not found" });
        });

        it("should prevent deletion of campaign with existing leads", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);

            const req = createMockRequest({
                method: "DELETE",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId }
            });

            const response = await DELETE(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data).toEqual({ error: "Cannot delete campaign with existing leads" });
            expect(mockPrisma.marketingCampaign.delete).not.toHaveBeenCalled();
        });

        it("should require admin authentication", async () => {
            mockBaseHandler.user = { ...mockUser, role: "USER" };
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "DELETE",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}`,
                params: { id: mockCampaignId }
            });

            const response = await DELETE(req, { params: { id: mockCampaignId } });

            expect(response.status).toBe(401);
        });
    });
}); 