// @ts-nocheck
import { prisma as mockPrisma } from "../../../../../../mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "../../../../../../utils/api-test-utils";

// Import after mocks are set up
import { POST } from "../../../../../../../app/api/admin/marketing-campaigns/[id]/reset-stats/route";

describe("Admin Marketing Campaigns Reset Stats API Route", () => {
    const mockCampaignId = "campaign-123";
    const mockCampaign = {
        id: mockCampaignId,
        slug: "test-campaign",
        title: "Test Campaign",
        description: "Test description",
        discount_type: "PERCENTAGE" as const,
        discount_value: 20,
        status: "ACTIVE" as const,
        views_count: 100,
        leads_count: 50,
        conversions_count: 25,
        created_at: new Date(),
        updated_at: new Date(),
    };

    const mockAdminUser = {
        ...mockUser,
        role: "ADMIN"
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockAdminUser;
        mockBaseHandler.isAdmin = true;
        mockBaseHandler.isAuthenticated = true;
    });

    describe("POST /api/admin/marketing-campaigns/[id]/reset-stats", () => {
        it("should reset campaign stats with confirmation", async () => {
            const resetCampaign = {
                ...mockCampaign,
                views_count: 0,
                leads_count: 0,
                conversions_count: 0,
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaign.update.mockResolvedValue(resetCampaign);

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/reset-stats`,
                params: { id: mockCampaignId },
                validatedData: { confirm: true }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({
                campaign: resetCampaign,
                message: "Campaign stats reset successfully"
            });
            expect(mockPrisma.marketingCampaign.update).toHaveBeenCalledWith({
                where: { id: mockCampaignId },
                data: {
                    views_count: 0,
                    leads_count: 0,
                    conversions_count: 0,
                },
            });
        });

        it("should require confirmation", async () => {
            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/reset-stats`,
                params: { id: mockCampaignId },
                validatedData: { confirm: false }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Confirmation required to reset stats");
            expect(mockPrisma.marketingCampaign.findUnique).not.toHaveBeenCalled();
        });

        it("should return 404 if campaign not found", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/reset-stats`,
                params: { id: mockCampaignId },
                validatedData: { confirm: true }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data).toEqual({ error: "Campaign not found" });
            expect(mockPrisma.marketingCampaign.update).not.toHaveBeenCalled();
        });

        it("should require admin authentication", async () => {
            mockBaseHandler.user = { ...mockUser, role: "USER" };
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/reset-stats`,
                params: { id: mockCampaignId },
                validatedData: { confirm: true }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });

            expect(response.status).toBe(401);
            expect(mockPrisma.marketingCampaign.findUnique).not.toHaveBeenCalled();
        });
    });
}); 