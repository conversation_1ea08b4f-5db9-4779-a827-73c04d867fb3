import { prisma as mockPrisma } from "../../../../../../../tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "../../../../../../../tests/utils/api-test-utils";

// Import after mocks are set up
import { POST } from "../../../../../../../app/api/admin/marketing-campaigns/[id]/toggle-status/route";

describe("Admin Marketing Campaigns Toggle Status API Route", () => {
    const mockCampaignId = "campaign123";
    const mockCampaign = {
        id: mockCampaignId,
        title: "Test Campaign",
        description: "Test campaign description",
        slug: "test-campaign",
        discount_type: "PERCENTAGE",
        discount_value: 25,
        status: "DRAFT",
        expires_at: new Date("2025-12-31T23:59:59Z"),
        views_count: 0,
        leads_count: 0,
        conversions_count: 0,
        created_at: new Date("2024-01-01T00:00:00Z"),
        updated_at: new Date("2024-01-01T00:00:00Z"),
    };

    const mockAdminUser = {
        ...mockUser,
        role: "ADMIN"
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockAdminUser;
        mockBaseHandler.isAdmin = true;
        mockBaseHandler.isAuthenticated = true;
    });

    describe("POST /api/admin/marketing-campaigns/[id]/toggle-status", () => {
        it("should toggle campaign status from DRAFT to ACTIVE", async () => {
            const updatedCampaign = {
                ...mockCampaign,
                status: "ACTIVE",
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaign.update.mockResolvedValue(updatedCampaign);

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/toggle-status`,
                params: { id: mockCampaignId },
                validatedData: { status: "ACTIVE" }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ campaign: updatedCampaign });
            expect(mockPrisma.marketingCampaign.update).toHaveBeenCalledWith({
                where: { id: mockCampaignId },
                data: { status: "ACTIVE" },
            });
        });

        it("should toggle campaign status from ACTIVE to PAUSED", async () => {
            const activeCampaign = {
                ...mockCampaign,
                status: "ACTIVE",
            };

            const pausedCampaign = {
                ...mockCampaign,
                status: "PAUSED",
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(activeCampaign);
            mockPrisma.marketingCampaign.update.mockResolvedValue(pausedCampaign);

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/toggle-status`,
                params: { id: mockCampaignId },
                validatedData: { status: "PAUSED" }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ campaign: pausedCampaign });
            expect(mockPrisma.marketingCampaign.update).toHaveBeenCalledWith({
                where: { id: mockCampaignId },
                data: { status: "PAUSED" },
            });
        });

        it("should set campaign status to EXPIRED", async () => {
            const expiredCampaign = {
                ...mockCampaign,
                status: "EXPIRED",
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaign.update.mockResolvedValue(expiredCampaign);

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/toggle-status`,
                params: { id: mockCampaignId },
                validatedData: { status: "EXPIRED" }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ campaign: expiredCampaign });
            expect(mockPrisma.marketingCampaign.update).toHaveBeenCalledWith({
                where: { id: mockCampaignId },
                data: { status: "EXPIRED" },
            });
        });

        it("should return 404 if campaign not found", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/toggle-status`,
                params: { id: mockCampaignId },
                validatedData: { status: "ACTIVE" }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data).toEqual({ error: "Campaign not found" });
            expect(mockPrisma.marketingCampaign.update).not.toHaveBeenCalled();
        });

        it("should prevent status change for expired campaigns", async () => {
            const expiredCampaign = {
                ...mockCampaign,
                status: "ACTIVE",
                expires_at: new Date("2023-12-31T23:59:59Z"), // Past date
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(expiredCampaign);

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/toggle-status`,
                params: { id: mockCampaignId },
                validatedData: { status: "PAUSED" }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data).toEqual({ error: "Cannot change status of expired campaign" });
            expect(mockPrisma.marketingCampaign.update).not.toHaveBeenCalled();
        });

        it("should allow status change for campaigns with future expiration", async () => {
            const futureExpiredCampaign = {
                ...mockCampaign,
                status: "DRAFT",
                expires_at: new Date("2025-12-31T23:59:59Z"), // Future date
            };

            const activeCampaign = {
                ...futureExpiredCampaign,
                status: "ACTIVE",
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(futureExpiredCampaign);
            mockPrisma.marketingCampaign.update.mockResolvedValue(activeCampaign);

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/toggle-status`,
                params: { id: mockCampaignId },
                validatedData: { status: "ACTIVE" }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ campaign: activeCampaign });
            expect(mockPrisma.marketingCampaign.update).toHaveBeenCalledWith({
                where: { id: mockCampaignId },
                data: { status: "ACTIVE" },
            });
        });

        it("should allow status change for campaigns with no expiration", async () => {
            const noExpirationCampaign = {
                ...mockCampaign,
                expires_at: null,
            };

            const activeCampaign = {
                ...noExpirationCampaign,
                status: "ACTIVE",
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(noExpirationCampaign);
            mockPrisma.marketingCampaign.update.mockResolvedValue(activeCampaign);

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/toggle-status`,
                params: { id: mockCampaignId },
                validatedData: { status: "ACTIVE" }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ campaign: activeCampaign });
            expect(mockPrisma.marketingCampaign.update).toHaveBeenCalledWith({
                where: { id: mockCampaignId },
                data: { status: "ACTIVE" },
            });
        });

        it("should require admin authentication", async () => {
            mockBaseHandler.user = { ...mockUser, role: "USER" };
            mockBaseHandler.isAdmin = false;

            const req = createMockRequest({
                method: "POST",
                url: `/api/admin/marketing-campaigns/${mockCampaignId}/toggle-status`,
                params: { id: mockCampaignId },
                validatedData: { status: "ACTIVE" }
            });

            const response = await POST(req, { params: { id: mockCampaignId } });

            expect(response.status).toBe(401);
            expect(mockPrisma.marketingCampaign.findUnique).not.toHaveBeenCalled();
        });
    });
}); 