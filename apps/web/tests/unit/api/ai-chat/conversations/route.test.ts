import { GET, POST } from "@/app/api/ai-chat/conversations/route";
import { createMockRequest, mockBaseHandler, mockUser } from "../../../../utils/api-test-utils";

// Mock the base handler
jest.mock("@/lib/api/baseHandler", () => ({
    createHandler: jest.fn((config) => {
        return async (req: any, context: any) => {
            // Mock the base handler context
            const handlerContext = {
                user: mockUser,
                params: context?.params || {},
                query: {},
                validatedData: context?.validatedData || req.body,
                isAdmin: false,
                req,
                session: { user: mockUser },
                respond: jest.fn((data, status = 200) => {
                    return new Response(JSON.stringify(data), { status });
                })
            };

            // Call the actual handler function
            return config.handler.call(handlerContext);
        };
    })
}));

// Mock the AIChatService
jest.mock("@/lib/services/ai-chat.service", () => ({
    AIChatService: {
        createConversation: jest.fn(),
        getUserConversations: jest.fn()
    }
}));

import { AIChatService } from "@/lib/services/ai-chat.service";

describe("AI Chat Conversations API", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockUser;
    });

    describe("POST /api/ai-chat/conversations", () => {
        it("should create a new conversation successfully", async () => {
            const mockConversation = {
                conversationId: "conv_123456789",
                status: "created"
            };

            (AIChatService.createConversation as jest.Mock).mockResolvedValue(mockConversation);

            const req = createMockRequest({
                method: "POST",
                url: "/api/ai-chat/conversations",
                body: {}
            });

            const response = await POST(req);

            const data = await response.json();

            expect(AIChatService.createConversation).toHaveBeenCalledWith(mockUser.id);
            expect(response.status).toBe(200);
            expect(data).toEqual(mockConversation);
        });

        it("should handle service errors gracefully", async () => {
            (AIChatService.createConversation as jest.Mock).mockRejectedValue(
                new Error("Database connection failed")
            );

            const req = createMockRequest({
                method: "POST",
                url: "/api/ai-chat/conversations",
                body: {}
            });

            const response = await POST(req);

            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Failed to create conversation");
        });
    });

    describe("GET /api/ai-chat/conversations", () => {
        it("should fetch user conversations successfully", async () => {
            const mockConversations = {
                conversations: [
                    {
                        id: "conv_123456789",
                        title: "Winterization Help",
                        status: "ACTIVE",
                        createdAt: "2024-01-15T10:30:00Z",
                        lastMessageAt: "2024-01-15T11:45:00Z",
                        messageCount: 8,
                        rvDetails: {
                            make: "Keystone",
                            model: "Montana",
                            year: 2026,
                            type: "fifth-wheel"
                        }
                    }
                ],
                total: 1
            };

            (AIChatService.getUserConversations as jest.Mock).mockResolvedValue(mockConversations);

            const req = createMockRequest({
                method: "GET",
                url: "/api/ai-chat/conversations"
            });

            const response = await GET(req);

            const data = await response.json();

            expect(AIChatService.getUserConversations).toHaveBeenCalledWith(mockUser.id);
            expect(response.status).toBe(200);
            expect(data).toEqual(mockConversations);
        });

        it("should return empty conversations when user has none", async () => {
            const mockEmptyConversations = {
                conversations: [],
                total: 0
            };

            (AIChatService.getUserConversations as jest.Mock).mockResolvedValue(mockEmptyConversations);

            const req = createMockRequest({
                method: "GET",
                url: "/api/ai-chat/conversations"
            });

            const response = await GET(req);

            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual(mockEmptyConversations);
        });

        it("should handle service errors gracefully", async () => {
            (AIChatService.getUserConversations as jest.Mock).mockRejectedValue(
                new Error("Database connection failed")
            );

            const req = createMockRequest({
                method: "GET",
                url: "/api/ai-chat/conversations"
            });

            const response = await GET(req);

            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Failed to fetch conversations");
        });
    });
});
