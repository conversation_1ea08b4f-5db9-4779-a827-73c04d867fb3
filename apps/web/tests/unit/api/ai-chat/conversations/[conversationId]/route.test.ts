import { GET } from "@/app/api/ai-chat/conversations/[conversationId]/route";
import { createMockRequest, mockBaseHandler, mockUser } from "../../../../../utils/api-test-utils";

// Mock the base handler
jest.mock("@/lib/api/baseHandler", () => ({
    createHandler: jest.fn((config) => {
        return async (req: any, context: any) => {
            // Mock the base handler context
            const handlerContext = {
                user: mockUser,
                params: context?.params || {},
                query: {},
                validatedData: context?.validatedData || req.body,
                isAdmin: false,
                req,
                session: { user: mockUser },
                respond: jest.fn((data, status = 200) => {
                    return new Response(JSON.stringify(data), { status });
                })
            };

            // Call the actual handler function
            return config.handler.call(handlerContext);
        };
    })
}));

// Mock the AIChatService
jest.mock("@/lib/services/ai-chat.service", () => ({
    AIChatService: {
        getConversationHistory: jest.fn()
    }
}));

import { AIChatService } from "@/lib/services/ai-chat.service";

describe("AI Chat Conversation History API", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockUser;
    });

    describe("GET /api/ai-chat/conversations/[conversationId]", () => {
        const mockConversationId = "conv_123456789";

        it("should fetch conversation history successfully", async () => {
            const mockHistory = {
                conversationId: mockConversationId,
                messages: [
                    {
                        id: "msg_1",
                        text: "How do I winterize my RV?",
                        isUser: true,
                        timestamp: "2024-01-15T10:30:00Z"
                    },
                    {
                        id: "msg_2",
                        text: "To winterize your RV, you'll need to...",
                        isUser: false,
                        timestamp: "2024-01-15T10:31:00Z"
                    }
                ]
            };

            (AIChatService.getConversationHistory as jest.Mock).mockResolvedValue(mockHistory);

            const req = createMockRequest({
                method: "GET",
                url: `/api/ai-chat/conversations/${mockConversationId}`,
                params: { conversationId: mockConversationId }
            });

            const response = await GET(req, { params: { conversationId: mockConversationId } });

            const data = await response.json();

            expect(AIChatService.getConversationHistory).toHaveBeenCalledWith(mockUser.id, mockConversationId);
            expect(response.status).toBe(200);
            expect(data).toEqual(mockHistory);
        });

        it("should return empty messages when conversation has no history", async () => {
            const mockEmptyHistory = {
                conversationId: mockConversationId,
                messages: []
            };

            (AIChatService.getConversationHistory as jest.Mock).mockResolvedValue(mockEmptyHistory);

            const req = createMockRequest({
                method: "GET",
                url: `/api/ai-chat/conversations/${mockConversationId}`,
                params: { conversationId: mockConversationId }
            });

            const response = await GET(req, { params: { conversationId: mockConversationId } });

            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual(mockEmptyHistory);
        });

        it("should handle service errors gracefully", async () => {
            (AIChatService.getConversationHistory as jest.Mock).mockRejectedValue(
                new Error("Database connection failed")
            );

            const req = createMockRequest({
                method: "GET",
                url: `/api/ai-chat/conversations/${mockConversationId}`,
                params: { conversationId: mockConversationId }
            });

            const response = await GET(req, { params: { conversationId: mockConversationId } });

            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Failed to fetch conversation history");
        });

        it("should handle conversation not found", async () => {
            (AIChatService.getConversationHistory as jest.Mock).mockRejectedValue(
                new Error("Conversation not found")
            );

            const req = createMockRequest({
                method: "GET",
                url: `/api/ai-chat/conversations/${mockConversationId}`,
                params: { conversationId: mockConversationId }
            });

            const response = await GET(req, { params: { conversationId: mockConversationId } });

            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Failed to fetch conversation history");
        });
    });
});
