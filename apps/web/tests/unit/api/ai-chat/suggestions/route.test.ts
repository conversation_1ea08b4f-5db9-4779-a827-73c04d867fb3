import { GET } from "@/app/api/ai-chat/suggestions/route";
import { createMockRequest, mockBaseHandler, mockUser } from "../../../../utils/api-test-utils";

// Mock the base handler
jest.mock("@/lib/api/baseHandler", () => ({
    createHandler: jest.fn((config) => {
        return async (req: any, context: any) => {
            // Mock the base handler context
            const handlerContext = {
                user: mockUser,
                params: context?.params || {},
                query: {},
                validatedData: context?.validatedData || req.body,
                isAdmin: false,
                req,
                session: { user: mockUser },
                respond: jest.fn((data, status = 200) => {
                    return new Response(JSON.stringify(data), { status });
                })
            };

            // Call the actual handler function
            return config.handler.call(handlerContext);
        };
    })
}));

// Mock the AIChatService
jest.mock("@/lib/services/ai-chat.service", () => ({
    AIChatService: {
        getSuggestedQuestions: jest.fn()
    }
}));

import { AIChatService } from "@/lib/services/ai-chat.service";

describe("AI Chat Suggestions API", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockUser;
    });

    describe("GET /api/ai-chat/suggestions", () => {
        it("should fetch suggested questions successfully", async () => {
            const mockSuggestions = [
                "How do I winterize my RV?",
                "What's the best way to maintain my RV batteries?",
                "How often should I check my RV tires?",
                "What should I do if my RV slide-out won't work?",
                "How do I troubleshoot my RV's electrical system?"
            ];

            (AIChatService.getSuggestedQuestions as jest.Mock).mockResolvedValue(mockSuggestions);

            const req = createMockRequest({
                method: "GET",
                url: "/api/ai-chat/suggestions"
            });

            const response = await GET(req);

            const data = await response.json();

            expect(AIChatService.getSuggestedQuestions).toHaveBeenCalledWith(mockUser.id);
            expect(response.status).toBe(200);
            expect(data).toEqual(mockSuggestions);
        });

        it("should return empty suggestions when user has no context", async () => {
            const mockEmptySuggestions: string[] = [];

            (AIChatService.getSuggestedQuestions as jest.Mock).mockResolvedValue(mockEmptySuggestions);

            const req = createMockRequest({
                method: "GET",
                url: "/api/ai-chat/suggestions"
            });

            const response = await GET(req);

            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual(mockEmptySuggestions);
        });

        it("should handle service errors gracefully", async () => {
            (AIChatService.getSuggestedQuestions as jest.Mock).mockRejectedValue(
                new Error("Database connection failed")
            );

            const req = createMockRequest({
                method: "GET",
                url: "/api/ai-chat/suggestions"
            });

            const response = await GET(req);

            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Failed to fetch suggestions");
        });

        it("should return contextual suggestions based on user's RV", async () => {
            const mockContextualSuggestions = [
                "How do I winterize my 2026 Keystone Montana?",
                "What's the best way to maintain my fifth-wheel's slide-outs?",
                "How often should I check my Montana's tires?",
                "What should I do if my Keystone's electrical system fails?",
                "How do I troubleshoot my fifth-wheel's plumbing system?"
            ];

            (AIChatService.getSuggestedQuestions as jest.Mock).mockResolvedValue(mockContextualSuggestions);

            const req = createMockRequest({
                method: "GET",
                url: "/api/ai-chat/suggestions"
            });

            const response = await GET(req);

            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual(mockContextualSuggestions);
        });
    });
});
