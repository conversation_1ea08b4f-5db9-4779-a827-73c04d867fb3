import { POST } from "@/app/api/users/set-warranty-password/route";
import { WarrantyRequestService } from "@/lib/services/warranty-request.service";
import { NextRequest } from "next/server";

// Mock the service
jest.mock("@/lib/services/warranty-request.service");

const mockWarrantyRequestService = WarrantyRequestService as jest.Mocked<typeof WarrantyRequestService>;

describe("/api/users/set-warranty-password", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	const createRequest = (body: any) => {
		return new NextRequest("http://localhost/api/users/set-warranty-password", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify(body)
		});
	};

	const validRequestBody = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "password123",
		confirmPassword: "password123",
		newsletterSourceTag: "Added by Test Company",
		companyName: "Test Company"
	};

	describe("Success cases", () => {
		it("should successfully set password using service", async () => {
			const expectedResult = {
				success: true,
				message: "Password set successfully",
				user: {
					id: "user-123",
					email: "<EMAIL>",
					first_name: "John",
					last_name: "Doe"
				}
			};

			mockWarrantyRequestService.setWarrantyPassword.mockResolvedValue(expectedResult);

			const request = createRequest(validRequestBody);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual(expectedResult);

			expect(mockWarrantyRequestService.setWarrantyPassword).toHaveBeenCalledWith({
				firstName: "John",
				lastName: "Doe",
				email: "<EMAIL>",
				password: "password123",
				newsletterSourceTag: "Added by Test Company",
				companyName: "Test Company",
				sms_opt_in: true
			});
		});

		it("should handle optional fields correctly", async () => {
			const requestWithoutOptionalFields = {
				firstName: "John",
				lastName: "Doe",
				email: "<EMAIL>",
				password: "password123",
				confirmPassword: "password123"
			};

			const expectedResult = {
				success: true,
				message: "Password set successfully",
				user: {
					id: "user-123",
					email: "<EMAIL>",
					first_name: "John",
					last_name: "Doe"
				}
			};

			mockWarrantyRequestService.setWarrantyPassword.mockResolvedValue(expectedResult);

			const request = createRequest(requestWithoutOptionalFields);
			const response = await POST(request);

			expect(response.status).toBe(200);
			expect(mockWarrantyRequestService.setWarrantyPassword).toHaveBeenCalledWith({
				firstName: "John",
				lastName: "Doe",
				email: "<EMAIL>",
				password: "password123",
				newsletterSourceTag: undefined,
				companyName: undefined,
				sms_opt_in: true
			});
		});
	});

	describe("Error cases from service", () => {
		it("should return 404 when service throws 'User not found'", async () => {
			mockWarrantyRequestService.setWarrantyPassword.mockRejectedValue(new Error("User not found"));

			const request = createRequest(validRequestBody);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(404);
			expect(data.error).toBe("User not found");
		});

		it("should return 400 when service throws password already set error", async () => {
			mockWarrantyRequestService.setWarrantyPassword.mockRejectedValue(
				new Error("User already has a password set. Please use login instead.")
			);

			const request = createRequest(validRequestBody);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toBe("User already has a password set. Please use login instead.");
		});

		it("should return 500 for other service errors", async () => {
			mockWarrantyRequestService.setWarrantyPassword.mockRejectedValue(new Error("Database connection failed"));

			const request = createRequest(validRequestBody);
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data.error).toBe("Failed to set password");
		});
	});

	describe("Validation errors", () => {
		it("should return 400 when passwords don't match", async () => {
			const request = createRequest({
				...validRequestBody,
				confirmPassword: "different-password"
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(422);
			expect(data.error).toBe("Validation failed");
		});

		it("should return 400 when password is too short", async () => {
			const request = createRequest({
				...validRequestBody,
				password: "123",
				confirmPassword: "123"
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(422);
			expect(data.error).toBe("Validation failed");
		});

		it("should return 422 when required fields are missing", async () => {
			const request = createRequest({
				email: "<EMAIL>",
				password: "password123"
				// Missing firstName, lastName, confirmPassword
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(422);
			expect(data.error).toBe("Validation failed");
		});

		it("should return 422 for invalid email format", async () => {
			const request = createRequest({
				...validRequestBody,
				email: "invalid-email"
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(422);
			expect(data.error).toBe("Validation failed");
		});
	});
});