import { prisma as mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler
} from "@/tests/utils/api-test-utils";

import { POST } from "@/app/api/auth/check-service-request-user/route";

describe("POST /api/auth/check-service-request-user", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should return 404 when service request not found", async () => {
        mockPrisma.job.findUnique.mockResolvedValue(null);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/check-service-request-user",
            body: {
                serviceRequestId: "nonexistent-id"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "nonexistent-id" }
        })(req);

        expect(response.status).toBe(404);
        expect(await response.json()).toEqual({
            error: "Service request not found"
        });
    });

    it("should return needsPasswordSetup: true when user does not exist", async () => {
        const mockJob = {
            id: "job123",
            email: "<EMAIL>"
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJob);
        mockPrisma.user.findFirst.mockResolvedValue(null);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/check-service-request-user",
            body: {
                serviceRequestId: "job123"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "job123" }
        })(req);

        expect(response.status).toBe(200);
        expect(await response.json()).toEqual({
            userExists: false,
            hasPassword: false,
            needsPasswordSetup: true,
            email: "<EMAIL>"
        });
    });

    it("should return needsPasswordSetup: true when user exists but has never logged in", async () => {
        const mockJob = {
            id: "job123",
            email: "<EMAIL>"
        };

        const mockUser = {
            id: "user123",
            email: "<EMAIL>",
            password: "hashedpassword",
            last_login: null, // Never logged in
            first_name: "John",
            last_name: "Doe"
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJob);
        mockPrisma.user.findFirst.mockResolvedValue(mockUser);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/check-service-request-user",
            body: {
                serviceRequestId: "job123"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "job123" }
        })(req);

        expect(response.status).toBe(200);
        expect(await response.json()).toEqual({
            userExists: true,
            hasPassword: true,
            needsPasswordSetup: true,
            email: "<EMAIL>",
            firstName: "John",
            lastName: "Doe"
        });
    });

    it("should return needsPasswordSetup: false when user exists and has logged in before", async () => {
        const mockJob = {
            id: "job123",
            email: "<EMAIL>"
        };

        const mockUser = {
            id: "user123",
            email: "<EMAIL>",
            password: "hashedpassword",
            last_login: new Date("2023-01-01"), // Has logged in before
            first_name: "John",
            last_name: "Doe"
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJob);
        mockPrisma.user.findFirst.mockResolvedValue(mockUser);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/check-service-request-user",
            body: {
                serviceRequestId: "job123"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "job123" }
        })(req);

        expect(response.status).toBe(200);
        expect(await response.json()).toEqual({
            userExists: true,
            hasPassword: true,
            needsPasswordSetup: false,
            email: "<EMAIL>",
            firstName: "John",
            lastName: "Doe"
        });
    });

    it("should handle case-insensitive email matching", async () => {
        const mockJob = {
            id: "job123",
            email: "<EMAIL>"
        };

        const mockUser = {
            id: "user123",
            email: "<EMAIL>",
            password: "hashedpassword",
            last_login: null,
            first_name: "John",
            last_name: "Doe"
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJob);
        mockPrisma.user.findFirst.mockResolvedValue(mockUser);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/check-service-request-user",
            body: {
                serviceRequestId: "job123"
            }
        });

        await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "job123" }
        })(req);

        expect(mockPrisma.user.findFirst).toHaveBeenCalledWith({
            where: {
                email: {
                    equals: "<EMAIL>",
                    mode: "insensitive"
                }
            },
            select: {
                id: true,
                email: true,
                password: true,
                last_login: true,
                first_name: true,
                last_name: true
            }
        });
    });
}); 