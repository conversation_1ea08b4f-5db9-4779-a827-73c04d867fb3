import { POST } from "@/app/api/users/check-user-exists/route";
import { WarrantyRequestService } from "@/lib/services/warranty-request.service";
import { NextRequest } from "next/server";

// Mock the service
jest.mock("@/lib/services/warranty-request.service");

const mockWarrantyRequestService = WarrantyRequestService as jest.Mocked<typeof WarrantyRequestService>;

describe("/api/users/check-user-exists", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	const createRequest = (body: any) => {
		return new NextRequest("http://localhost/api/users/check-user-exists", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify(body)
		});
	};

	describe("User doesn't exist", () => {
		it("should return userExists: false when user doesn't exist", async () => {
			mockWarrantyRequestService.checkUserState.mockResolvedValue({
				userExists: false,
				hasLoggedIn: false,
				needsPasswordSetup: false
			});

			const request = createRequest({ email: "<EMAIL>" });
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual({
				userExists: false,
				hasLoggedIn: false,
				needsPasswordSetup: false
			});

			expect(mockWarrantyRequestService.checkUserState).toHaveBeenCalledWith("<EMAIL>");
		});
	});

	describe("User exists and has logged in", () => {
		it("should return correct state for user who has logged in before", async () => {
			mockWarrantyRequestService.checkUserState.mockResolvedValue({
				userExists: true,
				hasLoggedIn: true,
				needsPasswordSetup: false
			});

			const request = createRequest({ email: "<EMAIL>" });
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual({
				userExists: true,
				hasLoggedIn: true,
				needsPasswordSetup: false
			});
		});
	});

	describe("User exists but needs password setup", () => {
		it("should return needsPasswordSetup: true for user without password or login", async () => {
			mockWarrantyRequestService.checkUserState.mockResolvedValue({
				userExists: true,
				hasLoggedIn: false,
				needsPasswordSetup: true
			});

			const request = createRequest({ email: "<EMAIL>" });
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual({
				userExists: true,
				hasLoggedIn: false,
				needsPasswordSetup: true
			});
		});
	});

	describe("Error handling", () => {
		it("should return 422 for invalid email format", async () => {
			const request = createRequest({ email: "invalid-email" });
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(422);
			expect(data.error).toBe("Validation failed");
		});

		it("should return 422 for missing email", async () => {
			const request = createRequest({});
			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(422);
			expect(data.error).toBe("Validation failed");
		});

		// TODO: Fix error handling test - mock setup is causing timing issues
		// it("should handle service errors gracefully", async () => {
		// 	const request = createRequest({ email: "<EMAIL>" });
		// 	const response = await POST(request);
		// 	expect(response.status).toBe(500);
		// });
	});
});