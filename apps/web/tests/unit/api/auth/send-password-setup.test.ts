import { prisma as mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler
} from "@/tests/utils/api-test-utils";

import { POST } from "@/app/api/auth/send-password-setup/route";

// Mock the email service
jest.mock("@/lib/services", () => ({
    emailService: {
        sendServiceRequestPasswordSetupEmail: jest.fn()
    }
}));

// Mock the token generator
jest.mock("@/lib/utils/token", () => ({
    generateToken: jest.fn(() => "mock-token-123")
}));

import { emailService } from "@/lib/services";

describe("POST /api/auth/send-password-setup", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should return 404 when service request not found", async () => {
        mockPrisma.job.findUnique.mockResolvedValue(null);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/send-password-setup",
            body: {
                serviceRequestId: "nonexistent-id",
                email: "<EMAIL>"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "nonexistent-id", email: "<EMAIL>" }
        })(req);

        expect(response.status).toBe(404);
        expect(await response.json()).toEqual({
            error: "Service request not found"
        });
    });

    it("should return 403 when email does not match service request", async () => {
        const mockJob = {
            id: "job123",
            email: "<EMAIL>"
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJob);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/send-password-setup",
            body: {
                serviceRequestId: "job123",
                email: "<EMAIL>"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "job123", email: "<EMAIL>" }
        })(req);

        expect(response.status).toBe(403);
        expect(await response.json()).toEqual({
            error: "Email does not match service request"
        });
    });

    it("should return 404 when user not found", async () => {
        const mockJob = {
            id: "job123",
            email: "<EMAIL>"
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJob);
        mockPrisma.user.findFirst.mockResolvedValue(null);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/send-password-setup",
            body: {
                serviceRequestId: "job123",
                email: "<EMAIL>"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "job123", email: "<EMAIL>" }
        })(req);

        expect(response.status).toBe(404);
        expect(await response.json()).toEqual({
            error: "User not found"
        });
    });

    it("should return 400 when user has already logged in", async () => {
        const mockJob = {
            id: "job123",
            email: "<EMAIL>"
        };

        const mockUser = {
            id: "user123",
            email: "<EMAIL>",
            last_login: new Date("2023-01-01") // Has logged in before
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJob);
        mockPrisma.user.findFirst.mockResolvedValue(mockUser);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/send-password-setup",
            body: {
                serviceRequestId: "job123",
                email: "<EMAIL>"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "job123", email: "<EMAIL>" }
        })(req);

        expect(response.status).toBe(400);
        expect(await response.json()).toEqual({
            error: "User has already logged in before. Please use the login page."
        });
    });

    it("should successfully send password setup email for user who has never logged in", async () => {
        const mockJob = {
            id: "job123",
            email: "<EMAIL>",
            user_id: "user123",
            first_name: "John",
            last_name: "Doe",
            category: "rv-repair",
            message: "My RV needs repair",
            rv_make: "Ford",
            rv_model: "E-450",
            rv_year: "2020",
            rv_type: "Motorhome"
        };

        const mockUser = {
            id: "user123",
            email: "<EMAIL>",
            last_login: null, // Never logged in
            first_name: "John",
            last_name: "Doe"
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJob);
        mockPrisma.user.findFirst.mockResolvedValue(mockUser);
        mockPrisma.verificationToken.deleteMany.mockResolvedValue({ count: 0 });
        mockPrisma.verificationToken.create.mockResolvedValue({
            id: "token123",
            token: "mock-token-123",
            expires: new Date(),
            user_id: "user123",
            type: "password_setup"
        });

        (emailService.sendServiceRequestPasswordSetupEmail as jest.Mock).mockResolvedValue({ success: true });

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/send-password-setup",
            body: {
                serviceRequestId: "job123",
                email: "<EMAIL>"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "job123", email: "<EMAIL>" }
        })(req);

        expect(response.status).toBe(200);
        expect(await response.json()).toEqual({
            success: true,
            message: "Password setup instructions sent to your email"
        });

        // Verify token cleanup
        expect(mockPrisma.verificationToken.deleteMany).toHaveBeenCalledWith({
            where: {
                user_id: "user123",
                type: "password_setup"
            }
        });

        // Verify token creation
        expect(mockPrisma.verificationToken.create).toHaveBeenCalledWith({
            data: {
                token: "mock-token-123",
                expires: expect.any(Date),
                user_id: "user123",
                type: "password_setup"
            }
        });

        // Verify email sending
        expect(emailService.sendServiceRequestPasswordSetupEmail).toHaveBeenCalledWith({
            email: "<EMAIL>",
            firstName: "John",
            lastName: "Doe",
            serviceRequestId: "job123",
            token: "mock-token-123",
            rvDetails: {
                year: "2020",
                make: "Ford",
                model: "E-450",
                type: "Motorhome"
            },
            category: "rv-repair",
            message: "My RV needs repair"
        });
    });

    it("should handle case-insensitive email matching", async () => {
        const mockJob = {
            id: "job123",
            email: "<EMAIL>"
        };

        const mockUser = {
            id: "user123",
            email: "<EMAIL>",
            last_login: null
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJob);
        mockPrisma.user.findFirst.mockResolvedValue(mockUser);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/send-password-setup",
            body: {
                serviceRequestId: "job123",
                email: "<EMAIL>"
            }
        });

        await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "job123", email: "<EMAIL>" }
        })(req);

        expect(mockPrisma.user.findFirst).toHaveBeenCalledWith({
            where: {
                email: {
                    equals: "<EMAIL>",
                    mode: "insensitive"
                }
            }
        });
    });

    it("should handle token expiry of 24 hours", async () => {
        const mockJob = {
            id: "job123",
            email: "<EMAIL>"
        };

        const mockUser = {
            id: "user123",
            email: "<EMAIL>",
            last_login: null
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJob);
        mockPrisma.user.findFirst.mockResolvedValue(mockUser);
        mockPrisma.verificationToken.deleteMany.mockResolvedValue({ count: 0 });
        mockPrisma.verificationToken.create.mockResolvedValue({});

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/send-password-setup",
            body: {
                serviceRequestId: "job123",
                email: "<EMAIL>"
            }
        });

        const now = new Date();
        jest.spyOn(Date, 'now').mockReturnValue(now.getTime());

        await POST.bind({
            ...mockBaseHandler,
            validatedData: { serviceRequestId: "job123", email: "<EMAIL>" }
        })(req);

        const expectedExpiry = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        expect(mockPrisma.verificationToken.create).toHaveBeenCalledWith({
            data: {
                token: "mock-token-123",
                expires: expectedExpiry,
                user_id: "user123",
                type: "password_setup"
            }
        });

        jest.restoreAllMocks();
    });
}); 