import bcrypt from "bcryptjs";
import { prisma as mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler
} from "@/tests/utils/api-test-utils";

// Mock bcrypt
jest.mock("bcryptjs");
const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

// Import after mocks are set up
import { POST } from "@/app/api/auth/setup-password/route";

describe("POST /api/auth/setup-password", () => {
    const mockVerificationToken = {
        id: "token123",
        token: "test-token-123",
        type: "password_setup",
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        user_id: "user123",
        user: {
            id: "user123",
            email: "<EMAIL>",
            first_name: "<PERSON>",
            last_name: "<PERSON><PERSON>"
        }
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBcrypt.hash.mockResolvedValue("hashed-password");
    });

    it("should successfully set password with valid token", async () => {
        mockPrisma.verificationToken.findFirst.mockResolvedValue(mockVerificationToken);
        mockPrisma.user.update.mockResolvedValue({
            id: "user123",
            email: "<EMAIL>"
        });
        mockPrisma.verificationToken.delete.mockResolvedValue(mockVerificationToken);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/setup-password",
            body: {
                token: "test-token-123",
                password: "newpassword123"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { token: "test-token-123", password: "newpassword123" }
        })(req);

        expect(response.status).toBe(200);
        expect(await response.json()).toEqual({
            success: true,
            email: "<EMAIL>"
        });

        // Verify token lookup
        expect(mockPrisma.verificationToken.findFirst).toHaveBeenCalledWith({
            where: {
                token: "test-token-123",
                type: "password_setup",
                expires: {
                    gt: expect.any(Date)
                }
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        first_name: true,
                        last_name: true
                    }
                }
            }
        });

        // Verify password hashing
        expect(mockBcrypt.hash).toHaveBeenCalledWith("newpassword123", 10);

        // Verify user update (auth endpoint also verifies email)
        expect(mockPrisma.user.update).toHaveBeenCalledWith({
            where: { id: "user123" },
            data: {
                password: "hashed-password",
                email_verified_at: expect.any(Date)
            }
        });

        // Verify token deletion
        expect(mockPrisma.verificationToken.delete).toHaveBeenCalledWith({
            where: { id: "token123" }
        });
    });

    it("should return 400 when token is invalid", async () => {
        mockPrisma.verificationToken.findFirst.mockResolvedValue(null);

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/setup-password",
            body: {
                token: "invalid-token",
                password: "newpassword123"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { token: "invalid-token", password: "newpassword123" }
        })(req);

        expect(response.status).toBe(400);
        expect(await response.json()).toEqual({
            error: "Invalid or expired token"
        });

        // Should not update user or delete token
        expect(mockPrisma.user.update).not.toHaveBeenCalled();
        expect(mockPrisma.verificationToken.delete).not.toHaveBeenCalled();
    });

    it("should return 400 when token is expired", async () => {
        mockPrisma.verificationToken.findFirst.mockResolvedValue(null); // Expired tokens won't be found

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/setup-password",
            body: {
                token: "expired-token",
                password: "newpassword123"
            }
        });

        const response = await POST.bind({
            ...mockBaseHandler,
            validatedData: { token: "expired-token", password: "newpassword123" }
        })(req);

        expect(response.status).toBe(400);
        expect(await response.json()).toEqual({
            error: "Invalid or expired token"
        });

        // Should not update user or delete token
        expect(mockPrisma.user.update).not.toHaveBeenCalled();
        expect(mockPrisma.verificationToken.delete).not.toHaveBeenCalled();
    });

    it("should handle database errors gracefully", async () => {
        mockPrisma.verificationToken.findFirst.mockResolvedValue(mockVerificationToken);
        mockPrisma.user.update.mockRejectedValue(new Error("Database error"));

        const req = createMockRequest({
            method: "POST",
            url: "/api/auth/setup-password",
            body: {
                token: "test-token-123",
                password: "newpassword123"
            }
        });

        await expect(
            POST.bind({
                ...mockBaseHandler,
                validatedData: { token: "test-token-123", password: "newpassword123" }
            })(req)
        ).rejects.toThrow("Database error");

        // Should still have attempted to hash password and update user
        expect(mockBcrypt.hash).toHaveBeenCalledWith("newpassword123", 10);
        expect(mockPrisma.user.update).toHaveBeenCalled();

        // Should not have deleted token due to error
        expect(mockPrisma.verificationToken.delete).not.toHaveBeenCalled();
    });
});