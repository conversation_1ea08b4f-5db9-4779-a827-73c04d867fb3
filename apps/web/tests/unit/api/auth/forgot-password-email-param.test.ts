import { POST } from "@/app/api/auth/forgot-password/route";
import { createMockRequest, mockBaseHandler } from "@/tests/utils/api-test-utils";
import { prisma as mockPrisma } from "../../../mocks/prisma-mock";

describe("Forgot Password with Email Parameter", () => {
    const mockUser = {
        id: "user123",
        email: "<EMAIL>",
        first_name: "Test",
        last_name: "User",
        role: "USER"
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.requireAuth = false;
    });

    describe("POST /api/auth/forgot-password", () => {
        it("should handle forgot password request with valid email", async () => {
            mockPrisma.user.findFirst.mockResolvedValue(mockUser);
            mockPrisma.verificationToken.deleteMany.mockResolvedValue({ count: 1 });
            mockPrisma.verificationToken.create.mockResolvedValue({
                id: "token123",
                token: "reset-token",
                expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
                user_id: mockUser.id,
                type: "password_reset"
            });

            const req = createMockRequest({
                method: "POST",
                url: "/api/auth/forgot-password",
                body: { email: "<EMAIL>" }
            });

            mockBaseHandler.validatedData = { email: "<EMAIL>" };

            const response = await POST(req);

            expect(response.status).toBe(200);
            expect(await response.json()).toEqual({
                success: true,
                message: "Password reset instructions have been sent to your email address."
            });

            // Verify that existing tokens were deleted
            expect(mockPrisma.verificationToken.deleteMany).toHaveBeenCalledWith({
                where: {
                    user_id: mockUser.id,
                    type: "password_reset"
                }
            });

            // Verify that a new token was created with 24-hour expiry
            expect(mockPrisma.verificationToken.create).toHaveBeenCalledWith({
                data: {
                    token: expect.any(String),
                    expires: expect.any(Date),
                    user_id: mockUser.id,
                    type: "password_reset"
                }
            });

            // Verify the token expiry is set to 24 hours from now
            const createCall = mockPrisma.verificationToken.create.mock.calls[0][0];
            const expectedExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);
            const actualExpiry = createCall.data.expires;

            // Allow for a small time difference (within 1 second)
            expect(Math.abs(actualExpiry.getTime() - expectedExpiry.getTime())).toBeLessThan(1000);
        });

        it("should handle case-insensitive email lookup", async () => {
            mockPrisma.user.findFirst.mockResolvedValue(mockUser);
            mockPrisma.verificationToken.deleteMany.mockResolvedValue({ count: 1 });
            mockPrisma.verificationToken.create.mockResolvedValue({
                id: "token123",
                token: "reset-token",
                expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
                user_id: mockUser.id,
                type: "password_reset"
            });

            const req = createMockRequest({
                method: "POST",
                url: "/api/auth/forgot-password",
                body: { email: "<EMAIL>" }
            });

            mockBaseHandler.validatedData = { email: "<EMAIL>" };

            const response = await POST(req);

            expect(response.status).toBe(200);
            expect(await response.json()).toEqual({
                success: true,
                message: "Password reset instructions have been sent to your email address."
            });

            // Verify case-insensitive email lookup was used
            expect(mockPrisma.user.findFirst).toHaveBeenCalledWith({
                where: { email: { equals: "<EMAIL>", mode: "insensitive" } }
            });
        });

        it("should return success message even if user doesn't exist (security)", async () => {
            mockPrisma.user.findFirst.mockResolvedValue(null);

            const req = createMockRequest({
                method: "POST",
                url: "/api/auth/forgot-password",
                body: { email: "<EMAIL>" }
            });

            mockBaseHandler.validatedData = { email: "<EMAIL>" };

            const response = await POST(req);

            expect(response.status).toBe(200);
            expect(await response.json()).toEqual({
                success: false,
                message: "No account found with this email address."
            });

            // Verify no tokens were created for non-existent user
            expect(mockPrisma.verificationToken.create).not.toHaveBeenCalled();
        });
    });
});
