import { EmailNewsletterService } from "../../../../../lib/services/emailNewsletter.service";
import { prisma as mockPrisma } from "../../../../../tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler
} from "../../../../../tests/utils/api-test-utils";

// Mock the email service
jest.mock("@/lib/services", () => ({
    emailService: {
        send: jest.fn().mockResolvedValue({ success: true })
    }
}));

// Mock the EmailNewsletterService
jest.mock("@/lib/services/emailNewsletter.service", () => ({
    EmailNewsletterService: {
        syncNewsletterSubscriber: jest.fn()
    }
}));

// Import after mocks are set up
import { GET, POST } from "../../../../../app/api/marketing-campaigns/[slug]/route";

describe("Public Marketing Campaigns API Routes", () => {
    const mockSlug = "test-campaign";
    const mockCampaign = {
        id: "campaign123",
        title: "Test Campaign",
        description: "Test campaign description",
        slug: mockSlug,
        discount_type: "PERCENTAGE",
        discount_value: 25,
        coupon_code: null, // Will auto-generate
        status: "ACTIVE",
        expires_at: new Date("2025-12-31T23:59:59Z"),
        page_title: "Special Offer",
        page_subtitle: "Limited Time Deal",
        page_description: "Get 25% off RV Help Pro membership",
        button_text: "Get Discount",
        success_message: "Check your email for the discount code!",
        email_subject: "Your RV Help Pro Discount Code",
        email_template: "Thanks for signing up! Use code {{coupon_code}} for 25% off.",
        views_count: 100,
        leads_count: 50,
        conversions_count: 10,
        created_at: new Date("2024-01-01T00:00:00Z"),
        updated_at: new Date("2024-01-01T00:00:00Z"),
    };

    const mockLeadData = {
        email: "<EMAIL>",
        first_name: "John",
        last_name: "Doe",
        utm_source: "google",
        utm_medium: "cpc",
        utm_campaign: "test-campaign",
    };

    const mockCreatedLead = {
        id: "lead123",
        campaign_id: mockCampaign.id,
        email: mockLeadData.email,
        first_name: mockLeadData.first_name,
        last_name: mockLeadData.last_name,
        coupon_code: "TEST-CAMPAIGN-ABC123",
        ip_address: "***********",
        user_agent: "Mozilla/5.0",
        referrer: "https://google.com",
        utm_source: mockLeadData.utm_source,
        utm_medium: mockLeadData.utm_medium,
        utm_campaign: mockLeadData.utm_campaign,
        created_at: new Date("2024-01-02T00:00:00Z"),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = null;
        mockBaseHandler.isAuthenticated = false;
        mockBaseHandler.isAdmin = false;

        // Mock crypto.randomBytes to return hex string that becomes 'ABC123'
        jest.spyOn(require('crypto'), 'randomBytes').mockReturnValue(Buffer.from('ABC123', 'hex'));
    });

    describe("GET /api/marketing-campaigns/[slug]", () => {
        it("should return campaign details for active campaign", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaign.update.mockResolvedValue({
                ...mockCampaign,
                views_count: 101,
            });

            const req = createMockRequest({
                method: "GET",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug }
            });

            const response = await GET(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.campaign).toEqual(mockCampaign);

            // Should increment view count
            expect(mockPrisma.marketingCampaign.update).toHaveBeenCalledWith({
                where: { slug: mockSlug },
                data: {
                    views_count: {
                        increment: 1,
                    },
                },
            });
        });

        it("should return 404 for non-existent campaign", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "GET",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug }
            });

            const response = await GET(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data).toEqual({ error: "Campaign not found" });
        });

        it("should return 400 for inactive campaign", async () => {
            const inactiveCampaign = {
                ...mockCampaign,
                status: "DRAFT",
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(inactiveCampaign);

            const req = createMockRequest({
                method: "GET",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug }
            });

            const response = await GET(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data).toEqual({ error: "Campaign is not active" });
        });

        it("should return 400 for expired campaign", async () => {
            const expiredCampaign = {
                ...mockCampaign,
                expires_at: new Date("2023-12-31T23:59:59Z"), // Past date
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(expiredCampaign);

            const req = createMockRequest({
                method: "GET",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug }
            });

            const response = await GET(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data).toEqual({ error: "Campaign has expired" });
        });

        it("should work for campaign with no expiration", async () => {
            const noExpirationCampaign = {
                ...mockCampaign,
                expires_at: null,
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(noExpirationCampaign);
            mockPrisma.marketingCampaign.update.mockResolvedValue({
                ...noExpirationCampaign,
                views_count: 101,
            });

            const req = createMockRequest({
                method: "GET",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug }
            });

            const response = await GET(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.campaign).toBeDefined();
        });
    });

    describe("POST /api/marketing-campaigns/[slug]", () => {
        it("should create lead and send email for valid submission", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaignLead.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaignLead.create.mockResolvedValue(mockCreatedLead);
            mockPrisma.marketingCampaign.update.mockResolvedValue({
                ...mockCampaign,
                leads_count: 51,
            });

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData,
                headers: {
                    "x-forwarded-for": "***********",
                    "user-agent": "Mozilla/5.0",
                    "referer": "https://google.com",
                }
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({
                success: true,
                message: "Check your email for the discount code!",
                coupon_code: "TEST-CAMPAIGN-ABC123",
            });

            // Should create lead
            expect(mockPrisma.marketingCampaignLead.create).toHaveBeenCalledWith({
                data: {
                    campaign_id: mockCampaign.id,
                    email: mockLeadData.email,
                    first_name: mockLeadData.first_name,
                    last_name: mockLeadData.last_name,
                    coupon_code: "TEST-CAMPAIGN-ABC123",
                    ip_address: "***********",
                    user_agent: "Mozilla/5.0",
                    referrer: "https://google.com",
                    utm_source: mockLeadData.utm_source,
                    utm_medium: mockLeadData.utm_medium,
                    utm_campaign: mockLeadData.utm_campaign,
                },
            });

            // Should update leads count
            expect(mockPrisma.marketingCampaign.update).toHaveBeenCalledWith({
                where: { id: mockCampaign.id },
                data: {
                    leads_count: {
                        increment: 1,
                    },
                },
            });
        });

        it("should return 404 for non-existent campaign", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(null);

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data).toEqual({ error: "Campaign not found" });
        });

        it("should return 400 for inactive campaign", async () => {
            const inactiveCampaign = {
                ...mockCampaign,
                status: "DRAFT",
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(inactiveCampaign);

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data).toEqual({ error: "Campaign is not active" });
        });

        it("should return 400 for expired campaign", async () => {
            const expiredCampaign = {
                ...mockCampaign,
                expires_at: new Date("2023-12-31T23:59:59Z"), // Past date
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(expiredCampaign);

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data).toEqual({ error: "Campaign has expired" });
        });

        it("should prevent duplicate email submissions", async () => {
            const existingLead = {
                ...mockCreatedLead,
                email: mockLeadData.email,
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaignLead.findFirst.mockResolvedValue(existingLead);

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data).toEqual({ error: "Email already registered for this campaign" });
            expect(mockPrisma.marketingCampaignLead.create).not.toHaveBeenCalled();
        });

        it("should handle campaign with no expiration", async () => {
            const noExpirationCampaign = {
                ...mockCampaign,
                expires_at: null,
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(noExpirationCampaign);
            mockPrisma.marketingCampaignLead.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaignLead.create.mockResolvedValue(mockCreatedLead);
            mockPrisma.marketingCampaign.update.mockResolvedValue({
                ...noExpirationCampaign,
                leads_count: 51,
            });

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);
        });

        it("should handle submission with minimal data", async () => {
            const minimalData = {
                email: "<EMAIL>",
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaignLead.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaignLead.create.mockResolvedValue({
                ...mockCreatedLead,
                first_name: null,
                last_name: null,
                utm_source: null,
                utm_medium: null,
                utm_campaign: null,
            });
            mockPrisma.marketingCampaign.update.mockResolvedValue({
                ...mockCampaign,
                leads_count: 51,
            });

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: minimalData
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);
        });

        it("should use default success message when campaign has none", async () => {
            const campaignWithoutMessage = {
                ...mockCampaign,
                success_message: null,
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(campaignWithoutMessage);
            mockPrisma.marketingCampaignLead.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaignLead.create.mockResolvedValue(mockCreatedLead);
            mockPrisma.marketingCampaign.update.mockResolvedValue({
                ...campaignWithoutMessage,
                leads_count: 51,
            });

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.message).toBe("Thank you! Check your email for your discount code.");
        });

        it("should use custom coupon code as base when campaign has one", async () => {
            const campaignWithCustomCode = {
                ...mockCampaign,
                coupon_code: "SUMMER2024",
            };

            const leadWithCustomCode = {
                ...mockCreatedLead,
                coupon_code: "SUMMER2024-ABC123",
            };

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(campaignWithCustomCode);
            mockPrisma.marketingCampaignLead.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaignLead.create.mockResolvedValue(leadWithCustomCode);
            mockPrisma.marketingCampaign.update.mockResolvedValue({
                ...campaignWithCustomCode,
                leads_count: 51,
            });

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.coupon_code).toBe("SUMMER2024-ABC123");

            // Should create lead with custom coupon code as base plus unique suffix
            expect(mockPrisma.marketingCampaignLead.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    coupon_code: "SUMMER2024-ABC123",
                }),
            });
        });

        it("should auto-generate coupon code when campaign has none", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaignLead.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaignLead.create.mockResolvedValue(mockCreatedLead);
            mockPrisma.marketingCampaign.update.mockResolvedValue({
                ...mockCampaign,
                leads_count: 51,
            });

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.coupon_code).toBe("TEST-CAMPAIGN-ABC123");

            // Should create lead with auto-generated coupon code
            expect(mockPrisma.marketingCampaignLead.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    coupon_code: "TEST-CAMPAIGN-ABC123",
                }),
            });
        });

        it("should sync with newsletter service with correct tags", async () => {
            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaignLead.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaignLead.create.mockResolvedValue(mockCreatedLead);
            mockPrisma.marketingCampaign.update.mockResolvedValue({
                ...mockCampaign,
                leads_count: 51,
            });

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData
            });

            const response = await POST(req, { params: { slug: mockSlug } });

            expect(response.status).toBe(200);
            expect(EmailNewsletterService.syncNewsletterSubscriber).toHaveBeenCalledWith({
                email: mockLeadData.email,
                first_name: mockLeadData.first_name,
                last_name: mockLeadData.last_name,
                user: null,
                tags: [
                    "consumer action: requested partner coupon code",
                    `source: ${mockCampaign.title}`
                ]
            });
        });

        it("should continue processing if newsletter sync fails", async () => {
            (EmailNewsletterService.syncNewsletterSubscriber as jest.Mock).mockRejectedValue(
                new Error("Newsletter sync failed")
            );

            mockPrisma.marketingCampaign.findUnique.mockResolvedValue(mockCampaign);
            mockPrisma.marketingCampaignLead.findFirst.mockResolvedValue(null);
            mockPrisma.marketingCampaignLead.create.mockResolvedValue(mockCreatedLead);
            mockPrisma.marketingCampaign.update.mockResolvedValue({
                ...mockCampaign,
                leads_count: 51,
            });

            const req = createMockRequest({
                method: "POST",
                url: `/api/marketing-campaigns/${mockSlug}`,
                params: { slug: mockSlug },
                validatedData: mockLeadData
            });

            const response = await POST(req, { params: { slug: mockSlug } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);
            expect(EmailNewsletterService.syncNewsletterSubscriber).toHaveBeenCalled();
        });
    });
}); 