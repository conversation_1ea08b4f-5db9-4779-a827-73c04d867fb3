import { prisma as mockPrisma } from "../../../../mocks/prisma-mock";
import { createMockRequest, mockUser } from "../../../../utils/api-test-utils";

// Mock the services before importing the route handlers
jest.mock("@/lib/services/messaging.service", () => ({
	MessageService: {
		getQuoteMessages: jest.fn(),
		createQuoteMessage: jest.fn(),
		createProviderQuoteMessage: jest.fn()
	}
}));

jest.mock("@/lib/services/listing-access.service", () => ({
	hasListingAccess: jest.fn()
}));

// Import after mocks are set up
import { GET, POST } from "../../../../../app/api/conversations/[id]/route";
import { hasListingAccess } from "../../../../../lib/services/listing-access.service";
import { MessageService } from "../../../../../lib/services/messaging.service";

describe("Conversation Messages API", () => {
	const mockQuoteId = "quote123";
	const mockProviderId = "provider123";
	const mockListingId = "listing123";
	const mockJobId = "job123";

	const mockQuote = {
		id: mockQuoteId,
		job_id: mockJobId,
		listing_id: mockListingId,
		status: "PROVIDER_INVITED" as const,
		invited_at: new Date(),
		responded_at: null,
		accepted_at: null,
		reviewed_at: null,
		created_at: new Date(),
		updated_at: new Date(),
		job: {
			user: {
				id: mockUser.id
			}
		},
		listing: {
			id: mockListingId
		}
	} as any;

	const mockJob = {
		id: mockJobId,
		category: "Electrical",
		message: "Need help with electrical issues",
		rv_year: "2020",
		rv_make: "Airstream",
		rv_model: "Classic",
		rv_type: "Travel Trailer",
		status: "OPEN",
		warranty_request_id: null,
		accepted_quote_id: null,
		warranty_request: null,
		accepted_quote: null
	} as any;

	const mockMessages = [
		{
			id: "message1",
			quote_id: mockQuoteId,
			content: "Test message 1",
			status: "SENT" as const,
			type: "TEXT" as const,
			attachments: [],
			metadata: {},
			created_at: new Date("2023-01-01T00:00:00.000Z"),
			sent_at: new Date("2023-01-01T00:00:00.000Z"),
			delivered_at: null,
			read_at: null,
			sender_id: mockUser.id,
			sender_type: "USER" as const,
			recipient_id: mockProviderId,
			recipient_type: "PROVIDER" as const,
			quote: {
				job: {
					user: {
						id: mockUser.id,
						first_name: "Test",
						last_name: "User",
						email: mockUser.email,
						avatar: null
					}
				},
				listing: {
					id: mockListingId,
					business_name: "Test Business",
					first_name: "Test",
					last_name: "Provider",
					email: "<EMAIL>",
					profile_image: null
				}
			}
		}
	];

	const mockUserMessage = {
		id: "user-message123",
		quote_id: mockQuoteId,
		content: "New user message",
		status: "SENT" as const,
		type: "TEXT" as const,
		attachments: [],
		metadata: {},
		created_at: "2023-01-03T00:00:00.000Z",
		sent_at: "2023-01-03T00:00:00.000Z",
		delivered_at: null,
		read_at: null,
		sender_id: mockUser.id,
		sender_type: "USER" as const,
		recipient_id: mockProviderId,
		recipient_type: "PROVIDER" as const,
		quote: {
			job: {
				user: {
					id: mockUser.id,
					first_name: "Test",
					last_name: "User",
					email: mockUser.email,
					avatar: null
				}
			},
			listing: {
				id: mockListingId,
				business_name: "Test Business",
				first_name: "Test",
				last_name: "Provider",
				email: "<EMAIL>",
				profile_image: null
			}
		}
	};

	const mockProviderMessage = {
		id: "provider-message123",
		quote_id: mockQuoteId,
		content: "New provider message",
		status: "SENT" as const,
		type: "TEXT" as const,
		attachments: [],
		metadata: {},
		created_at: "2023-01-03T00:00:00.000Z",
		sent_at: "2023-01-03T00:00:00.000Z",
		delivered_at: null,
		read_at: null,
		sender_id: mockProviderId,
		sender_type: "PROVIDER" as const,
		recipient_id: mockUser.id,
		recipient_type: "USER" as const,
		quote: {
			job: {
				user: {
					id: mockUser.id,
					first_name: "Test",
					last_name: "User",
					email: mockUser.email,
					avatar: null
				}
			},
			listing: {
				id: mockListingId,
				business_name: "Test Business",
				first_name: "Test",
				last_name: "Provider",
				email: "<EMAIL>",
				profile_image: null
			}
		}
	};

	beforeEach(() => {
		jest.clearAllMocks();
		// Setup default mocks - these need to be set after jest.clearAllMocks()
		mockPrisma.job.findFirst.mockResolvedValue(mockJob);
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote);
		mockPrisma.quoteMessage.findMany.mockResolvedValue(mockMessages);
		mockPrisma.listing.findUnique.mockResolvedValue({
			id: mockListingId,
			business_name: "Test Business",
			first_name: "Test",
			last_name: "Provider",
			email: "<EMAIL>",
			profile_image: null
		} as any);
		(hasListingAccess as jest.Mock).mockResolvedValue(true);
		(MessageService.getQuoteMessages as jest.Mock).mockResolvedValue(mockMessages);
		(MessageService.createQuoteMessage as jest.Mock).mockResolvedValue(mockUserMessage);
		(MessageService.createProviderQuoteMessage as jest.Mock).mockResolvedValue(mockProviderMessage);
	});

	describe("GET /api/conversations/[id]", () => {
		it("should return messages, job, and quote data for a quote", async () => {
			(MessageService.getQuoteMessages as jest.Mock).mockResolvedValue(
				mockMessages
			);

			const req = createMockRequest({
				method: "GET",
				url: `/api/conversations/${mockQuoteId}`,
				params: { id: mockQuoteId }
			});

			const response = await GET(req, { params: { id: mockQuoteId } });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toHaveProperty("messages");
			expect(data).toHaveProperty("job");
			expect(data).toHaveProperty("quote");

			// Verify job data structure
			expect(data.job).toHaveProperty("id", mockJobId);
			expect(data.job).toHaveProperty("category", "Electrical");
			expect(data.job).toHaveProperty("message", "Need help with electrical issues");
			expect(data.job).toHaveProperty("rv_year", "2020");
			expect(data.job).toHaveProperty("rv_make", "Airstream");
			expect(data.job).toHaveProperty("rv_model", "Classic");
			expect(data.job).toHaveProperty("rv_type", "Travel Trailer");
			expect(data.job).toHaveProperty("status", "OPEN");
			expect(data.job).toHaveProperty("warranty_request_id", null);
			expect(data.job).toHaveProperty("accepted_quote_id", null);

			// Verify quote data structure
			expect(data.quote).toHaveProperty("id", mockQuoteId);
			expect(data.quote).toHaveProperty("status", "PROVIDER_INVITED");
			expect(data.quote).toHaveProperty("listing");
		});

		it("should handle warranty jobs correctly", async () => {
			const warrantyJob = {
				...mockJob,
				warranty_request_id: "warranty123",
				warranty_request: {
					id: "warranty123",
					company: {
						name: "Test Company"
					}
				}
			};

			mockPrisma.job.findFirst.mockResolvedValue(warrantyJob);
			(MessageService.getQuoteMessages as jest.Mock).mockResolvedValue(
				mockMessages
			);

			const req = createMockRequest({
				method: "GET",
				url: `/api/conversations/${mockQuoteId}`,
				params: { id: mockQuoteId }
			});

			const response = await GET(req, { params: { id: mockQuoteId } });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data.job).toHaveProperty("warranty_request_id", "warranty123");
			expect(data.job.warranty_request).toHaveProperty("id", "warranty123");
			expect(data.job.warranty_request.company).toHaveProperty("name", "Test Company");
		});

		it("should handle accepted jobs correctly", async () => {
			const acceptedJob = {
				...mockJob,
				accepted_quote_id: "accepted123",
				accepted_quote: {
					id: "accepted123",
					listing: {
						id: "listing456",
						business_name: "Accepted Business",
						first_name: "Accepted",
						last_name: "Provider"
					}
				}
			};

			mockPrisma.job.findFirst.mockResolvedValue(acceptedJob);
			(MessageService.getQuoteMessages as jest.Mock).mockResolvedValue(
				mockMessages
			);

			const req = createMockRequest({
				method: "GET",
				url: `/api/conversations/${mockQuoteId}`,
				params: { id: mockQuoteId }
			});

			const response = await GET(req, { params: { id: mockQuoteId } });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data.job).toHaveProperty("accepted_quote_id", "accepted123");
			expect(data.job.accepted_quote).toHaveProperty("id", "accepted123");
			expect(data.job.accepted_quote.listing).toHaveProperty("business_name", "Accepted Business");
		});

		it("should handle service errors", async () => {
			(MessageService.getQuoteMessages as jest.Mock).mockRejectedValue(
				new Error("Database error")
			);

			const req = createMockRequest({
				method: "GET",
				url: `/api/conversations/${mockQuoteId}`,
				params: { id: mockQuoteId }
			});

			const response = await GET(req, { params: { id: mockQuoteId } });
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data).toEqual({ error: "Internal server error" });
		});
	});

	describe("POST /api/conversations/[id]", () => {
		describe("User Messages", () => {
			it("should create user message when userId is provided", async () => {
				(MessageService.createQuoteMessage as jest.Mock).mockResolvedValue(
					mockUserMessage
				);

				const req = createMockRequest({
					method: "POST",
					url: `/api/conversations/${mockQuoteId}`,
					params: { id: mockQuoteId },
					validatedData: {
						content: "New user message",
						userId: mockUser.id
					}
				});

				const response = await POST(req, { params: { id: mockQuoteId } });
				const data = await response.json();

				expect(MessageService.createQuoteMessage).toHaveBeenCalledWith({
					quoteId: mockQuoteId,
					userId: mockUser.id,
					content: "New user message"
				});
				expect(response.status).toBe(200);
				expect(data).toEqual({ message: mockUserMessage });
			});

			it("should reject when userId doesn't match job owner", async () => {
				const req = createMockRequest({
					method: "POST",
					url: `/api/conversations/${mockQuoteId}`,
					params: { id: mockQuoteId },
					validatedData: {
						content: "New user message",
						userId: "different-user-id"
					}
				});

				const response = await POST(req, { params: { id: mockQuoteId } });
				const data = await response.json();

				expect(response.status).toBe(403);
				expect(data).toEqual({ error: "User ID does not match job owner" });
			});

			it("should reject when current user is not the job owner", async () => {
				const req = createMockRequest({
					method: "POST",
					url: `/api/conversations/${mockQuoteId}`,
					params: { id: mockQuoteId },
					validatedData: {
						content: "New user message",
						userId: "different-user-id"
					}
				});

				const response = await POST(req, { params: { id: mockQuoteId } });
				const data = await response.json();

				expect(response.status).toBe(403);
				expect(data).toEqual({ error: "User ID does not match job owner" });
			});
		});

		describe("Provider Messages", () => {
			it("should create provider message when providerId is provided", async () => {
				(
					MessageService.createProviderQuoteMessage as jest.Mock
				).mockResolvedValue(mockProviderMessage);

				const req = createMockRequest({
					method: "POST",
					url: `/api/conversations/${mockQuoteId}`,
					params: { id: mockQuoteId },
					validatedData: {
						content: "New provider message",
						providerId: mockListingId
					}
				});

				const response = await POST(req, { params: { id: mockQuoteId } });
				const data = await response.json();

				expect(
					MessageService.createProviderQuoteMessage
				).toHaveBeenCalledWith({
					quoteId: mockQuoteId,
					listingId: mockListingId,
					content: "New provider message"
				});
				expect(response.status).toBe(200);
				expect(data).toEqual({ message: mockProviderMessage });
			});

			it("should reject when providerId doesn't match quote listing", async () => {
				const req = createMockRequest({
					method: "POST",
					url: `/api/conversations/${mockQuoteId}`,
					params: { id: mockQuoteId },
					validatedData: {
						content: "New provider message",
						providerId: "different-listing-id"
					}
				});

				const response = await POST(req, { params: { id: mockQuoteId } });
				const data = await response.json();

				expect(response.status).toBe(403);
				expect(data).toEqual({
					error: "Provider ID does not match quote listing"
				});
			});

			it("should reject when user doesn't have access to listing", async () => {
				(hasListingAccess as jest.Mock).mockResolvedValue(false);

				const req = createMockRequest({
					method: "POST",
					url: `/api/conversations/${mockQuoteId}`,
					params: { id: mockQuoteId },
					validatedData: {
						content: "New provider message",
						providerId: mockListingId
					}
				});

				const response = await POST(req, { params: { id: mockQuoteId } });
				const data = await response.json();

				expect(response.status).toBe(403);
				expect(data).toEqual({
					error: "You don't have access to this listing"
				});
			});
		});

		describe("Validation", () => {
			it("should handle service errors", async () => {
				(MessageService.createQuoteMessage as jest.Mock).mockRejectedValue(
					new Error("Database error")
				);

				const req = createMockRequest({
					method: "POST",
					url: `/api/conversations/${mockQuoteId}`,
					params: { id: mockQuoteId },
					validatedData: {
						content: "Test message",
						userId: mockUser.id
					}
				});

				const response = await POST(req, { params: { id: mockQuoteId } });
				const data = await response.json();

				expect(response.status).toBe(500);
				expect(data).toEqual({ error: "Internal server error" });
			});
		});
	});
});

