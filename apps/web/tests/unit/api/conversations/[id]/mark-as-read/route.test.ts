import { createMockRequest, mockUser } from "@/tests/utils/api-test-utils";

import { mockPrisma } from "../../../../../mocks/prisma-mock";


import { POST } from "@/app/api/conversations/[id]/mark-as-read/route";

// Mock the impersonation utility
jest.mock("@/lib/utils/impersonation", () => ({
	shouldAllowMarkAsRead: jest.fn()
}));

describe("Mark Messages As Read API", () => {
	const mockQuoteId = "quote123";

	beforeEach(() => {
		jest.clearAllMocks();

		// Mock the quote lookup
		mockPrisma.quote.findUnique.mockResolvedValue({
			id: mockQuoteId,
			job: {
				user: mockUser
			},
			listing: {
				id: "listing123"
			}
		});

		// Mock the listing service
		jest.doMock("@/lib/services/listing.service", () => ({
			ListingService: {
				getListingByUserId: jest.fn().mockResolvedValue(null)
			}
		}));

		// Default mock for shouldAllowMarkAsRead to return true
		const { shouldAllowMarkAsRead } = require("@/lib/utils/impersonation");
		shouldAllowMarkAsRead.mockResolvedValue(true);
	});

	describe("POST /api/conversations/[id]/mark-as-read", () => {
		it("should mark provider messages as read and return success", async () => {
			const mockUpdateResult = { count: 3 };
			mockPrisma.quoteMessage.updateMany.mockResolvedValue(mockUpdateResult);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId }
			});

			const response = await POST(req);
			const data = await response.json();

			expect(mockPrisma.quoteMessage.updateMany).toHaveBeenCalledWith({
				where: {
					quote_id: mockQuoteId,
					sender_type: "PROVIDER",
					read_at: null
				},
				data: {
					read_at: expect.any(Date)
				}
			});

			expect(response.status).toBe(200);
			expect(data).toEqual({
				success: true,
				updated: mockUpdateResult.count,
				context: "customer"
			});
		});

		it("should block mark-as-read when impersonating", async () => {
			const { shouldAllowMarkAsRead } = require("@/lib/utils/impersonation");
			shouldAllowMarkAsRead.mockResolvedValue(false);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId }
			});

			const response = await POST(req);
			const data = await response.json();

			expect(response.status).toBe(403);
			expect(data).toEqual({
				error: "Mark-as-read not allowed during impersonation"
			});

			// Verify that no database update was attempted
			expect(mockPrisma.quoteMessage.updateMany).not.toHaveBeenCalled();
		});

		it("should handle database errors", async () => {
			const mockError = new Error("Database connection failed");
			mockPrisma.quoteMessage.updateMany.mockRejectedValue(mockError);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId }
			});

			const response = await POST(req);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data).toEqual({
				error: "Internal server error",
				details: mockError.message
			});
		});

		it("should handle non-error exceptions", async () => {
			const mockException = "Something went wrong";
			mockPrisma.quoteMessage.updateMany.mockRejectedValue(mockException);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId }
			});

			const response = await POST(req);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data).toEqual({
				error: "Internal server error",
				details: mockException
			});
		});

		it("should return zero count when no messages are updated", async () => {
			const mockUpdateResult = { count: 0 };
			mockPrisma.quoteMessage.updateMany.mockResolvedValue(mockUpdateResult);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId }
			});

			const response = await POST(req);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual({
				success: true,
				updated: 0,
				context: "customer"
			});
		});
	});
});
