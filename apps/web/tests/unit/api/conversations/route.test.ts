import { GET } from "@/app/api/conversations/route";
import { ListingService } from "@/lib/services/listing.service";
import { MessageService } from "@/lib/services/messaging.service";
import {
	createMockRequest,
	mockProviderUser,
	mockUser
} from "@/tests/utils/api-test-utils";

// Mock the services
jest.mock("@/lib/services/messaging.service", () => ({
	MessageService: {
		getConversations: jest.fn()
	}
}));

jest.mock("@/lib/services/listing.service", () => ({
	ListingService: {
		getListingByUserId: jest.fn()
	}
}));

describe("Conversations API", () => {
	const mockConversations = {
		conversations: [
			{
				quote_id: "quote123",
				job: {
					id: "job123",
					category: "electrical",
					message: "Need help with electrical",
					location: { city: "Test City" },
					rv_details: {
						make: "Test Make",
						model: "Test Model",
						year: "2020",
						type: "Travel Trailer"
					},
					created_at: "2023-01-01T00:00:00.000Z",
					status: "active"
				},
				customer: {
					id: mockUser.id,
					name: "Test User",
					email: mockUser.email,
					phone: null
				},
				quote_status: "pending",
				messages: [],
				last_message_at: "2023-01-01T00:00:00.000Z",
				unread_count: 0
			}
		],
		pagination: {
			total: 1,
			offset: 0,
			limit: 50,
			hasMore: false
		}
	};

	const mockListing = {
		id: "listing123",
		user_id: mockProviderUser.id,
		business_name: "Test Business",
		first_name: "Test",
		last_name: "Provider",
		email: "<EMAIL>"
	};

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("GET /api/conversations", () => {
		it("should get user conversations when context is user", async () => {
			(MessageService.getConversations as jest.Mock).mockResolvedValue(
				mockConversations
			);

			const req = createMockRequest({
				method: "GET",
				url: "/api/conversations?context=user"
			});

			const response = await GET(req);
			const data = await response.json();

			expect(MessageService.getConversations).toHaveBeenCalledWith({
				providerId: null,
				userId: mockUser.id,
				limit: 50,
				offset: 0
			});

			expect(response.status).toBe(200);
			expect(data).toEqual(mockConversations);
		});

		it("should get provider conversations when context is provider", async () => {
			(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(
				mockListing
			);
			(MessageService.getConversations as jest.Mock).mockResolvedValue(
				mockConversations
			);

			const req = createMockRequest({
				method: "GET",
				url: "/api/conversations?context=provider"
			});

			const response = await GET(req);
			const data = await response.json();

			expect(ListingService.getListingByUserId).toHaveBeenCalledWith(
				mockUser.id
			);
			expect(MessageService.getConversations).toHaveBeenCalledWith({
				providerId: mockListing.id,
				userId: null,
				limit: 50,
				offset: 0
			});

			expect(response.status).toBe(200);
			expect(data).toEqual(mockConversations);
		});

		it("should default to user context when no context provided", async () => {
			(MessageService.getConversations as jest.Mock).mockResolvedValue(
				mockConversations
			);

			const req = createMockRequest({
				method: "GET",
				url: "/api/conversations"
			});

			const response = await GET(req);
			const data = await response.json();

			expect(MessageService.getConversations).toHaveBeenCalledWith({
				providerId: null,
				userId: mockUser.id,
				limit: 50,
				offset: 0
			});

			expect(response.status).toBe(200);
			expect(data).toEqual(mockConversations);
		});

		it("should handle service errors", async () => {
			(MessageService.getConversations as jest.Mock).mockRejectedValue(
				new Error("Database error")
			);

			const req = createMockRequest({
				method: "GET",
				url: "/api/conversations"
			});

			const response = await GET(req);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data).toEqual({
				error: "Failed to fetch conversations"
			});
		});
	});
});
