// Mock the email service before importing anything else
jest.mock("@/lib/services", () => ({
  emailService: {
    send: jest.fn(),
  },
}));

// Import the route handler after mocking dependencies
import { POST } from "@/app/api/emergency-dispatch/route";
import { emailService } from "@/lib/services";
import {
  createMockRequest
} from "@/tests/utils/api-test-utils";

describe("POST /api/emergency-dispatch", () => {
  const validEmergencyData = {
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "************",
    location: {
      address: "123 Main St, Austin, TX 78701",
      latitude: 30.2672,
      longitude: -97.7431
    },
    currentLocation: "RV Park A",
    emergencyDescription: "Complete electrical system failure - no power to any house systems including lights, outlets, and refrigerator. Need immediate assistance.",
    uploadedFiles: [
      {
        name: "electrical-panel.jpg",
        size: "2.5MB",
        type: "image/jpeg",
        url: "https://example.com/uploads/electrical-panel.jpg"
      }
    ]
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Successful emergency dispatch submission", () => {
    it("should submit emergency dispatch and send emails successfully", async () => {
      (emailService.send as jest.Mock).mockResolvedValue(undefined);

      const req = createMockRequest({
        method: "POST",
        url: "/api/emergency-dispatch",
        body: validEmergencyData,
        validatedData: validEmergencyData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty("success", true);
      expect(data).toHaveProperty("requestId");
      expect(data.requestId).toMatch(/^EMR-\d+-[a-z0-9]+$/);
      expect(data).toHaveProperty("message", "Emergency dispatch request submitted successfully");

      // Verify support team email was sent
      expect(emailService.send).toHaveBeenCalledWith({
        from: "RVHelp Emergency Dispatch <<EMAIL>>",
        to: "<EMAIL>",
        subject: `EMERGENCY DISPATCH: ${validEmergencyData.firstName} ${validEmergencyData.lastName} - ${validEmergencyData.location.address}`,
        react: expect.any(Object)
      });

      // Verify customer confirmation email was sent
      expect(emailService.send).toHaveBeenCalledWith({
        to: validEmergencyData.email,
        subject: "Emergency Dispatch Request Submitted - RV Help",
        react: expect.any(Object)
      });

      expect(emailService.send).toHaveBeenCalledTimes(2);
    });

    it("should handle emergency dispatch without optional fields", async () => {
      (emailService.send as jest.Mock).mockResolvedValue(undefined);

      const minimalData = {
        firstName: "Jane",
        lastName: "Smith",
        email: "<EMAIL>",
        phone: "************",
        location: {
          address: "456 Oak Ave, Houston, TX 77001",
          latitude: 29.7604,
          longitude: -95.3698
        },
        emergencyDescription: "AC failure in extreme heat - temperature inside RV is over 100 degrees"
      };

      const req = createMockRequest({
        method: "POST",
        url: "/api/emergency-dispatch",
        body: minimalData,
        validatedData: minimalData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty("success", true);
      expect(data).toHaveProperty("requestId");
      expect(emailService.send).toHaveBeenCalledTimes(2);
    });
  });


  describe("Error handling", () => {
    it("should handle email service errors gracefully", async () => {
      (emailService.send as jest.Mock).mockRejectedValue(new Error("Email service failed"));

      const req = createMockRequest({
        method: "POST",
        url: "/api/emergency-dispatch",
        body: validEmergencyData,
        validatedData: validEmergencyData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toHaveProperty("success", false);
      expect(data).toHaveProperty("error", "Failed to process emergency dispatch request");
    });

    it("should handle partial email failures", async () => {
      // Mock the first email (support team) to succeed, but second (customer) to fail
      (emailService.send as jest.Mock)
        .mockResolvedValueOnce(undefined) // Support team email succeeds
        .mockRejectedValueOnce(new Error("Customer email failed")); // Customer email fails

      const req = createMockRequest({
        method: "POST",
        url: "/api/emergency-dispatch",
        body: validEmergencyData,
        validatedData: validEmergencyData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toHaveProperty("success", false);
      expect(data).toHaveProperty("error", "Failed to process emergency dispatch request");

      // Verify support email was attempted
      expect(emailService.send).toHaveBeenCalledTimes(2);
    });
  });

  describe("Request ID generation", () => {
    it("should generate unique request IDs", async () => {
      (emailService.send as jest.Mock).mockResolvedValue(undefined);

      const req1 = createMockRequest({
        method: "POST",
        url: "/api/emergency-dispatch",
        body: validEmergencyData,
        validatedData: validEmergencyData
      });

      const req2 = createMockRequest({
        method: "POST",
        url: "/api/emergency-dispatch",
        body: validEmergencyData,
        validatedData: validEmergencyData
      });

      const response1 = await POST(req1);
      const data1 = await response1.json();

      // Small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 1));

      const response2 = await POST(req2);
      const data2 = await response2.json();

      expect(data1.requestId).not.toBe(data2.requestId);
      expect(data1.requestId).toMatch(/^EMR-\d+-[a-z0-9]+$/);
      expect(data2.requestId).toMatch(/^EMR-\d+-[a-z0-9]+$/);
    });
  });

  describe("Email content verification", () => {
    it("should send emails with correct emergency information", async () => {
      (emailService.send as jest.Mock).mockResolvedValue(undefined);

      const req = createMockRequest({
        method: "POST",
        url: "/api/emergency-dispatch",
        body: validEmergencyData,
        validatedData: validEmergencyData
      });

      const response = await POST(req);
      await response.json();

      // Verify support team email call
      const supportEmailCall = (emailService.send as jest.Mock).mock.calls[0][0];
      expect(supportEmailCall.from).toBe("RVHelp Emergency Dispatch <<EMAIL>>");
      expect(supportEmailCall.to).toBe("<EMAIL>");
      expect(supportEmailCall.subject).toContain("EMERGENCY DISPATCH");
      expect(supportEmailCall.subject).toContain(validEmergencyData.firstName);
      expect(supportEmailCall.subject).toContain(validEmergencyData.lastName);
      expect(supportEmailCall.subject).toContain(validEmergencyData.location.address);

      // Verify customer confirmation email call
      const customerEmailCall = (emailService.send as jest.Mock).mock.calls[1][0];
      expect(customerEmailCall.to).toBe(validEmergencyData.email);
      expect(customerEmailCall.subject).toBe("Emergency Dispatch Request Submitted - RV Help");
    });

    it("should handle uploaded files in email content", async () => {
      (emailService.send as jest.Mock).mockResolvedValue(undefined);

      const dataWithMultipleFiles = {
        ...validEmergencyData,
        uploadedFiles: [
          {
            name: "electrical-panel.jpg",
            size: "2.5MB",
            type: "image/jpeg",
            url: "https://example.com/uploads/electrical-panel.jpg"
          },
          {
            name: "fuse-box.jpg",
            size: "1.8MB",
            type: "image/jpeg",
            url: "https://example.com/uploads/fuse-box.jpg"
          }
        ]
      };

      const req = createMockRequest({
        method: "POST",
        url: "/api/emergency-dispatch",
        body: dataWithMultipleFiles,
        validatedData: dataWithMultipleFiles
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty("success", true);
      expect(emailService.send).toHaveBeenCalledTimes(2);
    });
  });
}); 