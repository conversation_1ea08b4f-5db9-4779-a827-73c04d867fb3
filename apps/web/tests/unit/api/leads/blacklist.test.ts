
import { POST } from "@/app/api/jobs/route";
import { BlacklistService } from "@/lib/services/blacklist.service";
import { JobService } from "@/lib/services/job.service";

import { prisma as mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest
} from "@/tests/utils/api-test-utils";

// Mock the BlacklistService
jest.mock("@/lib/services/blacklist.service");
jest.mock("@/lib/queue/qstash", () => ({
    queueMessage: jest.fn()
}));

// Mock the JobService
jest.mock("@/lib/services/job.service", () => ({
    JobService: {
        createJob: jest.fn(),
        getJobsForUser: jest.fn(),
        getExistingJobsByEmail: jest.fn(),
        getMostRecentActiveJobByEmail: jest.fn(),
    },
}));

// Get the mocked functions
const mockJobService = jest.mocked(JobService);

describe("Leads API - Blacklist Integration", () => {
    const mockLeadData = {
        listing_id: "test-listing-id",
        first_name: "<PERSON>",
        last_name: "Doe",
        email: "<EMAIL>",
        contact_phone: "+1234567890",
        contact_preference: "phone" as const,
        category: "rv-repair",
        location: {
            address: "123 Test Street, Test City, TS 12345",
            latitude: 40.7128,
            longitude: -74.0060,
            city: "Test City",
            state: "TS",
            zip: "12345",
            country: "USA"
        },
        message: "This is a test message that is long enough to meet the minimum character requirement for the validation schema."
    };

    const mockListing = {
        id: "test-listing-id",
        business_name: "Test Business",
        location: {
            latitude: 40.7128,
            longitude: -74.0060
        }
    };

    beforeEach(() => {
        jest.clearAllMocks();

        // Mock Prisma listing lookup by default
        (mockPrisma.listing.findUnique as jest.Mock).mockResolvedValue(mockListing);

        // Mock user upsert
        (mockPrisma.user.upsert as jest.Mock).mockResolvedValue({
            id: "user123",
            email: "<EMAIL>",
            first_name: "John",
            last_name: "Doe"
        });

        // Mock job creation
        (mockPrisma.job.create as jest.Mock).mockResolvedValue({
            id: "job123",
            quotes: []
        });

        // Mock no existing jobs by default
        mockJobService.getExistingJobsByEmail.mockResolvedValue({
            existingJobs: [],
            userStatus: {
                userExists: false,
                hasPassword: false,
                needsPasswordSetup: true,
                email: "<EMAIL>",
                firstName: null,
                lastName: null
            }
        });
        mockJobService.getMostRecentActiveJobByEmail.mockResolvedValue(null);

        // Mock successful job creation by default
        mockJobService.createJob.mockResolvedValue({
            success: true,
            message: "Lead submission received and will be processed shortly",
            job: { id: "job123" }
        });
    });

    describe("POST /api/jobs", () => {
        it("should block lead submission from blacklisted email", async () => {
            // Mock blacklist service to return blacklisted
            (BlacklistService.checkEmailAccess as jest.Mock).mockResolvedValue({
                isBlacklisted: true,
                message: "This domain is blacklisted"
            });

            // Mock JobService to return blacklist rejection
            mockJobService.createJob.mockResolvedValue({
                success: false,
                error: "Lead submission blocked",
                message: "This domain is blacklisted"
            });

            const req = createMockRequest({
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: mockLeadData,
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(403);
            expect(data.success).toBe(false);
            expect(data.error).toBe("Lead submission blocked");
            expect(data.message).toBe("This domain is blacklisted");
        });

        it("should allow lead submission from non-blacklisted email", async () => {
            // Mock blacklist service to return not blacklisted
            (BlacklistService.checkEmailAccess as jest.Mock).mockResolvedValue({
                isBlacklisted: false
            });

            const req = createMockRequest({
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: mockLeadData,
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);
        });

        it("should check email domain for blacklisting", async () => {
            // Mock blacklist service to return blacklisted for domain
            (BlacklistService.checkEmailAccess as jest.Mock).mockResolvedValue({
                isBlacklisted: true,
                message: "Domain is blacklisted"
            });

            // Mock JobService to return blacklist rejection
            mockJobService.createJob.mockResolvedValue({
                success: false,
                error: "Lead submission blocked",
                message: "Domain is blacklisted"
            });

            const req = createMockRequest({
                method: "POST",
                url: "http://localhost:3000/api/jobs",
                headers: { "Content-Type": "application/json" },
                body: {
                    ...mockLeadData,
                    email: "<EMAIL>"
                }
            });

            const response = await POST(req);
            const data = await response.json();

            expect(response.status).toBe(403);
            expect(data.message).toBe("Domain is blacklisted");
        });
    });
}); 