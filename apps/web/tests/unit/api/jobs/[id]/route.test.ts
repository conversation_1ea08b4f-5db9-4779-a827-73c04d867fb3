import { prisma as mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "@/tests/utils/api-test-utils";

import { GET } from "@/app/api/jobs/[id]/route";

describe("GET /api/jobs/[id]", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Reset mockBaseHandler to authenticated state
        mockBaseHandler.user = mockUser;
        mockBaseHandler.isAuthenticated = true;
        mockBaseHandler.session = { user: mockUser };

        // Override the respond mock to include headers for this specific test
        mockBaseHandler.respond.mockImplementation((data: any, status: number = 200) => {
            const mockHeaders = new Map();
            return {
                json: () => Promise.resolve(data),
                status,
                headers: {
                    set: (key: string, value: string) => mockHeaders.set(key, value),
                    get: (key: string) => mockHeaders.get(key),
                    has: (key: string) => mockHeaders.has(key),
                    delete: (key: string) => mockHeaders.delete(key)
                }
            };
        });
    });

    const mockJob = {
        id: "job123",
        user_id: "user123",
        message: "My RV needs repair",
        category: "rv-repair",
        location: { lat: 40.7128, lng: -74.0060 },
        status: "OPEN",
        created_at: new Date("2023-01-01"),
        updated_at: new Date("2023-01-01"),
        email: "<EMAIL>",
        rv_year: "2020",
        rv_make: "Ford",
        rv_model: "E-450",
        rv_type: "Motorhome",
        quotes: [],
        user: {
            id: "user123",
            email: "<EMAIL>",
            first_name: "John",
            last_name: "Doe"
        },
        timeline_updates: [],
        warranty_request: null,
        accepted_quote: null
    };

    it("should return 404 when job not found", async () => {
        mockPrisma.job.findUnique.mockResolvedValue(null);

        const req = createMockRequest({
            method: "GET",
            url: "/api/jobs/job123"
        });

        const response = await GET(req, { params: { id: "job123" } });

        expect(response.status).toBe(404);
        expect(await response.json()).toEqual({
            error: "Service request not found"
        });
    });


    it("should return full job data when authenticated user accesses their own job", async () => {
        const mockJobWithQuotes = {
            ...mockJob,
            quotes: [
                {
                    id: "quote1",
                    status: "PENDING",
                    messages: [
                        { id: "msg1", read_at: null },
                        { id: "msg2", read_at: new Date() }
                    ]
                }
            ]
        };

        mockPrisma.job.findUnique.mockResolvedValue(mockJobWithQuotes);

        // Mock authenticated user who owns the job
        const ownerUser = { ...mockUser, id: "user123" };
        mockBaseHandler.user = ownerUser;
        mockBaseHandler.session = { user: ownerUser };

        const req = createMockRequest({
            method: "GET",
            url: "/api/jobs/job123"
        });

        const response = await GET(req, { params: { id: "job123" } });

        expect(response.status).toBe(200);
        const responseData = await response.json();

        expect(responseData.id).toBe("job123");
        expect(responseData.user).toBeDefined();
        expect(responseData.quotes).toHaveLength(1);
        expect(responseData.quotes[0].messages_count).toBe(2);
        expect(responseData.quotes[0].unread_messages_count).toBe(1);
    });

    it("should handle complex job data structure correctly", async () => {
        const complexJob = {
            ...mockJob,
            quotes: [
                {
                    id: "quote1",
                    status: "PENDING",
                    listing: {
                        id: "listing1",
                        business_name: "Test Business",
                        location: { lat: 40.7128, lng: -74.0060 }
                    },
                    messages: []
                }
            ],
            user: {
                id: "user123",
                email: "<EMAIL>"
            },
            timeline_updates: [
                {
                    id: "update1",
                    updated_by: { id: "user123", name: "John Doe" }
                }
            ],
            warranty_request: {
                id: "warranty1",
                company: { id: "company1", name: "Test Company" },
                component: { id: "component1", type: "Engine" },
                listing: { id: "listing1", business_name: "Test Business" }
            },
            accepted_quote: {
                id: "quote1",
                listing: {
                    id: "listing1",
                    business_name: "Test Business",
                    location: { lat: 40.7128, lng: -74.0060 }
                }
            }
        };

        mockPrisma.job.findUnique.mockResolvedValue(complexJob);

        // Mock authenticated user who owns the job
        const ownerUser = { ...mockUser, id: "user123" };
        mockBaseHandler.user = ownerUser;
        mockBaseHandler.session = { user: ownerUser };

        const req = createMockRequest({
            method: "GET",
            url: "/api/jobs/job123"
        });

        const response = await GET(req, { params: { id: "job123" } });

        expect(response.status).toBe(200);
        const responseData = await response.json();
        expect(responseData.quotes).toHaveLength(1);
        expect(responseData.user).toBeDefined();
        expect(responseData.timeline_updates).toHaveLength(1);
        expect(responseData.warranty_request).toBeDefined();
        expect(responseData.accepted_quote).toBeDefined();
    });
}); 