import { prisma as mockPrisma } from "@/tests/mocks/prisma-mock";
import {
    createMockRequest,
    mockBaseHandler,
    mockUser
} from "@/tests/utils/api-test-utils";
import { JobStatus } from "@rvhelp/database";

// Mock the QuoteStatusService
jest.mock("@/lib/services/quote-status.service", () => ({
    QuoteStatusService: {
        customerCompleteJob: jest.fn(),
        customerCancelJob: jest.fn(),
    }
}));

// Import the handler after mocks are set up
import { POST } from "@/app/api/jobs/[id]/customer-status/route";

describe("POST /api/jobs/[id]/customer-status", () => {
    const mockJobId = "job-456";
    const mockUserId = "user-123";

    const mockJob = {
        id: mockJobId,
        user_id: mockUserId,
        status: JobStatus.OPEN,
        message: "My RV needs repair",
        category: "rv-repair",
        location: { lat: 40.7128, lng: -74.0060 },
        created_at: new Date("2023-01-01"),
        updated_at: new Date("2023-01-01"),
        email: "<EMAIL>",
        rv_year: "2020",
        rv_make: "Ford",
        rv_model: "E-450",
        rv_type: "Motorhome"
    };

    const mockCompletedJob = {
        ...mockJob,
        status: JobStatus.COMPLETED,
        updated_at: new Date()
    };

    const mockCancelledJob = {
        ...mockJob,
        status: JobStatus.CANCELLED,
        updated_at: new Date()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        // Reset mockBaseHandler to authenticated state
        mockBaseHandler.user = { ...mockUser, id: mockUserId };
        mockBaseHandler.isAuthenticated = true;
        mockBaseHandler.session = { user: { ...mockUser, id: mockUserId } };

        // Default mock - job exists and belongs to user
        mockPrisma.job.findFirst.mockResolvedValue(mockJob);
    });

    describe("Complete Job", () => {
        it("should successfully complete a job with selected provider", async () => {
            const { QuoteStatusService } = await import("@/lib/services/quote-status.service");
            (QuoteStatusService.customerCompleteJob as jest.Mock).mockResolvedValue(mockCompletedJob);

            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully",
                    selectedProviderId: "provider-123"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);
            expect(data.message).toBe("Job successfully marked as completed");
            expect(data.job.status).toBe(JobStatus.COMPLETED);

            expect(QuoteStatusService.customerCompleteJob).toHaveBeenCalledWith({
                jobId: mockJobId,
                userId: mockUserId,
                reason: "Work finished successfully",
                selectedProviderId: "provider-123"
            });
        });

        it("should successfully complete a job without selected provider", async () => {
            const { QuoteStatusService } = await import("@/lib/services/quote-status.service");
            (QuoteStatusService.customerCompleteJob as jest.Mock).mockResolvedValue(mockCompletedJob);

            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED",
                    reason: "Completed work myself"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);
            expect(data.message).toBe("Job successfully marked as completed");
            expect(data.job.status).toBe(JobStatus.COMPLETED);

            expect(QuoteStatusService.customerCompleteJob).toHaveBeenCalledWith({
                jobId: mockJobId,
                userId: mockUserId,
                reason: "Completed work myself",
                selectedProviderId: undefined
            });
        });

        it("should handle QuoteStatusService errors for completion", async () => {
            const { QuoteStatusService } = await import("@/lib/services/quote-status.service");
            (QuoteStatusService.customerCompleteJob as jest.Mock).mockRejectedValue(
                new Error("Selected provider not found")
            );

            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully",
                    selectedProviderId: "non-existent-provider"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Selected provider not found");
        });
    });

    describe("Cancel Job", () => {
        it("should successfully cancel a job", async () => {
            const { QuoteStatusService } = await import("@/lib/services/quote-status.service");
            (QuoteStatusService.customerCancelJob as jest.Mock).mockResolvedValue(mockCancelledJob);

            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "CANCELLED",
                    reason: "No longer needed"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data.success).toBe(true);
            expect(data.message).toBe("Job successfully marked as cancelled");
            expect(data.job.status).toBe(JobStatus.CANCELLED);

            expect(QuoteStatusService.customerCancelJob).toHaveBeenCalledWith({
                jobId: mockJobId,
                userId: mockUserId,
                reason: "No longer needed"
            });
        });

        it("should handle QuoteStatusService errors for cancellation", async () => {
            const { QuoteStatusService } = await import("@/lib/services/quote-status.service");
            (QuoteStatusService.customerCancelJob as jest.Mock).mockRejectedValue(
                new Error("Database connection failed")
            );

            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "CANCELLED",
                    reason: "No longer needed"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(500);
            expect(data.error).toBe("Database connection failed");
        });
    });

    describe("Validation", () => {
        it("should return 400 when status is missing", async () => {
            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    reason: "Work finished successfully"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Status is required");
        });

        it("should return 400 when reason is missing", async () => {
            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Reason is required");
        });

        it("should return 400 when status is invalid", async () => {
            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "INVALID_STATUS",
                    reason: "Work finished successfully"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Invalid status. Must be 'COMPLETED' or 'CANCELLED'");
        });

        it("should return 400 when reason is empty string", async () => {
            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED",
                    reason: ""
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Reason is required");
        });

        it("should return 400 when status is null after validation", async () => {
            // Mock a case where validation passes but status becomes null/undefined
            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully"
                },
                validatedData: {
                    status: null,
                    reason: "Work finished successfully"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Status is required");
        });

        it("should return 400 when reason is null after validation", async () => {
            // Mock a case where validation passes but reason becomes null/undefined
            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED",
                    reason: ""
                },
                validatedData: {
                    status: "COMPLETED",
                    reason: null
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Reason is required");
        });
    });

    describe("Job Access Control", () => {
        it("should return 404 when job not found", async () => {
            mockPrisma.job.findFirst.mockResolvedValue(null);

            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data.error).toBe("Job not found or access denied");
        });

        it("should return 404 when user doesn't own the job", async () => {
            // Mock a job that belongs to a different user
            mockPrisma.job.findFirst.mockResolvedValue(null);

            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data.error).toBe("Job not found or access denied");

            // Verify the query included user_id filter
            expect(mockPrisma.job.findFirst).toHaveBeenCalledWith({
                where: { id: mockJobId, user_id: mockUserId }
            });
        });
    });

    describe("Job Status Validation", () => {
        it("should return 400 when job is already completed", async () => {
            mockPrisma.job.findFirst.mockResolvedValue({
                ...mockJob,
                status: JobStatus.COMPLETED
            });

            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Job is already in a final state");
        });

        it("should return 400 when job is already cancelled", async () => {
            mockPrisma.job.findFirst.mockResolvedValue({
                ...mockJob,
                status: JobStatus.CANCELLED
            });

            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "CANCELLED",
                    reason: "No longer needed"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });
            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Job is already in a final state");
        });
    });

    describe("Authentication", () => {
        it("should require authentication", async () => {
            mockBaseHandler.user = null; // Not authenticated
            mockBaseHandler.isAuthenticated = false;

            const req = createMockRequest({
                method: "POST",
                url: `/api/jobs/${mockJobId}/customer-status`,
                body: {
                    status: "COMPLETED",
                    reason: "Work finished successfully"
                }
            });

            const response = await POST(req, { params: { id: mockJobId } });

            expect(response.status).toBe(401);
        });
    });
});