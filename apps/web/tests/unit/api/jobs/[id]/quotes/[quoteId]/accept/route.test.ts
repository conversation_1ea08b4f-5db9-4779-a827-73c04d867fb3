import { POST } from "@/app/api/jobs/[id]/quotes/[quoteId]/accept/route";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { mockPrisma } from "@/tests/mocks/prisma-mock";
import { createMockRequest, mockUser } from "@/tests/utils/api-test-utils";

// Mock the QuoteStatusService
jest.mock("@/lib/services/quote-status.service", () => ({
	QuoteStatusService: {
		customerAcceptQuote: jest.fn()
	}
}));

// Mock auth
jest.mock("next-auth", () => ({
	getServerSession: jest.fn()
}));

describe("POST /api/jobs/[id]/quotes/[quoteId]/accept", () => {
	const mockJobId = "job123";
	const mockQuoteId = "quote456";
	const mockUserId = mockUser.id; // Use the same ID as the mock user

	const mockQuote = {
		id: mockQuoteId,
		listing_id: "listing123",
		job_id: mockJobId,
		status: "PROVIDER_ACCEPTED"
	};

	const mockAcceptedQuote = {
		id: mockQuoteId,
		listing_id: "listing123",
		job_id: mockJobId,
		status: "CUSTOMER_ACCEPTED",
		listing: {
			business_name: "Test RV Service",
			email: "<EMAIL>"
		},
		job: {
			id: mockJobId,
			user_id: mockUserId,
			user: {
				first_name: "John",
				last_name: "Doe",
				email: "<EMAIL>"
			}
		}
	};

	beforeEach(() => {
		jest.clearAllMocks();
	});

	it("should accept a quote successfully", async () => {
		const testDate = new Date().toISOString();
		const expectedQuote = {
			...mockAcceptedQuote,
			accepted_at: testDate
		};

		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.customerAcceptQuote as jest.Mock).mockResolvedValue(
			expectedQuote
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockJobId, quoteId: mockQuoteId },
			session: { user: { id: mockUserId } }
		});

		const response = await POST(req);

		const responseData = await response.json();

		expect(mockPrisma.quote.findUnique).toHaveBeenCalledWith({
			where: { id: mockQuoteId }
		});
		expect(QuoteStatusService.customerAcceptQuote).toHaveBeenCalledWith({
			quote: mockQuote,
			userId: mockUserId
		});

		expect(response.status).toBe(200);
		expect(responseData).toEqual(expectedQuote);
	});

	it("should handle service errors gracefully", async () => {
		const errorMessage = "Quote cannot be accepted in its current state";
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.customerAcceptQuote as jest.Mock).mockRejectedValue(
			new Error(errorMessage)
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockJobId, quoteId: mockQuoteId },
			session: { user: { id: mockUserId } }
		});

		const response = await POST(req);
		const responseData = await response.json();

		expect(response.status).toBe(500);
		expect(responseData.error).toBe("Failed to accept quote");
	});

	it("should handle quote not found", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(null);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockJobId, quoteId: mockQuoteId },
			session: { user: { id: mockUserId } }
		});

		await expect(POST(req)).rejects.toThrow("Quote not found");
		expect(QuoteStatusService.customerAcceptQuote).not.toHaveBeenCalled();
	});

	it("should handle unauthorized access", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.customerAcceptQuote as jest.Mock).mockRejectedValue(
			new Error("Job not found")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockJobId, quoteId: mockQuoteId },
			session: { user: { id: "different-user" } }
		});

		const response = await POST(req);
		const responseData = await response.json();

		expect(response.status).toBe(500);
		expect(responseData.error).toBe("Failed to accept quote");
	});

	it("should handle already accepted quote", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.customerAcceptQuote as jest.Mock).mockRejectedValue(
			new Error("Another quote has already been accepted for this job")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockJobId, quoteId: mockQuoteId },
			session: { user: { id: mockUserId } }
		});

		const response = await POST(req);
		const responseData = await response.json();

		expect(response.status).toBe(500);
		expect(responseData.error).toBe("Failed to accept quote");
	});

	it("should handle quote-job mismatch", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.customerAcceptQuote as jest.Mock).mockRejectedValue(
			new Error("Quote does not belong to this job")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockJobId, quoteId: mockQuoteId },
			session: { user: { id: mockUserId } }
		});

		const response = await POST(req);
		const responseData = await response.json();

		expect(response.status).toBe(500);
		expect(responseData.error).toBe("Failed to accept quote");
	});

	it("should ensure quote is in quotable state", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(QuoteStatusService.customerAcceptQuote as jest.Mock).mockRejectedValue(
			new Error("Quote cannot be accepted in its current state")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockJobId, quoteId: mockQuoteId },
			session: { user: { id: mockUserId } }
		});

		const response = await POST(req);
		const responseData = await response.json();

		expect(response.status).toBe(500);
		expect(responseData.error).toBe("Failed to accept quote");
	});
});
