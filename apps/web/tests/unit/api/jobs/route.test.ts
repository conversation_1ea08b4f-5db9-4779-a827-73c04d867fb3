import { JobService } from "@/lib/services/job.service";
import { prisma as mockPrisma } from "@/tests/mocks/prisma-mock";
import {
	createMockRequest,
	mockBaseHandler,
} from "@/tests/utils/api-test-utils";

// Most mocks are now global - see tests/jest.setup.ts

// Mock the JobService
jest.mock("@/lib/services/job.service", () => ({
	JobService: {
		createJob: jest.fn(),
		getJobsForUser: jest.fn(),
		getExistingJobsByEmail: jest.fn(),
		getMostRecentActiveJobByEmail: jest.fn(),
	},
}));

// Import handlers after mocks are set up
import { POST } from "@/app/api/jobs/route";
import { POST as createPhoneLead } from "@/app/api/leads/phone/route";

// Get the mocked functions
const mockJobService = jest.mocked(JobService);

describe("Leads API", () => {
	const mockUser = {
		id: "user123",
		email: "<EMAIL>",
		first_name: "Test",
		last_name: "User",
		rv_details: {
			year: 2020,
			make: "Test Make",
			model: "Test Model",
			type: "Class A"
		}
	};

	const mockListing = {
		id: "list123",
		owner_id: "owner123",
		name: "Test Listing",
		email: "<EMAIL>",
		sms_verified_at: new Date(),
		phone: "+1234567890"
	};

	const mockLeadData = {
		listing_id: mockListing.id,
		first_name: "John",
		last_name: "Doe",
		email: "<EMAIL>",
		contact_phone: "************",
		contact_preference: "phone",
		category: "rv-repair",
		message: "This is a test message that is longer than 25 characters as required by the validation schema",
		location: {
			address: "123 Test St",
			city: "Test City",
			state: "TS",
			zip: "12345",
			country: "Test Country",
			latitude: 40.7128,
			longitude: -74.006
		},
		source: "web"
	};

	beforeEach(() => {
		jest.clearAllMocks();
		mockPrisma.listing.findUnique.mockResolvedValue(mockListing);
		mockPrisma.user.findUnique.mockResolvedValue(mockUser);
		mockPrisma.user.upsert.mockResolvedValue(mockUser);
		mockPrisma.job.create.mockResolvedValue({ id: "job123" });
		mockPrisma.lead.create.mockResolvedValue({ id: "lead123" });
		mockBaseHandler.user = mockUser; // Reset user for each test
		mockBaseHandler.isAuthenticated = true; // Reset auth for each test

		// Mock no existing jobs by default
		mockJobService.getExistingJobsByEmail.mockResolvedValue({
			existingJobs: [],
			userStatus: {
				userExists: false,
				hasPassword: false,
				needsPasswordSetup: true,
				email: "<EMAIL>",
				firstName: null,
				lastName: null
			}
		});
		mockJobService.getMostRecentActiveJobByEmail.mockResolvedValue(null);
	});

	describe("POST /api/jobs", () => {
		it("should create job successfully using JobService", async () => {
			const mockJob = {
				id: "job123",
				...mockLeadData,
				user_id: mockUser.id,
				quotes: [{
					id: "quote123",
					listing_id: mockListing.id,
					listing: mockListing
				}]
			};

			// Mock successful job creation
			mockJobService.createJob.mockResolvedValue({
				success: true,
				message: "Lead submission received and will be processed shortly",
				job: mockJob
			});

			const req = createMockRequest({
				method: "POST",
				body: mockLeadData
			});

			const response = await POST(req);

			// Verify JobService was called with correct parameters
			expect(mockJobService.createJob).toHaveBeenCalledWith({
				...mockLeadData,
				isWarranty: false
			});

			// Verify response
			expect(mockBaseHandler.respond).toHaveBeenCalledWith(
				{
					success: true,
					message: "Lead submission received and will be processed shortly",
					data: mockJob
				},
				200
			);
		});

		it("should handle blacklisted email", async () => {
			// Mock blacklist rejection
			mockJobService.createJob.mockResolvedValue({
				success: false,
				error: "Lead submission blocked",
				message: "Email address is not allowed to submit leads"
			});

			const req = createMockRequest({
				method: "POST",
				body: mockLeadData
			});

			const response = await POST(req);

			// Verify JobService was called
			expect(mockJobService.createJob).toHaveBeenCalledWith({
				...mockLeadData,
				isWarranty: false
			});

			// Verify 403 response
			expect(response.status).toBe(403);
			const data = await response.json();
			expect(data).toEqual({
				success: false,
				error: "Lead submission blocked",
				message: "Email address is not allowed to submit leads"
			});
		});

		it("should handle service errors", async () => {
			// Mock service error
			mockJobService.createJob.mockResolvedValue({
				success: false,
				error: "Internal server error",
				message: "Failed to create job"
			});

			const req = createMockRequest({
				method: "POST",
				body: mockLeadData
			});

			const response = await POST(req);

			// Verify 500 response
			expect(response.status).toBe(500);
			const data = await response.json();
			expect(data).toEqual({
				success: false,
				error: "Internal server error",
				message: "Failed to create job"
			});
		});

		it("should handle warranty requests", async () => {
			const warrantyData = {
				...mockLeadData,
				warranty_request_id: "warranty123"
			};

			const mockJob = {
				id: "job123",
				...warrantyData,
				user_id: mockUser.id
			};

			mockJobService.createJob.mockResolvedValue({
				success: true,
				message: "Lead submission received and will be processed shortly",
				job: mockJob
			});

			const req = createMockRequest({
				method: "POST",
				url: "/api/jobs?warranty=true",
				body: warrantyData
			});

			const response = await POST(req);

			// Verify JobService was called with warranty flag
			expect(mockJobService.createJob).toHaveBeenCalledWith({
				...warrantyData,
				isWarranty: true
			});
		});
	});

	describe("POST /api/leads/phone", () => {
		const mockPhoneLeadData = {
			listing_id: mockListing.id,
			first_name: "John",
			last_name: "Doe",
			email: "<EMAIL>",
			phone: "************",
			contact_preference: "phone",
			category: "rv-inspection",
			message: "This is a phone call lead message that is longer than 25 characters as required",
			source: "web",
			type: "phone",
			location: {
				address: "123 Test St",
				city: "Test City",
				state: "TS",
				zip: "12345",
				country: "Test Country",
				latitude: 40.7128,
				longitude: -74.006
			}
		};

		beforeEach(() => {
			mockBaseHandler.user = mockUser; // Set user for phone lead tests
			mockBaseHandler.isAuthenticated = true;
		});

		it("should create a phone lead successfully", async () => {
			const mockLead = {
				id: "lead123",
				...mockPhoneLeadData,
				user_id: mockUser.id,
				contact_preference: "phone",
				status: "sent",
				sent_at: new Date()
			};

			mockPrisma.user.findUnique.mockResolvedValue({
				...mockUser,
				rv_details: {
					year: 2020,
					make: "Test Make",
					model: "Test Model",
					type: "Class A"
				}
			});
			mockPrisma.lead.create.mockResolvedValue(mockLead);

			const req = createMockRequest({
				method: "POST",
				url: "/api/leads/phone",
				body: mockPhoneLeadData
			});

			const response = await createPhoneLead(req);
			const data = await response.json();

			expect(mockPrisma.lead.create).toHaveBeenCalledWith({
				data: expect.objectContaining({
					...mockPhoneLeadData,
					user_id: mockUser.id,
					contact_preference: "phone",
					status: "sent",
					message: expect.stringContaining("RV Details:")
				})
			});

			expect(response.status).toBe(200);
			expect(data).toEqual({
				success: true,
				lead_id: "lead123"
			});
		});

	});
});
