import { POST } from "@/app/api/provider/quotes/[id]/accept/route";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { mockPrisma } from "@/tests/mocks/prisma-mock";
import { createMockRequest, mockBaseHandler, mockProviderUser } from "@/tests/utils/api-test-utils";

// Mock the services
jest.mock("@/lib/services/quote-status.service", () => ({
	QuoteStatusService: {
		providerAcceptJob: jest.fn()
	}
}));


describe("POST /api/provider/quotes/[id]/accept", () => {
	const mockQuoteId = "quote123";
	const mockListingId = "listing123";
	const mockJobId = "job456";
	const mockUserId = "user789";

	const mockQuote = {
		id: mockQuoteId,
		listing_id: mockListingId,
		job_id: mockJobId,
		status: "PENDING",
		listing: {
			id: mockListingId,
			business_name: "Test RV Service",
			first_name: "<PERSON>",
			last_name: "Provider",
			owner: {
				id: mockUserId,
				email: "<EMAIL>"
			}
		}
	};

	const acceptData = {
		message: "I can help with this job"
	};

	const mockAcceptedQuote = {
		id: mockQuoteId,
		status: "ACCEPTED",
		message: acceptData.message
	};

	beforeEach(() => {
		jest.clearAllMocks();
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		// Set up the session to use the provider user
		mockBaseHandler.user = mockProviderUser;
		mockBaseHandler.session = { user: mockProviderUser };
	});

	it("should accept a quote with authentication", async () => {
		(QuoteStatusService.providerAcceptJob as jest.Mock).mockResolvedValue(
			mockAcceptedQuote
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: acceptData
		});

		const response = await POST(req);
		const data = await response.json();

		expect(mockPrisma.quote.findUnique).toHaveBeenCalledWith({
			where: { id: mockQuoteId }
		});

		expect(QuoteStatusService.providerAcceptJob).toHaveBeenCalledWith({
			quote: mockQuote,
			userId: mockProviderUser.id,
			message: acceptData.message
		});

		expect(response.status).toBe(200);
		expect(data).toEqual(mockAcceptedQuote);
	});

	it("should handle missing quote", async () => {
		mockPrisma.quote.findUnique.mockResolvedValue(null);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: acceptData
		});

		const response = await POST(req, { params: { id: mockQuoteId } });
		const data = await response.json();

		expect(response.status).toBe(404);
		expect(data).toEqual("Quote not found");
		expect(QuoteStatusService.providerAcceptJob).not.toHaveBeenCalled();
	});

	it("should handle service errors", async () => {
		(QuoteStatusService.providerAcceptJob as jest.Mock).mockRejectedValue(
			new Error("Service error")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: acceptData
		});

		await expect(POST(req, { params: { id: mockQuoteId } })).rejects.toThrow(
			"Service error"
		);
	});
});
