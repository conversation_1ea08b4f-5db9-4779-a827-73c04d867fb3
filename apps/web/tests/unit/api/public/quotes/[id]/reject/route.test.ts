import { POST } from "@/app/api/provider/quotes/[id]/reject/route";
import { ListingService } from "@/lib/services/listing.service";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { mockPrisma } from "@/tests/mocks/prisma-mock";
import { createMockRequest } from "@/tests/utils/api-test-utils";

// Mock the services
jest.mock("@/lib/services/quote-status.service", () => ({
	QuoteStatusService: {
		providerDeclineJob: jest.fn()
	}
}));

jest.mock("@/lib/services/listing.service", () => ({
	ListingService: {
		getListingByUserId: jest.fn()
	}
}));

describe("POST /api/public/quotes/[id]/reject", () => {
	const mockQuoteId = "quote123";
	const mockListingId = "listing123";
	const mockJobId = "job456";
	const mockUserId = "user789";

	const mockQuote = {
		id: mockQuoteId,
		listing_id: mockListingId,
		job_id: mockJobId,
		status: "PENDING",
		listing: {
			id: mockListingId,
			business_name: "Test RV Service",
			first_name: "John",
			last_name: "Provider",
			owner: {
				id: mockUserId,
				email: "<EMAIL>"
			}
		}
	};

	const rejectData = {
		rejection_reason: "TOO_BUSY" as const,
		rejection_reason_details: "Fully booked this month"
	};

	const mockRejectedQuote = {
		id: mockQuoteId,
		status: "REJECTED",
		rejection_reason: rejectData.rejection_reason,
		rejection_reason_details: rejectData.rejection_reason_details
	};

	const mockListing = {
		id: mockListingId,
		business_name: "Test RV Service",
		first_name: "John",
		last_name: "Provider"
	};

	beforeEach(() => {
		jest.clearAllMocks();
		mockPrisma.quote.findUnique.mockResolvedValue(mockQuote as any);
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(mockListing);
	});

	it("should reject a quote without authentication", async () => {
		(QuoteStatusService.providerDeclineJob as jest.Mock).mockResolvedValue(
			mockRejectedQuote
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: rejectData
		});

		const response = await POST(req);
		const data = await response.json();

		expect(ListingService.getListingByUserId).toHaveBeenCalled();

		expect(QuoteStatusService.providerDeclineJob).toHaveBeenCalledWith({
			quoteId: mockQuoteId,
			listingId: mockListingId,
			userId: "user123", // From mockUser in api-test-utils
			rejectionReason: rejectData.rejection_reason,
			rejectionReasonDetails: rejectData.rejection_reason_details
		});

		expect(response.status).toBe(200);
		expect(data).toEqual(mockRejectedQuote);
	});

	it("should handle missing quote", async () => {
		(QuoteStatusService.providerDeclineJob as jest.Mock).mockRejectedValue(
			new Error("Quote not found")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: rejectData
		});

		await expect(POST(req)).rejects.toThrow("Quote not found");
	});

	it("should handle missing listing", async () => {
		(ListingService.getListingByUserId as jest.Mock).mockResolvedValue(null);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: rejectData
		});

		await expect(POST(req)).rejects.toThrow("Provider listing not found");
		expect(QuoteStatusService.providerDeclineJob).not.toHaveBeenCalled();
	});

	it("should handle all rejection reason values", async () => {
		const rejectionReasons = [
			"TOO_BUSY",
			"SCHEDULE_CONFLICT",
			"OUTSIDE_TRAVEL_AREA",
			"NOT_A_GOOD_FIT",
			"OTHER"
		] as const;

		for (const reason of rejectionReasons) {
			const testData = { ...rejectData, rejection_reason: reason };
			(QuoteStatusService.providerDeclineJob as jest.Mock).mockResolvedValue(
				mockRejectedQuote
			);

			const req = createMockRequest({
				method: "POST",
				params: { id: mockQuoteId },
				body: testData
			});

			const response = await POST(req);

			expect(QuoteStatusService.providerDeclineJob).toHaveBeenCalledWith({
				quoteId: mockQuoteId,
				listingId: mockListingId,
				userId: "user123", // From mockUser in api-test-utils
				rejectionReason: reason,
				rejectionReasonDetails: testData.rejection_reason_details
			});

			expect(response.status).toBe(200);
		}
	});

	it("should handle rejection without message", async () => {
		const dataWithoutMessage = {
			rejection_reason: "TOO_BUSY" as const
		};
		(QuoteStatusService.providerDeclineJob as jest.Mock).mockResolvedValue(
			mockRejectedQuote
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: dataWithoutMessage
		});

		const response = await POST(req);

		expect(QuoteStatusService.providerDeclineJob).toHaveBeenCalledWith({
			quoteId: mockQuoteId,
			listingId: mockListingId,
			userId: "user123", // From mockUser in api-test-utils
			rejectionReason: dataWithoutMessage.rejection_reason,
			rejectionReasonDetails: undefined
		});

		expect(response.status).toBe(200);
	});

	it("should handle null message", async () => {
		const dataWithNullMessage = {
			rejection_reason: "TOO_BUSY" as const,
			rejection_reason_details: null
		};
		(QuoteStatusService.providerDeclineJob as jest.Mock).mockResolvedValue(
			mockRejectedQuote
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: dataWithNullMessage
		});

		const response = await POST(req);

		expect(QuoteStatusService.providerDeclineJob).toHaveBeenCalledWith({
			quoteId: mockQuoteId,
			listingId: mockListingId,
			userId: "user123", // From mockUser in api-test-utils
			rejectionReason: dataWithNullMessage.rejection_reason,
			rejectionReasonDetails: null
		});

		expect(response.status).toBe(200);
	});

	it("should handle service errors", async () => {
		(QuoteStatusService.providerDeclineJob as jest.Mock).mockRejectedValue(
			new Error("Service error")
		);

		const req = createMockRequest({
			method: "POST",
			params: { id: mockQuoteId },
			body: rejectData
		});

		await expect(POST(req)).rejects.toThrow("Service error");
	});
});
