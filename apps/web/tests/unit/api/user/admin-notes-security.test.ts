import { describe, expect, it, beforeEach } from "@jest/globals";
import { UserService } from "@/lib/services/user.service";
import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
  createMockRequest,
  mockUser,
  mockProviderUser
} from "@/tests/utils/api-test-utils";

// Set up mocks before importing the handlers
jest.mock("@/lib/services/user.service", () => ({
  UserService: {
    user: jest.fn()
  }
}));

jest.mock("@/lib/prisma", () => ({
  prisma: mockPrisma,
  __esModule: true,
  default: mockPrisma
}));

// Now import handlers after mocks are set up
import { GET, PUT } from "@/app/api/user/route";

const mockUserService = UserService as jest.Mocked<typeof UserService>;

describe("Admin Notes Security Tests", () => {
  const mockNonAdminUser = {
    ...mockUser,
    id: "user-123",
    email: "<EMAIL>",
    first_name: "Test",
    last_name: "User",
    role: "USER",
    admin_notes: "This is a secret admin note about the user",
    created_at: new Date(),
    updated_at: new Date()
  };

  const mockAdminUser = {
    ...mockProviderUser,
    id: "admin-123",
    email: "<EMAIL>",
    first_name: "Admin",
    last_name: "User",
    role: "ADMIN",
    admin_notes: "This is a secret admin note about the admin",
    created_at: new Date(),
    updated_at: new Date()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("GET /api/user endpoint", () => {
    it("should not return admin_notes for non-admin users", async () => {
      // Mock UserService to return user without admin_notes (as it would for session-based auth)
      const userWithoutAdminNotes = {
        ...mockNonAdminUser,
        admin_notes: undefined
      };
      delete userWithoutAdminNotes.admin_notes;
      
      mockUserService.user.mockResolvedValue(userWithoutAdminNotes);

      const req = createMockRequest({
        method: "GET",
        url: "/api/user"
      });

      const response = await GET(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty("id");
      expect(data).toHaveProperty("email");
      expect(data).toHaveProperty("first_name");
      expect(data).toHaveProperty("last_name");
      expect(data).toHaveProperty("role");
      expect(data).not.toHaveProperty("admin_notes");
    });

    it("should return admin_notes for admin users", async () => {
      mockUserService.user.mockResolvedValue(mockAdminUser);

      const req = createMockRequest({
        method: "GET",
        url: "/api/user"
      });

      const response = await GET(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty("admin_notes");
      expect(data.admin_notes).toBe("This is a secret admin note about the admin");
    });

    it("should handle null user gracefully", async () => {
      mockUserService.user.mockResolvedValue(null);

      const req = createMockRequest({
        method: "GET",
        url: "/api/user"
      });

      const response = await GET(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toBeNull();
    });

    it("should handle UserService errors gracefully", async () => {
      mockUserService.user.mockRejectedValue(new Error("Database error"));

      const req = createMockRequest({
        method: "GET",
        url: "/api/user"
      });

      const response = await GET(req);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toHaveProperty("error", "Internal Server Error");
    });
  });

  describe("PUT /api/user endpoint", () => {
    it("should handle unauthorized users", async () => {
      mockUserService.user.mockResolvedValue(null);

      const req = createMockRequest({
        method: "PUT",
        url: "/api/user",
        body: { first_name: "Updated" }
      });

      const response = await PUT(req);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data).toHaveProperty("error", "Unauthorized");
    });

    it("should update user data for authenticated users", async () => {
      mockUserService.user.mockResolvedValue(mockNonAdminUser);
      
      // Mock UserService.update to return updated user
      const mockUpdate = jest.fn().mockResolvedValue({
        ...mockNonAdminUser,
        first_name: "Updated"
      });
      (UserService as any).update = mockUpdate;

      const req = createMockRequest({
        method: "PUT",
        url: "/api/user",
        body: { first_name: "Updated" }
      });

      const response = await PUT(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty("first_name", "Updated");
      expect(mockUpdate).toHaveBeenCalledWith(mockNonAdminUser.id, { first_name: "Updated" });
    });
  });

  describe("Data structure validation", () => {
    it("should exclude admin_notes from object destructuring for non-admin", () => {
      const userWithAdminNotes = {
        id: "test",
        email: "<EMAIL>",
        admin_notes: "secret",
        role: "USER"
      };

      // Simulate the destructuring logic that should be in the API
      const { admin_notes, ...userWithoutAdminNotes } = userWithAdminNotes;

      expect(userWithoutAdminNotes).not.toHaveProperty("admin_notes");
      expect(userWithoutAdminNotes).toHaveProperty("id");
      expect(userWithoutAdminNotes).toHaveProperty("email");
      expect(userWithoutAdminNotes).toHaveProperty("role");
    });

    it("should preserve admin_notes for admin users in data handling", () => {
      const adminUserWithAdminNotes = {
        id: "test",
        email: "<EMAIL>",
        admin_notes: "secret",
        role: "ADMIN"
      };

      // Admin users should get the full object
      expect(adminUserWithAdminNotes).toHaveProperty("admin_notes");
      expect(adminUserWithAdminNotes.admin_notes).toBe("secret");
    });

    it("should sanitize listings array for non-admin users", () => {
      const listingsWithAdminNotes = [
        {
          id: "listing-1",
          business_name: "Business 1",
          admin_notes: "Secret note about business 1",
          status: "ACTIVE"
        },
        {
          id: "listing-2",
          business_name: "Business 2",
          admin_notes: "Secret note about business 2",
          status: "ACTIVE"
        }
      ];

      // Simulate the sanitization logic that should be used for non-admin users
      const sanitizedListings = listingsWithAdminNotes.map(({ admin_notes, ...listing }) => listing);

      expect(sanitizedListings).toHaveLength(2);
      expect(sanitizedListings[0]).not.toHaveProperty("admin_notes");
      expect(sanitizedListings[1]).not.toHaveProperty("admin_notes");
      expect(sanitizedListings[0]).toHaveProperty("id");
      expect(sanitizedListings[0]).toHaveProperty("business_name");
      expect(sanitizedListings[0]).toHaveProperty("status");
    });

    it("should preserve admin_notes in listings for admin users", () => {
      const listingsWithAdminNotes = [
        {
          id: "listing-1",
          business_name: "Business 1",
          admin_notes: "Secret note about business 1",
          status: "ACTIVE"
        }
      ];

      // Admin users should get unsanitized listings
      const adminListings = listingsWithAdminNotes; // No sanitization for admins

      expect(adminListings[0]).toHaveProperty("admin_notes");
      expect(adminListings[0].admin_notes).toBe("Secret note about business 1");
    });
  });
}); 