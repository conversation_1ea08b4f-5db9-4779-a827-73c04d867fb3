import { GET, PATCH, POST } from "@/app/api/warranty-requests/[id]/invoice/route";
import { mockPrisma } from "../../../../../mocks/prisma-mock";
import { createMockRequest, mockBaseHandler, mockProviderUser } from "../../../../../utils/api-test-utils";

// Mock the base handler
jest.mock("@/lib/api/baseHandler", () => ({
    createHandler: jest.fn((handler, options) => {
        return async (req: any, context: any) => {
            // Mock the base handler context
            mockBaseHandler.req = req;
            mockBaseHandler.user = mockProviderUser;
            mockBaseHandler.validatedData = context?.validatedData || req.body;
            mockBaseHandler.respond = jest.fn((data, status = 200) => {
                return new Response(JSON.stringify(data), { status });
            });

            // Call the actual handler with the context that includes params
            return handler.call(mockBaseHandler, req, context);
        };
    })
}));

describe("Warranty Invoice API Routes", () => {
    const mockWarrantyRequestId = "warranty-123";
    const mockInvoiceId = "invoice-456";
    const mockListingId = "listing-789";

    const mockWarrantyRequest = {
        id: mockWarrantyRequestId,
        company_id: "company-123",
        provider_invoice_id: null,
        status: "JOB_STARTED",
        company: {
            users: [{ id: mockProviderUser.id }]
        }
    };

    const mockInvoice = {
        id: mockInvoiceId,
        provider_id: mockListingId,
        customer_name: "Test Customer",
        customer_email: "<EMAIL>",
        amount: 25000,
        status: "DRAFT",
        warranty_request_id: mockWarrantyRequestId
    };

    const mockListing = {
        id: mockListingId,
        owner_id: mockProviderUser.id
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockBaseHandler.user = mockProviderUser;
    });

    describe("POST /api/warranty-requests/[id]/invoice", () => {
        it("should create invoice and link it to warranty request", async () => {
            // Mock finding the warranty request
            (mockPrisma.warrantyRequest.findFirst as jest.Mock).mockResolvedValue(mockWarrantyRequest);

            // Mock getting the listing
            const { ListingService } = require("@/lib/services/listing.service");
            ListingService.getListingByUserId = jest.fn().mockResolvedValue(mockListing);

            // Mock invoice service
            const { invoiceService } = require("@/lib/services");
            invoiceService.createInvoice = jest.fn().mockResolvedValue(mockInvoice);

            // Mock warranty request update (status doesn't change for draft invoices)
            (mockPrisma.warrantyRequest.update as jest.Mock).mockResolvedValue({
                ...mockWarrantyRequest,
                provider_invoice_id: mockInvoiceId
                // Status remains unchanged for draft invoices
            });



            const req = createMockRequest({
                method: "POST",
                url: `/api/warranty-requests/${mockWarrantyRequestId}/invoice`,
                body: {
                    customer_name: "Test Customer",
                    customer_email: "<EMAIL>",
                    amount: 25000,
                    status: "DRAFT"
                }
            });

            const response = await POST(req, {
                params: { id: mockWarrantyRequestId },
                validatedData: {
                    customer_name: "Test Customer",
                    customer_email: "<EMAIL>",
                    amount: 25000,
                    status: "DRAFT"
                }
            });

            const data = await response.json();

            // Verify warranty request was found
            expect(mockPrisma.warrantyRequest.findFirst).toHaveBeenCalledWith({
                where: {
                    id: mockWarrantyRequestId
                }
            });

            // Verify invoice was created
            expect(invoiceService.createInvoice).toHaveBeenCalledWith({
                customer_name: "Test Customer",
                customer_email: "<EMAIL>",
                amount: 25000,
                status: "DRAFT",
                provider_id: mockListingId,
                warranty_request_id: mockWarrantyRequestId
            });

            // Verify warranty request was updated with invoice ID (but status unchanged for draft)
            expect(mockPrisma.warrantyRequest.update).toHaveBeenCalledWith({
                where: { id: mockWarrantyRequestId },
                data: {
                    provider_invoice_id: mockInvoiceId
                    // Status remains unchanged for draft invoices
                }
            });



            expect(response.status).toBe(200);
            expect(data).toEqual({ invoice: mockInvoice });
        });

        it("should return error if invoice already exists", async () => {
            const warrantyRequestWithInvoice = {
                ...mockWarrantyRequest,
                provider_invoice_id: mockInvoiceId
            };

            (mockPrisma.warrantyRequest.findFirst as jest.Mock).mockResolvedValue(warrantyRequestWithInvoice);

            const req = createMockRequest({
                method: "POST",
                url: `/api/warranty-requests/${mockWarrantyRequestId}/invoice`,
                body: {
                    customer_name: "Test Customer",
                    customer_email: "<EMAIL>",
                    amount: 25000
                }
            });

            const response = await POST(req, {
                params: { id: mockWarrantyRequestId },
                validatedData: {
                    customer_name: "Test Customer",
                    customer_email: "<EMAIL>",
                    amount: 25000
                }
            });

            const data = await response.json();

            expect(response.status).toBe(400);
            expect(data.error).toBe("Invoice already exists for this warranty request");
        });

        it("should return 404 if warranty request not found", async () => {
            (mockPrisma.warrantyRequest.findFirst as jest.Mock).mockResolvedValue(null);

            const req = createMockRequest({
                method: "POST",
                url: `/api/warranty-requests/${mockWarrantyRequestId}/invoice`,
                body: {
                    customer_name: "Test Customer",
                    customer_email: "<EMAIL>",
                    amount: 25000
                }
            });

            const response = await POST(req, {
                params: { id: mockWarrantyRequestId },
                validatedData: {
                    customer_name: "Test Customer",
                    customer_email: "<EMAIL>",
                    amount: 25000
                }
            });

            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data.error).toBe("Warranty request not found");
        });
    });

    describe("GET /api/warranty-requests/[id]/invoice", () => {
        it("should return existing invoice for warranty request", async () => {
            const warrantyRequestWithInvoice = {
                ...mockWarrantyRequest,
                provider_invoice_id: mockInvoiceId,
                provider_invoice: mockInvoice
            };

            (mockPrisma.warrantyRequest.findFirst as jest.Mock).mockResolvedValue(warrantyRequestWithInvoice);

            const req = createMockRequest({
                method: "GET",
                url: `/api/warranty-requests/${mockWarrantyRequestId}/invoice`
            });

            const response = await GET(req, {
                params: { id: mockWarrantyRequestId }
            });

            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ invoice: mockInvoice });
        });

        it("should return null if no invoice exists", async () => {
            (mockPrisma.warrantyRequest.findFirst as jest.Mock).mockResolvedValue(mockWarrantyRequest);

            const req = createMockRequest({
                method: "GET",
                url: `/api/warranty-requests/${mockWarrantyRequestId}/invoice`
            });

            const response = await GET(req, {
                params: { id: mockWarrantyRequestId }
            });

            const data = await response.json();

            expect(response.status).toBe(200);
            expect(data).toEqual({ invoice: null });
        });
    });

    describe("PATCH /api/warranty-requests/[id]/invoice", () => {
        it("should update existing invoice", async () => {
            const warrantyRequestWithInvoice = {
                ...mockWarrantyRequest,
                provider_invoice_id: mockInvoiceId,
                provider_invoice: mockInvoice
            };

            (mockPrisma.warrantyRequest.findFirst as jest.Mock).mockResolvedValue(warrantyRequestWithInvoice);

            const { invoiceService } = require("@/lib/services");
            invoiceService.updateInvoice = jest.fn().mockResolvedValue({
                ...mockInvoice,
                customer_name: "Updated Customer"
            });

            const req = createMockRequest({
                method: "PATCH",
                url: `/api/warranty-requests/${mockWarrantyRequestId}/invoice`,
                body: {
                    customer_name: "Updated Customer"
                }
            });

            const response = await PATCH(req, {
                params: { id: mockWarrantyRequestId },
                validatedData: {
                    customer_name: "Updated Customer"
                }
            });

            const data = await response.json();

            expect(invoiceService.updateInvoice).toHaveBeenCalledWith(
                mockInvoiceId,
                { customer_name: "Updated Customer" }
            );

            expect(response.status).toBe(200);
            expect(data.invoice.customer_name).toBe("Updated Customer");
        });

        it("should return 404 if no invoice exists", async () => {
            (mockPrisma.warrantyRequest.findFirst as jest.Mock).mockResolvedValue(mockWarrantyRequest);

            const req = createMockRequest({
                method: "PATCH",
                url: `/api/warranty-requests/${mockWarrantyRequestId}/invoice`,
                body: {
                    customer_name: "Updated Customer"
                }
            });

            const response = await PATCH(req, {
                params: { id: mockWarrantyRequestId },
                validatedData: {
                    customer_name: "Updated Customer"
                }
            });

            const data = await response.json();

            expect(response.status).toBe(404);
            expect(data.error).toBe("No invoice found for this warranty request");
        });
    });
});
