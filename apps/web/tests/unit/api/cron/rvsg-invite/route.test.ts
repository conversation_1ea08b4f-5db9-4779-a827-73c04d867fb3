import { GET } from "@/app/api/cron/rvsg-invite/route";
import { createMockRequest } from "@/tests/utils/api-test-utils";

// Mock the RVSGService
jest.mock("@/lib/services/rvsg.service", () => ({
    RVSGService: {
        processRecentRVSGImports: jest.fn(),
    },
}));


describe("RVSG Invite Cron Job", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should process RVSG imports when called with valid secret", async () => {
        const { RVSGService } = await import("@/lib/services/rvsg.service");
        (RVSGService.processRecentRVSGImports as jest.Mock).mockResolvedValue({
            success: true,
            listings: [{ id: "1" }, { id: "2" }],
            stats: {
                totalProcessed: 2,
                invited: 1,
                skipped: 1,
                inspectionEligible: 1,
                repairEligible: 0
            }
        });

        const req = createMockRequest({
            method: "GET",
            headers: {
                "authorization": "Bearer test-secret"
            }
        });

        const response = await GET(req);
        const data = await response.json();

        expect(RVSGService.processRecentRVSGImports).toHaveBeenCalled();
        expect(response.status).toBe(200);
        expect(data).toEqual({
            success: true,
            message: "RVSG invite processing completed successfully",
            listings: 2,
            stats: {
                totalProcessed: 2,
                invited: 1,
                skipped: 1,
                inspectionEligible: 1,
                repairEligible: 0
            },
            duration: expect.any(Number)
        });
    });

    it("should return 401 when called with invalid secret", async () => {
        const req = createMockRequest({
            method: "GET",
            headers: {
                "authorization": "Bearer wrong-secret"
            }
        });

        const response = await GET(req);

        expect(response.status).toBe(401);
        expect(await response.text()).toBe("Unauthorized");
    });

    it("should return 401 when called with no authorization header", async () => {
        const req = createMockRequest({
            method: "GET"
        });

        const response = await GET(req);

        expect(response.status).toBe(401);
        expect(await response.text()).toBe("Unauthorized");
    });

    it("should handle service errors gracefully", async () => {
        const { RVSGService } = await import("@/lib/services/rvsg.service");
        (RVSGService.processRecentRVSGImports as jest.Mock).mockRejectedValue(
            new Error("Service error")
        );

        const req = createMockRequest({
            method: "GET",
            headers: {
                "authorization": "Bearer test-secret"
            }
        });

        const response = await GET(req);

        expect(response.status).toBe(500);
        expect(await response.text()).toBe("Error processing RVSG invites");
    });
}); 