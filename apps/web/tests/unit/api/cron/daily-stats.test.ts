import { GET } from '@/app/api/cron/daily-stats/route';
import { createMockRequest } from '@/tests/utils/api-test-utils';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';

// Mock the services
jest.mock('@/lib/services', () => ({
    slackService: {
        notifyDailyStats: jest.fn().mockResolvedValue(undefined)
    }
}));

jest.mock('@/lib/services/membership.service', () => ({
    membershipService: {
        // Add any methods that might be used by the membership service
    }
}));

jest.mock('@/config', () => ({
    __esModule: true,
    default: {
        cronSecret: 'test-secret'
    }
}));

describe('/api/cron/daily-stats', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return 401 for missing authorization header', async () => {
        const req = createMockRequest({
            method: 'GET'
        });

        const response = await GET(req);
        expect(response.status).toBe(401);
    });

    it('should return 401 for invalid authorization header', async () => {
        const req = createMockRequest({
            method: 'GET',
            headers: {
                'authorization': 'Bearer wrong-secret'
            }
        });

        const response = await GET(req);
        expect(response.status).toBe(401);
    });

    it('should return 500 when CRON_SECRET is not set', async () => {
        // Temporarily mock config to return undefined cronSecret
        const config = require('@/config').default;
        config.cronSecret = undefined;

        const req = createMockRequest({
            method: 'GET',
            headers: {
                'authorization': 'Bearer test-secret'
            }
        });

        const response = await GET(req);
        expect(response.status).toBe(500);

        // Restore the mock
        config.cronSecret = 'test-secret';
    });

    it('should successfully send daily stats notification', async () => {
        const { slackService } = await import('@/lib/services');
        const { membershipService } = await import('@/lib/services/membership.service');

        const req = createMockRequest({
            method: 'GET',
            headers: {
                'authorization': 'Bearer test-secret'
            }
        });

        const response = await GET(req);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data).toEqual({ success: true });

        // Verify that the slack service was called with the membership service
        expect(slackService.notifyDailyStats).toHaveBeenCalledWith(membershipService);
    });
});
