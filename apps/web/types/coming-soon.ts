import { RVHelpVerificationLevel } from "@prisma/client";

export interface ComingSoonLocation {
    id: string;
    city: string;
    state: string;
    latitude: number;
    longitude: number;
    radius: number | null;
    start_date: string;
    end_date: string | null;
    description: string | null;
}

export interface ComingSoonListing {
    id: string;
    slug: string;
    first_name: string;
    last_name: string;
    business_name: string;
    profile_image: string | null;
    short_description: string | null;
    rating: number | null;
    num_reviews: number;
    rv_help_verification_level: RVHelpVerificationLevel;
    distance: number;
    upcoming_location: ComingSoonLocation;
}

export interface ComingSoonApiResponse {
    success: boolean;
    listings: ComingSoonListing[];
    total: number;
    error?: string;
}

export interface ComingSoonSearchParams {
    lat: number;
    lng: number;
    category: string;
    subcategory?: string;
    radius?: number;
}
