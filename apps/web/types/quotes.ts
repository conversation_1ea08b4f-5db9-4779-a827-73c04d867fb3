import { JobWithUserAndLocation, ListingWithLocation } from '@/types/global';
import { ExtendedWarrantyRequest } from '@/types/warranty';
import { Quote } from "@prisma/client";

export interface QuoteMessage {
  id: string;
  quote_id: string;
  content: string;
  created_at: string;
  sender_id: string;
  sender_type: 'USER' | 'PROVIDER';
  type: 'TEXT' | 'IMAGE' | 'DOCUMENT' | 'SYSTEM';
  status: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED' | 'READ';
  sender_user?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  sender_listing?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export interface QuoteWithMessages extends Quote {
  messages: QuoteMessage[];
  unread_messages_count?: number;
  unread_message?: QuoteMessage | null;
  listing: ListingWithLocation;
}

export interface JobWithQuoteAndMessages extends JobWithUserAndLocation {
  distance_miles?: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  contact_preference: string;
  rv_year: string;
  rv_make: string;
  rv_model: string;
  rv_type: string;
  category: string;
  location: {
    id: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;

    address: string;
    city: string;
    state: string;
    zip: string;
    latitude: number;
    longitude: number;
  };
  source: string;
  message: string;
  warranty_request?: ExtendedWarrantyRequest | null;

  quotes: QuoteWithMessages[];
}

export interface QuoteMessage {
  id: string;
  quote_id: string;
  content: string;
  created_at: string;
  sender_id: string;
  sender_type: 'USER' | 'PROVIDER';
  type: 'TEXT' | 'IMAGE' | 'DOCUMENT' | 'SYSTEM';
  status: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED' | 'READ';
  sender_user?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  sender_listing?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export interface QuoteWithMessages extends Quote {
  messages: QuoteMessage[];
  unread_messages_count?: number;
  unread_message?: QuoteMessage | null;
  listing: ListingWithLocation;
}

export interface JobWithQuoteAndMessages extends JobWithUserAndLocation {
  distance_miles?: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  contact_preference: string;
  rv_year: string;
  rv_make: string;
  rv_model: string;
  rv_type: string;
  category: string;
  location: {
    id: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;

    address: string;
    city: string;
    state: string;
    zip: string;
    latitude: number;
    longitude: number;
  };
  source: string;
  message: string;
  warranty_request?: ExtendedWarrantyRequest | null;

  quotes: QuoteWithMessages[];
}
