# Provider Leads System - Technical Documentation

## Architecture Overview

The Provider Leads System consists of a React frontend with TypeScript, a Next.js API layer, and a PostgreSQL database with Prisma ORM. The system tracks provider performance metrics and manages lead response workflows.

## Database Schema

### Core Models

#### Listing Model
```prisma
model Listing {
  // ... existing fields ...
  
  // Provider Performance
  is_highly_responsive Boolean @default(false)
  
  // Relations
  provider_stats ProviderStats?
  quotes Quote[]
}
```

#### ProviderStats Model
```prisma
model ProviderStats {
  id String @id @default(cuid())
  listing_id String @unique
  listing Listing @relation(fields: [listing_id], references: [id], onDelete: Cascade)
  
  // 30-day rolling stats
  total_leads_30d Int @default(0)
  responded_leads_30d Int @default(0)
  response_rate_30d Float @default(0)
  avg_response_time_30d Float @default(0)
  
  // 90-day rolling stats
  total_leads_90d Int @default(0)
  responded_leads_90d Int @default(0)
  response_rate_90d Float @default(0)
  avg_response_time_90d Float @default(0)
  
  // All-time stats
  total_leads_all_time Int @default(0)
  responded_leads_all_time Int @default(0)
  response_rate_all_time Float @default(0)
  avg_response_time_all_time Float @default(0)
  
  last_calculated_at DateTime @default(now())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@index([listing_id])
  @@map("provider_stats")
}
```

#### Quote Model (Enhanced)
```prisma
model Quote {
  // ... existing fields ...
  
  // Response tracking
  invited_at DateTime @default(now())
  responded_at DateTime? // When provider responded
  
  // Status management
  status QuoteStatus @default(INVITED)
}
```

## Service Layer

### ProviderStatsService

Core service for calculating and managing provider performance metrics.

#### Key Methods

```typescript
class ProviderStatsService {
  // Calculate and update stats for a specific provider
  static async updateProviderStats(listingId: string): Promise<void>
  
  // Update stats when provider responds to a lead
  static async onProviderResponse(listingId: string): Promise<void>
  
  // Update stats when new lead is received
  static async onNewLead(listingId: string): Promise<void>
  
  // Get provider stats for a listing
  static async getProviderStats(listingId: string): Promise<ProviderStats | null>
  
  // Update all provider stats (for cron job)
  static async updateAllProviderStats(): Promise<void>
}
```

#### Calculation Logic

**Response Rate**: `(responded_leads / total_leads) * 100`

**Average Response Time**: 
```typescript
const responseTimes = quotes
  .filter(q => q.responded_at && q.invited_at)
  .map(q => {
    const invitedAt = new Date(q.invited_at);
    const quotedAt = new Date(q.responded_at);
    return (quotedAt.getTime() - invitedAt.getTime()) / (1000 * 60 * 60); // hours
  });

const avgResponseTime = responseTimes.length > 0 
  ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
  : 0;
```

**Highly Responsive Flag**:
```typescript
const isHighlyResponsive = responseRate30d >= 80 && avgResponseTime30d <= 24;
```

## API Endpoints

### Lead Management

#### GET `/api/provider/quotes`
Retrieve leads filtered by status.

**Query Parameters:**
- `status`: QuoteStatus enum or "all"

**Response:**
```typescript
type QuoteWithJob = Quote & {
  job: Job & {
    user: {
      first_name: string;
      last_name: string;
      email: string;
      phone: string;
    }
  }
  messages: Array<{
    id: string;
    content: string;
    created_at: Date;
  }>;
};
```

#### GET `/api/provider/stats`
Get provider performance metrics.

**Response:**
```typescript
{
  totalLeads: number;
  responseRate: number;
  avgResponseTime: number;
  recentResponseRate: number;
}
```

### Response Endpoints

#### POST `/api/provider/quotes/[id]/accept`
Submit a quote/proposal for a lead.

**Request Body:**
```typescript
{
  message: string;
  schedulingTimeframe: string;
  display_pricing: boolean;
  discount_hourly_rate: boolean;
  discount_dispatch_fee: boolean;
  first_hour_included?: boolean;
}
```

#### POST `/api/provider/quotes/[id]/withdraw`
Decline/withdraw from a lead.

**Request Body:**
```typescript
{
  message: string;
}
```

#### POST `/api/provider/quotes/[id]/request-info`
Request additional information from customer.

**Request Body:**
```typescript
{
  details: string;
}
```

### Cron Jobs

#### POST `/api/cron/update-provider-stats`
Periodic update of all provider statistics.

## Frontend Components

### Lead Management Page
**File:** `apps/web/app/(main)/provider/(dashboard)/leads/page.tsx`

**Features:**
- Stats overview cards
- Status-based filtering
- Lead cards with preview information
- Modal-based detail view

**State Management:**
```typescript
const [serviceRequests, setServiceRequests] = useState<QuoteWithJob[]>([]);
const [stats, setStats] = useState({
  totalLeads: 0,
  responseRate: 0,
  avgResponseTime: 0,
  recentResponseRate: 0
});
```

### Lead Details Modal
**File:** `apps/web/app/(main)/provider/(dashboard)/leads/components/LeadDetailsModal.tsx`

**Features:**
- Customer information display
- Service request details
- RV specifications (if available)
- Response action button

### Respond to Inquiry Modal
**File:** `apps/web/app/(main)/provider/(dashboard)/jobs/[id]/(components)/RespondToInquiryModal.tsx`

**Features:**
- Three response options (interested, not available, request info)
- Message composition
- Pricing display toggle
- Professional guidance

## Data Flow

### Lead Response Flow
1. Provider clicks lead card → Lead Details Modal opens
2. Provider reviews details → Clicks "Respond to Inquiry"
3. Respond to Inquiry Modal opens → Provider selects response type
4. API call made to appropriate endpoint → Quote status updated
5. ProviderStatsService.updateProviderStats() called → Stats recalculated
6. Frontend refreshes data → Updated stats displayed

### Stats Update Flow
1. Quote status changes → Trigger stats update
2. ProviderStatsService calculates new metrics → Database updated
3. is_highly_responsive flag evaluated → Listing updated
4. Frontend fetches new stats → UI reflects changes

## Performance Considerations

### Database Optimization
- Indexes on `listing_id`, `status`, `invited_at`, `responded_at`
- Efficient date range queries for rolling stats
- Upsert operations for stats updates

### Caching Strategy
- Stats cached in database (not calculated on-demand)
- Periodic updates via cron job
- Real-time updates for immediate responses

### Scalability
- Batch processing for bulk stats updates
- Async operations for non-critical updates
- Graceful degradation if stats service fails

## Error Handling

### Frontend Fallbacks
```typescript
// Fallback to calculated stats if API fails
setStats({
  totalLeads: serviceRequests.length,
  responseRate: 0,
  avgResponseTime: 0,
  recentResponseRate: 0
});
```

### API Error Responses
- 404: Listing or stats not found
- 500: Internal server error with logging
- Graceful degradation for non-critical failures

## Testing

### Unit Tests
- ProviderStatsService calculation methods
- API endpoint validation
- Component rendering and interactions

### Integration Tests
- End-to-end lead response flow
- Stats calculation accuracy
- Database consistency

### Performance Tests
- Bulk stats update performance
- API response times under load
- Database query optimization

## Monitoring and Logging

### Key Metrics
- Response time calculations
- Stats update frequency
- API endpoint performance
- Error rates and types

### Logging
- Stats calculation events
- Provider response actions
- System errors and warnings
- Performance bottlenecks

## Deployment Considerations

### Database Migrations
- ProviderStats table creation
- is_highly_responsive column addition
- Index creation for performance

### Environment Variables
- Database connection strings
- API rate limiting
- Cron job scheduling

### Monitoring Setup
- Database performance monitoring
- API endpoint health checks
- Error tracking and alerting

## Future Enhancements

### Potential Improvements
- Real-time notifications for new leads
- Advanced filtering and search
- Response templates and automation
- Performance analytics dashboard
- Integration with external CRM systems

### Scalability Plans
- Microservice architecture for stats service
- Redis caching for frequently accessed data
- Horizontal scaling for high-traffic periods
- Advanced analytics and reporting features
```