# Provider Leads System

## Overview

The Provider Leads System enables RV service providers to manage and respond to customer service requests efficiently. The system tracks provider performance metrics and provides a streamlined workflow for lead management with comprehensive notification, timeline tracking, and job completion features.

## Lead Management Workflow

### 1. Lead Reception

- Providers receive leads through the RV Help platform
- Leads are categorized by quote status: Provider Invited, Provider Quoted, Customer Accepted, etc.
- Each lead contains customer information, service details, and RV specifications

### 2. Lead Review Process

- **Step 1**: Provider clicks on lead card to view details
- **Step 2**: Lead details modal displays comprehensive information:
  - Customer contact information
  - Service request details with full message
  - RV specifications (if available)
  - Location and timing information
- **Step 3**: Provider reviews information and clicks "Respond to Inquiry"

### 3. Response Options

Providers can respond to leads through the Respond to Inquiry modal with multiple options:

1. **Accept Job** - Provider wants to take the job
   - Can include pricing information and scheduling timeframe
   - Can offer RV Help Pro discounts
   - Sets quote status to "PROVIDER_ACCEPTED"
   - Sends email notification to customer
   - Creates timeline event
   - Can be auto-accepted if customer pre-confirmed

2. **Decline Job** - Provider cannot take the job
   - Professional decline with optional message and rejection reason
   - Sets quote status to "PROVIDER_DECLINED"
   - Updates provider stats

3. **Request More Information** - Provider needs additional details
   - Sends information request to customer via email
   - Creates quote message in system
   - Sets quote status to "PROVIDER_INFO_REQUESTED"
   - Customer receives detailed email with request

4. **Complete Job** (for accepted jobs) - Provider marks job as completed
   - Sets quote status to "PROVIDER_COMPLETED"
   - Can include resolution notes
   - Option to send invoice
   - Option to request customer review (with delay)
   - Sends completion email to customer
   - Creates timeline event
   - Updates provider stats

5. **Withdraw from Job** (for accepted jobs) - Provider withdraws after acceptance
   - Sets quote status to "PROVIDER_WITHDRAWN"
   - Sends withdrawal notification to customer
   - Creates timeline event

## Customer Quote Management

### Customer Actions

1. **Accept Quote** - Customer accepts a provider's proposal
   - Sets quote status to "CUSTOMER_ACCEPTED"
   - Changes job status to "IN_PROGRESS"
   - Automatically rejects all other quotes for the job
   - Sends acceptance notification to provider (email & SMS)
   - Sends rejection notifications to other providers
   - Creates timeline event
   - Updates warranty request if applicable

2. **Reject Quote** - Customer rejects a provider's proposal
   - Sets quote status to "CUSTOMER_REJECTED"
   - Creates timeline event
   - Allows customer to consider other quotes

## Message System

### Quote-Based Messaging

- Providers and customers can exchange messages within each quote
- Messages are categorized by type (TEXT, IMAGE, DOCUMENT, etc.)
- Real-time notifications sent when new messages are received
- Message read status tracking
- Support for attachments and metadata

### Notification System

- Email notifications for all message types
- SMS notifications for urgent updates
- Slack monitoring for platform operators
- Customizable notification preferences per user

## Performance Tracking

### Key Metrics

The system tracks provider performance across multiple time periods:

1. **Response Rate** - Percentage of leads that received a response
2. **Average Response Time** - Time from invitation to response (in hours)
3. **Total Leads** - Number of leads received
4. **Recent Performance** - 30-day rolling metrics
5. **Quote Acceptance Rate** - Percentage of submitted quotes that are accepted by customers
6. **Job Completion Rate** - Percentage of accepted jobs that are completed successfully
7. **Review Completion Rate** - Percentage of completed jobs that receive customer reviews

### Enhanced Analytics

With the comprehensive quote tracking system:

- **Precise Win Rate Tracking** - Exact identification of which quotes win jobs
- **Provider Performance Analysis** - Detailed metrics on quote-to-job conversion
- **Competitive Intelligence** - Understanding of market competition patterns
- **Quality Scoring** - Assessment of quote quality based on acceptance rates
- **Timeline Analysis** - Track time-to-completion and workflow efficiency
- **Customer Satisfaction** - Review request and completion tracking

### Time Windows

- **30 Days** - Primary focus for performance evaluation
- **90 Days** - Medium-term performance tracking
- **All Time** - Historical performance data

### Highly Responsive Status

Providers automatically receive "Highly Responsive" status when they meet:

- 80% or higher response rate (30-day)
- Average response time of 24 hours or less (30-day)

This status is displayed on their listing and can improve search ranking.

## Job and Quote Status Management

### Job Status Flow

1. **OPEN** - Initial status when job is created
2. **IN_PROGRESS** - Job is actively being worked on (when quote is accepted)
3. **COMPLETED** - Job was completed successfully
4. **CANCELLED** - Job was cancelled

### Quote Status Flow

1. **PROVIDER_INVITED** - Initial status when quote is sent to provider
2. **PROVIDER_ACCEPTED** - Provider submitted a quote/proposal
3. **PROVIDER_DECLINED** - Provider declined the opportunity
4. **PROVIDER_INFO_REQUESTED** - Provider requested more information
5. **CUSTOMER_ACCEPTED** - Customer accepted the provider's quote
6. **CUSTOMER_REJECTED** - Customer rejected the provider's quote
7. **PROVIDER_WITHDRAWN** - Provider withdrew after initial response
8. **PROVIDER_COMPLETED** - Provider marked the job as completed
9. **INVITATION_REVOKED** - Invitation was revoked by system/customer
10. **TIMED_OUT** - Provider did not respond within time limit

### Quote Acceptance Tracking

When a customer accepts a provider's quote:

- The job status automatically changes to **IN_PROGRESS**
- The system records which specific quote was accepted via `accepted_quote_id`
- This creates a permanent link between the job and the winning quote
- Enables detailed reporting on quote acceptance rates by provider
- Facilitates accurate performance tracking and provider analytics

### Timeline Integration

Every major action creates timeline events:

- Quote submissions and responses
- Customer acceptances/rejections
- Job completions and withdrawals
- Information requests
- Status changes

Timeline events include:

- Event type classification
- User attribution
- Detailed notes
- Timestamp tracking
- Warranty request linking (when applicable)

### Status-Based Filtering

Providers can filter leads by status using tab navigation:

- Invited (PROVIDER_INVITED status)
- Quoted (PROVIDER_ACCEPTED status)
- Accepted (CUSTOMER_ACCEPTED status)
- Declined (PROVIDER_DECLINED status)
- All (complete overview)

## Customer Information Display

### Required Information

- Customer name and contact details
- Service category and description
- Location information
- Request timestamp

### Optional Information

- RV specifications (year, make, model, type)
- Warranty request details
- Additional customer preferences

## Notification System

### Multi-Channel Notifications

1. **Email Notifications**
   - Quote submissions and responses
   - Customer acceptances/rejections
   - Job completions
   - Information requests
   - Withdrawal notifications

2. **SMS Notifications**
   - Critical updates for verified providers
   - Quote acceptances
   - Job assignments

3. **Slack Monitoring**
   - Platform team notifications
   - Lead processing monitoring
   - Error tracking and alerts

### Error Handling

- Failed SMS notifications automatically remove verification
- Email failures are logged but don't block operations
- Slack notifications are skipped in development/test environments

## Response Quality Guidelines

### Best Practices

1. **Prompt Response** - Respond within 24 hours for best results
2. **Professional Communication** - Clear, courteous messaging
3. **Detailed Information** - Include availability, pricing, and expertise
4. **Follow-up** - Maintain communication throughout the process
5. **Complete Jobs Properly** - Use resolution notes and request reviews
6. **Handle Withdrawals Professionally** - Provide clear reasons and alternatives

### Response Content

- Express interest and availability
- Provide clear pricing expectations
- Include relevant experience/expertise
- Ask clarifying questions if needed
- Offer RV Help Pro discounts when applicable
- Set realistic scheduling expectations

## Job Completion Workflow

### Provider Completion Process

1. **Mark as Complete** - Provider indicates job is finished
2. **Resolution Notes** - Optional details about work performed
3. **Invoice Option** - Choose whether to send invoice to customer
4. **Review Request** - Option to request customer review
   - Can set delay period (hours) before review request
   - Automated follow-up system

### Completion Tracking

- Completion timestamps for analytics
- Customer notification emails
- Timeline event creation
- Stats update for provider performance
- Integration with warranty request tracking

## Permission and Access Control

### Provider Permissions

- Can only respond to quotes assigned to their listing
- Can withdraw from jobs they've accepted
- Can complete jobs they're working on
- Cannot modify other providers' quotes

### Customer Permissions

- Can only accept/reject quotes for their jobs
- Can exchange messages with assigned providers
- Cannot see other customers' information

### Validation Rules

- Quotes can only be accepted if status is PROVIDER_ACCEPTED
- Jobs can only be completed if status is CUSTOMER_ACCEPTED
- Providers can only withdraw from accepted or quoted jobs
- Information requests are only valid for invited quotes

## Performance Optimization

### Improving Response Rate

1. Set up email notifications for new leads
2. Check leads dashboard regularly
3. Respond promptly to all inquiries
4. Maintain accurate availability information

### Improving Response Time

1. Enable mobile notifications
2. Set aside dedicated time for lead review
3. Use saved response templates
4. Prioritize leads by urgency/category

### Building Reputation

1. Complete RV Help verification
2. Maintain detailed service descriptions
3. Collect positive customer reviews
4. Keep profile information current
5. Use professional completion notes
6. Request reviews systematically

## Business Impact & Benefits

### For Providers

- **Enhanced Performance Visibility** - Clear tracking of quote success rates
- **Competitive Analysis** - Understanding of win/loss patterns against competitors
- **Revenue Attribution** - Direct link between marketing efforts and job wins
- **Quality Improvement** - Data-driven insights for improving quote strategies
- **Professional Workflow** - Structured completion and review process
- **Customer Relationship Management** - Built-in messaging and communication

### For Platform Operations

- **Accurate Analytics** - Precise measurement of platform conversion rates
- **Provider Ranking** - Enhanced algorithms for provider search ranking
- **Market Intelligence** - Better understanding of service request patterns
- **Revenue Tracking** - Improved attribution of platform fees to successful matches
- **Quality Assurance** - Timeline tracking ensures accountability
- **Operational Monitoring** - Slack integration for real-time insights

### For Customers

- **Transparent Process** - Clear tracking of their service request journey
- **Quality Assurance** - System ensures accountability throughout the process
- **Better Matching** - Improved provider recommendations based on acceptance data
- **Communication Tools** - Direct messaging with providers
- **Completion Tracking** - Clear job completion workflow

## Technical Integration

### Core Services

- **QuoteStatusService** - Centralized quote status management
- **JobNotificationService** - Multi-channel notification handling
- **LeadNotificationService** - Initial lead processing and notifications
- **LeadFollowUpService** - Automated follow-up email system
- **ProviderStatsService** - Performance metrics calculation

### API Endpoints

- `/api/jobs` - Create and retrieve jobs
- `/api/jobs/[id]` - Get specific job details
- `/api/jobs/[id]/quotes/[quoteId]/accept` - Customer accepts specific quote
- `/api/jobs/[id]/quotes/[quoteId]/reject` - Customer rejects specific quote
- `/api/jobs/[id]/quotes/[quoteId]/messages` - Message exchange
- `/api/provider/quotes` - Retrieve quotes by status
- `/api/provider/quotes/[id]/accept` - Provider accepts job
- `/api/provider/quotes/[id]/complete` - Provider completes job
- `/api/provider/quotes/[id]/request-info` - Provider requests information
- `/api/provider/quotes/[id]/messages` - Provider messaging
- `/api/provider/stats` - Get performance metrics

### Data Model Architecture

- **Job Table**: Core service request with customer details and job status
- **Quote Table**: Provider responses to jobs with individual quote status
- **QuoteMessage Table**: Message exchange between providers and customers
- **Timeline Table**: Event tracking for all major actions
- **Quote Relationships**: Direct link between jobs and their accepted quotes
- **Referential Integrity**: Foreign key constraints ensure data consistency
- **Unique Constraints**: One-to-one relationship between jobs and accepted quotes

### Message Queue Integration

- Lead processing through queue system
- Notification delivery via background jobs
- Email and SMS sending through queued workers
- Invoice delivery and follow-up automation

### Data Updates

- Stats are updated in real-time when providers respond
- Quote acceptance automatically links winning quotes to jobs
- Periodic recalculation ensures accuracy
- Performance metrics influence search ranking
- Enhanced analytics available through accepted quote tracking
- Timeline events provide audit trail

## Support and Troubleshooting

### Common Issues

- **Missing lead notifications** - Check email settings and spam filters
- **Stats not updating** - Refresh page or contact support
- **Response not sending** - Verify internet connection and try again
- **Message delivery failures** - Check notification preferences
- **Quote status conflicts** - Contact support for manual resolution

### Error Handling

- Automatic retry for failed notifications
- Graceful degradation when services are unavailable
- Comprehensive logging for debugging
- Admin tools for manual intervention

### Getting Help

- Contact provider support for technical issues
- Review help documentation for best practices
- Attend provider training sessions for advanced features

Questions? Contact provider support for assistance.
