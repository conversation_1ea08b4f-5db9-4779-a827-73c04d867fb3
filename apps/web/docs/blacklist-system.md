# Blacklist System

The blacklist system is designed to prevent spam leads and block abusive users from accessing the platform. It provides comprehensive blocking capabilities at multiple levels.

## Features

### 1. Multi-Level Blocking
- **Email Addresses**: Block specific email addresses
- **Domains**: Block entire email domains (e.g., `spamdomain.com`)
- **User IDs**: Block specific user accounts
- **IP Addresses**: Block specific IP addresses

### 2. Custom Messages
- Each blacklist entry can include a custom message that will be shown to blocked users
- Messages are displayed on the banned user page

### 3. Expiration Support
- Blacklist entries can have optional expiration dates
- Expired entries are automatically ignored

### 4. Comprehensive Integration
- **Lead Submission**: Prevents blacklisted emails/domains from submitting leads
- **Site Access**: Blocks blacklisted users and IPs from accessing the site
- **Middleware**: Real-time checking on every page load

## Admin Interface

### Access
Navigate to `/admin/settings/blacklist` to manage blacklist entries.

### Adding Entries
1. Click "Add New Blacklist Entry"
2. Select the type (EMAIL, DOMAIN, USER_ID, IP_ADDRESS)
3. Enter the value to block
4. Provide a reason (required)
5. Optionally add a custom message for users
6. Set an expiration date if needed

### Managing Entries
- View all blacklist entries in a table format
- See entry details including type, value, reason, and status
- Remove entries by clicking the "Remove" button

## Technical Implementation

### Database Schema
```sql
model BlacklistEntry {
    id         String       @id @default(cuid())
    type       BlacklistType
    value      String       @unique
    reason     String       @db.Text
    message    String?      @db.Text
    created_at DateTime     @default(now())
    created_by String?
    expires_at DateTime?
    is_active  Boolean      @default(true)
}
```

### API Endpoints
- `GET /api/admin/blacklist` - List all entries
- `POST /api/admin/blacklist` - Add new entry
- `DELETE /api/admin/blacklist?id={id}` - Remove entry
- `POST /api/blacklist/check` - Check if value is blacklisted

### Services
- `BlacklistService` - Core service for blacklist operations
- Integration with lead processing to prevent spam submissions
- Middleware integration for real-time access control

## Usage Examples

### Blocking a Spam Domain
```typescript
await BlacklistService.addEntry({
    type: "DOMAIN",
    value: "spamdomain.com",
    reason: "Multiple spam lead submissions",
    message: "This domain has been blocked due to spam activity"
});
```

### Blocking a Specific User
```typescript
await BlacklistService.addEntry({
    type: "USER_ID",
    value: "user123",
    reason: "Abusive behavior",
    message: "Your account has been suspended for violating our terms of service"
});
```

### Checking if Blocked
```typescript
const result = await BlacklistService.checkEmailAccess("<EMAIL>");
if (result.isBlacklisted) {
    console.log("Blocked:", result.message);
}
```

## Integration Points

### 1. Lead Processing
The lead processing worker checks emails and user IDs before creating leads:
- Prevents blacklisted emails from submitting leads
- Blocks leads from blacklisted user accounts

### 2. API-Level Blocking
**NEW**: Direct API endpoint blocking prevents blacklisted users from even reaching the queue:
- `/api/leads` - Blocks blacklisted emails before queuing
- `/api/leads/phone` - Blocks blacklisted emails and user IDs before processing
- Returns 403 status with custom message
- Prevents unnecessary queue processing

### 3. Middleware
The application middleware checks on every request:
- IP address blacklist checking
- User ID blacklist checking for authenticated users
- Redirects to `/banned` page with custom message

### 4. Banned User Page
- Located at `/banned`
- Displays custom messages from blacklist entries
- Provides contact information for appeals

## Best Practices

1. **Use Domain Blocking**: When dealing with spam, block the entire domain rather than individual emails
2. **Provide Clear Messages**: Include helpful messages explaining why users are blocked
3. **Set Expiration Dates**: Use temporary blocks when appropriate
4. **Monitor Usage**: Regularly review blacklist entries for effectiveness
5. **Document Reasons**: Always provide clear reasons for blacklist entries

## Security Considerations

- Blacklist checks are performed at multiple levels for defense in depth
- IP blocking helps prevent access even for unauthenticated users
- User ID blocking prevents account reuse after email changes
- All blocking is logged for audit purposes

## Troubleshooting

### Common Issues
1. **Users still accessing site**: Check if IP address is being properly detected
2. **Leads still being submitted**: Verify blacklist service is properly integrated
3. **False positives**: Review blacklist entries and adjust as needed

### Testing
Run the test suite to verify functionality:
```bash
npm test -- blacklist.service.test.ts
``` 