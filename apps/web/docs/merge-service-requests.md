# Merge Service Requests Documentation

## Overview

The merge service requests functionality allows administrators to consolidate duplicate or related service requests to maintain data integrity and improve user experience. This feature is available in two contexts:

1. **Manual Merging** - From the Admin Leads page ("Group by Job" view)
2. **Automatic Merging** - From the Admin Users page (bulk merge by user)

## Manual Merging (Admin Leads Page)

### Access
- Navigate to **Admin → Leads**
- Switch to **"Group by Job"** view
- Select multiple jobs using checkboxes
- Click **"Merge Selected"** button

### Process
1. **Selection**: Check the boxes next to jobs you want to merge (minimum 2 jobs required)
2. **Dialog**: A merge dialog appears showing:
   - Selected jobs with details
   - Matching fields highlighted (email, phone, category, location)
   - Option to choose the primary job
3. **Confirmation**: Review and confirm the merge

### What Gets Merged
- **Quotes**: All quotes from merged jobs are transferred to the primary job
- **Quote Messages**: All associated quote messages are preserved
- **Timeline Updates**: All timeline updates are moved to the primary job
- **Warranty Information**: Warranty request IDs are preserved
- **Accepted Quotes**: Accepted quotes take priority over declined ones
- **Transaction Data**: Payment information is transferred if primary job doesn't have it

### Quote Conflict Resolution
When quotes from the same provider exist in multiple jobs:
- **Status Priority**: ACCEPTED > IN_PROGRESS > COMPLETED > PENDING > REJECTED/DECLINED
- **Tiebreaker**: Most recent activity timestamp (`responded_at` or `invited_at`)
- **Quote Messages**: Associated messages are deleted with the lower-priority quote

## Automatic Merging (Admin Users Page)

### Access
- Navigate to **Admin → Users**
- Find the user with duplicate service requests
- Click **"Additional Actions"** dropdown
- Select **"Merge Service Requests"**

### Process
1. **Confirmation Dialog**: Shows detailed information about what will be merged
2. **Automatic Grouping**: System groups service requests by:
   - **Same day** (creation date)
   - **Same category** (e.g., RV_REPAIR, INSPECTION)
   - **Same RV make and model**
3. **Merge Execution**: Each group is merged into the oldest service request

### Merge Criteria
Service requests are automatically grouped and merged if they meet ALL criteria:
- **Same user**
- **Same creation date** (YYYY-MM-DD)
- **Same service category**
- **Same RV make and model**

### What Gets Preserved
- **All quotes and quote messages** (with conflict resolution)
- **Timeline updates**
- **Warranty request IDs** (always preserved)
- **Accepted quotes** (highest priority)
- **Most recent activity timestamps**
- **Premium flags** and **fraud flags**
- **Transaction IDs** (if primary job doesn't have one)

## Data Preservation Rules

### Priority System
1. **Warranty Requests**: Always preserved if any job has one
2. **Accepted Quotes**: Highest priority in quote conflicts
3. **Status Priority**: COMPLETED > IN_PROGRESS > ASSIGNED > OPEN > CANCELLED > EXPIRED
4. **Timestamps**: Most recent activity dates are preserved

### Conflict Resolution
- **Same Provider Quotes**: Higher status priority wins, timestamp as tiebreaker
- **Warranty Requests**: First one found is preserved
- **Accepted Quotes**: First one found is preserved
- **Transaction IDs**: First one found is preserved

## Safety Features

### Confirmation Required
- Both manual and automatic merging require explicit confirmation
- Clear warnings about irreversible nature of the action
- Detailed explanation of what will be merged and preserved

### Data Integrity
- All operations use database transactions for atomicity
- Foreign key constraints are properly handled
- Quote messages are deleted before quotes to prevent constraint violations

### Error Handling
- Comprehensive error messages for failed operations
- Rollback on transaction failure
- User-friendly error notifications

## Use Cases

### Manual Merging Scenarios
- **Duplicate submissions**: User accidentally submits the same request multiple times
- **Related requests**: Multiple requests for the same RV issue
- **Data cleanup**: Consolidating fragmented service history

### Automatic Merging Scenarios
- **Bulk cleanup**: User has many duplicate requests from the same day
- **System migration**: Cleaning up data after system updates
- **User assistance**: Helping users who frequently create duplicates

## Best Practices

### Before Merging
1. **Review the data**: Ensure jobs are actually duplicates
2. **Check quotes**: Verify no important quote information will be lost
3. **Consider timing**: Avoid merging during active quote negotiations

### After Merging
1. **Verify results**: Check that all important data was preserved
2. **Notify users**: If necessary, inform users about consolidated requests
3. **Monitor**: Watch for any issues with the merged service request

## Technical Details

### Database Operations
- Uses Prisma transactions for data consistency
- Handles foreign key relationships properly
- Preserves audit trails through timeline updates

### API Endpoints
- **Manual Merge**: `POST /api/admin/jobs/merge`
- **Automatic Merge**: `POST /api/admin/users/merge-service-requests`

### Performance Considerations
- Operations are performed in batches for large datasets
- Database indexes are utilized for efficient queries
- Transaction timeouts prevent long-running operations

## Troubleshooting

### Common Issues
1. **"No service requests found"**: User has no jobs to merge
2. **"Failed to merge"**: Database constraint violation or network error
3. **Missing data**: Check if quotes were properly transferred

### Recovery Options
- **No automatic recovery**: Merged data cannot be automatically restored
- **Database backup**: Restore from backup if critical data was lost
- **Manual recreation**: Recreate lost data if necessary

## Support

For questions or issues with the merge functionality:
1. Check this documentation first
2. Review the error messages in the admin interface
3. Contact the development team with specific error details
4. Provide user ID and job IDs when reporting issues 