# RV Tech Marketplace - Jobs & Quotes Technical Specifications

## System Overview

This document outlines the technical implementation of the Jobs & Quotes workflow system for the RV Tech Marketplace. The system enables seamless connections between RV owners and qualified technicians through a structured quote-based marketplace.

## Database Schema

### Core Models

#### Job Model

- **Purpose**: Primary job/service request entity
- **Status Lifecycle**: `OPEN` → `IN_PROGRESS` → `COMPLETED` or `CANCELLED`
- **Key Fields**:
  - Customer contact info (name, email, phone, contact_preference)
  - RV details (year, make, model, type)
  - Service details (category, message, location)
  - Scheduling (preferred_date, preferred_time_slot, alternative_dates)
  - Relations to User, Transaction, WarrantyRequest, Quote[]

#### Quote Model

- **Purpose**: Individual provider responses to jobs
- **Status Lifecycle**: `INVITED` → `ACCEPTED`/`DECLINED` → `ACCEPTED`/`REJECTED`/`WITHDRAWN`
- **Key Fields**:
  - Communication (message, last_viewed_at)
  - Review tracking (review_requested, review_delay_hours)
  - Relations to Job, Listing, QuoteMessage[]

#### QuoteMessage Model

- **Purpose**: Bi-directional messaging between customers and providers
- **Message Types**: `TEXT`, `IMAGE`, `DOCUMENT`, `SYSTEM`
- **Status Tracking**: `PENDING` → `SENT` → `DELIVERED` → `READ`
- **Sender Types**: `USER` (customer) or `LISTING` (provider)

## API Architecture

### Customer Endpoints

#### Job Management

- `POST /api/jobs` - Create new job
- `GET /api/jobs` - List customer's jobs
- `GET /api/jobs/[id]` - Get job details with quotes
- `POST /api/jobs/[id]/quotes/[quoteId]/accept` - Accept a quote
- `POST /api/jobs/[id]/quotes/[quoteId]/reject` - Reject a quote

#### Messaging

- `POST /api/jobs/[id]/quotes/[quoteId]/messages` - Send message
- `POST /api/jobs/[id]/quotes/[quoteId]/messages/mark-as-read` - Mark messages as read

### Provider Endpoints

#### Quote Management

- `GET /api/provider/quotes` - List quotes (with status filtering)
- `GET /api/provider/quotes/[id]` - Get specific quote details
- `POST /api/provider/quotes/[id]/accept` - Submit quote response
- `POST /api/provider/quotes/[id]/withdraw` - Withdraw quote
- `POST /api/provider/quotes/[id]/complete` - Mark job as complete

#### Messaging

- `POST /api/provider/quotes/[id]/messages` - Send message to customer
- `POST /api/provider/quotes/[id]/messages/mark-as-read` - Mark messages as read

## Workflow Implementation

### Phase 1: Job Submission

```
1. Customer submits job via SendMessageModal
2. System creates User record if not logged in (upsert by email)
3. Job record created with status: OPEN
4. Quote record created with status: PROVIDER_INVITED (for direct invites)
5. Job processing job queued for notifications
```

### Phase 2: Provider Response

```
1. Provider views job details via /provider/jobs/[id]
2. Provider can:
   - Accept with quote details (scheduling_timeframe, message)
   - Decline with optional reason
   - Request more info via messaging
   - Indicate whether they have already spoken with the customer or not
3. Quote status updated: PROVIDER_INVITED → PROVIDER_ACCEPTED/PROVIDER_DECLINED/PROVIDER_REQUESTED_ADDITIONAL_INFO
4. Customer notification sent
```

### Phase 3: Customer Selection

```
1. Customer reviews quotes on /service-requests/[id]
2. Customer can:
   - Accept quote
   - Reject quote
   - Message provider for clarification
3. Accepted quote status: PROVIDER_ACCEPTED → PROVIDER_ACCEPTED
4. Reject quote: PROVIDER_ACCEPTED → CUSTOMER_REJECTED
5. Job status: OPEN → IN_PROGRESS
```

### Phase 4: Work Execution

```
1. Provider and customer communicate via messaging system
2. Provider can update progress (future enhancement)
3. Timeline updates occur automatically
4. Provider marks job complete
5. Job status: IN_PROGRESS → COMPLETED
6. Review request system triggered (if enabled)
```
