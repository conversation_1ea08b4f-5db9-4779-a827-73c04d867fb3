# RV Help Jobs System - Email Templates by User Journey

## Table of Contents

1. [Free Customer Journey](#free-customer-journey)
2. [Paid Customer Journey](#paid-customer-journey)
3. [Provider Journey](#provider-journey)
4. [Shared Templates](#shared-templates)

---

## Free Customer Journey

### 1. Job Submission Confirmation

**Template:** `LeadConfirmationEmail.tsx`
**Triggered:** When customer submits a job request
**Subject:** "Message Sent Successfully"

**Content:**

```
Message Sent Successfully

Hi [firstName],

Your message has been sent to [providerName] in [city, state]. They will contact you via [contactMethod] as soon as possible.

Your Contact Information:
- Name: [firstName lastName]
- Email: [email]
- Phone: [phone]
- Preferred Contact Method: [contactMethod]
- Service Type: [categoryName]

Your Message:
[message]

This is an automated email. Please do not reply to this message as it is not monitored.
```

### 2. General Follow-Up Email (24-hour)

**Template:** `LeadFollowUpEmail.tsx`
**Triggered:** 24 hours after job submission for ALL customers regardless of membership
**Subject:** "How's Your Service Request Going?" or "We're here to help with your service request"

**Content:**

```
[Dynamic subject based on response status]

Hi [firstName],

[Dynamic message based on provider status]:

NO RESPONSES:
"We wanted to check in on your [category] service request. We noticed that no providers have responded yet, but don't worry - this can happen during busy periods. We're here to help you find the right provider for your needs."

PROVIDER ACCEPTED:
"We noticed that [providerName] in [city, state] accepted your service request. Were they able to get in touch with you yet?"

PROVIDER REJECTED:
"We noticed that [providerName] in [city, state] was unable to accept your service request. Have you found another service provider? If not, we'd be happy to help connect you with someone else who can assist with your [category] needs."

PROVIDER PENDING:
"We noticed that one of our providers hasn't responded to your service request yet. Have you been able to find someone to help with your [category] needs? If not, we'd be happy to help connect you with another service provider."

[If no responses - Tips section:]
Here are a few tips to get faster responses:
• Make sure your request includes specific details about the issue
• Consider expanding your search radius to include more providers
• Pro members get priority placement and can invite up to 5 providers

[Find Providers in Your Area] or [Find Another Provider]

Your Original Request Details:
- Name: [firstName lastName]
- Email: [email]
- Phone: [phone]
- Preferred Contact Method: [contactMethod]
- Service Type: [category]

Your Original Message:
[message]

This is an automated email. Please feel free to reply to this <NAME_EMAIL> if you have any questions or need further assistance.
```

### 3. No Provider Response (72-hour escalation)

**Template:** `NoResponseEmail.tsx`
**Triggered:** When no providers respond after 72 hours
**Subject:** "We're sorry no one has responded to your service request yet - but we have options for you!"

**Content:**

```
💡 No Response Yet? We Have Options for You!

Hi [customerName],

We wanted to reach out because no providers have responded to your service request yet. Don't worry - this happens sometimes, and we have great options to help you get the service you need.

Here's what you can do:

🚀 Upgrade to Pro for Priority Placement
Get faster responses and invite up to 5 providers to your request

🔍 Find Another Provider
Search our directory and contact providers directly - it's completely free

[Learn About Pro Membership] [Find Another Provider]

Best regards,
The RV Help Team
```

### 4. Provider Rejection

**Template:** `LeadRejectionEmail.tsx`
**Triggered:** When a provider declines the job
**Subject:** "Update on your service request to [providerName]"

**Content:**

```
Service Request Update

Hi [firstName],

We wanted to let you know that [providerName] in [city, state] is unable to help with your service request at this time.

Reason:
[Reason varies by rejection type]:
- Too busy: "The provider is currently at capacity and unable to take on additional work at this time."
- Not a good fit: "The provider is not a good fit or does not offer the type of service you are looking for."
- Schedule conflict: "The provider has a scheduling conflict and is unable to help with your request."
- Outside travel area: "Your location is outside of the provider's service area."
- Other: [custom message]

Provider Comments: [details if provided]

You can visit our website to search for other providers in your area who may be able to assist you with your [category] needs.

RV Help Can Help Get You Connected:
As part of our RV Help Pro Membership, we offer a concierge service where our team will personally match you with a qualified technician in your area. If you're interested in this service, please visit our pro membership page at https://rvhelp.com/pro-membership.

Thank you for using RV Help. We're committed to connecting you with the right service provider for your needs.

This is an automated email. Please do not reply to this message as it is not monitored.
```

### 5. Quote Received

**Template:** `QuoteReceivedEmail.tsx`
**Triggered:** When a provider responds with a quote
**Subject:** "[providerName] has responded to your service request"

**Content:**

```
New Response Received!

Hi [customerName],

Great news! [providerName] from [providerLocation] has responded to your service request and is available to help.

Availability: They can schedule your service [schedulingTimeframe].

Message from [providerName]:
"[message]"

You can now review their profile, compare with other providers, and decide if you'd like to move forward.

[View Response & Respond]

Best regards,
The RV Help Team
```

### 6. Provider Withdrawal

**Template:** `QuoteWithdrawnEmail.tsx`
**Triggered:** When a provider withdraws their proposal
**Subject:** "RV Help: An update on your service request"

**Content:**

```
Proposal Withdrawn

Hi [customerName],

We wanted to let you know that [providerName] has withdrawn their proposal for your recent service request.

The reason provided was that [reason]:
- too_busy: "they are currently too busy to take on new work."
- schedule_conflict: "they have a scheduling conflict."
- outside_travel_area: "this request is outside of their travel area."
- not_a_good_fit: "this is not the type of work they perform."
- other: "of another reason."

[Provider message if provided]

We will continue to search for other qualified technicians in your area. You can view the status of your request and any other proposals you receive by clicking the button below.

[View Service Request]

If you need help finding another service provider, our team is here to assist you.

Best regards,
The RV Help Team
```

### 7. Additional Information Request

**Template:** `QuoteInfoRequestEmail.tsx`
**Triggered:** When a provider needs more information
**Subject:** "Additional Information Needed for Your Service Request"

**Content:**

```
Additional Information Needed

Hello [firstName lastName],

[providerName] in [city, state] has reviewed your service request for [category] and needs some additional information to better assist you.

Provider Comments:
[infoRequestDetails]

[Respond to Request]

Click the button above to respond to this request. You can also view your service request details and send any additional information that might be helpful.

Best regards,
The RV Help Team

This is an automated email from an unmonitored email address. Please do not reply directly to this email.
```

---

## Paid Customer Journey

### 1. Job Submission Confirmation + Pro Offer

**Template:** `InitialOfferEmail.tsx`
**Triggered:** When paid customer submits a job request
**Subject:** "Your Service Request and a Special Offer"

**Content:**

```
Your Service Request has been Submitted!

Hello [customerName],

Thank you for submitting your service request. We've received it and are working on finding you the best mobile RV technicians in your area. You can view your request details and status at any time by clicking the button below.

[View Your Service Request]

---

Your One-Time Pro Offer
Expires in 72 hours!

To help you get your issue resolved faster, we're giving you a special one-time offer. For the next 72 hours, you can upgrade to a Pro membership for 50% off the first year.

Pro members can:
• Invite up to 5 providers to a job to faster responses.
• Access unlimited pre-service diagnostic calls.
• Receive nationwide discounts from providers.

[Claim 50% Off Now]

Or copy and paste this URL into your browser: [URL]
```

### 2. Pro Offer Reminder

**Template:** `OfferReminderEmail.tsx`
**Triggered:** 48 hours after job submission if not upgraded
**Subject:** "Your 50% Off Offer Expires Soon!"

**Content:**

```
Don't Miss Out on Your Pro Offer!

Hello [customerName],

This is a reminder that your special one-time offer to upgrade to a Pro membership for 50% off is expiring in just 24 hours.

Becoming a Pro Member may help you get your current issue resolved faster by inviting multiple providers to your service request, plus you'll unlock other great benefits.

Last Chance: 50% Off Pro
Expires in 24 hours!

Don't miss out. Upgrade now to get all the benefits of Pro for half the price for your first year.

[Claim 50% Off Now]

You can also view your original service request here: [URL]
```

### 3. Same 24-Hour Follow-Up as Free Users

**Template:** `LeadFollowUpEmail.tsx`
**Triggered:** 24 hours after job submission (same as free users)
**Note:** Pro members receive the same 24-hour follow-up email as free users

### 4. Enhanced No Provider Response (72-hour escalation)

**Template:** `NoResponseEmail.tsx` (Pro version)
**Triggered:** When no providers respond after 72 hours
**Subject:** "We're sorry no one has responded to your service request yet - but we have options for you!"

**Content:**

```
💡 No Response Yet? We Have Several Great Options for You!

Hi [customerName],

We wanted to reach out because no providers have responded to your service request yet. Don't worry - this happens sometimes, and we have several great options to help you get the service you need.

Here's what you can do:

🔄 Withdraw & Invite More Providers
Return to your workroom to withdraw current invitations and invite up to 5 new providers

🎧 Contact Our Support Team
Get personalized help from our concierge team during regular business hours to find the right provider for your needs

[STANDARD PRO:]
📞 Request Pre-Service Troubleshooting Call
Get a quick troubleshooting call to help determine the best next steps

[PREMIUM PRO:]
🔬 Request Virtual Diagnosis
Get expert remote diagnosis to understand your issue before booking service

🚨 Emergency Dispatch
For urgent situations, contact our emergency dispatch team for immediate assistance

🔍 Search More Providers
Browse our directory and contact providers directly

[Return to Workroom] [Contact Support] [Search Providers]

Best regards,
The RV Help Team
```

### 5. All Other Templates Same as Free

- Quote Received
- Provider Rejection
- Provider Withdrawal
- Additional Information Request

---

## Provider Journey

### 1. New Lead Notification

**Template:** `NewLeadEmail.tsx`
**Triggered:** When provider is invited to a job
**Subject:** "New Lead from [firstName lastName]"

**Content:**

```
New Lead Received

You have received a new lead for [category] in [city, state].

Click the button below to respond to this lead. You can accept, decline, or message the customer directly:

[Respond to Lead]

Message Preview:
[message]
```

### 2. Lead Reminder (24-hour)

**Template:** `LeadReminderEmail.tsx`
**Triggered:** 24 hours after lead invitation if no response
**Subject:** "Reminder: Pending Lead from [firstName lastName]"

**Content:**

```
Reminder: Pending Lead Needs Your Attention

Hello [providerName],

This is a friendly reminder that you have a pending lead from [firstName lastName] that was submitted 12 hours ago and is still waiting for your response.

To view the full lead details and take action, please click the button below:

[View Lead Details]

Lead Details:
- Name: [firstName lastName]
- Email: [email]
- Phone: [phone]
- Preferred Contact Method: [contactMethod]
- Service Type: [category]
- Location: [address]

Message:
[message]

This is an automated email. Please do not reply to this message as it is not monitored.
```

### 3. Proposal Accepted

**Template:** `ProposalAcceptedEmail.tsx`
**Triggered:** When customer accepts provider's proposal
**Subject:** "Your proposal has been accepted!"

**Content:**

```
Proposal Accepted!

Great news! [customerName] has accepted your proposal. You can now proceed with the service request.

Please ensure to:
• Contact the customer to confirm details
• Update the job status as you progress
• Mark the job as complete when finished

[View Job Details]

About Reviews:
[If review scheduled:]
An automated review request will be sent to the customer [X] hours after you mark the job as complete.

[If not scheduled:]
Once you complete the job, you can request a review from the customer through the job details page.

Thank you for providing your services through RV Help!
```

### 4. Proposal Rejected

**Template:** `ProposalRejectedEmail.tsx`
**Triggered:** When customer rejects provider's proposal
**Subject:** "RV Help: Service Request Update"

**Content:**

```
Service Request Update

Hi [providerName],

We wanted to let you know that [customerName] has decided to go in a different direction with their service request. While your proposal wasn't selected this time, we appreciate your timeliness in responding.

There are many more opportunities available in your area. We encourage you to continue submitting proposals for other service requests that match your expertise.

[View Service Request]

Keep checking your dashboard for new service requests in your area.
```

### 5. Job Filled by Another Provider

**Template:** `JobFilledEmail.tsx`
**Triggered:** When customer selects another provider
**Subject:** "RV Help: Service Request Update"

**Content:**

```
Service Request Update

We wanted to let you know that a service request you responded to has been filled. [customerName] has selected another service provider for this job.

Job Details:
• Type: [category]
• Vehicle: [year] [make] [model]
• Location: [address]

Thank you for responding to the service request. We encourage you to continue responding to other service requests in your area.

[View Lead Details]

Keep an eye on your dashboard for new opportunities in your area.
```

### 6. Job Completed (Sent to Customer)

**Template:** `JobCompletedEmail.tsx`
**Triggered:** When provider marks job as complete
**Subject:** "Service Complete - [providerName]"

**Content:**

```
Job Completed Successfully! 🎉

Great work! You've successfully completed the service request for [customerName].

Resolution Notes:
[resolutionNotes]

Thank you for providing excellent service through RV Help. We look forward to connecting you with more customers in need of your expertise.

Keep checking your dashboard for new service opportunities in your area.
```

---

## Shared Templates

### 1. New Message Notification

**Template:** `new-quote-message.tsx`
**Triggered:** When customer or provider sends a message
**Subject:** "New message from [senderName]"

**Content:**

```
New Message Received

Hi [recipientName],

You have received a new message from [senderName] regarding your service request:

"[messagePreview]"

[View and Reply]

For a quick response, we recommend replying directly through the RV Help platform.
```

---

## Email Flow Summary

### Free Customer Path:

1. **Job Submission** → LeadConfirmationEmail
2. **24-Hour Follow-Up** → LeadFollowUpEmail (dynamic based on status)
3. **No Response (72h)** → NoResponseEmail (limited options)
4. **Provider Responds** → QuoteReceivedEmail
5. **Provider Rejects** → LeadRejectionEmail
6. **Provider Withdraws** → QuoteWithdrawnEmail
7. **Provider Needs Info** → QuoteInfoRequestEmail
8. **Messaging** → new-quote-message.tsx

### Paid Customer Path:

1. **Job Submission** → InitialOfferEmail (with Pro upgrade offer)
2. **24-Hour Follow-Up** → LeadFollowUpEmail (same as free users)
3. **Pro Offer Reminder** → OfferReminderEmail (48h later)
4. **No Response (72h)** → NoResponseEmail (Pro features available)
5. **All other emails same as Free** → Same templates as free customers

### Provider Path:

1. **Job Invitation** → NewLeadEmail
2. **No Response (24h)** → LeadReminderEmail
3. **Proposal Accepted** → ProposalAcceptedEmail
4. **Proposal Rejected** → ProposalRejectedEmail
5. **Job Filled by Another** → JobFilledEmail
6. **Job Completed** → JobCompletedEmail
7. **Messaging** → new-quote-message.tsx

---

## Key Differences by User Type

### Free vs Paid Customer Emails:

- **24-Hour Follow-Up:** Both free and paid customers receive the same follow-up email
- **72-Hour No Response:** Free users get basic options (upgrade to Pro, search providers), paid users get enhanced options (workroom access, concierge support during business hours)
- **Pro Features:** Focus on self-service tools and upgrade incentives for free users; multiple provider access and business hours support for paid users

### Provider Emails:

- **Consistent experience** regardless of customer type
- **Performance tracking** affects future lead visibility
- **Review system integration** for reputation building

### Support Availability:

- **Free Users:** Self-service options only
- **Paid Users:** Concierge support during regular business hours only (not 24/7)
- **Emergency Dispatch:** Available for urgent situations (Premium Pro)

### Timing:

- **24-Hour Follow-Up:** All customers (free and paid) get the same timing
- **72-Hour Escalation:** All customers get escalation email, but with different content based on membership level
- **Provider Reminders:** 24-hour reminder system for all providers

This comprehensive email system ensures clear communication throughout the entire job lifecycle while providing appropriate upgrade incentives for free users and enhanced support options for paid customers during business hours.
