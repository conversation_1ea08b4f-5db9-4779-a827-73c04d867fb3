# Customer Email Templates - Job Lifecycle

This document outlines all customer-facing email templates used throughout the RVHelp job lifecycle, including their timing, purpose, and key content.

## Email Timeline Overview

1. **Job Creation (Day 0)** - Service Request Confirmation + Offer
2. **24-Business Hour Follow-up (Day 1)** - Lead Follow-up Email
3. **48-Direct Hour** - Offer Expiration Reminder
4. **48-Business Hour Escalation (Day 2)** - No Response Email
5. **1-Week Warning (Day 7)** - No Response Email (Last Chance)
6. **Job Expiration (Day 10)** - No email sent (status change only)

---

## 1. Service Request Confirmation Email

**Template**: `ServiceRequestConfirmationEmail.tsx`  
**Timing**: Immediately after job creation  
**Recipients**: FREE users only  
**Subject**: "Your Service Request has been Submitted!"

### Content Structure:

- **Header**: "Your Service Request has been Submitted!"
- **Confirmation**: Thank you message + working on finding providers
- **CTA Button**: "View Your Service Request"
- **Service Details Section**:
  - Service Type
  - Contact Method
  - Email & Phone
  - Customer Message
- **Special Offer Section**:
  - "Your One-Time Pro Offer"
  - "Expires in 72 hours!"
  - 50% off Pro membership
  - Pro benefits list
  - "Claim 50% Off Now" button

### Key Features:

- Combines confirmation + offer in single email
- Includes full service request details
- Clear call-to-action for Pro upgrade
- Mobile-responsive design

---

## 2. Lead Follow-up Email

**Template**: `LeadFollowUpEmail.tsx`  
**Timing**: 24 business hours after job creation  
**Recipients**: All customers  
**Subject**: "Update: Your [Category] Service Request"

### Smart Follow-up Types:

#### A. Select Provider (when customer has accepted quotes)

- **Subject**: "Action Required: Select Your Provider"
- **Content**: Customer has quotes to review and select from
- **CTA**: "Review and Select Provider"
- **Color**: Green theme

#### B. All Declined (when all providers declined)

- **Subject**: "Let's Find You Another Provider"
- **Content**: All providers unable to help, need to find new ones
- **CTA**: "Find More Providers" (FREE) or "Get Help Finding Providers" (PRO)
- **Color**: Blue theme

#### C. Awaiting Responses (still waiting for responses)

- **Subject**: "Update on Your Service Request"
- **Content**: Still waiting to hear back from providers
- **CTA**: "View Request Status"
- **Color**: Yellow theme

#### D. Mixed Responses (some declined, some pending)

- **Subject**: "Partial Response Update"
- **Content**: Partial responses received
- **CTA**: "View All Responses"
- **Color**: Orange theme

### Key Features:

- Dynamic content based on quote status
- Membership-specific messaging
- Provider details for accepted quotes
- Clear action items for each scenario

---

## 3. Offer Expiration Reminder

**Template**: `ServiceRequestConfirmationEmail.tsx` (same as initial)  
**Timing**: Exactly 48 hours after job creation  
**Recipients**: FREE users only  
**Subject**: "⏰ Your 50% off Pro offer expires in 24 hours!"

### Content:

- Same template as initial confirmation
- Focus on offer expiration urgency
- 24-hour countdown messaging
- Reinforces Pro benefits

### Key Features:

- Uses same template as initial email
- Emphasizes urgency with countdown
- Clear expiration timeline

---

## 4. No Response Email (48-Hour Escalation)

**Template**: `NoResponseEmail.tsx`  
**Timing**: 48 business hours after job creation  
**Recipients**: All customers  
**Subject**: "Your service request - let's get you help!"

### Content Structure:

- **Header**: "💡 No one to help with your service request? We Have Options for You!"
- **Situation**: Explains no responses received yet
- **Options Section**: Membership-specific solutions

#### FREE User Options:

- Search for Another Provider
- Upgrade to Pro for Better Results
- Pro benefits explanation

#### PRO User Options:

- Withdraw & Invite More Providers
- Contact Our Support Team
- Request Pre-Service Troubleshooting Call
- Emergency Dispatch
- Search More Providers

### Key Features:

- Membership-tiered messaging
- Multiple solution paths
- Clear upgrade incentives
- Support contact information

---

## 5. No Response Email (1-Week Last Chance)

**Template**: `NoResponseEmail.tsx` (same as 48-hour)  
**Timing**: 1 week after job creation  
**Recipients**: All customers  
**Subject**: "Last chance - Your service request needs attention"

### Content:

- Same template as 48-hour escalation
- Final warning before expiration
- Emphasizes urgency of situation
- Same membership-specific options

### Key Features:

- Identical to 48-hour email
- Positioned as "last chance"
- Creates urgency for action

---

## Email Template Files

### Location: `apps/web/components/email-templates/jobs/customer/`

1. **ServiceRequestConfirmationEmail.tsx** - Initial confirmation + offer
2. **LeadFollowUpEmail.tsx** - 24-hour smart follow-up
3. **NoResponseEmail.tsx** - 48-hour and 1-week escalations
4. **InitialOfferEmail.tsx** - Legacy (replaced by merged template)
5. **LeadConfirmationEmail.tsx** - Legacy (replaced by merged template)

---

## Technical Implementation

### Email Service Integration:

- Uses `emailService.send()` method
- React Email templates
- BaseEmail wrapper for consistent styling
- Mobile-responsive design

### Business Logic:

- Membership level filtering (FREE vs PRO)
- Quote status analysis for smart follow-ups
- Business day filtering for most emails
- Exact timing for offer reminders

### Database Tracking:

- `offer_reminder_sent_at` - Initial offer sent
- `offer_reminder_48h_sent_at` - 48-hour offer reminder
- `follow_up_sent_at` - 24-hour follow-up
- `reminder_48h_sent_at` - 48-hour escalation
- `reminder_1w_sent_at` - 1-week warning

---

## Email Design System

### Common Elements:

- **BaseEmail wrapper** for consistent styling
- **emailStyles** shared styling object
- **Responsive design** for mobile/desktop
- **Clear CTAs** with prominent buttons
- **Professional branding** with RVHelp colors

### Color Scheme:

- Primary: `#437F6B` (green)
- Secondary: `#16a34a` (lighter green for offers)
- Text: `#374151` (gray-700)
- Background: `#f9fafb` (gray-50)

### Typography:

- Headings: Bold, larger font sizes
- Body: Regular weight, readable line height
- CTAs: Bold, prominent styling
- Small text: Gray, secondary information

---

## Testing & Development

### Test Routes:

- `/api/test/cron` - Test individual email types
- Available test cases:
  - `lead-follow-up` - 24-hour follow-up
  - `job-48h-offer-reminder` - 48-hour offer
  - `job-48h-reminders` - 48-hour escalation
  - `job-1w-reminders` - 1-week warning

### Development Notes:

- All templates use React Email components
- Consistent error handling and logging
- Business day filtering implemented
- Membership-specific content logic
- Queue system for reliable delivery
