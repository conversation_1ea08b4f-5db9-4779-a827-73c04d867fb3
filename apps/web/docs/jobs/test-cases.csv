Actor,Action,Expected Result,Notifications,Status,Notes

Customer,Submit service request,Service request is created with status OPEN,"Customer: Email confirmation with link to service request page
Provider: New Job Notification (Email + SMS)",Pass,

Customer (Free),Attempt to invite multiple providers at once,System shows upgrade prompt,Customer: Email about Pro account benefits,Pass,

Customer (Free),Invite single provider,Provider is invited successfully,"Provider: SMS notification of new job
Provider: Email with full job details
Customer: Confirmation of invitation sent",Pass,

Customer (Pro),Invite multiple providers,All selected providers are invited (up to 5),"Each Provider: SMS notification of new job
Each Provider: Email with full job details
Customer: Confirmation of invitations sent",Pass,Pro members can invite up to 5 providers initially

Customer,Receive new quote,Quote appears in customer dashboard,Customer: Email with quote details,Pass,Dashboard needs redesign for better quote visibility

Customer,Accept quote,Job status changes to ASSIGNED,"Selected Provider: SMS and email confirmation
Other Providers: Email notification of job being filled",Pass,Job status is ASSIGNED when quote accepted

Customer,Reject quote,Quote status changes to REJECTED,Provider: Email notification of rejection,Pass,

Customer,Message provider (after invitation),Message thread updated,Provider: Email notification of new message,Pass,Messaging available after provider is invited

Customer,Accept job completion,Job status changes to COMPLETED,"Customer: Optional review request email
Provider: Completion confirmation",Pass,

Customer,Submit review (connected to service request),Verified review posted,Provider: Verified Review Received Email,Needs Implementation,Reviews should connect to completed jobs

Customer,24 hours after job posting with no responses,Customer receives 24-hour follow-up email,Customer: Check-in email about service request status with encouragement,Pass,Automated follow-up regardless of membership level

Customer,24 hours after job posting with some responses,Customer receives 24-hour follow-up email,Customer: Check-in email mentioning they have responses to review,Pass,Different messaging when responses are available

Customer (Free),No responses after 72 hours,Customer receives follow-up email with search guidance,"Customer: ""No Response Yet - Find Another Provider"" email with links to search providers and upgrade options",Pass,Free users directed to search for another provider after 72 hours

Customer (Standard Pro),No responses after 72 hours,Customer receives follow-up email with pro support options,"Customer: ""No Response Yet"" email with options to withdraw/re-invite providers, contact support, request troubleshooting call, emergency dispatch, and search providers",Pass,Standard Pro users get comprehensive support options after 72 hours

Customer (Premium Pro),No responses after 72 hours,Customer receives follow-up email with premium support options,"Customer: ""No Response Yet"" email with options to withdraw/re-invite providers, contact support, request virtual diagnosis, emergency dispatch, and search providers",Pass,Premium Pro users get virtual diagnosis option instead of troubleshooting call

Customer (Free),Click search link in 72hr follow-up email,Directed to provider search page,No additional notifications,Pass,Free users can search and contact providers directly

Customer (Pro),Click support link in 72hr follow-up email,Directed to support contact page,No additional notifications,Pass,Pro users can contact support team for assistance

Customer (Pro),Click workroom link in 72hr follow-up email,Directed to service request workroom,No additional notifications,Pass,Pro users can return to workroom to withdraw/re-invite providers

Provider,View job invitation,Invitation marked as viewed with timestamp,No notifications,Pass,

Provider,Request more information,Quote status remains PENDING,Customer: Email with provider's question,Pass,

Provider,Submit quote,Quote status set to ACCEPTED,Customer: Email with quote details and provider info,Pass,

Provider,Decline job invitation,Quote status changes to WITHDRAWN,Customer: Email notification with alternative search suggestions,Pass,

Provider,Withdraw quote (after submitting),Quote status changes to WITHDRAWN,Customer: Email notification with search guidance,Pass,

Provider,Mark job complete,Job marked as completed,"Customer: Email to confirm completion
Customer: Optional review request",Pass,

Provider,Message customer,Message thread updated,Customer: Email notification of new message,Pass,

Provider,24 hours after invitation with no response,Provider receives 24-hour reminder,Provider: SMS reminder and email reminder about pending invitation,Pass,Automated 24hr reminder for providers who haven't responded

Provider,24 hours after invitation with response already submitted,No reminder sent,No notifications,Pass,Providers who already responded don't get reminder emails

Provider,Respond to job invitation after 24hr reminder,Quote status updated and customer notified,Customer: Email with quote details noting provider responded,Pass,Late responses still processed normally

Provider (No response for 24hrs),System reminder triggered,Reminder sent,"Provider: SMS reminder
Provider: Email reminder",Pass,Automated 24hr reminder

Provider (No response for 72hrs),Quote marked as EXPIRED,Quote expires internally (customer doesn't see this),No notifications to provider,Pass,Internal status change only

Admin,Upgrade job to premium,Job is_premium field set to true,"Customer: Email notification of premium upgrade benefits with optional admin message and workroom CTA
Provider: Email notification of premium job status
Admin: Confirmation of upgrade applied",Pass,Premium jobs get priority visibility and enhanced features. Admin can include optional message in customer notification. Email includes prominent call-to-action to return to workroom to invite more providers.

System,24hr follow-up trigger (no responses),Customer follow-up email sent with encouragement,Customer: Check-in email about service request status with tips for success,Pass,Daily cron at 6 AM checks for 24hr old jobs with no responses

System,24hr follow-up trigger (has responses),Customer follow-up email sent with response notification,Customer: Check-in email mentioning responses are available for review,Pass,Daily cron at 6 AM checks for 24hr old jobs with responses

System,24hr provider reminder trigger,Provider reminder sent to non-responsive providers,"Provider: SMS reminder
Provider: Email reminder",Pass,Hourly cron checks for 24hr old pending quotes and sends reminders

System,72hr no response trigger,Customer follow-up email sent with membership-specific guidance,"Customer: ""No Response Yet"" email with different content based on membership level (Free: upgrade/search options, Pro: withdrawal/support/service options)",Pass,Daily cron at 7 AM - sends different email content based on user membership level

System,Job ASSIGNED beyond estimated completion,Flag for follow-up,"Admin: Dashboard alert
Customer: Follow-up survey",Needs Implementation, 