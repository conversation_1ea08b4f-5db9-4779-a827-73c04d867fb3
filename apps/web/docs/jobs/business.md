# RV Tech Marketplace - Jobs & Quotes Workflow Requirements

## Business Objectives

**Primary Goal**: Create a seamless marketplace that connects RV owners with qualified technicians while maintaining complete visibility into service request status and outcomes.

**Key Success Metrics**:

- Minimize abandoned/stale job requests
- Maximize tech response rates within 24 hours
- Achieve high job completion rates
- Maintain platform oversight of all service interactions

## Core Workflow Architecture

### 1. Job Submission Phase

**Customer Actions**:

- Submit job request with details (description, location, timeline, RV details)
- Invite specific techs (platform matching coming soon)

**System Actions**:

- Create job record with unique ID
- Set initial status: "Pending Tech Responses"
- Start response tracking timers
- Log all activity for audit trail

### 2. Tech Response & Selection Phase

**Tech Invitation Process**:

- Platform identifies qualified techs based on location, specialization, availability (coming soon)
- Send invitations via multiple channels (in-app, email, SMS)
- No fixed response deadline - ongoing opportunity to respond

**Tech Response Options**:

- **Accept**: Provide quote details (hourly rate, dispatch fee, estimated timeline, message)
- **Decline**: Optional reason for platform improvement
- **Request More Info**: Ask clarifying questions before committing

**Customer Selection Process (As-They-Come)**:

- Customer receives real-time notifications for each new quote
- Can review and select providers immediately as quotes arrive
- No waiting period - customers act when ready
- Can message providers for clarification before selection
- First acceptable quote can be selected immediately

**Status Updates**:

- Notify selected tech of acceptance
- Notify declined techs (maintain positive relationships)
- Update job status to "In Progress"

## Detailed Workflow States

### Job Status Lifecycle

1. **Open** - Job posted and active (maps to `JobStatus.OPEN`)
2. **Assigned** - Provider selected and confirmed (maps to `JobStatus.ASSIGNED`)
3. **In Progress** - Work actively happening (maps to `JobStatus.IN_PROGRESS`)
4. **Completed** - Service finished, payment processed (maps to `JobStatus.COMPLETED`)
5. **Cancelled** - Job terminated (maps to `JobStatus.CANCELLED`)

_Note: The system uses the `accepted_quote_id` field to track which specific quote was selected when a job moves to ASSIGNED status._

### Quote Response Status Tracking

- **Pending** - Initial invitation sent (maps to `QuoteStatus.PENDING`)
- **Quoted** - Tech provided quote details (maps to `QuoteStatus.ACCEPTED`)
- **Accepted** - Customer accepted this quote (maps to `QuoteStatus.ACCEPTED`)
- **Rejected** - Customer rejected this quote (maps to `QuoteStatus.REJECTED`)
- **Withdrawn** - Tech withdrew their quote (maps to `QuoteStatus.WITHDRAWN`)
- **Expired** - Quote timed out or was revoked (maps to `QuoteStatus.EXPIRED`)

_Additional tracking: Response timestamps, reminder tracking, and provider performance metrics._

## Notification Strategy

### SMS Notifications (High Priority/Time Sensitive)

**To Providers** (if SMS verified):

- New job invitation: "New lead from RV Help - Name: [Customer] [Category] in [City]"
- 24hr reminder: "RV Help Reminder: You have a pending lead from [Customer] that needs attention"
- Selection confirmation: "RV Help: Your proposal has been accepted! Click to view job details"

**To Customers**:

- Currently email-focused, with SMS capabilities available for future expansion

### Email Notifications (Comprehensive Coverage)

**To Providers**:

- Detailed job invitation with full customer info, RV details, service description
- 24hr reminder with job summary and response options
- Quote acceptance notification with customer contact details
- Job filled notification when another provider is selected
- Job completion confirmation

**To Customers**:

- Job posting confirmation with service request details
- Quote received notification for each provider response
- 24hr follow-up email checking on service request status
- 72hr escalation email with job expiration and re-invitation options
- Provider rejection notifications with alternative suggestions
- Service completion confirmations
- Pro membership upgrade prompts when relevant

### In-App Notifications

**Universal**:

- Real-time status updates
- Message center for provider-customer communication
- Dashboard alerts for pending actions
- Service request status tracking
- Provider response notifications

### Automated Notification Schedule

**Currently Active Cron Jobs**:

- **Provider Reminders**: Every hour (`/api/cron/remind-providers`)
- **Customer Follow-ups**: Daily at 6 AM (`/api/cron/lead-follow-up`)
- **48-Hour Offer Reminders**: Every hour (`/api/cron/job-48h-offer-reminder`)
- **48-Business Hour Job Reminders**: Daily at 7 AM (`/api/cron/job-expiration`)
- **1-Week Job Reminders**: Daily at 8 AM (`/api/cron/job-1w-reminders`)
- **Job Final Expiration**: Daily at 9 AM (`/api/cron/job-final-expiration`)
- **Pro Membership Offers**: Hourly (`/api/cron/offers`)
- **Stats Updates**: Every 6 hours

**Notification Timing**:

- **Initial notifications**: Immediate upon job posting
- **Provider reminders**: 24 hours after invitation if no response
- **Customer follow-up**: 24 hours after job posting
- **48-hour offer expiration reminder**: Exactly 48 hours after job posting (FREE users only)
- **48-business hour escalation**: 48 business hours after job posting (if no responses)
- **Customer final warning**: 1 week after job posting (if no responses)
- **Job expiration**: 10 days after job posting (if no responses)
- **Quote responses**: Real-time when providers respond
- **Job status changes**: Immediate notification to relevant parties

**Queue System**:

- QStash integration for reliable delivery
- Automatic retry logic for failed notifications
- Graceful handling of SMS verification failures
- Comprehensive error logging and admin alerts

## 10-Day Job Expiration & Re-invitation Workflow

### Job Expiration Criteria

A job is eligible for expiration and re-invitation when:

- Job has been OPEN for 10+ days
- No providers have submitted quotes (all quotes still PENDING or WITHDRAWN)
- Job has not been cancelled by customer
- Customer has received their 1-week reminder

### Automatic Actions at 10-Day Mark

1. **Job Status Update**: Mark job as EXPIRED
2. **Customer Notification**: Job is automatically expired
3. **Job Status Display**: Show as "Expired - No Responses Received" in customer dashboard
4. **Provider Re-invitation**: Enable self-service provider search and invitation

### Customer Options After Expiration

**Free Tier Self-Service**:

- Browse and invite additional providers from marketplace
- Search by location, specialty, and availability
- Send up to 2 additional invitations without Pro upgrade
- Access to expanded provider pool beyond initial matches

**Upgrade Path Available**:

- Pro membership concierge service for hands-on assistance
- Up to 5 additional provider invitations (vs 2 for free tier)
- Guaranteed provider matching within 48 hours
- Priority placement with high-performing providers

### Technical Implementation

```typescript
// Cron job: Daily at 7 AM
- Find jobs OPEN for 10+ days with no ACCEPTED responses
- Mark job as EXPIRED
- Update job display status for customer
- Enable expanded provider search interface
```

### Customer Communication Timeline

**24-Hour Follow-up**: Initial status update and guidance
**72-Hour Escalation**: "Let's get you help" with options
**1-Week Warning**: "Last chance" before expiration
**10-Day Expiration**: Job automatically expires

## Loop-Closing & Status Management

### Automated Status Monitoring

**Job Health Monitoring**:

- Track job age and activity levels for administrative oversight
- Monitor quote response patterns (no rigid deadlines)
- Identify jobs with low engagement for potential assistance

**Progress Monitoring**:

- Require status updates from techs at booking confirmation
- Send completion prompts to both parties post-service date
- Flag jobs stuck in "In Progress" for manual follow-up

**Engagement Support**:

- Identify customers who may need additional provider options
- Proactive outreach: "Still need help with your RV service?"
- Offer additional tech matches or concierge assistance
- Optional: Gentle tech engagement nudges (not deadlines)

### Manual Intervention Triggers

**Platform Action Required When**:

- Job shows no activity for extended periods (configurable threshold)
- Job receives no responses from multiple invited providers
- Job remains in "In Progress" beyond estimated completion + 3 days
- Customer or provider reports issues/disputes
- Customer requests additional provider options

## Customer Support & Escalation System

### Free Tier Support Options

1. **Self-Service Tools**
   - Provider search and marketplace browsing
   - Direct messaging with providers
   - Service request status tracking
   - Help articles and FAQ resources

2. **Basic Support**
   - Email <NAME_EMAIL>
   - Support modal in customer dashboard
   - Automated follow-up emails at 24 hours
   - Provider rejection notifications with alternatives

### Pro Membership Support ($49/month)

1. **Concierge Support**
   - Dedicated support team intervention
   - Personal assistance finding qualified providers
   - Appointment scheduling help
   - Priority response guarantee

2. **Enhanced Services**
   - 3 free virtual diagnosis sessions ($297 value)
   - Direct phone support access
   - Proactive outreach for stale requests
   - Provider relationship management

### Automatic Support Triggers

The system automatically offers upgraded support when:

- No provider responses after 24 hours
- Provider rejects the job request
- Customer indicates need for additional help
- Job stalls in any status for extended periods

### Escalation Matrix

```
Level 1: Customer self-service (search, direct contact)
Level 2: Automated follow-up emails and guidance
Level 3: Pro membership concierge intervention
Level 4: Administrative manual assistance
```

### Success Metrics & KPIs

**Response Metrics**:

- Overall tech response rate (target: >80%)
- Average time to first tech response (target: <24 hours)
- Customer engagement rate with received quotes (target: >85%)

**Completion Metrics**:

- Job completion rate (target: >90%)
- Average time from posting to selection
- Average time from selection to completion
- Customer satisfaction scores
- Tech satisfaction and retention rates

**Platform Health**:

- Percentage of jobs reaching "Completed" status
- Average job lifecycle duration
- Abandonment rate at each stage
- Repeat customer and tech usage

## Quality Assurance

### Customer Protection

- Verify tech credentials and insurance
- Escrow payment system until completion
- Review and rating system
- Dispute resolution process

### Tech Protection

- Clear job specifications and expectations
- Transparent customer communication
- Fair payment terms and dispute process
- Protection from fraudulent bookings

### Platform Protection

- Complete audit trail of all interactions
- Automated fraud detection
- Clear terms of service enforcement
- Regular data backups and security measures

## Implementation Phases

### Phase 1: Core Workflow

- Job posting and tech invitation system
- Basic notification framework
- Simple status tracking

### Phase 2: Enhanced Communications

- Multi-channel notification system
- Automated reminders and follow-ups
- In-app messaging between parties

### Phase 3: Advanced Management

- Predictive analytics for job success
- Automated quality scoring
- Advanced reporting and insights

### Phase 4: Optimization

- AI-powered tech matching
- Dynamic pricing recommendations
- Predictive intervention for at-risk jobs

## Risk Mitigation

**Technical Risks**:

- Notification delivery failures (multiple channel backup)
- System downtime (status preservation and recovery)
- Data integrity (regular backups and validation)

**Business Risks**:

- Tech non-responsiveness (larger invitation pools)
- Customer abandonment (proactive engagement)
- Service quality issues (verification and review systems)

**Operational Risks**:

- Manual intervention capacity (clear escalation procedures)
- Customer service demands (self-service tools and FAQs)
- Regulatory compliance (terms of service and data protection)

## Success Indicators

**Short-term (3 months)**:

- 90% of jobs receive at least one tech response
- 70% of posted jobs result in confirmed bookings
- 85% of booked jobs reach completion

**Long-term (12 months)**:

- 95% job completion rate
- 4.5+ average customer satisfaction rating
- 80% customer repeat usage within 12 months
- 90% tech retention rate

## Next Steps

1. **Technical Architecture**: Design database schema and API endpoints
2. **Notification Infrastructure**: Set up SMS/email service providers
3. **User Interface**: Create intuitive dashboards for all user types
4. **Testing Framework**: Develop comprehensive testing protocols
5. **Analytics Setup**: Implement tracking for all defined KPIs
