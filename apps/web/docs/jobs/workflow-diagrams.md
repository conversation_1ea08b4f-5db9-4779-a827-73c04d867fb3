# RV Help Jobs & Quotes System - Visual Flow Diagrams

This document contains comprehensive visual flow diagrams for the RV Help jobs and quotes system. These Mermaid diagrams can be rendered in GitHub, GitLab, or converted to other formats using tools like Mermaid CLI or online converters.

---

## 1. Complete Job Lifecycle Flow

```mermaid
graph TD
    A[Customer Submits Job Request] --> B[Job Created - OPEN Status]
    B --> C[System Creates Quotes for Providers]
    C --> D[Providers Notified - Email + SMS]

    D --> E{Provider Response?}

    %% No Response Path
    E -->|No Response - 24hr| F[Automated Reminder Sent]
    F --> G{Still No Response?}
    G -->|Yes| H[Quote Status: EXPIRED]
    G -->|No| I[Provider Responds]

    %% Provider Response Path
    E -->|Responds| I[Provider Responds]
    I --> J{Response Type?}

    J -->|Submit Quote| K[Quote Status: ACCEPTED]
    J -->|Decline Job| L[Quote Status: WITHDRAWN]
    J -->|Request Info| M[Quote Status: PENDING]

    %% Customer Notification & Decision
    K --> N[Customer Notified - New Quote Email]
    L --> O[Customer Notified - Rejection Email]
    M --> P[Customer Contacted for More Info]

    N --> Q{Customer Decision?}
    Q -->|Accept Quote| R[Quote Status: ACCEPTED]
    Q -->|Reject Quote| S[Quote Status: REJECTED]

    %% Job Progression
    R --> T[Job Status: ASSIGNED]
    T --> U[Provider Completes Work]
    U --> V[Job Status: COMPLETED]

    %% Support Escalation Paths
    S --> W{Other Quotes Available?}
    W -->|Yes| X[Continue with Other Quotes]
    W -->|No| Y[Customer Support Options]

    O --> Y
    H --> Z[Customer Follow-up Email - 24hr]
    Z --> AA1{Any Responses by 72hr?}
    AA1 -->|No| BB1[Job Expiration Process]
    AA1 -->|Yes| Q

    BB1 --> CC1[Mark Quotes as EXPIRED - Internal Only]
    CC1 --> DD1[Send "Let's Find More Providers" Email]
    DD1 --> EE1[Enable Re-invitation Interface]
    EE1 --> Y

    %% Support Options
    Y --> AA{Customer Membership?}
    AA -->|Free| BB[Self-Service Options]
    AA -->|Pro| CC[Concierge Support]

    BB --> DD[Search More Providers]
    BB --> EE[Contact Basic Support]
    BB --> FF[Upgrade to Pro Prompt]

    CC --> GG[Dedicated Team Assistance]
    CC --> HH[Priority Provider Matching]
    CC --> II[Virtual Diagnosis Service]

    %% Return Paths
    X --> Q
    DD --> JJ[New Provider Invitations]
    FF --> CC
    JJ --> D

    %% Completion Paths
    V --> KK[Review Request Sent]
    KK --> LL[Process Complete]

    style A fill:#e1f5fe
    style V fill:#c8e6c9
    style Y fill:#fff3e0
    style CC fill:#f3e5f5
```

---

## 2. 72-Hour Job Expiration & Re-invitation Flow

```mermaid
graph TD
    A[Job Created - 72hr Timer Starts] --> B[24hr: Customer Follow-up Email]
    B --> C{Provider Responses by 72hr?}

    C -->|Yes| D[Continue Normal Flow]
    C -->|No| E[72hr Cron Job Triggers]

    E --> F[Find Eligible Jobs]
    F --> G[Mark PENDING Quotes as EXPIRED]
    G --> H[Update Job Status Display]
    H --> I[Send Expiration Email to Customer]
    I --> J[Enable Re-invitation Interface]

    J --> K[Customer Dashboard Shows: No Responses Yet - Let's Find More Providers]
    K --> L{Customer Action?}

    L -->|Browse Providers| M[Expanded Provider Search]
    L -->|Get Pro Help| N[Upgrade to Concierge]
    L -->|No Action| O[Remains Available for Later]

    M --> P{Free Tier User?}
    P -->|Yes| Q[Up to 2 Free Additional Invites]
    P -->|No| R[Up to 5 Pro Invites]

    Q --> S[Invite New Providers]
    R --> S
    N --> T[Personal Support Team Assistance]

    S --> U[New Quotes Created - PENDING Status]
    U --> V[Providers Notified via Email/SMS]
    V --> W[Return to Normal Workflow]

    T --> X[Guaranteed Provider Matching]
    X --> W

    %% Styling
    style E fill:#fff3e0
    style I fill:#e3f2fd
    style N fill:#f3e5f5
    style Q fill:#e8f5e8
```

---

## 3. Customer Experience Journey Map

```mermaid
journey
    title Customer Job Request Journey
    section Job Submission
      Submit Request: 5: Customer
      Receive Confirmation: 4: Customer
      Wait for Responses: 3: Customer
    section Provider Responses
      Receive Quote Notifications: 4: Customer
      Review Provider Options: 4: Customer
      Select Provider: 5: Customer
    section Service Delivery
      Communicate with Provider: 4: Customer
      Receive Service: 5: Customer
      Leave Review: 4: Customer
    section Support (If Needed)
      No Response Scenario: 2: Customer
      Contact Support: 3: Customer
      Get Concierge Help: 5: Customer
```

---

## 4. Provider Workflow Diagram

```mermaid
graph LR
    A[Provider Receives Invitation] --> B{Check Job Details}

    B --> C{Interested & Available?}

    C -->|Yes| D[Submit Quote]
    C -->|No| E[Decline with Reason]
    C -->|Need More Info| F[Request Additional Details]

    D --> G[Quote Status: ACCEPTED]
    E --> H[Quote Status: WITHDRAWN]
    F --> I[Customer Contacted]

    G --> J{Customer Response?}
    J -->|Accepts| K[Quote Status: ACCEPTED]
    J -->|Rejects| L[Quote Status: REJECTED]
    J -->|No Response| M[Waiting Status]

    K --> N[Begin Communication]
    N --> O[Schedule & Perform Service]
    O --> P[Mark Job Complete]
    P --> Q[Request Customer Review]

    %% Performance Tracking
    D --> R[Response Time Tracked]
    E --> R
    F --> R
    R --> S[Provider Stats Updated]

    %% No Response Consequences
    A --> T{Responds Within 24hr?}
    T -->|No| U[Reminder Sent]
    U --> V{Still No Response?}
    V -->|No| B
    V -->|Yes| W[Performance Impact]
    W --> X[Quote Status: EXPIRED]

    style A fill:#e3f2fd
    style K fill:#c8e6c9
    style L fill:#ffcdd2
    style P fill:#dcedc8
    style W fill:#ffe0b2
```

---

## 5. Support Escalation Matrix

```mermaid
graph TD
    A[Customer Needs Help] --> B{Issue Type?}

    B -->|No Provider Response| C[24hr Follow-up Email]
    B -->|Provider Rejected| D[Rejection Notification]
    B -->|General Question| E[Support Modal/Email]
    B -->|Service Issue| F[Direct Communication Tools]

    C --> G{Customer Membership?}
    D --> G
    E --> G
    F --> G

    G -->|Free Tier| H[Self-Service Options]
    G -->|Pro Member| I[Concierge Support]

    H --> J[Provider Search]
    H --> K[Help Articles]
    H --> L[Basic Email Support]
    H --> M[Pro Upgrade Prompt]

    I --> N[Dedicated Support Team]
    I --> O[Priority Provider Matching]
    I --> P[Virtual Diagnosis Sessions]
    I --> Q[Direct Phone Support]

    %% Success Outcomes
    J --> R[Find Alternative Provider]
    N --> S[Personal Assistance]
    O --> T[Guaranteed Provider Match]

    %% Upgrade Path
    M --> I

    %% Admin Escalation
    L --> U{Complex Issue?}
    S --> U
    U -->|Yes| V[Admin Manual Review]
    U -->|No| W[Standard Resolution]

    style A fill:#ffebee
    style I fill:#f3e5f5
    style V fill:#e8f5e8
    style T fill:#e1f5fe
```

---

## 6. Notification System Flow

```mermaid
graph TD
    A[System Event Triggered] --> B{Event Type?}

    %% Initial Notifications
    B -->|Job Created| C[Send Confirmation Email]
    B -->|Provider Invited| D[Send Job Invitation]
    B -->|Quote Submitted| E[Send Quote Notification]
    B -->|Quote Accepted| F[Send Success Notifications]

    %% Automated Follow-ups
    B -->|24hr No Response| G[Provider Reminder Job]
    B -->|24hr Job Follow-up| H[Customer Check-in Email]
    B -->|Job Completion| I[Completion Notifications]

    %% Notification Channels
    C --> J[Email Queue]
    D --> K{Provider SMS Verified?}
    E --> J
    F --> L[Multi-party Notifications]

    K -->|Yes| M[SMS + Email]
    K -->|No| N[Email Only]

    %% Queue Processing
    J --> O[QStash Queue]
    M --> O
    N --> O
    L --> O

    O --> P{Delivery Successful?}
    P -->|Yes| Q[Mark as Sent]
    P -->|No| R[Retry Logic]

    R --> S{Max Retries Reached?}
    S -->|No| O
    S -->|Yes| T[Log Error & Alert Admin]

    %% Scheduled Jobs
    G --> U[Cron: Every Hour]
    H --> V[Cron: Daily 6 AM]
    U --> W[Process Pending Reminders]
    V --> X[Process Follow-ups]

    %% Error Handling
    T --> Y[Slack Admin Notification]
    Y --> Z[Manual Intervention if Needed]

    style O fill:#e1f5fe
    style Q fill:#c8e6c9
    style T fill:#ffcdd2
    style Y fill:#fff3e0
```

---

## 7. Status State Machine

```mermaid
stateDiagram-v2
    [*] --> JobOpen

    state "Job Status Flow" as JobFlow {
        JobOpen --> JobAssigned : Customer accepts quote
        JobAssigned --> JobInProgress : Provider starts work
        JobInProgress --> JobCompleted : Provider completes
        JobOpen --> JobCancelled : Job cancelled
        JobAssigned --> JobCancelled : Job cancelled
        JobInProgress --> JobCancelled : Job cancelled
    }

    state "Quote Status Flow" as QuoteFlow {
        [*] --> QuotePending
        QuotePending --> QuoteQuoted : Provider submits quote
        QuotePending --> QuoteWithdrawn : Provider declines
        QuotePending --> QuoteExpired : No response timeout
        QuoteQuoted --> QuoteAccepted : Customer accepts
        QuoteQuoted --> QuoteRejected : Customer rejects
        QuoteAccepted --> [*] : Job completion
        QuoteRejected --> [*] : Final state
        QuoteWithdrawn --> [*] : Final state
        QuoteExpired --> [*] : Final state
    }

    JobCompleted --> [*]
    JobCancelled --> [*]
```

---

## 8. Provider Performance Tracking

```mermaid
graph TD
    A[Provider Action] --> B{Action Type?}

    B -->|Response to Invitation| C[Track Response Time]
    B -->|Quote Submission| D[Track Quote Quality]
    B -->|Job Completion| E[Track Completion Rate]
    B -->|No Response| F[Track Non-Response]

    C --> G[Update Response Rate Stats]
    D --> H[Update Quote Acceptance Rate]
    E --> I[Update Completion Rate]
    F --> J[Update Performance Score]

    G --> K{Response Time < 24hr?}
    K -->|Yes| L[Positive Performance Impact]
    K -->|No| M[Negative Performance Impact]

    H --> N{Quote Accepted?}
    N -->|Yes| L
    N -->|No| O[Neutral Impact]

    I --> P[Always Positive Impact]
    J --> Q[Always Negative Impact]

    L --> R[Calculate Overall Score]
    M --> R
    O --> R
    P --> R
    Q --> R

    R --> S{Score Threshold Met?}
    S -->|Yes| T[Highly Responsive Badge]
    S -->|No| U[Standard Status]

    T --> V[Improved Search Ranking]
    U --> W[Standard Search Position]

    %% Admin Alerts
    Q --> X{Score Below Minimum?}
    X -->|Yes| Y[Admin Alert for Review]
    X -->|No| Z[Continue Monitoring]

    style T fill:#c8e6c9
    style Y fill:#ffcdd2
    style V fill:#e8f5e8
```

---

## Usage Instructions

### Rendering These Diagrams

1. **GitHub/GitLab**: These diagrams will render automatically in markdown files
2. **Mermaid Live Editor**: Copy any diagram to [mermaid-live-editor.github.io](https://mermaid-live-editor.github.io)
3. **VS Code**: Install the Mermaid Preview extension
4. **Export Options**: Use Mermaid CLI or online tools to export as PNG, SVG, or PDF

### Converting to Other Formats

```bash
# Install Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# Convert to PNG
mmdc -i workflow-diagrams.md -o workflow-diagrams.png

# Convert to SVG
mmdc -i workflow-diagrams.md -o workflow-diagrams.svg

# Convert to PDF
mmdc -i workflow-diagrams.md -o workflow-diagrams.pdf
```

### Customization

Each diagram can be customized by:

- Modifying node styles (colors, shapes)
- Adding or removing flow paths
- Adjusting text and labels
- Changing layout direction

These diagrams provide a comprehensive visual representation of your jobs and quotes system workflow, suitable for documentation, presentations, or team onboarding.
