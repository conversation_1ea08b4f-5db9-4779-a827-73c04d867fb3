"use client";

import { Loader2 } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";

export default function OfferEligibilityPage() {
	const searchParams = useSearchParams();
	const jobId = searchParams?.get("jobId");
	const userId = searchParams?.get("userId");

	useEffect(() => {
		const checkEligibility = async () => {
			if (!jobId || !userId) {
				// Redirect to pro-membership without discount if missing params
				window.location.href = "/pro-membership";
				return;
			}

			try {
				const response = await fetch("/api/offer-eligibility/check", {
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({ jobId, userId })
				});

				const data = await response.json();

				if (response.ok) {
					// Redirect to pro-membership with user ID for server-side validation
					if (data.eligible) {
						window.location.href = `/pro-membership?userId=${userId}&jobId=${jobId}`;
					} else {
						// No discount available
						window.location.href = "/pro-membership";
					}
				} else {
					// Fallback to pro-membership without discount
					window.location.href = "/pro-membership";
				}
			} catch (error) {
				console.error("Error checking eligibility:", error);
				// Fallback to pro-membership without discount
				window.location.href = "/pro-membership";
			}
		};

		checkEligibility();
	}, [jobId, userId]);

	return (
		<div className="min-h-screen bg-gray-50 flex items-center justify-center">
			<div className="max-w-md w-full mx-auto text-center">
				<div className="bg-white rounded-lg shadow-lg p-8">
					<div className="flex justify-center mb-4">
						<Loader2 className="h-8 w-8 animate-spin text-blue-600" />
					</div>
					<h1 className="text-xl font-semibold text-gray-900 mb-2">
						Checking Your Offer Eligibility
					</h1>
					<p className="text-gray-600">
						Please wait while we determine your available discounts...
					</p>
				</div>
			</div>
		</div>
	);
}
