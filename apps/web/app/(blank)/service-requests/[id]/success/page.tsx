"use client";

import { But<PERSON> } from "@/components/ui/button";
import config from "@/config";
import { useAuth } from "@/lib/hooks/useAuth";
import { JobWithUserAndLocation, QuoteWithListing } from "@/types/global";
import { CheckCircle, DollarSign, Shield, Star, Users } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

export default function ServiceRequestSuccessPage({
	params
}: {
	params: { id: string };
}) {
	const router = useRouter();
	const { user, loading: authLoading, isPaid } = useAuth();

	const [job, setJob] = useState<JobWithUserAndLocation | null>(null);
	const [quote, setQuote] = useState<QuoteWithListing | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [timeLeft, setTimeLeft] = useState({
		days: 0,
		hours: 0,
		minutes: 0,
		seconds: 0
	});
	const [isExpired, setIsExpired] = useState(false);
	const [isCheckingOut, setIsCheckingOut] = useState(false);
	const [reviewCount, setReviewCount] = useState<number | null>(null);

	// Fetch review count
	useEffect(() => {
		fetch("/api/reviews/count")
			.then((res) => res.json())
			.then((data) => setReviewCount(data.total))
			.catch(() => setReviewCount(null));
	}, []);

	// Fetch job data
	useEffect(() => {
		const fetchJob = async () => {
			try {
				const response = await fetch(`/api/jobs/${params.id}`);
				const data = await response.json();
				if (data) {
					setJob(data);
					setQuote(data.quotes[0]);
				}
			} catch (error) {
				console.error("Error fetching service request:", error);
				toast.error("Failed to load service request details");
				router.push("/dashboard");
			} finally {
				setIsLoading(false);
			}
		};

		fetchJob();
	}, [params.id, router]);

	// Check if user is eligible for discount
	const [offerEligibilityCheck, setOfferEligibilityCheck] = useState<{
		eligible: boolean;
		reason?: string;
	}>({ eligible: false });

	// Check offer eligibility on page load
	useEffect(() => {
		const checkOfferEligibility = () => {
			if (!job?.id) {
				return;
			}

			if (!job?.user) {
				return;
			}

			// Use the job's user data instead of current session user
			const jobUser = job.user;

			// Simple eligibility check:
			// 1. User must be FREE tier
			// 2. Job must be within 72 hours
			// 3. User hasn't used their annual offer yet

			// Check if user is FREE tier
			if (jobUser.membership_level !== "FREE") {
				setOfferEligibilityCheck({
					eligible: false,
					reason: "User has paid membership"
				});
				// Clear any existing discount eligibility
				localStorage.removeItem("rvhelp_discount_eligibility");
				return;
			}

			// Check if job is within 72 hours
			const now = new Date();
			const jobCreated = new Date(job.created_at);
			const hoursElapsed =
				(now.getTime() - jobCreated.getTime()) / (1000 * 60 * 60);

			if (hoursElapsed > 72) {
				setOfferEligibilityCheck({
					eligible: false,
					reason: "Offer expired (72-hour window)"
				});
				// Clear any existing discount eligibility
				localStorage.removeItem("rvhelp_discount_eligibility");
				return;
			}

			// User is eligible!
			setOfferEligibilityCheck({ eligible: true });

			// Store eligibility in localStorage for use when navigating to pro-membership page
			const discountData = {
				eligible: true,
				jobId: job.id,
				userId: jobUser.id,
				jobCreatedAt: job.created_at,
				expiresAt: new Date(
					jobCreated.getTime() + 72 * 60 * 60 * 1000
				).toISOString()
			};
			localStorage.setItem(
				"rvhelp_discount_eligibility",
				JSON.stringify(discountData)
			);
		};

		checkOfferEligibility();
	}, [user, authLoading, job?.id, job?.created_at, job?.user]);

	// Timer countdown
	useEffect(() => {
		if (!job?.created_at) return;

		const expirationTime =
			new Date(job.created_at).getTime() + 72 * 60 * 60 * 1000;

		const updateTimer = () => {
			const now = new Date().getTime();
			const distance = expirationTime - now;

			if (distance < 0) {
				setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
				setIsExpired(true);
				return;
			}

			const days = Math.floor(distance / (1000 * 60 * 60 * 24));
			const hours = Math.floor(
				(distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
			);
			const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
			const seconds = Math.floor((distance % (1000 * 60)) / 1000);

			setTimeLeft({ days, hours, minutes, seconds });
			setIsExpired(false);
		};

		updateTimer();
		const timer = setInterval(updateTimer, 1000);
		return () => clearInterval(timer);
	}, [job?.created_at]);

	// If user is already logged in and has paid membership, redirect to service request
	useEffect(() => {
		if (!isLoading && !authLoading && user && isPaid) {
			router.push(`/service-requests/${params.id}`);
		}
	}, [isLoading, authLoading, user, isPaid, params.id, router]);

	// Calculate discounted prices
	const calculateDiscountedPrice = (originalPrice: number) => {
		if (!offerEligibilityCheck.eligible) return originalPrice;
		return Math.round(originalPrice * 0.5); // 50% discount
	};

	const upgradeToProMembership = async (priceId: string) => {
		try {
			setIsCheckingOut(true);

			const response = await fetch("/api/stripe/checkout", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					priceId,
					applyDiscount: offerEligibilityCheck.eligible && !isExpired,
					serviceRequestId: job?.id,
					email: job?.user?.email
				})
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Something went wrong");
			}

			// Redirect to Stripe Checkout
			window.location.href = data.url;
		} catch (error) {
			console.error("Checkout error:", error);
			toast.error("Failed to start checkout process. Please try again.");
		} finally {
			setIsCheckingOut(false);
		}
	};

	const handleDecline = () => {
		// Simply redirect to password setup page
		router.push(`/service-requests/${params.id}/setup-password`);
	};

	if (isLoading || authLoading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="max-w-md w-full mx-auto text-center">
					<div className="bg-white rounded-lg shadow-lg p-8">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
						<h1 className="text-xl font-semibold text-gray-900 mb-2">
							Loading Your Offer
						</h1>
						<p className="text-gray-600">
							Please wait while we prepare your exclusive upgrade offer...
						</p>
					</div>
				</div>
			</div>
		);
	}

	if (!job) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="max-w-md w-full mx-auto text-center">
					<div className="bg-white rounded-lg shadow-lg p-8">
						<h1 className="text-xl font-semibold text-gray-900 mb-2">
							Service Request Not Found
						</h1>
						<p className="text-gray-600 mb-4">
							We couldn't find the service request for this offer.
						</p>
						<Button onClick={() => router.push("/dashboard")}>
							Return to Dashboard
						</Button>
					</div>
				</div>
			</div>
		);
	}

	const providerImages = [
		"/images/home/<USER>",
		"/images/home/<USER>",
		"/images/home/<USER>",
		"/images/home/<USER>"
	];

	return (
		<div className="min-h-screen bg-gray-50 overflow-x-hidden">
			{/* Special Offer Banner */}
			{offerEligibilityCheck.eligible && !isExpired && (
				<div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white relative">
					<div className="max-w-7xl mx-auto px-4 py-3 sm:py-4">
						{/* Centered main content */}
						<div className="text-center">
							<h3 className="font-semibold text-base sm:text-lg">
								🎉 Special Limited-Time Offer!
							</h3>
							<p className="text-green-100 text-sm sm:text-base">
								Save 50% on your first year of Pro membership
							</p>
						</div>

						{/* Absolute positioned expiration timer */}
						<div className="absolute top-1/2 right-4 transform -translate-y-1/2 text-right hidden md:block">
							<div className="text-sm text-green-100">Expires in</div>
							<div className="font-bold text-xl whitespace-nowrap">
								{timeLeft.days > 0 && `${timeLeft.days}d `}
								{timeLeft.hours}h {timeLeft.minutes}m {timeLeft.seconds}s
							</div>
						</div>

						{/* Mobile expiration timer - shown below main content */}
						<div className="text-center mt-2 md:hidden">
							<div className="text-sm text-green-100">Expires in</div>
							<div className="font-bold text-base sm:text-lg">
								{timeLeft.days > 0 && `${timeLeft.days}d `}
								{timeLeft.hours}h {timeLeft.minutes}m {timeLeft.seconds}s
							</div>
						</div>
					</div>
				</div>
			)}

			{/* Main Content */}
			<div className="max-w-7xl mx-auto px-4 py-6 sm:py-8">
				{/* Big Success Header */}
				<div className="text-center mb-8 sm:mb-12">
					<div className="relative">
						{/* Success Icon */}
						<div className="mx-auto mb-4 sm:mb-6 w-16 h-16 sm:w-24 sm:h-24 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-2xl animate-pulse">
							<svg
								className="w-8 h-8 sm:w-12 sm:h-12 text-white"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={3}
									d="M5 13l4 4L19 7"
								/>
							</svg>
						</div>

						{/* Main Success Message */}
						<div className="space-y-3 sm:space-y-4">
							<h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 mb-3 sm:mb-4">
								🎉 Success!
							</h1>
							<h2 className="text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-800 mb-4 sm:mb-6">
								Your Service Request Has Been Sent
							</h2>
							<div className="max-w-2xl mx-auto px-4">
								<p className="text-base sm:text-lg lg:text-xl text-gray-600 leading-relaxed">
									We've successfully sent your service request to{" "}
									{quote.listing.business_name}.
									<span className="font-semibold text-green-600">
										{" "}
										You should hear from them soon!
									</span>
								</p>
							</div>
						</div>

						{/* Decorative Elements */}
						<div
							className="absolute -top-2 sm:-top-4 -left-2 sm:-left-4 w-6 h-6 sm:w-8 sm:h-8 bg-yellow-400 rounded-full animate-bounce"
							style={{ animationDelay: "0.5s" }}
						></div>
						<div
							className="absolute -top-1 sm:-top-2 -right-4 sm:-right-8 w-4 h-4 sm:w-6 sm:h-6 bg-blue-400 rounded-full animate-bounce"
							style={{ animationDelay: "1s" }}
						></div>
						<div
							className="absolute -bottom-2 sm:-bottom-4 left-4 sm:left-8 w-4 h-4 sm:w-5 sm:h-5 bg-pink-400 rounded-full animate-bounce"
							style={{ animationDelay: "1.5s" }}
						></div>
						<div
							className="absolute -bottom-1 sm:-bottom-2 -right-2 sm:-right-4 w-5 h-5 sm:w-7 sm:h-7 bg-purple-400 rounded-full animate-bounce"
							style={{ animationDelay: "2s" }}
						></div>
					</div>
				</div>

				{/* Transition Message */}
				<div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border border-blue-200 p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 text-center">
					<h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">
						Think RV Help is just a directory? Think again.
					</h3>
					<p className="text-base sm:text-lg text-gray-700 max-w-3xl mx-auto">
						While you wait for responses, discover how our{" "}
						<span className="font-semibold text-primary">Pro Membership</span>{" "}
						transforms RV Help from a simple directory into your{" "}
						<span className="font-semibold text-secondary">
							complete RV support system
						</span>
						.
					</p>
				</div>

				{/* Hero Section */}
				<div className="bg-gradient-to-br from-white via-blue-50 to-green-50 rounded-lg shadow-sm border mb-6 sm:mb-8">
					<div className="p-4 sm:p-6 lg:p-8">
						<div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 items-center">
							<div className="space-y-4 sm:space-y-6">
								{/* Badge */}
								<div className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-3 sm:px-4 py-1 sm:py-2 rounded-full text-xs sm:text-sm font-semibold">
									<Star className="w-3 h-3 sm:w-4 sm:h-4" fill="currentColor" />
									<span>More Than Just a Directory</span>
								</div>

								{/* Main heading */}
								<div className="space-y-3 sm:space-y-4">
									<h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight">
										<span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
											Your Complete RV
										</span>
										<br />
										<span className="text-gray-800">Support System</span>
									</h1>
									<p className="text-base sm:text-lg text-gray-600 leading-relaxed">
										RV Help Pro goes far beyond connecting you with providers.
										Get{" "}
										<span className="font-semibold text-primary">
											direct access to RV experts
										</span>
										,{" "}
										<span className="font-semibold text-secondary">
											emergency support
										</span>
										, and{" "}
										<span className="font-semibold text-primary">
											concierge services
										</span>{" "}
										when you need them most.
									</p>
								</div>

								{/* Value propositions */}
								<div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
									<div className="flex items-center space-x-2 p-2 sm:p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-gray-200/50">
										<div className="w-5 h-5 sm:w-6 sm:h-6 bg-green-100 rounded-full flex items-center justify-center">
											<DollarSign className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" />
										</div>
										<span className="text-xs sm:text-sm font-medium text-gray-700">
											Up to 25% savings
										</span>
									</div>
									<div className="flex items-center space-x-2 p-2 sm:p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-gray-200/50">
										<div className="w-5 h-5 sm:w-6 sm:h-6 bg-blue-100 rounded-full flex items-center justify-center">
											<Users className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600" />
										</div>
										<span className="text-xs sm:text-sm font-medium text-gray-700">
											Expert support
										</span>
									</div>
									<div className="flex items-center space-x-2 p-2 sm:p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-gray-200/50">
										<div className="w-5 h-5 sm:w-6 sm:h-6 bg-orange-100 rounded-full flex items-center justify-center">
											<Shield className="w-3 h-3 sm:w-4 sm:h-4 text-orange-600" />
										</div>
										<span className="text-xs sm:text-sm font-medium text-gray-700">
											Emergency help
										</span>
									</div>
								</div>

								{/* Social proof */}
								<div className="flex items-center space-x-3 sm:space-x-4 pt-3 sm:pt-4">
									<div className="flex -space-x-1 sm:-space-x-2">
										{providerImages.map((img, i) => (
											<div
												key={i}
												className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gray-300 border-2 border-white flex items-center justify-center overflow-hidden"
											>
												<img
													src={img}
													alt="Provider"
													className="w-full h-full object-cover"
												/>
											</div>
										))}
									</div>
									<div className="text-xs sm:text-sm text-gray-600">
										<span className="font-semibold">4.97/5</span> provider
										rating
										<span className="text-gray-400 ml-1">
											(
											{reviewCount !== null
												? reviewCount.toLocaleString()
												: "…"}
											+ reviews)
										</span>
									</div>
								</div>
							</div>

							<div className="relative">
								<div className="relative bg-white rounded-2xl shadow-xl overflow-hidden">
									<Image
										src="/images/pro-membership/RV Help Premium Content.jpeg"
										alt="RV Help Pro Membership"
										width={600}
										height={400}
										className="w-full h-auto"
									/>
									<div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 right-2 sm:right-4 bg-white/90 backdrop-blur-sm rounded-lg p-2 sm:p-4">
										<div className="flex items-center space-x-2 sm:space-x-3">
											<div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
												<span className="text-white font-bold text-xs sm:text-sm">
													RV
												</span>
											</div>
											<div>
												<div className="font-semibold text-gray-800 text-sm sm:text-base">
													RV Help Pro
												</div>
												<div className="text-xs text-gray-600">
													More than just a directory
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Pricing Section */}
				<div className="bg-white rounded-lg shadow-sm border mb-6 sm:mb-8">
					<div className="p-4 sm:p-6 lg:p-8">
						<div className="text-center mb-6 sm:mb-8">
							{offerEligibilityCheck.eligible && (
								<span className="bg-green-100 text-green-800 inline-block px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold mb-3 sm:mb-4">
									50% OFF LIMITED TIME
								</span>
							)}
							<h2 className="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4">
								Choose Your Pro Membership Plan
							</h2>
							<p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto">
								Select the plan that best fits your RV lifestyle and get access
								to premium features, provider discounts, and expert support.
								{offerEligibilityCheck.eligible && (
									<span className="font-semibold block mt-2">
										Special discount applied at checkout!
									</span>
								)}
							</p>
						</div>

						<div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 max-w-4xl mx-auto">
							{/* Standard Plan */}
							<div className="bg-white rounded-lg shadow-xl border overflow-hidden">
								<div className="bg-primary text-white p-4 sm:p-6 text-center">
									<span className="bg-white/20 text-white inline-block px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-bold mb-2">
										STANDARD
									</span>
									<h3 className="text-2xl sm:text-3xl font-bold mb-2">
										{offerEligibilityCheck.eligible ? (
											<>
												<span className="text-sm sm:text-lg line-through opacity-60">
													$99
												</span>
												<br />${calculateDiscountedPrice(99)}
											</>
										) : (
											"$99"
										)}
										<span className="text-sm sm:text-lg font-normal">
											/year
										</span>
									</h3>
									{offerEligibilityCheck.eligible && (
										<p className="text-blue-100 text-xs sm:text-sm mt-1">
											Save ${99 - calculateDiscountedPrice(99)} first year
										</p>
									)}
									<p className="text-blue-100 text-xs sm:text-sm mt-2">
										Perfect for Weekend Warriors
									</p>
								</div>
								<div className="p-4 sm:p-6">
									<div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												Unlimited pre-service diagnostic calls
											</span>
										</div>
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												Up to 25% provider discounts
											</span>
										</div>
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												Invite multiple providers
											</span>
										</div>
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												Maintenance tracker
											</span>
										</div>
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												Premium content & resources
											</span>
										</div>
									</div>
									<Button
										onClick={() =>
											upgradeToProMembership(
												config.stripe.membership.standard.priceId
											)
										}
										disabled={isCheckingOut}
										className="w-full bg-primary hover:bg-primary-dark text-white py-2 sm:py-3 text-base sm:text-lg font-semibold"
									>
										{isCheckingOut ? "Loading..." : "Join Standard"}
									</Button>
								</div>
							</div>

							{/* Premium Plan */}
							<div className="bg-white rounded-lg shadow-xl border-2 border-secondary overflow-hidden">
								<div className="bg-secondary text-white p-4 sm:p-6 text-center">
									<span className="bg-white/20 text-white inline-block px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-bold mb-2">
										PREMIUM
									</span>
									<h3 className="text-2xl sm:text-3xl font-bold mb-2">
										{offerEligibilityCheck.eligible ? (
											<>
												<span className="text-sm sm:text-lg line-through opacity-60">
													$199
												</span>
												<br />${calculateDiscountedPrice(199)}
											</>
										) : (
											"$199"
										)}
										<span className="text-sm sm:text-lg font-normal">
											/year
										</span>
									</h3>
									{offerEligibilityCheck.eligible && (
										<p className="text-orange-100 text-xs sm:text-sm mt-1">
											Save ${199 - calculateDiscountedPrice(199)} first year
										</p>
									)}
									<p className="text-orange-100 text-xs sm:text-sm mt-2">
										Perfect for Full Time RVers
									</p>
								</div>
								<div className="p-4 sm:p-6">
									<div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												Everything in Standard
											</span>
										</div>
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												<strong>3 free</strong> virtual diagnostic sessions
											</span>
										</div>
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												<strong>Priority</strong> phone support
											</span>
										</div>
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												Concierge support
											</span>
										</div>
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												Emergency dispatch to nearest 20 providers
											</span>
										</div>
										<div className="flex items-start">
											<CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
											<span className="text-sm sm:text-base text-gray-800">
												Early access to new features
											</span>
										</div>
									</div>
									<Button
										onClick={() =>
											upgradeToProMembership(
												config.stripe.membership.premium.priceId
											)
										}
										disabled={isCheckingOut}
										className="w-full bg-secondary hover:bg-secondary-dark text-white py-2 sm:py-3 text-base sm:text-lg font-semibold"
									>
										{isCheckingOut ? "Loading..." : "Join Premium"}
									</Button>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Key Benefits Sections */}
				<div className="space-y-6 sm:space-y-8">
					{/* Virtual Diagnostic Calls */}
					<div className="bg-white rounded-lg shadow-sm border">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-0">
							<div className="p-4 sm:p-6 lg:p-8 flex flex-col justify-center">
								<div className="inline-flex items-center bg-secondary text-white px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold mb-3 sm:mb-4">
									PREMIUM MEMBER BENEFIT
								</div>
								<h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">
									Video Call an RV Tech When You're Stuck
								</h3>
								<p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4">
									Get instant help through video calls with certified RV
									technicians. Perfect for when you're stranded or need expert
									guidance on the road.
								</p>
								<div className="bg-blue-50 border-l-4 border-blue-400 p-3 sm:p-4 mb-3 sm:mb-4">
									<div className="font-semibold text-blue-800 text-sm sm:text-base">
										💡 Save up to $300 in repairs
									</div>
									<div className="text-xs sm:text-sm text-blue-700">
										35% of common RV issues can be diagnosed and resolved
										remotely
									</div>
								</div>
								<ul className="space-y-2 text-sm sm:text-base text-gray-600">
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										In-depth video diagnosis with screen sharing
									</li>
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Step-by-step repair guidance when possible
									</li>
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Premium: 3 free sessions ($99 value each)
									</li>
								</ul>
							</div>
							<div className="bg-secondary-lighter p-3 sm:p-6 flex items-center justify-center">
								<Image
									src="/images/offer-1/virtual-diagnosis.jpg"
									alt="Video call an RV tech when you're stuck in the middle of nowhere"
									width={400}
									height={300}
									className="rounded-lg shadow-lg w-full h-auto"
								/>
							</div>
						</div>
					</div>

					{/* Emergency Dispatch */}
					<div className="bg-white rounded-lg shadow-sm border">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-0">
							<div className="bg-red-50 p-3 sm:p-6 flex items-center justify-center">
								<Image
									src="/images/offer-1/emergency-dispatch.jpg"
									alt="Broadcast your RV emergency to providers outside your service area"
									width={400}
									height={300}
									className="rounded-lg shadow-lg w-full h-auto"
								/>
							</div>
							<div className="p-4 sm:p-6 lg:p-8 flex flex-col justify-center">
								<div className="inline-flex items-center bg-red-100 text-red-800 px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold mb-3 sm:mb-4">
									PREMIUM MEMBER BENEFIT
								</div>
								<h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">
									Emergency Dispatch When You Need It Most
								</h3>
								<p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4">
									Critical RV house system failure? Broadcast urgent alerts to
									all available techs in your area, even outside your normal
									service area.
								</p>
								<div className="bg-red-50 border-l-4 border-red-400 p-3 sm:p-4 mb-3 sm:mb-4">
									<div className="font-semibold text-red-800 text-sm sm:text-base">
										🚨 Get help when you're stranded
									</div>
									<div className="text-xs sm:text-sm text-red-700">
										Emergency dispatch reaches providers beyond your service
										area
									</div>
								</div>
								<ul className="space-y-2 text-sm sm:text-base text-gray-600">
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Broadcast to all available techs in your area
									</li>
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Reach providers outside your normal service area
									</li>
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Priority response for critical system failures
									</li>
								</ul>
							</div>
						</div>
					</div>

					{/* Concierge Support */}
					<div className="bg-white rounded-lg shadow-sm border">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-0">
							<div className="p-4 sm:p-6 lg:p-8 flex flex-col justify-center">
								<div className="inline-flex items-center bg-green-100 text-green-800 px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold mb-3 sm:mb-4">
									PREMIUM MEMBER BENEFIT
								</div>
								<h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">
									Have Our Team Make the Phone Calls for You
								</h3>
								<p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4">
									Can't find a tech or inspector in your area? Need help with
									something specific? Our concierge team is here to help you get
									the support you need.
								</p>
								<div className="bg-green-50 border-l-4 border-green-400 p-3 sm:p-4 mb-3 sm:mb-4">
									<div className="font-semibold text-green-800 text-sm sm:text-base">
										🤝 Personal assistance when you need it
									</div>
									<div className="text-xs sm:text-sm text-green-700">
										Our team handles the legwork so you can focus on your RV
									</div>
								</div>
								<ul className="space-y-2 text-sm sm:text-base text-gray-600">
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Personal assistance finding providers in your area
									</li>
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Help with specific RV issues and questions
									</li>
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Priority support for Premium members
									</li>
								</ul>
							</div>
							<div className="bg-primary-lighter p-3 sm:p-6 flex items-center justify-center">
								<Image
									src="/images/offer-1/concierge-support.jpg"
									alt="Have our team make the phone calls for you"
									width={400}
									height={300}
									className="rounded-lg shadow-lg w-full h-auto"
								/>
							</div>
						</div>
					</div>

					{/* Quick Troubleshooting Calls */}
					<div className="bg-white rounded-lg shadow-sm border">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-0">
							<div className="bg-blue-50 p-3 sm:p-6 flex items-center justify-center">
								<Image
									src="/images/offer-1/troubleshooting.jpg"
									alt="Book a quick troubleshooting call with an RV tech"
									width={400}
									height={300}
									className="rounded-lg shadow-lg w-full h-auto"
								/>
							</div>
							<div className="p-4 sm:p-6 lg:p-8 flex flex-col justify-center">
								<div className="inline-flex items-center bg-blue-100 text-blue-800 px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold mb-3 sm:mb-4">
									INCLUDED IN ALL PLANS
								</div>
								<h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">
									Quick Troubleshooting Calls with Local Techs
								</h3>
								<p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4">
									Connect with local techs in your area who offer 10-minute
									troubleshooting calls to Pro Members. Get quick answers to
									your RV questions.
								</p>
								<div className="bg-blue-50 border-l-4 border-blue-400 p-3 sm:p-4 mb-3 sm:mb-4">
									<div className="font-semibold text-blue-800 text-sm sm:text-base">
										⚡ Get answers in 10 minutes or less
									</div>
									<div className="text-xs sm:text-sm text-blue-700">
										Quick diagnostic calls with local RV technicians
									</div>
								</div>
								<ul className="space-y-2 text-sm sm:text-base text-gray-600">
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										10-minute troubleshooting calls with local techs
									</li>
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Quick answers to common RV questions
									</li>
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Pre-service diagnostic support
									</li>
								</ul>
							</div>
						</div>
					</div>

					{/* Provider Discounts */}
					<div className="bg-white rounded-lg shadow-sm border">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-0">
							<div className="p-4 sm:p-6 lg:p-8 flex flex-col justify-center">
								<div className="inline-flex items-center bg-green-100 text-green-800 px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold mb-3 sm:mb-4">
									INCLUDED IN ALL PLANS
								</div>
								<h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">
									Save Up to 25% on Service
								</h3>
								<p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4">
									Enjoy significant savings with our nationwide network of RV
									service providers that offer special rates exclusively for Pro
									members.
								</p>
								<div className="bg-green-50 border-l-4 border-green-400 p-3 sm:p-4 mb-3 sm:mb-4">
									<div className="font-semibold text-green-800 text-sm sm:text-base">
										💰 Save up to $80 per service call
									</div>
									<div className="text-xs sm:text-sm text-green-700">
										Use it 1-2 times per year and the membership pays for itself
									</div>
								</div>
								<ul className="space-y-2 text-sm sm:text-base text-gray-600">
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										25% off dispatch fees with participating providers
									</li>
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										10% off hourly labor rates (up to 4 hours)
									</li>
									<li className="flex items-start">
										<CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
										Service at the convenience of your campsite
									</li>
								</ul>
							</div>
							<div className="bg-primary-lighter p-3 sm:p-6 flex items-center justify-center">
								<Image
									src="/images/pro-membership/Discounts on RV Service.jpeg"
									alt="Provider Discounts"
									width={400}
									height={300}
									className="rounded-lg shadow-lg w-full h-auto"
								/>
							</div>
						</div>
					</div>
				</div>

				{/* Final CTA */}
				<div className="bg-gradient-to-r from-[#43806c] to-[#2c5446] text-white rounded-lg shadow-lg mt-6 sm:mt-8">
					<div className="p-4 sm:p-6 lg:p-8 text-center">
						<h2 className="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4">
							Ready to Upgrade Your RV Service Experience?
						</h2>
						<p className="text-base sm:text-lg mb-4 sm:mb-6 opacity-90">
							Join thousands of RV owners who save time and money with Pro
							membership
						</p>
						<div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-2xl mx-auto">
							<Button
								onClick={() =>
									upgradeToProMembership(
										config.stripe.membership.standard.priceId
									)
								}
								disabled={isCheckingOut}
								className="bg-white text-primary hover:bg-gray-100 px-6 sm:px-8 py-2 sm:py-3 text-base sm:text-lg font-semibold"
							>
								{isCheckingOut
									? "Loading..."
									: offerEligibilityCheck.eligible
										? `Join Standard - $${calculateDiscountedPrice(99)}`
										: "Join Standard - $99"}
							</Button>
							<Button
								onClick={() =>
									upgradeToProMembership(
										config.stripe.membership.premium.priceId
									)
								}
								disabled={isCheckingOut}
								className="bg-secondary text-white hover:bg-secondary-dark px-6 sm:px-8 py-2 sm:py-3 text-base sm:text-lg font-semibold"
							>
								{isCheckingOut
									? "Loading..."
									: offerEligibilityCheck.eligible
										? `Join Premium - $${calculateDiscountedPrice(199)}`
										: "Join Premium - $199"}
							</Button>
						</div>
						<p className="text-xs sm:text-sm mt-3 sm:mt-4 opacity-75">
							{offerEligibilityCheck.eligible
								? "Limited time 50% off offer"
								: "Cancel anytime"}
						</p>
					</div>
				</div>

				{/* Decline Option */}
				<div className="text-center mt-6 sm:mt-8">
					<Button
						variant="ghost"
						onClick={handleDecline}
						className="text-gray-500 hover:text-gray-700 text-base sm:text-lg"
					>
						No thanks, I'll continue with the free plan
					</Button>
				</div>
			</div>
		</div>
	);
}
