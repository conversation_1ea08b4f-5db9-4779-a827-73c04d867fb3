"use client";

import { But<PERSON> } from "@/components/ui/button";
import { url } from "@/lib/url";
import { motion } from "framer-motion";
import { ArrowRight, Search, Wrench } from "lucide-react";
import Link from "next/link";

const providerImages = [
	"/images/home/<USER>",
	"/images/home/<USER>",
	"/images/home/<USER>",
	"/images/home/<USER>"
];

export default function CTA() {
	return (
		<section className="relative overflow-hidden py-24 bg-white">
			<div className="container mx-auto px-4 text-center relative">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5 }}
					viewport={{ once: true }}
					className="inline-flex items-center gap-2 bg-[#43806c]/10 backdrop-blur-sm px-4 py-2 rounded-full mb-6"
				>
					<span className="inline-block w-2 h-2 rounded-full bg-[#43806c] animate-pulse"></span>
					<span className="text-[#43806c] text-sm font-medium">
						2500+ Certified Providers
					</span>
				</motion.div>

				<motion.h2
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5, delay: 0.1 }}
					viewport={{ once: true }}
					className="text-4xl md:text-5xl font-bold mb-6 text-gray-900"
				>
					Ready to Get <span className="text-[#43806c]">Started</span>?
				</motion.h2>

				<motion.p
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5, delay: 0.2 }}
					viewport={{ once: true }}
					className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto"
				>
					Connect with trusted RV service professionals today and travel with
					confidence.
				</motion.p>

				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5, delay: 0.3 }}
					viewport={{ once: true }}
					className="flex flex-col sm:flex-row gap-4 justify-center"
				>
					{/* Find a Technician CTA */}
					<Link href={url("rv-repair")}>
						<Button
							size="lg"
							className="bg-amber-500 hover:bg-amber-600 text-white font-medium text-lg px-8 py-7 rounded-xl shadow-lg shadow-amber-500/20 border-0 transition-all duration-300 hover:translate-y-[-2px] hover:shadow-xl hover:shadow-amber-500/30 group w-full sm:w-auto"
						>
							<Wrench className="w-6 h-6 mr-3" />
							<span>Find a Technician</span>
							<ArrowRight className="w-5 h-5 ml-2 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
						</Button>
					</Link>

					{/* Find an Inspector CTA */}
					<Link href={url("rv-inspection")}>
						<Button
							size="lg"
							className="bg-[#43806c] hover:bg-[#3a6f5d] text-white font-medium text-lg px-8 py-7 rounded-xl shadow-lg shadow-[#43806c]/20 border-0 transition-all duration-300 hover:translate-y-[-2px] hover:shadow-xl hover:shadow-[#43806c]/30 group w-full sm:w-auto"
						>
							<Search className="w-6 h-6 mr-3" />
							<span>Find an Inspector</span>
							<ArrowRight className="w-5 h-5 ml-2 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
						</Button>
					</Link>
				</motion.div>

				{/* Trust indicators */}
				<motion.div
					initial={{ opacity: 0 }}
					whileInView={{ opacity: 1 }}
					transition={{ duration: 0.5, delay: 0.5 }}
					viewport={{ once: true }}
					className="mt-12 flex items-center justify-center gap-4"
				>
					<div className="flex -space-x-2">
						{providerImages.map((i) => (
							<div
								key={i}
								className="w-8 h-8 rounded-full bg-gray-300 border-2 border-white flex items-center justify-center overflow-hidden"
							>
								<img
									src={i}
									alt="User avatar"
									className="w-full h-full object-cover"
								/>
							</div>
						))}
					</div>
					<p className="text-gray-600 text-sm">
						<span className="text-gray-900 font-medium">Join 10,000+</span> RV
						owners who trust our service
					</p>
				</motion.div>
			</div>
		</section>
	);
}
