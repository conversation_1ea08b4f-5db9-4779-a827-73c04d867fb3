"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useNewsletterSubscription } from "@/lib/hooks/useNewsletterSubscription";
import { url } from "@/lib/url";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { ArrowRight, Search, Wrench } from "lucide-react";
import Link from "next/link";

const providerImages = [
	"/images/home/<USER>",
	"/images/home/<USER>",
	"/images/home/<USER>",
	"/images/home/<USER>"
];

export default function HeroSection() {
	const { isSubscribed } = useNewsletterSubscription();

	return (
		<section className="relative overflow-hidden rounded-2xl">
			{/* Background with gradient overlay */}
			<div
				className="absolute inset-0 z-0"
				style={{
					backgroundImage: `url("/images/rvhelp-bg.jpg")`,
					backgroundSize: "cover",
					backgroundPosition: "center"
				}}
			/>
			{/* Modern gradient overlay */}
			<div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10" />
			<div
				className={cn(
					"relative z-10 max-w-7xl mx-auto px-8 min-h-[90vh] flex items-center",
					!isSubscribed ? "py-32 md:pb-32 md:pt-[160px]" : "py-24 lg:py-32"
				)}
			>
				<div className="max-w-3xl">
					{/* Badge */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5 }}
						className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full mb-6"
					>
						<span className="inline-block w-2 h-2 rounded-full bg-green-400 animate-pulse"></span>
						<span className="text-white/90 text-sm font-medium">
							Nationwide Coverage
						</span>
					</motion.div>

					{/* Heading with animation */}
					<motion.h1
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5, delay: 0.1 }}
						className="text-5xl md:text-6xl lg:text-7xl font-bold text-white tracking-tight mb-6"
					>
						<span className="block">Get RV Help</span>
						<span className="block">When You Need It.</span>
						<span className="text-secondary italic">Fast.</span>
					</motion.h1>

					{/* Description */}
					<motion.p
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5, delay: 0.2 }}
						className="text-xl md:text-2xl text-white mb-8 max-w-2xl"
					>
						Connect with vetted & trusted RV service pros who come directly to
						your campsite.
					</motion.p>

					{/* CTAs */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5, delay: 0.3 }}
						className="flex flex-col sm:flex-row gap-4"
					>
						{/* Find a Technician CTA */}
						<Link href={url("rv-repair")}>
							<Button
								size="lg"
								className="bg-amber-500 hover:bg-amber-600 text-white font-medium text-lg px-8 py-7 rounded-xl shadow-lg shadow-amber-500/20 border-0 transition-all duration-300 hover:translate-y-[-2px] hover:shadow-xl hover:shadow-amber-500/30 group w-full sm:w-auto"
							>
								<Wrench className="w-6 h-6 mr-3" />
								<span>Find a Technician</span>
								<ArrowRight className="w-5 h-5 ml-2 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
							</Button>
						</Link>

						{/* Find an Inspector CTA */}
						<Link href={url("rv-inspection")}>
							<Button
								size="lg"
								className="bg-[#43806c] hover:bg-[#3a6f5d] text-white font-medium text-lg px-8 py-7 rounded-xl shadow-lg shadow-[#43806c]/20 border-0 transition-all duration-300 hover:translate-y-[-2px] hover:shadow-xl hover:shadow-[#43806c]/30 group w-full sm:w-auto"
							>
								<Search className="w-6 h-6 mr-3" />
								<span>Find an Inspector</span>
								<ArrowRight className="w-5 h-5 ml-2 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
							</Button>
						</Link>
					</motion.div>

					{/* Trust indicators */}
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ duration: 0.5, delay: 0.5 }}
						className="mt-12 flex items-center gap-4"
					>
						<div className="flex -space-x-2.5">
							{providerImages.map((i) => (
								<div
									key={i}
									className="w-10 h-10 rounded-full bg-gray-300 border-1 border-white flex items-center justify-center overflow-hidden"
								>
									<img
										src={i}
										alt="User avatar"
										className="w-full h-full object-cover"
									/>
								</div>
							))}
						</div>
						<p className="text-white/70 text-sm">
							<span className="text-white font-medium">2500+</span> certified
							professionals nationwide
						</p>
					</motion.div>
				</div>
			</div>
		</section>
	);
}
