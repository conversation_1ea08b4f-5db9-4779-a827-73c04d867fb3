'use client';

import { useState, FormEvent } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const json_form = [
    {
        "name": "1 - Dealer Account",
        "type": "PDFTextField",
        "index": 48
    },
    {
        "name": "2 - Ref/PO Number",
        "type": "PDFTextField",
        "index": 49
    },
    {
        "name": "3 - Dealership",
        "type": "PDFTextField",
        "index": 0
    },
    {
        "name": "4 - Contact",
        "type": "PDFTextField",
        "index": 1
    },
    {
        "name": "5 - Dealer Phone Number",
        "type": "PDFTextField",
        "index": 2
    },
    {
        "name": "6 - Dealer Fax Number",
        "type": "PDFTextField",
        "index": 3
    },
    {
        "name": "7 - Customer Name",
        "type": "PDFTextField",
        "index": 4
    },
    {
        "name": "8 - Customer Address 1",
        "type": "PDFTextField",
        "index": 5
    },
    {
        "name": "9 - Customer Address 2",
        "type": "PDFTextField",
        "index": 6
    },
    {
        "name": "10 - Customer City",
        "type": "PDFTextField",
        "index": 7
    },
    {
        "name": "11 - Customer State",
        "type": "PDFTextField",
        "index": 8
    },
    {
        "name": "12 - Date of Purchase",
        "type": "PDFTextField",
        "index": 9
    },
    {
        "name": "13 - Original Owner",
        "type": "PDFCheckBox",
        "index": 10
    },
    {
        "name": "14 - Coach Manufacturer",
        "type": "PDFTextField",
        "index": 50
    },
    {
        "name": "15 - Coach VIN",
        "type": "PDFTextField",
        "index": 11
    },
    {
        "name": "16 - Repair Date",
        "type": "PDFTextField",
        "index": 12
    },
    {
        "name": "17 - Model Number",
        "type": "PDFTextField",
        "index": 13
    },
    {
        "name": "18 - Serial Number",
        "type": "PDFTextField",
        "index": 14
    },
    {
        "name": "19 - Product Number",
        "type": "PDFTextField",
        "index": 15
    },
    {
        "name": "20 - Icemaker",
        "type": "PDFCheckBox",
        "index": 16
    },
    {
        "name": "21 - Fan",
        "type": "PDFCheckBox",
        "index": 17
    },
    {
        "name": "22 - Left Hinge",
        "type": "PDFCheckBox",
        "index": 18
    },
    {
        "name": "23 - Right Hinge",
        "type": "PDFCheckBox",
        "index": 19
    },
    {
        "name": "24 - Is door large enough to remove from Coach?",
        "type": "PDFCheckBox",
        "index": 20
    },
    {
        "name": "25 - Comments 1",
        "type": "PDFTextField",
        "index": 47
    },
    {
        "name": "26 - Door/Frame/Cabinet - Defect",
        "type": "PDFTextField",
        "index": 21
    },
    {
        "name": "27 - Cooling Unit - AC Volts",
        "type": "PDFTextField",
        "index": 22
    },
    {
        "name": "28 - Cooling Unit - DC Volts",
        "type": "PDFTextField",
        "index": 23
    },
    {
        "name": "29 - Cooling Unit - CHK Venting",
        "type": "PDFCheckBox",
        "index": 24
    },
    {
        "name": "30 - Cooling Unit - Freezer Temp",
        "type": "PDFTextField",
        "index": 25
    },
    {
        "name": "31 - Cooling Unit - Box Temp",
        "type": "PDFTextField",
        "index": 26
    },
    {
        "name": "32 - Cooling Unit - Direct Wired?",
        "type": "PDFCheckBox",
        "index": 27
    },
    {
        "name": "33 - Cooling Unit - Direct Wired - How long?",
        "type": "PDFTextField",
        "index": 28
    },
    {
        "name": "34 - Cooling Unit - Outside Temp",
        "type": "PDFTextField",
        "index": 29
    },
    {
        "name": "35 - Cooling Unit - Thermister Ohms",
        "type": "PDFTextField",
        "index": 30
    },
    {
        "name": "36 - Thermister/Thermostat - Ohms (Ice water)",
        "type": "PDFTextField",
        "index": 31
    },
    {
        "name": "37 - Thermister/Thermostat - Continuity",
        "type": "PDFTextField",
        "index": 32
    },
    {
        "name": "38 - Heating Element - Ohms",
        "type": "PDFTextField",
        "index": 33
    },
    {
        "name": "39 - Heating Element - Continuity (DC Element)",
        "type": "PDFCheckBox",
        "index": 34
    },
    {
        "name": "40 - Control Board - Part Number",
        "type": "PDFTextField",
        "index": 35
    },
    {
        "name": "41 - Control Board - DC Volts",
        "type": "PDFTextField",
        "index": 36
    },
    {
        "name": "42 - Control Board - AC Volts",
        "type": "PDFTextField",
        "index": 37
    },
    {
        "name": "43 - Control Board - Defect",
        "type": "PDFTextField",
        "index": 38
    },
    {
        "name": "44 - Upper Board - Part Number",
        "type": "PDFTextField",
        "index": 39
    },
    {
        "name": "45 - Upper Board - Defect",
        "type": "PDFTextField",
        "index": 40
    },
    {
        "name": "46 - Wiring - Defect",
        "type": "PDFTextField",
        "index": 41
    },
    {
        "name": "47 - Gas Valve - Ohms",
        "type": "PDFTextField",
        "index": 42
    },
    {
        "name": "48 - Thermocouple - Millivolts",
        "type": "PDFTextField",
        "index": 43
    },
    {
        "name": "49 - Electrode - Defect",
        "type": "PDFTextField",
        "index": 44
    },
    {
        "name": "50 - Reigniter - Defect",
        "type": "PDFTextField",
        "index": 45
    },
    {
        "name": "51 - Other - Defect",
        "type": "PDFTextField",
        "index": 46
    }
];

interface FormField {
    name: string;
    type: string;
    index: number;
    value?: string | boolean;
}

export default function DynamicFormPage() {
    const [formValues, setFormValues] = useState<Record<string, string | boolean>>({});
    const [outputJson, setOutputJson] = useState<FormField[] | null>(null);

    const handleInputChange = (fieldName: string, value: string | boolean) => {
        setFormValues(prev => ({
            ...prev,
            [fieldName]: value
        }));
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        
        const formWithValues = json_form.map(field => ({
            ...field,
            value: formValues[field.name] || (field.type === 'PDFCheckBox' ? false : '')
        }));
        
        setOutputJson(formWithValues);
    };

    const handleReset = () => {
        setFormValues({});
        setOutputJson(null);
    };

    const renderFormField = (field: FormField) => {
        const fieldId = `field-${field.index}`;
        
        if (field.type === 'PDFCheckBox') {
            return (
                <div key={field.index} className="flex items-center space-x-2 mb-4">
                    <Checkbox
                        id={fieldId}
                        checked={!!formValues[field.name]}
                        onCheckedChange={(checked) => handleInputChange(field.name, checked)}
                    />
                    <Label htmlFor={fieldId} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                        {field.name}
                    </Label>
                </div>
            );
        }
        
        return (
            <div key={field.index} className="mb-4">
                <Label htmlFor={fieldId} className="text-sm font-medium mb-2 block">
                    {field.name}
                </Label>
                <Input
                    id={fieldId}
                    type="text"
                    value={formValues[field.name] as string || ''}
                    onChange={(e) => handleInputChange(field.name, e.target.value)}
                    placeholder={`Enter ${field.name.toLowerCase()}`}
                />
            </div>
        );
    };

    return (
        <div className="container mx-auto py-8 px-4">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-3xl font-bold mb-8">Dynamic PDF Form</h1>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Form Section */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Form Fields</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="max-h-96 overflow-y-auto pr-2">
                                    {json_form.map(renderFormField)}
                                </div>
                                
                                <div className="flex gap-4 pt-4 border-t">
                                    <Button type="submit" className="flex-1">
                                        Generate JSON
                                    </Button>
                                    <Button type="button" variant="outline" onClick={handleReset}>
                                        Reset
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                    
                    {/* Output Section */}
                    <Card>
                        <CardHeader>
                            <CardTitle>JSON Output</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {outputJson ? (
                                <div className="max-h-96 overflow-y-auto">
                                    <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
                                        {JSON.stringify(outputJson, null, 2)}
                                    </pre>
                                    <Button
                                        className="mt-4 w-full"
                                        variant="outline"
                                        onClick={() => navigator.clipboard.writeText(JSON.stringify(outputJson, null, 2))}
                                    >
                                        Copy to Clipboard
                                    </Button>
                                </div>
                            ) : (
                                <div className="text-center text-gray-500 py-12">
                                    <p>Fill out the form and click "Generate JSON" to see the output here.</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
                
                {/* Form Summary */}
                <Card className="mt-8">
                    <CardHeader>
                        <CardTitle>Form Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                    {json_form.length}
                                </div>
                                <div className="text-gray-600">Total Fields</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">
                                    {json_form.filter(f => f.type === 'PDFTextField').length}
                                </div>
                                <div className="text-gray-600">Text Fields</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-purple-600">
                                    {json_form.filter(f => f.type === 'PDFCheckBox').length}
                                </div>
                                <div className="text-gray-600">Checkboxes</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-orange-600">
                                    {Object.keys(formValues).length}
                                </div>
                                <div className="text-gray-600">Fields Filled</div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
} 