import OEMWalkthrough from "@/components/oem/oem-walkthrough/oem-walkthrough";
import prisma from "@/lib/prisma";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { notFound } from "next/navigation";

async function getWarrantyRequest(
	uuid: string
): Promise<ExtendedWarrantyRequest | null> {
	try {
		const warrantyRequest = await prisma.warrantyRequest.findUnique({
			where: { uuid },
			include: { company: true }
		});
		return warrantyRequest as ExtendedWarrantyRequest | null;
	} catch (error) {
		console.error("Failed to fetch warranty request:", error);
		return null;
	}
}

async function getJob(id?: string) {
	try {
		if (id == null) {
			return null;
		}
		const job = await prisma.job.findUnique({
			where: { id },
			include: { quotes: true }
		});
		return job;
	} catch (error) {
		console.error("Failed to fetch job:", error);
		return null;
	}
}

export default async function WarrantyServicePage({
	params
}: {
	params: { slug: string };
}) {
	const warrantyRequest = await getWarrantyRequest(params.slug);
	if (!warrantyRequest) {
		notFound();
	}
	const job = await getJob(warrantyRequest.job_id);

	return <OEMWalkthrough initialRequest={warrantyRequest} initialJob={job} />;
}
