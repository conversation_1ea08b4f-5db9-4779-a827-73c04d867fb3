"use client";

import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Search } from "lucide-react";
import { useState } from "react";

interface RVSGMember {
	member_id: string;
	member_email: string;
	inspector: {
		status: string;
		inspector_id: string | null;
		inspector_level: string | null;
		inspector_last_update: string | null;
	};
	technician: {
		status: string;
		technician_id: string | null;
		technician_level: string | null;
		technician_last_update: string | null;
	};
	profile: {
		show: string;
		name: string;
		company: string;
		logo_image_url: string;
		image_image_url: string;
		description: string;
		address: string;
		social: {
			phone: string;
			web: string;
			email: string;
			facebook: string;
		};
		certifications: string[];
		locations: {
			address: string;
			lat: string;
			lon: string;
			start?: string;
			end?: string;
		}[];
	};
}

export default function RVSGTestPage() {
	const [listingId, setListingId] = useState("");
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<RVSGMember | null>(null);
	const [error, setError] = useState<string | null>(null);
	const [listingInfo, setListingInfo] = useState<any>(null);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!listingId.trim()) return;

		setLoading(true);
		setError(null);
		setResult(null);
		setListingInfo(null);

		try {
			const response = await fetch("/api/admin/test/rvta", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({ listingId })
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Failed to fetch member data");
			}

			setResult(data.member);
			setListingInfo(data.listing);
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto p-6 max-w-4xl">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Search className="w-5 h-5" />
						RVSG Member Lookup Test
					</CardTitle>
					<CardDescription>
						Enter a listing ID to fetch the corresponding RVSG member data using
						the listing's rvtaa_member_id
					</CardDescription>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSubmit} className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="listingId">Listing ID</Label>
							<Input
								id="listingId"
								value={listingId}
								onChange={(e) => setListingId(e.target.value)}
								placeholder="Enter listing ID..."
								disabled={loading}
							/>
						</div>
						<Button type="submit" disabled={loading || !listingId.trim()}>
							{loading ? (
								<>
									<Loader2 className="w-4 h-4 mr-2 animate-spin" />
									Loading...
								</>
							) : (
								<>
									<Search className="w-4 h-4 mr-2" />
									Lookup Member
								</>
							)}
						</Button>
					</form>

					{error && (
						<Alert className="mt-6" variant="destructive">
							<AlertDescription>{error}</AlertDescription>
						</Alert>
					)}

					{listingInfo && (
						<Card className="mt-6">
							<CardHeader>
								<CardTitle>Listing Information</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-2 text-sm">
									<div>
										<strong>ID:</strong> {listingInfo.id}
									</div>
									<div>
										<strong>Business Name:</strong> {listingInfo.business_name}
									</div>
									<div>
										<strong>Email:</strong> {listingInfo.email}
									</div>
									<div>
										<strong>RVTAA Member ID:</strong>{" "}
										{listingInfo.rvtaa_member_id || "Not set"}
									</div>
									<div>
										<strong>RVTAA Technician Level:</strong>{" "}
										{listingInfo.rvtaa_technician_level || "Not set"}
									</div>
									<div>
										<strong>NRVIA Inspector ID:</strong>{" "}
										{listingInfo.nrvia_inspector_id || "Not set"}
									</div>
									<div>
										<strong>NRVIA Inspector Level:</strong>{" "}
										{listingInfo.nrvia_inspector_level || "Not set"}
									</div>
								</div>
							</CardContent>
						</Card>
					)}

					{result && (
						<Card className="mt-6">
							<CardHeader>
								<CardTitle>RVSG Member Data</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-4">
									<div>
										<h4 className="font-semibold mb-2">Basic Information</h4>
										<div className="space-y-1 text-sm">
											<div>
												<strong>Member ID:</strong> {result.member_id}
											</div>
											<div>
												<strong>Email:</strong> {result.member_email}
											</div>
										</div>
									</div>

									<div>
										<h4 className="font-semibold mb-2">Profile</h4>
										<div className="space-y-1 text-sm">
											<div>
												<strong>Name:</strong> {result.profile.name}
											</div>
											<div>
												<strong>Company:</strong> {result.profile.company}
											</div>
											<div>
												<strong>Phone:</strong> {result.profile.social.phone}
											</div>
											<div>
												<strong>Website:</strong>{" "}
												{result.profile.social.web || "Not provided"}
											</div>
											<div>
												<strong>Facebook:</strong>{" "}
												{result.profile.social.facebook || "Not provided"}
											</div>
										</div>
									</div>

									<div>
										<h4 className="font-semibold mb-2">Technician Status</h4>
										<div className="space-y-1 text-sm">
											<div>
												<strong>Status:</strong> {result.technician.status}
											</div>
											<div>
												<strong>Technician ID:</strong>{" "}
												{result.technician.technician_id || "Not set"}
											</div>
											<div>
												<strong>Technician Level:</strong>{" "}
												{result.technician.technician_level || "Not set"}
											</div>
											<div>
												<strong>Last Update:</strong>{" "}
												{result.technician.technician_last_update || "Not set"}
											</div>
										</div>
									</div>

									<div>
										<h4 className="font-semibold mb-2">Inspector Status</h4>
										<div className="space-y-1 text-sm">
											<div>
												<strong>Status:</strong> {result.inspector.status}
											</div>
											<div>
												<strong>Inspector ID:</strong>{" "}
												{result.inspector.inspector_id || "Not set"}
											</div>
											<div>
												<strong>Inspector Level:</strong>{" "}
												{result.inspector.inspector_level || "Not set"}
											</div>
											<div>
												<strong>Last Update:</strong>{" "}
												{result.inspector.inspector_last_update || "Not set"}
											</div>
										</div>
									</div>

									<div>
										<h4 className="font-semibold mb-2">Certifications</h4>
										<div className="text-sm">
											{result.profile.certifications?.length > 0 ? (
												<ul className="list-disc list-inside">
													{result.profile.certifications?.map((cert, index) => (
														<li key={index}>{cert}</li>
													))}
												</ul>
											) : (
												<div>No certifications listed</div>
											)}
										</div>
									</div>

									<div>
										<h4 className="font-semibold mb-2">Locations</h4>
										<div className="text-sm">
											{result.profile.locations?.length > 0 ? (
												<div className="space-y-2">
													{result.profile.locations?.map((location, index) => (
														<div key={index} className="border p-2 rounded">
															<div>
																<strong>Address:</strong> {location.address}
															</div>
															<div>
																<strong>Latitude:</strong> {location.lat}
															</div>
															<div>
																<strong>Longitude:</strong> {location.lon}
															</div>
															{location.start && (
																<div>
																	<strong>Start:</strong> {location.start}
																</div>
															)}
															{location.end && (
																<div>
																	<strong>End:</strong> {location.end}
																</div>
															)}
														</div>
													))}
												</div>
											) : (
												<div>No locations listed</div>
											)}
										</div>
									</div>

									<div>
										<h4 className="font-semibold mb-2">Raw JSON</h4>
										<Textarea
											value={JSON.stringify(result, null, 2)}
											readOnly
											className="font-mono text-xs"
											rows={10}
										/>
									</div>
								</div>
							</CardContent>
						</Card>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
