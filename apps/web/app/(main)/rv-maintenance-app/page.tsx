"use client";

import config from "@/config";
import { ArrowRight, Calendar, CheckCircle, Clock, FileText, MessageSquare, Shield, Wrench } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";

function NextArrow(props: any) {
    const { className, style, onClick } = props;
    return (
        <button
            className={className + " !right-[24px] z-100 bg-white/80 hover:bg-white rounded-full shadow p-1"}
            style={{ ...style }}
            onClick={onClick}
            aria-label="Next"
        >

        </button>
    );
}

function PrevArrow(props: any) {
    const { className, style, onClick } = props;
    return (
        <button
            className={className + " !left-[7px] z-999 bg-white/80 hover:bg-white rounded-full shadow p-1"}
            style={{ ...style, }}
            onClick={onClick}
            aria-label="Previous"
        >
        </button>
    );
}

export default function AppLandingPage() {

    const appScreenshots = [
        {
            src: "/images/app-screenshots/1.png",
            alt: "RV Help app home screen showing service categories and search bar",
            caption: "Find trusted RV service professionals near you."
        },
        {
            src: "/images/app-screenshots/2.png",
            alt: "RV Help app map view with technician locations",
            caption: "See certified technicians on the map."
        },
        {
            src: "/images/app-screenshots/3.png",
            alt: "RV Help app booking screen for scheduling service appointments",
            caption: "Book appointments with certified RV technicians."
        },
        {
            src: "/images/app-screenshots/4.png",
            alt: "RV Help app maintenance checklist feature",
            caption: "Track your RV maintenance with our checklist app."
        },
        {
            src: "/images/app-screenshots/5.png",
            alt: "RV Help app notifications for upcoming service",
            caption: "Get notified when RV service is due."
        },
        {
            src: "/images/app-screenshots/6.png",
            alt: "RV Help app chat feature for direct communication with technicians",
            caption: "Chat with technicians and get real-time updates."
        },
        {
            src: "/images/app-screenshots/7.png",
            alt: "RV Help app pro membership benefits screen",
            caption: "Unlock premium features with Pro Membership."
        },
        {
            src: "/images/app-screenshots/8.png",
            alt: "RV Help app user profile and maintenance history",
            caption: "Manage your RV profile and view maintenance history."
        }
    ];
    useEffect(() => {
        // Check if user is on iOS device
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;

        if (isIOS && config.apps.appStore.url) {
            // Redirect to App Store
            window.location.href = config.apps.appStore.url;
        }
    }, []);


    return (
        <div className="min-h-screen">
            {/* Hero Section */}
            <header className="relative text-white py-24 px-4">
                <div className="absolute inset-0">
                    <Image
                        src="/images/myrvresource.webp"
                        alt="RV Help Mobile App"
                        layout="fill"
                        objectFit="cover"
                        quality={100}
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-[#43806c]/90 to-[#2c5446]/90"></div>
                </div>
                <div className="container mx-auto relative z-10">
                    <h1 className="text-4xl md:text-5xl font-bold mb-4 text-center">
                        The Ultimate RV Maintenance App
                    </h1>
                    <p className="text-xl text-center max-w-3xl mx-auto mb-8">
                        Schedule service, track maintenance, and get expert help - all in one place
                    </p>
                    <div className="flex justify-center w-auto">
                        <Link
                            href={config.apps.appStore.url}
                            className="flex items-center rounded-md text-lg px-8 py-6 bg-white text-[#43806c] hover:bg-gray-100 flex-shrink-1"
                        >
                            <span className="flex-shrink-1">Download on the App Store</span>
                            <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                    </div>
                </div>
            </header>

            {/* App Preview */}
            <section className="py-16 bg-white">
                <div className="container mx-auto px-4">
                    <div className="grid md:grid-cols-2 gap-12 items-center">
                        <div>
                            <h2 className="text-3xl font-bold mb-6 text-[#43806c]">
                                Your Complete RV Maintenance Solution
                            </h2>
                            <p className="text-lg text-gray-600 mb-6">
                                The RV Help app puts the power of our nationwide network of certified RV technicians in your pocket.
                                Find service providers, book appointments, and manage your RV maintenance needs from anywhere.
                            </p>
                            <div className="space-y-4">
                                <div className="flex items-start gap-3">
                                    <CheckCircle className="h-6 w-6 text-[#43806c] flex-shrink-0 mt-1" />
                                    <div>
                                        <h3 className="font-semibold text-lg">Maintenance Tracking</h3>
                                        <p className="text-gray-600">Get notified when service is due and keep track of your RV's maintenance history</p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <CheckCircle className="h-6 w-6 text-[#43806c] flex-shrink-0 mt-1" />
                                    <div>
                                        <h3 className="font-semibold text-lg">Certified Technicians</h3>
                                        <p className="text-gray-600">Connect with certified mobile RV technicians who come to your location</p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <CheckCircle className="h-6 w-6 text-[#43806c] flex-shrink-0 mt-1" />
                                    <div>
                                        <h3 className="font-semibold text-lg">Easy Scheduling</h3>
                                        <p className="text-gray-600">Book appointments and manage service requests with just a few taps</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="relative">
                            <Image
                                src="/images/app-screenshots/app-featured-image.jpg"
                                alt="RV Help App Preview"
                                width={500}
                                height={1000}
                                className="rounded-lg shadow-xl"
                            />
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Grid */}
            <section className="py-16 bg-gray-50">
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl font-bold mb-12 text-center text-[#43806c]">
                        Everything You Need for RV Service
                    </h2>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div className="bg-white p-6 rounded-lg shadow-sm">
                            <Calendar className="h-12 w-12 text-[#43806c] mb-4" />
                            <h3 className="text-xl font-semibold mb-2">Maintenance Scheduling</h3>
                            <p className="text-gray-600">Schedule regular maintenance and get reminders when service is due</p>
                        </div>
                        <div className="bg-white p-6 rounded-lg shadow-sm">
                            <Wrench className="h-12 w-12 text-[#43806c] mb-4" />
                            <h3 className="text-xl font-semibold mb-2">Mobile Service</h3>
                            <p className="text-gray-600">Find certified technicians who come to your location</p>
                        </div>
                        <div className="bg-white p-6 rounded-lg shadow-sm">
                            <FileText className="h-12 w-12 text-[#43806c] mb-4" />
                            <h3 className="text-xl font-semibold mb-2">Maintenance Checklist</h3>
                            <p className="text-gray-600">Track your RV's maintenance history and upcoming service needs</p>
                        </div>
                        <div className="bg-white p-6 rounded-lg shadow-sm">
                            <MessageSquare className="h-12 w-12 text-[#43806c] mb-4" />
                            <h3 className="text-xl font-semibold mb-2">Direct Communication</h3>
                            <p className="text-gray-600">Chat with technicians and get real-time updates on your service</p>
                        </div>
                        <div className="bg-white p-6 rounded-lg shadow-sm">
                            <Shield className="h-12 w-12 text-[#43806c] mb-4" />
                            <h3 className="text-xl font-semibold mb-2">Certified Professionals</h3>
                            <p className="text-gray-600">All technicians are certified and verified through RVTAA and NRVIA</p>
                        </div>
                        <div className="bg-white p-6 rounded-lg shadow-sm">
                            <Clock className="h-12 w-12 text-[#43806c] mb-4" />
                            <h3 className="text-xl font-semibold mb-2">Fast Response</h3>
                            <p className="text-gray-600">Get service within days, not months, with our nationwide network</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* App Screenshots Carousel */}
            <section className="py-16 bg-white">
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl font-bold mb-8 text-[#43806c] text-center">
                        RV Help App Screenshots
                    </h2>
                    <Slider
                        dots={true}
                        infinite={false}
                        speed={500}
                        slidesToShow={4}
                        slidesToScroll={1}
                        responsive={[
                            { breakpoint: 1024, settings: { slidesToShow: 3 } },
                            { breakpoint: 768, settings: { slidesToShow: 2 } },
                            { breakpoint: 480, settings: { slidesToShow: 1 } }
                        ]}
                        centerMode={false}
                        centerPadding="0"
                        className="app-screenshots-carousel"
                        nextArrow={<NextArrow />}
                        prevArrow={<PrevArrow />}
                        arrows={true}
                    >
                        {appScreenshots.map((shot, idx) => (
                            <div key={idx} className="flex flex-col items-center px-2">
                                <img
                                    src={shot.src}
                                    alt={shot.alt}
                                    width={330}
                                    height={500}
                                    style={{ aspectRatio: '1290/2796', objectFit: 'cover' }}
                                    className="rounded-lg shadow-md border"
                                    loading="lazy"
                                />
                                <p className="mt-2 text-sm text-gray-600 text-center min-h-[40px]">{shot.caption}</p>
                            </div>
                        ))}
                    </Slider>
                </div>
            </section>


            {/* CTA Section */}
            <section className="py-16 bg-[#43806c] text-white">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">
                        Ready to Transform Your RV Service Experience?
                    </h2>
                    <p className="text-xl mb-8 max-w-2xl mx-auto">
                        Download the RV Help app today and join thousands of RV owners who are getting better service
                    </p>

                    <div className="flex justify-center w-auto mb-8">
                        <Link
                            href={config.apps.appStore.url}
                            className="w-[340px] rounded-md text-lg px-8 py-6 bg-white text-[#43806c] hover:bg-gray-100 flex items-center justify-center flex-shrink-1"
                        >
                            <span className="flex-shrink-1">Download on the App Store</span>
                            <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                    </div>
                </div>
            </section>


            {/* Pro Features */}
            <section className="py-16 bg-white">
                <div className="container mx-auto px-4">
                    <div className="max-w-3xl mx-auto text-center">
                        <h2 className="text-3xl font-bold mb-6 text-[#43806c]">
                            Upgrade to Pro for Premium Features
                        </h2>
                        <p className="text-lg text-gray-600 mb-8">
                            Get access to exclusive features and benefits with RV Help Pro membership
                        </p>
                        <div className="space-y-4 text-left">
                            <div className="flex items-start gap-3">
                                <CheckCircle className="h-6 w-6 text-[#43806c] flex-shrink-0 mt-1" />
                                <div>
                                    <h3 className="font-semibold text-lg">Unlimited Pre-Service Diagnostic Calls</h3>
                                    <p className="text-gray-600">Get expert advice before scheduling service</p>
                                </div>
                            </div>
                            <div className="flex items-start gap-3">
                                <CheckCircle className="h-6 w-6 text-[#43806c] flex-shrink-0 mt-1" />
                                <div>
                                    <h3 className="font-semibold text-lg">Nationwide Provider Discounts</h3>
                                    <p className="text-gray-600">Save on service with exclusive member discounts</p>
                                </div>
                            </div>
                            <div className="flex items-start gap-3">
                                <CheckCircle className="h-6 w-6 text-[#43806c] flex-shrink-0 mt-1" />
                                <div>
                                    <h3 className="font-semibold text-lg">Advanced Maintenance Tracking</h3>
                                    <p className="text-gray-600">Get detailed maintenance history and predictive service alerts</p>
                                </div>
                            </div>
                        </div>

                        <Link href="/pro-membership" className="btn btn-primary mt-8">
                            Learn More About Pro Membership
                            <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>

                    </div>
                </div>
            </section>
        </div>
    );
} 