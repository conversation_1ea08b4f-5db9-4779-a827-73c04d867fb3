"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { AlertTriangle, CheckCircle } from "lucide-react";
import { useEffect, useState } from "react";

export default function ClearBanMarkersPage() {
	const [cleared, setCleared] = useState(false);
	const [hasMarkers, setHasMarkers] = useState(false);

	useEffect(() => {
		// Check if ban markers exist
		const hasBanMarkers =
			localStorage.getItem("rvhelp_banned") === "true" ||
			document.cookie.includes("rvhelp_banned=true");

		setHasMarkers(hasBanMarkers);
	}, []);

	const clearBanMarkers = () => {
		// Clear localStorage ban markers
		localStorage.removeItem("rvhelp_banned");
		localStorage.removeItem("rvhelp_ban_message");
		localStorage.removeItem("rvhelp_ban_timestamp");

		// Clear cookie ban markers
		document.cookie =
			"rvhelp_banned=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict";
		document.cookie =
			"rvhelp_ban_message=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict";
		document.cookie =
			"rvhelp_ban_timestamp=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict";

		console.log("✅ Ban markers cleared for unbanned user");
		setCleared(true);
		setHasMarkers(false);
	};

	return (
		<div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
			<Card className="w-full max-w-2xl">
				<CardHeader className="text-center">
					<div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
						{cleared ? (
							<CheckCircle className="h-8 w-8 text-green-600" />
						) : (
							<AlertTriangle className="h-8 w-8 text-blue-600" />
						)}
					</div>
					<CardTitle className="text-3xl text-blue-600 mb-2">
						Clear Ban Markers
					</CardTitle>
					<CardDescription className="text-lg">
						{cleared
							? "Your ban markers have been successfully cleared."
							: "This page helps clear any remaining ban markers from your browser."}
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-6">
					{cleared ? (
						<div className="bg-green-50 border border-green-200 rounded-lg p-6">
							<div className="flex items-center space-x-3">
								<CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
								<div className="flex-1">
									<h3 className="font-medium text-green-800 mb-2">Success!</h3>
									<p className="text-sm text-green-700">
										All ban markers have been cleared from your browser. You can
										now navigate to the homepage.
									</p>
								</div>
							</div>
						</div>
					) : hasMarkers ? (
						<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
							<div className="flex items-center space-x-3">
								<AlertTriangle className="h-5 w-5 text-yellow-600 flex-shrink-0" />
								<div className="flex-1">
									<h3 className="font-medium text-yellow-800 mb-2">
										Ban Markers Detected
									</h3>
									<p className="text-sm text-yellow-700 mb-4">
										Your browser has ban markers that may prevent you from
										accessing the site normally. If your ban has been lifted,
										click the button below to clear these markers.
									</p>
									<Button
										onClick={clearBanMarkers}
										className="bg-blue-600 hover:bg-blue-700"
									>
										Clear Ban Markers
									</Button>
								</div>
							</div>
						</div>
					) : (
						<div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
							<div className="flex items-center space-x-3">
								<CheckCircle className="h-5 w-5 text-blue-600 flex-shrink-0" />
								<div className="flex-1">
									<h3 className="font-medium text-blue-800 mb-2">
										No Ban Markers Found
									</h3>
									<p className="text-sm text-blue-700">
										Your browser doesn't have any ban markers. You should be
										able to access the site normally.
									</p>
								</div>
							</div>
						</div>
					)}

					<div className="text-center">
						<a href="/" className="text-blue-600 hover:underline font-medium">
							Return to Homepage
						</a>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
