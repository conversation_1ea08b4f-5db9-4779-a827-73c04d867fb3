"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import config from "@/config";
import { useAuth } from "@/lib/hooks/useAuth";
import { Copy, Download } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";

const logoOptions = [
    {
        id: "primary",
        name: "Primary Logo",
        file: "https://rvhelp-prod.s3.us-east-1.amazonaws.com/public/logo.webp",
        description: "Full color logo on light background",
        previewBg: "bg-white"
    },
    {
        id: "white",
        name: "White Logo",
        file: "https://rvhelp-prod.s3.us-east-1.amazonaws.com/public/rvhelp-white.png",
        description: "White logo for dark backgrounds",
        previewBg: "bg-gray-900"
    },
    {
        id: "vertical",
        name: "Vertical Logo",
        file: "https://rvhelp-prod.s3.us-east-1.amazonaws.com/public/rvhelp-logo-vertical.png",
        description: "Vertical logo for vertical layouts",
        previewBg: "bg-white"
    },
    {
        id: "vertical-white",
        name: "Vertical White Logo",
        file: "https://rvhelp-prod.s3.us-east-1.amazonaws.com/public/rvhelp-logo-vertical-white.png",
        description: "White logo for dark backgrounds",
        previewBg: "bg-gray-900"
    },
    {
        id: "icon",
        name: "Icon Only",
        file: "https://rvhelp-prod.s3.us-east-1.amazonaws.com/public/icon.png",
        description: "Icon without text",
        previewBg: "bg-white"
    },
    {
        id: "icon-white",
        name: "Icon Only White",
        file: "https://rvhelp-prod.s3.us-east-1.amazonaws.com/public/icon-light.png",
        description: "White icon for dark backgrounds",
        previewBg: "bg-gray-900"
    }
];

const textOptions = [
    {
        id: "find-rv-repair",
        text: "Find RV Repair in Your Area",
        description: "Generic RV repair text"
    },
    {
        id: "find-rv-inspection",
        text: "Find RV Inspection in Your Area",
        description: "Generic RV inspection text"
    },
    {
        id: "certified-providers",
        text: "Certified RV Service Providers",
        description: "Emphasizes certification"
    },
    {
        id: "mobile-rv-services",
        text: "Mobile RV Services Near You",
        description: "Emphasizes mobile services"
    },
    {
        id: "rv-help-directory",
        text: "RV Help Directory",
        description: "Simple and direct"
    },
    {
        id: "custom",
        text: "Custom Text",
        description: "Enter your own text"
    }
];

export function BacklinkGeneratorClient() {
    const { user } = useAuth();
    const [providerSlug, setProviderSlug] = useState("");
    const [selectedLogo, setSelectedLogo] = useState("primary");
    const [selectedText, setSelectedText] = useState("find-rv-repair");
    const [customText, setCustomText] = useState("");
    const [activeTab, setActiveTab] = useState("logo");
    const [loadingListing, setLoadingListing] = useState(false);

    // Fetch user's listing on component mount
    useEffect(() => {
        if (!user || user.role !== "PROVIDER") return;

        const fetchListing = async () => {
            try {
                setLoadingListing(true);
                const res = await fetch("/api/user/listings");
                const data = await res.json();

                if (data[0] && data[0].slug) {
                    setProviderSlug(data[0].slug);
                }
            } catch (error) {
                console.error("Error fetching user listings:", error);
            } finally {
                setLoadingListing(false);
            }
        };

        fetchListing();
    }, [user]);

    const getDisplayText = () => {
        if (selectedText === "custom") {
            return customText || "Find RV Services";
        }
        return textOptions.find(option => option.id === selectedText)?.text || "Find RV Services";
    };

    const getBacklinkUrl = () => {
        if (!providerSlug) return "";
        return `${config.appUrl}/backlink/${providerSlug}`;
    };

    const getLogoCode = () => {
        const logo = logoOptions.find(option => option.id === selectedLogo);
        if (!logo || !providerSlug) return "";

        return `<a href="${getBacklinkUrl()}" target="_blank" rel="noopener noreferrer">
  <img src="${logo.file}" alt="${getDisplayText()}" style="max-width: 200px; height: auto;">
</a>`;
    };

    const getTextCode = () => {
        if (!providerSlug) return "";
        return `<a href="${getBacklinkUrl()}" target="_blank" rel="noopener noreferrer">${getDisplayText()}</a>`;
    };

    const getButtonCode = () => {
        if (!providerSlug) return "";
        return `<a href="${getBacklinkUrl()}" target="_blank" rel="noopener noreferrer" style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500;">
  ${getDisplayText()}
</a>`;
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
    };

    const downloadCode = (code: string, filename: string) => {
        const blob = new Blob([code], { type: "text/html" });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    };

    return (
        <div className="min-h-screen bg-gray-50">
            <header className="bg-primary text-white py-16 px-4">
                <div className="container mx-auto text-center">
                    <h1 className="text-4xl font-bold mb-4">Share RV Help</h1>
                    <p className="text-xl max-w-3xl mx-auto">
                        Share the RV Help logo and link on your website to help customers find you.
                    </p>
                </div>
            </header>

            <main className="container mx-auto py-16 px-4">
                <div className="max-w-4xl mx-auto space-y-8">
                    {/* Provider Slug Input */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Step 1: Your Provider Slug</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="provider-slug">Provider Slug</Label>
                                    {loadingListing ? (
                                        <Input
                                            id="provider-slug"
                                            placeholder="Loading your provider slug..."
                                            disabled
                                        />
                                    ) : (
                                        <Input
                                            id="provider-slug"
                                            placeholder="e.g., johns-rv-repair"
                                            value={providerSlug}
                                            onChange={(e) => setProviderSlug(e.target.value)}
                                        />
                                    )}
                                    <p className="text-sm text-gray-500 mt-1">
                                        This is the unique identifier in your RV Help profile URL: rvhelp.com/providers/[your-slug]
                                    </p>
                                    {providerSlug && (
                                        <p className="text-sm text-green-600 mt-1">
                                            ✅ Your backlink will redirect to your current service area
                                        </p>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Display Options */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Step 2: Choose Your Display Style</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Tabs value={activeTab} onValueChange={setActiveTab}>
                                <TabsList className="grid w-full grid-cols-3">
                                    <TabsTrigger value="logo">Logo</TabsTrigger>
                                    <TabsTrigger value="text">Text Link</TabsTrigger>
                                    <TabsTrigger value="button">Button</TabsTrigger>
                                </TabsList>

                                <TabsContent value="logo" className="space-y-6">
                                    <div>
                                        <h3 className="text-lg font-semibold mb-4">Choose Logo Style</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {logoOptions.map((logo) => (
                                                <div
                                                    key={logo.id}
                                                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${selectedLogo === logo.id
                                                        ? "border-blue-500 bg-blue-50"
                                                        : "border-gray-200 hover:border-gray-300"
                                                        }`}
                                                    onClick={() => setSelectedLogo(logo.id)}
                                                >
                                                    <div className={`${logo.previewBg} p-4 rounded flex items-center justify-center mb-3`}>
                                                        <Image
                                                            src={logo.file}
                                                            alt={logo.name}
                                                            width={120}
                                                            height={60}
                                                            className="object-contain"
                                                        />
                                                    </div>
                                                    <h4 className="font-medium">{logo.name}</h4>
                                                    <p className="text-sm text-gray-500">{logo.description}</p>
                                                </div>
                                            ))}
                                        </div>
                                    </div>

                                    <div>
                                        <h3 className="text-lg font-semibold mb-4">Choose Link Text</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                            {textOptions.map((option) => (
                                                <div
                                                    key={option.id}
                                                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${selectedText === option.id
                                                        ? "border-blue-500 bg-blue-50"
                                                        : "border-gray-200 hover:border-gray-300"
                                                        }`}
                                                    onClick={() => setSelectedText(option.id)}
                                                >
                                                    <h4 className="font-medium">{option.text}</h4>
                                                    <p className="text-sm text-gray-500">{option.description}</p>
                                                </div>
                                            ))}
                                        </div>
                                        {selectedText === "custom" && (
                                            <div className="mt-4">
                                                <Label htmlFor="custom-text">Custom Text</Label>
                                                <Input
                                                    id="custom-text"
                                                    placeholder="Enter your custom link text"
                                                    value={customText}
                                                    onChange={(e) => setCustomText(e.target.value)}
                                                />
                                            </div>
                                        )}
                                    </div>
                                </TabsContent>

                                <TabsContent value="text" className="space-y-6">
                                    <div>
                                        <h3 className="text-lg font-semibold mb-4">Choose Link Text</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                            {textOptions.map((option) => (
                                                <div
                                                    key={option.id}
                                                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${selectedText === option.id
                                                        ? "border-blue-500 bg-blue-50"
                                                        : "border-gray-200 hover:border-gray-300"
                                                        }`}
                                                    onClick={() => setSelectedText(option.id)}
                                                >
                                                    <h4 className="font-medium">{option.text}</h4>
                                                    <p className="text-sm text-gray-500">{option.description}</p>
                                                </div>
                                            ))}
                                        </div>
                                        {selectedText === "custom" && (
                                            <div className="mt-4">
                                                <Label htmlFor="custom-text-2">Custom Text</Label>
                                                <Input
                                                    id="custom-text-2"
                                                    placeholder="Enter your custom link text"
                                                    value={customText}
                                                    onChange={(e) => setCustomText(e.target.value)}
                                                />
                                            </div>
                                        )}
                                    </div>
                                </TabsContent>

                                <TabsContent value="button" className="space-y-6">
                                    <div>
                                        <h3 className="text-lg font-semibold mb-4">Choose Button Text</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                            {textOptions.map((option) => (
                                                <div
                                                    key={option.id}
                                                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${selectedText === option.id
                                                        ? "border-blue-500 bg-blue-50"
                                                        : "border-gray-200 hover:border-gray-300"
                                                        }`}
                                                    onClick={() => setSelectedText(option.id)}
                                                >
                                                    <h4 className="font-medium">{option.text}</h4>
                                                    <p className="text-sm text-gray-500">{option.description}</p>
                                                </div>
                                            ))}
                                        </div>
                                        {selectedText === "custom" && (
                                            <div className="mt-4">
                                                <Label htmlFor="custom-text-3">Custom Text</Label>
                                                <Input
                                                    id="custom-text-3"
                                                    placeholder="Enter your custom button text"
                                                    value={customText}
                                                    onChange={(e) => setCustomText(e.target.value)}
                                                />
                                            </div>
                                        )}
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>

                    {/* Generated Code */}
                    {providerSlug && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Step 3: Copy Your Backlink Code</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div>
                                        <Label>Your Backlink URL</Label>
                                        <div className="flex gap-2 mt-1">
                                            <Input value={getBacklinkUrl()} readOnly />
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => copyToClipboard(getBacklinkUrl())}
                                            >
                                                <Copy className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>

                                    <div>
                                        <Label>Generated HTML Code</Label>
                                        <div className="mt-1">
                                            <Textarea
                                                value={
                                                    activeTab === "logo" ? getLogoCode() :
                                                        activeTab === "text" ? getTextCode() :
                                                            getButtonCode()
                                                }
                                                readOnly
                                                rows={6}
                                                className="font-mono text-sm"
                                            />
                                        </div>
                                        <div className="flex gap-2 mt-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => copyToClipboard(
                                                    activeTab === "logo" ? getLogoCode() :
                                                        activeTab === "text" ? getTextCode() :
                                                            getButtonCode()
                                                )}
                                            >
                                                <Copy className="h-4 w-4 mr-2" />
                                                Copy Code
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => downloadCode(
                                                    activeTab === "logo" ? getLogoCode() :
                                                        activeTab === "text" ? getTextCode() :
                                                            getButtonCode(),
                                                    `rvhelp-backlink-${activeTab}.html`
                                                )}
                                            >
                                                <Download className="h-4 w-4 mr-2" />
                                                Download
                                            </Button>
                                        </div>
                                    </div>

                                    {/* Preview */}
                                    <div>
                                        <Label>Preview</Label>
                                        <div className="mt-2 p-4 border rounded-lg bg-white">
                                            <div
                                                dangerouslySetInnerHTML={{
                                                    __html: activeTab === "logo" ? getLogoCode() :
                                                        activeTab === "text" ? getTextCode() :
                                                            getButtonCode()
                                                }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Instructions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>How to Use Your Backlink</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="prose prose-gray max-w-none">
                                <ol className="list-decimal pl-6 space-y-2">
                                    <li>Copy the generated HTML code above</li>
                                    <li>Paste it into your website&apos;s HTML where you want the backlink to appear</li>
                                    <li>The link will automatically redirect visitors to your current service area page</li>
                                    <li>If you move to a new location, update your profile on RV Help and the backlink will automatically redirect to the new area</li>
                                </ol>
                                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <h4 className="font-semibold text-blue-900 mb-2">SEO Benefits</h4>
                                    <p className="text-blue-800 text-sm">
                                        This backlink will help improve search engine rankings and drive targeted traffic to your city page. The link automatically updates when you change locations, ensuring visitors always find your current service area.
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </main>
        </div>
    );
} 