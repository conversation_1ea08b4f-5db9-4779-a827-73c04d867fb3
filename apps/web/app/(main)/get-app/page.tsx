"use client";

import { But<PERSON> } from "@/components/ui/button";
import config from "@/config";
import { Apple, Smartphone } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

// Utility function to preserve UTM parameters in redirects
function getAppStoreUrlWithUtm(baseUrl: string): string {
	const url = new URL(window.location.href);
	const utmParams = new URLSearchParams();

	// Extract UTM parameters from current URL
	const utmKeys = [
		"utm_source",
		"utm_medium",
		"utm_campaign",
		"utm_term",
		"utm_content"
	];
	utmKeys.forEach((key) => {
		const value = url.searchParams.get(key);
		if (value) {
			utmParams.set(key, value);
		}
	});

	// If we have UTM parameters, append them to the app store URL
	if (utmParams.toString()) {
		const separator = baseUrl.includes("?") ? "&" : "?";
		return `${baseUrl}${separator}${utmParams.toString()}`;
	}

	return baseUrl;
}

export default function GetAppPage() {
	const router = useRouter();

	useEffect(() => {
		// Check if user is on iOS device
		const isIOS =
			/iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;

		const isAndroid = /Android/.test(navigator.userAgent);
		if (isIOS) {
			// Redirect to App Store with UTM parameters
			const appStoreUrl = getAppStoreUrlWithUtm(config.apps.appStore.url);
			window.location.href = appStoreUrl;
		} else if (isAndroid) {
			// Redirect to Google Play with UTM parameters
			const googlePlayUrl = getAppStoreUrlWithUtm(config.apps.googlePlay.url);
			window.location.href = googlePlayUrl;
		}
	}, []);

	return (
		<div className="min-h-screen">
			{/* Hero Section */}
			<header className="relative text-white py-24 px-4">
				<div className="absolute inset-0">
					<Image
						src="/images/myrvresource.webp"
						alt="RV Help Mobile App"
						layout="fill"
						objectFit="cover"
						quality={100}
					/>
					<div className="absolute inset-0 bg-gradient-to-r from-[#43806c]/90 to-[#2c5446]/90"></div>
				</div>
				<div className="container mx-auto relative z-10">
					<h1 className="text-4xl md:text-5xl font-bold mb-4 text-center">
						Get the RV Help App
					</h1>
					<p className="text-xl text-center max-w-3xl mx-auto">
						Find mobile RV repair services, RV inspectors, book appointments,
						and manage your RV maintenance and inspection needs on the go
					</p>
				</div>
			</header>

			{/* Main Content */}
			<main className="container mx-auto px-4 py-16">
				{/* App Features */}
				<div className="max-w-3xl mx-auto text-center mb-16">
					<h2 className="text-3xl font-bold mb-8 text-[#43806c]">
						Everything You Need for RV Service & Inspection
					</h2>
					<p className="text-lg text-gray-600 mb-6">
						The RV Help mobile app puts the power of our nationwide network of
						certified RV technicians and inspectors in your pocket. Find service
						providers, book appointments, and manage your RV maintenance and
						inspection needs from anywhere.
					</p>
				</div>

				{/* Download Options */}
				<div className="grid gap-6 md:grid-cols-2 max-w-4xl mx-auto">
					{/* iOS Download */}
					<div className="bg-white rounded-lg shadow-lg p-8 text-center">
						<Apple className="h-12 w-12 mx-auto mb-4 text-[#43806c]" />
						<h3 className="text-2xl font-bold mb-4">Download for iOS</h3>
						<p className="text-gray-600 mb-6">Available on iPhone and iPad</p>
						<Button
							className="w-full"
							onClick={() => {
								const appStoreUrl = getAppStoreUrlWithUtm(
									config.apps.appStore.url
								);
								window.location.href = appStoreUrl;
							}}
						>
							Download on the App Store
						</Button>
					</div>

					{/* Android Download */}
					<div className="bg-white rounded-lg shadow-lg p-8 text-center">
						<Smartphone className="h-12 w-12 mx-auto mb-4 text-[#43806c]" />
						<h3 className="text-2xl font-bold mb-4">
							{config.apps.googlePlay.url
								? "Download for Android"
								: "Coming Soon for Android"}
						</h3>
						<p className="text-gray-600 mb-6">
							{config.apps.googlePlay.url
								? "Available on Android phones and tablets"
								: "In the meantime, visit our mobile site"}
						</p>
						<Button
							className="w-full"
							variant={config.apps.googlePlay.url ? "default" : "outline"}
							onClick={() => {
								const googlePlayUrl = getAppStoreUrlWithUtm(
									config.apps.googlePlay.url
								);
								window.location.href = googlePlayUrl;
							}}
						>
							{config.apps.googlePlay.url
								? "Get it on Google Play"
								: "Visit Mobile Site"}
						</Button>
					</div>
				</div>
			</main>
		</div>
	);
}
