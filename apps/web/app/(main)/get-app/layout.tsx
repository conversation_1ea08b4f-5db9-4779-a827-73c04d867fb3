import { generateMetadata as generateCategoryMetadata } from "@/lib/metadata";

export async function generateMetadata() {
    return generateCategoryMetadata({
        title: "Download RV Help Mobile App",
        description: "Get the RV Help mobile app for iOS and Android. Find mobile RV repair services, book appointments, and manage your RV maintenance needs on the go.",
        path: "/get-app",
        ogImage: "/images/myrvresource.webp"
    });
}

export default function GetAppLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return children;
} 