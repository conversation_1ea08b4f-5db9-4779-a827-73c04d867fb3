import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function LeadsLoading() {
    return (
        <div className="space-y-6">
            {/* Tabs Skeleton */}
            <Tabs defaultValue="pending" className="w-full">
                <TabsList className="w-full sm:w-auto">
                    <TabsTrigger value="pending" disabled>
                        <Skeleton className="h-4 w-16" />
                    </TabsTrigger>
                    <TabsTrigger value="accepted" disabled>
                        <Skeleton className="h-4 w-16" />
                    </TabsTrigger>
                    <TabsTrigger value="rejected" disabled>
                        <Skeleton className="h-4 w-16" />
                    </TabsTrigger>
                    <TabsTrigger value="all" disabled>
                        <Skeleton className="h-4 w-16" />
                    </TabsTrigger>
                </TabsList>
            </Tabs>

            {/* Leads Section */}
            <Card>
                <CardHeader>
                    <CardTitle>Service Leads</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {[1, 2, 3].map((i) => (
                            <Card key={i} className="hover:shadow-md transition-shadow">
                                <CardHeader className="pb-2">
                                    <div className="flex justify-between items-start">
                                        <div className="space-y-1.5">
                                            <Skeleton className="h-5 w-32" />
                                            <Skeleton className="h-4 w-24" />
                                        </div>
                                        <Skeleton className="h-6 w-16" />
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Skeleton className="h-4 w-full" />
                                        <Skeleton className="h-4 w-3/4" />
                                    </div>
                                    <div className="space-y-2">
                                        <Skeleton className="h-4 w-24" />
                                        <Skeleton className="h-4 w-32" />
                                    </div>
                                    <div className="flex gap-2">
                                        <Skeleton className="h-9 w-24" />
                                        <Skeleton className="h-9 w-24" />
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </CardContent>
            </Card>

            {/* Troubleshooting Requests Section */}
            <Card>
                <CardHeader>
                    <CardTitle>Troubleshooting Requests</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {[1, 2].map((i) => (
                            <Card key={i} className="hover:shadow-md transition-shadow">
                                <CardHeader className="pb-2">
                                    <div className="flex justify-between items-start">
                                        <div className="space-y-1.5">
                                            <Skeleton className="h-5 w-32" />
                                            <Skeleton className="h-4 w-24" />
                                        </div>
                                        <Skeleton className="h-6 w-16" />
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Skeleton className="h-4 w-full" />
                                        <Skeleton className="h-4 w-3/4" />
                                    </div>
                                    <div className="space-y-2">
                                        <Skeleton className="h-4 w-24" />
                                        <Skeleton className="h-4 w-32" />
                                    </div>
                                    <div className="flex gap-2">
                                        <Skeleton className="h-9 w-24" />
                                        <Skeleton className="h-9 w-24" />
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
} 