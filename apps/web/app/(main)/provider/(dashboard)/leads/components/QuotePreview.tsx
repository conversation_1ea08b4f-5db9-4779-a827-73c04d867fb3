import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { formatSchedulingTimeframe } from "@/lib/utils";
import { Listing, RVHelpVerificationLevel } from "@rvhelp/database";
import { CheckCircle, MapPin, Star } from "lucide-react";
import { QuoteWithMessages } from "../../jobs/[id]/types";

interface QuotePreviewProps {
	quote: QuoteWithMessages & {
		listing: Listing & { location?: { city: string; state: string } };
	};
	enableDispatchDiscount: boolean;
	enableHourlyDiscount: boolean;
	enablePricing: boolean;
	message: string;
	schedulingTimeframe: string;
}

export function QuotePreview({
	quote,
	enableDispatchDiscount,
	enableHourlyDiscount,
	enablePricing,
	message,
	schedulingTimeframe
}: QuotePreviewProps) {
	const renderStarRating = () => {
		const rating = quote?.listing?.rating || 0;
		return (
			<div className="flex items-center justify-center space-x-1 md:justify-start">
				{[...Array(5)].map((_, i) => (
					<Star
						key={i}
						className={`h-4 w-4 ${i < rating ? "text-yellow-500 fill-yellow-500" : "text-gray-300"}`}
					/>
				))}
				{quote?.listing?.num_reviews && (
					<span className="text-xs text-gray-500">
						({quote?.listing?.num_reviews})
					</span>
				)}
			</div>
		);
	};

	return (
		<Card className="mt-2">
			<CardContent className="pt-6">
				<div className="flex flex-col items-center gap-4 text-center md:flex-row md:items-start md:text-left">
					{/* Provider Avatar */}
					<Avatar className="h-16 w-16 flex-shrink-0 border-2 border-gray-100">
						{quote?.listing?.profile_image && (
							<AvatarImage
								src={quote.listing.profile_image}
								alt={quote.listing.business_name || ""}
							/>
						)}
						<AvatarFallback className="bg-primary text-white text-xl">
							{quote?.listing?.first_name?.[0] || "P"}
							{quote?.listing?.last_name?.[0] || ""}
						</AvatarFallback>
					</Avatar>

					{/* Provider Info */}
					<div className="w-full flex-1">
						<div className="flex flex-col md:flex-row md:items-start md:justify-between">
							<div className="space-y-1">
								<div className="flex flex-wrap items-center justify-center gap-2 md:justify-start">
									<h3 className="text-lg font-semibold">
										{quote?.listing?.business_name ||
											`${quote?.listing?.first_name || ""} ${quote?.listing?.last_name || ""}`}
									</h3>
									<Badge
										variant="default"
										className="bg-green-100 text-green-700 hover:bg-green-200"
									>
										Proposal Received
									</Badge>
								</div>
								{renderStarRating()}
							</div>
							<div className="mt-2 flex flex-row justify-center gap-1.5 md:mt-0 md:flex-col md:justify-start">
								{quote?.listing?.rv_help_verification_level ===
									RVHelpVerificationLevel.VERIFIED && (
									<Badge
										variant="outline"
										className="bg-emerald-50 text-emerald-700 border-emerald-200"
									>
										<CheckCircle className="h-3 w-3 mr-1" />
										Verified Pro
									</Badge>
								)}
								{(enableDispatchDiscount || enableHourlyDiscount) &&
									!enablePricing && (
										<Badge
											variant="outline"
											className="bg-blue-50 text-blue-700 border-blue-200 whitespace-nowrap"
										>
											<Star className="h-3 w-3 mr-1 fill-current" />
											Pro Discounts Available
										</Badge>
									)}
							</div>
						</div>

						{/* Location and Description */}
						<div className="mt-3 space-y-2">
							<div className="flex items-center justify-center gap-2 text-sm text-gray-600 md:justify-start">
								<MapPin className="h-4 w-4" />
								<span>
									{quote?.listing?.location?.city},{" "}
									{quote?.listing?.location?.state}
								</span>
							</div>
							<p className="text-sm text-gray-600 line-clamp-2">
								{quote?.listing?.short_description ||
									quote?.listing?.long_description ||
									"No description available"}
							</p>
						</div>

						{/* Quote Details */}
						{(enablePricing || message || schedulingTimeframe) && (
							<div className="my-4 rounded-md bg-green-50 border border-green-200 px-4 py-3 text-left">
								<div className="flex flex-wrap items-center gap-6 mb-2">
									{enablePricing && quote?.listing?.pricing_settings && (
										<>
											<div>
												<span className="font-semibold">Service Call:</span> $
												{enableDispatchDiscount
													? (
															(quote.listing.pricing_settings as any)
																.dispatch_fee * 0.75
														).toFixed(2)
													: (
															quote.listing.pricing_settings as any
														).dispatch_fee.toFixed(2)}
												{enableDispatchDiscount && (
													<span className="text-xs text-green-700 ml-1">
														(Pro Discount Applied)
													</span>
												)}
											</div>
											<div>
												<span className="font-semibold">Hourly Rate:</span>{" "}
												{enableHourlyDiscount ? (
													<>
														First 4 hours: $
														{(
															(quote.listing.pricing_settings as any)
																.hourly_rate * 0.9
														).toFixed(2)}
														/hr
														<span className="text-xs text-green-700 ml-1">
															(Pro Discount)
														</span>
														<br />
														<span className="text-sm">
															Additional hours: $
															{(
																quote.listing.pricing_settings as any
															).hourly_rate.toFixed(2)}
															/hr
														</span>
													</>
												) : (
													<>
														$
														{(
															quote.listing.pricing_settings as any
														).hourly_rate.toFixed(2)}
														/hr
													</>
												)}
											</div>
										</>
									)}
									{schedulingTimeframe && (
										<div>
											<span className="font-semibold">
												Scheduling Timeframe:
											</span>{" "}
											{formatSchedulingTimeframe(schedulingTimeframe)}
										</div>
									)}
								</div>
								{message && (
									<blockquote className="border-l-4 border-green-400 pl-4 italic text-gray-700">
										'{message}'
									</blockquote>
								)}
							</div>
						)}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
