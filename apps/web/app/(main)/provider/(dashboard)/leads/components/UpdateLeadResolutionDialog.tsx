import { But<PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ResolutionStatus } from "@rvhelp/database";

interface UpdateLeadResolutionDialogProps {
	showResolutionDialog: boolean;
	setShowResolutionDialog: (open: boolean) => void;
	resolutionStatus: ResolutionStatus;
	setResolutionStatus: (status: ResolutionStatus) => void;
	resolutionDetails: string;
	setResolutionDetails: (details: string) => void;
	validationError: string | null;
	setValidationError: (error: string | null) => void;
	handleResolutionSubmit: () => void;
	isResponding: boolean;
}
export function UpdateLeadResolutionDialog({
	showResolutionDialog,
	setShowResolutionDialog,
	resolutionStatus,
	setResolutionStatus,
	resolutionDetails,
	setResolutionDetails,
	validationError,
	setValidationError,
	handleResolutionSubmit,
	isResponding
}: UpdateLeadResolutionDialogProps) {
	return (
		<Dialog
			open={showResolutionDialog}
			onOpenChange={(open) => {
				setShowResolutionDialog(open);
				if (!open) {
					setValidationError(null);
					setResolutionDetails("");
				}
			}}
		>
			<DialogContent className="max-w-xl">
				<DialogHeader>
					<DialogTitle>Update Lead Resolution</DialogTitle>
				</DialogHeader>
				<div className="space-y-4 py-4">
					<div className="space-y-2">
						<label className="text-sm font-medium">Resolution Status</label>
						<Select
							value={resolutionStatus}
							onValueChange={(value: ResolutionStatus) => {
								setResolutionStatus(value);
								setValidationError(null);
							}}
						>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value={ResolutionStatus.COMPLETED}>
									Completed Successfully
								</SelectItem>
								<SelectItem value={ResolutionStatus.CANCELLED}>
									Customer Cancelled
								</SelectItem>
								<SelectItem value={ResolutionStatus.NO_RESPONSE}>
									No Response from Customer
								</SelectItem>
								<SelectItem value={ResolutionStatus.NOT_VIABLE}>
									Not Viable After Contact
								</SelectItem>
								<SelectItem value={ResolutionStatus.REFERRED}>
									Referred to Another Provider
								</SelectItem>
								<SelectItem value={ResolutionStatus.OTHER}>
									Other (Requires Notes)
								</SelectItem>
							</SelectContent>
						</Select>
					</div>
					<div className="space-y-2">
						<label className="text-sm font-medium flex flex-col gap-1">
							<span>
								Additional Details{" "}
								{resolutionStatus === ResolutionStatus.OTHER && (
									<span className="text-red-500">*</span>
								)}
							</span>
							<span className="text-xs text-gray-500">
								(Not shared with customer)
							</span>
						</label>
						<Textarea
							value={resolutionDetails}
							onChange={(e) => {
								setResolutionDetails(e.target.value);
								setValidationError(null);
							}}
							placeholder={
								resolutionStatus === ResolutionStatus.OTHER
									? "Please provide details about the resolution (required)"
									: "Add any additional details about the resolution..."
							}
							className={`min-h-[100px] ${
								validationError ? "border-red-500" : ""
							}`}
						/>
						{validationError && (
							<p className="text-sm text-red-500 mt-1">{validationError}</p>
						)}
					</div>
				</div>
				<div className="flex justify-end gap-3">
					<Button
						variant="outline"
						onClick={() => setShowResolutionDialog(false)}
					>
						Cancel
					</Button>
					<Button
						variant="default"
						onClick={handleResolutionSubmit}
						disabled={isResponding}
						className="bg-[#43806c] text-white hover:bg-[#2c5446]"
					>
						Update Resolution
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
}
