"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	CheckCircle,
	CreditCard,
	ExternalLink,
	FileText,
	MessageSquare,
	Settings
} from "lucide-react";

interface WarrantyLeadSuccessDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onNavigateToJob: () => void;
}

export function WarrantyLeadSuccessDialog({
	open,
	onOpenChange,
	onNavigateToJob
}: WarrantyLeadSuccessDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-lg">
				<DialogHeader>
					<div className="text-center">
						<div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
							<CheckCircle className="h-8 w-8 text-green-600" />
						</div>
						<DialogTitle className="text-2xl font-bold text-gray-900">
							Warranty Lead Accepted!
						</DialogTitle>
						<DialogDescription className="text-gray-600 mt-2">
							Great! You've successfully accepted this warranty lead. Here's
							what you can do next:
						</DialogDescription>
					</div>
				</DialogHeader>

				<div className="py-6 space-y-6">
					{/* Features Grid */}
					<div className="grid grid-cols-1 gap-4">
						<div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
							<MessageSquare className="h-5 w-5 text-blue-600 mt-0.5" />
							<div>
								<h4 className="font-medium text-blue-900">
									Communicate with Customer
								</h4>
								<p className="text-sm text-blue-700">
									Send messages, share updates, and coordinate with the customer
								</p>
							</div>
						</div>

						<div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
							<FileText className="h-5 w-5 text-green-600 mt-0.5" />
							<div>
								<h4 className="font-medium text-green-900">
									Request Authorization
								</h4>
								<p className="text-sm text-green-700">
									Submit repair details and get approval from the manufacturer
								</p>
							</div>
						</div>

						<div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
							<CreditCard className="h-5 w-5 text-purple-600 mt-0.5" />
							<div>
								<h4 className="font-medium text-purple-900">Request Payment</h4>
								<p className="text-sm text-purple-700">
									Submit invoices and get paid for your warranty work
								</p>
							</div>
						</div>

						<div className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
							<Settings className="h-5 w-5 text-orange-600 mt-0.5" />
							<div>
								<h4 className="font-medium text-orange-900">
									Manage Job Details
								</h4>
								<p className="text-sm text-orange-700">
									Update status, add notes, and track progress
								</p>
							</div>
						</div>
					</div>

					{/* Action Button */}
					<div className="text-center">
						<Button
							onClick={onNavigateToJob}
							className="w-full h-12 bg-[#43806c] hover:bg-[#2c5446] text-white font-semibold text-base flex items-center justify-center gap-2"
						>
							<ExternalLink className="h-5 w-5" />
							View this Job
						</Button>
						<p className="text-xs text-gray-500 mt-2">
							You can always access this job from your Jobs page
						</p>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
