"use client";

import { Listing, TroubleshootingRequest, User } from "@rvhelp/database";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import TroubleshootingRequestDetails from "./TroubleshootingRequestDetails";

type TroubleshootingRequestWithUser = TroubleshootingRequest & {
	user: User;
	listing: Listing;
};

export default function TroubleshootingRequestPage({
	params
}: {
	params: { requestId: string };
}) {
	const [request, setRequest] = useState<TroubleshootingRequestWithUser | null>(
		null
	);
	const [loading, setLoading] = useState(true);
	useEffect(() => {
		const fetchRequest = async () => {
			try {
				const response = await fetch(
					`/api/provider/troubleshooting/${params.requestId}`
				);

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = await response.json();
				setRequest(data);
			} catch (error) {
				console.error('Error fetching troubleshooting request:', error);
				setRequest(null);
			} finally {
				setLoading(false);
			}
		};
		fetchRequest();
	}, [params.requestId]);

	if (loading) {
		return <div>Loading...</div>;
	}

	if (!request) {
		return <div>Request not found</div>;
	}

	return (
		<div className="max-w-3xl mx-auto">
			{/* Breadcrumbs */}
			<nav className="flex items-center gap-1 text-sm text-gray-500 mb-6">
				<Link
					href="/provider/leads"
					className="hover:text-gray-800 transition-colors"
				>
					Leads
				</Link>
				<ChevronRight className="w-4 h-4" />
				<Link
					href="/provider/leads"
					className="hover:text-gray-800 transition-colors"
				>
					Troubleshooting
				</Link>
				<ChevronRight className="w-4 h-4" />
				<span className="text-gray-800">
					{request.user.first_name} {request.user.last_name}
				</span>
			</nav>

			<TroubleshootingRequestDetails request={request} />
		</div>
	);
}
