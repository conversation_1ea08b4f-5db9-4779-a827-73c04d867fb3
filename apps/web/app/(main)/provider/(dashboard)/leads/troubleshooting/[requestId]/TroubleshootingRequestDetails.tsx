"use client";

import {
	<PERSON>,
	CardContent,
	CardD<PERSON><PERSON>,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Listing, TroubleshootingRequest, User } from "@rvhelp/database";
import { format } from "date-fns";
import { useState } from "react";
import toast from "react-hot-toast";

interface RequestWithRelations extends TroubleshootingRequest {
	user: User;
	listing: Listing;
}

type ActionType = "accepted" | "rejected" | "completed" | "send-review";

export default function TroubleshootingRequestDetails({
	request
}: {
	request: RequestWithRelations;
}) {
	const [isUpdating, setIsUpdating] = useState(false);
	const [notes, setNotes] = useState("");
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [pendingAction, setPendingAction] = useState<ActionType | null>(null);
	const [requestReview, setRequestReview] = useState(false);
	const [reviewDelay, setReviewDelay] = useState(24); // Default 24 hours
	const [reviewDelayUnit, setReviewDelayUnit] = useState<"hours" | "days">(
		"hours"
	);

	const handleActionClick = (action: ActionType) => {
		setPendingAction(action);
		setIsModalOpen(true);
		// Reset review settings when opening modal
		setRequestReview(false);
		setReviewDelay(24);
		setReviewDelayUnit("hours");
	};

	const handleStatusUpdate = async () => {
		if (!pendingAction) return;

		if (
			pendingAction !== "completed" &&
			pendingAction !== "send-review" &&
			!notes
		) {
			toast.error("Please add a note for the customer");
			return;
		}

		setIsUpdating(true);
		try {
			// Calculate review delay in hours
			const reviewDelayHours =
				reviewDelayUnit === "days" ? reviewDelay * 24 : reviewDelay;

			const response = await fetch(
				`/api/provider/troubleshooting/${request.id}?action=${pendingAction}`,
				{
					method: "PUT",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						requestId: request.id,
						notes,
						...(pendingAction === "completed" && requestReview
							? {
									requestReview: true,
									reviewDelayHours
								}
							: {})
					})
				}
			);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to update request");
			}

			if (pendingAction === "send-review") {
				toast.success("Review request sent successfully");
			} else {
				toast.success("Request updated successfully");
				if (pendingAction === "completed" && requestReview) {
					if (reviewDelayHours === 0) {
						toast.success("Review request sent immediately");
					} else {
						const delayText =
							reviewDelayUnit === "days"
								? `${reviewDelay} ${reviewDelay === 1 ? "day" : "days"}`
								: `${reviewDelay} ${reviewDelay === 1 ? "hour" : "hours"}`;
						toast.success(`Review request will be sent in ${delayText}`);
					}
				}
			}
			setIsModalOpen(false);
			window.location.reload();
		} catch (error) {
			console.error("Error updating request:", error);
			toast.error(
				error instanceof Error ? error.message : "Failed to update request"
			);
		} finally {
			setIsUpdating(false);
		}
	};

	const getActionTitle = () => {
		switch (pendingAction) {
			case "accepted":
				return "Accept Request";
			case "rejected":
				return "Reject Request";
			case "completed":
				return "Complete Request";
			case "send-review":
				return "Send Review Request";
			default:
				return "";
		}
	};

	const getActionDescription = () => {
		switch (pendingAction) {
			case "accepted":
				return "Add a note about when you'll contact the customer for the free troubleshooting call (5-10 minutes).";
			case "rejected":
				return "Please provide a reason for rejecting this request.";
			case "completed":
				return "Optionally add any final notes about the troubleshooting session.";
			case "send-review":
				return "This will send a review request email to the customer immediately.";
			default:
				return "";
		}
	};

	// Check if review request can be sent
	const canSendReviewRequest =
		request.status === "completed" && !request.review_sent_at;

	return (
		<div>
			<h1 className="text-2xl font-bold mb-6">
				Troubleshooting Request Details
			</h1>

			{/* Status Banner */}
			<div
				className={`mb-6 p-4 rounded-lg ${
					request.status === "pending"
						? "bg-yellow-50 border border-yellow-200"
						: request.status === "completed"
							? "bg-green-50 border border-green-200"
							: request.status === "rejected"
								? "bg-red-50 border border-red-200"
								: "bg-blue-50 border border-blue-200"
				}`}
			>
				<div className="flex items-center justify-between">
					<div>
						<p className="font-semibold capitalize">{request.status}</p>
						<p className="text-sm text-gray-600">
							Received{" "}
							{format(new Date(request.created_at), "MMM d, yyyy h:mm a")}
						</p>
					</div>
					{request.status === "pending" && (
						<div className="flex gap-3">
							<button
								onClick={() => handleActionClick("accepted")}
								disabled={isUpdating}
								className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors disabled:opacity-50"
							>
								Accept Request
							</button>
							<button
								onClick={() => handleActionClick("rejected")}
								disabled={isUpdating}
								className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
							>
								Reject Request
							</button>
						</div>
					)}
					{request.status === "accepted" && (
						<button
							onClick={() => handleActionClick("completed")}
							disabled={isUpdating}
							className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors disabled:opacity-50"
						>
							Mark as Completed
						</button>
					)}
					{canSendReviewRequest && (
						<button
							onClick={() => handleActionClick("send-review")}
							disabled={isUpdating}
							className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
						>
							Send Review Request
						</button>
					)}
				</div>
			</div>

			{/* Service Type */}
			<div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
				<h2 className="font-semibold text-blue-900 mb-2">Service Type</h2>
				<p className="text-blue-800 mb-3">
					Quick Troubleshooting Call (5-10 minutes)
				</p>
				{!request.listing.settings_virtual_diagnosis && (
					<div className="text-blue-800">
						<p className="mb-2">
							<strong>Note:</strong> This pro customer in your area would like
							to know if you're available to help them with a pre-service
							troubleshooting call. You haven't explicitly enabled this service,
							so the customer was notified you may or may not offer this
							service.
						</p>
					</div>
				)}
			</div>

			{/* Provider Response - show when request is accepted */}
			{request.status === "accepted" && request.provider_response && (
				<div className="mb-6 p-4 bg-emerald-50 border border-emerald-200 rounded-lg">
					<h2 className="font-semibold text-emerald-900 mb-2">
						Provider Response
					</h2>
					<p className="text-emerald-800 whitespace-pre-wrap">
						{request.provider_response}
					</p>
				</div>
			)}

			{/* Customer Information */}
			<Card>
				<CardHeader>
					<CardTitle>Customer Information</CardTitle>
					<CardDescription>
						Customer information for troubleshooting request
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<p className="text-sm text-gray-600">Name</p>
							<p className="font-medium">
								{request.first_name} {request.last_name}
							</p>
						</div>
						<div>
							<p className="text-sm text-gray-600">Phone</p>
							<p className="font-medium">{request.phone}</p>
						</div>
						<div>
							<p className="text-sm text-gray-600">Email</p>
							<p className="font-medium">{request.email}</p>
						</div>
					</div>

					{/* Issue Description */}
					<div className="mb-6">
						<h2 className="text-lg font-semibold mb-3">Issue Description</h2>
						<div className="bg-gray-50 p-4 rounded-lg">
							<p className="whitespace-pre-wrap">{request.issue_description}</p>
						</div>
					</div>

					{/* Provider Notes */}
					{request.provider_notes &&
						(request.provider_notes as any[]).length > 0 && (
							<div className="mb-6">
								<h2 className="text-lg font-semibold mb-3">Provider Notes</h2>
								{request.provider_notes &&
									(request.provider_notes as any[]).length > 0 && (
										<div className="space-y-3">
											{(request.provider_notes as any[]).map((note, index) => (
												<div key={index} className="bg-gray-50 rounded-lg p-4">
													<div className="flex items-center justify-between mb-2">
														<div className="flex items-center gap-2">
															{note.type === "accepted" && (
																<span className="w-2 h-2 rounded-full bg-emerald-500" />
															)}
															{note.type === "rejected" && (
																<span className="w-2 h-2 rounded-full bg-red-500" />
															)}
															{note.type === "completed" && (
																<span className="w-2 h-2 rounded-full bg-blue-500" />
															)}
															<span className="font-medium capitalize">
																{note.type}
															</span>
														</div>
														<span className="text-sm text-gray-600">
															{format(
																new Date(note.created_at),
																"MMM d, yyyy h:mm a"
															)}
														</span>
													</div>
													{note.content && (
														<p className="text-gray-700 whitespace-pre-wrap">
															{note.content}
														</p>
													)}
												</div>
											))}
										</div>
									)}
							</div>
						)}

					{/* Review Request Status */}
					{request.status === "completed" && (
						<div className="mb-6">
							<h2 className="text-lg font-semibold mb-3">
								Review Request Status
							</h2>
							<div
								className={`p-4 rounded-lg ${
									request.review_sent_at
										? "bg-green-50 border border-green-200"
										: request.review_requested
											? "bg-yellow-50 border border-yellow-200"
											: "bg-gray-50 border border-gray-200"
								}`}
							>
								{request.review_sent_at ? (
									<>
										<p className="font-medium text-green-800">
											Review request sent
										</p>
										<p className="text-sm text-green-600">
											Sent on{" "}
											{format(
												new Date(request.review_sent_at),
												"MMM d, yyyy h:mm a"
											)}
										</p>
									</>
								) : request.review_requested ? (
									<>
										<p className="font-medium text-yellow-800">
											Review request scheduled
										</p>
										<p className="text-sm text-yellow-600">
											Requested on{" "}
											{request.review_requested_at &&
												format(
													new Date(request.review_requested_at),
													"MMM d, yyyy h:mm a"
												)}
											{request.review_delay_hours &&
												request.review_delay_hours > 0 &&
												` • Will be sent in ${
													request.review_delay_hours >= 24
														? `${Math.floor(request.review_delay_hours / 24)} day${Math.floor(request.review_delay_hours / 24) === 1 ? "" : "s"}`
														: `${request.review_delay_hours} hour${request.review_delay_hours === 1 ? "" : "s"}`
												}`}
										</p>
									</>
								) : (
									<p className="text-gray-700">No review request sent yet</p>
								)}
							</div>
						</div>
					)}

					{!request.listing.settings_virtual_diagnosis &&
						request.listing.settings_virtual_diagnosis_notifications && (
							<div className="text-sm bg-gray-50 p-4 rounded-lg border border-gray-200">
								<p className="text-gray-600">
									If you would like to enable this service, you can opt in by
									enabling the "Pre-Service Troubleshooting" in your business
									settings. If you want to stop receiving these requests, you
									can opt out by disabling the "Pre-Service Troubleshooting
									Notifications" feature in your business settings.
								</p>
								<div className="flex items-center gap-2 mt-4">
									<a
										target="_blank"
										href="/provider/business/settings?setting=quick-troubleshooting"
										className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors disabled:opacity-50"
									>
										Enable Pre-Service Troubleshooting →
									</a>

									<a
										target="_blank"
										href="/provider/business/settings?setting=quick-troubleshooting-notifications"
										className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
									>
										Stop Receiving Requests →
									</a>
								</div>
							</div>
						)}
				</CardContent>
			</Card>
			{/* Action Modal */}
			<Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
				<DialogContent className="max-w-xl">
					<DialogHeader>
						<DialogTitle>{getActionTitle()}</DialogTitle>
					</DialogHeader>

					<div className="py-4">
						<p className="text-gray-600 mb-4">{getActionDescription()}</p>
						{pendingAction !== "send-review" && (
							<textarea
								className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
								rows={4}
								value={notes}
								onChange={(e) => setNotes(e.target.value)}
								placeholder="Add your note here..."
							/>
						)}

						{/* Review Request Section for Completed Actions */}
						{pendingAction === "completed" && (
							<div className="mt-6 p-4 bg-gray-50 rounded-lg">
								<div className="flex items-center gap-2">
									<Switch
										checked={requestReview}
										onCheckedChange={setRequestReview}
									/>
									<span className="font-medium">
										Request a review from the customer
									</span>
								</div>

								{requestReview && (
									<div className="mt-4 space-y-3">
										<p className="text-sm text-gray-600">
											When should we send the review request?
										</p>

										<div className="space-y-3">
											{/* Immediate option */}
											<label className="flex items-center gap-2 cursor-pointer">
												<input
													type="radio"
													name="reviewTiming"
													checked={reviewDelay === 0}
													onChange={() => {
														setReviewDelay(0);
														setReviewDelayUnit("hours");
													}}
													className="w-4 h-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
												/>
												<span className="text-sm">Send immediately</span>
											</label>

											{/* Delayed option */}
											<label className="flex items-center gap-2 cursor-pointer">
												<input
													type="radio"
													name="reviewTiming"
													checked={reviewDelay > 0}
													onChange={() => {
														if (reviewDelay === 0) {
															setReviewDelay(24);
														}
													}}
													className="w-4 h-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
												/>
												<span className="text-sm">Send after a delay</span>
											</label>

											{/* Delay configuration */}
											{reviewDelay > 0 && (
												<div className="ml-6 flex items-center gap-3">
													<input
														type="number"
														min="1"
														value={reviewDelay}
														onChange={(e) =>
															setReviewDelay(parseInt(e.target.value) || 1)
														}
														className="w-20 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-emerald-500"
													/>
													<select
														value={reviewDelayUnit}
														onChange={(e) =>
															setReviewDelayUnit(
																e.target.value as "hours" | "days"
															)
														}
														className="px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-emerald-500"
													>
														<option value="hours">hours</option>
														<option value="days">days</option>
													</select>
													<span className="text-sm text-gray-600">
														after marking as completed
													</span>
												</div>
											)}
										</div>
									</div>
								)}
							</div>
						)}
					</div>

					<DialogFooter>
						<button
							onClick={() => setIsModalOpen(false)}
							className="px-4 py-2 text-gray-600 hover:text-gray-800"
						>
							Cancel
						</button>
						<button
							onClick={handleStatusUpdate}
							disabled={
								isUpdating ||
								(pendingAction !== "completed" &&
									pendingAction !== "send-review" &&
									!notes)
							}
							className={`px-4 py-2 rounded-lg text-white ${
								pendingAction === "rejected"
									? "bg-red-600 hover:bg-red-700"
									: pendingAction === "send-review"
										? "bg-blue-600 hover:bg-blue-700"
										: "bg-emerald-600 hover:bg-emerald-700"
							} disabled:opacity-50`}
						>
							{isUpdating ? "Updating..." : "Confirm"}
						</button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
