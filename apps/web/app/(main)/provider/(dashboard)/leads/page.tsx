"use client";

import {
	<PERSON>,
	CardContent,
	CardDescription,
	Card<PERSON><PERSON>er,
	CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
	QuoteWithJob,
	TroubleshootingRequestWithUser,
	UnifiedLead
} from "@/types/global";
import { QuoteStatus } from "@rvhelp/database";

import { Button } from "@/components/ui/button";
import { filterLeadsByStatus, getQuoteStatusBadge } from "@/lib/utils/quote";
import { format } from "date-fns";
import { MessageSquare, RefreshCw, Shield } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

export default function ProviderLeadsPage() {
	const [leads, setLeads] = useState<UnifiedLead[]>([]);
	const [loading, setLoading] = useState(true);
	const [activeTab, setActiveTab] = useState<string>("needs_action");
	const [filteredLeads, setFilteredLeads] = useState<UnifiedLead[]>([]);
	const [isRefreshing, setIsRefreshing] = useState(false);

	const [stats, setStats] = useState({
		totalLeads: 0,
		responseRate: 0,
		avgResponseTime: 0,
		recentResponseRate: 0
	});

	const router = useRouter();

	// Utility function to check if a lead is warranty work
	const isWarrantyWork = (lead: UnifiedLead): boolean => {
		if (lead.type === "quote") {
			return Boolean(
				(lead.originalData as QuoteWithJob)?.job?.warranty_request_id
			);
		}
		return false;
	};

	// Utility function to redact email
	const redactEmail = (email: string): string => {
		const [username, domain] = email.split("@");
		const redactedUsername =
			username.length > 2
				? username.charAt(0) +
				"•".repeat(username.length - 2) +
				username.charAt(username.length - 1)
				: "•".repeat(username.length);
		const [domainName, extension] = domain.split(".");
		const redactedDomain =
			domainName.length > 2
				? domainName.charAt(0) +
				"•".repeat(domainName.length - 2) +
				domainName.charAt(domainName.length - 1)
				: "•".repeat(domainName.length);
		return `${redactedUsername}@${redactedDomain}.${extension}`;
	};

	// Utility function to redact phone
	const redactPhone = (phone: string): string => {
		return "(•••) •••-••••";
	};

	// Utility function to get city/state from location
	const getCityStateFromLocation = (location: any): string => {
		if (location?.city && location?.state) {
			return `${location.city}, ${location.state}`;
		}
		if (location?.address) {
			// Try to extract city/state from address string
			const parts = location.address.split(", ");
			if (parts.length >= 2) {
				// Take the last two parts which are typically city, state
				return parts.slice(-2).join(", ");
			}
		}
		return "Location available after terms acceptance";
	};

	// Utility function to format response time in hours and minutes
	const formatResponseTime = (minutes: number): string => {
		if (minutes < 60) {
			return `${minutes}m`;
		}
		const hours = Math.floor(minutes / 60);
		const remainingMinutes = minutes % 60;
		if (remainingMinutes === 0) {
			return `${hours}h`;
		}
		return `${hours}h ${remainingMinutes}m`;
	};

	// Convert quotes to unified lead format
	const convertQuoteToLead = (quote: QuoteWithJob): UnifiedLead => ({
		id: quote.id,
		type: "quote",
		created_at:
			typeof quote.created_at === "string"
				? quote.created_at
				: quote.created_at.toString(),
		status: quote.status,
		customer: {
			first_name: quote.job.user.first_name || "",
			last_name: quote.job.user.last_name || "",
			email: quote.job.user.email || "",
			phone: quote.job.user.phone || null
		},
		location: quote.job.location,
		message: quote.job.warranty_request?.complaint || quote.job.message || "",
		category: quote.job.category,
		originalData: quote
	});

	// Convert troubleshooting requests to unified lead format
	const convertTroubleshootingToLead = (
		request: TroubleshootingRequestWithUser
	): UnifiedLead => ({
		id: request.id,
		type: "troubleshooting",
		created_at:
			typeof request.created_at === "string"
				? request.created_at
				: request.created_at.toString(),
		status: request.status,
		customer: {
			first_name: request.first_name,
			last_name: request.last_name,
			email: request.email,
			phone: request.phone
		},
		location: request.location,
		message: request.issue_description,
		category: undefined, // Troubleshooting requests don't have categories
		originalData: request
	});

	const refreshStats = async () => {
		setIsRefreshing(true);
		try {
			// Refresh both stats and leads data
			const [statsResponse, quotesResponse, troubleshootingResponse] =
				await Promise.all([
					fetch("/api/provider/stats/refresh", { method: "POST" }),
					fetch(`/api/provider/quotes`),
					fetch(`/api/provider/troubleshooting`)
				]);

			if (!statsResponse.ok)
				throw new Error("Failed to refresh provider stats");
			const statsData = await statsResponse.json();
			setStats(statsData);

			// Refresh leads data
			const quotesData = quotesResponse.ok ? await quotesResponse.json() : [];
			const troubleshootingData = troubleshootingResponse.ok
				? await troubleshootingResponse.json()
				: [];

			// Convert to unified format
			const unifiedQuotes = quotesData.map(convertQuoteToLead);
			const unifiedTroubleshooting = troubleshootingData.map(
				convertTroubleshootingToLead
			);

			// Combine and sort by created_at
			const allLeads = [...unifiedQuotes, ...unifiedTroubleshooting].sort(
				(a, b) =>
					new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
			);

			setLeads(allLeads);
			setFilteredLeads(filterLeadsByStatus(allLeads, activeTab));

			toast.success("Data refreshed successfully");
		} catch (error) {
			console.error("Error refreshing data:", error);
			toast.error("Failed to refresh data");
		} finally {
			setIsRefreshing(false);
		}
	};

	useEffect(() => {
		const fetchData = async () => {
			try {
				// Fetch both quotes and troubleshooting requests
				const [quotesResponse, troubleshootingResponse] = await Promise.all([
					fetch(`/api/provider/quotes`),
					fetch(`/api/provider/troubleshooting`)
				]);

				const quotesData = quotesResponse.ok ? await quotesResponse.json() : [];
				const troubleshootingData = troubleshootingResponse.ok
					? await troubleshootingResponse.json()
					: [];

				// Convert to unified format
				const unifiedQuotes = quotesData.map(convertQuoteToLead);
				const unifiedTroubleshooting = troubleshootingData.map(
					convertTroubleshootingToLead
				);

				// Combine and sort by created_at
				const allLeads = [...unifiedQuotes, ...unifiedTroubleshooting].sort(
					(a, b) =>
						new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
				);

				setLeads(allLeads);
				setFilteredLeads(filterLeadsByStatus(allLeads, activeTab));
			} catch (error) {
				console.error("Error fetching leads:", error);
				toast.error("Failed to load leads");
			} finally {
				setLoading(false);
			}
		};

		const fetchStats = async () => {
			try {
				const response = await fetch("/api/provider/stats");
				if (!response.ok) throw new Error("Failed to fetch provider stats");
				const statsData = await response.json();
				setStats(statsData);
			} catch (error) {
				console.error("Error fetching provider stats:", error);
				setStats({
					totalLeads: leads.length,
					responseRate: 0,
					avgResponseTime: 0,
					recentResponseRate: 0
				});
			}
		};

		fetchData();
		fetchStats();
	}, [activeTab]);

	// Update filtered leads when activeTab changes
	useEffect(() => {
		setFilteredLeads(filterLeadsByStatus(leads, activeTab));
	}, [leads, activeTab]);

	// Calculate counts for each status
	const getStatusCount = (status: string) => {
		return filterLeadsByStatus(leads, status).length;
	};

	const handleCardClick = (lead: UnifiedLead) => {
		if (lead.type === "troubleshooting") {
			// Navigate to troubleshooting details page
			router.push(`/provider/leads/troubleshooting/${lead.id}`);
		} else {
			// Navigate to lead details page
			router.push(`/provider/leads/${lead.id}`);
		}
	};

	const getStatusBadge = (lead: UnifiedLead) => {
		if (lead.type === "quote") {
			return getQuoteStatusBadge(lead.status as QuoteStatus);
		} else {
			// Handle troubleshooting status badges
			const statusColors = {
				pending: "bg-orange-100 text-orange-800",
				accepted: "bg-green-100 text-green-800",
				rejected: "bg-red-100 text-red-800",
				completed: "bg-blue-100 text-blue-800"
			};
			const colorClass =
				statusColors[lead.status as keyof typeof statusColors] ||
				"bg-gray-100 text-gray-800";
			return (
				<span
					className={`px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}
				>
					{lead.status.charAt(0).toUpperCase() + lead.status.slice(1)}
				</span>
			);
		}
	};

	const getLeadTypeBadge = (lead: UnifiedLead) => {
		if (lead.type === "troubleshooting") {
			return (
				<div className="absolute bottom-2 right-0">
					<span className="text-sm font-medium bg-orange-400 text-white px-2 py-1 rounded-l-md">
						Troubleshooting Request
					</span>
				</div>
			);
		} else {
			if (isWarrantyWork(lead)) {
				return (
					<div className="absolute bottom-2 right-0">
						<span className="text-sm font-medium bg-purple-400 text-white px-2 py-1 rounded-l-md">
							Warranty Request
						</span>
					</div>
				);
			}
			return (
				<div className="absolute bottom-2 right-0">
					<span className="text-sm font-medium bg-blue-400 text-white px-2 py-1 rounded-l-md">
						Service Request
					</span>
				</div>
			);
		}
	};

	if (loading) {
		return (
			<div className="container mx-auto">
				<h1 className="text-2xl font-bold mb-6">Leads</h1>

				{/* Stats Overview Skeleton */}
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
					{[1, 2, 3, 4].map((i) => (
						<Card key={i} className="animate-pulse">
							<CardContent className="p-6">
								<div className="flex items-center space-x-2 mb-2">
									<div className="h-2 w-2 bg-gray-200 rounded-full" />
									<div className="h-4 w-20 bg-gray-200 rounded" />
								</div>
								<div className="h-8 w-12 bg-gray-200 rounded" />
							</CardContent>
						</Card>
					))}
				</div>

				<div className="space-y-4">
					{/* Tabs Skeleton */}
					<div className="h-auto flex flex-wrap gap-2">
						{[1, 2, 3, 4, 5].map((i) => (
							<div
								key={i}
								className="h-10 w-32 bg-gray-200 rounded animate-pulse"
							/>
						))}
					</div>

					{/* Cards Skeleton */}
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
						{[1, 2, 3, 4, 5, 6].map((i) => (
							<Card
								key={i}
								className="animate-pulse hover:shadow-md cursor-pointer transition-shadow"
							>
								<CardHeader className="pb-2">
									<div className="flex justify-between items-start">
										<div className="h-5 w-32 bg-gray-200 rounded" />
										<div className="h-5 w-16 bg-gray-200 rounded" />
									</div>
								</CardHeader>
								<CardContent className="space-y-2">
									{/* Type */}
									<div className="flex items-center gap-2">
										<div className="h-4 w-8 bg-gray-200 rounded" />
										<div className="h-4 w-32 bg-gray-200 rounded" />
									</div>
									{/* Contact */}
									<div className="flex items-center gap-2">
										<div className="h-4 w-12 bg-gray-200 rounded" />
										<div className="h-4 w-48 bg-gray-200 rounded" />
									</div>
									{/* Location */}
									<div className="flex items-center gap-2">
										<div className="h-4 w-14 bg-gray-200 rounded" />
										<div className="h-4 w-40 bg-gray-200 rounded" />
									</div>
									{/* Message */}
									<div className="space-y-1">
										<div className="flex items-center gap-2">
											<div className="h-4 w-12 bg-gray-200 rounded" />
											<div className="h-4 w-16 bg-gray-200 rounded" />
										</div>
										<div className="h-8 w-full bg-gray-200 rounded" />
									</div>
									{/* Date */}
									<div className="h-3 w-24 bg-gray-200 rounded" />
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</div>
		);
	}

	const getEmptyStateMessage = () => {
		switch (activeTab) {
			case "needs_action":
				return "No leads need your attention right now. New pending requests and troubleshooting requests will appear here.";
			case "in_progress":
				return "No leads in progress found. Leads you've accepted will appear here.";
			case "completed":
				return "No completed leads found. Leads that have been finished will appear here.";
			case "rejected":
				return "No rejected leads found. Leads you've declined will appear here.";
			case "proposal_declined":
				return "No declined proposals found. Proposals that customers have declined will appear here.";
			case "all":
				return "No leads found. All your service requests and troubleshooting requests will appear here.";
			default:
				return "No leads found.";
		}
	};

	return (
		<div className="container mx-auto">
			<div className="flex justify-between items-center mb-6">
				<h1 className="text-2xl font-bold">Leads</h1>
				<Button
					variant="outline"
					size="sm"
					onClick={refreshStats}
					disabled={isRefreshing}
					className="flex items-center gap-2"
				>
					<RefreshCw
						className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
					/>
					{isRefreshing ? "Refreshing..." : "Refresh Data"}
				</Button>
			</div>

			{/* Stats Overview */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
				<Card>
					<CardContent className="p-6">
						<div className="flex items-center space-x-2">
							<div className="h-2 w-2 bg-blue-500 rounded-full"></div>
							<span className="text-sm font-medium">Total Leads</span>
						</div>
						<p className="text-2xl font-bold mt-2">{stats.totalLeads}</p>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="p-6">
						<div className="flex items-center space-x-2">
							<div className="h-2 w-2 bg-green-500 rounded-full"></div>
							<span className="text-sm font-medium">Response Rate</span>
						</div>
						<p className="text-2xl font-bold mt-2">{stats.responseRate}%</p>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="p-6">
						<div className="flex items-center space-x-2">
							<div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
							<span className="text-sm font-medium">Avg Response</span>
						</div>
						<p className="text-2xl font-bold mt-2">
							{formatResponseTime(stats.avgResponseTime)}
						</p>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="p-6">
						<div className="flex items-center space-x-2">
							<div className="h-2 w-2 bg-primary rounded-full"></div>
							<span className="text-sm font-medium">Completion Rate</span>
						</div>
						<p className="text-2xl font-bold mt-2">
							{stats.recentResponseRate}%
						</p>
					</CardContent>
				</Card>
			</div>

			<Tabs
				value={activeTab}
				onValueChange={(value: string) => setActiveTab(value)}
				className="space-y-4"
			>
				<TabsList className="h-auto flex flex-wrap justify-start">
					<TabsTrigger value="needs_action" className="relative mb-2 md:mb-0">
						Needs Action
						<span className="ml-2 rounded-full bg-orange-500/20 text-orange-600 px-2 py-0.5 text-xs font-medium">
							{getStatusCount("needs_action")}
						</span>
					</TabsTrigger>

					<TabsTrigger value="completed">
						Completed ({getStatusCount("completed")})
					</TabsTrigger>
					<TabsTrigger value="rejected">
						Rejected ({getStatusCount("rejected")})
					</TabsTrigger>
					<TabsTrigger value="proposal_declined">
						Declined ({getStatusCount("proposal_declined")})
					</TabsTrigger>
					<TabsTrigger value="all" className="relative mb-2 md:mb-0">
						All
						<span className="ml-2 rounded-full bg-primary/20 text-primary px-2 py-0.5 text-xs font-medium">
							{leads.length}
						</span>
					</TabsTrigger>
				</TabsList>

				<TabsContent value={activeTab} className="space-y-4">
					{filteredLeads.length === 0 ? (
						<Card>
							<CardContent className="py-12">
								<div className="text-center space-y-3">
									<MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
									<CardTitle className="text-xl">No leads found</CardTitle>
									<CardDescription className="max-w-sm mx-auto">
										{getEmptyStateMessage()}
									</CardDescription>
								</div>
							</CardContent>
						</Card>
					) : (
						<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
							{filteredLeads.map((lead) => (
								<Card
									key={`${lead.type}-${lead.id}`}
									className="hover:shadow-md relative cursor-pointer transition-shadow"
									onClick={() => handleCardClick(lead)}
								>
									<CardHeader className="pb-2">
										<div className="flex justify-between items-start">
											<CardTitle className="text-lg">
												{lead.customer.first_name || "N/A"}
												{lead.customer.last_name &&
													` ${lead.customer.last_name}`}
											</CardTitle>
											<div className="flex items-center gap-2">
												{isWarrantyWork(lead) && (
													<div className="flex items-center gap-1 bg-orange-100 text-orange-800 px-2 py-1 rounded-md text-xs">
														<Shield className="h-3 w-3" />
														<span>Protected</span>
													</div>
												)}
												{getStatusBadge(lead)}
											</div>
										</div>
									</CardHeader>
									<CardContent className="space-y-2 pb-8">
										<div className="text-sm">
											<span className="font-medium">Contact:</span>{" "}
											{isWarrantyWork(lead)
												? redactEmail(lead.customer.email || "N/A")
												: lead.customer.email || "N/A"}
											{lead.customer.phone &&
												` / ${isWarrantyWork(lead) ? redactPhone(lead.customer.phone || "") : lead.customer.phone}`}
										</div>
										{lead.location && (
											<div className="text-sm">
												<span className="font-medium">Location:</span>{" "}
												{isWarrantyWork(lead)
													? getCityStateFromLocation(lead.location)
													: lead.location.address}
											</div>
										)}
										<div className="text-sm">
											<span className="font-medium">Message:</span>{" "}
											<span className="line-clamp-2">{lead.message}</span>
										</div>
										<div className="text-xs text-muted-foreground">
											{format(new Date(lead.created_at), "PPp")}
										</div>
										{getLeadTypeBadge(lead)}
									</CardContent>
								</Card>
							))}
						</div>
					)}
				</TabsContent>
			</Tabs>
		</div>
	);
}
