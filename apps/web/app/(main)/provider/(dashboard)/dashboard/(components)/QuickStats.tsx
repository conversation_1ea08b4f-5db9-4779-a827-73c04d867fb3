import QRCodeDialog from "@/components/directory/listing-details/QRCodeDialog";
import RV<PERSON>elpQRCodeDialog from "@/components/directory/listing-details/RVHelpQRCodeDialog";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Heart, QrCode, Star, TrendingDown, TrendingUp } from "lucide-react";

interface ListingStats {
    qr_scans: number;
    qr_scans_trend: number;
    favorites_count: number;
    favorites_trend: number;
    reviews_count: number;
    reviews_last_month: number;
    average_rating: number;
}
interface StatItem {
    label: string;
    value: string | number;
    trend?: string | number;
    icon: React.ReactNode;
}

const QuickStats = ({
    stats,
    listingId,
    listingName,
    isLoading
}: {
    stats: ListingStats;
    listingId: string;
    listingName: string;
    isLoading?: boolean;
}) => {

    const statsData: StatItem[] = stats
        ? [
            {
                label: "Profile QR Code Scans",
                value: stats.qr_scans || 0,
                trend: stats.qr_scans_trend || 0,
                icon: <QrCode className="w-5 h-5 text-[#447D6A]" />
            },
            {
                label: "Favorites",
                value: stats.favorites_count || 0,
                trend: stats.favorites_trend || 0,
                icon: <Heart className="w-5 h-5 text-[#447D6A]" />
            },
            {
                label: "Reviews",
                value: stats.reviews_count || 0,
                trend:
                    stats.reviews_last_month > 0
                        ? (
                            (stats.reviews_last_month / (stats.reviews_count || 1)) *
                            100
                        ).toFixed(0)
                        : undefined,
                icon: <Star className="w-5 h-5 text-[#447D6A]" />
            },
            {
                label: "Rating",
                value: (stats.average_rating || 0).toFixed(1),
                trend: undefined,
                icon: <Star className="w-5 h-5 text-[#447D6A]" />
            }
        ]
        : [];
    // Helper function to get a visual representation of the trend
    const getTrendIcon = (trend) => {
        if (!trend || trend === undefined) return null;
        return Number(trend) >= 0 ? (
            <TrendingUp className="w-4 h-4" />
        ) : (
            <TrendingDown className="w-4 h-4" />
        );
    };

    // Helper for trend color
    const getTrendColor = (trend) => {
        if (!trend || trend === undefined) return "text-gray-600";
        return Number(trend) >= 0 ? "text-green-600" : "text-red-600";
    };

    // Helper for progress percentage (normalize values)
    const getProgressValue = (item) => {
        // For ratings, scale based on 5 stars
        if (item.label === "Rating") {
            return (Number(item.value) / 5) * 100;
        }
        // For views, normalize to a reasonable scale (assuming 1000 is a good benchmark)
        if (item.label === "Profile Views") {
            return Math.min((Number(item.value) / 1000) * 100, 100);
        }
        // For other metrics, use a logarithmic scale for better visualization
        return Math.min(Math.log(Number(item.value) + 1) * 20, 100);
    };

    const StatSkeleton = () => (
        <div className="bg-white rounded-xl p-5 border border-gray-100 shadow-sm flex flex-col">
            <div className="flex items-start justify-between mb-3">
                <div className="h-5 w-24 bg-gray-200 rounded animate-pulse" />
                <div className="bg-gray-200 p-2 rounded-lg animate-pulse" />
            </div>
            <div className="flex items-baseline mt-1 mb-3">
                <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
            </div>
            <div className="h-1.5 bg-gray-100 rounded-full animate-pulse" />
        </div>
    );

    return (
        <Card className="shadow-sm border-gray-200 rounded-2xl overflow-hidden mb-8">
            <CardHeader
                padding="sm"
                className="p-4 border-b border-gray-100 flex flex-col sm:flex-row justify-between items-start sm:items-center bg-white"
            >
                <div>
                    <CardTitle className="text-lg font-medium text-gray-900">
                        Listing Performance
                    </CardTitle>
                    <p className="text-sm text-gray-500 mt-1">
                        Total metrics with 30-day trends
                    </p>
                </div>
                <div className="flex items-center gap-2 mt-3 sm:mt-0">
                    <QRCodeDialog listingId={listingId} listingName={listingName} />
                    <RVHelpQRCodeDialog />
                </div>
            </CardHeader>

            <CardContent className="p-4 bg-gradient-to-br from-white to-gray-50">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    {isLoading ? (
                        <>
                            <StatSkeleton />
                            <StatSkeleton />
                            <StatSkeleton />
                            <StatSkeleton />
                        </>
                    ) : (
                        statsData.map((stat, index) => (
                            <div
                                key={index}
                                className="bg-white rounded-xl p-5 border border-gray-100 shadow-sm hover:shadow transition-all duration-200 flex flex-col"
                            >
                                <div className="flex items-start justify-between mb-3">
                                    <h3 className="text-gray-500 font-medium">{stat.label}</h3>
                                    <div className="bg-[#447D6A]/10 p-2 rounded-lg">
                                        {stat.icon}
                                    </div>
                                </div>

                                <div className="flex items-baseline mt-1 mb-3">
                                    <span className="text-2xl font-bold text-gray-900">
                                        {stat.value}
                                    </span>
                                    {stat.trend !== undefined && (
                                        <div
                                            className={`ml-2 flex items-center ${getTrendColor(
                                                stat.trend
                                            )}`}
                                        >
                                            {getTrendIcon(stat.trend)}
                                            <span className="text-sm font-medium ml-1">
                                                {Number(stat.trend) > 0 ? "+" : ""}
                                                {stat.trend}%
                                            </span>
                                        </div>
                                    )}
                                </div>

                                <Progress
                                    value={getProgressValue(stat)}
                                    className="h-1.5 bg-gray-100 mb-2 [&>div]:bg-gradient-to-r [&>div]:from-[#447D6A] [&>div]:to-[#447D6A]/80"
                                />

                                {stat.trend !== undefined && (
                                    <p className="text-xs text-gray-500 mt-1">
                                        {Number(stat.trend) >= 0 ? "Increased" : "Decreased"} from
                                        last 30 days
                                    </p>
                                )}
                            </div>
                        ))
                    )}
                </div>
            </CardContent>

            <CardFooter className="p-4 border-t border-gray-100 bg-white flex justify-end">
                {/* <Button
					variant="ghost"
					size="sm"
					className="text-[#447D6A] hover:text-[#447D6A]/80 hover:bg-[#447D6A]/5"
				>
					<span>View All Metrics</span>
					<ArrowRight className="w-4 h-4 ml-1" />
				</Button> */}
            </CardFooter>
        </Card>
    );
};

export default QuickStats;