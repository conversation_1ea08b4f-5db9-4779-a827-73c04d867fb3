"use client";

import KeystonePartnershipModal from "@/components/partner-portal/KeystonePartnershipModal";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const PartnerPortal = () => {
	const [activeCategory, setActiveCategory] = useState("all");
	const router = useRouter();
	const [notifications, setNotifications] = useState({
		thor: false
	});
	const [showKeystoneModal, setShowKeystoneModal] = useState(false);
	const [certifications, setCertifications] = useState<any[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchCertifications();
	}, []);

	const fetchCertifications = async () => {
		try {
			const response = await fetch("/api/provider/certifications");
			if (response.ok) {
				const data = await response.json();
				setCertifications(data);
			}
		} catch (error) {
			console.error("Error fetching certifications:", error);
		} finally {
			setLoading(false);
		}
	};

	const categories = [
		{ id: "all", label: "All Partners", icon: "🛠️" },
		{ id: "manufacturers", label: "RV Manufacturers", icon: "🏭" },
		{ id: "suppliers", label: "Suppliers", icon: "🔧" }
	];

	const partners = [
		{
			id: "keystone",
			name: "Keystone RV",
			type: "Premium Manufacturer Partner",
			category: "manufacturers",
			logo: "K",
			logoColor: "bg-orange-500",
			benefits: [
				{
					icon: "🚚",
					title: "FREE parts with expedited shipping",
					description: "No cost parts directly from Keystone"
				},
				{
					icon: "📞",
					title: "Direct technical support hotline",
					description: "Skip the queue, get expert help fast"
				},
				{
					icon: "💰",
					title: "Expedited warranty payouts",
					description: "Get paid faster for warranty work"
				},
				{
					icon: "💼",
					title: "More work opportunities",
					description: "Priority access to Keystone RV jobs"
				},
				{
					icon: "🎓",
					title: "Exclusive training sessions",
					description: "Stay ahead with latest tech knowledge"
				}
			]
		}
	];

	const getPartnerStatus = (partnerId: string) => {
		if (partnerId === "thor") return "coming";

		const cert = certifications.find(
			(c) => c.certification.name === `${partnerId}-warranty`
		);

		// Check if certification exists and is active
		if (!cert || !cert.certification.is_active) return "coming";
		if (!cert.status) return "available";

		if (cert.status.status === "COMPLETED") return "enrolled";
		if (cert.status.opted_out) return "opted-out";
		return "in-progress";
	};

	const getStatusConfig = (status: string) => {
		switch (status) {
			case "available":
				return { label: "Available", className: "bg-green-100 text-green-800" };
			case "enrolled":
				return { label: "Enrolled", className: "bg-blue-100 text-blue-800" };
			case "in-progress":
				return {
					label: "In Progress",
					className: "bg-yellow-100 text-yellow-800"
				};
			case "opted-out":
				return { label: "Opted Out", className: "bg-red-100 text-red-800" };
			case "coming":
				return { label: "Coming Soon", className: "bg-red-100 text-red-800" };
			default:
				return { label: "Unknown", className: "bg-gray-100 text-gray-800" };
		}
	};

	const filteredPartners =
		activeCategory === "all"
			? partners
			: partners.filter((partner) => partner.category === activeCategory);

	const handleKeystoneJoin = async () => {
		try {
			// Start the certification process first
			const response = await fetch("/api/provider/certifications", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					certificationName: "keystone-warranty"
				})
			});

			if (!response.ok) {
				const error = await response.json();
				console.error("Failed to start certification:", error);
				alert("Failed to start certification. Please try again.");
				return;
			}

			// Refresh certifications and navigate
			await fetchCertifications();
			router.push("/provider/certifications/keystone-warranty");
		} catch (error) {
			console.error("Error starting certification:", error);
			alert("Failed to start certification. Please try again.");
		}
	};

	const handleLearnMore = (partnerId: string) => {
		if (partnerId === "keystone") {
			setShowKeystoneModal(true);
		} else {
			alert("Learn more about " + partnerId);
		}
	};

	const handleNotificationChange = (partnerId: string) => {
		setNotifications((prev) => ({
			...prev,
			[partnerId]: !prev[partnerId as keyof typeof prev]
		}));
	};

	if (loading) {
		return (
			<div className="bg-gray-50 min-h-screen">
				<div className="container">
					<div className="flex items-center justify-center h-64">
						<div className="text-center">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
							<p className="text-gray-600">Loading partner programs...</p>
						</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="bg-gray-50 min-h-screen">
			<div className="container">
				{/* Page Header */}
				<div className="mb-8">
					<h1 className="text-3xl font-semibold text-gray-900 mb-2">
						Partner Portal
					</h1>
					<p className="text-gray-600">
						Connect with manufacturer partners for exclusive benefits and
						support
					</p>
				</div>

				{/* Partner Categories */}
				<div className="mb-8">
					<h2 className="text-lg font-semibold mb-4">Partner Categories</h2>
					<div className="flex flex-wrap gap-2 mb-8">
						{categories.map((category) => (
							<button
								key={category.id}
								onClick={() => setActiveCategory(category.id)}
								className={`px-5 py-2 border rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
									activeCategory === category.id
										? "bg-green-600 text-white border-green-600"
										: "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
								}`}
							>
								<span>{category.icon}</span>
								{category.label}
							</button>
						))}
					</div>
				</div>

				{/* Partners Grid */}
				<div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
					{filteredPartners.map((partner) => {
						const statusConfig = getStatusConfig(getPartnerStatus(partner.id));

						return (
							<div
								key={partner.id}
								className="bg-white border border-gray-200 rounded-xl p-6 relative hover:shadow-lg transition-shadow duration-200"
							>
								{/* Status Label */}
								<span
									className={`absolute top-4 right-4 text-xs font-medium px-3 py-1 rounded ${statusConfig.className}`}
								>
									{statusConfig.label}
								</span>

								{/* Partner Header */}
								<div className="flex items-center mb-5">
									<div
										className={`w-12 h-12 rounded-lg ${partner.logoColor} flex items-center justify-center text-white text-xl font-semibold mr-4`}
									>
										{partner.logo}
									</div>
									<div>
										<h3 className="text-lg font-semibold text-gray-900">
											{partner.name}
										</h3>
										<p className="text-sm text-gray-600">{partner.type}</p>
									</div>
								</div>

								{/* Benefits List */}
								<div className="mb-6">
									{partner.benefits.map((benefit, index) => (
										<div
											key={index}
											className="flex items-start mb-3 text-sm text-gray-700 leading-relaxed"
										>
											<span className="mr-3 text-green-600 flex-shrink-0 mt-0.5">
												{benefit.icon}
											</span>
											<span>
												<strong>{benefit.title}</strong>
												{benefit.description && (
													<span> - {benefit.description}</span>
												)}
											</span>
										</div>
									))}
								</div>

								{/* Action Buttons */}
								<div className="flex gap-3">
									{getPartnerStatus(partner.id) === "available" && (
										<>
											<button
												onClick={handleKeystoneJoin}
												className="flex-1 bg-green-600 text-white px-5 py-2.5 rounded-md text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center gap-2"
											>
												<span>⚡</span> Join Program
											</button>
											<button
												onClick={() => handleLearnMore(partner.id)}
												className="bg-white text-gray-700 border border-gray-300 px-5 py-2.5 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200"
											>
												Learn More
											</button>
										</>
									)}

									{getPartnerStatus(partner.id) === "enrolled" && (
										<>
											<button className="flex-1 bg-green-600 text-white px-5 py-2.5 rounded-md text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center gap-2">
												<span>🔓</span> Access Portal
											</button>
											<button className="bg-white text-gray-700 border border-gray-300 px-5 py-2.5 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
												Manage
											</button>
										</>
									)}

									{getPartnerStatus(partner.id) === "in-progress" && (
										<>
											<button
												onClick={() =>
													router.push(
														`/provider/certifications/${partner.id}-warranty`
													)
												}
												className="flex-1 bg-yellow-600 text-white px-5 py-2.5 rounded-md text-sm font-medium hover:bg-yellow-700 transition-colors duration-200 flex items-center justify-center gap-2"
											>
												<span>📚</span> Continue Training
											</button>
											<button className="bg-white text-gray-700 border border-gray-300 px-5 py-2.5 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
												View Progress
											</button>
										</>
									)}

									{getPartnerStatus(partner.id) === "opted-out" && (
										<>
											<button
												onClick={() =>
													router.push(
														`/provider/certifications/${partner.id}-warranty`
													)
												}
												className="flex-1 bg-green-600 text-white px-5 py-2.5 rounded-md text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center gap-2"
											>
												<span>🔄</span> Re-enroll
											</button>
											<button className="bg-white text-gray-700 border border-gray-300 px-5 py-2.5 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
												Learn More
											</button>
										</>
									)}

									{getPartnerStatus(partner.id) === "coming" && (
										<button
											disabled
											className="flex-1 bg-gray-200 text-gray-500 px-5 py-2.5 rounded-md text-sm font-medium cursor-not-allowed opacity-60"
										>
											Coming Soon
										</button>
									)}
								</div>
							</div>
						);
					})}
				</div>

				{/* Footer Message */}
				<div className="text-center mt-12 p-8 bg-gray-100 rounded-lg">
					<h3 className="text-lg font-semibold text-gray-900 mb-2">
						More Partners Coming Soon
					</h3>
					<p className="text-sm text-gray-600">
						We're actively working to bring you more manufacturer partnerships
						and exclusive benefits
					</p>
				</div>
			</div>
			{/* Keystone Partnership Modal */}
			<KeystonePartnershipModal
				isOpen={showKeystoneModal}
				onClose={() => setShowKeystoneModal(false)}
			/>
		</div>
	);
};

export default PartnerPortal;
