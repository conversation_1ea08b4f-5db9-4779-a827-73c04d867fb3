"use client";

import { CenteredPageLoader } from "@/components/Loader";
import { CertificationTraining } from "@/components/certifications/CertificationTraining";
import { Breadcrumbs } from "@/components/ui/breadcrumbs";
import { keystoneWarrantyTraining } from "@/lib/certifications/keystone-warranty/training-content";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface CertificationPageProps {
	params: { name: string };
}

export default function CertificationPage({ params }: CertificationPageProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [loading, setLoading] = useState(true);
	const [certification, setCertification] = useState<any>(null);

	// Get redirect URL from query parameters
	const redirectUrl = searchParams.get("redirect");

	useEffect(() => {
		fetchCertification();
	}, [params.name]);

	const fetchCertification = async () => {
		try {
			const response = await fetch(`/api/provider/certifications`);
			if (!response.ok) {
				throw new Error("Failed to fetch certification");
			}
			const data = await response.json();

			const cert = data.find(
				(item: any) => item.certification.name === params.name
			);
			if (!cert) {
				toast.error("Certification not found");
				const targetUrl = redirectUrl
					? decodeURIComponent(redirectUrl)
					: "/provider/dashboard";
				router.push(targetUrl);
				return;
			}

			// Check if the user has a record for this certification
			if (!cert.status) {
				// No record exists, create one automatically
				try {
					const startResponse = await fetch("/api/provider/certifications", {
						method: "POST",
						headers: {
							"Content-Type": "application/json"
						},
						body: JSON.stringify({
							certificationName: params.name
						})
					});

					if (!startResponse.ok) {
						throw new Error("Failed to start certification");
					}

					// Refresh the certification data
					const refreshResponse = await fetch(`/api/provider/certifications`);
					if (refreshResponse.ok) {
						const refreshData = await refreshResponse.json();
						const updatedCert = refreshData.find(
							(item: any) => item.certification.name === params.name
						);
						if (updatedCert) {
							setCertification(updatedCert);
							return;
						}
					}
				} catch (startError) {
					console.error("Error starting certification:", startError);
					toast.error("Failed to start certification. Please try again.");
					const targetUrl = redirectUrl
						? decodeURIComponent(redirectUrl)
						: "/provider/partner-portal";
					router.push(targetUrl);
					return;
				}
			}

			setCertification(cert);
		} catch (error) {
			console.error("Error fetching certification:", error);
			toast.error("Failed to load certification");
			const targetUrl = redirectUrl
				? decodeURIComponent(redirectUrl)
				: "/provider/dashboard";
			router.push(targetUrl);
		} finally {
			setLoading(false);
		}
	};

	const handleComplete = () => {
		// Use redirect URL if provided, otherwise go to partner portal
		const targetUrl = redirectUrl
			? decodeURIComponent(redirectUrl)
			: "/provider/partner-portal";
		router.push(targetUrl);
	};

	const handleOptOut = () => {
		toast.success("You have opted out of this certification");
		// Use redirect URL if provided, otherwise go to partner portal
		const targetUrl = redirectUrl
			? decodeURIComponent(redirectUrl)
			: "/provider/partner-portal";
		router.push(targetUrl);
	};

	if (loading) {
		return <CenteredPageLoader />;
	}

	if (!certification) {
		return (
			<div className="container mx-auto py-6">
				<div className="text-center">
					<h1 className="text-2xl font-bold text-gray-900 mb-4">
						Certification Not Found
					</h1>
					<p className="text-gray-600 mb-6">
						The requested certification could not be found.
					</p>
					<button
						onClick={() => {
							const targetUrl = redirectUrl
								? decodeURIComponent(redirectUrl)
								: "/provider/dashboard";
							router.push(targetUrl);
						}}
						className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90"
					>
						Return to Dashboard
					</button>
				</div>
			</div>
		);
	}

	// Get training content based on certification name
	const getTrainingContent = (certName: string) => {
		switch (certName) {
			case "keystone-warranty":
				return keystoneWarrantyTraining;
			default:
				return null;
		}
	};

	const trainingContent = getTrainingContent(params.name);
	if (!trainingContent) {
		return (
			<div className="container mx-auto py-6">
				<div className="text-center">
					<h1 className="text-2xl font-bold text-gray-900 mb-4">
						Training Content Not Available
					</h1>
					<p className="text-gray-600 mb-6">
						Training content for this certification is not yet available.
					</p>
					<button
						onClick={() => {
							const targetUrl = redirectUrl
								? decodeURIComponent(redirectUrl)
								: "/provider/dashboard";
							router.push(targetUrl);
						}}
						className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90"
					>
						Return to Dashboard
					</button>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-6">
			<Breadcrumbs
				className="mb-6"
				items={[
					{ label: "Dashboard", href: "/provider/dashboard" },
					{ label: "Certifications", href: "/provider/dashboard" },
					{
						label: certification.certification.display_name,
						href: `/provider/certifications/${params.name}`
					}
				]}
			/>

			<CertificationTraining
				certificationName={params.name}
				displayName={certification.certification.display_name}
				trainingContent={trainingContent.training_content}
				termsConditions={trainingContent.terms_conditions}
				onComplete={handleComplete}
				onOptOut={handleOptOut}
			/>
		</div>
	);
}
