import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail, Phone } from "lucide-react";
import { JobWithQuoteAndMessages } from "../types";

interface CustomerCardProps {
	job: JobWithQuoteAndMessages;
	onCallClick: () => void;
	onMessageClick: () => void;
}

export function CustomerCard({
	job,
	onCallClick,
	onMessageClick
}: CustomerCardProps) {
	return (
		<Card className="mt-4 md:mt-16">
			<CardHeader className="pb-3 md:pb-6">
				<CardTitle>Customer</CardTitle>
			</CardHeader>
			<CardContent className="space-y-3 md:space-y-4">
				<div className="flex items-start gap-3">
					<Avatar className="h-12 w-12">
						<AvatarFallback className="bg-primary text-white">
							{job.first_name?.[0] || "C"}
							{job.last_name?.[0] || ""}
						</AvatarFallback>
					</Avatar>

					<div className="flex-1">
						<h4 className="font-semibold">
							{job.first_name} {job.last_name}
						</h4>

						<div className="flex items-center gap-1 mt-1 text-sm text-gray-600">
							<Mail className="h-3 w-3" />
							<span>{job.email}</span>
						</div>

						<div className="flex items-center gap-1 mt-1 text-sm text-gray-600">
							<Phone className="h-3 w-3" />
							<span>{job.phone}</span>
						</div>
					</div>
				</div>

				{/* <div className="grid grid-cols-2 gap-2 pt-4 border-t">
					<Button
						variant="outline"
						size="sm"
						className="border-2"
						onClick={onCallClick}
					>
						<Phone className="h-4 w-4 mr-2" />
						Call
					</Button>
					<Button
						variant="outline"
						size="sm"
						className="border-2"
						onClick={onMessageClick}
					>
						<MessageSquare className="h-4 w-4 mr-2" />
						Message
					</Button>
				</div> */}
			</CardContent>
		</Card>
	);
}
