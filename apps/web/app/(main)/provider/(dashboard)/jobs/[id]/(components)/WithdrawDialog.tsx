import { useState } from "react";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    <PERSON><PERSON><PERSON>ooter,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Loader2 } from "lucide-react";

interface WithdrawDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onWithdraw: (message: string) => Promise<void>;
}

export function WithdrawDialog({
    open,
    onOpenChange,
    onWithdraw
}: WithdrawDialogProps) {
    const [message, setMessage] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const handleSubmit = async () => {
        setError(null);
        if (!message.trim()) {
            setError("Please provide a message explaining why you're withdrawing");
            return;
        }

        setIsSubmitting(true);
        try {
            await onWithdraw(message);
            onOpenChange(false);
        } catch (err) {
            setError("Failed to withdraw quote. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle>Withdraw Quote</DialogTitle>
                    <DialogDescription>
                        Please explain why you need to withdraw your quote. This message will be sent to the customer.
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4 py-4">
                    {error && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}

                    <div className="space-y-2">
                        <Textarea
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            placeholder="Example: I apologize, but I am no longer available during the requested timeframe..."
                            className="min-h-[100px]"
                        />
                    </div>
                </div>

                <DialogFooter>
                    <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
                        Cancel
                    </Button>
                    <Button 
                        variant="destructive" 
                        onClick={handleSubmit} 
                        disabled={isSubmitting}
                    >
                        {isSubmitting ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Withdrawing...
                            </>
                        ) : (
                            "Withdraw Quote"
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
} 