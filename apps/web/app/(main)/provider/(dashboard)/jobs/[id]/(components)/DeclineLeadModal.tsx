"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { RejectionReasonType } from "@rvhelp/database";
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import React, { useEffect, useState } from "react";
import type { QuoteWithMessages } from "../types";

interface DeclineLeadModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	quote: QuoteWithMessages;
	onResponseSent: () => void;
}

export function DeclineLeadModal({
	open,
	onOpenChange,
	quote,
	onResponseSent
}: DeclineLeadModalProps) {
	const [responseMessage, setResponseMessage] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [submissionState, setSubmissionState] = useState<
		"idle" | "success" | "error"
	>("idle");
	const [submissionMessage, setSubmissionMessage] = useState<string>("");
	const [declineReason, setDeclineReason] = useState<RejectionReasonType>(
		RejectionReasonType.TOO_BUSY
	);

	// Reason mapping for better UX
	const reasonOptions = [
		{ value: RejectionReasonType.TOO_BUSY, label: "📅 Too busy" },
		{
			value: RejectionReasonType.SCHEDULE_CONFLICT,
			label: "📅 Schedule conflict"
		},
		{
			value: RejectionReasonType.OUTSIDE_TRAVEL_AREA,
			label: "📍 Too far away"
		},
		{ value: RejectionReasonType.NOT_A_GOOD_FIT, label: "🔧 Not a good fit" },
		{ value: RejectionReasonType.OTHER, label: "❓ Other reason" }
	];

	const handleSubmitResponse = async () => {
		setIsSubmitting(true);

		try {
			const response = await fetch(`/api/provider/quotes/${quote.id}/reject`, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					rejection_reason_details: responseMessage,
					rejection_reason: declineReason
				})
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to decline job");
			}

			setSubmissionState("success");
			setSubmissionMessage("Response sent. The customer has been notified.");
		} catch (error) {
			console.error("Error declining job:", error);
			setSubmissionState("error");
			setSubmissionMessage(
				error instanceof Error ? error.message : "An error occurred"
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		if (isSubmitting) return;
		onOpenChange(false);
	};

	const handleSuccessClose = (e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();
		onResponseSent();
	};

	const handleReset = () => {
		setSubmissionState("idle");
		setSubmissionMessage("");
		setResponseMessage("");
		setDeclineReason(RejectionReasonType.TOO_BUSY);
	};

	// Reset state when modal opens/closes
	useEffect(() => {
		if (open) {
			setSubmissionState("idle");
			setSubmissionMessage("");
			setResponseMessage("");
			setDeclineReason(RejectionReasonType.TOO_BUSY);
		}
	}, [open]);

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="max-w-md">
				<div className="gap-2">
					{submissionState === "idle" && (
						<>
							<DialogHeader>
								<DialogTitle className="text-xl font-bold text-center">
									Decline Lead
								</DialogTitle>
								<DialogDescription className="text-center text-sm">
									Let customer know you aren't available with reason:
								</DialogDescription>
							</DialogHeader>

							<div className="py-4 space-y-4">
								<div className="flex flex-wrap gap-2">
									{reasonOptions.map((reason) => (
										<Button
											key={reason.value}
											variant="outline"
											onClick={() =>
												setDeclineReason(reason.value as RejectionReasonType)
											}
											className={`rounded-full px-4 py-2 text-sm ${
												declineReason === reason.value
													? "bg-blue-600 text-white border-blue-600"
													: "border-gray-300 hover:border-blue-600 hover:bg-blue-50"
											}`}
										>
											{reason.label}
										</Button>
									))}
								</div>

								<Textarea
									value={responseMessage}
									onChange={(e) => setResponseMessage(e.target.value)}
									placeholder="Add a personal note (optional)... e.g., 'I'm booked until next week but Mike's Mobile RV might be available'"
									className="min-h-[80px]"
								/>

								<div className="bg-green-50 p-3 rounded-lg text-sm">
									<div className="font-medium text-green-800">
										✓ Customer will be notified
									</div>
									<div className="text-green-700 mt-1">
										We'll let them know you can't help right now and the reason
										why. We'll also help find another service provider.
									</div>
								</div>

								<Button
									onClick={handleSubmitResponse}
									disabled={isSubmitting}
									className="w-full h-12 bg-green-600 hover:bg-green-700 font-semibold text-base"
								>
									{isSubmitting ? (
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
									) : null}
									Not Available
								</Button>
							</div>
						</>
					)}

					{submissionState === "success" && (
						<div className="text-center py-8">
							<CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
							<h2 className="text-xl font-bold mb-2">Response Sent!</h2>
							<p className="text-gray-600 mb-6">{submissionMessage}</p>
							<Button onClick={handleSuccessClose} className="w-full">
								Close
							</Button>
						</div>
					)}

					{submissionState === "error" && (
						<div className="text-center py-8">
							<AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
							<h2 className="text-xl font-bold mb-2">Error Occurred</h2>
							<p className="text-gray-600 mb-6">{submissionMessage}</p>
							<Button onClick={handleReset} className="w-full">
								Try Again
							</Button>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
