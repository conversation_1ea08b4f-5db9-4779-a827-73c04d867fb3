"use client";

import { Badge } from "@/components/ui/badge";
import { getCategoryName } from "@/lib/categories";
import { Calendar, MapPin, Truck } from "lucide-react";
import { useMemo } from "react";
import type { JobWithQuoteAndMessages, QuoteWithMessages } from "../types";

interface ProviderWorkroomHeaderProps {
	job: JobWithQuoteAndMessages;
	quote: QuoteWithMessages;
	isExpanded: boolean;
	onToggleExpand: () => void;
}

export default function ProviderWorkroomHeader({
	job,
	quote,
	isExpanded,
	onToggleExpand
}: ProviderWorkroomHeaderProps) {
	const requestLabel = useMemo(
		() =>
			`${job.rv_year || ""} ${job.rv_make || ""} ${job.rv_model || ""} ${getCategoryName(job.category)}`.trim(),
		[job.rv_year, job.rv_make, job.rv_model, job.category]
	);

	const formattedDate = useMemo(
		() =>
			new Date(job.created_at).toLocaleDateString("en-US", {
				year: "numeric",
				month: "long",
				day: "numeric"
			}),
		[job.created_at]
	);

	return (
		<div className="space-y-4">
			<div className="flex gap-6">
				{/* Main content column */}
				<div className="flex-1">
					<div className="flex items-center gap-2">
						<h1 className="text-2xl font-semibold">{requestLabel}</h1>
						<Badge className="capitalize bg-green-100 text-green-800 hover:bg-green-100 border-0">
							{job.status}
						</Badge>
					</div>

					<div className="flex flex-wrap gap-x-6 gap-y-2 mt-2 text-sm text-gray-600">
						<div className="flex items-center">
							<Calendar className="h-4 w-4 mr-1.5 text-gray-500" />
							<span>Created {formattedDate}</span>
						</div>
						{job.location && (
							<div className="flex items-center">
								<MapPin className="h-4 w-4 mr-1.5 text-gray-500" />
								<span>{job.location.address}</span>
							</div>
						)}
						<div className="flex items-center">
							<Truck className="h-4 w-4 mr-1.5 text-gray-500" />
							<span>
								{job.rv_year} {job.rv_make} {job.rv_model}
							</span>
						</div>
					</div>

					{/* Request message and read more */}
					<div className="mt-4">
						<div
							className={
								isExpanded ? "" : "line-clamp-2 text-ellipsis overflow-hidden"
							}
						>
							<span className="text-gray-700 whitespace-pre-line">
								{job.message}
							</span>
						</div>
						{!isExpanded && (
							<button
								className="text-primary underline text-sm"
								onClick={onToggleExpand}
							>
								Read more
							</button>
						)}
						{isExpanded && (
							<button
								className="text-primary underline text-sm mt-1"
								onClick={onToggleExpand}
							>
								Hide details
							</button>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
