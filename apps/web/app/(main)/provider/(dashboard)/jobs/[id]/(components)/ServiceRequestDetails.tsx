import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getCategoryName } from "@/lib/categories";
import { Car, Info, MapPin } from "lucide-react";
import { JobWithQuoteAndMessages } from "../types";

interface ServiceRequestDetailsProps {
	job: JobWithQuoteAndMessages;
}

export function ServiceRequestDetails({ job }: ServiceRequestDetailsProps) {
	return (
		<div className="space-y-6">
			{/* Service Request Info */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg font-medium flex items-center">
						<Info className="mr-2 h-5 w-5 text-gray-500" />
						Service Request Information
					</CardTitle>
				</CardHeader>
				<CardContent className="p-6">
					<div className="space-y-6">
						{/* Customer Message */}
						<div>
							<h3 className="text-md font-semibold text-gray-700 mb-3">
								Customer Message
							</h3>
							<div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
								<p className="text-gray-700 whitespace-pre-wrap break-words">
									{job.message}
								</p>
							</div>
						</div>

						{/* Service Type */}
						<div>
							<h3 className="text-md font-semibold text-gray-700 mb-3">
								Service Type
							</h3>
							<div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
								<div className="flex items-center gap-2">
									<Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-100">
										{getCategoryName(job.category)}
									</Badge>
								</div>
							</div>
						</div>

						{/* Location */}
						{job.location && (
							<div>
								<h3 className="text-md font-semibold text-gray-700 mb-3">
									Location
								</h3>
								<div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
									<div className="flex items-start">
										<MapPin className="w-5 h-5 text-emerald-600 mr-3 mt-0.5" />
										<div>
											<p className="text-gray-700">{job.location.address}</p>
											{job.distance_miles !== null &&
												typeof job.distance_miles === "number" && (
													<p className="text-sm text-gray-500 mt-1">
														{job.distance_miles.toFixed(1)} miles away
													</p>
												)}
										</div>
									</div>
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>

			{/* RV Information */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg font-medium flex items-center">
						<Car className="mr-2 h-5 w-5 text-gray-500" />
						RV Information
					</CardTitle>
				</CardHeader>
				<CardContent className="p-6">
					{job.rv_year || job.rv_make || job.rv_model ? (
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{job.rv_year && (
								<div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
									<p className="text-sm text-gray-500">Year</p>
									<p className="text-gray-700 font-medium">{job.rv_year}</p>
								</div>
							)}
							{job.rv_make && (
								<div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
									<p className="text-sm text-gray-500">Make</p>
									<p className="text-gray-700 font-medium">{job.rv_make}</p>
								</div>
							)}
							{job.rv_model && (
								<div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
									<p className="text-sm text-gray-500">Model</p>
									<p className="text-gray-700 font-medium">{job.rv_model}</p>
								</div>
							)}
							{job.rv_type && (
								<div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
									<p className="text-sm text-gray-500">Type</p>
									<p className="text-gray-700 font-medium">{job.rv_type}</p>
								</div>
							)}
						</div>
					) : (
						<div className="bg-gray-50 rounded-lg p-4 border border-gray-100 text-center">
							<p className="text-gray-500">No RV details provided</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
