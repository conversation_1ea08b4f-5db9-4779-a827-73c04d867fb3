"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import type { QuoteWithMessages } from "../types";

interface AcceptLeadModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	quote: QuoteWithMessages;
	onResponseSent: () => void;
	isWarrantyJob?: boolean;
}

export function AcceptLeadModal({
	open,
	onOpenChange,
	quote,
	onResponseSent,
	isWarrantyJob = false
}: AcceptLeadModalProps) {
	const [responseMessage, setResponseMessage] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [shouldStartJob, setShouldStartJob] = useState(false);
	const [submissionState, setSubmissionState] = useState<
		"idle" | "success" | "error"
	>("idle");
	const [submissionMessage, setSubmissionMessage] = useState<string>("");

	const handleSubmitResponse = useCallback(async () => {
		setIsSubmitting(true);

		try {
			const response = await fetch(`/api/provider/quotes/${quote.id}/accept`, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					message: responseMessage,
					shouldStartJob
				})
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to accept job");
			}

			// For warranty jobs, call onResponseSent immediately to show the warranty success dialog
			// For regular jobs, show the success state
			if (isWarrantyJob) {
				onResponseSent();
			} else {
				setSubmissionState("success");
			}
		} catch (error) {
			console.error("Error accepting job:", error);
			setSubmissionState("error");
			setSubmissionMessage(
				error instanceof Error ? error.message : "An error occurred"
			);
		} finally {
			setIsSubmitting(false);
		}
	}, [quote.id, responseMessage, shouldStartJob, isWarrantyJob, onResponseSent]);

	const handleClose = () => {
		if (isSubmitting) return;
		onOpenChange(false);
	};

	const handleReset = () => {
		setSubmissionState("idle");
		setSubmissionMessage("");
		setResponseMessage("");
		setShouldStartJob(false);
	};

	const handleSuccessContinue = () => {
		onResponseSent();
	};

	// Reset state when modal opens/closes
	useEffect(() => {
		if (open) {
			setSubmissionState("idle");
			setSubmissionMessage("");
			setResponseMessage("");
			setShouldStartJob(false);
		}
	}, [open]);

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="max-w-md">
				<div className="gap-2">
					{submissionState === "idle" && (
						<>
							<DialogHeader>
								<DialogTitle className="text-xl font-bold text-center">
									Accept Lead
								</DialogTitle>
								<DialogDescription className="text-center text-sm">
									Add an optional message for the customer
								</DialogDescription>
							</DialogHeader>

							<div className="py-6 space-y-4">
								<div className="bg-green-50 p-3 rounded-lg text-sm">
									<div className="font-medium text-green-800">
										✓ Customer will be notified via email
									</div>
									<div className="text-green-700 mt-1">
										They'll receive an email notification that you've accepted
										their request and can start working together.
									</div>
								</div>

								<Textarea
									value={responseMessage}
									onChange={(e) => setResponseMessage(e.target.value)}
									placeholder="Add a personal note (optional)..."
									className="min-h-[80px]"
								/>

								{isWarrantyJob && (
									<>
										<div className="space-y-4">
											<div className="space-y-1">

												<h4 className="font-medium text-gray-900">
													Already agreed to perform the warranty service?
												</h4>
											</div>
											<div className="flex items-start space-x-3 p-4 bg-yellow-50 border border-blue-200 rounded-lg">
												<Checkbox
													id="confirm-contact"
													checked={shouldStartJob}
													onCheckedChange={(checked) =>
														setShouldStartJob(checked as boolean)
													}
													className="mt-0.5"
												/>
												<label
													htmlFor="confirm-contact"
													className="text-sm text-yellow-800 leading-relaxed cursor-pointer"
												>
													I have already communicated with the customer and come to an agreement to perform the requested service.
												</label>
											</div>
										</div>

										<div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
											<div className="flex items-start space-x-2">
												<div className="text-blue-600 text-lg font-semibold">💡</div>
												<div className="space-y-1">
													<div className="font-medium text-blue-900">
														Pro Tip
													</div>
													<div className="text-sm text-blue-800 leading-relaxed">
														Its not necessary to accept the lead before you contact the customer, but by doing so you will improve your response time metric.  This doesn&rsquo;t lock you into the job &mdash; just shows your interest.
													</div>
												</div>
											</div>
										</div>
									</>
								)}

								<Button
									onClick={handleSubmitResponse}
									disabled={isSubmitting}
									className="w-full h-12 bg-green-600 hover:bg-green-700 font-semibold text-base"
								>
									{isSubmitting ? (
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
									) : null}
									{shouldStartJob ? "Accept and Start Job" : "Accept Lead"}
								</Button>
							</div>
						</>
					)}

					{submissionState === "success" && (
						<div className="text-center py-8">
							<div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
								<CheckCircle className="h-8 w-8 text-green-600" />
							</div>
							<h2 className="text-xl font-bold mb-2 text-gray-900">
								Lead Accepted Successfully!
							</h2>
							<p className="text-gray-600 mb-6">
								{shouldStartJob
									? "You&apos;ve successfully accepted this lead and started the job. The status has been set to &apos;In Progress&apos;."
									: "You&apos;ve successfully accepted this lead. You can now communicate with the customer and manage the job."
								}
							</p>
							<Button
								onClick={handleSuccessContinue}
								className="w-full bg-[#43806c] hover:bg-[#2c5446] text-white"
							>
								Continue
							</Button>
						</div>
					)}

					{submissionState === "error" && (
						<div className="text-center py-8">
							<AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
							<h2 className="text-xl font-bold mb-2">Error Occurred</h2>
							<p className="text-gray-600 mb-6">{submissionMessage}</p>
							<Button onClick={handleReset} className="w-full">
								Try Again
							</Button>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog >
	);
}
