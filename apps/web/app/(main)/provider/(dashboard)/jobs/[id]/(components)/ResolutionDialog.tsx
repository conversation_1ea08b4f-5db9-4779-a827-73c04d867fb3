import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>er,
	DialogTitle
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { JobStatus, Quote } from "@rvhelp/database";
import { Lock } from "lucide-react";
import { useState } from "react";
import toast from "react-hot-toast";

export type JobResolution = {
	status: JobStatus;
	sendInvoice: boolean;
	requestReview: boolean;
	reviewDelayHours?: number;
};

interface ResolutionDialogProps {
	quote: Quote;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSuccess?: () => void;
}

export function ResolutionDialog({
	quote,
	open,
	onOpenChange,
	onSuccess
}: ResolutionDialogProps) {
	const [step, setStep] = useState<"confirm" | "options">("confirm");
	const [sendInvoice, setSendInvoice] = useState(false);
	const [requestReview, setRequestReview] = useState(false);
	const [reviewDelay, setReviewDelay] = useState(24);
	const [reviewDelayUnit, setReviewDelayUnit] = useState<"hours" | "days">(
		"hours"
	);
	const [isLoading, setIsLoading] = useState(false);

	const handleCompleteJob = async (resolution: JobResolution) => {
		if (!quote) return;

		setIsLoading(true);
		try {
			const response = await fetch(
				`/api/provider/quotes/${quote.id}/complete`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify(resolution)
				}
			);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to complete job");
			}

			toast.success("Job completed successfully");
			onOpenChange(false);
			onSuccess?.();
		} catch (error) {
			console.error("Error completing job:", error);
			toast.error("Failed to complete job");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog
			open={open}
			onOpenChange={(open) => {
				if (!isLoading) {
					onOpenChange(open);
					if (!open) {
						setStep("confirm");
						setSendInvoice(false);
						setRequestReview(false);
						setReviewDelay(24);
						setReviewDelayUnit("hours");
					}
				}
			}}
		>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>
						{step === "confirm" ? "Confirm Completion" : "Additional Options"}
					</DialogTitle>
				</DialogHeader>
				<div className="space-y-4 py-4">
					{step === "confirm" ? (
						<div className="space-y-4">
							<p className="text-sm text-gray-600">
								Are you sure you want to mark this job as complete? This action
								cannot be undone.
							</p>
							<div className="flex justify-end gap-3">
								<Button
									variant="outline"
									onClick={() => onOpenChange(false)}
									disabled={isLoading}
								>
									Cancel
								</Button>
								<Button
									variant="default"
									onClick={() => setStep("options")}
									className="bg-[#43806c] text-white hover:bg-[#2c5446]"
									disabled={isLoading}
								>
									Continue
								</Button>
							</div>
						</div>
					) : (
						<div className="space-y-6">
							<div className="space-y-4">
								<div className="p-4 bg-gray-50 rounded-lg">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											<Lock className="w-4 h-4 text-gray-400" />
											<div className="space-y-0.5">
												<label className="text-sm font-medium">
													Send Invoice
												</label>
												<p className="text-sm text-gray-500">Coming soon</p>
											</div>
										</div>
										<Switch disabled checked={false} />
									</div>
								</div>

								<div className="p-4 bg-gray-50 rounded-lg">
									<div className="flex items-center gap-2">
										<Switch
											checked={requestReview}
											onCheckedChange={setRequestReview}
											disabled={isLoading}
										/>
										<span className="font-medium">
											Request a review from the customer
										</span>
									</div>

									{requestReview && (
										<div className="mt-4 space-y-3">
											<p className="text-sm text-gray-600">
												When should we send the review request?
											</p>

											<div className="space-y-3">
												<label className="flex items-center gap-2 cursor-pointer">
													<input
														type="radio"
														name="reviewTiming"
														checked={reviewDelay === 0}
														onChange={() => {
															setReviewDelay(0);
															setReviewDelayUnit("hours");
														}}
														className="w-4 h-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
														disabled={isLoading}
													/>
													<span className="text-sm">Send immediately</span>
												</label>

												<label className="flex items-center gap-2 cursor-pointer">
													<input
														type="radio"
														name="reviewTiming"
														checked={reviewDelay > 0}
														onChange={() => {
															if (reviewDelay === 0) {
																setReviewDelay(24);
															}
														}}
														className="w-4 h-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
														disabled={isLoading}
													/>
													<span className="text-sm">Send after a delay</span>
												</label>

												{reviewDelay > 0 && (
													<div className="ml-6 flex items-center gap-3">
														<input
															type="number"
															min="1"
															value={reviewDelay}
															onChange={(e) =>
																setReviewDelay(parseInt(e.target.value) || 1)
															}
															className="w-20 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-emerald-500"
															disabled={isLoading}
														/>
														<select
															value={reviewDelayUnit}
															onChange={(e) =>
																setReviewDelayUnit(
																	e.target.value as "hours" | "days"
																)
															}
															className="px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-emerald-500"
															disabled={isLoading}
														>
															<option value="hours">hours</option>
															<option value="days">days</option>
														</select>
														<span className="text-sm text-gray-600">
															after marking as completed
														</span>
													</div>
												)}
											</div>
										</div>
									)}
								</div>
							</div>

							<div className="flex justify-end gap-3">
								<Button
									variant="outline"
									onClick={() => setStep("confirm")}
									disabled={isLoading}
								>
									Back
								</Button>
								<Button
									variant="default"
									onClick={() =>
										handleCompleteJob({
											status: JobStatus.COMPLETED,
											sendInvoice,
											requestReview,
											reviewDelayHours: requestReview
												? reviewDelayUnit === "days"
													? reviewDelay * 24
													: reviewDelay
												: undefined
										})
									}
									disabled={isLoading}
									className="bg-[#43806c] text-white hover:bg-[#2c5446]"
								>
									{isLoading ? "Completing..." : "Complete Job"}
								</Button>
							</div>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
