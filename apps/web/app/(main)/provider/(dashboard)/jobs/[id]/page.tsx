"use client";

import { Breadcrumbs } from "@/components/ui/breadcrumbs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { withAuthorization } from "@/lib/hooks/withAuthorization";
import { AlertCircle, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";

// Components
import MessageThread from "@/components/MessageThread";
import FormFillModal from "@/components/oem/form-filling/form-fill-modal";
import { OEMRequirementsDialog } from "@/components/oem/oem-requirements-dialog";
import { ProviderInvoiceWizard } from "@/components/oem/provider-invoice-wizard";
import { ProviderUpdateWizard } from "@/components/oem/provider-update-wizard";
import { WarrantyPricingModal } from "@/components/oem/warranty-pricing-modal";
import { QuickActions } from "./(components)/QuickActions";
import { ServiceRequestDetails } from "./(components)/ServiceRequestDetails";
import { StartJobModal } from "./(components)/StartJobModal";
import { WithdrawDialog } from "./(components)/WithdrawDialog";

// Types
import { WarrantyRequestHeader } from "@/components/oem/warranty-request-header";
import {
	ExtendedCompany,
	ExtendedListing,
	ExtendedWarrantyRequest
} from "@/types/warranty";
import { QuoteStatus, StripeConnection } from "@rvhelp/database";
import { CompleteJobModal } from "./(components)/CompleteJobModal";

import { ProviderWarrantyTabs } from "@/components/oem/provider-warranty-tabs";
import { WarrantyPrerequisites } from "@/components/oem/warranty-prerequisites";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { formatCurrency } from "@/lib/utils";
import { CheckCircle, ExternalLink } from "lucide-react";
import { useSession } from "next-auth/react";
import { CustomerCard } from "./(components)/CustomerCard";
import { OEMSupportCard } from "./(components)/OEMSupportCard";
import { JobWithQuoteAndMessages, QuoteWithMessages } from "./types";

function ServiceRequestDetailsPage({ params }: { params: { id: string } }) {
	const router = useRouter();
	const [job, setJob] = useState<JobWithQuoteAndMessages | null>(null);
	const [quote, setQuote] = useState<QuoteWithMessages | null>(null);
	const [warrantyRequest, setWarrantyRequest] =
		useState<ExtendedWarrantyRequest | null>(job?.warranty_request);
	const [company, setCompany] = useState<ExtendedCompany | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [showWithdrawDialog, setShowWithdrawDialog] = useState(false);
	const [warrantyInvoiceSuccess, setWarrantyInvoiceSuccess] = useState<{
		invoice?: any;
		request?: any;
	} | null>(null);
	const [showWarrantyUpdateModal, setShowWarrantyUpdateModal] = useState(false);
	const [showWarrantyInvoiceModal, setShowWarrantyInvoiceModal] =
		useState(false);
	const [activeTab, setActiveTab] = useState("details");
	const [showCompleteJobModal, setShowCompleteJobModal] = useState(false);
	const [showDocumentUpdateModal, setShowDocumentUpdateModal] = useState(false);
	const [showDownloadFormsModal, setShowDownloadFormsModal] = useState(false);
	const [showStartJobModal, setShowStartJobModal] = useState(false);
	const [showWarrantyPricingModal, setShowWarrantyPricingModal] =
		useState(false);
	const [stripeConnect, setStripeConnect] = useState<StripeConnection | null>(
		null
	);
	const [listing, setListing] = useState<ExtendedListing | null>(null);
	const { data: session } = useSession();

	useEffect(() => {
		setWarrantyRequest(job?.warranty_request);
	}, [job?.warranty_request]);

	const fetchServiceRequest = useCallback(async () => {
		try {
			const response = await fetch(`/api/provider/quotes/${params.id}`);
			if (!response.ok) {
				throw new Error("Failed to fetch service request");
			}
			const data = await response.json();
			setQuote(data);
			setListing(data.listing);
			setJob(data.job);
			setWarrantyRequest(data.job.warranty_request);
		} catch (error) {
			console.error("Error fetching service request:", error);
			toast.error("Failed to load service request details");
		} finally {
			setIsLoading(false);
		}
	}, [params.id]);

	useEffect(() => {
		fetchServiceRequest();
	}, [fetchServiceRequest, params.id]);

	const fetchStripeConnect = useCallback(async () => {
		const response = await fetch(
			`/api/provider/${session?.user.id}/stripe-connect`
		);
		if (response.ok) {
			const data = await response.json();
			setStripeConnect(data);
		}
	}, [session?.user.id]);

	useEffect(() => {
		fetchStripeConnect();
	}, [fetchStripeConnect]);

	useEffect(() => {
		const fetchCompany = async () => {
			if (!warrantyRequest?.company_id) return;
			const result = await fetch(
				`/api/provider/companies/${warrantyRequest.company_id}`
			);
			const data = await result.json();
			setCompany(data);
		};
		fetchCompany();
	}, [warrantyRequest?.company_id]);

	const handleWithdraw = useCallback(
		async (message: string) => {
			if (!quote) return;
			try {
				const response = await fetch(
					`/api/provider/quotes/${quote.id}/withdraw`,
					{
						method: "POST",
						headers: {
							"Content-Type": "application/json"
						},
						body: JSON.stringify({ message })
					}
				);
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || "Failed to withdraw quote");
				}
				toast.success("Proposal withdrawn successfully");
				const updatedQuote = await response.json();
				setQuote(updatedQuote);
			} catch (error) {
				console.error("Error withdrawing proposal:", error);
				toast.error(
					error instanceof Error ? error.message : "Failed to withdraw proposal"
				);
			}
		},
		[quote]
	);

	const handleDownloadForms = useCallback(async () => {
		if (!warrantyRequest) return;
		setShowDownloadFormsModal(true);
	}, [warrantyRequest]);

	const handleStartJob = useCallback(async () => {
		setShowStartJobModal(true);
	}, []);

	const handleDocumentUpdate = useCallback(async () => {
		if (!warrantyRequest) return;
		setShowDocumentUpdateModal(true);
	}, [warrantyRequest]);

	const handleCompleteJob = useCallback(async () => {
		setShowCompleteJobModal(true);
	}, []);

	const handleConfigureWarrantyPricing = useCallback(async () => {
		setShowWarrantyPricingModal(true);
	}, []);

	const handleRequestAuthorization = useCallback(async () => {
		setShowWarrantyUpdateModal(true);
	}, []);

	const handleRequestPayment = useCallback(async () => {
		setShowWarrantyInvoiceModal(true);
	}, []);

	const handleWarrantyUpdateSuccess = useCallback(
		async (
			updatedRequest?: ExtendedWarrantyRequest & { _invoiceData?: any }
		) => {
			setShowWarrantyUpdateModal(false);
			// Refresh the data to show the updated warranty request
			const response = await fetch(`/api/provider/quotes/${params.id}`);
			if (response.ok) {
				const data = await response.json();
				setJob(data.job);
				setWarrantyRequest(data.job.warranty_request);
				setQuote(data);
			}
		},
		[params.id]
	);

	const handleWarrantyInvoiceSuccess = useCallback(
		async (
			updatedRequest?: ExtendedWarrantyRequest & { _invoiceData?: any }
		) => {
			// Check if this was an invoice creation (complete job)
			if (updatedRequest?._invoiceData) {
				setWarrantyInvoiceSuccess({
					invoice: updatedRequest._invoiceData,
					request: updatedRequest
				});
				// Don't close modal yet - let the success state handle it
				return;
			}

			// For other updates, proceed as normal
			setShowWarrantyInvoiceModal(false);
			// Refresh the data to show the updated warranty request
			const response = await fetch(`/api/provider/quotes/${params.id}`);
			if (response.ok) {
				const data = await response.json();
				setJob(data.job);
				setWarrantyRequest(data.job.warranty_request);
				setQuote(data);
			}
		},
		[params.id]
	);

	const handleDocumentUpdateSuccess = useCallback(async () => {
		setShowDocumentUpdateModal(false);
		fetchServiceRequest();
	}, [fetchServiceRequest]);

	const handleWarrantyInvoiceSuccessClose = useCallback(async () => {
		setShowWarrantyInvoiceModal(false);
		// Refresh the data to show the updated warranty request
		const response = await fetch(`/api/provider/quotes/${params.id}`);
		if (response.ok) {
			const data = await response.json();
			setJob(data.job);
			setWarrantyRequest(data.job.warranty_request);
			setQuote(data);
		}
	}, [params.id]);

	// Find the first PDF attachment for demonstration
	const pdfAttachment = job?.warranty_request?.attachments?.find(
		(att) => att.type === "form"
	);

	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-[60vh]">
				<div className="flex flex-col items-center gap-4">
					<Loader2 className="h-12 w-12 animate-spin text-emerald-600" />
					<p className="text-emerald-800 font-medium">
						Loading service request...
					</p>
				</div>
			</div>
		);
	}

	if (!job || !quote) {
		return (
			<div className="container mx-auto py-8 px-4">
				<Breadcrumbs
					className="mb-4"
					items={[
						{ label: "Jobs", href: "/provider/jobs" },
						{
							label: "Job Details",
							href: `/provider/jobs/${params.id}`
						}
					]}
				/>
				<Card>
					<CardContent className="py-12">
						<div className="text-center space-y-6">
							<AlertCircle className="h-16 w-16 text-gray-400 mx-auto" />
							<p className="text-lg text-gray-600">
								We couldn't find the service request you're looking for.
							</p>
							<Button
								onClick={() => router.push("/provider/service-requests")}
								className="mt-4 bg-emerald-600 hover:bg-emerald-700"
							>
								Back to Service Requests
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	const shouldShowMessages =
		quote.status === QuoteStatus.PENDING ||
		quote.status === QuoteStatus.ACCEPTED ||
		quote.status === QuoteStatus.IN_PROGRESS;

	return (
		<div className="py-4 md:py-6">
			<Breadcrumbs
				className="mb-3 md:mb-4"
				items={[
					{ label: "Jobs", href: "/provider/jobs" },
					{
						label: "Job Details",
						href: `/provider/jobs/${params.id}`
					}
				]}
			/>

			<div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
				{/* Left Column - Main Content */}
				<div className="lg:col-span-2">
					<Tabs value={activeTab} onValueChange={setActiveTab}>
						{shouldShowMessages && (
							<TabsList className="grid w-full max-w-md mx-auto mb-4 md:mb-6 bg-gray-100 p-1 rounded-lg grid-cols-2">
								<TabsTrigger
									value="details"
									className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm rounded-md transition-all text-sm md:text-base"
								>
									Request Details
								</TabsTrigger>

								<TabsTrigger
									value="messages"
									className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm rounded-md transition-all text-sm md:text-base"
								>
									Messages
								</TabsTrigger>
							</TabsList>
						)}

						<TabsContent value="details" className="mt-0">
							{warrantyRequest ? (
								<WarrantyRequestHeader
									job={job}
									collapsable={false}
									content={
										<div className="grid grid-cols-1 gap-4 mb-4 text-gray-900">
											<ProviderWarrantyTabs
												quote={quote}
												job={job}
												stripeConnect={stripeConnect}
												onRequestAuthorization={handleRequestAuthorization}
												onRequestPayment={handleRequestPayment}
												onDownloadForms={handleDownloadForms}
												onCompleteJob={handleCompleteJob}
												onStartJob={handleStartJob}
											/>
										</div>
									}
								/>
							) : (
								<ServiceRequestDetails job={job} />
							)}
						</TabsContent>

						<TabsContent value="messages" className="mt-0">
							{quote && (
								<MessageThread
									quoteId={quote.id}
									viewerRole="PROVIDER"
									providerId={quote.listing_id}
									onMessageSent={async () => {
										const response = await fetch(
											`/api/provider/quotes/${params.id}`
										);
										if (response.ok) {
											const data = await response.json();
											setJob(data.job);
											setQuote(data);
										}
									}}
								/>
							)}
						</TabsContent>
					</Tabs>
				</div>

				{/* Right Column - Customer Info & Quick Actions */}
				<div className="space-y-4 md:space-y-6">
					{/* OEM Support Card - Mobile Only (before CustomerCard) */}
					<div className="block md:hidden">
						<OEMSupportCard
							company={company}
							representative={
								job.warranty_request.oem_user.first_name +
								" " +
								job.warranty_request.oem_user.last_name
							}
							email={
								job.warranty_request.oem_user.email
							}
						/>
					</div>

					<CustomerCard
						job={job}
						onCallClick={() => { }}
						onMessageClick={() => { }}
					/>
					{warrantyRequest &&
						(!listing?.pricing_settings?.warranty_rate ||
							stripeConnect?.stripe_account_status !== "active") && (
							<WarrantyPrerequisites
								onConfigureWarrantyPricing={handleConfigureWarrantyPricing}
								pricingConfigured={listing?.pricing_settings?.warranty_rate > 0}
								stripeConfigured={
									stripeConnect?.stripe_account_status === "active"
								}
							/>
						)}
					<QuickActions
						status={quote.status as QuoteStatus}
						onAccept={() => { }}
						onDecline={() => { }}
						onMessage={() => setActiveTab("messages")}
						onCompleteJob={handleCompleteJob}
						onWithdraw={() => setShowWithdrawDialog(true)}
						onDocumentUpdate={handleDocumentUpdate}
						onRequestAuthorization={handleRequestAuthorization}
						onRequestPayment={handleRequestPayment}
						onConfigureWarrantyPricing={handleConfigureWarrantyPricing}
						job={job}
						stripeConnect={stripeConnect}
					/>

					{/* OEM Support Card - Desktop Only (at bottom) */}
					<div className="hidden md:block">
						<OEMSupportCard
							company={company}
							representative={
								job.warranty_request.oem_user.first_name +
								" " +
								job.warranty_request.oem_user.last_name
							}
							email={
								job.warranty_request.oem_user.email
							}
						/>
					</div>
				</div>
			</div>
			<WithdrawDialog
				open={showWithdrawDialog}
				onOpenChange={setShowWithdrawDialog}
				onWithdraw={handleWithdraw}
			/>
			{warrantyRequest && (
				<>
					<Dialog
						open={showWarrantyUpdateModal}
						onOpenChange={() => setShowWarrantyUpdateModal(false)}
					>
						<DialogContent>
							<ProviderUpdateWizard
								company={company}
								request={warrantyRequest}
								quote={quote}
								onCancel={() => {
									setShowWarrantyUpdateModal(false);
								}}
								onSuccess={handleWarrantyUpdateSuccess}
							/>
						</DialogContent>
					</Dialog>
				</>
			)}

			{warrantyRequest && (
				<>
					<Dialog
						open={showWarrantyInvoiceModal}
						onOpenChange={() => setShowWarrantyInvoiceModal(false)}
					>
						<DialogContent>
							{warrantyInvoiceSuccess ? (
								<div className="flex flex-col items-center justify-center py-10">
									<CheckCircle className="h-16 w-16 text-green-500 mb-4" />
									<h2 className="text-2xl font-bold mb-2">
										Invoice Submitted Successfully!
									</h2>
									<p className="text-center text-lg text-muted-foreground mb-4">
										Your invoice has been created and sent to RV Help to review.
										You will be notified when the invoice is approved and paid.
									</p>

									<p className="text-center text-xl text-muted-foreground mb-4">
										We review and pay all invoices within 2-3 business days.
									</p>

									<Card className="w-full max-w-md mb-6">
										<CardHeader>
											<CardTitle className="text-lg">Invoice Details</CardTitle>
										</CardHeader>
										<CardContent className="space-y-2">
											<div className="flex justify-between">
												<span className="text-muted-foreground">
													Invoice #:
												</span>
												<span className="font-medium">
													#{warrantyInvoiceSuccess.invoice.invoice_number}
												</span>
											</div>
											<div className="flex justify-between">
												<span className="text-muted-foreground">Amount:</span>
												<span className="font-medium">
													{formatCurrency(
														warrantyInvoiceSuccess.invoice.amount
													)}
												</span>
											</div>
											<div className="flex justify-between">
												<span className="text-muted-foreground">Status:</span>
												<Badge variant="default">Sent to RV Help</Badge>
											</div>
										</CardContent>
									</Card>

									<div className="flex gap-3">
										<Button variant="outline" asChild>
											<a
												href={`/api/invoices/${warrantyInvoiceSuccess.invoice.id}/pdf`}
												target="_blank"
												rel="noopener noreferrer"
												className="flex items-center"
											>
												<ExternalLink className="w-4 h-4 mr-2" />
												View PDF
											</a>
										</Button>
										<Button onClick={handleWarrantyInvoiceSuccessClose}>
											Continue
										</Button>
									</div>
								</div>
							) : (
								<ProviderInvoiceWizard
									company={company}
									request={warrantyRequest}
									quote={quote}
									onCancel={() => {
										setShowWarrantyUpdateModal(false);
									}}
									onSuccess={handleWarrantyInvoiceSuccess}
								/>
							)}
						</DialogContent>
					</Dialog>
				</>
			)}

			{/* Non-warranty job completion modal */}
			<CompleteJobModal
				job={job}
				quote={quote}
				open={showCompleteJobModal}
				onClose={() => setShowCompleteJobModal(false)}
				onJobCompleted={() => {
					// Refresh the data after completion
					fetchServiceRequest();
					setShowCompleteJobModal(false);
				}}
			/>
			{/* Start Job Modal */}
			<StartJobModal
				job={job}
				quote={quote}
				open={showStartJobModal}
				onClose={() => setShowStartJobModal(false)}
				onJobStarted={() => {
					// Refresh the data after starting job
					fetchServiceRequest();
					setShowStartJobModal(false);
				}}
			/>
			{/* PDF Form Fill Modal */}
			<FormFillModal
				open={showDocumentUpdateModal}
				onClose={handleDocumentUpdateSuccess}
				attachment={pdfAttachment}
				warrantyRequest={warrantyRequest}
			/>
			{/* Download Required Forms Modal */}
			{warrantyRequest && (
				<OEMRequirementsDialog
					open={showDownloadFormsModal}
					onClose={async () => {
						setShowDownloadFormsModal(false);
						const response = await fetch(`/api/provider/quotes/${params.id}`);
						if (response.ok) {
							const data = await response.json();
							setJob(data.job);
							setWarrantyRequest(data.job.warranty_request);
							setQuote(data);
						}
					}}
					warrantyRequest={warrantyRequest}
				/>
			)}
			{/* Warranty Pricing Modal */}
			{warrantyRequest && (
				<WarrantyPricingModal
					open={showWarrantyPricingModal}
					onOpenChange={setShowWarrantyPricingModal}
					listingId={listing?.id}
					currentWarrantyRate={
						listing?.pricing_settings?.warranty_rate ??
						listing?.pricing_settings?.hourly_rate
					}
					onPricingUpdated={() => {
						// Refresh the data after pricing update
						fetchServiceRequest();
					}}
				/>
			)}
		</div>
	);
}

export default withAuthorization(ServiceRequestDetailsPage, "PROVIDER");
