import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	<PERSON><PERSON>,
	<PERSON>alog<PERSON>ontent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { JobWithQuoteAndMessages, QuoteWithMessages } from "../types";

interface StartJobModalProps {
	open: boolean;
	onClose: () => void;
	job: JobWithQuoteAndMessages;
	quote: QuoteWithMessages;
	onJobStarted: () => void;
}

export function StartJobModal({
	open,
	onClose,
	job,
	quote,
	onJobStarted
}: StartJobModalProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [hasConfirmedContact, setHasConfirmedContact] = useState(false);

	const handleStartJob = async () => {
		if (!hasConfirmedContact) {
			toast.error("Please confirm that you have contacted the customer");
			return;
		}

		setIsSubmitting(true);
		try {
			const response = await fetch(
				`/api/provider/quotes/${quote.id}/start-job`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					}
				}
			);

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.message || "Failed to start job");
			}

			toast.success("Job started successfully");
			onJobStarted();
			onClose();
		} catch (error) {
			console.error("Error starting job:", error);
			toast.error(
				error instanceof Error ? error.message : "Failed to start job"
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		setHasConfirmedContact(false);
		onClose();
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>Start Job</DialogTitle>
				</DialogHeader>
				<div className="space-y-4 py-4">
					<div className="space-y-4">
						<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
							<h3 className="font-medium text-blue-900 mb-2">
								Before Starting the Job
							</h3>
							<p className="text-sm text-blue-800 leading-relaxed">
								Please ensure you have contacted the customer off-platform and
								they have agreed to work with you. Setting a job to 'In
								Progress' indicates that work has begun and the customer is
								expecting service.
							</p>
						</div>

						<div className="space-y-3">
							<h4 className="font-medium text-gray-900">
								Service Request Details
							</h4>
							<div className="p-3 bg-gray-50 rounded-md">
								<p className="text-sm text-gray-700 whitespace-pre-wrap">
									{job.message}
								</p>
							</div>
						</div>

						<div className="flex items-start space-x-3 p-4 bg-amber-50 border border-amber-200 rounded-lg">
							<Checkbox
								id="confirm-contact"
								checked={hasConfirmedContact}
								onCheckedChange={(checked) =>
									setHasConfirmedContact(checked as boolean)
								}
								className="mt-0.5"
							/>
							<label
								htmlFor="confirm-contact"
								className="text-sm text-amber-800 leading-relaxed cursor-pointer"
							>
								I confirm that I have contacted the customer off-platform and
								they have agreed to work with me. I understand that setting this
								job to 'In Progress' indicates that work has begun.
							</label>
						</div>
					</div>
				</div>

				<div className="flex justify-end space-x-3 pt-4 border-t">
					<Button
						variant="outline"
						onClick={handleClose}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						onClick={handleStartJob}
						disabled={!hasConfirmedContact || isSubmitting}
						className="bg-emerald-600 hover:bg-emerald-700"
					>
						{isSubmitting ? "Starting..." : "Start Job"}
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
}
