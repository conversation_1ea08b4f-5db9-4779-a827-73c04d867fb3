"use client";

import ImageUpload from "@/components/ImageUpload";
import { EditModal } from "@/components/oem/warranty-details/edit-modal";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ExtendedCompany, ExtendedWarrantyRequest } from "@/types/warranty";
import { Company } from "@rvhelp/database";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";

interface UpdateProgressModalProps {
	open: boolean;
	onClose: () => void;
	request: ExtendedWarrantyRequest;
	onProgressUpdated: () => void;
	company?: Company;
}

interface ProgressAttachment {
	id: string;
	type: "image" | "document";
	title: string;
	url: string;
}

export function UpdateProgressModal({
	open,
	onClose,
	request,
	onProgressUpdated,
	company
}: UpdateProgressModalProps) {
	const [loading, setLoading] = useState(false);
	const [notes, setNotes] = useState("");
	const [attachments, setAttachments] = useState<ProgressAttachment[]>([]);
	const [error, setError] = useState<string | null>(null);

	const handleAddAttachment = (url: string, type: "image" | "document") => {
		const newAttachment: ProgressAttachment = {
			id: uuidv4(),
			type,
			title: `Attachment ${attachments.length + 1}`,
			url
		};
		setAttachments([...attachments, newAttachment]);
	};

	const handleRemoveAttachment = (id: string) => {
		setAttachments(attachments.filter((attachment) => attachment.id !== id));
	};

	const handleSave = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!notes.trim() && attachments.length === 0) {
			setError("Please add either notes or attachments");
			return;
		}

		setLoading(true);
		setError(null);

		try {
			// TODO: Implement API call to save progress update
			// await updateProgress(request.id, { notes, attachments });
			onProgressUpdated();
			onClose();
		} catch (error) {
			console.error("Failed to update progress:", error);
			setError("Failed to update progress. Please try again.");
		} finally {
			setLoading(false);
		}
	};

	return (
		<EditModal
			open={open}
			onClose={onClose}
			onSave={handleSave}
			isSaving={loading}
			saveButtonText={loading ? "Saving..." : "Save Progress"}
			company={company as ExtendedCompany}
		>
			{/* Enhanced Header */}
			<div className="bg-primary text-white p-6 rounded-t-xl">
				<div className="flex items-center gap-3">
					<div className="bg-white/20 rounded-full p-2">
						<svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
							<path
								fillRule="evenodd"
								d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
								clipRule="evenodd"
							/>
						</svg>
					</div>
					<div>
						<h2 className="text-xl font-bold">Update Progress</h2>
						<p className="text-white/80 text-sm">
							Add notes and attachments about the work being performed
						</p>
					</div>
				</div>
			</div>

			<div className="p-6 bg-gray-50">
				<div className="bg-white rounded-lg p-6 shadow-sm space-y-6">
					{/* Notes Section */}
					<div>
						<label
							htmlFor="progress-notes"
							className="block text-sm font-medium text-gray-700 mb-2"
						>
							Progress Notes
						</label>
						<Textarea
							id="progress-notes"
							value={notes}
							onChange={(e) => setNotes(e.target.value)}
							placeholder="Enter details about the work being performed..."
							rows={4}
							className="w-full"
						/>
					</div>

					{/* Attachments Section */}
					<div>
						<div className="flex items-center justify-between mb-4">
							<h4 className="text-md font-semibold text-gray-900">
								Attachments
							</h4>
						</div>

						{/* Image Upload */}
						<div className="mb-4">
							<ImageUpload
								label="Upload Photos"
								path={`warranty-requests/${request.id}/progress-photos`}
								description="Upload photos and other documents related to the work being performed"
								onImageUpload={(url) => handleAddAttachment(url, "image")}
								currentImageUrl=""
								options={{
									maxFileSize: 5 * 1024 * 1024, // 5MB
									maxDimensions: { width: 4000, height: 4000 }
								}}
							/>
						</div>

						{/* Attachments List */}
						{attachments.length > 0 && (
							<div className="mt-4 space-y-3">
								{attachments.map((attachment) => (
									<div
										key={attachment.id}
										className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
									>
										<div className="flex items-center gap-3">
											<div
												className={`p-2 rounded-full ${attachment.type === "image" ? "bg-blue-100" : "bg-purple-100"}`}
											>
												{attachment.type === "image" ? (
													<svg
														className="w-4 h-4 text-blue-600"
														fill="currentColor"
														viewBox="0 0 20 20"
													>
														<path
															fillRule="evenodd"
															d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
															clipRule="evenodd"
														/>
													</svg>
												) : (
													<svg
														className="w-4 h-4 text-purple-600"
														fill="currentColor"
														viewBox="0 0 20 20"
													>
														<path
															fillRule="evenodd"
															d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
															clipRule="evenodd"
														/>
													</svg>
												)}
											</div>
											<span className="text-sm text-gray-700">
												{attachment.title}
											</span>
										</div>
										<Button
											variant="ghost"
											size="sm"
											onClick={() => handleRemoveAttachment(attachment.id)}
											className="text-red-600 hover:text-red-700 hover:bg-red-50"
										>
											Remove
										</Button>
									</div>
								))}
							</div>
						)}
					</div>

					{error && (
						<div className="bg-red-50 border border-red-200 rounded-md p-3">
							<div className="text-red-600 text-sm">{error}</div>
						</div>
					)}
				</div>
			</div>
		</EditModal>
	);
}
