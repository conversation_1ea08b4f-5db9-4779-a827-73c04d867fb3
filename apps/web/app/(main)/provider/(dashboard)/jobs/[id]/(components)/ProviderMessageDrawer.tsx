"use client";

import MessageThread from "@/components/MessageThread";
import {
	Sheet,
	Sheet<PERSON>ontent,
	SheetHeader,
	SheetTitle
} from "@/components/ui/sheet";
import { ApiQuoteMessage } from "@/types/global";
import { JobWithQuoteAndMessages, QuoteWithMessages } from "../types";

type ProviderMessageDrawerProps = {
	open: boolean;
	onClose: () => void;
	job: JobWithQuoteAndMessages;
	quote: QuoteWithMessages;
	onMessageSent: (message: ApiQuoteMessage) => void;
	// Add caching props
	cachedMessages?: ApiQuoteMessage[];
	onMessagesUpdate?: (messages: ApiQuoteMessage[]) => void;
	isLoadingMessages?: boolean;
};

export default function ProviderMessageDrawer({
	open,
	onClose,
	job,
	quote,
	onMessageSent,
	cachedMessages,
	onMessagesUpdate,
	isLoadingMessages
}: ProviderMessageDrawerProps) {
	const handleMessagesUpdate = (messages: ApiQuoteMessage[]) => {
		if (onMessagesUpdate) {
			onMessagesUpdate(messages);
		}
	};

	return (
		<Sheet open={open} onOpenChange={onClose}>
			<SheetContent className="w-[90vw] sm:max-w-[700px] p-0">
				<SheetHeader className="p-4">
					<SheetTitle>
						Messages with {job.first_name} {job.last_name}
					</SheetTitle>
				</SheetHeader>
				{isLoadingMessages ? (
					<div className="flex-1 flex items-center justify-center p-8">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
					</div>
				) : (
					<MessageThread
						quoteId={quote.id}
						viewerRole="PROVIDER"
						providerId={quote.listing_id}
						onMessageSent={onMessageSent}
						cachedMessages={cachedMessages}
						onMessagesUpdate={handleMessagesUpdate}
						initiallyMarkAsRead={false} // We handle mark as read in the parent
					/>
				)}
			</SheetContent>
		</Sheet>
	);
}
