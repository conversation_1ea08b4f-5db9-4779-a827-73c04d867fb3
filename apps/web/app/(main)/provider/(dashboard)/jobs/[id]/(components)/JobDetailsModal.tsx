import { CauseCorrectionSection } from "@/components/oem/warranty-details/cause-correction-section";
import { CustomerInformationSection } from "@/components/oem/warranty-details/customer-information-section";
import { IssueDescriptionSection } from "@/components/oem/warranty-details/issue-description-section";
import { RVDetailsSection } from "@/components/oem/warranty-details/rv-details-section";
import { StatusHistorySection } from "@/components/oem/warranty-details/status-history-section";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { JobWithQuoteAndMessages, QuoteWithMessages } from "../types";

interface JobDetailsModalProps {
	job: JobWithQuoteAndMessages;
	quote: QuoteWithMessages;
	showDetailsModal: boolean;
	setShowDetailsModal: (show: boolean) => void;
}

export default function JobDetailsModal({
	job,
	quote,
	showDetailsModal,
	setShowDetailsModal
}: JobDetailsModalProps) {
	const hasTimelineUpdates =
		job.timeline_updates && job.timeline_updates.length > 0;

	return (
		<Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
			<DialogContent className="max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Service Request Details</DialogTitle>
				</DialogHeader>
				<Tabs defaultValue="details" className="w-full">
					<TabsList
						className={`grid w-full ${hasTimelineUpdates ? "grid-cols-2" : "grid-cols-1"}`}
					>
						<TabsTrigger value="details">Issue Details</TabsTrigger>
						{hasTimelineUpdates && (
							<TabsTrigger value="messages">Status History</TabsTrigger>
						)}
					</TabsList>
					<TabsContent value="details">
						<div className="grid grid-cols-1 gap-4 mb-4 text-gray-900">
							<CustomerInformationSection job={job} />
							<RVDetailsSection job={job} />
							{job.warranty_request && (
								<>
									<IssueDescriptionSection
										request={job.warranty_request}
										isProvider={true}
									/>
									<CauseCorrectionSection request={job.warranty_request} />
								</>
							)}
						</div>
					</TabsContent>
					{hasTimelineUpdates && (
						<TabsContent value="messages">
							<StatusHistorySection job={job as any} />
						</TabsContent>
					)}
				</Tabs>
			</DialogContent>
		</Dialog>
	);
}
