"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Company } from "@rvhelp/database";
import { useState } from "react";

interface OEMSupportCardProps {
	company: Company;
	representative: string;
	email: string;
}

export function OEMSupportCard({
	company,
	representative,
	email,
}: OEMSupportCardProps) {
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [selectedSupportType, setSelectedSupportType] = useState<"oem" | "rvhelp" | null>(null);

	if (!company) {
		return <Card className="mt-16"></Card>;
	}

	const handleOEMSupport = () => {
		setSelectedSupportType("oem");
		setIsDialogOpen(false);
	};

	const handleRVHelpSupport = () => {
		// Trigger Intercom popup
		if (typeof window !== "undefined" && window.Intercom) {
			window.Intercom("show");
		}
		setIsDialogOpen(false);
	};

	return (
		<>
			<Card className="mt-16">
				<CardHeader>
					<CardTitle>Get Support</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-sm text-gray-600 mb-4">
						Have questions? We're here to help!
					</p>
					<Button
						onClick={() => setIsDialogOpen(true)}
						className="w-full"
						style={{ backgroundColor: "#43806c" }}
					>
						Get Support
					</Button>
				</CardContent>
			</Card>

			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="sm:max-w-2xl">
					<DialogHeader>
						<DialogTitle>How can we help you?</DialogTitle>
					</DialogHeader>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
						{/* RV Help Support Card */}
						<Card
							className="cursor-pointer hover:shadow-md transition-shadow"
							onClick={handleRVHelpSupport}
						>
							<CardContent className="p-6">
								<div className="text-center space-y-3">
									<div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto">
										<svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
										</svg>
									</div>
									<h3 className="font-semibold text-lg">
										RV Help Support
									</h3>
									<p className="text-sm text-gray-600">
										For any questions reguarding payments, bugs in the app, or other general questions about RV Help's warranty process, click here.
									</p>
								</div>
							</CardContent>
						</Card>
						{/* OEM Support Card */}
						<Card
							className="cursor-pointer hover:shadow-md transition-shadow"
							onClick={handleOEMSupport}
						>
							<CardContent className="p-6">
								<div className="text-center space-y-3">
									<div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
										<svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
										</svg>
									</div>

									<h3 className="font-semibold text-lg">{company.name} Support</h3>
									<p className="text-sm text-gray-600">
										For any questions regarding parts ordering, warranty coverage, or pre-approval status, click here.
									</p>
								</div>
							</CardContent>
						</Card>


					</div>
				</DialogContent>
			</Dialog>

			{/* OEM Support Details Dialog */}
			<Dialog open={selectedSupportType === "oem"} onOpenChange={() => setSelectedSupportType(null)}>
				<DialogContent className="sm:max-w-lg">
					<DialogHeader>
						<DialogTitle>{`${company.name} Support`}</DialogTitle>
					</DialogHeader>
					<div className="space-y-4 mt-4">
						<p>{`To order parts or for manufacturer specific questions, please contact ${company.name} support:`}</p>
						<div className="space-y-3">
							<div>
								<span className="text-sm text-muted-foreground">Phone:</span>
								<div className="text-md">{company.support_phone}</div>
							</div>
							<div className="space-y-1">
								<span className="text-sm text-muted-foreground">
									Representative:
								</span>
								<div className="text-md">{representative}</div>
							</div>
							<div className="space-y-1">
								<span className="text-sm text-muted-foreground">Email:</span>
								<div className="text-md">{email ?? company.support_email}</div>
							</div>
							<div className="border-b border-gray-200" />
							<div className="space-y-1">
								<span className="text-md italic">
									Any questions regarding payment, please direct <NAME_EMAIL>.
								</span>
							</div>
						</div>
					</div>
				</DialogContent>
			</Dialog>
		</>
	);
}
