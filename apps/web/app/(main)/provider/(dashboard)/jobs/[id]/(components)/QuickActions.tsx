import { ProviderWarrantyQuickActions } from "@/components/oem/provider-warranty-quickactions";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { WarrantyAttachment } from "@/types/warranty";
import {
	QuoteStatus,
	StripeConnection,
	WarrantyRequestStatus
} from "@rvhelp/database";
import { CheckCircle, MessageSquare, X, XCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useMemo } from "react";
import { JobWithQuoteAndMessages } from "../types";

interface QuickActionsProps {
	status: QuoteStatus;
	onAccept: () => void;
	onDecline: () => void;
	onMessage: () => void;
	onCompleteJob?: () => void;
	onWithdraw?: () => void;
	onWarrantyUpdate?: () => void;
	onRequestAuthorization?: () => void;
	onRequestPayment?: () => void;
	onDocumentUpdate?: () => void;
	onConfigureWarrantyPricing?: () => void;
	job: JobWithQuoteAndMessages;
	mobile?: boolean;
	stripeConnect?: StripeConnection;
}

export function QuickActions({
	status,
	onAccept,
	onDecline,
	onMessage,
	onCompleteJob,
	onWithdraw,
	onWarrantyUpdate,
	onRequestAuthorization,
	onRequestPayment,
	onDocumentUpdate,
	onConfigureWarrantyPricing,
	job,
	stripeConnect,
	mobile = false
}: QuickActionsProps) {
	const router = useRouter();

	// Find the current quote with the given status
	const currentQuote = job.quotes?.find((q) => q.status === status);

	const requiredAttachments = useMemo(() => {
		const attachments =
			(job.warranty_request?.attachments as WarrantyAttachment[]) || [];
		return attachments.filter((attachment) => attachment.required);
	}, [job.warranty_request?.attachments]);

	// Don't show actions if job is completed
	if (status === QuoteStatus.ACCEPTED && currentQuote?.completed_at)
		return null;

	// For mobile, return just the buttons without the card wrapper
	if (mobile) {
		return (
			<div className="space-y-2">
				{/* Job Actions - Only show for accepted quotes */}
				{(status === QuoteStatus.ACCEPTED ||
					status === QuoteStatus.IN_PROGRESS) && (
					<>
						{!currentQuote?.completed_at &&
							job.warranty_request?.status ===
								WarrantyRequestStatus.INVOICE_PAID && (
								<Button
									className="w-full bg-primary hover:bg-emerald-700"
									onClick={onCompleteJob}
								>
									<CheckCircle className="mr-2 h-4 w-4" />
									Mark Complete
								</Button>
							)}

						<Button
							variant="outline"
							className="w-full py-3 text-base"
							onClick={onMessage}
						>
							<MessageSquare className="mr-2 h-4 w-4" />
							Send Message
						</Button>
					</>
				)}

				{/* Response Actions - Only show for pending quotes */}
				{status === QuoteStatus.PENDING && (
					<div className="space-y-2">
						<Button
							className="w-full bg-emerald-600 hover:bg-emerald-700 py-3 text-base"
							onClick={onAccept}
						>
							<CheckCircle className="mr-2 h-4 w-4" />
							Accept Lead
						</Button>
						<Button
							variant="outline"
							className="w-full py-3 text-base"
							onClick={onDecline}
						>
							<X className="mr-2 h-4 w-4" />
							Not Available
						</Button>
					</div>
				)}

				{/* Withdraw option for quoted jobs */}
				{status === QuoteStatus.ACCEPTED && (
					<Button
						variant="outline"
						className="w-full py-3 text-base"
						onClick={onWithdraw}
					>
						<XCircle className="mr-2 h-4 w-4" />
						Withdraw
					</Button>
				)}

				{job.warranty_request && (
					<>
						<div className="border-t border-gray-200 my-4"></div>
						<ProviderWarrantyQuickActions
							warrantyRequest={job.warranty_request}
							onRequestAuthorization={onRequestAuthorization}
							onRequestPayment={onRequestPayment}
							onConfigureWarrantyPricing={onConfigureWarrantyPricing}
						/>
					</>
				)}
			</div>
		);
	}

	return (
		<>
			<Card>
				<CardHeader>
					<CardTitle className="text-xl font-semibold flex items-center">
						{/* <Wrench className="mr-2 h-5 w-5 text-gray-500" /> */}
						Quick Actions
					</CardTitle>
				</CardHeader>
				<CardContent className="px-6 pb-6 -mt-2">
					<div className="space-y-2">
						{/* Job Actions - Only show for accepted quotes */}
						{status === QuoteStatus.ACCEPTED ||
							(status == QuoteStatus.IN_PROGRESS && (
								<>
									{!currentQuote?.completed_at &&
										job.warranty_request?.status ===
											WarrantyRequestStatus.INVOICE_PAID && (
											<Button
												className="w-full bg-primary hover:bg-green-700"
												onClick={onCompleteJob}
											>
												<CheckCircle className="mr-2 h-4 w-4" />
												Mark Complete
											</Button>
										)}
									<Button
										variant="outline"
										className="w-full"
										onClick={onMessage}
									>
										<MessageSquare className="mr-2 h-4 w-4" />
										Send Message
									</Button>
								</>
							))}
						{status === QuoteStatus.ACCEPTED && (
							<Button variant="outline" className="w-full" onClick={onWithdraw}>
								<XCircle className="mr-2 h-4 w-4" />
								Withdraw
							</Button>
						)}

						{/* Response Actions - Only show for pending quotes */}
						{status === QuoteStatus.PENDING && (
							<div className="space-y-2">
								<Button
									className="w-full bg-primary hover:bg-emerald-700"
									onClick={onAccept}
								>
									<CheckCircle className="mr-2 h-4 w-4" />
									Accept Lead
								</Button>
								<Button
									variant="outline"
									className="w-full"
									onClick={onDecline}
								>
									<X className="mr-2 h-4 w-4" />
									Not Available
								</Button>
							</div>
						)}

						{/* WARRANTY ONLY - Update warranty status */}
						{job.warranty_request && (
							<>
								<ProviderWarrantyQuickActions
									warrantyRequest={job.warranty_request}
									onRequestAuthorization={onRequestAuthorization}
									onRequestPayment={onRequestPayment}
									onConfigureWarrantyPricing={onConfigureWarrantyPricing}
								/>
							</>
						)}
					</div>
				</CardContent>
			</Card>
		</>
	);
}
