import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { QuoteStatus } from "@rvhelp/database";
import { formatDistanceToNow } from "date-fns";
import {
	Clock,
	FileText,
	Mail,
	MapPin,
	MessageSquare,
	Phone
} from "lucide-react";
import { JobWithQuoteAndMessages } from "../types";

interface ServiceRequestHeaderProps {
	job: JobWithQuoteAndMessages;
	quote: any;
	onMessageClick: () => void;
	onViewDetailsClick: () => void;
	onCallClick: () => void;
}

export function ServiceRequestHeader({
	job,
	quote,
	onViewDetailsClick,
	onMessageClick,
	onCallClick
}: ServiceRequestHeaderProps) {
	return (
		<Card className="mb-6 shadow-sm">
			<CardHeader>
				<div className="space-y-4">
					<div className="flex flex-col lg:flex-row gap-6 lg:items-start">
						{/* Main content column */}
						<div className="flex-1 min-w-0">
							<div className="flex items-center gap-3 mb-4">
								<Avatar className="h-12 w-12 border-2 border-emerald-100">
									<AvatarFallback className="bg-emerald-100 text-emerald-700">
										{job.first_name?.[0]}
										{job.last_name?.[0]}
									</AvatarFallback>
								</Avatar>
								<div>
									<h1 className="text-2xl font-bold text-gray-900">
										{job.first_name} {job.last_name}
									</h1>
									<div className="flex items-center gap-2 text-sm text-gray-500">
										<Clock className="h-4 w-4" />
										<span>
											Requested{" "}
											{formatDistanceToNow(new Date(quote.created_at), {
												addSuffix: true
											})}
										</span>
										{quote && getStatusBadge(quote.status)}
									</div>
								</div>
							</div>

							{/* Service Request Details */}
							<div className="space-y-3">
								{job.location && (
									<div className="flex items-center">
										<MapPin className="h-4 w-4 mr-1.5 text-gray-500" />
										<span className="text-gray-600">
											{job.location.address}
										</span>
									</div>
								)}

								{/* Request message preview */}
								<div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
									<div className="flex items-start justify-between">
										<div className="flex-1">
											<h4 className="font-medium text-gray-900 text-sm mb-2">
												Service Request Details
											</h4>
											<div className="line-clamp-3 text-ellipsis overflow-hidden">
												<span className="text-gray-700 whitespace-pre-line text-sm">
													{job.message}
												</span>
											</div>
										</div>
										<Button
											variant="outline"
											size="sm"
											onClick={onViewDetailsClick}
											className="ml-4 flex-shrink-0"
										>
											<FileText className="h-4 w-4 mr-2" />
											View Details
										</Button>
									</div>
								</div>
							</div>
						</div>

						{/* Customer Card in header for lg+ screens ONLY */}
						<div className="hidden lg:block mt-6 lg:mt-0 lg:ml-8 lg:w-[340px] flex-shrink-0">
							<Card className="shadow-lg">
								<CardHeader>
									<CardTitle>Customer</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="flex items-start gap-3">
										<Avatar className="h-12 w-12">
											<AvatarFallback className="bg-primary text-white">
												{job.first_name?.[0] || "C"}
												{job.last_name?.[0] || ""}
											</AvatarFallback>
										</Avatar>

										<div className="flex-1">
											<h4 className="font-semibold">
												{job.first_name} {job.last_name}
											</h4>

											<div className="flex items-center gap-1 mt-1 text-sm text-gray-600">
												<Mail className="h-3 w-3" />
												<span>{job.email}</span>
											</div>

											<div className="flex items-center gap-1 mt-1 text-sm text-gray-600">
												<Phone className="h-3 w-3" />
												<span>{job.phone}</span>
											</div>
										</div>
									</div>

									<div className="grid grid-cols-2 gap-2 pt-4 border-t">
										<Button
											variant="outline"
											size="sm"
											className="border-2"
											onClick={onCallClick}
										>
											<Phone className="h-4 w-4 mr-2" />
											Call
										</Button>
										<Button
											variant="outline"
											size="sm"
											className="border-2"
											onClick={onMessageClick}
										>
											<MessageSquare className="h-4 w-4 mr-2" />
											Message
										</Button>
									</div>
								</CardContent>
							</Card>
						</div>
					</div>
				</div>
			</CardHeader>
		</Card>
	);
}

function getStatusBadge(status: string) {
	switch (status) {
		case QuoteStatus.PENDING:
			return (
				<Badge className="bg-amber-100 py-2 px-4 text-amber-800 hover:bg-amber-100">
					Invited
				</Badge>
			);
		case QuoteStatus.ACCEPTED:
			return (
				<Badge className="bg-green-100 py-2 px-4 text-green-800 hover:bg-green-100">
					Accepted
				</Badge>
			);
		case QuoteStatus.REJECTED:
			return (
				<Badge className="bg-red-100 py-2 px-4 text-red-800 hover:bg-red-100">
					Rejected
				</Badge>
			);
		case QuoteStatus.WITHDRAWN:
			return (
				<Badge className="bg-gray-100 py-2 px-4 text-gray-800 hover:bg-gray-100">
					Withdrawn
				</Badge>
			);
		case QuoteStatus.EXPIRED:
			return (
				<Badge className="bg-gray-100 py-2 px-4 text-gray-800 hover:bg-gray-100">
					Expired
				</Badge>
			);
		default:
			return (
				<Badge className="bg-gray-100 py-2 px-4 text-gray-800 hover:bg-gray-100">
					{status}
				</Badge>
			);
	}
}
