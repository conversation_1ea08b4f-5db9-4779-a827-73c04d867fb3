"use client";

import {
	<PERSON>,
	CardContent,
	CardDescription,
	<PERSON><PERSON><PERSON><PERSON>,
	CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { getCategoryName } from "@/lib/categories";
import { Job, Quote, QuoteStatus, WarrantyRequest } from "@rvhelp/database";

type QuoteWithJob = Quote & {
	job: Job & {
		user: {
			first_name: string;
			last_name: string;
			email: string;
			phone: string;
		};
		warranty_request: WarrantyRequest;
	};
	messages: Array<{
		id: string;
		content: string;
		created_at: Date;
	}>;
};

import { getQuoteStatusBadge } from "@/lib/utils/quote";
import { format } from "date-fns";
import { CheckCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

export default function ProviderJobsPage() {
	const [jobs, setJobs] = useState<QuoteWithJob[]>([]);
	const [loading, setLoading] = useState(true);
	const [activeTab, setActiveTab] = useState<string>("all");
	const [filteredJobs, setFilteredJobs] = useState<QuoteWithJob[]>([]);
	const router = useRouter();

	useEffect(() => {
		const fetchJobs = async () => {
			try {
				const response = await fetch(`/api/provider/jobs?status=${activeTab}`);
				if (!response.ok) throw new Error("Failed to fetch jobs");
				const data = await response.json();

				setJobs(data || []);
				setFilteredJobs(data || []);
			} catch (error) {
				console.error("Error fetching jobs:", error);
				toast.error("Failed to load jobs");
			} finally {
				setLoading(false);
			}
		};

		fetchJobs();
	}, [activeTab]);

	const getLocationDisplay = (location: any) => {
		if (!location) return "Location not specified";
		try {
			const parsedLocation =
				typeof location === "string" ? JSON.parse(location) : location;
			if (parsedLocation.city && parsedLocation.state) {
				return `${parsedLocation.city}, ${parsedLocation.state}`;
			}
			if (parsedLocation.address) {
				const addressParts = parsedLocation.address.split(", ");
				if (addressParts.length >= 2) {
					const cityState = addressParts.slice(-2)[0].split(" ")[0];
					const state = addressParts.slice(-2)[1].split(" ")[0];
					return `${cityState}, ${state}`;
				}
				return parsedLocation.address;
			}
			return "Location not specified";
		} catch (e) {
			return "Location not specified";
		}
	};

	// Calculate counts for each status
	const getStatusCount = (status: string) => {
		return jobs.filter((job) => job.status === status).length;
	};

	if (loading) {
		return (
			<div className="container mx-auto">
				<h1 className="text-2xl font-bold mb-6">Jobs</h1>

				{/* Skeleton for tabs */}
				<div className="space-y-4">
					<div className="h-10 flex gap-2">
						{[1, 2, 3, 4].map((i) => (
							<div
								key={i}
								className="h-10 w-20 bg-gray-200 rounded animate-pulse"
							/>
						))}
					</div>

					{/* Skeleton for cards */}
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
						{[1, 2, 3, 4, 5, 6].map((i) => (
							<Card key={i} className="animate-pulse">
								<CardHeader className="pb-2">
									<div className="flex justify-between items-start">
										<div className="h-5 w-32 bg-gray-200 rounded" />
										<div className="h-5 w-16 bg-gray-200 rounded" />
									</div>
								</CardHeader>
								<CardContent className="space-y-2">
									<div className="h-4 w-full bg-gray-200 rounded" />
									<div className="h-4 w-3/4 bg-gray-200 rounded" />
									<div className="h-4 w-full bg-gray-200 rounded" />
									<div className="h-4 w-2/3 bg-gray-200 rounded" />
									<div className="h-16 w-full bg-gray-200 rounded" />
									<div className="h-3 w-1/2 bg-gray-200 rounded" />
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</div>
		);
	}

	const getEmptyStateMessage = () => {
		switch (activeTab) {
			case "ACCEPTED":
				return "You don't have any accepted jobs at the moment.";
			case "COMPLETED":
				return "You haven't completed any jobs yet.";
			case "IN_PROGRESS":
				return "You don't have any jobs in progress.";
			default:
				return "You don't have any active jobs yet.";
		}
	};

	return (
		<div className="container mx-auto">
			<h1 className="text-2xl font-bold mb-6">Jobs</h1>

			<Tabs
				value={activeTab}
				onValueChange={(value: string) => setActiveTab(value)}
				className="space-y-4"
			>
				<TabsList className="h-auto flex flex-wrap justify-start">
					<TabsTrigger value="all" className="relative mb-2 md:mb-0">
						All Jobs
						<span className="ml-2 rounded-full bg-primary/20 text-primary px-2 py-0.5 text-xs font-medium">
							{jobs.length}
						</span>
					</TabsTrigger>

					<TabsTrigger value="QUOTED" className="relative mb-2 md:mb-0">
						Responded
						<span className="ml-2 rounded-full bg-primary/20 text-primary px-2 py-0.5 text-xs font-medium">
							{getStatusCount("QUOTED")}
						</span>
					</TabsTrigger>
					<TabsTrigger value="ACCEPTED" className="relative mb-2 md:mb-0">
						Accepted
						<span className="ml-2 rounded-full bg-primary/20 text-primary px-2 py-0.5 text-xs font-medium">
							{getStatusCount("ACCEPTED")}
						</span>
					</TabsTrigger>
					<TabsTrigger value="COMPLETED" className="relative mb-2 md:mb-0">
						Completed
						<span className="ml-2 rounded-full bg-primary/20 text-primary px-2 py-0.5 text-xs font-medium">
							{getStatusCount("COMPLETED")}
						</span>
					</TabsTrigger>
				</TabsList>

				<TabsContent value={activeTab} className="space-y-4">
					{filteredJobs.length === 0 ? (
						<Card>
							<CardContent className="py-12">
								<div className="text-center space-y-3">
									<CheckCircle className="mx-auto h-12 w-12 text-gray-400" />
									<CardTitle className="text-xl">No jobs found</CardTitle>
									<CardDescription className="max-w-sm mx-auto">
										{getEmptyStateMessage()}
									</CardDescription>
								</div>
							</CardContent>
						</Card>
					) : (
						<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
							{filteredJobs.map((job) => (
								<Card
									key={job.id}
									className="hover:shadow-md cursor-pointer transition-shadow"
									onClick={() => router.push(`/provider/jobs/${job.id}`)}
								>
									<CardHeader className="pb-2">
										<div className="flex justify-between items-start">
											<CardTitle className="text-lg">
												{job.job.user.first_name || "N/A"}
												{job.job.user.last_name && ` ${job.job.user.last_name}`}
											</CardTitle>
											{getQuoteStatusBadge(job.job.status as QuoteStatus)}
										</div>
									</CardHeader>
									<CardContent className="space-y-2">
										<div className="text-sm">
											<span className="font-medium">Category:</span>{" "}
											{getCategoryName(job.job.category)}
										</div>
										<div className="text-sm">
											<span className="font-medium">Contact:</span>{" "}
											{job.job.user.email || "N/A"}
											{job.job.user.phone && ` / ${job.job.user.phone}`}
										</div>
										<div className="text-sm">
											<span className="font-medium">Location:</span>{" "}
											{getLocationDisplay(job.job.location)}
										</div>
										<div className="text-sm">
											<span className="font-medium">Job Description:</span>{" "}
											<span className="line-clamp-2">
												{job.job.warranty_request?.complaint ?? job.job.message}
											</span>
										</div>
										<div className="text-xs text-muted-foreground">
											{format(new Date(job.created_at), "PPp")}
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					)}
				</TabsContent>
			</Tabs>
		</div>
	);
}
