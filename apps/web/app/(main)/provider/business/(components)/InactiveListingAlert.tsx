"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertTriangle, CheckCircle } from "lucide-react";
import { useState } from "react";
import toast from "react-hot-toast";
import { useEditListing } from "./context/EditListingContext";

export function InactiveListingAlert() {
	const { state, updateSettings } = useEditListing();
	const [isActivating, setIsActivating] = useState(false);

	// Check if listing is inactive
	if (state.formData.is_active !== false) {
		return null;
	}

	// Check eligibility for inspection or repair categories
	const isEligibleForInspection =
		(state.formData.nrvia_inspector_level || 0) > 0;
	const isEligibleForRepair = (state.formData.rvtaa_technician_level || 0) > 0;
	const isEligible = isEligibleForInspection || isEligibleForRepair;

	const handleActivate = async () => {
		if (!isEligible) {
			toast.error(
				"Your listing is not eligible for activation. Please contact support for assistance."
			);
			return;
		}

		setIsActivating(true);
		try {
			const success = await updateSettings({ is_active: true });
			if (success) {
				toast.success("Your listing has been activated successfully!");
			} else {
				toast.error("Failed to activate listing. Please try again.");
			}
		} catch (error) {
			console.error("Error activating listing:", error);
			toast.error("An error occurred while activating your listing.");
		} finally {
			setIsActivating(false);
		}
	};

	return (
		<Alert className="mb-6 border-orange-200 bg-orange-50">
			<AlertTriangle className="h-4 w-4 text-orange-600" />
			<AlertDescription className="text-orange-800">
				<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
					<div>
						<strong>Your listing is currently inactive</strong>
						<p className="text-sm text-orange-700 mt-1">
							{isEligible
								? "Your listing is not visible to potential customers. Click the button below to activate it."
								: "Your listing is not eligible for activation because you don't meet the requirements for inspection or repair categories. Please contact support for assistance."}
						</p>
					</div>
					{isEligible && (
						<Button
							onClick={handleActivate}
							disabled={isActivating}
							className="bg-orange-600 hover:bg-orange-700 text-white flex items-center gap-2"
						>
							{isActivating ? (
								<>
									<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
									Activating...
								</>
							) : (
								<>
									<CheckCircle className="h-4 w-4" />
									Activate Listing
								</>
							)}
						</Button>
					)}
				</div>
			</AlertDescription>
		</Alert>
	);
}
