"use client";

import ConversationsPage from "@/components/conversations/ConversationsPage";
import { CenteredPageLoader } from "@/components/Loader";
import { useAuth } from "@/lib/hooks/useAuth";

export default function UserConversationsPage() {
	const { user, loading } = useAuth();

	// Show loading state while authentication is being determined
	if (loading) {
		return <CenteredPageLoader />;
	}

	// If user is not authenticated, the ConversationsPage will handle the redirect
	if (!user) {
		return <ConversationsPage viewerRole="USER" />;
	}

	// Determine viewerRole based on user's role
	const viewerRole = user.role === "PROVIDER" ? "PROVIDER" : "USER";

	return <ConversationsPage viewerRole={viewerRole} />;
}
