'use client';

import VirtualDiagnosticModal from "@/components/modals/VirtualDiagnosticModal";
import { Button } from "@/components/ui/button";
import { CheckCircle, Clock, Shield, Users, VideoIcon } from "lucide-react";
import { useEffect, useState } from "react";

export default function VirtualDiagnosticClient() {
    const [modalOpen, setModalOpen] = useState(false);

    // Auto-open modal after a short delay
    useEffect(() => {
        const timer = setTimeout(() => {
            setModalOpen(true);
        }, 500);

        return () => clearTimeout(timer);
    }, []);

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="max-w-6xl mx-auto px-4 py-12">
                {/* Hero Section */}
                <div className="text-center mb-16">
                    <h1 className="text-4xl font-bold text-gray-900 mb-6">
                        Virtual RV Diagnostic Service
                    </h1>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
                        Get expert diagnosis and troubleshooting from certified RV technicians via video call.
                        No need to visit a shop - we'll guide you through the solution step by step.
                    </p>
                    <Button
                        onClick={() => setModalOpen(true)}
                        size="lg"
                        className="bg-[#42806c] hover:bg-[#356556] text-white px-8 py-4 text-lg"
                    >
                        <VideoIcon className="w-5 h-5 mr-2" />
                        Book Virtual Diagnostic
                    </Button>
                </div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                    <div className="bg-white rounded-xl p-6 border border-gray-200 text-center">
                        <div className="w-12 h-12 bg-[#42806c]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <VideoIcon className="w-6 h-6 text-[#42806c]" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Video Diagnosis</h3>
                        <p className="text-sm text-gray-600">Real-time troubleshooting via video call with mobile access to your RV</p>
                    </div>

                    <div className="bg-white rounded-xl p-6 border border-gray-200 text-center">
                        <div className="w-12 h-12 bg-[#42806c]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <Users className="w-6 h-6 text-[#42806c]" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Certified Techs</h3>
                        <p className="text-sm text-gray-600">Work with experienced, certified RV technicians</p>
                    </div>

                    <div className="bg-white rounded-xl p-6 border border-gray-200 text-center">
                        <div className="w-12 h-12 bg-[#42806c]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <Clock className="w-6 h-6 text-[#42806c]" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Save Time</h3>
                        <p className="text-sm text-gray-600">No need to travel to a service center or wait for appointments</p>
                    </div>

                    <div className="bg-white rounded-xl p-6 border border-gray-200 text-center">
                        <div className="w-12 h-12 bg-[#42806c]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <Shield className="w-6 h-6 text-[#42806c]" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Expert Guidance</h3>
                        <p className="text-sm text-gray-600">Step-by-step guidance to fix issues safely and correctly</p>
                    </div>
                </div>

                {/* How It Works */}
                <div className="bg-white rounded-2xl p-8 border border-gray-200 mb-16">
                    <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">How It Works</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#42806c] text-white rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                                1
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-4">Book Your Session</h3>
                            <p className="text-gray-600">
                                Fill out our detailed form describing your RV issue and upload any relevant photos or videos.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#42806c] text-white rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                                2
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-4">Get Matched</h3>
                            <p className="text-gray-600">
                                We'll match you with the best technician for your specific issue and send you a Zoom link.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#42806c] text-white rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                                3
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-4">Fix Together</h3>
                            <p className="text-gray-600">
                                Join your video call and work with our tech to diagnose and solve your RV problem.
                            </p>
                        </div>
                    </div>
                </div>

                {/* Benefits */}
                <div className="bg-gradient-to-r from-[#42806c] to-[#356556] rounded-2xl p-8 text-white mb-16">
                    <h2 className="text-3xl font-bold text-center mb-12">Why Choose Virtual Diagnostics?</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="flex items-start gap-4">
                            <CheckCircle className="w-6 h-6 text-green-300 flex-shrink-0 mt-1" />
                            <div>
                                <h3 className="font-semibold mb-2">Save Money</h3>
                                <p className="text-gray-100">Avoid expensive diagnostic fees and potentially unnecessary service visits</p>
                            </div>
                        </div>

                        <div className="flex items-start gap-4">
                            <CheckCircle className="w-6 h-6 text-green-300 flex-shrink-0 mt-1" />
                            <div>
                                <h3 className="font-semibold mb-2">Learn as You Go</h3>
                                <p className="text-gray-100">Understand your RV better by working through problems with an expert</p>
                            </div>
                        </div>

                        <div className="flex items-start gap-4">
                            <CheckCircle className="w-6 h-6 text-green-300 flex-shrink-0 mt-1" />
                            <div>
                                <h3 className="font-semibold mb-2">Convenient</h3>
                                <p className="text-gray-100">No need to tow your RV or find transportation to a service center</p>
                            </div>
                        </div>

                        <div className="flex items-start gap-4">
                            <CheckCircle className="w-6 h-6 text-green-300 flex-shrink-0 mt-1" />
                            <div>
                                <h3 className="font-semibold mb-2">Quick Resolution</h3>
                                <p className="text-gray-100">Many issues can be resolved in a single 30-60 minute session</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* CTA Section */}
                <div className="text-center">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Ready to Get Your RV Fixed?</h2>
                    <p className="text-xl text-gray-600 mb-8">
                        Book your virtual diagnostic session today and get back on the road faster.
                    </p>
                    <Button
                        onClick={() => setModalOpen(true)}
                        size="lg"
                        className="bg-[#fea72a] hover:bg-[#e8941f] text-white px-8 py-4 text-lg"
                    >
                        <VideoIcon className="w-5 h-5 mr-2" />
                        Book Your Session Now
                    </Button>
                </div>
            </div>

            {/* Virtual Diagnostic Modal */}
            <VirtualDiagnosticModal
                open={modalOpen}
                onOpenChange={setModalOpen}
            />
        </div>
    );
} 