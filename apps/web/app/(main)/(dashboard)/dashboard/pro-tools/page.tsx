"use client";

import { EmergencyDispatchModal } from "@/components/modals/EmergencyDispatchModal";
import { SupportModal } from "@/components/modals/SupportModal";
import UpgradeModal from "@/components/modals/UpgradeModal";
import VirtualDiagnosticModal from "@/components/modals/VirtualDiagnosticModal";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/hooks/useAuth";
import { withAuthorization } from "@/lib/hooks/withAuthorization";
import {
    ChevronRight,
    Crown,
    Lock,
    Percent,
    Phone,
    Search,
    Shield,
    Users,
    Video,
    Wrench,
    X,
    Zap
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

const ProToolsPage = () => {
    const { isPaid, isStandard, isPremium, user } = useAuth();
    const [supportOpen, setSupportOpen] = useState(false);
    const [virtualDiagnosticOpen, setVirtualDiagnosticOpen] = useState(false);
    const [showUpgradeModal, setShowUpgradeModal] = useState(false);
    const [emergencyDispatchOpen, setEmergencyDispatchOpen] = useState(false);
    const [discountsModalOpen, setDiscountsModalOpen] = useState(false);

    if (!isPaid) {
        return (
            <div className="min-h-screen bg-gray-50/50">
                <div className="container py-12">
                    <div className="max-w-4xl mx-auto text-center">
                        <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
                            <div className="mb-6">
                                <Crown className="w-16 h-16 text-amber-500 mx-auto mb-4" />
                                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                                    Pro Member Benefits
                                </h1>
                                <p className="text-lg text-gray-600 mb-8">
                                    This page is exclusively for RV Help Pro members. Upgrade to access all our premium tools and services.
                                </p>
                            </div>
                            <Link href="/pro-membership">
                                <Button className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-8 py-3 text-lg font-semibold">
                                    Upgrade to Pro
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    const memberBenefits = [
        {
            title: "Talk to a Tech",
            description: "Connect with local techs who offer 10-minute troubleshooting calls to Pro Members.",
            icon: Search,
            href: "/dashboard/virtual-troubleshooting",
            color: "#42806c",
            bgColor: "#e8f4f1",
            featured: true
        },
        {
            title: "Concierge Support",
            description: "Need help finding a tech or inspector? Our support team is here to assist you.",
            icon: Users,
            onClick: () => setSupportOpen(true),
            color: "#42806c",
            bgColor: "#e8f4f1",
            featured: true
        },
        {
            title: "Virtual Diagnostic Call",
            description: "Schedule a video call with our certified RV Help technicians for instant help.",
            icon: Video,
            onClick: () => isStandard ? setShowUpgradeModal(true) : setVirtualDiagnosticOpen(true),
            color: "#fea72a",
            bgColor: "#fef7e6",
            premium: true,
            featured: true
        },
        {
            title: "Emergency Dispatch",
            description: "Critical RV system failure? Broadcast urgent alerts to all available techs in your area.",
            icon: Zap,
            onClick: () => setEmergencyDispatchOpen(true),
            color: "#dc2626",
            bgColor: "#fef2f2",
            featured: true
        },
        {
            title: "Find RV Technician",
            description: "Get matched with qualified technicians in your area.",
            icon: Wrench,
            href: "/mobile-rv-repair",
            color: "#42806c",
            bgColor: "#e8f4f1"
        },
        {
            title: "Find RV Inspector",
            description: "Get your RV professionally inspected by certified inspectors.",
            icon: Shield,
            href: "/rv-inspection",
            color: "#42806c",
            bgColor: "#e8f4f1"
        },
        {
            title: "Priority Support",
            description: "Get faster response times and dedicated support for all your RV needs.",
            icon: Phone,
            onClick: () => setSupportOpen(true),
            color: "#42806c",
            bgColor: "#e8f4f1"
        },
        {
            title: "Member Discounts",
            description: "Learn how to access exclusive discounts from Pro-friendly providers.",
            icon: Percent,
            onClick: () => setDiscountsModalOpen(true),
            color: "#fea72a",
            bgColor: "#fef7e6"
        }
    ];

    const featuredBenefits = memberBenefits.filter(benefit => benefit.featured);
    const otherBenefits = memberBenefits.filter(benefit => !benefit.featured);

    return (
        <div className="min-h-screen bg-gray-50/50">
            <div className="container py-12">
                <div className="max-w-7xl mx-auto space-y-8">
                    {/* Header */}
                    <div className="text-center">
                        <div className="inline-flex items-center gap-2 bg-gradient-to-r from-amber-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold mb-4">
                            <Crown className="w-4 h-4" />
                            Pro Member
                        </div>
                        <h1 className="text-4xl font-bold text-gray-900 mb-4">
                            Your Pro Tools & Benefits
                        </h1>
                        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                            Welcome back, {user?.first_name}! Here are all your exclusive Pro member benefits and tools.
                        </p>
                    </div>

                    {/* Featured Benefits */}
                    <div>
                        <h2 className="text-2xl font-semibold text-gray-900 mb-6">Quick Actions</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {featuredBenefits.map((benefit, index) => (
                                <div
                                    key={index}
                                    className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200 cursor-pointer"
                                    onClick={benefit.onClick}
                                >
                                    <div className="flex items-center gap-4 mb-4">
                                        <div
                                            className="w-12 h-12 rounded-xl flex items-center justify-center"
                                            style={{ background: benefit.bgColor }}
                                        >
                                            <benefit.icon
                                                className="w-6 h-6"
                                                style={{ color: benefit.color }}
                                            />
                                        </div>
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2">
                                                <h3 className="text-lg font-semibold text-gray-900">
                                                    {benefit.title}
                                                </h3>
                                                {benefit.premium && (
                                                    <span className="bg-amber-100 text-amber-800 px-2 py-0.5 rounded text-xs font-medium">
                                                        Premium
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                    <p className="text-gray-600 text-sm leading-relaxed mb-4">
                                        {benefit.description}
                                    </p>
                                    {benefit.href ? (
                                        <Link href={benefit.href}>
                                            <Button
                                                className="w-full"
                                                style={{ background: benefit.color }}
                                            >
                                                Get Started
                                                <ChevronRight className="w-4 h-4 ml-2" />
                                            </Button>
                                        </Link>
                                    ) : (
                                        <Button
                                            onClick={benefit.onClick}
                                            className="w-full"
                                            style={{ background: benefit.color }}
                                        >
                                            {benefit.premium && isStandard ? (
                                                <>
                                                    <Lock className="w-4 h-4 mr-2" />
                                                    Upgrade to Premium
                                                </>
                                            ) : (
                                                <>
                                                    Get Started
                                                    <ChevronRight className="w-4 h-4 ml-2" />
                                                </>
                                            )}
                                        </Button>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Other Benefits */}
                    <div>
                        <h2 className="text-2xl font-semibold text-gray-900 mb-6">All Member Benefits</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {otherBenefits.map((benefit, index) => (
                                <div
                                    key={index}
                                    className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200 cursor-pointer"
                                    onClick={benefit.onClick}
                                >
                                    <div className="flex items-center gap-4 mb-4">
                                        <div
                                            className="w-10 h-10 rounded-lg flex items-center justify-center"
                                            style={{ background: benefit.bgColor }}
                                        >
                                            <benefit.icon
                                                className="w-5 h-5"
                                                style={{ color: benefit.color }}
                                            />
                                        </div>
                                        <div className="flex-1">
                                            <h3 className="text-base font-semibold text-gray-900">
                                                {benefit.title}
                                            </h3>
                                        </div>
                                    </div>
                                    <p className="text-gray-600 text-sm leading-relaxed mb-4">
                                        {benefit.description}
                                    </p>
                                    {benefit.href ? (
                                        <Link href={benefit.href}>
                                            <Button
                                                variant="outline"
                                                className="w-full"
                                                style={{ borderColor: benefit.color, color: benefit.color }}
                                            >
                                                Learn More
                                                <ChevronRight className="w-4 h-4 ml-2" />
                                            </Button>
                                        </Link>
                                    ) : (
                                        <Button
                                            onClick={benefit.onClick}
                                            variant="outline"
                                            className="w-full"
                                            style={{ borderColor: benefit.color, color: benefit.color }}
                                        >
                                            Access
                                            <ChevronRight className="w-4 h-4 ml-2" />
                                        </Button>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Membership Status */}
                    <div className="bg-gradient-to-r from-amber-500 to-orange-500 rounded-2xl p-8 text-white">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-2xl font-bold mb-2">
                                    {isPremium ? "Premium Member" : "Standard Pro Member"}
                                </h3>
                                <p className="text-amber-100">
                                    {isPremium
                                        ? "You have access to all Pro features including Virtual Diagnostic Calls."
                                        : "Upgrade to Premium for access to Virtual Diagnostic Calls and more exclusive features."
                                    }
                                </p>
                            </div>
                            {!isPremium && (
                                <Link href="/pro-membership">
                                    <Button variant="secondary" className="bg-white text-amber-600 hover:bg-gray-100">
                                        Upgrade to Premium
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Modals */}
            <EmergencyDispatchModal
                open={emergencyDispatchOpen}
                onOpenChange={setEmergencyDispatchOpen}
            />
            <SupportModal open={supportOpen} onOpenChange={setSupportOpen} />
            <VirtualDiagnosticModal
                open={virtualDiagnosticOpen}
                onOpenChange={setVirtualDiagnosticOpen}
            />
            <UpgradeModal
                open={showUpgradeModal}
                onOpenChange={setShowUpgradeModal}
            />

            {/* Member Discounts Modal */}
            {discountsModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                        <div className="p-6 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-amber-100">
                                        <Percent className="w-5 h-5 text-amber-600" />
                                    </div>
                                    <h2 className="text-2xl font-bold text-gray-900">Member Discounts</h2>
                                </div>
                                <button
                                    onClick={() => setDiscountsModalOpen(false)}
                                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                                >
                                    <X className="w-5 h-5 text-gray-500" />
                                </button>
                            </div>
                        </div>

                        <div className="p-6 space-y-6">
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                                    How Pro Member Discounts Work
                                </h3>
                                <p className="text-gray-600 mb-4">
                                    As a Pro member, you have access to exclusive discounts from providers who offer special rates to our Pro community.
                                </p>
                            </div>

                            <div className="space-y-4">
                                <div className="flex items-start gap-4">
                                    <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-1">
                                        <span className="text-green-600 font-semibold text-sm">1</span>
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-1">Search for Services</h4>
                                        <p className="text-gray-600 text-sm">
                                            Use our search to find RV technicians or inspectors in your area.
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-4">
                                    <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-1">
                                        <span className="text-green-600 font-semibold text-sm">2</span>
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-1">Filter for Pro Discounts</h4>
                                        <p className="text-gray-600 text-sm">
                                            Look for the "Pro Discount" filter option to see only providers who offer special rates to Pro members.
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-4">
                                    <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-1">
                                        <span className="text-green-600 font-semibold text-sm">3</span>
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-1">Book with Discount</h4>
                                        <p className="text-gray-600 text-sm">
                                            When you contact these providers, mention your Pro membership to receive the discounted rate.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
                                <div className="flex items-start gap-3">
                                    <Crown className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                                    <div>
                                        <h4 className="font-semibold text-amber-900 mb-1">Pro Member Tip</h4>
                                        <p className="text-amber-800 text-sm">
                                            Always mention your Pro membership when contacting providers. Some providers offer discounts automatically, while others require you to ask for the Pro rate.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="flex gap-3 pt-4">
                                <Button
                                    onClick={() => setDiscountsModalOpen(false)}
                                    variant="outline"
                                    className="flex-1"
                                >
                                    Got It
                                </Button>
                                <Link href="/mobile-rv-repair" className="flex-1">
                                    <Button className="w-full bg-amber-500 hover:bg-amber-600">
                                        Find Providers with Discounts
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default withAuthorization(ProToolsPage, "USER");
