"use client";

import { AbsoluteLoader } from "@/components/Loader";
import { SearchableSelect } from "@/components/SearchableSelect";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { allRvMakes, popularRvMakes } from "@/data/rvMakes";
import { useAuth } from "@/lib/hooks/useAuth";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { z } from "zod";

const rvDetailsSchema = z.object({
	type: z.string().min(1, "RV type is required"),
	make: z.string().min(1, "RV make is required"),
	model: z.string().min(1, "RV model is required"),
	year: z
		.number()
		.min(1950, "Year must be after 1950")
		.max(new Date().getFullYear() + 1)
});

type RVDetailsFormValues = z.infer<typeof rvDetailsSchema>;

export default function RVDetailsPage() {
	const { user, refreshUser, loading } = useAuth();

	const form = useForm<RVDetailsFormValues>({
		resolver: zodResolver(rvDetailsSchema),
		defaultValues: {
			type: "",
			make: "",
			model: "",
			year: null
		}
	});

	useEffect(() => {
		if (user?.rv_details) {
			form.reset({
				type: user.rv_details.type || "",
				make: user.rv_details.make || "",
				model: user.rv_details.model || "",
				year: user.rv_details.year || null
			});
		}
	}, [user, form]);

	const makeOptions = [
		{
			label: "Popular Makes",
			options: popularRvMakes.map((make) => ({
				value: make,
				label: make
			}))
		},
		{
			label: "All Makes",
			options: allRvMakes.map((make) => ({
				value: make,
				label: make
			}))
		}
	];

	async function onSubmit(data: RVDetailsFormValues) {
		try {
			const response = await fetch("/api/user/rv-details", {
				method: "PUT",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(data)
			});

			if (!response.ok) {
				throw new Error("Failed to update RV details");
			}

			await refreshUser();
			toast.success("Your RV details have been updated.");
		} catch (error) {
			toast.error("Failed to update RV details.");
		}
	}

	if (loading) {
		return <AbsoluteLoader />;
	}

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle>RV Details</CardTitle>
			</CardHeader>
			<CardContent>
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						{form.formState.isSubmitting && <AbsoluteLoader />}

						<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
							<FormField
								control={form.control}
								name="type"
								render={({ field }) => (
									<FormItem>
										<FormLabel>RV Type</FormLabel>
										<Select onValueChange={field.onChange} value={field.value}>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select RV type" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{[
													"Class A",
													"Class B",
													"Class C",
													"Travel Trailer",
													"Fifth Wheel",
													"Truck Camper",
													"Toy Hauler"
												].map((type) => (
													<SelectItem key={type} value={type}>
														{type}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="make"
								render={({ field }) => (
									<FormItem>
										<FormLabel>RV Make</FormLabel>
										<FormControl>
											<SearchableSelect
												name="make"
												options={makeOptions}
												value={field.value}
												onChange={field.onChange}
												placeholder="Select RV make"
												error={form.formState.errors.make}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="model"
								render={({ field }) => (
									<FormItem>
										<FormLabel>RV Model</FormLabel>
										<FormControl>
											<Input placeholder="Enter RV model" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="year"
								render={({ field }) => (
									<FormItem>
										<FormLabel>RV Year</FormLabel>
										<FormControl>
											<Input
												type="number"
												placeholder="Enter RV year"
												{...field}
												onChange={(e) =>
													field.onChange(
														e.target.value ? parseInt(e.target.value) : null
													)
												}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="flex items-center justify-end gap-2">
							<Button
								type="submit"
								className="w-full sm:w-auto"
								disabled={form.formState.isSubmitting}
							>
								{form.formState.isSubmitting ? "Saving..." : "Save Changes"}
							</Button>
						</div>
					</form>
				</Form>
			</CardContent>
		</Card>
	);
}
