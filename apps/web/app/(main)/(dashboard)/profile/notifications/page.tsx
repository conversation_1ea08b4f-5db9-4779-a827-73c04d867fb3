"use client";

import NotificationPreferencesForm from "@/components/profile/notification-preferences-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function SettingsPage() {
	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle>Notification Preferences</CardTitle>
			</CardHeader>
			<CardContent>
				<NotificationPreferencesForm />
			</CardContent>
		</Card>
	);
}
