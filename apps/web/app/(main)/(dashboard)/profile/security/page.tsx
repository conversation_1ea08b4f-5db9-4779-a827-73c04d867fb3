"use client";

import DeleteAccountForm from "@/components/profile/delete-account-form";
import ProfilePasswordForm from "@/components/profile/profile-password-form";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function SettingsPage() {
	return (
		<div className="space-y-4">
			<Card className="w-full">
				<CardHeader>
					<CardTitle>Security Settings</CardTitle>
				</CardHeader>
				<CardContent>
					<ProfilePasswordForm />
				</CardContent>
			</Card>
			<Card className="w-full border-red-500">
				<CardHeader>
					<CardTitle>Account Settings</CardTitle>
				</CardHeader>
				<CardContent>
					<DeleteAccountForm />
				</CardContent>
			</Card>
		</div>
	);
}
