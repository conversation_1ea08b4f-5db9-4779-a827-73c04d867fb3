"use client";

import { SupportModal } from "@/components/modals/SupportModal";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { useAuth } from "@/lib/hooks/useAuth";
import { Crown, Loader2, MessageSquare } from "lucide-react";
import { useEffect, useState } from "react";

type PricingInfo = {
	id: string;
	productId: string;
	name: string;
	description: string;
	unitAmount: number;
	currency: string;
	interval: string;
	intervalCount: number;
	metadata: any;
};

export default function ProfileMembershipPage() {
	const { isPaid, loading } = useAuth();
	const [pricing, setPricing] = useState<PricingInfo[]>([]);
	const [pricingLoading, setPricingLoading] = useState(true);
	const [supportModalOpen, setSupportModalOpen] = useState(false);

	useEffect(() => {
		const fetchPricing = async () => {
			try {
				const response = await fetch("/api/subscription/pricing");
				if (response.ok) {
					const data = await response.json();
					setPricing(data.pricing || []);
				}
			} catch (error) {
				console.error("Error fetching pricing:", error);
			} finally {
				setPricingLoading(false);
			}
		};

		fetchPricing();
	}, []);

	const formatPrice = (amount: number, currency: string) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: currency.toUpperCase()
		}).format(amount / 100); // Convert cents to dollars
	};

	const getCurrentPlan = () => {
		if (!isPaid) return null;

		// Find the most likely current plan (you might want to store this in user data)
		return (
			pricing.find(
				(p) =>
					p.name.toLowerCase().includes("premium") ||
					p.name.toLowerCase().includes("pro")
			) || pricing[0]
		);
	};

	const currentPlan = getCurrentPlan();

	if (loading || pricingLoading) {
		return (
			<div className="flex items-center justify-center min-h-[400px]">
				<Loader2 className="w-8 h-8 animate-spin text-emerald-600" />
			</div>
		);
	}

	if (!isPaid) {
		return (
			<div className="container mx-auto py-8 px-4">
				<Card>
					<CardContent className="p-6">
						<div className="text-center">
							<p className="text-gray-600">
								You are not currently a Pro member.
							</p>
							<Button
								className="mt-4"
								onClick={() => (window.location.href = "/pro-membership")}
							>
								View Membership Plans
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-8 px-4">
			<div className="max-w-4xl mx-auto space-y-6">
				<div>
					<h1 className="text-3xl font-bold">Pro Membership</h1>
					<p className="text-gray-600 mt-2">
						Manage your Pro membership settings
					</p>
				</div>

				<Card>
					<CardHeader>
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-3">
								<div className="h-10 w-10 bg-emerald-100 rounded-full flex items-center justify-center">
									<Crown className="h-5 w-5 text-emerald-600" />
								</div>
								<div>
									<CardTitle>Pro Plan</CardTitle>
									<CardDescription>Active Subscription</CardDescription>
								</div>
							</div>
							<Button
								variant="outline"
								onClick={() => setSupportModalOpen(true)}
								className="flex items-center gap-2"
							>
								<MessageSquare className="w-4 h-4" />
								Request Cancellation
							</Button>
						</div>
					</CardHeader>
					<CardContent className="space-y-6">
						<div>
							<h3 className="font-medium text-gray-900">
								Subscription Details
							</h3>
							<dl className="mt-3 space-y-3">
								<div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
									<dt className="text-gray-600">Status</dt>
									<dd className="font-medium">
										{isPaid ? "Active" : "Inactive"}
									</dd>
								</div>
								<div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
									<dt className="text-gray-600">Plan</dt>
									<dd className="font-medium">
										{currentPlan ? (
											<>
												{currentPlan.name} (
												{formatPrice(
													currentPlan.unitAmount,
													currentPlan.currency
												)}
												/{currentPlan.interval})
											</>
										) : (
											"Pro Membership"
										)}
									</dd>
								</div>
								<div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
									<dt className="text-gray-600">Billing Cycle</dt>
									<dd className="font-medium">
										{currentPlan
											? `${currentPlan.intervalCount} ${currentPlan.interval}${currentPlan.intervalCount > 1 ? "s" : ""}`
											: "Annual"}
									</dd>
								</div>
							</dl>
						</div>

						<div>
							<h3 className="font-medium text-gray-900 mb-3">Pro Benefits</h3>
							<ul className="space-y-2 text-gray-600">
								<li>• Unlimited service requests</li>
								<li>• Priority support</li>
								<li>• Early access to new features</li>
								<li>• Detailed service history</li>
								<li>• Virtual diagnostic sessions</li>
								<li>• Pro maintenance tracker</li>
								<li>• Emergency dispatch to nearest 20 providers</li>
							</ul>
						</div>

						<div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
							<h4 className="font-medium text-amber-800 mb-2">
								Need to Cancel?
							</h4>
							<p className="text-amber-700 text-sm mb-3">
								We're sorry to see you go! Please contact our support team to
								request cancellation. We'll process your request within 24 hours
								and you'll continue to have access until the end of your current
								billing period.
							</p>
							<Button
								variant="outline"
								size="sm"
								onClick={() => setSupportModalOpen(true)}
								className="border-amber-300 text-amber-700 hover:bg-amber-100"
							>
								Contact Support to Cancel
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>

			<SupportModal
				open={supportModalOpen}
				onOpenChange={setSupportModalOpen}
				defaultSubject="Membership Cancellation Request"
				defaultMessage={`I would like to request cancellation of my Pro membership.

Current Plan: ${currentPlan?.name || "Pro Membership"}
Reason for cancellation (optional):

Please process my cancellation request. I understand I will continue to have access until the end of my current billing period.`}
				isPremiumFeature={true}
			/>
		</div>
	);
}
