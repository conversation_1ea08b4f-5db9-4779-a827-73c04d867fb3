"use client";

import ListingCard from "@/components/directory/search/ListingCard";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/lib/hooks/useAuth";
import { useFavorites } from "@/lib/hooks/useFavorites";
import { ListingWithReviewsAndDistance } from "@/types/directory";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

type ListingCategory = "all" | "rv-repair" | "rv-inspection" | "rv-services";

export default function FavoritesPage() {
	const [listings, setListings] = useState<ListingWithReviewsAndDistance[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [activeTab, setActiveTab] = useState<ListingCategory>("rv-repair");
	const { refetchFavorites } = useFavorites();
	const { user, loading: authLoading } = useAuth();
	const router = useRouter();

	useEffect(() => {
		if (!authLoading && !user) {
			router.push("/login?redirectUrl=/favorites");
		}
	}, [authLoading, user, router]);

	const fetchFavoriteListings = async () => {
		try {
			setIsLoading(true);
			const response = await fetch("/api/favorites");
			if (!response.ok) throw new Error("Failed to fetch favorites");
			const data = await response.json();
			setListings(data.favorites.map((f: any) => f.listing));
		} catch (error) {
			console.error("Error fetching favorite listings:", error);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		if (user) {
			fetchFavoriteListings();
		}
	}, [user]);

	const filteredListings = listings.filter((listing) => {
		if (activeTab === "all") return true;
		const categories = listing.categories as Record<
			string,
			{ selected: boolean }
		>;
		return categories?.[activeTab]?.selected === true;
	});

	if (authLoading || isLoading) {
		return <div>Loading...</div>;
	}

	if (!user) {
		return null;
	}

	return (
		<div className="bg-white rounded-lg shadow-sm p-6 space-y-6">
			<h1 className="text-2xl font-semibold">My Favorites</h1>

			<Tabs
				defaultValue="all"
				onValueChange={(value) => setActiveTab(value as ListingCategory)}
			>
				<TabsList className="grid w-full grid-cols-3 max-w-[500px]">
					<TabsTrigger value="all">All</TabsTrigger>
					<TabsTrigger value="rv-repair">Technicians</TabsTrigger>
					<TabsTrigger value="rv-inspection">Inspectors</TabsTrigger>
				</TabsList>

				{["all", "rv-repair", "rv-inspection"].map((tab) => (
					<TabsContent key={tab} value={tab} className="mt-6">
						{filteredListings.length === 0 ? (
							<p className="text-gray-500">
								{tab === "all"
									? "You haven't favorited any listings yet."
									: tab === "rv-repair"
										? "You haven't favorited any technicians yet."
										: "You haven't favorited any inspectors yet."}
							</p>
						) : (
							<div className="grid gap-4">
								{filteredListings.map((listing, index) => (
									<ListingCard
										key={listing.id}
										listing={listing}
										id={`favorite-${listing.id}`}
										isMobile={false}
										category={
											tab === "all"
												? "rv-repair"
												: (tab as "rv-repair" | "rv-inspection")
										}
										position={index}
										path="/favorites"
										onFavoriteToggle={() => {
											refetchFavorites();
											fetchFavoriteListings();
										}}
									/>
								))}
							</div>
						)}
					</TabsContent>
				))}
			</Tabs>
		</div>
	);
}
