"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Wrench } from "lucide-react";
import { useState } from "react";
import { toast } from "react-hot-toast";

export default function MaintenanceTrackerInterestPage() {
	const [message, setMessage] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);

	const handleSubmit = async () => {
		setIsSubmitting(true);
		try {
			const response = await fetch("/api/support/contact", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					name: "Maintenance Tracker Interest",
					email: "<EMAIL>",
					subject: "Maintenance Tracker Interest",
					message: message || "User expressed interest in the Maintenance Tracker feature.",
				}),
			});

			if (!response.ok) {
				throw new Error("Failed to submit interest");
			}

			toast.success("Thank you for your interest! We'll keep you updated.");
			setMessage("");
		} catch (error) {
			console.error("Error submitting interest:", error);
			toast.error("Failed to submit your interest. Please try again.");
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<div className="container mx-auto py-8 px-4">
			<Card className="max-w-2xl mx-auto">
				<CardHeader className="text-center">
					<div className="mx-auto w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mb-4">
						<Wrench className="w-6 h-6 text-emerald-600" />
					</div>
					<CardTitle className="text-2xl font-bold">Maintenance Tracker</CardTitle>
					<CardDescription className="mt-2">
						Coming soon! Help us build the perfect maintenance tracking tool for your RV.
						Let us know what features you'd like to see.
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="space-y-2">
						<label htmlFor="message" className="text-sm font-medium text-gray-700">
							What features would you like to see? (Optional)
						</label>
						<Textarea
							id="message"
							placeholder="Tell us what would make this tool valuable for you..."
							value={message}
							onChange={(e) => setMessage(e.target.value)}
							className="min-h-[100px]"
						/>
					</div>
					<Button
						onClick={handleSubmit}
						disabled={isSubmitting}
						className="w-full"
					>
						{isSubmitting ? "Submitting..." : "I'm Interested!"}
					</Button>
				</CardContent>
			</Card>
		</div>
	);
} 