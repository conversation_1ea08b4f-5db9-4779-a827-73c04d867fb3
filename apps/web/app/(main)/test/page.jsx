import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
	CalendarDays,
	Camera,
	CheckCircle,
	CreditCard,
	FilePlus,
	MapPin,
	Send,
	XCircle
} from "lucide-react";

/**
 * Job Workroom Page – mobile‑first refinement
 * • Keeps desktop sidebar layout
 * • Introduces a mobile‑only "central" action panel with larger, touch‑friendly buttons
 */
export default function JobWorkroom() {
	/** Shared button list so we don't repeat icon / label pairs */
	const actions = [
		{ icon: MapPin, label: "Mark On The Way", variant: "default" },
		{ icon: CheckCircle, label: "Start Job", variant: "default" },
		{ icon: CheckCircle, label: "Complete Job", variant: "default" },
		{ icon: CreditCard, label: "Request Payment", variant: "secondary" },
		{ icon: FilePlus, label: "Add Note", variant: "outline" },
		{ icon: Camera, label: "Upload Photos", variant: "outline" },
		{ icon: CalendarDays, label: "Reschedule", variant: "outline" },
		{ icon: XCircle, label: "Cancel Job", variant: "outline" }
	];

	const renderButtons = (mobile = false) => (
		<CardContent className="space-y-2">
			{actions.map(({ icon: Icon, label, variant }, idx) => (
				<Button
					key={idx}
					variant={variant}
					className={`w-full ${mobile ? "py-3 text-base" : ""}`}
					size={mobile ? "default" : "sm"}
				>
					<Icon className="mr-2 h-4 w-4" /> {label}
				</Button>
			))}
		</CardContent>
	);

	return (
		<div className="min-h-screen bg-gray-50 flex flex-col">
			{/* Header */}
			<header className="bg-primary text-white px-6 py-4 flex justify-between items-center">
				<h1 className="text-xl font-semibold">Job #12345 • HVAC Repair</h1>
				<span className="font-semibold">Scheduled</span>
			</header>

			{/* MOBILE‑ONLY action panel (appears above everything else) */}
			<div className="lg:hidden px-6 pt-4">
				<Card className="mx-auto w-full max-w-md">
					<CardHeader>
						<CardTitle>Actions</CardTitle>
					</CardHeader>
					{renderButtons(true)}
				</Card>
			</div>

			{/* Body */}
			<div className="flex flex-1 flex-col lg:flex-row gap-6 p-6">
				{/* Sidebar */}
				<aside className="w-full lg:w-72 space-y-6">
					{/* Customer Card */}
					<Card>
						<CardHeader>
							<CardTitle>Customer</CardTitle>
						</CardHeader>
						<CardContent>
							<p className="font-medium">Jane Smith</p>
							<p className="text-sm">555‑123‑4567</p>
							<p className="text-sm mb-2"><EMAIL></p>
							<p className="text-sm leading-tight">
								123 Main St
								<br />
								Springfield, IL
							</p>
						</CardContent>
					</Card>

					{/* Job Details Card */}
					<Card>
						<CardHeader>
							<CardTitle>Job Details</CardTitle>
						</CardHeader>
						<CardContent className="space-y-2 text-sm">
							<p>
								<span className="font-medium">Scheduled:</span>{" "}
								July&nbsp;10,&nbsp;2025&nbsp;@&nbsp;2:00 PM
							</p>
							<p>
								<span className="font-medium">Equipment:</span> Carrier Furnace
								Model X
							</p>
							<p>
								<span className="font-medium">Issue:</span> Not heating
							</p>
							<p>
								<span className="font-medium">Estimate:</span> $250 – $400
							</p>
						</CardContent>
					</Card>

					{/* Desktop‑only Action Buttons */}
					<Card className="hidden lg:block">
						<CardHeader>
							<CardTitle>Actions</CardTitle>
						</CardHeader>
						{renderButtons(false)}
					</Card>
				</aside>

				{/* Main Messaging Panel */}
				<main className="flex-1 flex flex-col">
					<Card className="flex flex-1 flex-col h-full">
						<CardHeader>
							<CardTitle>Messages</CardTitle>
						</CardHeader>
						<CardContent className="flex flex-1 flex-col p-0">
							{/* Scrollable message list */}
							<ScrollArea className="flex-1 px-6 py-4">
								<div className="space-y-4">
									<div>
										<p className="font-medium">You → Jane</p>
										<p className="text-sm">
											Hi Jane, just confirming your appointment this Friday at
											2 PM.
										</p>
										<span className="text-xs text-muted-foreground">
											Jul 6, 9:12 AM
										</span>
									</div>
									<div>
										<p className="font-medium">Jane</p>
										<p className="text-sm">
											Sounds great. Please ring the doorbell when you arrive.
										</p>
										<span className="text-xs text-muted-foreground">
											Jul 6, 9:15 AM
										</span>
									</div>
									{/* more messages */}
								</div>
							</ScrollArea>

							{/* Compose box */}
							<form className="border-t p-4 flex gap-2">
								<Input placeholder="Type a message…" className="flex-1" />
								<Button type="submit">
									<Send className="h-4 w-4 mr-2" /> Send
								</Button>
							</form>
						</CardContent>
					</Card>
				</main>
			</div>
		</div>
	);
}
