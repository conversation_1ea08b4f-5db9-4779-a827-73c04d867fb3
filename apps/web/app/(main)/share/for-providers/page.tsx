"use client";

import { SupportModal } from "@/components/modals/SupportModal";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { CreditCard, FileText, HeadsetIcon } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

export default function CampgroundsPage() {
	const [isBusinessCardModalOpen, setIsBusinessCardModalOpen] = useState(false);
	const [isSupportModalOpen, setIsSupportModalOpen] = useState(false);

	const downloadBrochure = () => {
		// apps/web/public/promo/flyer-campgrounds-(tracked).pdf
		window.open("/promo/flyer-providers-(tracked).pdf", "_blank");
	};

	const downloadBusinessCards = () => {
		window.open("/promo/business-cards-providers-(tracked).pdf", "_blank");
	};

	return (
		<div className="min-h-screen">
			{/* Hero Section */}
			<header className="relative text-white py-24 px-4">
				<div className="absolute inset-0">
					<Image
						src="/images/myrvresource.webp"
						alt="RV in scenic landscape"
						layout="fill"
						objectFit="cover"
						quality={100}
					/>
					<div className="absolute inset-0 bg-gradient-to-r from-[#43806c]/90 to-[#2c5446]/90"></div>
				</div>
				<div className="container mx-auto relative z-10">
					<h1 className="text-4xl md:text-5xl font-bold mb-4 text-center">
						Marketing Resources for Service Providers
					</h1>
					<p className="text-xl text-center max-w-3xl mx-auto">
						Share your services with campgrounds and RV owners through our
						professional marketing materials
					</p>
				</div>
			</header>

			{/* Main Content */}
			<main className="container mx-auto px-4 py-16">
				{/* Introduction Section */}
				<div className="max-w-3xl mx-auto text-center mb-16">
					<h2 className="text-3xl font-bold mb-8 text-[#43806c]">
						Grow Your RV Service Business
					</h2>
					<p className="text-lg text-gray-600 mb-6">
						As a certified RV service provider, you can expand your reach by
						partnering with local campgrounds and sharing your services with
						their guests. Our marketing materials make it easy to establish
						these valuable connections.
					</p>
					<p className="text-lg text-gray-600 mb-6">
						We provide professional brochures and business cards that highlight
						your services and make it simple for campgrounds to refer their
						guests to you. These materials include your unique QR code that
						connects directly to your RV Help profile.
					</p>
					<p className="text-lg text-gray-600">
						Download our marketing materials to share with campgrounds in your
						service area. You can also distribute these to RV owners directly at
						shows, events, or through your existing customer base.
					</p>
				</div>

				{/* Resources Grid */}
				<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mx-auto">
					{/* PDF Brochure Card */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2 mb-4">
								<FileText className="h-5 w-5" />
								Professional Brochure
							</CardTitle>
							<CardDescription>
								Download our customizable brochure to share with campgrounds.
								Includes your unique QR code for direct access to your services.
							</CardDescription>
						</CardHeader>
						<CardContent>
							<Button className="w-full" onClick={downloadBrochure}>
								Download PDF
							</Button>
						</CardContent>
					</Card>

					{/* Business Cards Card */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2 mb-4">
								<CreditCard className="h-5 w-5" />
								Business Cards
							</CardTitle>
							<CardDescription>
								Professional business cards to leave with campgrounds and
								potential customers. Features your unique QR code for easy
								service booking.
							</CardDescription>
						</CardHeader>
						<CardContent>
							<Button className="w-full" onClick={downloadBusinessCards}>
								Download Business Cards
							</Button>
						</CardContent>
					</Card>

					{/* Support Card */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2 mb-4">
								<HeadsetIcon className="h-5 w-5" />
								Marketing Support
							</CardTitle>
							<CardDescription>
								Need help with your marketing strategy? Our team can help you
								maximize your presence in the RV Help network.
							</CardDescription>
						</CardHeader>
						<CardContent>
							<Button
								className="w-full"
								variant="secondary"
								onClick={() => setIsSupportModalOpen(true)}
							>
								Get Support
							</Button>
						</CardContent>
					</Card>
				</div>
			</main>

			{/* Modals */}
			<SupportModal
				open={isBusinessCardModalOpen}
				onOpenChange={setIsBusinessCardModalOpen}
				defaultSubject="Campground Business Card Request"
				defaultMessage={`I would like to request business cards for my campground. Please include the following information:

Campground Name:
Address:
Number of cards needed:

Thank you!`}
			/>
			<SupportModal
				open={isSupportModalOpen}
				onOpenChange={setIsSupportModalOpen}
			/>
		</div>
	);
}
