"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/lib/hooks/useAuth";
import { signIn } from "next-auth/react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface UserStatus {
	userExists: boolean;
	hasPassword: boolean;
	needsPasswordSetup: boolean;
	email: string;
	firstName?: string;
	lastName?: string;
}

export default function SetupPasswordPage({
	params
}: {
	params: { id: string };
}) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { user } = useAuth();
	const [password, setPassword] = useState("");
	const [confirmPassword, setConfirmPassword] = useState("");
	const [settingPassword, setSettingPassword] = useState(false);
	const [signingIn, setSigningIn] = useState(false);
	const [userStatus, setUserStatus] = useState<UserStatus | null>(null);
	const [loading, setLoading] = useState(true);

	// Get token from URL params
	const token = searchParams.get("token");
	const emailFromUrl = searchParams.get("email");

	// Check user status on page load
	useEffect(() => {
		const checkUserStatus = async () => {
			try {
				const response = await fetch("/api/auth/check-service-request-user", {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({ serviceRequestId: params.id })
				});

				if (!response.ok) {
					throw new Error("Failed to check user status");
				}

				const data = await response.json();
				setUserStatus(data);
			} catch (error) {
				console.error("Error checking user status:", error);
				toast.error("An error occurred. Please try again.");
			} finally {
				setLoading(false);
			}
		};

		if (!user) {
			checkUserStatus();
		}
	}, [params.id, user]);

	const handleSetPassword = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!password) {
			toast.error("Password is required");
			return;
		}

		if (password !== confirmPassword) {
			toast.error("Passwords do not match");
			return;
		}

		setSettingPassword(true);
		try {
			// Use token-based password setup if token is provided
			if (token) {
				const passwordResponse = await fetch(
					"/api/service-requests/setup-password",
					{
						method: "POST",
						headers: { "Content-Type": "application/json" },
						body: JSON.stringify({
							token,
							password
						})
					}
				);

				if (!passwordResponse.ok) {
					const data = await passwordResponse.json();
					throw new Error(data.error || "Failed to set password");
				}

				const { email } = await passwordResponse.json();

				// Auto-login the user
				const result = await signIn("credentials", {
					email,
					password,
					redirect: false
				});

				if (result?.error) {
					throw new Error("Failed to log in");
				}
			} else {
				// Fallback to old method if no token
				const passwordResponse = await fetch(
					"/api/auth/set-service-request-password",
					{
						method: "POST",
						headers: { "Content-Type": "application/json" },
						body: JSON.stringify({
							email: userStatus?.email,
							password,
							serviceRequestId: params.id
						})
					}
				);

				if (!passwordResponse.ok) {
					const data = await passwordResponse.json();
					throw new Error(data.error || "Failed to set password");
				}

				// Auto-login the user
				const result = await signIn("credentials", {
					email: userStatus?.email,
					password,
					redirect: false
				});

				if (result?.error) {
					throw new Error("Failed to log in");
				}
			}

			toast.success("Password set successfully");
			router.push(`/service-requests/${params.id}`);
		} catch (error) {
			toast.error(error instanceof Error ? error.message : "An error occurred");
		} finally {
			setSettingPassword(false);
		}
	};

	const handleLogin = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!password) {
			toast.error("Password is required");
			return;
		}

		setSigningIn(true);
		try {
			const email = emailFromUrl || userStatus?.email;
			const result = await signIn("credentials", {
				email,
				password,
				redirect: false
			});

			if (result?.error) {
				toast.error("Invalid password. Please try again.");
			} else {
				toast.success("Login successful");
				router.push(`/service-requests/${params.id}`);
			}
		} catch (error) {
			toast.error("An error occurred. Please try again.");
		} finally {
			setSigningIn(false);
		}
	};

	// If user is already logged in, redirect them to the service request page
	if (user) {
		router.push(`/service-requests/${params.id}`);
		return null;
	}

	// Show loading state
	if (loading) {
		return (
			<div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
				<Card className="max-w-md w-full shadow-xl border-0 bg-white/80 backdrop-blur-sm">
					<CardContent className="pt-8 pb-8">
						<div className="text-center">
							<div className="w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
							<p className="text-gray-600 font-medium">
								Loading your account setup...
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Determine if user should see password setup or login form
	const shouldShowPasswordSetup =
		token || (userStatus?.userExists && userStatus?.needsPasswordSetup);

	return (
		<div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
			<Card className="max-w-lg w-full shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
				<CardHeader className="text-center pb-4">
					{/* Success Icon */}
					<div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
						<svg
							className="w-8 h-8 text-white"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M5 13l4 4L19 7"
							/>
						</svg>
					</div>

					<CardTitle className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
						Set Up Your Account
					</CardTitle>
				</CardHeader>

				<CardContent className="space-y-6">
					{/* Account Benefits */}
					<div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-xl border border-blue-200 p-6">
						<h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
							Free Account Benefits
						</h3>
						<div className="space-y-4">
							<div className="flex items-start space-x-3">
								<div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
									<svg
										className="w-4 h-4 text-blue-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
								</div>
								<div>
									<h4 className="text-sm font-semibold text-gray-900">
										Service Request History
									</h4>
									<p className="text-xs text-gray-600">
										Track all your past and current requests
									</p>
								</div>
							</div>

							<div className="flex items-start space-x-3">
								<div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
									<svg
										className="w-4 h-4 text-green-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
										/>
									</svg>
								</div>
								<div>
									<h4 className="text-sm font-semibold text-gray-900">
										Favorite Providers
									</h4>
									<p className="text-xs text-gray-600">
										Save your trusted technicians for future jobs
									</p>
								</div>
							</div>

							<div className="flex items-start space-x-3">
								<div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
									<svg
										className="w-4 h-4 text-purple-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
										/>
									</svg>
								</div>
								<div>
									<h4 className="text-sm font-semibold text-gray-900">
										Direct Messaging
									</h4>
									<p className="text-xs text-gray-600">
										Chat directly with providers about your request
									</p>
								</div>
							</div>

							<div className="flex items-start space-x-3">
								<div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
									<svg
										className="w-4 h-4 text-orange-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
										/>
									</svg>
								</div>
								<div>
									<h4 className="text-sm font-semibold text-gray-900">
										Invite More Providers (Pro Membership)
									</h4>
									<p className="text-xs text-gray-600">
										Get faster responses and invite more providers
									</p>
								</div>
							</div>
						</div>
					</div>

					{/* Form Section */}
					<div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
						{!shouldShowPasswordSetup ? (
							// Login form
							<form onSubmit={handleLogin} className="space-y-5">
								<div className="text-center mb-6">
									<div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
										<svg
											className="w-6 h-6 text-white"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
											/>
										</svg>
									</div>
									<h4 className="text-lg font-semibold text-gray-900 mb-2">
										Welcome back,{" "}
										{userStatus?.firstName || emailFromUrl || userStatus?.email}
										!
									</h4>
									<p className="text-sm text-gray-600">
										Sign in to access your service request.
									</p>
								</div>

								<div className="space-y-4">
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-2">
											Password
										</label>
										<Input
											type="password"
											placeholder="Enter your password"
											value={password}
											onChange={(e) => setPassword(e.target.value)}
											required
											className="h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
										/>
									</div>

									<Button
										type="submit"
										className="w-full h-12 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
										disabled={signingIn}
									>
										{signingIn ? (
											<div className="flex items-center justify-center space-x-2">
												<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
												<span>Signing In...</span>
											</div>
										) : (
											"Sign In & View Request"
										)}
									</Button>
								</div>

								<div className="text-center">
									<Link
										href="/forgot-password"
										className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
									>
										Forgot your password?
									</Link>
								</div>
							</form>
						) : (
							// Password setting form
							<form onSubmit={handleSetPassword} className="space-y-5">
								<div className="text-center mb-6">
									<div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3">
										<svg
											className="w-6 h-6 text-white"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
											/>
										</svg>
									</div>
									<h4 className="text-lg font-semibold text-gray-900 mb-2">
										{userStatus?.userExists
											? `Hi ${userStatus.firstName || ""}!`
											: `Hi ${emailFromUrl?.split("@")[0] || ""}!`}
									</h4>
									<p className="text-sm text-gray-600">
										Set up your password to access your service request
										dashboard.
									</p>
								</div>

								<div className="space-y-4">
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-2">
											Create Password
										</label>
										<Input
											type="password"
											placeholder="Minimum 8 characters"
											value={password}
											onChange={(e) => setPassword(e.target.value)}
											required
											minLength={8}
											className="h-12 border-gray-300 focus:border-purple-500 focus:ring-purple-500"
										/>
									</div>

									<div>
										<label className="block text-sm font-medium text-gray-700 mb-2">
											Confirm Password
										</label>
										<Input
											type="password"
											placeholder="Confirm your password"
											value={confirmPassword}
											onChange={(e) => setConfirmPassword(e.target.value)}
											required
											minLength={8}
											className="h-12 border-gray-300 focus:border-purple-500 focus:ring-purple-500"
										/>
									</div>

									<Button
										type="submit"
										className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
										disabled={settingPassword}
									>
										{settingPassword ? (
											<div className="flex items-center justify-center space-x-2">
												<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
												<span>Setting Up Account...</span>
											</div>
										) : (
											"Create Account & View Request"
										)}
									</Button>
								</div>
							</form>
						)}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
