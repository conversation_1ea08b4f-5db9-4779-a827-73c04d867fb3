"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail, RefreshCw } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { toast } from "react-hot-toast";

export default function CheckEmailPage({ params }: { params: { id: string } }) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [isResending, setIsResending] = useState(false);

	const email = searchParams.get("email");
	const serviceRequestId = params.id;

	const handleResendEmail = async () => {
		if (!email) {
			toast.error("Email address not found");
			return;
		}

		setIsResending(true);
		try {
			const response = await fetch("/api/auth/send-password-setup", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					serviceRequestId,
					email
				})
			});

			if (response.ok) {
				toast.success("Password setup instructions sent!");
			} else {
				const data = await response.json();
				toast.error(data.error || "Failed to send email");
			}
		} catch (error) {
			toast.error("An error occurred while sending the email");
		} finally {
			setIsResending(false);
		}
	};

	const maskEmail = (email: string) => {
		const [username, domain] = email.split("@");
		const maskedUsername =
			username.charAt(0) +
			"*".repeat(Math.max(0, username.length - 2)) +
			(username.length > 1 ? username.charAt(username.length - 1) : "");
		return `${maskedUsername}@${domain}`;
	};

	return (
		<div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
			<Card className="w-full max-w-md">
				<CardHeader className="text-center">
					<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
						<Mail className="h-6 w-6 text-blue-600" />
					</div>
					<CardTitle className="text-xl">Check Your Email</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="text-center text-gray-600">
						<p className="mb-2">We've sent a password setup link to:</p>
						<p className="font-medium text-gray-900">
							{email ? maskEmail(email) : "your email address"}
						</p>
					</div>

					<div className="bg-green-50 p-4 rounded-lg">
						<p className="text-sm text-green-800">
							<strong>What's in your email:</strong> A personalized link with
							details about your RV service request and a secure way to set up
							your password.
						</p>
					</div>

					<div className="bg-blue-50 p-4 rounded-lg">
						<p className="text-sm text-blue-800">
							Click the "Set Up Your Password" button in the email to access
							your service request workroom and communicate with providers.
						</p>
					</div>

					<div className="space-y-3">
						<Button
							onClick={handleResendEmail}
							disabled={isResending}
							variant="outline"
							className="w-full"
						>
							{isResending ? (
								<>
									<RefreshCw className="h-4 w-4 mr-2 animate-spin" />
									Sending...
								</>
							) : (
								<>
									<Mail className="h-4 w-4 mr-2" />
									Resend Email
								</>
							)}
						</Button>

						<Button
							onClick={() => router.push("/login")}
							variant="ghost"
							className="w-full"
						>
							Already have an account? Sign in
						</Button>
					</div>

					<div className="text-center text-sm text-gray-500">
						<p>
							Didn't receive the email? Check your spam folder or try resending.
							The link will expire in 24 hours.
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
