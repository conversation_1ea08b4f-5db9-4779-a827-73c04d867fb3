"use client";

import MessageThread from "@/components/MessageThread";
import {
	She<PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	SheetTitle
} from "@/components/ui/sheet";
import { useAuth } from "@/lib/hooks/useAuth";
import { ApiQuoteMessage } from "@/types/global";
import { type Listing } from "@rvhelp/database";

type MessageDrawerProps = {
	open: boolean;
	onClose: () => void;
	quoteId: string;
	provider: Listing;
	onMessageSent: (message: ApiQuoteMessage) => void;
	onMessagesRead?: () => void; // Add callback for when messages are marked as read
};

export default function MessageDrawer({
	open,
	onClose,
	quoteId,
	provider,
	onMessageSent,
	onMessagesRead
}: MessageDrawerProps) {
	const { user } = useAuth();

	return (
		<Sheet open={open} onOpenChange={onClose}>
			<SheetContent className="w-[90vw] sm:max-w-[600px] p-0 flex flex-col h-screen">
				<SheetHeader className="p-4 flex-shrink-0">
					<SheetTitle>
						Messages with {provider.first_name} {provider.last_name}
					</SheetTitle>
				</SheetHeader>

				<div className="flex-1 min-h-0 flex flex-col">
					<MessageThread
						quoteId={quoteId}
						viewerRole="USER"
						userId={user?.id}
						onMessageSent={onMessageSent}
						onMessagesRead={onMessagesRead}
					/>
				</div>
			</SheetContent>
		</Sheet>
	);
}
