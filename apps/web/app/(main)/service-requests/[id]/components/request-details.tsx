"use client";

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { getCategoryName } from "@/lib/categories";
import { JobWithUserAndLocation } from "@/types/global";
import {
	Calendar,
	ChevronDown,
	ChevronUp,
	Clock,
	FileText,
	Hash,
	Mail,
	MapPin,
	Phone,
	Truck,
	UserIcon
} from "lucide-react";
import { useCallback, useState } from "react";

export default function RequestDetails({
	job
}: {
	job: JobWithUserAndLocation;
}) {
	const [expandedSections, setExpandedSections] = useState({
		originalRequest: true,
		serviceDetails: true,
		customerInfo: true
	});

	const toggleSection = useCallback(
		(section: keyof typeof expandedSections) => {
			setExpandedSections((prev) => ({
				...prev,
				[section]: !prev[section]
			}));
		},
		[]
	);

	function getStatusColor(status: string) {
		switch (status) {
			case "REQUEST_CREATED":
				return "bg-amber-100 text-amber-800 border border-amber-200";
			case "REQUEST_APPROVED":
				return "bg-blue-100 text-emerald-800 border border-emerald-200";
			case "REQUEST_REJECTED":
				return "bg-red-100 text-red-800 border border-red-200";
			case "JOB_REQUESTED":
				return "bg-blue-100 text-blue-800 border border-blue-200";
			case "JOB_ACCEPTED":
				return "bg-blue-100 text-blue-800 border border-blue-200";
			case "JOB_STARTED":
				return "bg-emerald-100 text-emerald-800 border border-emerald-200";
			case "JOB_COMPLETED":
				return "bg-gray-100 text-gray-800 border border-gray-200";
			case "JOB_CANCELLED":
				return "bg-red-100 text-red-800 border border-red-200";
			case "AUTHORIZATION_REQUESTED":
				return "bg-amber-100 text-amber-800 border border-amber-200";
			case "AUTHORIZATION_APPROVED":
				return "bg-blue-100 text-emerald-800 border border-emerald-200";
			case "AUTHORIZATION_REJECTED":
				return "bg-red-100 text-red-800 border border-red-200";
			case "INVOICE_CREATED":
				return "bg-amber-100 text-amber-800 border border-amber-200";
			case "INVOICE_PAID":
				return "bg-emerald-100 text-emerald-800 border border-emerald-200";
			default:
				return "bg-gray-100 text-gray-800 border border-gray-200";
		}
	}

	return (
		<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<div className="space-y-6">
				{/* Original Request Section */}
				{job.warranty_request ? (
					<div className="border rounded-md bg-white">
						<button
							onClick={() => toggleSection("originalRequest")}
							className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
						>
							<div className="flex items-center justify-between w-full">
								<h3 className="text-lg font-medium">{`${job.warranty_request.company.name} Warranty Request`}</h3>
								<div className="flex items-center gap-3">
									<Badge
										className={getStatusColor(job.warranty_request.status)}
									>
										{job.warranty_request.status
											.toUpperCase()
											.replace("_", " ")}
									</Badge>
									{expandedSections.originalRequest ? (
										<ChevronUp className="h-5 w-5 text-gray-500" />
									) : (
										<ChevronDown className="h-5 w-5 text-gray-500" />
									)}
								</div>
							</div>
						</button>
						{expandedSections.originalRequest && (
							<div className="px-6 pb-6">
								{/* Warranty Request Details */}
								<div className="space-y-6">
									<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
										<div className="flex items-start">
											<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
												<Hash className="h-4 w-4 text-gray-500" />
											</div>
											<div>
												<p className="font-medium">ID</p>
												<p className="text-gray-500 mt-1 font-mono text-sm">
													{job.warranty_request.uuid}
												</p>
											</div>
										</div>

										{job.warranty_request.estimated_hours && (
											<div className="flex items-start">
												<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
													<Clock className="h-4 w-4 text-gray-500" />
												</div>
												<div>
													<p className="font-medium">Estimated Hours</p>
													<p className="text-gray-500 mt-1">
														{job.warranty_request.estimated_hours} hours
													</p>
												</div>
											</div>
										)}
									</div>

									<div className="flex items-start">
										<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
											<FileText className="h-4 w-4 text-gray-500" />
										</div>
										<div>
											<p className="font-medium">Issue Description</p>
											<p className="text-gray-500 mt-1">
												{job.warranty_request.complaint}
											</p>
										</div>
									</div>

									{job.warranty_request.component && (
										<div className="flex items-start">
											<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
												<FileText className="h-4 w-4 text-gray-500" />
											</div>
											<div>
												<p className="font-medium">Affected Component</p>
												<p className="text-gray-500 mt-1">{`${job.warranty_request.component.type} (${job.warranty_request.component.manufacturer})`}</p>
											</div>
										</div>
									)}

									<div className="flex items-start">
										<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
											<FileText className="h-4 w-4 text-gray-500" />
										</div>
										<div>
											<p className="font-medium">Cause</p>
											<p className="text-gray-500 mt-1">
												{job.warranty_request.cause || (
													<span className="italic text-gray-400">
														Not specified
													</span>
												)}
											</p>
										</div>
									</div>

									<div className="flex items-start">
										<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
											<FileText className="h-4 w-4 text-gray-500" />
										</div>
										<div>
											<p className="font-medium">Correction</p>
											<p className="text-gray-500 mt-1">
												{job.warranty_request.correction || (
													<span className="italic text-gray-400">
														Not specified
													</span>
												)}
											</p>
										</div>
									</div>
								</div>
							</div>
						)}
					</div>
				) : (
					<div className="border rounded-md bg-white">
						<button
							onClick={() => toggleSection("originalRequest")}
							className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
						>
							<h3 className="text-lg font-medium">Original Request</h3>
							{expandedSections.originalRequest ? (
								<ChevronUp className="h-5 w-5 text-gray-500" />
							) : (
								<ChevronDown className="h-5 w-5 text-gray-500" />
							)}
						</button>
						{expandedSections.originalRequest && (
							<div className="px-6 pb-6">
								<p className="text-gray-600">{job.message}</p>
							</div>
						)}
					</div>
				)}
			</div>
			<div className="space-y-6">
				{/* Customer Information Section */}
				<div className="border rounded-md bg-white">
					<button
						onClick={() => toggleSection("customerInfo")}
						className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
					>
						<h3 className="text-lg font-medium">Customer Information</h3>
						{expandedSections.customerInfo ? (
							<ChevronUp className="h-5 w-5 text-gray-500" />
						) : (
							<ChevronDown className="h-5 w-5 text-gray-500" />
						)}
					</button>
					{expandedSections.customerInfo && (
						<div className="px-6 pb-6">
							<div className="flex items-center space-x-4 mb-6">
								<Avatar className="h-12 w-12 bg-gray-100">
									<AvatarFallback className="text-gray-500">
										{job.user.first_name?.[0] || "U"}
										{job.user.last_name?.[0] || ""}
									</AvatarFallback>
								</Avatar>
								<div>
									<h2 className="font-semibold text-lg">
										{job.user.first_name} {job.user.last_name}
									</h2>
									<div className="flex items-center mt-1">
										{[1, 2, 3, 4, 5].map((star) => (
											<svg
												key={star}
												className={`w-4 h-4 ${star <= (job.user.rating || 0) ? "text-yellow-400" : "text-gray-200"}`}
												fill="currentColor"
												viewBox="0 0 20 20"
												xmlns="http://www.w3.org/2000/svg"
											>
												<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
											</svg>
										))}
										<span className="ml-1 text-sm text-gray-500">
											{job.user.rating || 0} ({job.user.num_reviews || 0}{" "}
											reviews)
										</span>
									</div>
								</div>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div className="flex items-start">
									<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
										<UserIcon className="h-4 w-4 text-gray-500" />
									</div>
									<div>
										<p className="font-medium">Name</p>
										<p className="text-gray-500 mt-1">
											{job.user.first_name} {job.user.last_name}
										</p>
									</div>
								</div>

								<div className="flex items-start">
									<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
										<Mail className="h-4 w-4 text-gray-500" />
									</div>
									<div>
										<p className="font-medium">Email</p>
										<p className="text-gray-500 mt-1">{job.user.email}</p>
									</div>
								</div>

								{job.phone && (
									<div className="flex items-start">
										<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
											<Phone className="h-4 w-4 text-gray-500" />
										</div>
										<div>
											<p className="font-medium">Phone</p>
											<p className="text-gray-500 mt-1">{job.phone}</p>
										</div>
									</div>
								)}

								<div className="flex items-start">
									<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
										<UserIcon className="h-4 w-4 text-gray-500" />
									</div>
								</div>
							</div>
						</div>
					)}
				</div>
				{/* Service Details Section */}
				<div className="border rounded-md bg-white">
					<button
						onClick={() => toggleSection("serviceDetails")}
						className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
					>
						<h3 className="text-lg font-medium">Service Details</h3>
						{expandedSections.serviceDetails ? (
							<ChevronUp className="h-5 w-5 text-gray-500" />
						) : (
							<ChevronDown className="h-5 w-5 text-gray-500" />
						)}
					</button>
					{expandedSections.serviceDetails && (
						<div className="px-6 pb-6">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div className="space-y-6">
									<div className="flex items-start">
										<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
											<FileText className="h-4 w-4 text-gray-500" />
										</div>
										<div>
											<p className="font-medium">Service Type</p>
											<p className="text-gray-500 mt-1">
												{getCategoryName(job.category)}
											</p>
										</div>
									</div>

									<div className="flex items-start">
										<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
											<Calendar className="h-4 w-4 text-gray-500" />
										</div>
										<div>
											<p className="font-medium">Request Date</p>
											<p className="text-gray-500 mt-1">
												{new Date(job.created_at).toLocaleDateString(
													undefined,
													{
														year: "numeric",
														month: "long",
														day: "numeric"
													}
												)}
											</p>
										</div>
									</div>
								</div>

								<div className="space-y-6">
									<div className="flex items-start">
										<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
											<Truck className="h-4 w-4 text-gray-500" />
										</div>
										<div>
											<p className="font-medium">RV Details</p>
											<p className="text-gray-500 mt-1">
												{job.rv_year} {job.rv_make} {job.rv_model}
											</p>
											<p className="text-gray-500">Type: {job.rv_type}</p>
										</div>
									</div>

									<div className="flex items-start">
										<div className="mr-3 mt-0.5 bg-gray-100 p-2 rounded-md">
											<MapPin className="h-4 w-4 text-gray-500" />
										</div>
										<div>
											<p className="font-medium">Location</p>
											<p className="text-gray-500 mt-1">
												{job.location.address}, {job.location.city},{" "}
												{job.location.state}
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
