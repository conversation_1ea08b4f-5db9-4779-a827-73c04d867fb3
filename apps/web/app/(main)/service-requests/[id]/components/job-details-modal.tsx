import { CauseCorrectionSection } from "@/components/oem/warranty-details/cause-correction-section";
import { CustomerInformationSection } from "@/components/oem/warranty-details/customer-information-section";
import { IssueDescriptionSection } from "@/components/oem/warranty-details/issue-description-section";
import { RVDetailsSection } from "@/components/oem/warranty-details/rv-details-section";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { JobWithUserAndLocation, QuoteWithListing } from "@/types/global";

const JobDetailsModal = ({
	job,
	showDetailsModal,
	setShowDetailsModal,
	acceptedQuote
}: {
	job: JobWithUserAndLocation;
	showDetailsModal: boolean;
	setShowDetailsModal: (show: boolean) => void;
	acceptedQuote?: QuoteWithListing;
}) => {
	return (
		<Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
			<DialogContent className="max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Service Request Details</DialogTitle>
				</DialogHeader>

				<div className="grid grid-cols-1 gap-4 mb-4 text-gray-900">
					<CustomerInformationSection job={job} />
					<RVDetailsSection job={job} />
					{job.warranty_request && (
						<>
							<IssueDescriptionSection
								request={job.warranty_request}
								isProvider={false}
							/>
							<CauseCorrectionSection request={job.warranty_request} />
						</>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default JobDetailsModal;
