import ConversationMessageThread from "@/components/conversations/ConversationMessageThread";
import InviteProvidersModal from "@/components/global-modals/InviteProvidersModal";
import { JobStatusModal } from "@/components/modals/JobStatusModal";
import { SupportModal } from "@/components/modals/SupportModal";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Breadcrumbs } from "@/components/ui/breadcrumbs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import UpgradeWorkroomCTA from "@/components/upgrade/UpgradeWorkroomCTA";
import { getCategoryName } from "@/lib/categories";
import { useAuth } from "@/lib/hooks/useAuth";
import { OfferUtils } from "@/lib/utils/offer-utils";
import { JobWithUserAndLocation, QuoteWithListing } from "@/types/global";
import { JobStatus, QuoteStatus } from "@rvhelp/database";
import { formatDistanceToNow } from "date-fns";
import {
	AlertCircle,
	CheckCircle,
	CheckCircle2,
	Clock,
	HeadphonesIcon,
	Lock,
	MessageCircle,
	MessageSquare,
	Phone,
	UserPlus
} from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import EmptyWorkroom from "./empty-workroom";
import QuotesList from "./quotes-list";
import WorkroomHeader from "./workroom-header";

// Define quote status groups for filtering
const QUOTE_STATUS_GROUPS = {
	all: { label: "All", statuses: [] as QuoteStatus[] },
	pending: { label: "Pending", statuses: [QuoteStatus.PENDING] },
	accepted: { label: "Accepted", statuses: [QuoteStatus.ACCEPTED] },
	rejected: { label: "Provider Declined", statuses: [QuoteStatus.REJECTED] },
	customerRejected: {
		label: "Customer Rejected",
		statuses: [QuoteStatus.CUSTOMER_REJECTED]
	},
	withdrawn: { label: "Withdrawn", statuses: [QuoteStatus.WITHDRAWN] },
	expired: { label: "Expired", statuses: [QuoteStatus.EXPIRED] }
} as const;

type QuoteFilterKey = keyof typeof QUOTE_STATUS_GROUPS;

type SelectProviderRoomProps = {
	job: JobWithUserAndLocation;
	fetchJob: () => Promise<void>;
	setIsOfferModalOpen: (isOpen: boolean) => void;
	setOfferVariant?: (variant: "invite-providers" | "pro-support") => void;
	preSelectedProviders?: string[];
};

const SelectProviderRoom = ({
	job,
	fetchJob,
	setIsOfferModalOpen,
	setOfferVariant,
	preSelectedProviders = []
}: SelectProviderRoomProps) => {
	const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
	const [showSupportModal, setShowSupportModal] = useState(false);
	const [activeTab, setActiveTab] = useState("invitations");
	const [selectedConversationId, setSelectedConversationId] = useState<
		string | null
	>(null);
	const [showStatusModal, setShowStatusModal] = useState(false);
	const [statusAction, setStatusAction] = useState<"complete" | "cancel">(
		"complete"
	);
	const [quoteFilter, setQuoteFilter] = useState<QuoteFilterKey>("all");
	const { isPaid, loading: authLoading, user } = useAuth();
	const [quotes, setQuotes] = useState<QuoteWithListing[]>(job.quotes || []);

	// Update local quotes state when job.quotes changes
	useEffect(() => {
		console.log("job.quotes in select-provider-room", job.quotes);
		setQuotes(job.quotes || []);
	}, [job.quotes]);

	// Calculate quote counts for each filter group
	const quoteCounts = useMemo(() => {
		const counts: Record<QuoteFilterKey, number> = {} as Record<
			QuoteFilterKey,
			number
		>;

		Object.entries(QUOTE_STATUS_GROUPS).forEach(([key, group]) => {
			const filterKey = key as QuoteFilterKey;
			if (filterKey === "all") {
				counts[filterKey] = quotes.length;
			} else {
				counts[filterKey] = quotes.filter((quote) =>
					(group.statuses as readonly QuoteStatus[]).includes(
						quote.status as QuoteStatus
					)
				).length;
			}
		});

		return counts;
	}, [quotes]);

	// Get filtered quotes based on current filter
	const filteredQuotes = useMemo(() => {
		if (quoteFilter === "all") {
			return quotes;
		}

		const group = QUOTE_STATUS_GROUPS[quoteFilter];
		return quotes.filter((quote) =>
			(group.statuses as readonly QuoteStatus[]).includes(
				quote.status as QuoteStatus
			)
		);
	}, [quotes, quoteFilter]);

	// Get filters that have quotes (for dropdown)
	const availableFilters = useMemo(() => {
		return Object.entries(QUOTE_STATUS_GROUPS).filter(([key, _]) => {
			const filterKey = key as QuoteFilterKey;
			return quoteCounts[filterKey] > 0;
		});
	}, [quoteCounts]);

	// Get quotes that require action
	const quotesAwaitingReview = quotes.filter(
		(q) => q.status === QuoteStatus.ACCEPTED
	); // Provider submitted quote, awaiting customer review
	const pendingQuotes = quotes.filter((q) => q.status === QuoteStatus.PENDING); // Provider hasn't responded yet
	const providersRequestingInfo = quotes.filter(
		(q) =>
			q.status === QuoteStatus.PENDING &&
			q.messages &&
			q.messages.length > 0 &&
			q.messages.some((msg) => msg.sender_type === "PROVIDER")
	); // Provider has sent messages asking for more info

	// Get conversation data organized by messaging activity
	const conversations = useMemo(() => {
		return quotes.map((quote) => ({
			quote_id: quote.id,
			providerId: quote.listing.id,
			providerName: `${quote.listing.first_name} ${quote.listing.last_name}`,
			providerImage: quote.listing.profile_image,
			businessName: quote.listing.business_name,
			status: quote.status,
			lastMessage: quote.unread_message?.content || null,
			last_message_at: quote.unread_message?.created_at || quote.updated_at,
			unread_count: quote.unread_messages_count || 0,
			hasResponse: quote.status === QuoteStatus.ACCEPTED,
			isAccepted: job.accepted_quote_id === quote.id,
			quote: quote,
			messages: [] // Will be populated by ConversationMessageThread
		}));
	}, [quotes, job.accepted_quote_id]);

	// Get conversation statistics
	const conversationStats = useMemo(() => {
		const totalUnread = conversations.reduce(
			(sum, conv) => sum + conv.unread_count,
			0
		);
		const activeConversations = conversations.filter(
			(conv) => conv.unread_count > 0 || conv.hasResponse || conv.lastMessage
		).length;
		const totalProposals = quotesAwaitingReview.length;

		return {
			totalUnread,
			activeConversations,
			totalProposals
		};
	}, [conversations, quotesAwaitingReview]);

	const handleProviderInvited = useCallback(
		(newQuotes: QuoteWithListing[]) => {
			setQuotes((prev) => [...prev, ...newQuotes]);
			fetchJob();
		},
		[fetchJob]
	);

	const handleFilterChange = (value: string) => {
		setQuoteFilter(value as QuoteFilterKey);
	};

	const handleStatusButtonClick = (action: "complete" | "cancel") => {
		setStatusAction(action);
		setShowStatusModal(true);
	};

	const handleStatusSuccess = () => {
		// Refresh job data
		fetchJob();
	};

	const workroomIsUpgraded = job.is_premium || isPaid;
	const requestLabel = `${job.rv_year || ""} ${job.rv_make || ""} ${job.rv_model || ""} ${getCategoryName(job.category)}`;
	const isOfferActive =
		job && OfferUtils.isJobOfferActive(new Date(job.created_at));

	// Check if user is eligible for discount
	const offerEligibilityCheck = useMemo(() => {
		if (!user || authLoading || workroomIsUpgraded || !isOfferActive) {
			return { eligible: false, reason: "Not applicable" };
		}
		return OfferUtils.isUserEligibleForAnnualOfferClientSide(
			user,
			new Date(job.created_at)
		);
	}, [user, authLoading, workroomIsUpgraded, isOfferActive]);

	const isEligibleForDiscount = offerEligibilityCheck.eligible;
	const shouldShowOffer = !authLoading && !workroomIsUpgraded && isOfferActive;

	// Show invite modal with pre-selected providers
	useEffect(() => {
		if (preSelectedProviders.length > 0 && workroomIsUpgraded) {
			setIsInviteDialogOpen(true);
		}
	}, [preSelectedProviders, workroomIsUpgraded]);

	// Auto-select first conversation with activity
	useEffect(() => {
		if (!selectedConversationId && conversations.length > 0) {
			const activeConversation =
				conversations.find(
					(conv) => conv.unread_count > 0 || conv.hasResponse
				) || conversations[0];
			setSelectedConversationId(activeConversation.quote_id);
		}
	}, [conversations, selectedConversationId]);

	if (quotes.length === 0) {
		return (
			<div className="flex flex-col min-h-screen bg-gray-50 pb-12">
				<div className="p-4 pb-0">
					<Breadcrumbs
						showHome={false}
						items={[
							{ label: "Dashboard", href: "/dashboard" },
							{ label: "Service Requests", href: "/service-requests" },
							{ label: requestLabel, href: `/service-requests/${job.id}` }
						]}
						className="mb-4"
					/>
				</div>
				<div className="container mx-auto px-4 flex-1">
					<EmptyWorkroom
						job={job}
						onProviderInvited={handleProviderInvited}
						setIsOfferModalOpen={setIsOfferModalOpen}
						setOfferVariant={setOfferVariant}
					/>
				</div>
			</div>
		);
	}

	const requiredActions = () => {
		if (job.status === JobStatus.CANCELLED) {
			return null;
		}

		/* Required Action Alerts - moved below header */
		if (
			quotesAwaitingReview.length > 0 ||
			providersRequestingInfo.length > 0 ||
			(pendingQuotes.length > 0 && providersRequestingInfo.length === 0)
		) {
			return (
				<div className="space-y-3 mb-6">
					{quotesAwaitingReview.length > 0 && (
						<Alert variant="info" className="border-green-200 bg-green-50">
							<CheckCircle className="h-4 w-4 text-green-600" />
							<AlertTitle className="text-green-800">
								{quotesAwaitingReview.length} Response
								{quotesAwaitingReview.length > 1 ? "s" : ""} Ready for Review
							</AlertTitle>
							<AlertDescription className="flex justify-between items-start">
								<span className="text-green-700">
									You have {quotesAwaitingReview.length} response
									{quotesAwaitingReview.length > 1 ? "s" : ""} from provider
									{quotesAwaitingReview.length > 1 ? "s" : ""} waiting for your
									review. Accept or reject{" "}
									{quotesAwaitingReview.length > 1 ? "them" : "it"} to move
									forward with your service request.
								</span>
								<Button
									variant="default"
									size="sm"
									onClick={() => {
										const quotesSection =
											document.getElementById("quotes-section");
										if (quotesSection) {
											quotesSection.scrollIntoView({ behavior: "smooth" });
										}
									}}
								>
									Review Responses
								</Button>
							</AlertDescription>
						</Alert>
					)}

					{providersRequestingInfo.length > 0 && (
						<Alert variant="info" className="border-blue-200 bg-blue-50">
							<MessageSquare className="h-4 w-4 text-blue-600" />
							<AlertTitle className="text-blue-800">
								{providersRequestingInfo.length} Provider
								{providersRequestingInfo.length > 1 ? "s" : ""} Requesting
								Information
							</AlertTitle>
							<AlertDescription className="flex justify-between items-center">
								<span className="text-blue-700">
									{providersRequestingInfo.length} provider
									{providersRequestingInfo.length > 1 ? "s" : ""}{" "}
									{providersRequestingInfo.length > 1 ? "have" : "has"}{" "}
									requested additional information from you. Check your messages
									and respond to help{" "}
									{providersRequestingInfo.length > 1 ? "them" : "them"} provide
									accurate responses.
								</span>
								<Button
									variant="default"
									size="sm"
									onClick={() => {
										setActiveTab("inbox");
									}}
								>
									View Messages
								</Button>
							</AlertDescription>
						</Alert>
					)}

					{pendingQuotes.length > 0 && providersRequestingInfo.length === 0 && (
						<Alert variant="info" className="border-amber-200 bg-amber-50">
							<AlertCircle className="h-4 w-4 text-amber-600" />
							<AlertTitle className="text-amber-800">
								Awaiting Provider Response
								{pendingQuotes.length > 1 ? "s" : ""}
							</AlertTitle>
							<AlertDescription>
								<span className="text-amber-700">
									Your service request is being reviewed by{" "}
									{pendingQuotes.length} provider
									{pendingQuotes.length > 1 ? "s" : ""}. You'll receive{" "}
									{pendingQuotes.length > 1 ? "responses" : "a response"} soon.
								</span>
							</AlertDescription>
						</Alert>
					)}
				</div>
			);
		}
		return null;
	};

	return (
		<div className="flex flex-col min-h-screen bg-gray-50 pb-12">
			<div className="container mx-auto">
				<Breadcrumbs
					showHome={false}
					items={[
						{ label: "Dashboard", href: "/dashboard" },
						{ label: "Service Requests", href: "/service-requests" },
						{ label: requestLabel, href: `/service-requests/${job.id}` }
					]}
					className="my-4"
				/>

				<UpgradeWorkroomCTA
					job={job}
					setIsOfferModalOpen={setIsOfferModalOpen}
					setOfferVariant={setOfferVariant}
				/>

				{requiredActions()}

				<div className="flex-1 flex flex-col">
					<Card className="mb-6 shadow-sm">
						<CardHeader className="pb-0">
							<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
								<div className="col-span-2">
									<WorkroomHeader job={job} />
								</div>
								<div className="col-span-1 hidden md:block">
									<Card className="bg-gray-50 border-dashed">
										<CardContent className="space-y-4">
											<h3 className="text-lg font-semibold mb-2 mt-4 flex items-center gap-2">
												{!workroomIsUpgraded && (
													<Lock className="h-5 w-5 text-amber-500" />
												)}
												Premium Actions
											</h3>
											<div className="flex items-start flex-col gap-3">
												<Button
													disabled={
														job.status === JobStatus.COMPLETED ||
														job.status === JobStatus.CANCELLED
													}
													onClick={() => {
														if (!workroomIsUpgraded) {
															setOfferVariant?.("invite-providers");
															setIsOfferModalOpen(true);
														} else if (workroomIsUpgraded) {
															setIsInviteDialogOpen(true);
														}
													}}
													className={`w-full flex items-center gap-2 shrink-0 transition-all duration-200 ${
														!workroomIsUpgraded
															? "bg-gradient-to-r from-yellow-400 to-amber-500 hover:from-yellow-500 hover:to-amber-600 text-white shadow-lg hover:shadow-xl"
															: "bg-green-700 hover:bg-green-800"
													}`}
													title={
														!workroomIsUpgraded
															? "Upgrade to invite more providers"
															: "Invite More Providers"
													}
												>
													<UserPlus className="h-4 w-4" />
													Invite More Providers
												</Button>
												<Button
													variant="outline"
													disabled={job.status === JobStatus.CANCELLED}
													onClick={() => {
														if (!workroomIsUpgraded) {
															setOfferVariant?.("pro-support");
															setIsOfferModalOpen(true);
														} else if (workroomIsUpgraded) {
															setShowSupportModal(true);
														}
													}}
													className={`w-full flex items-center gap-2 shrink-0 transition-all duration-200 ${
														!workroomIsUpgraded
															? "border-2 border-amber-400 text-amber-600 bg-white hover:bg-amber-50 hover:border-amber-500 shadow-sm hover:shadow-md"
															: ""
													}`}
													title={
														!workroomIsUpgraded
															? "Upgrade to contact support"
															: "Contact support"
													}
												>
													<HeadphonesIcon className="h-4 w-4" />
													Contact Support
												</Button>
											</div>
										</CardContent>
									</Card>

									{/* Job Actions Alert */}
									{(job.status === "OPEN" ||
										job.status === "ASSIGNED" ||
										job.status === "IN_PROGRESS") && (
										<div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
											<div className="flex items-start">
												<div className="flex-shrink-0">
													<svg
														className="h-5 w-5 text-amber-400"
														viewBox="0 0 20 20"
														fill="currentColor"
													>
														<path
															fillRule="evenodd"
															d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
															clipRule="evenodd"
														/>
													</svg>
												</div>
												<div className="ml-3 flex-1">
													<p className="text-sm text-amber-800">
														Need to complete or cancel this job?
													</p>
													<div className="mt-2 flex gap-2">
														<button
															onClick={() =>
																handleStatusButtonClick("complete")
															}
															className="text-xs font-medium text-green-600 hover:text-green-700 underline"
														>
															Mark as Completed
														</button>
														<span className="text-amber-600">•</span>
														<button
															onClick={() => handleStatusButtonClick("cancel")}
															className="text-xs font-medium text-gray-600 hover:text-gray-700 underline"
														>
															Cancel Job
														</button>
													</div>
												</div>
											</div>
										</div>
									)}
								</div>
							</div>
						</CardHeader>
					</Card>

					{/* Mobile Premium Actions Card */}
					<Card className="mb-6 shadow-sm block md:hidden">
						<CardContent className="p-4">
							<h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
								{!workroomIsUpgraded && (
									<Lock className="h-5 w-5 text-amber-500" />
								)}
								Premium Actions
							</h3>
							<div className="flex flex-col gap-3">
								<Button
									onClick={() => {
										if (!workroomIsUpgraded) {
											setOfferVariant?.("invite-providers");
											setIsOfferModalOpen(true);
										} else if (workroomIsUpgraded) {
											setIsInviteDialogOpen(true);
										}
									}}
									className={`w-full flex items-center gap-2 shrink-0 transition-all duration-200 ${
										!workroomIsUpgraded
											? "bg-gradient-to-r from-yellow-400 to-amber-500 hover:from-yellow-500 hover:to-amber-600 text-white shadow-lg hover:shadow-xl"
											: "bg-green-700 hover:bg-green-800"
									}`}
									title={
										!workroomIsUpgraded
											? "Upgrade to invite more providers"
											: "Invite More Providers"
									}
								>
									<UserPlus className="h-4 w-4" />
									Invite More Providers
								</Button>
								<Button
									variant="outline"
									onClick={() => {
										if (!workroomIsUpgraded) {
											setOfferVariant?.("pro-support");
											setIsOfferModalOpen(true);
										} else if (workroomIsUpgraded) {
											setShowSupportModal(true);
										}
									}}
									className={`w-full flex items-center gap-2 shrink-0 transition-all duration-200 ${
										!workroomIsUpgraded
											? "border-2 border-amber-400 text-amber-600 bg-white hover:bg-amber-50 hover:border-amber-500 shadow-sm hover:shadow-md"
											: ""
									}`}
									title={
										!workroomIsUpgraded
											? "Upgrade to contact support"
											: "Contact support"
									}
								>
									<HeadphonesIcon className="h-4 w-4" />
									Contact Support
								</Button>
							</div>
						</CardContent>
					</Card>

					{/* Job Actions Alert - Mobile */}
					{(job.status === "OPEN" ||
						job.status === "ASSIGNED" ||
						job.status === "IN_PROGRESS") && (
						<div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg block md:hidden">
							<div className="flex items-start">
								<div className="flex-shrink-0">
									<svg
										className="h-5 w-5 text-amber-400"
										viewBox="0 0 20 20"
										fill="currentColor"
									>
										<path
											fillRule="evenodd"
											d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
											clipRule="evenodd"
										/>
									</svg>
								</div>
								<div className="ml-3 flex-1">
									<p className="text-sm text-amber-800">
										Need to complete or cancel this job?
									</p>
									<div className="mt-2 flex gap-2">
										<button
											onClick={() => handleStatusButtonClick("complete")}
											className="text-xs font-medium text-green-600 hover:text-green-700 underline"
										>
											Mark as Completed
										</button>
										<span className="text-amber-600">•</span>
										<button
											onClick={() => handleStatusButtonClick("cancel")}
											className="text-xs font-medium text-gray-600 hover:text-gray-700 underline"
										>
											Cancel Job
										</button>
									</div>
								</div>
							</div>
						</div>
					)}

					{/* Main Content - Tabbed Interface */}
					<Card className="flex-1 shadow-sm">
						<CardContent className="p-0">
							<Tabs
								value={activeTab}
								onValueChange={setActiveTab}
								className="h-full"
							>
								<div className="border-b px-6 py-4">
									<TabsList className="grid w-full grid-cols-2 lg:w-auto lg:grid-cols-2">
										<TabsTrigger
											value="invitations"
											className="flex items-center gap-2"
										>
											<CheckCircle2 className="h-4 w-4" />
											Invitations
											{conversationStats.totalProposals > 0 && (
												<Badge
													variant="secondary"
													className="ml-1 px-1.5 py-0.5 text-xs"
												>
													{conversationStats.totalProposals}
												</Badge>
											)}
										</TabsTrigger>
										<TabsTrigger
											value="inbox"
											className="flex items-center gap-2"
										>
											<MessageCircle className="h-4 w-4" />
											Inbox
											{conversationStats.totalUnread > 0 && (
												<Badge
													variant="destructive"
													className="ml-1 px-1.5 py-0.5 text-xs"
												>
													{conversationStats.totalUnread}
												</Badge>
											)}
										</TabsTrigger>
									</TabsList>
								</div>

								<TabsContent value="invitations" className="mt-0 h-full">
									<div className="p-6 pb-0">
										<div className="mb-4 w-full max-w-xs">
											<label
												htmlFor="quote-filter"
												className="block mb-1 text-sm font-medium text-gray-700"
											>
												Filter Responses
											</label>
											<Select
												value={quoteFilter}
												onValueChange={handleFilterChange}
											>
												<SelectTrigger id="quote-filter" className="w-full">
													<SelectValue placeholder="Filter responses" />
												</SelectTrigger>
												<SelectContent>
													{availableFilters.map(([key, _]) => (
														<SelectItem key={key} value={key}>
															{QUOTE_STATUS_GROUPS[key as QuoteFilterKey].label}{" "}
															({quoteCounts[key as QuoteFilterKey]})
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</div>

										<QuotesList
											job={job}
											quotes={filteredQuotes}
											fetchJob={fetchJob}
										/>
									</div>
								</TabsContent>

								<TabsContent value="inbox" className="mt-0 h-full">
									<div className="flex flex-col h-full">
										<div className="grid grid-cols-1 lg:grid-cols-3 flex-1 min-h-0">
											{/* Conversations List */}
											<div className="lg:col-span-1 border-r flex flex-col">
												<div className="p-4 border-b flex-shrink-0">
													<h3 className="font-semibold text-lg">
														Active Conversations
													</h3>
													<p className="text-sm text-gray-600">
														{conversationStats.activeConversations} active •{" "}
														{conversationStats.totalUnread} unread
													</p>
												</div>
												<div className="flex-1 overflow-hidden">
													<ScrollArea className="h-full">
														<div className="p-2">
															{conversations.map((conversation) => (
																<div
																	key={conversation.quote_id}
																	className={`p-3 rounded-lg cursor-pointer transition-all duration-200 mb-2 ${
																		selectedConversationId ===
																		conversation.quote_id
																			? "bg-blue-50 border border-blue-200"
																			: "hover:bg-gray-50 border border-transparent"
																	}`}
																	onClick={() =>
																		setSelectedConversationId(
																			conversation.quote_id
																		)
																	}
																>
																	<div className="flex items-start gap-3">
																		<div className="relative">
																			<Avatar className="h-10 w-10">
																				{conversation.providerImage ? (
																					<AvatarImage
																						src={conversation.providerImage}
																						alt={conversation.providerName}
																					/>
																				) : (
																					<AvatarFallback>
																						{conversation.providerName[0]}
																					</AvatarFallback>
																				)}
																			</Avatar>
																			{conversation.unread_count > 0 && (
																				<div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
																					{conversation.unread_count}
																				</div>
																			)}
																		</div>
																		<div className="flex-1 min-w-0">
																			<div className="flex items-center justify-between mb-1">
																				<h4 className="font-medium text-sm truncate">
																					{conversation.businessName ||
																						conversation.providerName}
																				</h4>
																				<div className="flex items-center gap-1">
																					{conversation.isAccepted && (
																						<CheckCircle className="h-4 w-4 text-green-600" />
																					)}
																					{conversation.hasResponse &&
																						!conversation.isAccepted && (
																							<Badge
																								variant="outline"
																								className="text-xs"
																							>
																								Proposal
																							</Badge>
																						)}
																				</div>
																			</div>
																			{conversation.lastMessage && (
																				<p className="text-xs text-gray-600 line-clamp-2 mb-1">
																					{conversation.lastMessage}
																				</p>
																			)}
																			<div className="flex items-center justify-between">
																				<span className="text-xs text-gray-500">
																					{formatDistanceToNow(
																						new Date(
																							conversation.last_message_at
																						),
																						{ addSuffix: true }
																					)}
																				</span>
																				<div className="flex items-center gap-1">
																					{conversation.quote.status ===
																						QuoteStatus.PENDING && (
																						<Badge
																							variant="secondary"
																							className="text-xs"
																						>
																							<Clock className="h-3 w-3 mr-1" />
																							Pending
																						</Badge>
																					)}
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
															))}
														</div>
													</ScrollArea>
												</div>
											</div>

											{/* Message Thread */}
											<div className="lg:col-span-2 flex flex-col min-h-0">
												{selectedConversationId ? (
													<>
														{/* Conversation Header */}
														<div className="border-b p-4 bg-white flex-shrink-0">
															{(() => {
																const conversation = conversations.find(
																	(c) => c.quote_id === selectedConversationId
																);
																if (!conversation) return null;

																return (
																	<div className="flex items-center justify-between">
																		<div className="flex items-center gap-3">
																			<Avatar className="h-10 w-10">
																				{conversation.providerImage ? (
																					<AvatarImage
																						src={conversation.providerImage}
																						alt={conversation.providerName}
																					/>
																				) : (
																					<AvatarFallback>
																						{conversation.providerName[0]}
																					</AvatarFallback>
																				)}
																			</Avatar>
																			<div>
																				<h3 className="font-medium">
																					{conversation.businessName ||
																						conversation.providerName}
																				</h3>
																				<p className="text-sm text-gray-600">
																					{conversation.quote.status ===
																					QuoteStatus.PENDING
																						? "Reviewing your request"
																						: conversation.isAccepted
																							? "Accepted Provider"
																							: conversation.hasResponse
																								? "Information Requested"
																								: "Invited"}
																				</p>
																			</div>
																		</div>

																		{/* Quick Actions */}
																		<div className="flex items-center gap-2">
																			<Button
																				size="sm"
																				variant="outline"
																				onClick={() => {
																					setActiveTab("invitations");
																				}}
																			>
																				<Phone className="h-4 w-4 mr-1" />
																				Call
																			</Button>
																		</div>
																	</div>
																);
															})()}
														</div>

														{/* Message Thread */}
														<div className="flex-1 min-h-0">
															<ConversationMessageThread
																quoteId={selectedConversationId}
																viewerRole="USER"
																userId={user?.id}
																isVisible={true}
																hideServiceRequestHeader={true}
															/>
														</div>
													</>
												) : (
													<div className="h-full flex items-center justify-center">
														<div className="text-center">
															<MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
															<h3 className="text-lg font-medium mb-2">
																Select a conversation
															</h3>
															<p className="text-gray-600">
																Choose a conversation to start messaging
															</p>
														</div>
													</div>
												)}
											</div>
										</div>
									</div>
								</TabsContent>
							</Tabs>
						</CardContent>
					</Card>

					<InviteProvidersModal
						isOpen={isInviteDialogOpen}
						onClose={() => setIsInviteDialogOpen(false)}
						jobId={job.id}
						category={job.category}
						latitude={job.location?.latitude || 0}
						longitude={job.location?.longitude || 0}
						quotes={job.quotes}
						onInvited={handleProviderInvited}
						preSelectedProviders={preSelectedProviders}
					/>
					<SupportModal
						open={showSupportModal}
						onOpenChange={() => setShowSupportModal(false)}
						isPremiumFeature={true}
					/>
					<JobStatusModal
						isOpen={showStatusModal}
						onClose={() => setShowStatusModal(false)}
						job={job}
						action={statusAction}
						onSuccess={handleStatusSuccess}
					/>
				</div>
			</div>
		</div>
	);
};

export default SelectProviderRoom;
