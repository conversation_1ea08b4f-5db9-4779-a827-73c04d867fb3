"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { UserPlus, Lock } from "lucide-react"
import { JobWithUserAndLocation, QuoteWithListing } from "@/types/global"
import InviteProvidersModal from "@/components/global-modals/InviteProvidersModal"
import { useAuth } from "@/lib/hooks/useAuth"


type EmptyWorkroomProps = {
    onProviderInvited: (quotes: QuoteWithListing[]) => void
    job: JobWithUserAndLocation
    setIsOfferModalOpen?: (isOpen: boolean) => void
    setOfferVariant?: (variant: "invite-providers" | "pro-support") => void
}

export default function EmptyWorkroom({ onProviderInvited, job, setIsOfferModalOpen, setOfferVariant }: EmptyWorkroomProps) {
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false)
  const { isPaid } = useAuth()
  
  const workroomIsUpgraded = job.is_premium || isPaid

  return (
    <div className="border rounded-md p-8 bg-white">
      <div className="flex flex-col items-center text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6">
          <UserPlus className="w-8 h-8 text-gray-400" />
        </div>

        <h2 className="text-xl font-semibold mb-2 flex items-center justify-center gap-2">
          {!workroomIsUpgraded && <Lock className="h-5 w-5 text-amber-500" />}
          No providers yet
        </h2>

        <p className="text-gray-500 max-w-md mb-6">
          This workroom doesn't have any service providers yet. {!workroomIsUpgraded ? "Upgrade to Pro to invite" : "Invite"} providers to start collaborating on your service
          request.
        </p>

        <Button
          onClick={() => {
            if (!workroomIsUpgraded && setIsOfferModalOpen) {
              setOfferVariant?.("invite-providers");
              setIsOfferModalOpen(true);
            } else {
              setIsInviteDialogOpen(true);
            }
          }}
          className={`flex items-center gap-2 transition-all duration-200 ${
            !workroomIsUpgraded 
              ? "bg-gradient-to-r from-yellow-400 to-amber-500 hover:from-yellow-500 hover:to-amber-600 text-white shadow-lg hover:shadow-xl" 
              : "bg-green-700 hover:bg-green-800"
          }`}
          title={!workroomIsUpgraded ? "Upgrade to invite providers" : "Invite Your First Provider"}
        >
          <UserPlus className="h-5 w-5" />
          Invite Your First Provider
        </Button>

      </div>
        <InviteProvidersModal
          isOpen={isInviteDialogOpen}
          onClose={() => setIsInviteDialogOpen(false)}
          jobId={job.id}
          category={job.category}
          latitude={job.location?.latitude as number || 0}
          longitude={job.location?.longitude as number || 0}
          onInvited={(quotes) => {
            onProviderInvited(quotes);
          }}
        />      
    </div>
  )
}
