"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { formatDate, formatDistanceToNow } from "date-fns";

import {
	JobWithUserAndLocation,
	ListingWithLocation,
	QuoteWithListing
} from "@/types/global";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
	Tooltip,
	TooltipProvider,
	TooltipTrigger
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { QuoteStatus, RVHelpVerificationLevel } from "@rvhelp/database";
import {
	Calendar,
	CheckCircle,
	Loader2,
	MapPin,
	MessageSquare,
	PhoneCall,
	Star
} from "lucide-react";
import React from "react";

type QuoteCardProps = {
	quote: QuoteWithListing;
	job?: JobWithUserAndLocation;
	handleOpenMessageDrawer: (providerId: string) => void;
	handleViewDetails: (provider: ListingWithLocation) => void;
	handleAcceptQuote?: (quoteId: string) => void;
	handleRejectQuote?: (quoteId: string) => void;
	handleCall?: (providerId: string) => void;
	isProcessing?: boolean;
	disabled?: boolean;
};

const QuoteCard = ({
	quote,
	job,
	handleOpenMessageDrawer,
	handleViewDetails,
	handleAcceptQuote,
	handleRejectQuote,
	handleCall,
	isProcessing = false,
	disabled = false
}: QuoteCardProps) => {
	const getStatusInfo = () => {
		switch (quote.status) {
			case QuoteStatus.PENDING:
				return {
					text: "Invited",
					dotColor: "bg-blue-500",
					textColor: "text-blue-700",
					cardBorder: "border-l-blue-300"
				};
			case QuoteStatus.ACCEPTED:
				// Only show "Accepted" if customer has actually accepted this specific quote
				if (job?.accepted_quote_id === quote.id) {
					return {
						text: "Accepted",
						dotColor: "bg-emerald-500",
						textColor: "text-emerald-700",
						cardBorder: "border-l-emerald-300"
					};
				} else {
					// Provider accepted but customer hasn't - show as "Response Received"
					return {
						text: "Response Received",
						dotColor: "bg-green-500",
						textColor: "text-green-700",
						cardBorder: "border-l-green-300"
					};
				}
			case QuoteStatus.REJECTED:
				return {
					text: "Provider Declined",
					dotColor: "bg-red-500",
					textColor: "text-red-700",
					cardBorder: "border-l-red-300"
				};
			case QuoteStatus.CUSTOMER_REJECTED:
				return {
					text: "Proposal Declined",
					dotColor: "bg-orange-500",
					textColor: "text-orange-700",
					cardBorder: "border-l-orange-300"
				};
			case QuoteStatus.WITHDRAWN:
				return {
					text: "Withdrawn",
					dotColor: "bg-red-500",
					textColor: "text-red-700",
					cardBorder: "border-l-red-300"
				};
			case QuoteStatus.EXPIRED:
				return {
					text: "Expired",
					dotColor: "bg-gray-500",
					textColor: "text-gray-700",
					cardBorder: "border-l-gray-300"
				};
			default:
				return {
					text: "",
					dotColor: "bg-gray-500",
					textColor: "text-gray-700",
					cardBorder: "border-l-gray-300"
				};
		}
	};

	const [showAcceptSheet, setShowAcceptSheet] = React.useState(false);
	const [showPhoneModal, setShowPhoneModal] = React.useState(false);

	const formatRejectionReason = (reason: string) => {
		// Reason mapping for better UX
		const rejectionReasonOptions = [
			{ value: "too_busy", label: "Too Busy / At Capacity" },
			{ value: "schedule_conflict", label: "Schedule conflict" },
			{ value: "outside_travel_area", label: "Too far away" },
			{ value: "not_a_good_fit", label: "Not my specialty" },
			{ value: "other", label: "Other reason" }
		];

		return (
			rejectionReasonOptions.find((r) => r.value === reason)?.label || reason
		);
	};

	const renderStarRating = () => {
		const rating = quote.listing.rating || 0;
		const roundedRating = Math.round(rating);
		return (
			<div className="flex items-center gap-1">
				{[1, 2, 3, 4, 5].map((star) => (
					<Star
						key={star}
						className={`w-4 h-4 ${star <= roundedRating ? "text-yellow-400 fill-yellow-400" : "text-gray-200"}`}
					/>
				))}
				<span className="text-sm text-gray-600 ml-1">
					({quote.listing.num_reviews || 0})
				</span>
			</div>
		);
	};

	const renderQuoteDetails = () => {
		if (
			quote.preferred_date ||
			quote.dispatch_fee ||
			quote.hourly_rate ||
			quote.provider_notes ||
			quote.status === QuoteStatus.ACCEPTED
		) {
			return (
				<div className="my-4 rounded-md bg-green-50 border border-green-200 px-4 py-3">
					{quote.provider_notes ? (
						<blockquote className="border-l-4 border-green-400 pl-4 italic text-gray-700">
							'{quote.provider_notes}'
						</blockquote>
					) : (
						quote.status === QuoteStatus.ACCEPTED && (
							<div className="text-green-700 font-medium">
								This provider has responded to your service request and is ready
								to help.
							</div>
						)
					)}
				</div>
			);
		}

		if (quote.status === QuoteStatus.REJECTED) {
			return (
				<div className="my-4 rounded-lg bg-blue-50 border border-blue-200 px-4 py-3">
					<div className="text-gray-700 font-medium mb-2">
						This provider is not available to help with your service request.
					</div>
					<div className="text-gray-600 text-sm italic">
						Reason: {formatRejectionReason(quote.rejection_reason)}
					</div>
					{quote.rejection_reason_details && (
						<div className="text-gray-600 text-sm italic">
							Provider notes: {quote.rejection_reason_details}
						</div>
					)}
				</div>
			);
		}

		if (quote.status === QuoteStatus.CUSTOMER_REJECTED) {
			return (
				<div className="my-4 rounded-lg bg-orange-50 border border-orange-200 px-4 py-3">
					<div className="text-gray-700 font-medium mb-2">
						You rejected this provider's proposal.
					</div>
					<div className="text-gray-600 text-sm italic">
						This provider's quote was not selected for your service request.
					</div>
				</div>
			);
		}

		return null;
	};

	const statusInfo = getStatusInfo();

	return (
		<Card
			className={cn(
				`cursor-pointer hover:shadow-md transition-shadow duration-200`,
				// Mobile-first responsive styles
				"shadow-sm border-0 rounded-lg mx-0 md:shadow-md md:border md:rounded-lg",
				// Status-based left border color
				statusInfo.cardBorder,
				"border-l-4",
				quote.unread_messages_count ? "border-l-red-500" : "",
				disabled && "bg-gray-50 pointer-events-none"
			)}
			onClick={() => !disabled && handleViewDetails(quote.listing)}
		>
			<CardContent className="pt-4 px-4 pb-4 md:pt-6 md:px-6 md:pb-6">
				<div className="flex items-start gap-3 md:gap-4">
					{/* Provider Avatar */}
					<Avatar className="h-12 w-12 border flex-shrink-0 md:h-16 md:w-16 md:border-2 border-gray-100">
						{quote.listing.profile_image && (
							<AvatarImage
								src={quote.listing.profile_image}
								alt={quote.listing.business_name || ""}
							/>
						)}
						<AvatarFallback className="bg-primary text-white text-sm md:text-xl">
							{quote.listing.first_name?.[0] || "P"}
							{quote.listing.last_name?.[0] || ""}
						</AvatarFallback>
					</Avatar>

					{/* Provider Info */}
					<div className="flex-1 min-w-0">
						<div className="flex flex-col items-start gap-2 md:flex-row md:items-start md:justify-between md:gap-0">
							<div className="space-y-1 flex-1 min-w-0">
								<div className="flex flex-col items-start gap-1 md:flex-row md:gap-2">
									<div className="flex flex-col w-full md:w-auto">
										<div className="flex items-center gap-2">
											<h3 className="text-base font-medium leading-tight md:text-lg md:font-semibold">
												{quote.listing.first_name
													? `${quote.listing.first_name} ${quote.listing.last_name}`
													: quote.listing.business_name}
											</h3>

											<div className="self-start mt-0.5 md:self-auto md:mt-0 flex items-center gap-2">
												<div
													className={`w-2 h-2 rounded-full ${statusInfo.dotColor}`}
												></div>
												<span
													className={`text-xs font-medium ${statusInfo.textColor} md:text-sm`}
												>
													{statusInfo.text}
												</span>
											</div>
										</div>
										<h4 className="text-xs text-gray-500 truncate md:text-sm">
											{quote.listing.business_name}
										</h4>
									</div>
								</div>
								<div className="mt-2 md:mt-0">{renderStarRating()}</div>
							</div>
							<div className="flex flex-wrap gap-2 mt-2 md:mt-0 md:block md:space-y-2">
								{quote.listing.rv_help_verification_level ===
									RVHelpVerificationLevel.VERIFIED && (
									<Badge
										variant="outline"
										className="bg-emerald-50 text-emerald-700 border-emerald-200 text-xs px-2 py-1 md:text-sm md:px-3 md:py-1"
									>
										<CheckCircle className="h-2 w-2 mr-1 md:h-3 md:w-3" />
										Verified Pro
									</Badge>
								)}
								{quote.listing.discount_dispatch_fee ||
								quote.listing.discount_hourly_rate
									? !quote.display_pricing && (
											<Badge
												variant="outline"
												className="bg-blue-50 text-blue-700 border-blue-200 whitespace-nowrap text-xs px-2 py-1 md:text-sm md:px-3 md:py-1"
											>
												<Star className="h-2 w-2 mr-1 fill-current md:h-3 md:w-3" />
												Pro Discounts Available
											</Badge>
										)
									: null}
							</div>
						</div>

						{/* Location and Description */}
						<div className="mt-2 space-y-1 md:mt-3 md:space-y-2">
							<div className="flex items-center gap-2 text-xs text-gray-600 md:text-sm">
								<MapPin className="h-3 w-3 flex-shrink-0 md:h-4 md:w-4" />
								<span className="truncate md:truncate-none">
									{quote.listing.location?.city},{" "}
									{quote.listing.location?.state}
								</span>
							</div>
							<p className="text-xs text-gray-600 line-clamp-3 md:text-sm md:line-clamp-2">
								{quote.listing.short_description ||
									quote.listing.long_description ||
									"No description available"}
							</p>
						</div>

						{/* Inline Quote Details Section */}
						<div className="mt-3 text-sm">{renderQuoteDetails()}</div>

						{quote.unread_message &&
							(quote.status !== QuoteStatus.PENDING ||
								(quote.status === QuoteStatus.PENDING &&
									quote.messages &&
									quote.messages.some &&
									quote.messages.some(
										(msg: any) => msg.sender_type === "PROVIDER"
									))) && (
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<div
												onClick={(e) => {
													e.stopPropagation();
													handleOpenMessageDrawer(quote.listing.id);
												}}
												className="my-2 rounded-md border border-secondary bg-secondary-light px-3 py-2 cursor-pointer md:my-3 md:px-4 md:py-3"
											>
												<div className="flex items-center justify-between mb-0 md:mb-1">
													<span className="text-sm font-medium md:text-base md:font-semibold">
														New Message
														{quote.unread_message.created_at && (
															<span className="block text-xs font-normal text-gray-500 md:inline md:text-sm md:font-semibold">
																{" "}
																{formatDistanceToNow(
																	new Date(quote.unread_message.created_at)
																)}{" "}
																ago
															</span>
														)}
													</span>
													<span className="ml-2 rounded bg-secondary px-1.5 py-0.5 text-xs font-semibold text-white md:px-2">
														Unread
													</span>
												</div>
												<div className="line-clamp-2 text-sm mt-1 md:text-base md:mt-0">
													{quote.unread_message.content}
												</div>
											</div>
										</TooltipTrigger>
									</Tooltip>
								</TooltipProvider>
							)}

						{/* Action Buttons */}
						<div className="mt-3 flex flex-col gap-3 border-t pt-3 md:mt-4 md:flex-row md:justify-between md:gap-2 md:pt-4">
							{/* Primary action buttons (Accept/Reject) - shown first on mobile */}
							<div className="flex gap-2 order-1 md:order-2">
								{quote.status === QuoteStatus.ACCEPTED && (
									<>
										<Button
											variant="destructive"
											className="flex-1 h-12 text-base font-medium md:flex-initial md:h-auto md:text-sm md:font-normal"
											onClick={(e) => {
												e.stopPropagation();
												if (handleRejectQuote) handleRejectQuote(quote.id);
											}}
											disabled={isProcessing || disabled}
										>
											{isProcessing ? (
												<Loader2 className="h-4 w-4 animate-spin mr-2" />
											) : null}
											Reject
										</Button>
										<Button
											variant="default"
											className="flex-1 h-12 text-base font-medium md:flex-initial md:h-auto md:text-sm md:font-normal"
											onClick={(e) => {
												e.stopPropagation();
												if (handleAcceptQuote) handleAcceptQuote(quote.id);
											}}
											disabled={isProcessing}
										>
											{isProcessing ? (
												<Loader2 className="h-4 w-4 animate-spin mr-2" />
											) : null}
											Accept
										</Button>
									</>
								)}
							</div>

							{/* Communication buttons - shown second on mobile */}
							{quote.status !== QuoteStatus.REJECTED && !disabled && (
								<div className="flex gap-2 order-2 md:order-1">
									<TooltipProvider>
										<Tooltip>
											<TooltipTrigger asChild>
												<Button
													variant="outline"
													className="flex-1 h-12 text-base font-medium hover:bg-gray-50 hover:text-gray-900 md:flex-initial md:h-auto md:text-sm md:font-normal"
													onClick={(e) => {
														e.stopPropagation();
														if (handleCall) {
															handleCall(quote.listing.id);
														} else {
															setShowPhoneModal(true);
														}
													}}
													disabled={isProcessing || disabled}
												>
													<PhoneCall className="h-4 w-4 mr-2" />
													Call
												</Button>
											</TooltipTrigger>
										</Tooltip>
									</TooltipProvider>
									<TooltipProvider>
										<Tooltip>
											<TooltipTrigger asChild>
												<Button
													variant="secondary"
													className="relative flex-1 h-12 text-base font-medium hover:bg-gray-200 hover:text-gray-900 md:flex-initial md:h-auto md:text-sm md:font-normal"
													onClick={(e) => {
														e.stopPropagation();
														handleOpenMessageDrawer(quote.listing.id);
													}}
													disabled={isProcessing || disabled}
												>
													<MessageSquare className="h-4 w-4 mr-2" />
													Message
													{quote.messages_count > 0 && (
														<span className="absolute -top-1 -right-1 bg-amber-700 text-white text-sm rounded-full h-6 w-6 flex items-center justify-center font-bold md:h-5 md:w-5 md:text-xs">
															{quote.messages_count}
														</span>
													)}
												</Button>
											</TooltipTrigger>
										</Tooltip>
									</TooltipProvider>
								</div>
							)}
						</div>
					</div>
				</div>
			</CardContent>
			<Sheet open={showAcceptSheet} onOpenChange={setShowAcceptSheet}>
				<SheetContent className="w-[90vw] sm:max-w-[600px] overflow-y-auto">
					<div className="space-y-6">
						<div className="relative h-48 -mx-6 -mt-6 mb-6 bg-gradient-to-b from-gray-800 to-gray-700">
							<div className="absolute inset-0 flex items-center justify-center">
								<Avatar className="h-24 w-24 border-4 border-white">
									{quote.listing.profile_image && (
										<AvatarImage
											src={quote.listing.profile_image}
											alt={quote.listing.business_name || ""}
										/>
									)}
									<AvatarFallback className="text-3xl bg-primary text-white">
										{quote.listing.first_name?.[0] || "P"}
										{quote.listing.last_name?.[0] || ""}
									</AvatarFallback>
								</Avatar>
							</div>
						</div>

						<div className="space-y-6">
							<div>
								<h2 className="text-2xl font-semibold mb-2">
									{quote.listing.business_name ||
										`${quote.listing.first_name || ""} ${quote.listing.last_name || ""}`}
								</h2>
								<p className="text-gray-500">
									Are you sure you want to accept this quote?
								</p>
							</div>

							{quote.dispatch_fee && (
								<Card>
									<CardHeader>
										<CardTitle>Quote Details</CardTitle>
									</CardHeader>
									<CardContent className="space-y-4">
										<div className="flex justify-between items-center">
											<span className="text-gray-500">Dispatch Fee</span>
											<span className="text-xl font-semibold">
												${quote.dispatch_fee?.toFixed(2)}
											</span>
										</div>
										{quote.preferred_date && (
											<div className="flex justify-between items-center">
												<span className="text-gray-500">Scheduled Date</span>
												<div className="flex items-center gap-2">
													<Calendar className="h-4 w-4 text-gray-500" />
													<span>
														{formatDate(
															quote.preferred_date.toISOString(),
															"yyyy-MM-dd"
														)}
														{quote.preferred_time_slot && (
															<span className="text-gray-500">
																{" "}
																({quote.preferred_time_slot})
															</span>
														)}
													</span>
												</div>
											</div>
										)}
									</CardContent>
								</Card>
							)}

							<div className="flex gap-3 mt-6">
								<Button
									variant="outline"
									className="flex-1"
									onClick={() => setShowAcceptSheet(false)}
									disabled={disabled}
								>
									Cancel
								</Button>
								<Button className="flex-1" disabled={disabled}>
									<CheckCircle className="h-4 w-4 mr-2" />
									Accept Quote
								</Button>
							</div>
						</div>
					</div>
				</SheetContent>
			</Sheet>

			{/* Phone Modal */}
			<Dialog open={showPhoneModal} onOpenChange={setShowPhoneModal}>
				<DialogContent className="sm:max-w-md">
					<DialogHeader>
						<DialogTitle>
							Contact{" "}
							{quote.listing.business_name ||
								`${quote.listing.first_name} ${quote.listing.last_name}`}
						</DialogTitle>
					</DialogHeader>
					<div className="space-y-4">
						<div className="text-center">
							<PhoneCall className="h-8 w-8 mx-auto mb-2 text-primary" />
							<p className="text-sm text-gray-600 mb-2">Phone Number</p>
							<p className="text-2xl font-semibold">
								{quote.listing.phone || "Phone number not available"}
							</p>
						</div>
						{quote.listing.phone && (
							<div className="text-center">
								<a
									href={`tel:${quote.listing.phone}`}
									className="inline-flex items-center justify-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
								>
									<PhoneCall className="h-4 w-4 mr-2" />
									Call Now
								</a>
							</div>
						)}
					</div>
				</DialogContent>
			</Dialog>
		</Card>
	);
};

export default QuoteCard;
