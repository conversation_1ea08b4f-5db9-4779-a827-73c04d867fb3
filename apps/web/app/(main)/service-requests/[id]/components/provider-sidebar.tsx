"use client"

import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { MessageSquare } from 'lucide-react'
import type { User } from "@prisma/client"

type ServiceProvider = User & {
  rating: number
  num_reviews: number
  invited_at: string
  status: "invited" | "accepted" | "declined"
}

type ProviderSidebarProps = {
  providers: ServiceProvider[]
  selectedProviderId: string | null
  onSelectProvider: (providerId: string) => void
}

export default function ProviderSidebar({ providers, selectedProviderId, onSelectProvider }: ProviderSidebarProps) {
  return (
    <div className="w-64 border-r bg-white overflow-y-auto hidden md:block">
      <div className="p-4 border-b">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-4 w-4 text-gray-500" />
          <h2 className="font-medium text-sm text-gray-500">CONVERSATIONS</h2>
        </div>
      </div>

      <div className="divide-y">
        {providers.map((provider) => (
          <div
            key={provider.id}
            className={cn(
              "p-4 cursor-pointer hover:bg-gray-50 transition-colors",
              selectedProviderId === provider.id && "bg-gray-50",
            )}
            onClick={() => onSelectProvider(provider.id)}
          >
            <div className="flex items-start gap-3">
              <Avatar className="h-10 w-10">
                <AvatarFallback>
                  {provider.first_name?.[0] || "P"}
                  {provider.last_name?.[0] || ""}
                </AvatarFallback>
              </Avatar>

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between gap-2">
                  <p className="font-medium text-sm truncate">
                    {provider.first_name} {provider.last_name}
                  </p>
                  <Badge
                    variant={
                      provider.status === "invited"
                        ? "outline"
                        : provider.status === "accepted"
                          ? "success"
                          : "destructive"
                    }
                    className="text-xs"
                  >
                    {provider.status === "invited" ? "Invited" : provider.status === "accepted" ? "Active" : "Declined"}
                  </Badge>
                </div>

                <div className="flex items-center mt-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <svg
                      key={star}
                      className={`w-3 h-3 ${star <= (provider.rating || 0) ? "text-yellow-400" : "text-gray-300"}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                  <span className="ml-1 text-xs text-gray-500">{provider.rating || 0}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
