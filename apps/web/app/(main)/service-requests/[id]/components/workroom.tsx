"use client";

import ConversationMessageThread from "@/components/conversations/ConversationMessageThread";
import { JobStatusModal } from "@/components/modals/JobStatusModal";
import OEMLogo from "@/components/oem/oem-logo";
import { PhoneModal } from "@/components/shared/PhoneModal";
import { TimelineComponent } from "@/components/timeline/TimelineComponent";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Breadcrumbs } from "@/components/ui/breadcrumbs";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import UpgradeWorkroomCTA from "@/components/upgrade/UpgradeWorkroomCTA";
import { getCategoryName } from "@/lib/categories";
import { useAuth } from "@/lib/hooks/useAuth";
import { renderStarRating } from "@/lib/utils/star-rating";
import type { JobWithUserAndLocation, QuoteWithListing } from "@/types/global";
import { JobStatus, QuoteStatus } from "@rvhelp/database";
import { formatDate } from "date-fns";
import {
	CheckCircle,
	MapPin,
	MessageSquare,
	Phone,
	Star,
	Truck
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useState } from "react";
import JobDetailsModal from "./job-details-modal";

type QuoteWithLocation = QuoteWithListing & {
	location: {
		city: string;
		state: string;
	};
};

function getJobStatusBadge(status: JobStatus) {
	switch (status) {
		// assigned
		case JobStatus.ASSIGNED:
			return <Badge className="bg-blue-100 text-blue-700">In Progress</Badge>;
		// in progress
		case JobStatus.COMPLETED:
			return <Badge className="bg-green-100 text-green-700">Completed</Badge>;
		case JobStatus.IN_PROGRESS:
			return (
				<Badge className="bg-orange-100 text-orange-700">In Progress</Badge>
			);
	}
}

export default function Workroom({ job }: { job: JobWithUserAndLocation }) {
	const params = useParams();
	const id = params.id as string;
	const [activeTab, setActiveTab] = useState("overview");
	const [showPhoneModal, setShowPhoneModal] = useState(false);
	const [showDetailsModal, setShowDetailsModal] = useState(false);
	const [showStatusModal, setShowStatusModal] = useState(false);
	const [statusAction, setStatusAction] = useState<"complete" | "cancel">(
		"complete"
	);
	const [currentJob, setCurrentJob] = useState(job);
	const { user } = useAuth();

	const handleStatusButtonClick = (action: "complete" | "cancel") => {
		setStatusAction(action);
		setShowStatusModal(true);
	};

	const handleStatusSuccess = () => {
		// Refresh job data by fetching it again
		window.location.reload();
	};
	if (!currentJob) {
		return <div>Job not found</div>;
	}

	const requestLabel =
		`${currentJob.rv_year || ""} ${currentJob.rv_make || ""} ${currentJob.rv_model || ""} ${getCategoryName(currentJob.category)}`.trim();

	const acceptedQuote = currentJob.accepted_quote as QuoteWithLocation;

	return (
		<div className="flex flex-col min-h-screen bg-gray-50">
			<div className="container mt-4">
				<Breadcrumbs
					showHome={false}
					items={[
						{ label: "Dashboard", href: "/dashboard" },
						{ label: "Service Requests", href: "/service-requests" },
						{ label: requestLabel, href: `/service-requests/${id}` }
					]}
					className="mb-4"
					maxMobileItems={2}
					maxDesktopItems={4}
				/>

				<UpgradeWorkroomCTA
					job={currentJob}
					setIsOfferModalOpen={() => {}}
					setOfferVariant={() => {}}
				/>

				{/* Job Completion Alert */}
				{acceptedQuote.status === QuoteStatus.ACCEPTED &&
					acceptedQuote.completed_at && (
						<div className="mb-6">
							<div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
								<div className="flex items-start gap-3">
									<CheckCircle className="h-5 w-5 text-emerald-600 mt-0.5 flex-shrink-0" />
									<div className="flex-1">
										<h3 className="font-semibold text-emerald-800">
											Service Request Completed!
										</h3>
										<p className="text-emerald-700 text-sm mt-1">
											Your service request has been marked as completed by{" "}
											{acceptedQuote.listing.business_name ||
												`${acceptedQuote.listing.first_name} ${acceptedQuote.listing.last_name}`}
											.
											{acceptedQuote.completed_at &&
												` Completed on ${formatDate(new Date(acceptedQuote.completed_at), "EEEE, MMM d")}.`}
										</p>
										<div className="flex flex-col sm:flex-row gap-2 mt-3">
											<Link
												href={`/providers/${acceptedQuote.listing.slug}/review`}
											>
												<Button
													size="sm"
													className="bg-emerald-600 hover:bg-emerald-700"
												>
													<Star className="h-4 w-4 mr-2" />
													Leave a Review
												</Button>
											</Link>
											<Button
												size="sm"
												variant="outline"
												className="border-emerald-200 text-emerald-700 hover:bg-emerald-50"
												onClick={() => setActiveTab("communication")}
											>
												<MessageSquare className="h-4 w-4 mr-2" />
												Contact Provider
											</Button>
										</div>
									</div>
								</div>
							</div>
						</div>
					)}

				<div className="flex-1">
					{/* Mobile Review Section - Only show if job is completed */}
					{job.status === JobStatus.COMPLETED && (
						<Card className="mb-6 shadow-sm lg:hidden">
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<Star className="h-5 w-5 text-yellow-500" />
									Leave a Review
								</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<p className="text-sm text-gray-600">
									How was your experience with{" "}
									{acceptedQuote.listing.business_name ||
										`${acceptedQuote.listing.first_name} ${acceptedQuote.listing.last_name}`}
									? Your feedback helps other RV owners and supports quality
									service providers.
								</p>

								<Link
									href={`/providers/${acceptedQuote.listing.slug}/review`}
									className="inline-block w-full"
								>
									<Button className="w-full bg-emerald-600 hover:bg-emerald-700">
										<Star className="h-4 w-4 mr-2" />
										Write a Review
									</Button>
								</Link>

								<div className="text-xs text-gray-500 text-center">
									Share your experience to help other RV owners
								</div>
							</CardContent>
						</Card>
					)}

					{/* Project Header */}
					<Card className="mb-6 shadow-sm">
						<CardHeader className={`relative`}>
							<div className="space-y-4">
								<div className="flex flex-col lg:flex-row gap-6 lg:items-start">
									{job.warranty_request && (
										<div className="flex-shrink-0">
											{/* Active Project badge below started date */}
											<div className="mt-2">
												<Badge className={"bg-green-100 text-green-700 w-fit"}>
													<CheckCircle className="h-3 w-3 mr-1" />
													Active Project
												</Badge>
											</div>
											<OEMLogo
												company={job.warranty_request.company}
												alt_logo={false}
											/>
										</div>
									)}
									{/* Main content column */}
									<div className="flex-1 min-w-0">
										<div className="flex items-center gap-2">
											<h1 className={`text-2xl font-semibold`}>
												{requestLabel}
											</h1>
										</div>

										<div className="flex flex-wrap gap-x-6 gap-y-2 mt-2 text-sm">
											{job.location && (
												<div className="flex items-center">
													<MapPin className={`h-4 w-4 mr-1.5`} />
													<span className={`text-gray-600`}>
														{job.location.address}
													</span>
												</div>
											)}

											<div className="flex items-center">
												<Truck className={`h-4 w-4 mr-1.5`} />
												<span className={`text-gray-600`}>
													{job.rv_year} {job.rv_make} {job.rv_model}
												</span>
											</div>
										</div>

										{/* Request message preview */}
										<div className="mt-4">
											<div className="line-clamp-5 text-ellipsis overflow-hidden">
												<span className={`text-gray-700 whitespace-pre-line`}>
													{job.message}
												</span>
											</div>
										</div>
									</div>

									{/* Provider Card in header for lg+ screens */}
									<div className="mt-6 lg:mt-0 lg:ml-8 lg:w-[340px] flex-shrink-0">
										<Card className="shadow-lg">
											<CardHeader>
												<CardTitle>Your Provider</CardTitle>
											</CardHeader>
											<CardContent className="space-y-4">
												<div className="flex items-start gap-3">
													<Avatar className="h-12 w-12">
														{acceptedQuote.listing.profile_image && (
															<AvatarImage
																src={
																	acceptedQuote.listing.profile_image ||
																	"/placeholder.svg"
																}
																alt={acceptedQuote.listing.business_name || ""}
															/>
														)}
														<AvatarFallback className="bg-primary text-white">
															{acceptedQuote.listing.first_name?.[0] || "P"}
															{acceptedQuote.listing.last_name?.[0] || ""}
														</AvatarFallback>
													</Avatar>

													<div className="flex-1">
														<h4 className="font-semibold">
															{acceptedQuote.listing.business_name ||
																`${acceptedQuote.listing.first_name || ""} ${acceptedQuote.listing.last_name || ""}`}
														</h4>

														<div className="flex items-center gap-1 mt-1">
															{renderStarRating(
																acceptedQuote.listing.rating,
																acceptedQuote.listing.num_reviews
															)}
														</div>

														<div className="flex items-center gap-1 mt-2 text-sm text-gray-600">
															<MapPin className="h-3 w-3" />
															<span>
																{acceptedQuote.location.city},{" "}
																{acceptedQuote.location.state}
															</span>
														</div>
													</div>
												</div>

												<div className="grid grid-cols-2 gap-2 pt-4 border-t">
													<Button
														variant="outline"
														size="sm"
														className={`border-2`}
														onClick={() => setShowPhoneModal(true)}
													>
														<Phone className="h-4 w-4 mr-2" />
														Call
													</Button>
													<Button
														variant="outline"
														size="sm"
														className={`border-2`}
														onClick={() => setActiveTab("communication")}
													>
														<MessageSquare className="h-4 w-4 mr-2" />
														Message
													</Button>
												</div>
											</CardContent>
										</Card>
									</div>
									{/* End Provider Card in header */}
								</div>
							</div>
						</CardHeader>
					</Card>

					{/* Main Workroom Content */}
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
						{/* Left Column - Main Content */}
						<div className="lg:col-span-2 space-y-6">
							{/* Workroom Tabs */}
							<Card>
								<CardContent className="p-0">
									<Tabs value={activeTab} onValueChange={setActiveTab}>
										<TabsList className="w-full justify-start rounded-none bg-transparent h-auto p-0">
											<TabsTrigger
												value="overview"
												className="py-3 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
											>
												Timeline
											</TabsTrigger>
											<TabsTrigger
												value="messages"
												className="py-3 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
											>
												Messages
											</TabsTrigger>
										</TabsList>

										<div className="px-6 py-4">
											<TabsContent value="overview" className="mt-0 space-y-4">
												<TimelineComponent
													jobId={currentJob.id}
													warrantyRequestId={currentJob.warranty_request?.id}
												/>
											</TabsContent>

											<TabsContent value="messages" className="mt-0">
												<ConversationMessageThread
													quoteId={acceptedQuote.id}
													viewerRole="USER"
													userId={user?.id}
													isVisible={true}
													hideServiceRequestHeader={true}
												/>
											</TabsContent>
										</div>
									</Tabs>
								</CardContent>
							</Card>
						</div>

						{/* Right Column - Provider Info & Quick Actions */}
						<div className="space-y-6 mb-6">
							{/* Project Status */}
							<Card>
								<CardHeader>
									<CardTitle>Project Status</CardTitle>
								</CardHeader>
								<CardContent className="space-y-3">
									<div className="flex items-center justify-between">
										<span className="text-sm">Status</span>
										{getJobStatusBadge(currentJob.status)}
									</div>

									<div className="flex items-center justify-between">
										<span className="text-sm">Started</span>
										<span className="text-sm text-gray-600">
											{formatDate(new Date(acceptedQuote.updated_at), "MMM d")}
										</span>
									</div>

									{acceptedQuote.status === QuoteStatus.ACCEPTED &&
										acceptedQuote.completed_at && (
											<div className="flex items-center justify-between">
												<span className="text-sm">Completed</span>
												<span className="text-sm text-gray-600">
													{formatDate(
														new Date(acceptedQuote.completed_at),
														"MMM d"
													)}
												</span>
											</div>
										)}
								</CardContent>
							</Card>

							{/* Job Actions Alert */}
							{(currentJob.status === JobStatus.ASSIGNED ||
								currentJob.status === JobStatus.IN_PROGRESS) && (
								<div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
									<div className="flex items-start">
										<div className="flex-shrink-0">
											<svg
												className="h-5 w-5 text-amber-400"
												viewBox="0 0 20 20"
												fill="currentColor"
											>
												<path
													fillRule="evenodd"
													d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
													clipRule="evenodd"
												/>
											</svg>
										</div>
										<div className="ml-3 flex-1">
											<p className="text-sm text-amber-800">
												Need to complete or cancel this job?
											</p>
											<div className="mt-2 flex gap-2">
												<button
													onClick={() => handleStatusButtonClick("complete")}
													className="text-xs font-medium text-green-600 hover:text-green-700 underline"
												>
													Mark as Completed
												</button>
												<span className="text-amber-600">•</span>
												<button
													onClick={() => handleStatusButtonClick("cancel")}
													className="text-xs font-medium text-gray-600 hover:text-gray-700 underline"
												>
													Cancel Job
												</button>
											</div>
										</div>
									</div>
								</div>
							)}

							{/* Review Section - Only show if job is completed and on desktop */}
							{currentJob.status === JobStatus.COMPLETED && (
								<Card className="hidden lg:block">
									<CardHeader>
										<CardTitle className="flex items-center gap-2">
											<Star className="h-5 w-5 text-yellow-500" />
											Leave a Review
										</CardTitle>
									</CardHeader>
									<CardContent className="space-y-4">
										<p className="text-sm text-gray-600">
											How was your experience with{" "}
											{acceptedQuote.listing.business_name ||
												`${acceptedQuote.listing.first_name} ${acceptedQuote.listing.last_name}`}
											? Your feedback helps other RV owners and supports quality
											service providers.
										</p>

										<Link
											href={`/providers/${acceptedQuote.listing.slug}/review`}
											className="inline-block w-full"
										>
											<Button className="w-full bg-emerald-600 hover:bg-emerald-700">
												<Star className="h-4 w-4 mr-2" />
												Write a Review
											</Button>
										</Link>

										<div className="text-xs text-gray-500 text-center">
											Share your experience to help other RV owners
										</div>
									</CardContent>
								</Card>
							)}
						</div>
					</div>
				</div>

				<PhoneModal
					open={showPhoneModal}
					onOpenChange={setShowPhoneModal}
					phoneNumber={acceptedQuote.listing.phone}
					name={
						acceptedQuote.listing.business_name ||
						`${acceptedQuote.listing.first_name} ${acceptedQuote.listing.last_name}`
					}
				/>

				<JobDetailsModal
					job={currentJob}
					showDetailsModal={showDetailsModal}
					setShowDetailsModal={setShowDetailsModal}
					acceptedQuote={acceptedQuote}
				/>

				<JobStatusModal
					isOpen={showStatusModal}
					onClose={() => setShowStatusModal(false)}
					job={currentJob}
					action={statusAction}
					onSuccess={handleStatusSuccess}
				/>
			</div>
		</div>
	);
}
