"use client";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertCircle, Rocket, Sparkles } from "lucide-react";
import { useState } from "react";
import { useAuth } from "../../../../../lib/hooks/useAuth";

interface TopOfferAlertProps {
	title?: string;
	description?: string;
	primaryButtonText?: string;
	secondaryButtonText?: string;
	onPrimaryClick?: () => void;
	onSecondaryClick?: () => void;
	onDismiss?: () => void;
	showDismiss?: boolean;
	badge?: string;
	icon?: "rocket" | "sparkles" | "alert" | null;
}

export default function TopOfferAlert({
	title = "Unlock Pro Features & Save 50%!",
	description = "For the next 72 hours, upgrade to a Pro membership for 50% off and unlock the ability to invite multiple providers to this job.",
	primaryButtonText = "View Offer",
	secondaryButtonText = "Dismiss",
	onPrimaryClick,
	onSecondaryClick,
	onDismiss,
	badge = "Limited Time",
	icon = "alert"
}: TopOfferAlertProps) {
	const [isVisible, setIsVisible] = useState(true);
	const { isPaid } = useAuth();

	const handleDismiss = () => {
		setIsVisible(false);
		onDismiss?.();
	};

	const handleSecondaryClick = () => {
		handleDismiss();
		onSecondaryClick?.();
	};

	if (isPaid || !isVisible) return null;

	const IconComponent =
		icon === "rocket"
			? Rocket
			: icon === "sparkles"
				? Sparkles
				: icon === "alert"
					? AlertCircle
					: null;

	return (
		<div className="w-full bg-gradient-to-r from-orange-50 to-amber-50 border-b border-orange-200">
			<div className="container mx-auto px-4 py-4">
				<Alert className="border-0 bg-transparent p-0 shadow-none">
					<div className="flex items-center justify-between gap-4">
						<div className="flex items-center gap-4 flex-1">
							<div className="flex items-center gap-3">
								{IconComponent && (
									<div className="flex-shrink-0">
										<IconComponent className="h-5 w-5 text-orange-600" />
									</div>
								)}
								<div className="space-y-1">
									{badge && (
										<div className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
											<Sparkles className="h-3 w-3" />
											{badge}
										</div>
									)}
									<AlertTitle className="text-lg font-bold text-gray-900 leading-tight">
										{title}
									</AlertTitle>
									<AlertDescription className="text-gray-600 text-sm leading-relaxed">
										{description}
									</AlertDescription>
								</div>
							</div>
						</div>

						<div className="flex items-center gap-3 flex-shrink-0">
							<Button
								onClick={onPrimaryClick}
								className="bg-orange-500 hover:bg-orange-600 text-white font-semibold px-6 py-2 rounded-lg shadow-sm transition-colors"
							>
								{primaryButtonText}
							</Button>
							<Button
								variant="ghost"
								onClick={handleSecondaryClick}
								className="text-blue-600 hover:text-blue-700 font-medium px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors"
							>
								{secondaryButtonText}
							</Button>
						</div>
					</div>
				</Alert>
			</div>
		</div>
	);
}
