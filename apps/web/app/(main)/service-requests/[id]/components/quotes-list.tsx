"use client";

import { useConfirmation } from "@/lib/context/modal-context";
import type {
	JobWithUserAndLocation,
	ListingWithLocation,
	QuoteWithListing
} from "@/types/global";
import { JobStatus, QuoteStatus } from "@rvhelp/database";
import * as React from "react";
import { toast } from "react-hot-toast";
import MessageDrawer from "./message-drawer";
import ProviderDetailsDrawer from "./provider-details-drawer";
import QuoteCard from "./quote-card";

type QuotesListProps = {
	job: JobWithUserAndLocation;
	quotes: QuoteWithListing[];
	fetchJob: () => Promise<void>;
};

export default function QuotesList({ job, quotes, fetchJob }: QuotesListProps) {
	const [selectedProvider, setSelectedProvider] =
		React.useState<ListingWithLocation | null>(null);
	const [showProviderDetails, setShowProviderDetails] = React.useState(false);
	const [showMessageDrawer, setShowMessageDrawer] = React.useState(false);
	const [selectedProviderId, setSelectedProviderId] = React.useState<
		string | null
	>(null);
	const [processingQuoteId, setProcessingQuoteId] = React.useState<
		string | null
	>(null);
	const { confirmAccept, confirmReject } = useConfirmation();

	const handleViewDetails = (provider: ListingWithLocation) => {
		setSelectedProvider(provider);
		setShowProviderDetails(true);
	};

	const handleOpenMessageDrawer = async (providerId: string) => {
		setSelectedProviderId(providerId);
		setShowMessageDrawer(true);

		// Get the quote ID for this provider
		const quoteId = quotes.find((q) => q.listing.id === providerId)?.id;
		if (!quoteId) return;

		try {
			const response = await fetch(`/api/conversations/${quoteId}`);
			if (response.ok) {
				const data = await response.json();
			}
		} catch (error) {
			console.error("Error fetching messages:", error);
		}
		// Mark messages as read when opening the drawer
		try {
			const response = await fetch(
				`/api/conversations/${quoteId}/mark-as-read`,
				{
					method: "POST"
				}
			);
			if (!response.ok) throw new Error("Failed to mark messages as read");
			// Refresh job data to update unread message counts
			await fetchJob();
		} catch (error) {
			console.error("Error marking messages as read:", error);
		}
	};

	const handleCloseMessageDrawer = async () => {
		setShowMessageDrawer(false);
		setSelectedProviderId(null);
		// Refresh job data to get latest messages and status
		await fetchJob();
	};

	const handleAcceptQuote = async (quoteId: string) => {
		confirmAccept(async () => {
			try {
				setProcessingQuoteId(quoteId);
				const response = await fetch(
					`/api/jobs/${job.id}/quotes/${quoteId}/accept`,
					{
						method: "POST"
					}
				);
				if (!response.ok) throw new Error("Failed to accept quote");

				// Instead of reloading, fetch the latest data
				await fetchJob();

				// Show success message
				toast.success("Proposal accepted successfully");
			} catch (error) {
				console.error("Error accepting quote:", error);
				toast.error("Failed to accept quote");
			} finally {
				setProcessingQuoteId(null);
			}
		});
	};

	const handleRejectQuote = async (quoteId: string) => {
		confirmReject(async () => {
			try {
				setProcessingQuoteId(quoteId);
				const response = await fetch(
					`/api/jobs/${job.id}/quotes/${quoteId}/reject`,
					{
						method: "POST"
					}
				);
				if (!response.ok) throw new Error("Failed to reject quote");

				// Instead of reloading, fetch the latest data
				await fetchJob();

				// Show success message
				toast.error("Proposal rejected");
			} catch (error) {
				console.error("Error rejecting proposal:", error);
				toast.error("Failed to reject proposal");
			} finally {
				setProcessingQuoteId(null);
			}
		});
	};

	const handleMessagesRead = async () => {
		await fetchJob();
	};

	// Group quotes by status for display (using the filtered quotes from parent)
	// Treat ACCEPTED quotes as ACCEPTED unless customer has actually accepted that specific quote
	const quotedProviders = quotes.filter(
		(q) => q.status === QuoteStatus.ACCEPTED
	);
	const pendingProviders = quotes.filter(
		(q) => q.status === QuoteStatus.PENDING
	);
	const rejectedQuotes = quotes.filter(
		(q) =>
			q.status === QuoteStatus.REJECTED ||
			q.status === QuoteStatus.CUSTOMER_REJECTED
	);
	const withdrawnQuotes = quotes.filter(
		(q) => q.status === QuoteStatus.WITHDRAWN
	);
	const expiredQuotes = quotes.filter((q) => q.status === QuoteStatus.EXPIRED);

	// If customer has actually accepted a quote (job has accepted_quote_id), show a different view
	if (job.accepted_quote_id) {
		const acceptedQuote = quotes.find((q) => q.id === job.accepted_quote_id);

		// Get all other quotes for the "Other Quotes" section
		const otherQuotesForCollapse = quotes.filter(
			(q) => q.id !== job.accepted_quote_id
		);

		return (
			<div className="space-y-4 md:space-y-6" id="quotes-section">
				<div className="bg-green-50 border border-green-200 rounded-lg p-4 md:p-6">
					<h2 className="text-lg font-semibold text-green-800 mb-2 md:text-xl">
						Service Request Accepted
					</h2>
					<p className="text-sm text-green-700 md:text-base">
						You have accepted a quote from{" "}
						{acceptedQuote.listing.business_name ||
							`${acceptedQuote.listing.first_name} ${acceptedQuote.listing.last_name}`}
						.
					</p>
				</div>

				<div className="bg-white rounded-lg shadow-sm md:shadow">
					<QuoteCard
						key={acceptedQuote.id}
						quote={acceptedQuote}
						job={job}
						handleOpenMessageDrawer={handleOpenMessageDrawer}
						handleViewDetails={handleViewDetails}
						disabled={
							job.status === JobStatus.COMPLETED ||
							job.status === JobStatus.CANCELLED
						}
					/>
				</div>

				{/* Show other quotes in a collapsed section */}
				{otherQuotesForCollapse.length > 0 && (
					<div className="mt-6 md:mt-8">
						<details className="group">
							<summary className="flex items-center cursor-pointer">
								<h3 className="text-base font-semibold text-gray-500 md:text-lg">
									Other Quotes
								</h3>
								<span className="ml-2 text-sm text-gray-400 md:text-base">
									{otherQuotesForCollapse.length} quotes
								</span>
							</summary>
							<div className="mt-3 space-y-3 md:mt-4 md:space-y-4">
								{otherQuotesForCollapse.map((quote) => (
									<div
										key={quote.id}
										className={
											[
												QuoteStatus.REJECTED,
												QuoteStatus.CUSTOMER_REJECTED
											].includes(quote.status as any)
												? "pointer-events-none"
												: ""
										}
									>
										<QuoteCard
											quote={quote}
											job={job}
											handleOpenMessageDrawer={handleOpenMessageDrawer}
											handleViewDetails={handleViewDetails}
											isProcessing={processingQuoteId === quote.id}
											disabled={
												quote.status === QuoteStatus.REJECTED ||
												quote.status === QuoteStatus.CUSTOMER_REJECTED
											}
										/>
									</div>
								))}
							</div>
						</details>
					</div>
				)}

				{/* Keep the drawers */}
				<ProviderDetailsDrawer
					provider={selectedProvider}
					open={showProviderDetails}
					onClose={() => setShowProviderDetails(false)}
				/>

				{showMessageDrawer && selectedProviderId && (
					<MessageDrawer
						quoteId={
							quotes.find((q) => q.listing.id === selectedProviderId)?.id || ""
						}
						provider={
							quotes.find((q) => q.listing.id === selectedProviderId)?.listing
						}
						onClose={handleCloseMessageDrawer}
						open={showMessageDrawer}
						onMessageSent={() => {}}
						onMessagesRead={handleMessagesRead}
					/>
				)}
			</div>
		);
	}

	// Original view for when no quote is accepted - now displays whatever quotes are passed from parent
	return (
		<div className="space-y-6 md:space-y-8" id="quotes-section">
			{/* Quotes Received */}
			{quotedProviders.length > 0 && (
				<div className="space-y-3 md:space-y-4">
					<h3 className="text-base font-semibold mb-3 md:text-lg md:mb-4">
						Responses Received
					</h3>
					<div className="space-y-3 md:space-y-4">
						{quotedProviders.map((quote) => (
							<QuoteCard
								key={quote.id}
								quote={quote}
								job={job}
								handleOpenMessageDrawer={handleOpenMessageDrawer}
								handleViewDetails={handleViewDetails}
								handleAcceptQuote={handleAcceptQuote}
								handleRejectQuote={handleRejectQuote}
								isProcessing={processingQuoteId === quote.id}
								disabled={
									quote.status === QuoteStatus.REJECTED ||
									quote.status === QuoteStatus.CUSTOMER_REJECTED ||
									job.status === JobStatus.COMPLETED ||
									job.status === JobStatus.CANCELLED
								}
							/>
						))}
					</div>
				</div>
			)}

			{/* Pending Providers */}
			{pendingProviders.length > 0 && (
				<div className="space-y-3 md:space-y-4">
					<h3 className="text-base font-semibold mb-3 md:text-lg md:mb-4">
						Awaiting Responses
					</h3>
					<div className="space-y-3 md:space-y-4">
						{pendingProviders.map((quote) => (
							<QuoteCard
								key={quote.id}
								quote={quote}
								job={job}
								handleOpenMessageDrawer={handleOpenMessageDrawer}
								handleViewDetails={handleViewDetails}
								handleAcceptQuote={handleAcceptQuote}
								handleRejectQuote={handleRejectQuote}
								isProcessing={processingQuoteId === quote.id}
								disabled={
									quote.status === QuoteStatus.REJECTED ||
									quote.status === QuoteStatus.CUSTOMER_REJECTED ||
									job.status === JobStatus.COMPLETED ||
									job.status === JobStatus.CANCELLED
								}
							/>
						))}
					</div>
				</div>
			)}

			{/* Rejected/Withdrawn Quotes */}
			{rejectedQuotes.length > 0 && (
				<div className="space-y-3 md:space-y-4">
					<h3 className="text-base font-semibold mb-3 text-gray-500 md:text-lg md:mb-4">
						Not Available
					</h3>
					<div className="space-y-3 md:space-y-4">
						{rejectedQuotes.map((quote) => (
							<div key={quote.id} className="pointer-events-none">
								<QuoteCard
									quote={quote}
									job={job}
									handleOpenMessageDrawer={handleOpenMessageDrawer}
									handleViewDetails={handleViewDetails}
									isProcessing={processingQuoteId === quote.id}
									disabled={true}
								/>
							</div>
						))}
					</div>
				</div>
			)}

			{/* Other status quotes */}
			{withdrawnQuotes.length > 0 && (
				<div className="space-y-3 md:space-y-4">
					<h3 className="text-base font-semibold mb-3 text-gray-500 md:text-lg md:mb-4">
						Withdrawn
					</h3>
					<div className="space-y-3 md:space-y-4">
						{withdrawnQuotes.map((quote) => (
							<QuoteCard
								key={quote.id}
								quote={quote}
								job={job}
								handleOpenMessageDrawer={handleOpenMessageDrawer}
								handleViewDetails={handleViewDetails}
								isProcessing={processingQuoteId === quote.id}
								disabled={
									quote.status === QuoteStatus.REJECTED ||
									quote.status === QuoteStatus.CUSTOMER_REJECTED ||
									job.status === JobStatus.COMPLETED ||
									job.status === JobStatus.CANCELLED
								}
							/>
						))}
					</div>
				</div>
			)}

			{/* Expired Quotes */}
			{expiredQuotes.length > 0 && (
				<div className="space-y-3 md:space-y-4">
					<h3 className="text-base font-semibold mb-3 text-gray-500 md:text-lg md:mb-4">
						Expired
					</h3>
					<div className="space-y-3 md:space-y-4">
						{expiredQuotes.map((quote) => (
							<QuoteCard
								key={quote.id}
								quote={quote}
								job={job}
								handleOpenMessageDrawer={handleOpenMessageDrawer}
								handleViewDetails={handleViewDetails}
								isProcessing={processingQuoteId === quote.id}
								disabled={
									quote.status === QuoteStatus.REJECTED ||
									quote.status === QuoteStatus.CUSTOMER_REJECTED ||
									job.status === JobStatus.COMPLETED ||
									job.status === JobStatus.CANCELLED
								}
							/>
						))}
					</div>
				</div>
			)}

			{/* Keep the drawers */}
			<ProviderDetailsDrawer
				provider={selectedProvider}
				open={showProviderDetails}
				onClose={() => setShowProviderDetails(false)}
			/>

			{showMessageDrawer && selectedProviderId && (
				<MessageDrawer
					quoteId={
						quotes.find((q) => q.listing.id === selectedProviderId)?.id || ""
					}
					provider={
						quotes.find((q) => q.listing.id === selectedProviderId)?.listing
					}
					onClose={handleCloseMessageDrawer}
					open={showMessageDrawer}
					onMessageSent={() => {}}
					onMessagesRead={handleMessagesRead}
				/>
			)}
		</div>
	);
}
