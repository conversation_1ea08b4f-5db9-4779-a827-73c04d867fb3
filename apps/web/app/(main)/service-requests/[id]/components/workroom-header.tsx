"use client";

import OEMLogo from "@/components/oem/oem-logo";
import { getWarrantyRequestState } from "@/components/oem/warranty-details/warranty-utils";
import { Badge } from "@/components/ui/badge";
import { getCategoryName } from "@/lib/categories";
import { JobWithUserAndLocation } from "@/types/global";
import { JobStatus } from "@rvhelp/database";
import { Calendar, MapPin, Truck } from "lucide-react";
import { useMemo, useState } from "react";
import JobDetailsModal from "./job-details-modal";

type WorkroomHeaderProps = {
	job: JobWithUserAndLocation;
};

export default function WorkroomHeader({ job }: WorkroomHeaderProps) {
	const [showDetailsModal, setShowDetailsModal] = useState(false);
	const requestLabel = useMemo(
		() =>
			`${job.rv_year || ""} ${job.rv_make || ""} ${job.rv_model || ""} ${getCategoryName(job.category)}`.trim(),
		[job.rv_year, job.rv_make, job.rv_model, job.category]
	);

	// Format the date in a more readable format
	const formattedDate = useMemo(
		() =>
			new Date(job.created_at).toLocaleDateString("en-US", {
				year: "numeric",
				month: "long",
				day: "numeric"
			}),
		[job.created_at]
	);

	const getJobStatusDetails = (status: JobStatus) => {
		switch (status) {
			case JobStatus.OPEN:
				return { state: "Open", color: "bg-green-100 text-green-800" };
			case JobStatus.IN_PROGRESS:
				return { state: "In Progress", color: "bg-blue-100 text-blue-800" };
			case JobStatus.COMPLETED:
				return { state: "Completed", color: "bg-green-100 text-green-800" };
			case JobStatus.CANCELLED:
				return { state: "Cancelled", color: "bg-red-100 text-red-800" };
			default:
				return { state: "Pending", color: "bg-yellow-100 text-yellow-800" };
		}
	};

	const statusDetails = job.warranty_request_id
		? getWarrantyRequestState(job.warranty_request)
		: getJobStatusDetails(job.status);

	return (
		<div className="space-y-4">
			<div className="flex gap-6">
				{job.warranty_request && (
					<div className="flex-shrink-0">
						<OEMLogo company={job.warranty_request.company} />
					</div>
				)}

				{/* Main content column */}
				<div className="flex-1">
					<div className="flex items-center gap-2">
						<h1 className="text-2xl font-semibold">{requestLabel}</h1>

						<Badge className={`capitalize ${statusDetails.color} border-0`}>
							{statusDetails.state}
						</Badge>
					</div>

					<div className="flex flex-wrap gap-x-6 gap-y-2 mt-2 text-sm text-gray-600">
						<div className="flex items-center">
							<Calendar className="h-4 w-4 mr-1.5 text-gray-500" />
							<span>Created {formattedDate}</span>
						</div>

						{job.location && (
							<div className="flex items-center">
								<MapPin className="h-4 w-4 mr-1.5 text-gray-500" />
								<span>{job.location.address}</span>
							</div>
						)}

						<div className="flex items-center">
							<Truck className="h-4 w-4 mr-1.5 text-gray-500" />
							<span>
								{job.rv_year} {job.rv_make} {job.rv_model}
							</span>
						</div>
					</div>

					{/* Request message and read more */}
					<div className="mt-4">
						<span className="text-gray-700  line-clamp-4">{job.message}</span>

						<button
							className="mt-2 text-primary underline text-sm"
							type="button"
							onClick={() => setShowDetailsModal(true)}
						>
							View Request Details
						</button>
					</div>
				</div>
			</div>

			<JobDetailsModal
				job={job}
				showDetailsModal={showDetailsModal}
				setShowDetailsModal={setShowDetailsModal}
			/>
		</div>
	);
}
