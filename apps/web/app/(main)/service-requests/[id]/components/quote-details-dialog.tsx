"use client";

import { ReactNode } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import type { QuoteWithListing } from "@/types/global";
import { DollarSign, MessageSquare, Truck } from "lucide-react";

interface QuoteDetailItemProps {
	icon: ReactNode;
	label: string;
	value: ReactNode;
}

const QuoteDetailItem = ({ icon, label, value }: QuoteDetailItemProps) => (
	<div className="flex items-start gap-3">
		<div className="mt-0.5 text-muted-foreground">{icon}</div>
		<div className="space-y-1">
			<p className="text-sm font-medium text-muted-foreground">{label}</p>
			<p className="text-base font-semibold">{value}</p>
		</div>
	</div>
);

const QuoteDetailsDialog = ({
	quote,
	open,
	onClose
}: {
	quote: QuoteWithListing;
	open: boolean;
	onClose: () => void;
}) => {
	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent
				className="max-w-xl p-0 overflow-hidden"
				onClick={(e) => e.stopPropagation()}
			>
				<div className="px-6">
					<DialogHeader>
						<div className="flex items-center justify-between">
							<DialogTitle className="text-xl font-bold">
								Quote Details
							</DialogTitle>
						</div>
					</DialogHeader>
				</div>

				<div className="px-6 pb-6">
					<div className="grid gap-6 sm:grid-cols-2">
						{quote.display_pricing && quote.dispatch_fee !== undefined && (
							<QuoteDetailItem
								icon={<Truck size={18} />}
								label="Dispatch Fee"
								value={
									<>
										$
										{quote.discount_dispatch_fee
											? (quote.dispatch_fee * 0.75).toFixed(2)
											: quote.dispatch_fee.toFixed(2)}
										{quote.discount_dispatch_fee && (
											<span className="text-xs text-green-700 ml-1">
												(Pro Discount)
											</span>
										)}
										{quote.first_hour_included && (
											<span className="text-xs text-gray-500 ml-1">
												(includes first hour)
											</span>
										)}
									</>
								}
							/>
						)}
						{quote.display_pricing && quote.hourly_rate !== undefined && (
							<QuoteDetailItem
								icon={<DollarSign size={18} />}
								label="Hourly Rate"
								value={
									<>
										{quote.discount_hourly_rate ? (
											<>
												First 4 hours: ${(quote.hourly_rate * 0.9).toFixed(2)}
												/hr
												<span className="text-xs text-green-700 ml-1">
													(Pro Discount)
												</span>
												<br />
												<span className="text-sm">
													Additional hours: ${quote.hourly_rate.toFixed(2)}/hr
												</span>
											</>
										) : (
											`$${quote.hourly_rate.toFixed(2)}/hr`
										)}
									</>
								}
							/>
						)}
					</div>

					{quote.provider_notes && (
						<div className="mt-6 space-y-2">
							<div className="flex items-center gap-2">
								<MessageSquare size={16} className="text-muted-foreground" />
								<h4 className="font-medium">Message from Provider</h4>
							</div>
							<div className="bg-gray-200 p-4 rounded-lg">
								<p className="italic">{quote.provider_notes}</p>
							</div>
						</div>
					)}
				</div>

				<div className="bg-muted/30 px-6 py-4 flex items-center justify-between">
					<Button variant="destructive" className="mr-2">
						Reject Quote
					</Button>
					<Button>Accept Quote</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default QuoteDetailsDialog;
