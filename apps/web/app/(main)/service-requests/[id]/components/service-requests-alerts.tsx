"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/hooks/useAuth";
import type { JobWithUserAndLocation, QuoteWithListing } from "@/types/global";
import { QuoteStatus } from "@rvhelp/database";
import {
	AlertTriangle,
	Calendar,
	ChevronRight,
	Clock,
	MessageSquare,
	Star,
	Wrench
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

interface ServiceRequestAlert {
	id: string;
	type:
		| "pending_responses"
		| "completed_needs_review"
		| "scheduled_service"
		| "provider_message"
		| "overdue_response"
		| "awaiting_responses"
		| "no_response_72hrs";
	title: string;
	description: string;
	actionText: string;
	actionHref: string;
	priority: "high" | "medium" | "low";
	job: JobWithUserAndLocation;
	quote?: QuoteWithListing;
	icon: React.ReactNode;
	color: string;
}

const ServiceRequestsAlerts = () => {
	const { user } = useAuth();
	const [alerts, setAlerts] = useState<ServiceRequestAlert[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		const fetchServiceRequests = async () => {
			if (!user) return;

			try {
				const response = await fetch("/api/jobs");
				const jobs: JobWithUserAndLocation[] = await response.json();

				const generatedAlerts: ServiceRequestAlert[] = [];

				jobs.forEach((job) => {
					// Check for quotes that need customer review (provider responded, customer hasn't accepted/rejected)
					// Only show review alerts if the job doesn't already have an accepted quote
					const quotesNeedingReview =
						job.quotes?.filter(
							(q) =>
								q.status === QuoteStatus.ACCEPTED &&
								q.responded_at &&
								!q.accepted_at
						) || [];

					if (quotesNeedingReview.length > 0 && !job.accepted_quote_id) {
						generatedAlerts.push({
							id: `pending-${job.id}`,
							type: "pending_responses",
							title: `${quotesNeedingReview.length} Response${quotesNeedingReview.length > 1 ? "s" : ""} Pending Review`,
							description: `You have ${quotesNeedingReview.length} provider${quotesNeedingReview.length > 1 ? "s" : ""} waiting for your review for your ${job.rv_year} ${job.rv_make} ${job.rv_model}.`,
							actionText: "Review responses",
							actionHref: `/service-requests/${job.id}`,
							priority: "high",
							job,
							icon: <Clock className="h-4 w-4" />,
							color: "bg-orange-50 border-orange-200 text-orange-800"
						});
					}

					// Check for completed jobs that need review
					const completedQuotes =
						job.quotes?.filter(
							(q) =>
								q.status === QuoteStatus.COMPLETED &&
								q.completed_at &&
								!q.review_requested
						) || [];

					if (completedQuotes.length > 0) {
						const completedQuote = completedQuotes[0]; // Take the first one
						generatedAlerts.push({
							id: `review-${job.id}`,
							type: "completed_needs_review",
							title: "Service Completed - Leave a Review",
							description: `Your service request for ${job.rv_year} ${job.rv_make} ${job.rv_model} has been completed. Please leave a review for your provider.`,
							actionText: "Leave Review",
							actionHref: `/providers/${completedQuote.listing.slug}/review`,
							priority: "medium",
							job,
							quote: completedQuote,
							icon: <Star className="h-4 w-4" />,
							color: "bg-green-50 border-green-200 text-green-800"
						});
					}

					// Check for scheduled services (accepted quotes with scheduling information)
					const scheduledQuotes =
						job.quotes?.filter(
							(q) => q.status === QuoteStatus.ACCEPTED && q.accepted_at
						) || [];

					if (scheduledQuotes.length > 0) {
						const acceptedQuote = scheduledQuotes[0];
						const daysSinceAccepted = acceptedQuote.accepted_at
							? Math.ceil(
									(Date.now() - new Date(acceptedQuote.accepted_at).getTime()) /
										(1000 * 60 * 60 * 24)
								)
							: 0;

						// Show alert if recently accepted (within 3 days)
						if (daysSinceAccepted <= 3) {
							generatedAlerts.push({
								id: `scheduled-${job.id}`,
								type: "scheduled_service",
								title: "Service Scheduled",
								description: `Your service for ${job.rv_year} ${job.rv_make} ${job.rv_model} has been scheduled. The provider will contact you soon.`,
								actionText: "View Details",
								actionHref: `/service-requests/${job.id}`,
								priority: "medium",
								job,
								quote: acceptedQuote,
								icon: <Calendar className="h-4 w-4" />,
								color: "bg-blue-50 border-blue-200 text-blue-800"
							});
						}
					}

					// Check for unread messages
					const quotesWithMessages =
						job.quotes?.filter(
							(q) => q.unread_messages_count && q.unread_messages_count > 0
						) || [];

					if (quotesWithMessages.length > 0) {
						const totalUnread = quotesWithMessages.reduce(
							(sum, q) => sum + (q.unread_messages_count || 0),
							0
						);
						generatedAlerts.push({
							id: `messages-${job.id}`,
							type: "provider_message",
							title: `${totalUnread} New Message${totalUnread > 1 ? "s" : ""}`,
							description: `You have ${totalUnread} unread message${totalUnread > 1 ? "s" : ""} from your service provider${quotesWithMessages.length > 1 ? "s" : ""}.`,
							actionText: "View Messages",
							actionHref: `/service-requests/${job.id}`,
							priority: "high",
							job,
							icon: <MessageSquare className="h-4 w-4" />,
							color: "bg-purple-50 border-purple-200 text-purple-800"
						});
					}

					// Check for overdue responses (quotes that have been waiting for customer review for over 7 days)
					// Only show overdue alerts if the job doesn't already have an accepted quote
					const overdueQuotes =
						job.quotes?.filter(
							(q) =>
								q.status === QuoteStatus.ACCEPTED &&
								q.responded_at &&
								!q.accepted_at &&
								new Date(q.responded_at).getTime() <
									Date.now() - 7 * 24 * 60 * 60 * 1000
						) || [];

					if (overdueQuotes.length > 0 && !job.accepted_quote_id) {
						generatedAlerts.push({
							id: `overdue-${job.id}`,
							type: "overdue_response",
							title: "Quotes Need Your Attention",
							description: `You have ${overdueQuotes.length} quote${overdueQuotes.length > 1 ? "s" : ""} that have been waiting for your review for over a week.`,
							actionText: "Review Now",
							actionHref: `/service-requests/${job.id}`,
							priority: "high",
							job,
							icon: <AlertTriangle className="h-4 w-4" />,
							color: "bg-red-50 border-red-200 text-red-800"
						});
					}

					// Check for jobs with no responses after 72 hours
					// Only show if job doesn't have an accepted quote
					const pendingQuotes =
						job.quotes?.filter((q) => q.status === QuoteStatus.PENDING) || [];

					if (pendingQuotes.length > 0 && !job.accepted_quote_id) {
						generatedAlerts.push({
							id: `awaiting-${job.id}`,
							type: "awaiting_responses",
							title: "Awaiting Provider Responses",
							description: `Your service request for ${job.rv_year} ${job.rv_make} ${job.rv_model} is being reviewed by ${pendingQuotes.length} provider${pendingQuotes.length > 1 ? "s" : ""}. You'll receive responses soon.`,
							actionText: "View Request",
							actionHref: `/service-requests/${job.id}`,
							priority: "medium",
							job,
							icon: <Wrench className="h-4 w-4" />,
							color: "bg-blue-50 border-blue-200 text-blue-800"
						});
					}

					// Show follow-up alert for jobs with no responses after 72 hours
					// Only show if job doesn't have an accepted quote
					if (!job.accepted_quote_id) {
						const now = new Date();
						const createdAt = new Date(job.created_at);
						const hoursOld =
							(now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);

						// Show if older than 72 hours and no provider has responded
						if (hoursOld > 72 && !job.quotes?.some((q) => q.responded_at)) {
							generatedAlerts.push({
								id: `no-response-${job.id}`,
								type: "no_response_72hrs",
								title: "No Responses Yet",
								description: `Your service request for ${job.rv_year} ${job.rv_make} ${job.rv_model} hasn't received any responses yet. You may want to invite more providers.`,
								actionText: "Invite More Providers",
								actionHref: `/service-requests/${job.id}`,
								priority: "medium",
								job,
								icon: <AlertTriangle className="h-4 w-4" />,
								color: "bg-amber-50 border-amber-200 text-amber-800"
							});
						}
					}
				});

				// Sort alerts by priority (high first, then by created date)
				const priorityOrder = { high: 0, medium: 1, low: 2 };
				generatedAlerts.sort((a, b) => {
					if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
						return priorityOrder[a.priority] - priorityOrder[b.priority];
					}
					return (
						new Date(b.job.created_at).getTime() -
						new Date(a.job.created_at).getTime()
					);
				});

				setAlerts(generatedAlerts);
			} catch (error) {
				console.error("Error fetching service requests:", error);
			} finally {
				setLoading(false);
			}
		};

		fetchServiceRequests();
	}, [user]);

	if (loading) {
		return (
			<div className="mb-8">
				<div className="animate-pulse">
					<div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div className="space-y-3">
						<div className="h-20 bg-gray-200 rounded"></div>
						<div className="h-20 bg-gray-200 rounded"></div>
					</div>
				</div>
			</div>
		);
	}

	if (alerts.length === 0) {
		return null;
	}

	return (
		<div className="mb-8">
			<div className="flex items-center justify-between mb-4">
				<h2 className="text-lg font-medium text-gray-900">
					Service Request Alerts
				</h2>
				<Link href="/service-requests">
					<Button variant="ghost" size="sm">
						View All
						<ChevronRight className="h-4 w-4 ml-1" />
					</Button>
				</Link>
			</div>

			<div className="space-y-3">
				{alerts.slice(0, 3).map((alert) => (
					<Alert key={alert.id} className={alert.color}>
						<div className="flex items-start gap-3">
							<div className="flex-shrink-0 mt-0.5">{alert.icon}</div>
							<div className="flex-1 min-w-0">
								<div className="flex items-center justify-between mb-1">
									<h3 className="font-medium">{alert.title}</h3>
									<Badge
										variant="outline"
										className={
											alert.priority === "high"
												? "border-red-300 text-red-700"
												: alert.priority === "medium"
													? "border-orange-300 text-orange-700"
													: "border-gray-300 text-gray-700"
										}
									>
										{alert.priority === "high"
											? "High Priority"
											: alert.priority === "medium"
												? "Medium Priority"
												: "Low Priority"}
									</Badge>
								</div>
								<AlertDescription className="text-sm mb-3">
									{alert.description}
								</AlertDescription>
								<div className="flex items-center gap-2">
									<Link href={alert.actionHref}>
										<Button size="sm" className="h-8">
											{alert.actionText}
										</Button>
									</Link>
									<span className="text-xs text-gray-500">
										{alert.job.rv_year} {alert.job.rv_make} {alert.job.rv_model}
									</span>
								</div>
							</div>
						</div>
					</Alert>
				))}

				{alerts.length > 3 && (
					<div className="text-center pt-2">
						<Link href="/service-requests">
							<Button variant="outline" size="sm">
								View {alerts.length - 3} More Alert
								{alerts.length - 3 > 1 ? "s" : ""}
							</Button>
						</Link>
					</div>
				)}
			</div>
		</div>
	);
};

export default ServiceRequestsAlerts;
