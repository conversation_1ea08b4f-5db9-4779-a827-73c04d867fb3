"use client";

import CertificationsSection from "@/components/directory/listing-details/CertificationsSection";
import CompanyDetailsSection from "@/components/directory/listing-details/CompanyDetailsSection";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { TooltipProvider } from "@/components/ui/tooltip";
import { renderStarRating } from "@/lib/utils/star-rating";
import type { ListingWithLocation } from "@/types/global";
import { ExternalLink, MapPin } from "lucide-react";
import Link from "next/link";

function ProviderDetailsDrawer({
	provider,
	open,
	onClose
}: {
	provider: ListingWithLocation | null;
	open: boolean;
	onClose: () => void;
}) {
	if (!provider) return null;

	return (
		<Sheet open={open} onOpenChange={onClose}>
			<SheetContent className="w-[90vw] sm:max-w-[600px] overflow-y-auto">
				<TooltipProvider>
					{/* Header */}
					<div className="relative h-48 -mx-6 -mt-6 mb-6 bg-gradient-to-b from-gray-800 to-gray-700">
						<div className="absolute inset-0 flex items-center justify-center">
							<Avatar className="h-24 w-24 border-4 border-white">
								{provider.profile_image && (
									<AvatarImage
										src={provider.profile_image}
										alt={provider.business_name || ""}
									/>
								)}
								<AvatarFallback className="text-3xl bg-primary text-white">
									{provider.first_name?.[0] || "P"}
									{provider.last_name?.[0] || ""}
								</AvatarFallback>
							</Avatar>
						</div>
					</div>

					{/* Provider Name and Status */}
					<div className="text-center mb-6">
						<h2 className="text-2xl font-semibold">
							{provider.business_name ||
								`${provider.first_name || ""} ${provider.last_name || ""}`}
						</h2>
						{/* Show individual name if business name exists */}
						{provider.business_name && provider.first_name && (
							<p className="text-gray-600 mt-1">
								{provider.first_name} {provider.last_name}
							</p>
						)}
					</div>

					{/* Quick Stats */}
					<div className="grid grid-cols-2 gap-4 mb-6">
						<div className="text-center p-4 bg-gray-50 rounded-lg">
							<div className="flex justify-center mb-2">
								{renderStarRating(provider.rating, provider.num_reviews)}
							</div>
							<div className="text-sm text-gray-600">
								{provider.rating?.toFixed(1) || "0.0"} (
								{provider.num_reviews || 0} reviews)
							</div>
						</div>
						<div className="text-center p-4 bg-gray-50 rounded-lg">
							<div className="flex justify-center mb-2">
								<MapPin className="w-5 h-5 text-gray-600" />
							</div>
							<div className="text-sm text-gray-600">
								{provider.location?.city}, {provider.location?.state}
								{provider.distance !== undefined && (
									<span className="block">
										{provider.distance.toFixed(1)} mi away
									</span>
								)}
							</div>
						</div>
					</div>

					{/* Sections */}
					<div className="space-y-6">
						<Card>
							<CardHeader>
								<CardTitle>About</CardTitle>
							</CardHeader>
							<CardContent>
								<div
									className="prose prose-sm max-w-none"
									dangerouslySetInnerHTML={{
										__html:
											provider.long_description ||
											provider.short_description ||
											provider.long_description ||
											"No description available"
									}}
								></div>
							</CardContent>
						</Card>

						<Card className="shadow-sm">
							<CardHeader className="border-b border-gray-100">
								<CardTitle className="text-xl font-semibold text-gray-800">
									Certifications
								</CardTitle>
							</CardHeader>
							<CardContent className="pt-6">
								<CertificationsSection
									listing={provider}
									disableTooltip={true}
								/>
							</CardContent>
						</Card>

						{/* Company Details */}
						<Card>
							<CardHeader>
								<CardTitle>Company Details</CardTitle>
							</CardHeader>
							<CardContent>
								<CompanyDetailsSection
									details={provider}
									redactContactInfo={true}
								/>
							</CardContent>
						</Card>

						{/* View Full Profile Link */}
						{provider.slug && (
							<Link
								href={`/providers/${provider.slug}`}
								target="_blank"
								className="flex items-center justify-center gap-2 w-full text-primary hover:text-primary/90 font-medium"
							>
								View Full Profile
								<ExternalLink className="h-4 w-4" />
							</Link>
						)}
					</div>
				</TooltipProvider>
			</SheetContent>
		</Sheet>
	);
}

export default ProviderDetailsDrawer;
