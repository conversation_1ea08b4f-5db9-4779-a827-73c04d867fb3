import { redirect } from "next/navigation";
import config from "../../../../config";
import { UserService } from "../../../../lib/services/user.service";
import JobClient from "./client";

const fetchJob = async (id: string) => {
	try {
		const response = await fetch(`${config.appUrl}/api/jobs/${id}`, {
			cache: "no-store",
			headers: {
				"Cache-Control": "no-cache, no-store, must-revalidate",
				Pragma: "no-cache"
			}
		});
		const data = await response.json();
		if (data) {
			return data;
		}
	} catch (error) {
		console.error("Error fetching service request:", error);
		return null;
	}
};

export default async function JobPage({ params }: { params: { id: string } }) {
	const id = params.id;

	const [job, user] = await Promise.all([fetchJob(id), UserService.user()]);

	if (!job) {
		return <div>Job not found</div>;
	}

	// Check if user is authenticated and authorized to view this job
	if (!user) {
		return redirect(
			`${config.appUrl}/login?redirectUrl=${encodeURIComponent(`/service-requests/${id}`)}`
		);
	}

	if (job.user_id !== user.id && user.role !== "ADMIN") {
		return redirect(`${config.appUrl}/403`);
	}

	return <JobClient initialJob={job} />;
}
