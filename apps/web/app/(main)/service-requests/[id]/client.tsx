"use client";

import type { JobWithUserAndLocation } from "@/types/global";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import EmptyWorkroom from "./components/empty-workroom";

import SelectProviderRoom from "./components/select-provider-room";
import Workroom from "./components/workroom";

import UpgradeWorkroomModal from "@/components/modals/UpgradeWorkroomModal";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/lib/hooks/useAuth";
import { OfferUtils } from "@/lib/utils/offer-utils";

export default function JobPage({
	initialJob
}: {
	initialJob: JobWithUserAndLocation;
}) {
	const [job, setJob] = useState<JobWithUserAndLocation | null>(initialJob);
	const params = useParams();
	const router = useRouter();
	const searchParams = useSearchParams();
	const id = params?.id as string;
	const [isLoading, setIsLoading] = useState(false);
	const [isCheckingAuth, setIsCheckingAuth] = useState(true);
	const [isOfferModalOpen, setIsOfferModalOpen] = useState(false);
	const [offerVariant, setOfferVariant] = useState<
		"invite-providers" | "pro-support"
	>("invite-providers");
	const [preSelectedProviders, setPreSelectedProviders] = useState<string[]>(
		[]
	);

	const { isPaid, loading: authLoading, user } = useAuth();

	// Check authentication and handle password setup flow
	const checkAuthAndRedirect = useCallback(async () => {
		// Skip auth check if user is already authenticated
		if (user) {
			setIsCheckingAuth(false);
			return;
		}

		// If still loading auth, wait
		if (authLoading) {
			return;
		}

		// User is not authenticated, check if they have an account for this service request
		try {
			const response = await fetch("/api/auth/check-service-request-user", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					serviceRequestId: id
				})
			});

			if (response.ok) {
				const data = await response.json();

				// If user exists but needs password setup (never logged in), redirect to password setup flow
				if (data.userExists && data.needsPasswordSetup) {
					// Redirect directly to setup password page - let user manually request email
					router.push(
						`/service-requests/${id}/setup-password?email=${encodeURIComponent(data.email)}`
					);
					return;
				} else if (data.userExists && !data.needsPasswordSetup) {
					// User exists and has logged in before, redirect to login
					router.push(
						`/login?redirectUrl=${encodeURIComponent(window.location.pathname)}`
					);
					return;
				}
				// If user doesn't exist, continue to load the page (they might be a new user)
			}
		} catch (error) {
			console.error("Error checking auth:", error);
			// Continue to load the page on error
		}

		setIsCheckingAuth(false);
	}, [user, authLoading, id, router]);

	useEffect(() => {
		checkAuthAndRedirect();
	}, [checkAuthAndRedirect]);

	const fetchJob = useCallback(async () => {
		try {
			const response = await fetch(`/api/jobs/${id}`, {
				cache: "no-store",
				headers: {
					"Cache-Control": "no-cache, no-store, must-revalidate",
					Pragma: "no-cache"
				}
			});
			const data = await response.json();
			console.log("data from fetchJob", data);
			if (data) {
				setJob(data); // Use the fresh data instead of initialJob
			}
			setIsLoading(false);
		} catch (error) {
			console.error("Error fetching service request:", error);
			setIsLoading(false);
		}
	}, [id]);

	// Check for pre-selected providers from URL (after successful pro membership purchase)
	useEffect(() => {
		const selectedProvidersParam = searchParams.get("selectedProviders");
		if (selectedProvidersParam) {
			try {
				const providers = JSON.parse(
					decodeURIComponent(selectedProvidersParam)
				);
				setPreSelectedProviders(providers);
				// Clear the URL parameter after setting the state
				const url = new URL(window.location.href);
				url.searchParams.delete("selectedProviders");
				window.history.replaceState({}, "", url.toString());
			} catch (error) {
				console.error("Error parsing selected providers:", error);
			}
		}
	}, [searchParams]);

	const isOfferActive =
		job && OfferUtils.isJobOfferActive(new Date(job.created_at));

	// Check if user is eligible for discount (client-side)
	const offerEligibilityCheck = useMemo(() => {
		if (!user || authLoading || isPaid || !isOfferActive || !job?.created_at) {
			return { eligible: false, reason: "Not applicable" };
		}
		return OfferUtils.isUserEligibleForAnnualOfferClientSide(
			user,
			new Date(job.created_at)
		);
	}, [user, authLoading, isPaid, isOfferActive, job?.created_at]);

	const handleModalClose = () => {
		setIsOfferModalOpen(false);
	};

	// Show loading while checking auth or fetching job
	if (isCheckingAuth || isLoading) {
		return (
			<div className="container mx-auto px-4 py-8">
				<div className="max-w-4xl mx-auto">
					<div className="mb-8">
						<div className="flex items-center gap-3 mb-4">
							<Skeleton className="h-8 w-8 rounded-full" />
							<div className="space-y-2">
								<Skeleton className="h-6 w-48" />
								<Skeleton className="h-4 w-32" />
							</div>
						</div>
						<Skeleton className="h-4 w-full mb-2" />
						<Skeleton className="h-4 w-3/4" />
					</div>

					<div className="space-y-4">
						{[1, 2, 3].map((i) => (
							<div key={i} className="border rounded-lg bg-white p-6">
								<div className="flex items-start justify-between mb-4">
									<div className="flex items-center gap-3">
										<Skeleton className="h-12 w-12 rounded-full" />
										<div className="space-y-2">
											<Skeleton className="h-5 w-32" />
											<Skeleton className="h-4 w-24" />
										</div>
									</div>
									<Skeleton className="h-8 w-20" />
								</div>
								<div className="space-y-2">
									<Skeleton className="h-4 w-full" />
									<Skeleton className="h-4 w-3/4" />
									<Skeleton className="h-4 w-1/2" />
								</div>
								<div className="flex justify-between items-center mt-4">
									<Skeleton className="h-6 w-16" />
									<div className="flex gap-2">
										<Skeleton className="h-9 w-20" />
										<Skeleton className="h-9 w-16" />
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		);
	}

	if (!job) {
		return <EmptyWorkroom job={job} onProviderInvited={() => {}} />;
	}

	// Workroom interface for accepted quotes
	const acceptedQuote = job.accepted_quote;

	if (acceptedQuote) {
		return <Workroom job={job} />;
	}

	return (
		<>
			<SelectProviderRoom
				job={job}
				fetchJob={fetchJob}
				setIsOfferModalOpen={setIsOfferModalOpen}
				setOfferVariant={setOfferVariant}
				preSelectedProviders={preSelectedProviders}
			/>
			<UpgradeWorkroomModal
				open={isOfferModalOpen}
				onOpenChange={handleModalClose}
				job={job}
				variant={offerVariant}
				hasDiscount={offerEligibilityCheck.eligible}
			/>
		</>
	);
}
