"use client";

import {
	<PERSON>,
	CardContent,
	CardDescription,
	Card<PERSON>eader,
	CardTitle
} from "@/components/ui/card";
import { AlertTriangle, Mail } from "lucide-react";
import { useEffect, useState } from "react";

export default function BannedPage() {
	const [banInfo, setBanInfo] = useState<{ message?: string }>({});

	useEffect(() => {
		// Get ban info from URL params
		const urlParams = new URLSearchParams(window.location.search);
		const message = urlParams.get("message");
		if (message) {
			setBanInfo({ message: decodeURIComponent(message) });
		}

		// Set persistent ban markers
		const setBanMarkers = () => {
			// Set localStorage ban marker
			localStorage.setItem("rvhelp_banned", "true");
			localStorage.setItem(
				"rvhelp_ban_message",
				message || "Access restricted"
			);
			localStorage.setItem("rvhelp_ban_timestamp", Date.now().toString());

			// Set cookie ban marker (expires in 1 year)
			const expires = new Date();
			expires.setFullYear(expires.getFullYear() + 1);
			document.cookie = `rvhelp_banned=true; expires=${expires.toUTCString()}; path=/; SameSite=Strict`;
			document.cookie = `rvhelp_ban_message=${encodeURIComponent(message || "Access restricted")}; expires=${expires.toUTCString()}; path=/; SameSite=Strict`;
			document.cookie = `rvhelp_ban_timestamp=${Date.now()}; expires=${expires.toUTCString()}; path=/; SameSite=Strict`;

			console.log("🚫 Ban markers set for user");
		};

		setBanMarkers();
	}, []);

	return (
		<div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
			<Card className="w-full max-w-2xl">
				<CardHeader className="text-center">
					<div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
						<AlertTriangle className="h-8 w-8 text-red-600" />
					</div>
					<CardTitle className="text-3xl text-red-600 mb-2">
						Access Restricted
					</CardTitle>
					<CardDescription className="text-lg">
						Your access to this website has been restricted.
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-6">
					{banInfo.message && (
						<div className="bg-red-50 border border-red-200 rounded-lg p-6">
							<div className="flex items-start space-x-3">
								<Mail className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
								<div className="flex-1">
									<h3 className="font-medium text-red-800 mb-2">
										Message from RV Help
									</h3>
									<div className="text-sm text-red-700 whitespace-pre-wrap leading-relaxed">
										{banInfo.message}
									</div>
								</div>
							</div>
						</div>
					)}

					<div className="text-center text-sm text-gray-600 bg-gray-50 rounded-lg p-4">
						<p className="mb-2">
							If you believe this is an error or would like to appeal this
							decision, please contact our support team.
						</p>
						<p className="font-medium">
							Email:{" "}
							<a
								href="mailto:<EMAIL>"
								className="text-blue-600 hover:underline"
							>
								<EMAIL>
							</a>
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
