"use client";

import { SupportModal } from "@/components/modals/SupportModal";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CreditCard, FileText, HeadsetIcon } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

export default function CampgroundsPage() {
    const [isBusinessCardModalOpen, setIsBusinessCardModalOpen] = useState(false);
    const [isSupportModalOpen, setIsSupportModalOpen] = useState(false);

    const downloadBrochure = () => {
        window.open("/promo/flyer-campgrounds-(tracked).pdf", "_blank");
    };

    const downloadBusinessCards = () => {
        window.open("/promo/business-cards-campgrounds-(tracked).pdf", "_blank");
    };

    return (
        <div className="min-h-screen">
            {/* Hero Section */}
            <header className="relative text-white py-24 px-4">
                <div className="absolute inset-0">
                    <Image
                        src="/images/myrvresource.webp"
                        alt="RV in scenic landscape"
                        layout="fill"
                        objectFit="cover"
                        quality={100}
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-[#43806c]/90 to-[#2c5446]/90"></div>
                </div>
                <div className="container mx-auto relative z-10">
                    <h1 className="text-4xl md:text-5xl font-bold mb-4 text-center">
                        Resources for Campgrounds
                    </h1>
                    <p className="text-xl text-center max-w-3xl mx-auto">
                        Help your guests find reliable RV service and support when they need it most
                    </p>
                </div>
            </header>

            {/* Main Content */}
            <main className="container mx-auto px-4 py-16">
                {/* Introduction Section */}
                <div className="max-w-3xl mx-auto text-center mb-16">
                    <h2 className="text-3xl font-bold mb-8 text-[#43806c]">
                        Supporting Your RV Community
                    </h2>
                    <p className="text-lg text-gray-600 mb-6">
                        As a campground, you know that RV maintenance and repairs are a common concern for your guests.
                        When RVers need service, they often turn to your office for recommendations. That's where RV Help comes in.
                    </p>
                    <p className="text-lg text-gray-600 mb-6">
                        We connect RV owners with certified mobile technicians and inspectors across North America,
                        bringing expert service directly to your campground. Our network of over 1,500 certified
                        professionals ensures your guests can get the help they need, right at their campsite.
                    </p>
                    <p className="text-lg text-gray-600">
                        Help your guests find reliable service by displaying our materials in your office or on your
                        bulletin board. We provide business cards and brochures that make it easy for RVers to connect
                        with certified technicians in your area.
                    </p>
                </div>

                {/* Resources Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mx-auto">
                    {/* PDF Brochure Card */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 mb-4">
                                <FileText className="h-5 w-5" />
                                Printable Brochure With QR Code
                            </CardTitle>
                            <CardDescription>
                                Download our RV Help brochure for your campground. Prominent QR code allows RVers to easily connect with local techs.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button className="w-full" onClick={downloadBrochure}>
                                Download PDF
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Business Cards Card */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 mb-4">
                                <CreditCard className="h-5 w-5" />
                                Printable Business Cards
                            </CardTitle>
                            <CardDescription>
                                Print RV Help business cards to display in your office or on your bulletin board.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button
                                className="w-full"
                                onClick={downloadBusinessCards}
                            >
                                Download Business Cards
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Support Card */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 mb-4">
                                <HeadsetIcon className="h-5 w-5" />
                                Contact Support
                            </CardTitle>
                            <CardDescription>
                                Need help? Have something else in mind? Our team is here to help empower you to serve RV Owners!
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button
                                className="w-full"
                                variant="secondary"
                                onClick={() => setIsSupportModalOpen(true)}
                            >
                                Get Support
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </main>

            {/* Modals */}
            <SupportModal
                open={isBusinessCardModalOpen}
                onOpenChange={setIsBusinessCardModalOpen}
                defaultSubject="Campground Business Card Request"
                defaultMessage={`I would like to request business cards for my campground. Please include the following information:

Campground Name:
Address:
Number of cards needed:

Thank you!`}
            />
            <SupportModal
                open={isSupportModalOpen}
                onOpenChange={setIsSupportModalOpen}
            />
        </div>
    );
} 