"use client";

import { PageHeader } from "@/components/ui/PageHeader";
import Link from "next/link";
import { usePathname } from "next/navigation";

export default function AdminSettingsLayout({
    children
}: {
    children: React.ReactNode;
}) {
    return (
        <div className="container mx-auto py-6 space-y-6">
            <PageHeader
                title="Admin Settings"
                description="Manage system settings and configurations"
            />

            <nav className="flex space-x-4 pb-4 border-b">
                <SettingsNavLink href="/admin/settings/safelist">
                    Safelist
                </SettingsNavLink>
                <SettingsNavLink href="/admin/settings/blacklist">
                    Blacklist
                </SettingsNavLink>
            </nav>

            <div className="bg-white p-4 rounded-md shadow-sm border">
                {children}
            </div>
        </div>
    );
}

function SettingsNavLink({ href, children }: { href: string; children: React.ReactNode }) {
    const pathname = usePathname();
    const isActive = pathname === href;

    return (
        <Link
            href={href}
            className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${isActive
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:text-foreground hover:bg-muted"
                }`}
        >
            {children}
        </Link>
    );
} 