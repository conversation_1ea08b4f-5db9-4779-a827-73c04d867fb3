"use client";

import { AddBlacklistEntry } from "@/components/admin/blacklist/AddBlacklistEntry";
import { BlacklistTable } from "@/components/admin/blacklist/BlacklistTable";
import { useRef } from "react";

export default function BlacklistPage() {
    const tableRef = useRef<{ fetchEntries: () => Promise<void> }>(null);

    const handleEntryAdded = () => {
        tableRef.current?.fetchEntries();
    };

    return (
        <div className="space-y-6">
            <AddBlacklistEntry onSuccess={handleEntryAdded} />
            <BlacklistTable ref={tableRef} />
        </div>
    );
} 