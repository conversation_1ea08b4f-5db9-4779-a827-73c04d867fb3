"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface DeleteListingDialogProps {
    isOpen: boolean;
    onClose: () => void;
    listingId: string;
    listingName: string;
    onSuccess: () => void;
}

type DeletionStatus = "DELETED" | "BANNED" | "CLOSED" | "SUSPENDED";

const statusOptions = [
    { value: "DELETED" as DeletionStatus, label: "Deleted", description: "Standard deletion" },
    { value: "BANNED" as DeletionStatus, label: "Banned", description: "Policy violation" },
    { value: "CLOSED" as DeletionStatus, label: "Closed", description: "Business permanently closed" },
    { value: "SUSPENDED" as DeletionStatus, label: "Suspended", description: "Temporary suspension" }
];

const defaultReasons = {
    DELETED: "admin_deleted",
    BANNED: "policy_violation",
    CLOSED: "business_closed",
    SUSPENDED: "under_review"
};

export default function DeleteListingDialog({
    isOpen,
    onClose,
    listingId,
    listingName,
    onSuccess
}: DeleteListingDialogProps) {
    const [status, setStatus] = useState<DeletionStatus>("DELETED");
    const [deletionReason, setDeletionReason] = useState("");
    const [adminNotes, setAdminNotes] = useState("");
    const [isDeleting, setIsDeleting] = useState(false);

    const handleSubmit = async () => {
        if (!deletionReason.trim()) {
            toast.error("Please provide a deletion reason");
            return;
        }

        setIsDeleting(true);
        try {
            const response = await fetch(`/api/admin/listings/${listingId}`, {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    status,
                    deletion_reason: deletionReason,
                    admin_notes: adminNotes.trim() || undefined
                })
            });

            if (!response.ok) {
                throw new Error("Failed to delete listing");
            }

            toast.success(`Listing ${status.toLowerCase()} successfully`);
            onSuccess();
            onClose();
        } catch (error) {
            console.error("Error deleting listing:", error);
            toast.error("Failed to delete listing");
        } finally {
            setIsDeleting(false);
        }
    };

    const handleClose = () => {
        if (!isDeleting) {
            setStatus("DELETED");
            setDeletionReason("");
            setAdminNotes("");
            onClose();
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle>Delete Listing</DialogTitle>
                    <DialogDescription>
                        You are about to delete &quot;{listingName}&quot;. This action will hide the listing
                        from public view but preserve all historical data including leads and reviews.
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                    <div>
                        <label className="text-sm font-medium mb-2 block">
                            Status Type
                        </label>
                        <Select
                            value={status}
                            onValueChange={(value: DeletionStatus) => {
                                setStatus(value);
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                {statusOptions.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        <div>
                                            <div className="font-medium">{option.label}</div>
                                            <div className="text-xs text-muted-foreground">
                                                {option.description}
                                            </div>
                                        </div>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    <div>
                        <label className="text-sm font-medium mb-2 block">
                            Reason <span className="text-red-500">*</span>
                        </label>
                        <Textarea
                            value={deletionReason}
                            onChange={(e) => setDeletionReason(e.target.value)}
                            placeholder="Enter the specific reason for this action..."
                            rows={2}
                        />
                    </div>

                    <div>
                        <label className="text-sm font-medium mb-2 block">
                            Admin Notes (Optional)
                        </label>
                        <Textarea
                            value={adminNotes}
                            onChange={(e) => setAdminNotes(e.target.value)}
                            placeholder="Add any additional context or notes..."
                            rows={3}
                        />
                    </div>
                </div>

                <DialogFooter>
                    <Button variant="outline" onClick={handleClose} disabled={isDeleting}>
                        Cancel
                    </Button>
                    <Button
                        variant="destructive"
                        onClick={handleSubmit}
                        disabled={isDeleting || !deletionReason}
                    >
                        {isDeleting ? "Processing..." : `${status === "DELETED" ? "Delete" : status} Listing`}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
} 