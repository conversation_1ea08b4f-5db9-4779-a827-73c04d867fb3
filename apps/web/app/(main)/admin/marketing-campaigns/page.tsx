"use client";

import ImageUpload from "@/components/ImageUpload";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger
} from "@/components/ui/tooltip";
import { withAuthorization } from "@/lib/hooks/withAuthorization";
import { DiscountType, MarketingCampaignStatus } from "@rvhelp/database";
import { Edit, ExternalLink, Plus, RotateCcw, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface MarketingCampaign {
	id: string;
	slug: string;
	title: string;
	description: string;
	discount_type: DiscountType;
	discount_value: number;
	coupon_code?: string;
	status: MarketingCampaignStatus;
	expires_at: string | null;
	views_count: number;
	leads_count: number;
	conversions_count: number;
	created_at: string;
	updated_at: string;
	page_title?: string;
	page_subtitle?: string;
	page_description?: string;
	button_text?: string;
	success_message?: string;
	background_image?: string;
	logo?: string;
}

interface CampaignFormData {
	title: string;
	description: string;
	slug: string;
	discount_type: DiscountType;
	discount_value: number;
	coupon_code: string;
	expires_at: string;
	page_title: string;
	page_subtitle: string;
	page_description: string;
	button_text: string;
	success_message: string;
	background_image: string;
	logo: string;
}

const initialFormData: CampaignFormData = {
	title: "",
	description: "",
	slug: "",
	discount_type: "PERCENTAGE" as DiscountType,
	discount_value: 0,
	coupon_code: "",
	expires_at: "",
	page_title: "",
	page_subtitle: "",
	page_description: "",
	button_text: "Get My Discount",
	success_message: "",
	background_image: "",
	logo: ""
};

// Extract CampaignForm component outside to prevent re-renders
interface CampaignFormProps {
	isEdit?: boolean;
	formData: CampaignFormData;
	onInputChange: (
		field: keyof CampaignFormData,
		value: string | number
	) => void;
	generateSlug: (title: string) => string;
}

const CampaignForm = ({
	isEdit = false,
	formData,
	onInputChange,
	generateSlug
}: CampaignFormProps) => (
	<div className="space-y-6">
		<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
			<div>
				<Label htmlFor="title">Campaign Title</Label>
				<Input
					id="title"
					value={formData.title}
					onChange={(e) => {
						onInputChange("title", e.target.value);
						if (!isEdit) {
							onInputChange("slug", generateSlug(e.target.value));
						}
					}}
					placeholder="Enter campaign title"
				/>
			</div>
			<div>
				<Label htmlFor="slug">URL Slug</Label>
				<Input
					id="slug"
					value={formData.slug}
					onChange={(e) => onInputChange("slug", e.target.value)}
					placeholder="campaign-slug"
					disabled={isEdit}
				/>
				<p className="text-sm text-gray-500 mt-1">
					Landing page will be: /lp/{formData.slug}
				</p>
			</div>
		</div>

		<div>
			<Label htmlFor="description">Description</Label>
			<Textarea
				id="description"
				value={formData.description}
				onChange={(e) => onInputChange("description", e.target.value)}
				placeholder="Campaign description"
				rows={3}
			/>
		</div>

		<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
			<div>
				<Label htmlFor="discount_type">Discount Type</Label>
				<Select
					value={formData.discount_type}
					onValueChange={(value) =>
						onInputChange("discount_type", value as DiscountType)
					}
				>
					<SelectTrigger>
						<SelectValue />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="PERCENTAGE">Percentage</SelectItem>
						<SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
					</SelectContent>
				</Select>
			</div>
			<div>
				<Label htmlFor="discount_value">Discount Value</Label>
				<Input
					id="discount_value"
					type="number"
					value={formData.discount_value}
					onChange={(e) =>
						onInputChange("discount_value", parseFloat(e.target.value) || 0)
					}
					placeholder={formData.discount_type === "PERCENTAGE" ? "50" : "99"}
				/>
			</div>
		</div>

		<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
			<div>
				<Label htmlFor="coupon_code">Coupon Code (Optional)</Label>
				<Input
					id="coupon_code"
					value={formData.coupon_code}
					onChange={(e) => onInputChange("coupon_code", e.target.value)}
					placeholder="SUMMER2024"
				/>
				<p className="text-sm text-gray-500 mt-1">
					Leave blank to auto-generate unique codes for each lead
				</p>
			</div>
			<div>
				<Label htmlFor="expires_at">Expiration Date</Label>
				<Input
					id="expires_at"
					type="date"
					value={formData.expires_at}
					onChange={(e) => onInputChange("expires_at", e.target.value)}
				/>
			</div>
		</div>

		<Separator />

		<div className="space-y-4">
			<h3 className="text-lg font-semibold">Landing Page Customization</h3>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<Label htmlFor="page_title">Page Title</Label>
					<Input
						id="page_title"
						value={formData.page_title}
						onChange={(e) => onInputChange("page_title", e.target.value)}
						placeholder="Get 50% Off RV Help Pro"
					/>
				</div>
				<div>
					<Label htmlFor="page_subtitle">Page Subtitle</Label>
					<Input
						id="page_subtitle"
						value={formData.page_subtitle}
						onChange={(e) => onInputChange("page_subtitle", e.target.value)}
						placeholder="Limited Time Offer"
					/>
				</div>
			</div>

			<div>
				<Label htmlFor="page_description">Page Description</Label>
				<Textarea
					id="page_description"
					value={formData.page_description}
					onChange={(e) => onInputChange("page_description", e.target.value)}
					placeholder="Enter your email to receive your exclusive discount code"
					rows={3}
				/>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<Label htmlFor="button_text">Button Text</Label>
					<Input
						id="button_text"
						value={formData.button_text}
						onChange={(e) => onInputChange("button_text", e.target.value)}
						placeholder="Get My Discount"
					/>
				</div>
			</div>

			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Visual Assets</h3>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div>
						<ImageUpload
							label="Background Image"
							path="marketing-campaigns/backgrounds"
							description="Upload a background image for the landing page (optional)"
							onImageUpload={(url) => onInputChange("background_image", url)}
							currentImageUrl={formData.background_image}
							options={{
								maxFileSize: 5 * 1024 * 1024, // 5MB
								maxDimensions: { width: 1920, height: 1080 }
							}}
						/>
					</div>
					<div>
						<ImageUpload
							label="Partner Logo"
							path="marketing-campaigns/logos"
							description="Upload a logo for your partner brand (optional)"
							onImageUpload={(url) => onInputChange("logo", url)}
							currentImageUrl={formData.logo}
							options={{
								maxFileSize: 2 * 1024 * 1024, // 2MB
								maxDimensions: { width: 800, height: 400 }
							}}
						/>
					</div>
				</div>
			</div>

			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Display Settings</h3>

				<div>
					<Label htmlFor="success_message">Success Message</Label>
					<Textarea
						id="success_message"
						value={formData.success_message}
						onChange={(e) => onInputChange("success_message", e.target.value)}
						placeholder="Thank you! Check your email for your discount code."
						rows={3}
					/>
				</div>
			</div>
		</div>
	</div>
);

function MarketingCampaignsPage() {
	const [campaigns, setCampaigns] = useState<MarketingCampaign[]>([]);
	const [loading, setLoading] = useState(true);
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
	const [isEditModalOpen, setIsEditModalOpen] = useState(false);
	const [editingCampaign, setEditingCampaign] =
		useState<MarketingCampaign | null>(null);
	const [formData, setFormData] = useState<CampaignFormData>(initialFormData);
	const [resetConfirmOpen, setResetConfirmOpen] = useState(false);
	const [campaignToReset, setCampaignToReset] = useState<string | null>(null);

	useEffect(() => {
		fetchCampaigns();
	}, []);

	const fetchCampaigns = async () => {
		try {
			const response = await fetch("/api/admin/marketing-campaigns");
			if (response.ok) {
				const data = await response.json();
				setCampaigns(data.campaigns);
			}
		} catch (error) {
			console.error("Error fetching campaigns:", error);
			toast.error("Failed to load campaigns");
		} finally {
			setLoading(false);
		}
	};

	const handleCreateCampaign = async () => {
		try {
			const response = await fetch("/api/admin/marketing-campaigns", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(formData)
			});

			if (response.ok) {
				toast.success("Campaign created successfully");
				setIsCreateModalOpen(false);
				setFormData(initialFormData);
				fetchCampaigns();
			} else {
				const error = await response.json();
				toast.error(error.message || "Failed to create campaign");
			}
		} catch (error) {
			console.error("Error creating campaign:", error);
			toast.error("Failed to create campaign");
		}
	};

	const handleUpdateCampaign = async () => {
		if (!editingCampaign) return;

		try {
			const response = await fetch(
				`/api/admin/marketing-campaigns/${editingCampaign.id}`,
				{
					method: "PUT",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify(formData)
				}
			);

			if (response.ok) {
				toast.success("Campaign updated successfully");
				setIsEditModalOpen(false);
				setEditingCampaign(null);
				setFormData(initialFormData);
				fetchCampaigns();
			} else {
				const error = await response.json();
				toast.error(error.message || "Failed to update campaign");
			}
		} catch (error) {
			console.error("Error updating campaign:", error);
			toast.error("Failed to update campaign");
		}
	};

	const handleDeleteCampaign = async (id: string) => {
		if (!confirm("Are you sure you want to delete this campaign?")) return;

		try {
			const response = await fetch(`/api/admin/marketing-campaigns/${id}`, {
				method: "DELETE"
			});

			if (response.ok) {
				toast.success("Campaign deleted successfully");
				fetchCampaigns();
			} else {
				const error = await response.json();
				toast.error(error.message || "Failed to delete campaign");
			}
		} catch (error) {
			console.error("Error deleting campaign:", error);
			toast.error("Failed to delete campaign");
		}
	};

	const handleToggleStatus = async (
		id: string,
		newStatus: MarketingCampaignStatus
	) => {
		try {
			const response = await fetch(
				`/api/admin/marketing-campaigns/${id}/toggle-status`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({ status: newStatus })
				}
			);

			if (response.ok) {
				toast.success(`Campaign ${newStatus.toLowerCase()} successfully`);
				fetchCampaigns();
			} else {
				const error = await response.json();
				toast.error(error.message || "Failed to update campaign status");
			}
		} catch (error) {
			console.error("Error updating campaign status:", error);
			toast.error("Failed to update campaign status");
		}
	};

	const handleResetStats = async (campaignId: string) => {
		try {
			const response = await fetch(
				`/api/admin/marketing-campaigns/${campaignId}/reset-stats`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({ confirm: true })
				}
			);

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to reset stats");
			}

			const result = await response.json();
			toast.success("Campaign stats reset successfully");

			// Update the campaign in the list
			setCampaigns((prev) =>
				prev.map((campaign) =>
					campaign.id === campaignId
						? {
								...campaign,
								views_count: 0,
								leads_count: 0,
								conversions_count: 0
							}
						: campaign
				)
			);
		} catch (error) {
			console.error("Error resetting stats:", error);
			toast.error(
				error instanceof Error ? error.message : "Failed to reset stats"
			);
		} finally {
			setResetConfirmOpen(false);
			setCampaignToReset(null);
		}
	};

	const openResetConfirm = (campaignId: string) => {
		setCampaignToReset(campaignId);
		setResetConfirmOpen(true);
	};

	const openEditModal = (campaign: MarketingCampaign) => {
		setEditingCampaign(campaign);
		setFormData({
			title: campaign.title,
			description: campaign.description,
			slug: campaign.slug,
			discount_type: campaign.discount_type,
			discount_value: campaign.discount_value,
			coupon_code: campaign.coupon_code || "",
			expires_at: campaign.expires_at
				? new Date(campaign.expires_at).toISOString().split("T")[0]
				: "",
			page_title: campaign.page_title || "",
			page_subtitle: campaign.page_subtitle || "",
			page_description: campaign.page_description || "",
			button_text: campaign.button_text || "Get My Discount",
			success_message: campaign.success_message || "",
			background_image: campaign.background_image || "",
			logo: campaign.logo || ""
		});
		setIsEditModalOpen(true);
	};

	const handleInputChange = (
		field: keyof CampaignFormData,
		value: string | number
	) => {
		setFormData((prev) => ({
			...prev,
			[field]: value
		}));
	};

	const generateSlug = (title: string) => {
		return title
			.toLowerCase()
			.replace(/[^a-z0-9 -]/g, "")
			.replace(/\s+/g, "-")
			.replace(/-+/g, "-")
			.trim();
	};

	const getStatusColor = (status: MarketingCampaignStatus) => {
		switch (status) {
			case "ACTIVE":
				return "bg-green-100 text-green-800";
			case "PAUSED":
				return "bg-yellow-100 text-yellow-800";
			case "DRAFT":
				return "bg-gray-100 text-gray-800";
			case "EXPIRED":
				return "bg-red-100 text-red-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const formatDiscount = (campaign: MarketingCampaign) => {
		if (campaign.discount_type === "PERCENTAGE") {
			return `${campaign.discount_value}% off`;
		}
		return `$${campaign.discount_value} off`;
	};

	if (loading) {
		return <div className="p-6">Loading campaigns...</div>;
	}

	return (
		<div className="p-6 max-w-7xl mx-auto">
			<div className="flex justify-between items-center mb-6">
				<div>
					<h1 className="text-2xl font-bold">Marketing Campaigns</h1>
					<p className="text-gray-600">
						Create and manage discount campaigns with landing pages
					</p>
				</div>
				<Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
					<DialogTrigger asChild>
						<Button>
							<Plus className="mr-2 h-4 w-4" />
							Create Campaign
						</Button>
					</DialogTrigger>
					<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
						<DialogHeader>
							<DialogTitle>Create New Campaign</DialogTitle>
						</DialogHeader>
						<CampaignForm
							formData={formData}
							onInputChange={handleInputChange}
							generateSlug={generateSlug}
						/>
						<div className="flex justify-end space-x-2 mt-6">
							<Button
								variant="outline"
								onClick={() => setIsCreateModalOpen(false)}
							>
								Cancel
							</Button>
							<Button onClick={handleCreateCampaign}>Create Campaign</Button>
						</div>
					</DialogContent>
				</Dialog>
			</div>

			<div className="grid gap-6">
				{campaigns.map((campaign) => (
					<Card key={campaign.id}>
						<CardHeader>
							<div className="flex justify-between items-start">
								<div>
									<CardTitle className="flex items-center gap-2">
										{campaign.title}
										<Badge className={getStatusColor(campaign.status)}>
											{campaign.status}
										</Badge>
									</CardTitle>
									<CardDescription>{campaign.description}</CardDescription>
								</div>
								<div className="flex items-center space-x-2">
									<Button
										variant="outline"
										size="sm"
										onClick={() =>
											window.open(`/lp/${campaign.slug}`, "_blank")
										}
									>
										<ExternalLink className="h-4 w-4" />
									</Button>
									<Button
										variant="outline"
										size="sm"
										onClick={() => openEditModal(campaign)}
									>
										<Edit className="h-4 w-4" />
									</Button>
									<Button
										variant="outline"
										size="sm"
										onClick={() => handleDeleteCampaign(campaign.id)}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
									<TooltipProvider>
										<Tooltip>
											<TooltipTrigger asChild>
												<Button
													variant="outline"
													size="sm"
													onClick={() => openResetConfirm(campaign.id)}
												>
													<RotateCcw className="h-4 w-4" />
												</Button>
											</TooltipTrigger>
											<TooltipContent>
												<p>Reset campaign stats (views, leads, conversions)</p>
											</TooltipContent>
										</Tooltip>
									</TooltipProvider>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
								<div>
									<p className="text-sm font-medium">Discount</p>
									<p className="text-lg">{formatDiscount(campaign)}</p>
								</div>
								<div>
									<p className="text-sm font-medium">Views</p>
									<p className="text-lg">{campaign.views_count}</p>
								</div>
								<div>
									<p className="text-sm font-medium">Leads</p>
									<p className="text-lg">{campaign.leads_count}</p>
								</div>
								<div>
									<p className="text-sm font-medium">Conversions</p>
									<p className="text-lg">{campaign.conversions_count}</p>
								</div>
							</div>
							<div className="mt-4">
								<p className="text-sm text-gray-600">
									Landing Page:{" "}
									<code className="bg-gray-100 px-1 rounded">
										/lp/{campaign.slug}
									</code>
								</p>
								{campaign.expires_at && (
									<p className="text-sm text-gray-600">
										Expires:{" "}
										{new Date(campaign.expires_at).toLocaleDateString()}
									</p>
								)}
							</div>
							<div className="mt-4 flex space-x-2">
								{campaign.status === "ACTIVE" && (
									<Button
										variant="outline"
										size="sm"
										onClick={() => handleToggleStatus(campaign.id, "PAUSED")}
									>
										Pause
									</Button>
								)}
								{campaign.status === "PAUSED" && (
									<Button
										variant="outline"
										size="sm"
										onClick={() => handleToggleStatus(campaign.id, "ACTIVE")}
									>
										Activate
									</Button>
								)}
								{campaign.status === "DRAFT" && (
									<Button
										variant="outline"
										size="sm"
										onClick={() => handleToggleStatus(campaign.id, "ACTIVE")}
									>
										Activate
									</Button>
								)}
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			{campaigns.length === 0 && (
				<div className="text-center py-12">
					<p className="text-gray-500">No campaigns created yet.</p>
				</div>
			)}

			{/* Edit Modal */}
			<Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
				<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
					<DialogHeader>
						<DialogTitle>Edit Campaign</DialogTitle>
					</DialogHeader>
					<CampaignForm
						isEdit={true}
						formData={formData}
						onInputChange={handleInputChange}
						generateSlug={generateSlug}
					/>
					<div className="flex justify-end space-x-2 mt-6">
						<Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
							Cancel
						</Button>
						<Button onClick={handleUpdateCampaign}>Update Campaign</Button>
					</div>
				</DialogContent>
			</Dialog>

			{/* Reset Confirmation Dialog */}
			<Dialog open={resetConfirmOpen} onOpenChange={setResetConfirmOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Confirm Reset</DialogTitle>
					</DialogHeader>
					<div className="py-4">
						<p>Are you sure you want to reset the stats for this campaign?</p>
						<p>This action cannot be undone.</p>
					</div>
					<div className="flex justify-end space-x-2">
						<Button
							variant="outline"
							onClick={() => setResetConfirmOpen(false)}
						>
							Cancel
						</Button>
						<Button
							variant="destructive"
							onClick={() =>
								campaignToReset && handleResetStats(campaignToReset)
							}
						>
							Reset Stats
						</Button>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}

export default withAuthorization(MarketingCampaignsPage, "ADMIN");
