"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
	Di<PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { format } from "date-fns";
import { MapPin, Plus, Send } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface DispatchEmail {
	id: string;
	title: string;
	subject: string;
	status: "DRAFT" | "SENT" | "FAILED";
	sent_at: string | null;
	created_at: string;
	category: string | null;
	campaign_url: string | null; // Contains targeting data
	recipients: Array<{
		listingId: string;
		email: string;
		sentAt: string;
		status: "sent" | "failed";
		errorMessage?: string;
		distance?: number;
	}> | null;
}

interface Targeting {
	address: string;
	latitude: number;
	longitude: number;
	count: number;
}

export default function DispatchEmailsPage() {
	const [emails, setEmails] = useState<DispatchEmail[]>([]);
	const [loading, setLoading] = useState(true);
	const [sendingId, setSendingId] = useState<string | null>(null);
	const [confirmDialog, setConfirmDialog] = useState<{
		open: boolean;
		email: DispatchEmail | null;
	}>({ open: false, email: null });

	useEffect(() => {
		fetchEmails();
	}, []);

	const fetchEmails = async () => {
		try {
			const response = await fetch("/api/admin/dispatch-emails");
			const data = await response.json();
			if (response.ok) {
				setEmails(data);
			} else {
				console.error("Error fetching dispatch emails:", data);
			}
		} catch (error) {
			console.error("Error fetching dispatch emails:", error);
		} finally {
			setLoading(false);
		}
	};

	const parseTargeting = (campaignUrl: string | null): Targeting | null => {
		if (!campaignUrl) return null;
		try {
			const parsed = JSON.parse(campaignUrl);
			return parsed.type === "nearest_verified_techs" ? parsed : null;
		} catch {
			return null;
		}
	};

	const handleSendNow = (email: DispatchEmail) => {
		setConfirmDialog({ open: true, email });
	};

	const confirmSendNow = async () => {
		if (!confirmDialog.email) return;

		setSendingId(confirmDialog.email.id);
		setConfirmDialog({ open: false, email: null });

		try {
			const response = await fetch(
				`/api/admin/dispatch-emails/${confirmDialog.email.id}/send`,
				{
					method: "POST"
				}
			);

			const data = await response.json();

			if (response.ok) {
				toast.success(`Success! ${data.message}`);
				fetchEmails(); // Refresh the list
			} else {
				toast.error(`Error: ${data.error}`);
			}
		} catch (error) {
			console.error("Error sending dispatch email:", error);
			toast.error("Error sending dispatch email");
		} finally {
			setSendingId(null);
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "DRAFT":
				return "text-gray-500";
			case "SENT":
				return "text-green-500";
			case "FAILED":
				return "text-red-500";
			default:
				return "text-gray-500";
		}
	};

	const canBeSent = (status: string) => {
		return status === "DRAFT";
	};

	return (
		<>
			<Card>
				<CardHeader>
					<div className="flex justify-between items-center">
						<CardTitle>Service Dispatch Emails</CardTitle>
						<Link href="/admin/communication/dispatch/new">
							<Button>
								<Plus className="w-4 h-4 mr-2" />
								New Dispatch
							</Button>
						</Link>
					</div>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div>Loading...</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Title</TableHead>
									<TableHead>Category</TableHead>
									<TableHead>Target Location</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>Recipients</TableHead>
									<TableHead>Sent At</TableHead>
									<TableHead>Created At</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{emails.map((email) => {
									const targeting = parseTargeting(email.campaign_url);

									return (
										<TableRow key={email.id}>
											<TableCell>{email.title}</TableCell>
											<TableCell>{email.category || "-"}</TableCell>
											<TableCell>
												{targeting ? (
													<div className="flex items-center gap-1 text-sm">
														<MapPin className="w-3 h-3 text-gray-400" />
														<span>{targeting.address}</span>
														<span className="text-gray-500">
															(nearest {targeting.count})
														</span>
													</div>
												) : (
													<span className="text-gray-500 text-sm">
														No targeting
													</span>
												)}
											</TableCell>
											<TableCell>
												<span className={getStatusColor(email.status)}>
													{email.status}
												</span>
											</TableCell>
											<TableCell>
												{email.recipients ? (
													<div className="text-sm">
														<div>{email.recipients.length} total</div>
														<div className="text-green-600">
															{
																email.recipients.filter(
																	(r) => r.status === "sent"
																).length
															}{" "}
															sent
														</div>
														{email.recipients.filter(
															(r) => r.status === "failed"
														).length > 0 && (
															<div className="text-red-600">
																{
																	email.recipients.filter(
																		(r) => r.status === "failed"
																	).length
																}{" "}
																failed
															</div>
														)}
														{email.recipients.length > 0 &&
															email.recipients[0].distance !== undefined && (
																<div className="text-gray-500 text-xs">
																	Up to{" "}
																	{Math.max(
																		...email.recipients.map(
																			(r) => r.distance || 0
																		)
																	)}{" "}
																	mi
																</div>
															)}
													</div>
												) : (
													"-"
												)}
											</TableCell>
											<TableCell>
												{email.sent_at
													? format(
															new Date(email.sent_at),
															"MMM d, yyyy h:mm a"
														)
													: "-"}
											</TableCell>
											<TableCell>
												{format(
													new Date(email.created_at),
													"MMM d, yyyy h:mm a"
												)}
											</TableCell>
											<TableCell>
												<div className="flex gap-2">
													<Link
														href={`/admin/communication/dispatch/${email.id}`}
													>
														<Button variant="outline" size="sm">
															View
														</Button>
													</Link>
													{canBeSent(email.status) && (
														<Button
															variant="default"
															size="sm"
															onClick={() => handleSendNow(email)}
															disabled={sendingId === email.id}
														>
															<Send className="w-4 h-4 mr-1" />
															{sendingId === email.id
																? "Sending..."
																: "Send Now"}
														</Button>
													)}
												</div>
											</TableCell>
										</TableRow>
									);
								})}
							</TableBody>
						</Table>
					)}
				</CardContent>
			</Card>

			<Dialog
				open={confirmDialog.open}
				onOpenChange={(open) => setConfirmDialog({ open, email: null })}
			>
				<DialogContent className="max-w-md">
					<DialogHeader>
						<DialogTitle>Confirm Send Email</DialogTitle>
						<DialogDescription>
							{(() => {
								const targeting = confirmDialog.email?.campaign_url
									? parseTargeting(confirmDialog.email.campaign_url)
									: null;

								return targeting
									? `Are you sure you want to send "${confirmDialog.email?.title}" to the nearest ${targeting.count} verified technicians to ${targeting.address}? This cannot be undone.`
									: `Are you sure you want to send "${confirmDialog.email?.title}"? This cannot be undone.`;
							})()}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setConfirmDialog({ open: false, email: null })}
						>
							Cancel
						</Button>
						<Button onClick={confirmSendNow}>Send Now</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
