"use client";

import { PlacesAutocomplete } from "@/components/places/PlacesAutocomplete";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import Wysiwyg from "@/components/Wysiwyg";
import { categories } from "@/lib/categories";
import { Category } from "@/types/categories";
import { zodResolver } from "@hookform/resolvers/zod";
import { Save, Send } from "lucide-react";
import { useRouter } from "next/navigation";
import { useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import * as z from "zod";

const formSchema = z.object({
	title: z.string().min(1, "Title is required"),
	body: z.string().min(1, "Body is required"),
	category: z
		.string()
		.min(1, "Category is required")
		.refine((val) => val !== "", {
			message: "Please select a category"
		}),
	location: z.object({
		address: z.string().min(1, "Location is required"),
		latitude: z.number(),
		longitude: z.number()
	})
});

type SubmitAction = "draft" | "send_now";

export default function NewDispatchEmailPage() {
	const router = useRouter();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [confirmDialog, setConfirmDialog] = useState<{
		open: boolean;
		action: SubmitAction;
		values: z.infer<typeof formSchema> | null;
	}>({ open: false, action: "draft", values: null });

	// Add ref to track if a submission is in progress
	const submissionInProgress = useRef(false);

	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			title: "",
			body: "",
			category: "",
			location: {
				address: "",
				latitude: 0,
				longitude: 0
			}
		}
	});

	const handleSubmit =
		(action: SubmitAction) => (values: z.infer<typeof formSchema>) => {
			// Prevent multiple simultaneous submissions
			if (submissionInProgress.current) {
				console.log(
					"🚫 Submission already in progress, ignoring duplicate click"
				);
				return;
			}

			console.log("📋 Form submitted", { action, values });
			if (action === "send_now") {
				console.log("⚠️ Opening confirmation dialog for send_now action");
				setConfirmDialog({ open: true, action, values });
			} else {
				console.log("💾 Proceeding with draft action");
				submitEmail(action, values);
			}
		};

	const confirmSendNow = () => {
		// Prevent multiple simultaneous submissions
		if (submissionInProgress.current) {
			console.log(
				"🚫 Submission already in progress, ignoring duplicate confirmation"
			);
			return;
		}

		console.log("✅ Confirmation dialog confirmed, proceeding with send_now");
		if (confirmDialog.values) {
			submitEmail(confirmDialog.action, confirmDialog.values);
		} else {
			console.error("❌ No values found in confirmDialog");
			toast.error("Error: No form data found. Please try again.");
		}
		setConfirmDialog({ open: false, action: "draft", values: null });
	};

	const submitEmail = async (
		action: SubmitAction,
		values: z.infer<typeof formSchema>
	) => {
		// Set submission in progress flag
		submissionInProgress.current = true;
		console.log("🚀 Starting submitEmail function", { action, values });
		setIsSubmitting(true);

		try {
			console.log("📝 Creating dispatch email as draft...");
			// Create the email as a draft
			const response = await fetch("/api/admin/dispatch-emails", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(values)
			});

			console.log("📨 Draft creation response:", {
				status: response.status,
				statusText: response.statusText,
				ok: response.ok
			});

			if (!response.ok) {
				const errorText = await response.text();
				console.error("❌ Draft creation failed:", errorText);
				throw new Error(
					`Failed to create dispatch email: ${response.status} ${response.statusText} - ${errorText}`
				);
			}

			const createdEmail = await response.json();
			console.log("✅ Draft email created successfully:", createdEmail);

			if (action === "send_now") {
				console.log("📤 Sending email immediately...");
				// Send immediately
				const sendResponse = await fetch(
					`/api/admin/dispatch-emails/${createdEmail.id}/send`,
					{
						method: "POST"
					}
				);

				console.log("📨 Send response:", {
					status: sendResponse.status,
					statusText: sendResponse.statusText,
					ok: sendResponse.ok
				});

				const sendData = await sendResponse.json();
				console.log("📋 Send response data:", sendData);

				if (sendResponse.ok) {
					console.log("✅ Email sent successfully:", sendData.message);
					toast.success(`Success! ${sendData.message}`);
				} else {
					console.error("❌ Email sending failed:", sendData.error);
					toast.error(`Email created but sending failed: ${sendData.error}`);
				}
			} else {
				console.log("💾 Email saved as draft only");
				toast.success("Email saved as draft successfully");
			}

			console.log("🔄 Redirecting to dispatch list...");
			router.push("/admin/communication/dispatch");
		} catch (error) {
			console.error("💥 Error in submitEmail:", error);
			toast.error(
				`Error creating dispatch email: ${error instanceof Error ? error.message : "Unknown error"}`
			);
		} finally {
			console.log("🏁 submitEmail function completed");
			setIsSubmitting(false);
			// Reset submission in progress flag
			submissionInProgress.current = false;
		}
	};

	return (
		<>
			<div className="container mx-auto py-8">
				<Card className="max-w-4xl mx-auto">
					<CardHeader>
						<CardTitle>Create New Dispatch Email</CardTitle>
						<p className="text-sm text-gray-600">
							This will be sent to the nearest 20 verified technicians to the
							selected location.
						</p>
					</CardHeader>
					<CardContent>
						<Form {...form}>
							<div className="space-y-6">
								<FormField
									control={form.control}
									name="title"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Title / Subject</FormLabel>
											<FormControl>
												<Input
													className="bg-gray-50"
													placeholder="Enter email title and subject"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="location"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Target Location</FormLabel>
												<FormControl>
													<PlacesAutocomplete
														value={field.value?.address || ""}
														onPlaceSelect={(address, details) => {
															if (details?.geometry?.location) {
																field.onChange({
																	address: address,
																	latitude: details.geometry.location.lat(),
																	longitude: details.geometry.location.lng()
																});
															}
														}}
														placeholder="Enter city, state or address"
														className="bg-gray-50"
														locationType="city"
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="category"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Category</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger className="bg-gray-50">
															<SelectValue placeholder="Select a category to target specific providers" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{Object.entries(categories).map(
															([id, category]) => (
																<SelectItem key={id} value={id}>
																	{(category as Category).name}
																</SelectItem>
															)
														)}
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<FormField
									control={form.control}
									name="body"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Body</FormLabel>
											<FormControl>
												<Wysiwyg
													variant="full"
													placeholder="Enter email body"
													className="min-h-[200px] rounded-md w-full"
													enableLinks={true}
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<div className="flex justify-between items-center pt-4 border-t">
									<Button
										type="button"
										variant="outline"
										onClick={() => router.back()}
										disabled={isSubmitting}
									>
										Cancel
									</Button>

									<div className="flex gap-3">
										<Button
											type="button"
											variant="outline"
											onClick={form.handleSubmit(handleSubmit("draft"))}
											disabled={isSubmitting}
										>
											<Save className="w-4 h-4 mr-2" />
											Save as Draft
										</Button>

										<Button
											type="button"
											variant="default"
											onClick={form.handleSubmit(handleSubmit("send_now"))}
											disabled={isSubmitting}
										>
											<Send className="w-4 h-4 mr-2" />
											{isSubmitting ? "Sending..." : "Send Now"}
										</Button>
									</div>
								</div>
							</div>
						</Form>
					</CardContent>
				</Card>
			</div>

			<Dialog
				open={confirmDialog.open}
				onOpenChange={(open) => setConfirmDialog({ ...confirmDialog, open })}
			>
				<DialogContent className="max-w-md">
					<DialogHeader>
						<DialogTitle>Confirm Send Email</DialogTitle>
						<DialogDescription>
							Are you sure you want to send &quot;{confirmDialog.values?.title}
							&quot; to the nearest 20 verified technicians to{" "}
							{confirmDialog.values?.location?.address}? This cannot be undone.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() =>
								setConfirmDialog({ open: false, action: "draft", values: null })
							}
						>
							Cancel
						</Button>
						<Button onClick={confirmSendNow}>Send Now</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
