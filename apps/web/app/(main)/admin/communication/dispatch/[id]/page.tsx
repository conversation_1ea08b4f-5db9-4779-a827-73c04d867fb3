"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	Di<PERSON>Header,
	DialogTitle
} from "@/components/ui/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import Wysiwyg from "@/components/Wysiwyg";
import { categories } from "@/lib/categories";
import { Category } from "@/types/categories";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { MapPin, Send } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import * as z from "zod";

const formSchema = z.object({
	title: z.string().min(1, "Title is required"),
	subject: z.string().min(1, "Subject is required"),
	body: z.string().min(1, "Body is required")
});

interface Recipient {
	listingId: string;
	email: string;
	sentAt: string;
	status: "sent" | "failed";
	errorMessage?: string;
	distance?: number;
}

interface DispatchEmail {
	id: string;
	title: string;
	subject: string;
	body: string;
	status: "DRAFT" | "SENT" | "FAILED";
	sent_at: string | null;
	created_at: string;
	category: string | null;
	campaign_url: string | null;
	recipients: Recipient[] | null;
}

export default function DispatchEmailPage({
	params
}: {
	params: { id: string };
}) {
	const router = useRouter();
	const [email, setEmail] = useState<DispatchEmail | null>(null);
	const [loading, setLoading] = useState(true);
	const [sending, setSending] = useState(false);
	const [confirmDialog, setConfirmDialog] = useState(false);
	const [recipientProviders, setRecipientProviders] = useState<
		Record<
			string,
			{ first_name: string; last_name: string; business_name: string }
		>
	>({});

	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			title: "",
			subject: "",
			body: ""
		}
	});

	useEffect(() => {
		fetchEmail();
	}, [params.id]);

	const fetchEmail = async () => {
		try {
			const response = await fetch(`/api/admin/dispatch-emails/${params.id}`);
			const data = await response.json();
			setEmail(data);
			form.reset({
				title: data.title,
				subject: data.subject,
				body: data.body
			});

			// If there are recipients, fetch provider details
			if (data.recipients && data.recipients.length > 0) {
				await fetchRecipientProviders(data.recipients);
			}
		} catch (error) {
			console.error("Error fetching dispatch email:", error);
		} finally {
			setLoading(false);
		}
	};

	const fetchRecipientProviders = async (recipients: Recipient[]) => {
		try {
			const listingIds = recipients.map((r) => r.listingId);
			const response = await fetch("/api/admin/listings/bulk", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ listingIds })
			});

			if (response.ok) {
				const providers = await response.json();
				const providerMap: Record<
					string,
					{ first_name: string; last_name: string; business_name: string }
				> = {};
				providers.forEach((provider: any) => {
					providerMap[provider.id] = {
						first_name: provider.first_name,
						last_name: provider.last_name,
						business_name: provider.business_name
					};
				});
				setRecipientProviders(providerMap);
			}
		} catch (error) {
			console.error("Error fetching recipient providers:", error);
		}
	};

	const onSubmit = async (values: z.infer<typeof formSchema>) => {
		try {
			const response = await fetch(`/api/admin/dispatch-emails/${params.id}`, {
				method: "PUT",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(values)
			});

			if (!response.ok) {
				throw new Error("Failed to update dispatch email");
			}

			router.push("/admin/communication/dispatch");
		} catch (error) {
			console.error("Error updating dispatch email:", error);
		}
	};

	const handleSendNow = () => {
		setConfirmDialog(true);
	};

	const confirmSendNow = async () => {
		if (!email) return;

		setSending(true);
		setConfirmDialog(false);

		try {
			const response = await fetch(
				`/api/admin/dispatch-emails/${email.id}/send`,
				{
					method: "POST"
				}
			);

			const data = await response.json();

			if (response.ok) {
				toast.success(`Success! ${data.message}`);
				// Refresh the email data to show recipients
				await fetchEmail();
			} else {
				toast.error(`Error: ${data.error}`);
			}
		} catch (error) {
			console.error("Error sending dispatch email:", error);
			toast.error("Error sending dispatch email");
		} finally {
			setSending(false);
		}
	};

	const getCategoryName = (categoryId: string | null) => {
		if (!categoryId) return "No category";
		const category = categories[
			categoryId as keyof typeof categories
		] as Category;
		return category?.name || categoryId;
	};

	const parseTargeting = (campaignUrl: string | null) => {
		if (!campaignUrl) return null;
		try {
			const parsed = JSON.parse(campaignUrl);
			return parsed.type === "nearest_verified_techs" ? parsed : null;
		} catch {
			return null;
		}
	};

	const getProviderName = (listingId: string) => {
		const provider = recipientProviders[listingId];
		if (!provider) return "Unknown Provider";
		return `${provider.first_name} ${provider.last_name}`;
	};

	const getBusinessName = (listingId: string) => {
		const provider = recipientProviders[listingId];
		return provider?.business_name || "-";
	};

	if (loading) {
		return <div>Loading...</div>;
	}

	if (!email) {
		return <div>Email not found</div>;
	}

	const isEditable = email.status === "DRAFT";
	const canBeSent = email.status === "DRAFT";
	const targeting = parseTargeting(email.campaign_url);

	return (
		<>
			<Card className="max-w-5xl mx-auto">
				<CardHeader>
					<CardTitle>Dispatch Email</CardTitle>
					<CardDescription>
						Edit the dispatch email details below.
					</CardDescription>
					<CardDescription>
						<div className="flex gap-2">
							<Button variant="outline" onClick={() => router.back()}>
								Back
							</Button>
							{canBeSent && (
								<Button
									variant="default"
									onClick={handleSendNow}
									disabled={sending}
								>
									<Send className="w-4 h-4 mr-2" />
									{sending ? "Sending..." : "Send Now"}
								</Button>
							)}
						</div>
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="bg-gray-50 rounded-lg p-4 mb-6">
						<div className="grid grid-cols-2 gap-4">
							<div>
								<p className="text-sm text-gray-500">Status</p>
								<p className="font-medium">{email.status}</p>
							</div>
							<div>
								<p className="text-sm text-gray-500">Category</p>
								<p className="font-medium">{getCategoryName(email.category)}</p>
							</div>
							<div>
								<p className="text-sm text-gray-500">Created</p>
								<p className="font-medium">
									{format(new Date(email.created_at), "MMM d, yyyy h:mm a")}
								</p>
							</div>
							{email.sent_at && (
								<div>
									<p className="text-sm text-gray-500">Sent At</p>
									<p className="font-medium">
										{format(new Date(email.sent_at), "MMM d, yyyy h:mm a")}
									</p>
								</div>
							)}
							{targeting && (
								<div className="col-span-2">
									<p className="text-sm text-gray-500">Target Location</p>
									<div className="flex items-center gap-1">
										<MapPin className="w-4 h-4 text-gray-400" />
										<span className="font-medium">{targeting.address}</span>
										<span className="text-gray-500">
											(nearest {targeting.count} verified providers)
										</span>
									</div>
								</div>
							)}
						</div>
					</div>

					{/* Recipients Section */}
					{email.recipients && email.recipients.length > 0 && (
						<Card className="mb-6">
							<CardHeader>
								<CardTitle className="text-lg">
									Recipients ({email.recipients.length})
								</CardTitle>
								<CardDescription>
									Verified providers who received this dispatch email
								</CardDescription>
							</CardHeader>
							<CardContent>
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Provider</TableHead>
											<TableHead>Business</TableHead>
											<TableHead>Email</TableHead>
											<TableHead>Distance</TableHead>
											<TableHead>Status</TableHead>
											<TableHead>Sent At</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{email.recipients.map((recipient, index) => (
											<TableRow key={index}>
												<TableCell>
													{getProviderName(recipient.listingId)}
												</TableCell>
												<TableCell>
													{getBusinessName(recipient.listingId)}
												</TableCell>
												<TableCell>
													<span className="text-sm font-mono">
														{recipient.email}
													</span>
												</TableCell>
												<TableCell>
													{recipient.distance
														? `${recipient.distance} mi`
														: "-"}
												</TableCell>
												<TableCell>
													<span
														className={
															recipient.status === "sent"
																? "text-green-600 font-medium"
																: "text-red-600 font-medium"
														}
													>
														{recipient.status === "sent"
															? "✅ Sent"
															: "❌ Failed"}
													</span>
													{recipient.status === "failed" &&
														recipient.errorMessage && (
															<div className="text-xs text-gray-500 mt-1">
																{recipient.errorMessage}
															</div>
														)}
												</TableCell>
												<TableCell>
													{format(new Date(recipient.sentAt), "MMM d, h:mm a")}
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</CardContent>
						</Card>
					)}

					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							<FormField
								control={form.control}
								name="title"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Title</FormLabel>
										<FormControl>
											<Input
												className="bg-gray-50"
												placeholder="Enter email title"
												{...field}
												disabled={!isEditable}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="subject"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Subject</FormLabel>
										<FormControl>
											<Input
												className="bg-gray-50"
												placeholder="Enter email subject"
												{...field}
												disabled={!isEditable}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="body"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Body</FormLabel>
										<FormControl>
											{isEditable ? (
												<Wysiwyg
													variant="full"
													placeholder="Enter email body"
													className="min-h-[200px] w-full"
													readOnly={true}
													{...field}
												/>
											) : (
												<div
													className="min-h-[200px] bg-gray-50 p-4 rounded-md prose w-full"
													dangerouslySetInnerHTML={{ __html: field.value }}
												/>
											)}
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{isEditable && (
								<div className="flex justify-end space-x-4">
									<Button type="submit">Update Dispatch</Button>
								</div>
							)}
						</form>
					</Form>
				</CardContent>
			</Card>

			<Dialog open={confirmDialog} onOpenChange={setConfirmDialog}>
				<DialogContent className="max-w-md">
					<DialogHeader>
						<DialogTitle>Confirm Send Email</DialogTitle>
						<DialogDescription>
							{(() => {
								if (!targeting) {
									return `Are you sure you want to send "${email?.title}" immediately?`;
								}
								return `Are you sure you want to send "${email?.title}" to the nearest ${targeting.count} verified ${getCategoryName(email.category).toLowerCase()} providers near ${targeting.address}? This cannot be undone.`;
							})()}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button variant="outline" onClick={() => setConfirmDialog(false)}>
							Cancel
						</Button>
						<Button onClick={confirmSendNow}>Send Now</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
