"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Listing, TroubleshootingRequest, User } from "@rvhelp/database";
import { format } from "date-fns";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface RequestWithRelations extends TroubleshootingRequest {
	user: User;
	listing: Listing;
}

type ActionType = "accepted" | "rejected" | "completed";

export default function TroubleshootingRequestDetails({
	request
}: {
	request: RequestWithRelations;
}) {
	const [isUpdating, setIsUpdating] = useState(false);
	const [notes, setNotes] = useState("");
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [pendingAction, setPendingAction] = useState<ActionType | null>(null);

	const handleActionClick = (action: ActionType) => {
		setPendingAction(action);
		setIsModalOpen(true);
	};

	const handleStatusUpdate = async () => {
		if (!pendingAction) return;

		if (pendingAction !== "completed" && !notes) {
			toast.error("Please add a note for the customer");
			return;
		}

		setIsUpdating(true);
		try {
			const response = await fetch(
				`/api/troubleshooting-requests?action=${pendingAction}`,
				{
					method: "PUT",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({ requestId: request.id, notes })
				}
			);

			if (!response.ok) {
				throw new Error("Failed to update request");
			}

			toast.success("Request updated successfully");
			setIsModalOpen(false);
			window.location.reload();
		} catch (error) {
			console.error("Error updating request:", error);
			toast.error("Failed to update request");
		} finally {
			setIsUpdating(false);
		}
	};

	const getActionTitle = () => {
		switch (pendingAction) {
			case "accepted":
				return "Accept Request";
			case "rejected":
				return "Reject Request";
			case "completed":
				return "Complete Request";
			default:
				return "";
		}
	};

	const getActionDescription = () => {
		switch (pendingAction) {
			case "accepted":
				return request.listing.settings_virtual_diagnosis
					? "Add a note about when you'll contact the customer for the free troubleshooting call."
					: "Add a note about how you'll proceed with the remote diagnosis.";
			case "rejected":
				return "Please provide a reason for rejecting this request.";
			case "completed":
				return "Optionally add any final notes about the troubleshooting session.";
			default:
				return "";
		}
	};

	return (
		<>
			<div className="bg-white rounded-lg shadow-md p-6">
				{/* Status Banner */}
				<div
					className={`mb-6 p-4 rounded-lg ${
						request.status === "pending"
							? "bg-yellow-50 border border-yellow-200"
							: request.status === "completed"
								? "bg-green-50 border border-green-200"
								: request.status === "rejected"
									? "bg-red-50 border border-red-200"
									: "bg-blue-50 border border-blue-200"
					}`}
				>
					<div className="flex items-center justify-between">
						<div>
							<p className="font-semibold capitalize">{request.status}</p>
							<p className="text-sm text-gray-600">
								Received{" "}
								{format(new Date(request.created_at), "MMM d, yyyy h:mm a")}
							</p>
						</div>
						{request.status === "pending" && (
							<div className="space-x-2">
								<Button
									variant="outline"
									onClick={() => handleActionClick("accepted")}
								>
									Accept
								</Button>
								<Button
									variant="outline"
									className="text-red-600 hover:text-red-700"
									onClick={() => handleActionClick("rejected")}
								>
									Reject
								</Button>
							</div>
						)}
						{request.status === "accepted" && (
							<Button
								variant="outline"
								onClick={() => handleActionClick("completed")}
							>
								Mark as Completed
							</Button>
						)}
					</div>
				</div>

				{/* Service Type */}
				<div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
					<h2 className="font-semibold text-blue-900">Service Type</h2>
					<p className="text-blue-800">
						{request.listing.settings_virtual_diagnosis
							? "Free Troubleshooting Call"
							: "Remote Diagnosis Assistance"}
					</p>
				</div>

				{/* Provider Information */}
				<div className="mb-6">
					<h2 className="text-lg font-semibold mb-3">Provider Information</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<p className="text-sm text-gray-600">Business Name</p>
							<p className="font-medium">{request.listing.business_name}</p>
						</div>
						{request.listing.phone && (
							<div>
								<p className="text-sm text-gray-600">Phone</p>
								<p className="font-medium">{request.listing.phone}</p>
							</div>
						)}
						{request.listing.email && (
							<div>
								<p className="text-sm text-gray-600">Email</p>
								<p className="font-medium">{request.listing.email}</p>
							</div>
						)}
					</div>
				</div>

				{/* Provider Response - show when request is accepted */}
				{request.status === "accepted" && request.provider_response && (
					<div className="mb-6 p-4 bg-emerald-50 border border-emerald-200 rounded-lg">
						<h2 className="font-semibold text-emerald-900 mb-2">
							Provider Response
						</h2>
						<p className="text-emerald-800 whitespace-pre-wrap">
							{request.provider_response}
						</p>
					</div>
				)}

				{/* Customer Information */}
				<div className="mb-6">
					<h2 className="text-lg font-semibold mb-3">Customer Information</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<p className="text-sm text-gray-600">Name</p>
							<p className="font-medium">
								{request.first_name} {request.last_name}
							</p>
						</div>
						<div>
							<p className="text-sm text-gray-600">Phone</p>
							<p className="font-medium">{request.phone}</p>
						</div>
						<div>
							<p className="text-sm text-gray-600">Email</p>
							<p className="font-medium">{request.email}</p>
						</div>
					</div>
				</div>

				{/* Issue Description */}
				<div className="mb-6">
					<h2 className="text-lg font-semibold mb-3">Issue Description</h2>
					<div className="bg-gray-50 p-4 rounded-lg">
						<p className="whitespace-pre-wrap">{request.issue_description}</p>
					</div>
				</div>

				{/* Provider Notes */}
				<div className="mb-6">
					<h2 className="text-lg font-semibold mb-3">Provider Notes</h2>
					{request.provider_notes &&
					(request.provider_notes as any[]).length > 0 ? (
						<div className="space-y-3">
							{(request.provider_notes as any[]).map((note, index) => (
								<div key={index} className="bg-gray-50 rounded-lg p-4">
									<div className="flex items-center justify-between mb-2">
										<div className="flex items-center gap-2">
											{note.type === "accepted" && (
												<span className="w-2 h-2 rounded-full bg-emerald-500" />
											)}
											{note.type === "rejected" && (
												<span className="w-2 h-2 rounded-full bg-red-500" />
											)}
											{note.type === "completed" && (
												<span className="w-2 h-2 rounded-full bg-blue-500" />
											)}
											<span className="font-medium capitalize">
												{note.type}
											</span>
										</div>
										<span className="text-sm text-gray-600">
											{format(new Date(note.created_at), "MMM d, yyyy h:mm a")}
										</span>
									</div>
									<p className="text-gray-700 whitespace-pre-wrap">
										{note.content}
									</p>
								</div>
							))}
						</div>
					) : (
						<p className="text-gray-500">No notes yet</p>
					)}
				</div>
			</div>

			{/* Action Modal */}
			<Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>{getActionTitle()}</DialogTitle>
					</DialogHeader>
					<div className="py-4">
						<p className="text-sm text-gray-600 mb-4">
							{getActionDescription()}
						</p>
						<Textarea
							value={notes}
							onChange={(e) => setNotes(e.target.value)}
							placeholder="Enter your notes here..."
							className="min-h-[100px]"
						/>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsModalOpen(false)}
							disabled={isUpdating}
						>
							Cancel
						</Button>
						<Button onClick={handleStatusUpdate} disabled={isUpdating}>
							{isUpdating ? "Updating..." : "Confirm"}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
