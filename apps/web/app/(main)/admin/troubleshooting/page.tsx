import { Suspense } from "react";
import { TroubleshootingRequestsTable } from "./troubleshooting-requests-table";

export const dynamic = "force-dynamic";

export default function AdminTroubleshootingPage() {
    return (
        <div className="container mx-auto py-10">
            <h1 className="text-2xl font-bold mb-6">Troubleshooting Requests</h1>
            <Suspense fallback={<div>Loading...</div>}>
                <TroubleshootingRequestsTable />
            </Suspense>
        </div>
    );
} 