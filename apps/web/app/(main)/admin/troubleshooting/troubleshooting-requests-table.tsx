"use client";

import Loader from "@/components/Loader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { Listing, TroubleshootingRequest, User } from "@rvhelp/database";
import { format } from "date-fns";
import { Eye } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import TroubleshootingRequestDetails from "./[requestId]/TroubleshootingRequestDetails";

interface RequestWithRelations extends TroubleshootingRequest {
	user: User;
	listing: Listing;
}

export function TroubleshootingRequestsTable() {
	const [requests, setRequests] = useState<RequestWithRelations[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [statusFilter, setStatusFilter] = useState<string>("all");
	const [selectedRequest, setSelectedRequest] =
		useState<RequestWithRelations | null>(null);

	useEffect(() => {
		const fetchRequests = async () => {
			try {
				const response = await fetch(
					`/api/troubleshooting-requests${statusFilter !== "all" ? `?status=${statusFilter}` : ""}`
				);
				if (!response.ok) throw new Error("Failed to fetch requests");
				const data = await response.json();
				setRequests(data);
			} catch (error) {
				console.error("Error fetching requests:", error);
				toast.error("Failed to load troubleshooting requests");
			} finally {
				setIsLoading(false);
			}
		};

		fetchRequests();
	}, [statusFilter]);

	return (
		<>
			<div className="space-y-4">
				<div className="flex justify-end">
					<Select value={statusFilter} onValueChange={setStatusFilter}>
						<SelectTrigger className="w-[180px]">
							<SelectValue placeholder="Filter by status" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Statuses</SelectItem>
							<SelectItem value="pending">Pending</SelectItem>
							<SelectItem value="accepted">Accepted</SelectItem>
							<SelectItem value="rejected">Rejected</SelectItem>
							<SelectItem value="completed">Completed</SelectItem>
						</SelectContent>
					</Select>
				</div>

				<div className="bg-white rounded-lg border">
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Customer</TableHead>
								<TableHead>Provider</TableHead>
								<TableHead>Contact</TableHead>
								<TableHead>Service Type</TableHead>
								<TableHead>Status</TableHead>
								<TableHead>Created</TableHead>
								<TableHead>Actions</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{isLoading ? (
								<TableRow>
									<TableCell colSpan={7} className="h-24">
										<Loader />
									</TableCell>
								</TableRow>
							) : requests.length === 0 ? (
								<TableRow>
									<TableCell colSpan={7} className="text-center py-8">
										No troubleshooting requests found
									</TableCell>
								</TableRow>
							) : (
								requests.map((request) => (
									<TableRow key={request.id} className="hover:bg-muted/50">
										<TableCell>
											{request.first_name} {request.last_name}
										</TableCell>
										<TableCell>
											<div className="font-medium">
												{request.listing.business_name}
											</div>
										</TableCell>
										<TableCell>
											<div className="space-y-1">
												<div>{request.email}</div>
												<div className="text-sm text-gray-500">
													{request.phone}
												</div>
											</div>
										</TableCell>
										<TableCell>
											{request.listing.settings_virtual_diagnosis
												? "Free Troubleshooting"
												: "Remote Diagnosis"}
										</TableCell>
										<TableCell>
											<Badge
												variant={
													request.status === "pending"
														? "secondary"
														: request.status === "completed"
															? "success"
															: request.status === "rejected"
																? "destructive"
																: "default"
												}
												className="capitalize"
											>
												{request.status}
											</Badge>
										</TableCell>
										<TableCell>
											{format(
												new Date(request.created_at),
												"MMM d, yyyy h:mm a"
											)}
										</TableCell>
										<TableCell>
											<Button
												variant="ghost"
												size="icon"
												onClick={() => setSelectedRequest(request)}
											>
												<Eye className="h-4 w-4" />
											</Button>
										</TableCell>
									</TableRow>
								))
							)}
						</TableBody>
					</Table>
				</div>
			</div>

			<Dialog
				open={!!selectedRequest}
				onOpenChange={() => setSelectedRequest(null)}
			>
				<DialogContent className="max-w-4xl">
					<DialogHeader>
						<DialogTitle>Troubleshooting Request Details</DialogTitle>
					</DialogHeader>
					{selectedRequest && (
						<TroubleshootingRequestDetails request={selectedRequest} />
					)}
				</DialogContent>
			</Dialog>
		</>
	);
}
