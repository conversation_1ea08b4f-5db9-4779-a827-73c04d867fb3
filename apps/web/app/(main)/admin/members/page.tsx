"use client";

import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	AlertCircle,
	Calendar,
	CheckCircle,
	Crown,
	UserCheck,
	Users
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Member {
	id: string;
	email: string;
	first_name: string;
	last_name: string;
	membership_level: string;
	member_number: number | null;
	created_at: string;
	hasExistingMembership: boolean;
}

interface MembershipStats {
	totalActiveMembers: number;
	newMemberships: number;
	totalCancellations: number;
}

export default function MembersPage() {
	const router = useRouter();
	const [members, setMembers] = useState<Member[]>([]);
	const [membershipStats, setMembershipStats] =
		useState<MembershipStats | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [statsLoading, setStatsLoading] = useState(false);

	useEffect(() => {
		fetchMembers();
		fetchMembershipStats();
	}, []);

	const fetchMembers = async () => {
		try {
			setLoading(true);
			const response = await fetch("/api/admin/migrate-members/preview");

			if (!response.ok) {
				throw new Error("Failed to fetch members");
			}

			const data = await response.json();
			setMembers(data.members);
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
		} finally {
			setLoading(false);
		}
	};

	const fetchMembershipStats = async () => {
		try {
			setStatsLoading(true);
			// Get stats for the last 30 days
			const endDate = new Date();
			const startDate = new Date();
			startDate.setDate(startDate.getDate() - 30);

			const response = await fetch(
				`/api/admin/membership-stats?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
			);

			if (response.ok) {
				const data = await response.json();
				setMembershipStats(data);
			}
		} catch (err) {
			console.error("Failed to fetch membership stats:", err);
		} finally {
			setStatsLoading(false);
		}
	};

	const activeMembers = members
		.filter((member) => member.hasExistingMembership)
		.sort((a, b) => {
			// Handle null values - put them at the end
			if (a.member_number === null && b.member_number === null) return 0;
			if (a.member_number === null) return 1;
			if (b.member_number === null) return -1;
			// Sort by member_number in descending order (high to low)
			return b.member_number - a.member_number;
		});
	const standardMembers = activeMembers.filter(
		(member) => member.membership_level === "STANDARD"
	);
	const premiumMembers = activeMembers.filter(
		(member) => member.membership_level === "PREMIUM"
	);

	if (loading) {
		return (
			<div className="p-6">
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
						<p>Loading members...</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="p-6 space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold">Members</h1>
					<p className="text-gray-600">Manage and view all RV Help members</p>
				</div>
				<Button onClick={() => router.back()} variant="outline">
					Back to Admin
				</Button>
			</div>

			{error && (
				<Alert className="border-red-200 bg-red-50">
					<AlertCircle className="h-4 w-4 text-red-600" />
					<AlertDescription className="text-red-800">{error}</AlertDescription>
				</Alert>
			)}

			{/* Member Stats Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Total Active Members
						</CardTitle>
						<Users className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{activeMembers.length}</div>
						<p className="text-xs text-muted-foreground">
							Active paid memberships
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Standard Members
						</CardTitle>
						<UserCheck className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-blue-600">
							{standardMembers.length}
						</div>
						<p className="text-xs text-muted-foreground">Standard tier</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Premium Members
						</CardTitle>
						<Crown className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-purple-600">
							{premiumMembers.length}
						</div>
						<p className="text-xs text-muted-foreground">Premium tier</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							New Members (30d)
						</CardTitle>
						<Calendar className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">
							{statsLoading ? "..." : membershipStats?.newMemberships || 0}
						</div>
						<p className="text-xs text-muted-foreground">Last 30 days</p>
					</CardContent>
				</Card>
			</div>

			{/* Action Buttons */}
			<Card>
				<CardHeader>
					<CardTitle>Member Actions</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex gap-4">
						<Button onClick={fetchMembers} variant="outline">
							Refresh Data
						</Button>
						<Button
							onClick={fetchMembershipStats}
							variant="outline"
							disabled={statsLoading}
						>
							{statsLoading ? "Loading..." : "Refresh Stats"}
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Active Members Table */}
			<Card>
				<CardHeader>
					<CardTitle>Active Members</CardTitle>
					<p className="text-sm text-gray-600">
						Current paid members with active subscriptions
					</p>
				</CardHeader>
				<CardContent>
					<div className="overflow-x-auto">
						<table className="w-full border-collapse border border-gray-200">
							<thead>
								<tr className="bg-gray-50">
									<th className="border border-gray-200 px-4 py-2 text-left">
										Member #
									</th>
									<th className="border border-gray-200 px-4 py-2 text-left">
										Name
									</th>
									<th className="border border-gray-200 px-4 py-2 text-left">
										Email
									</th>
									<th className="border border-gray-200 px-4 py-2 text-left">
										Level
									</th>
									<th className="border border-gray-200 px-4 py-2 text-left">
										Status
									</th>
									<th className="border border-gray-200 px-4 py-2 text-left">
										Joined
									</th>
								</tr>
							</thead>
							<tbody>
								{activeMembers.map((member) => (
									<tr key={member.id}>
										<td className="border border-gray-200 px-4 py-2 font-mono">
											#{member.member_number || "N/A"}
										</td>
										<td className="border border-gray-200 px-4 py-2">
											{member.first_name} {member.last_name}
										</td>
										<td className="border border-gray-200 px-4 py-2">
											{member.email}
										</td>
										<td className="border border-gray-200 px-4 py-2">
											<Badge
												variant={
													member.membership_level === "PREMIUM"
														? "default"
														: "secondary"
												}
											>
												{member.membership_level === "PREMIUM" && (
													<Crown className="h-3 w-3 mr-1" />
												)}
												{member.membership_level}
											</Badge>
										</td>
										<td className="border border-gray-200 px-4 py-2">
											{member.hasExistingMembership ? (
												<Badge variant="outline" className="text-green-600">
													<CheckCircle className="h-3 w-3 mr-1" />
													Active
												</Badge>
											) : (
												<Badge variant="outline" className="text-orange-600">
													Needs Migration
												</Badge>
											)}
										</td>
										<td className="border border-gray-200 px-4 py-2">
											{new Date(member.created_at).toLocaleDateString()}
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</CardContent>
			</Card>

			{activeMembers.length === 0 && !loading && (
				<Card>
					<CardContent className="text-center py-8">
						<Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium mb-2">No Active Members</h3>
						<p className="text-gray-600">
							No active paid members found in the system.
						</p>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
