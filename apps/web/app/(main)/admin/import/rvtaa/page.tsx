"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import {
	AlertTriangle,
	CheckCircle,
	Clock,
	ExternalLink,
	Loader2,
	Mail,
	Play,
	XCircle
} from "lucide-react";
import { useEffect, useState } from "react";

interface Listing {
	id: string;
	email: string;
	first_name: string;
	last_name: string;
	business_name: string;
	slug: string;
	created_at: string;
	invite_sent_at: string | null;
	rvtaa_member_id: string | null;
	rvtaa_technician_level: number | null;
	nrvia_inspector_id: string | null;
	nrvia_inspector_level: number | null;
}

interface ListingWithEligibility extends Listing {
	eligibility: {
		isEligible: boolean;
		hasRVTAA: boolean;
		hasNRVIA: boolean;
		hasInviteSent: boolean;
		rvtaaLevel: number;
		nrviaLevel: number;
	};
	status: "pending" | "processing" | "invited" | "ineligible" | "error";
}

type FilterType = "all" | "eligible" | "invited" | "ineligible" | "error";

export default function ImportRVTAAAdminPage() {
	const [listings, setListings] = useState<ListingWithEligibility[]>([]);
	const [filteredListings, setFilteredListings] = useState<
		ListingWithEligibility[]
	>([]);
	const [activeFilter, setActiveFilter] = useState<FilterType>("all");
	const [isLoading, setIsLoading] = useState(true);
	const [isProcessing, setIsProcessing] = useState(false);

	const [stats, setStats] = useState({
		total: 0,
		eligible: 0,
		invited: 0,
		ineligible: 0,
		errors: 0
	});

	useEffect(() => {
		loadListings();
	}, []);

	useEffect(() => {
		filterListings();
	}, [listings, activeFilter]);

	const loadListings = async () => {
		try {
			const response = await fetch("/api/admin/import/rvtaa/new/listings");
			const data = await response.json();

			const listingsWithEligibility = data.listings.map((listing: Listing) => ({
				...listing,
				eligibility: checkEligibility(listing),
				status: listing.invite_sent_at ? "invited" : "pending"
			}));

			setListings(listingsWithEligibility);
			updateStats(listingsWithEligibility);
		} catch (error) {
			console.error("Error loading listings:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const checkEligibility = (listing: Listing) => {
		const hasRVTAA =
			listing.rvtaa_member_id && listing.rvtaa_technician_level > 0;
		const hasNRVIA =
			listing.nrvia_inspector_id && listing.nrvia_inspector_level > 0;
		const hasInviteSent = listing.invite_sent_at !== null;

		return {
			isEligible: (hasRVTAA || hasNRVIA) && !hasInviteSent,
			hasRVTAA,
			hasNRVIA,
			hasInviteSent,
			rvtaaLevel: listing.rvtaa_technician_level || 0,
			nrviaLevel: listing.nrvia_inspector_level || 0
		};
	};

	const filterListings = () => {
		let filtered: ListingWithEligibility[] = [];

		switch (activeFilter) {
			case "all":
				filtered = listings;
				break;
			case "eligible":
				filtered = listings.filter((l) => l.eligibility.isEligible);
				break;
			case "invited":
				filtered = listings.filter((l) => l.status === "invited");
				break;
			case "ineligible":
				filtered = listings.filter(
					(l) => !l.eligibility.isEligible && l.status !== "invited"
				);
				break;
			case "error":
				filtered = listings.filter((l) => l.status === "error");
				break;
		}

		setFilteredListings(filtered);
	};

	const updateStats = (listings: ListingWithEligibility[]) => {
		const stats = {
			total: listings.length,
			eligible: listings.filter((l) => l.eligibility.isEligible).length,
			invited: listings.filter((l) => l.status === "invited").length,
			ineligible: listings.filter(
				(l) => !l.eligibility.isEligible && l.status !== "invited"
			).length,
			errors: listings.filter((l) => l.status === "error").length
		};
		setStats(stats);
	};

	const runRVSGImport = async () => {
		setIsProcessing(true);

		try {
			// Mark eligible listings as processing
			setListings((prev) =>
				prev.map((listing) => ({
					...listing,
					status: listing.eligibility.isEligible ? "processing" : listing.status
				}))
			);

			const response = await fetch("/api/admin/import/rvtaa/new/run", {
				method: "POST"
			});

			if (!response.ok) {
				throw new Error("Failed to queue RVSG import jobs");
			}

			const result = await response.json();

			// Show success message
			console.log("Successfully queued RVSG import jobs:", result);

			// Reload listings to get fresh data after a short delay
			setTimeout(() => {
				loadListings();
			}, 2000);
		} catch (error) {
			console.error("Error queuing RVSG import jobs:", error);
			// Mark processing listings as error
			setListings((prev) =>
				prev.map((listing) => ({
					...listing,
					status: listing.status === "processing" ? "error" : listing.status
				}))
			);
		} finally {
			setIsProcessing(false);
		}
	};

	const runSingleRVSGImport = async (listingId: string) => {
		try {
			// Mark this specific listing as processing
			setListings((prev) =>
				prev.map((listing) => ({
					...listing,
					status: listing.id === listingId ? "processing" : listing.status
				}))
			);

			const response = await fetch("/api/admin/import/rvtaa/new/run-single", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({ listingId })
			});

			if (!response.ok) {
				throw new Error("Failed to queue single RVSG import job");
			}

			const result = await response.json();
			console.log("Successfully queued single RVSG import job:", result);

			// Reload listings to get fresh data after a short delay
			setTimeout(() => {
				loadListings();
			}, 2000);
		} catch (error) {
			console.error("Error queuing single RVSG import job:", error);
			// Mark this listing as error
			setListings((prev) =>
				prev.map((listing) => ({
					...listing,
					status: listing.id === listingId ? "error" : listing.status
				}))
			);
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "invited":
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case "processing":
				return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
			case "ineligible":
				return <XCircle className="h-4 w-4 text-gray-500" />;
			case "error":
				return <AlertTriangle className="h-4 w-4 text-red-500" />;
			default:
				return <Clock className="h-4 w-4 text-yellow-500" />;
		}
	};

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "invited":
				return (
					<Badge variant="default" className="bg-green-100 text-green-800">
						Invited
					</Badge>
				);
			case "processing":
				return (
					<Badge variant="default" className="bg-blue-100 text-blue-800">
						Processing
					</Badge>
				);
			case "ineligible":
				return <Badge variant="secondary">Ineligible</Badge>;
			case "error":
				return <Badge variant="destructive">Error</Badge>;
			default:
				return <Badge variant="outline">Pending</Badge>;
		}
	};

	const getFilterButtonVariant = (filter: FilterType) => {
		return activeFilter === filter ? "default" : "outline";
	};

	if (isLoading) {
		return (
			<div className="container mx-auto p-6">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="h-8 w-8 animate-spin" />
					<span className="ml-2">Loading listings...</span>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto p-6">
			<div className="mb-6">
				<h1 className="text-3xl font-bold mb-2">New Listings</h1>
				<p className="text-gray-600">
					Import and invite new listings from the RVTAA and NRVIA.
				</p>
			</div>

			{/* Stats Cards */}
			<div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
				<Card>
					<CardContent className="p-4">
						<div className="text-2xl font-bold">{stats.total}</div>
						<div className="text-sm text-gray-600">Total Listings</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="p-4">
						<div className="text-2xl font-bold text-green-600">
							{stats.eligible}
						</div>
						<div className="text-sm text-gray-600">Eligible</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="p-4">
						<div className="text-2xl font-bold text-blue-600">
							{stats.invited}
						</div>
						<div className="text-sm text-gray-600">Invited</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="p-4">
						<div className="text-2xl font-bold text-gray-600">
							{stats.ineligible}
						</div>
						<div className="text-sm text-gray-600">Ineligible</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="p-4">
						<div className="text-2xl font-bold text-red-600">
							{stats.errors}
						</div>
						<div className="text-sm text-gray-600">Errors</div>
					</CardContent>
				</Card>
			</div>

			{/* Filter Buttons */}
			<div className="mb-6">
				<div className="flex flex-wrap gap-2">
					<Button
						variant={getFilterButtonVariant("all")}
						onClick={() => setActiveFilter("all")}
						size="sm"
					>
						All ({stats.total})
					</Button>
					<Button
						variant={getFilterButtonVariant("eligible")}
						onClick={() => setActiveFilter("eligible")}
						size="sm"
						className="text-green-700 border-green-200 hover:bg-green-50"
					>
						Eligible ({stats.eligible})
					</Button>
					<Button
						variant={getFilterButtonVariant("invited")}
						onClick={() => setActiveFilter("invited")}
						size="sm"
						className="text-blue-700 border-blue-200 hover:bg-blue-50"
					>
						Invited ({stats.invited})
					</Button>
					<Button
						variant={getFilterButtonVariant("ineligible")}
						onClick={() => setActiveFilter("ineligible")}
						size="sm"
						className="text-gray-700 border-gray-200 hover:bg-gray-50"
					>
						Ineligible ({stats.ineligible})
					</Button>
					<Button
						variant={getFilterButtonVariant("error")}
						onClick={() => setActiveFilter("error")}
						size="sm"
						className="text-red-700 border-red-200 hover:bg-red-50"
					>
						Errors ({stats.errors})
					</Button>
				</div>
			</div>

			{/* Action Buttons */}
			<div className="mb-6 flex gap-4">
				<Button
					onClick={runRVSGImport}
					disabled={isProcessing || stats.eligible === 0}
					className="flex items-center gap-2"
				>
					{isProcessing ? (
						<Loader2 className="h-4 w-4 animate-spin" />
					) : (
						<Mail className="h-4 w-4" />
					)}
					{isProcessing
						? "Queuing Jobs..."
						: stats.eligible === 0
							? "No Eligible Listings"
							: `Queue RVSG Import (${stats.eligible} eligible)`}
				</Button>
			</div>

			{/* Listings */}
			<Card>
				<CardHeader>
					<CardTitle>
						Recent Listings
						{activeFilter !== "all" && (
							<span className="text-sm font-normal text-gray-500 ml-2">
								({filteredListings.length} {activeFilter})
							</span>
						)}
					</CardTitle>
					<CardDescription>
						Listings created in the last week that may be eligible for RVSG
						import and provider invites
					</CardDescription>
				</CardHeader>
				<CardContent>
					{filteredListings.length === 0 ? (
						<Alert>
							<AlertDescription>
								{activeFilter === "all"
									? "No listings found for the last week."
									: `No ${activeFilter} listings found.`}
							</AlertDescription>
						</Alert>
					) : (
						<div className="space-y-4">
							{filteredListings.map((listing) => (
								<div
									key={listing.id}
									className="flex items-center justify-between p-4 border rounded-lg"
								>
									<div className="flex items-center gap-3">
										{getStatusIcon(listing.status)}
										<div>
											<div className="font-medium">
												{listing.first_name} {listing.last_name}
											</div>
											<div className="text-sm text-gray-600">
												{listing.business_name} • {listing.email}
											</div>
											<div className="text-xs text-gray-500">
												Created:{" "}
												{new Date(listing.created_at).toLocaleDateString()}
											</div>
										</div>
									</div>

									<div className="flex items-center gap-2">
										{listing.eligibility.hasRVTAA && (
											<Badge variant="outline" className="text-xs">
												RVTAA Level {listing.eligibility.rvtaaLevel}
											</Badge>
										)}
										{listing.eligibility.hasNRVIA && (
											<Badge variant="outline" className="text-xs">
												NRVIA Level {listing.eligibility.nrviaLevel}
											</Badge>
										)}
										{getStatusBadge(listing.status)}

										{/* Individual Process Button */}
										{listing.eligibility.isEligible &&
											listing.status !== "invited" && (
												<Button
													size="sm"
													variant="outline"
													onClick={() => runSingleRVSGImport(listing.id)}
													disabled={listing.status === "processing"}
													className="h-7 px-2 text-xs"
												>
													{listing.status === "processing" ? (
														<Loader2 className="h-3 w-3 animate-spin" />
													) : (
														<Play className="h-3 w-3" />
													)}
													{listing.status === "processing"
														? "Processing..."
														: "Process"}
												</Button>
											)}

										<a
											href={`/provider/business/profile?id=${listing.id}`}
											target="_blank"
											rel="noopener noreferrer"
											className="p-1 hover:bg-gray-100 rounded transition-colors"
											title="View listing"
										>
											<ExternalLink className="h-4 w-4 text-gray-500 hover:text-gray-700" />
										</a>
									</div>
								</div>
							))}
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
