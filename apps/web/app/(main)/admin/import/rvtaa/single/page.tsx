"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { url } from "@/lib/url";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

const formSchema = z.object({
	userId: z.string().min(1, "Please enter a valid user ID")
});

type FormData = z.infer<typeof formSchema>;

export default function SingleRVTAImport() {
	const router = useRouter();
	const form = useForm<FormData>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			userId: ""
		}
	});

	const onSubmit = async (data: FormData) => {
		try {
			const response = await fetch("/api/admin/import/rvtaa/single", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(data)
			});

			if (!response.ok) {
				throw new Error("Import failed");
			}

			const result = await response.json();

			if (!result.listing) {
				throw new Error("Failed to import member");
			}
			toast.success("Import completed successfully");
			router.push(url("edit-listing", { id: result.listing?.id }));
			form.reset();
		} catch (error) {
			console.error("Import error:", error);
			toast.error("Failed to import RVTA data. " + error.message);
		}
	};

	return (
		<div className="container mx-auto py-6">
			<h1 className="text-2xl font-bold mb-6">Single RVTA Import</h1>

			<Card>
				<CardHeader>
					<CardTitle>Import RVTA Member</CardTitle>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
							<FormField
								control={form.control}
								name="userId"
								// no prefill

								render={({ field }) => (
									<FormItem>
										<FormLabel>User ID</FormLabel>
										<FormControl>
											<Input
												autoComplete="off"
												type="text"
												placeholder="Enter RVTA Member ID"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<Button type="submit" disabled={form.formState.isSubmitting}>
								{form.formState.isSubmitting ? "Importing..." : "Import Member"}
							</Button>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
}
