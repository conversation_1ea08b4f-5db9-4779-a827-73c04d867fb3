"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	<PERSON><PERSON>Header,
	DialogTitle
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AlertTriangle, CheckCircle, Shield, UserX } from "lucide-react";
import { useState } from "react";

interface FlaggedJob {
	id: string;
	first_name: string;
	last_name: string;
	email: string;
	message: string;
	created_at: string;
	user: {
		id: string;
		first_name: string;
		last_name: string;
		email: string;
		phone: string;
		created_at: string;
	};
	quotes: Array<{
		listing: {
			id: string;
			business_name: string;
			first_name: string;
			last_name: string;
			email: string;
		};
	}>;
}

interface FraudManagementDialogProps {
	isOpen: boolean;
	onClose: () => void;
	job: FlaggedJob;
	onAction: (action: string, data: any) => Promise<void>;
}

export function FraudManagementDialog({
	isOpen,
	onClose,
	job,
	onAction
}: FraudManagementDialogProps) {
	const [action, setAction] = useState<"unflag" | "ban">("unflag");
	const [reason, setReason] = useState("");
	const [sendNotifications, setSendNotifications] = useState(true);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const handleSubmit = async () => {
		if (!reason.trim()) {
			return;
		}

		setIsSubmitting(true);
		try {
			if (action === "unflag") {
				await onAction("unflag_job", { jobId: job.id });
			} else {
				await onAction("ban_user", {
					userId: job.user.id,
					reason,
					sendNotifications
				});
			}
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		setAction("unflag");
		setReason("");
		setSendNotifications(true);
		onClose();
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleClose}>
			<DialogContent className="max-w-2xl">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<AlertTriangle className="h-5 w-5 text-orange-600" />
						Fraud Management
					</DialogTitle>
					<DialogDescription>
						Manage this flagged job and take appropriate action
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-6">
					{/* Job Details */}
					<div className="bg-gray-50 p-4 rounded-lg">
						<h3 className="font-semibold mb-3">Job Details</h3>
						<div className="grid grid-cols-2 gap-4 text-sm">
							<div>
								<span className="font-medium">Customer:</span>
								<div>
									{job.first_name} {job.last_name}
								</div>
								<div className="text-gray-600">{job.email}</div>
							</div>
							<div>
								<span className="font-medium">Submitted:</span>
								<div>{new Date(job.created_at).toLocaleString()}</div>
							</div>
							<div className="col-span-2">
								<span className="font-medium">Message:</span>
								<div className="mt-1 p-2 bg-white rounded border text-gray-700">
									{job.message}
								</div>
							</div>
						</div>
					</div>

					{/* User Details */}
					<div className="bg-blue-50 p-4 rounded-lg">
						<h3 className="font-semibold mb-3 flex items-center gap-2">
							<Shield className="h-4 w-4" />
							User Information
						</h3>
						<div className="grid grid-cols-2 gap-4 text-sm">
							<div>
								<span className="font-medium">User ID:</span>
								<div className="font-mono text-xs">{job.user.id}</div>
							</div>
							<div>
								<span className="font-medium">Member Since:</span>
								<div>{new Date(job.user.created_at).toLocaleDateString()}</div>
							</div>
							<div>
								<span className="font-medium">Phone:</span>
								<div>{job.user.phone || "Not provided"}</div>
							</div>
							<div>
								<span className="font-medium">Providers Contacted:</span>
								<Badge variant="outline">{job.quotes.length}</Badge>
							</div>
						</div>
					</div>

					{/* Affected Providers */}
					{job.quotes.length > 0 && (
						<div className="bg-yellow-50 p-4 rounded-lg">
							<h3 className="font-semibold mb-3">Affected Providers</h3>
							<div className="space-y-2 max-h-32 overflow-y-auto">
								{job.quotes.map((quote, index) => (
									<div key={index} className="text-sm">
										<div className="font-medium">
											{quote.listing.business_name ||
												`${quote.listing.first_name} ${quote.listing.last_name}`}
										</div>
										<div className="text-gray-600">{quote.listing.email}</div>
									</div>
								))}
							</div>
						</div>
					)}

					{/* Action Selection */}
					<div className="space-y-4">
						<div>
							<Label className="text-base font-medium">Select Action</Label>
							<div className="mt-2 space-y-2">
								<div className="flex items-center space-x-2">
									<input
										type="radio"
										id="unflag"
										name="action"
										value="unflag"
										checked={action === "unflag"}
										onChange={(e) =>
											setAction(e.target.value as "unflag" | "ban")
										}
										className="text-green-600"
									/>
									<label htmlFor="unflag" className="flex items-center gap-2">
										<CheckCircle className="h-4 w-4 text-green-600" />
										<span>Unflag Job (Mark as legitimate)</span>
									</label>
								</div>
								<div className="flex items-center space-x-2">
									<input
										type="radio"
										id="ban"
										name="action"
										value="ban"
										checked={action === "ban"}
										onChange={(e) =>
											setAction(e.target.value as "unflag" | "ban")
										}
										className="text-red-600"
									/>
									<label htmlFor="ban" className="flex items-center gap-2">
										<UserX className="h-4 w-4 text-red-600" />
										<span>Ban User (Block all future activity)</span>
									</label>
								</div>
							</div>
						</div>

						{/* Reason */}
						<div>
							<Label htmlFor="reason" className="text-base font-medium">
								Reason for {action === "unflag" ? "unflagging" : "banning"}
							</Label>
							<Textarea
								id="reason"
								value={reason}
								onChange={(e) => setReason(e.target.value)}
								placeholder={`Explain why you are ${action === "unflag" ? "unflagging this job" : "banning this user"}...`}
								className="mt-2"
								rows={3}
							/>
						</div>

						{/* Provider Notification (only for ban action) */}
						{action === "ban" && (
							<div className="flex items-center space-x-2">
								<Checkbox
									id="notifications"
									checked={sendNotifications}
									onCheckedChange={(checked) =>
										setSendNotifications(checked as boolean)
									}
								/>
								<Label htmlFor="notifications" className="text-sm">
									Send notification emails to affected providers
								</Label>
							</div>
						)}
					</div>
				</div>

				<DialogFooter>
					<Button
						variant="outline"
						onClick={handleClose}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						disabled={!reason.trim() || isSubmitting}
						variant={action === "ban" ? "destructive" : "default"}
					>
						{isSubmitting
							? "Processing..."
							: action === "unflag"
								? "Unflag Job"
								: "Ban User"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
