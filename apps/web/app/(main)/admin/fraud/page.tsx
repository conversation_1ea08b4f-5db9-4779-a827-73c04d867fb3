"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Pagination } from "@/components/ui/pagination";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { AlertTriangle, FileText, Shield, Users } from "lucide-react";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { FraudManagementDialog } from "./fraud-management-dialog";

interface FraudStats {
	flaggedJobsToday: number;
	totalFlaggedJobs: number;
	suspiciousUsersToday: number;
}

interface FlaggedJob {
	id: string;
	first_name: string;
	last_name: string;
	email: string;
	message: string;
	created_at: string;
	user: {
		id: string;
		first_name: string;
		last_name: string;
		email: string;
		phone: string;
		created_at: string;
	};
	quotes: Array<{
		listing: {
			id: string;
			business_name: string;
			first_name: string;
			last_name: string;
			email: string;
		};
	}>;
}

export default function AdminFraudManagement() {
	const [stats, setStats] = useState<FraudStats | null>(null);
	const [flaggedJobs, setFlaggedJobs] = useState<FlaggedJob[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [search, setSearch] = useState("");
	const [selectedJob, setSelectedJob] = useState<FlaggedJob | null>(null);
	const [isDialogOpen, setIsDialogOpen] = useState(false);

	const fetchStats = async () => {
		try {
			const response = await fetch("/api/admin/fraud");
			if (response.ok) {
				const data = await response.json();
				setStats(data);
			}
		} catch (error) {
			console.error("Error fetching fraud stats:", error);
		}
	};

	const fetchFlaggedJobs = async () => {
		setIsLoading(true);
		try {
			const params = new URLSearchParams({
				page: currentPage.toString(),
				per_page: "10",
				...(search && { search })
			});

			const response = await fetch(`/api/admin/fraud/jobs?${params}`);
			if (response.ok) {
				const data = await response.json();
				setFlaggedJobs(data.jobs);
				setTotalPages(data.totalPages);
			}
		} catch (error) {
			console.error("Error fetching flagged jobs:", error);
			toast.error("Failed to fetch flagged jobs");
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchStats();
	}, []);

	useEffect(() => {
		fetchFlaggedJobs();
	}, [currentPage, search]);

	const handleFraudAction = async (action: string, data: any) => {
		try {
			const response = await fetch("/api/admin/fraud", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ action, ...data })
			});

			if (response.ok) {
				const result = await response.json();
				toast.success(result.message);
				fetchStats();
				fetchFlaggedJobs();
				setIsDialogOpen(false);
				setSelectedJob(null);
			} else {
				const error = await response.json();
				toast.error(error.error || "Failed to perform action");
			}
		} catch (error) {
			console.error("Error performing fraud action:", error);
			toast.error("Failed to perform action");
		}
	};

	return (
		<div className="container mx-auto px-4 py-8">
			<div className="flex items-center justify-between mb-8">
				<h1 className="text-2xl font-bold">Fraud Management</h1>
			</div>

			{/* Stats Cards */}
			{stats && (
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Flagged Jobs Today
							</CardTitle>
							<AlertTriangle className="h-4 w-4 text-orange-600" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{stats.flaggedJobsToday}</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Total Flagged Jobs
							</CardTitle>
							<Shield className="h-4 w-4 text-red-600" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{stats.totalFlaggedJobs}</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Suspicious Users Today
							</CardTitle>
							<Users className="h-4 w-4 text-yellow-600" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats.suspiciousUsersToday}
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Flagged Jobs Table */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<FileText className="h-5 w-5" />
						Flagged Jobs
					</CardTitle>
					<div className="flex gap-4">
						<Input
							placeholder="Search by name, email, or message..."
							value={search}
							onChange={(e) => setSearch(e.target.value)}
							className="max-w-sm"
						/>
					</div>
				</CardHeader>
				<CardContent>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Customer</TableHead>
								<TableHead>Email</TableHead>
								<TableHead>Message Preview</TableHead>
								<TableHead>Providers Contacted</TableHead>
								<TableHead>Date</TableHead>
								<TableHead>Actions</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{isLoading ? (
								<TableRow>
									<TableCell colSpan={6} className="text-center py-8">
										Loading...
									</TableCell>
								</TableRow>
							) : flaggedJobs.length === 0 ? (
								<TableRow>
									<TableCell colSpan={6} className="text-center py-8">
										No flagged jobs found
									</TableCell>
								</TableRow>
							) : (
								flaggedJobs.map((job) => (
									<TableRow key={job.id}>
										<TableCell>
											<div>
												<div className="font-medium">
													{job.first_name} {job.last_name}
												</div>
												<div className="text-sm text-gray-500">
													User since{" "}
													{new Date(job.user.created_at).toLocaleDateString()}
												</div>
											</div>
										</TableCell>
										<TableCell>{job.email}</TableCell>
										<TableCell>
											<div className="max-w-xs truncate" title={job.message}>
												{job.message}
											</div>
										</TableCell>
										<TableCell>
											<Badge variant="outline">
												{job.quotes.length} providers
											</Badge>
										</TableCell>
										<TableCell>
											{new Date(job.created_at).toLocaleDateString()}
										</TableCell>
										<TableCell>
											<div className="flex gap-2">
												<Button
													variant="outline"
													size="sm"
													onClick={() => {
														setSelectedJob(job);
														setIsDialogOpen(true);
													}}
												>
													Manage
												</Button>
											</div>
										</TableCell>
									</TableRow>
								))
							)}
						</TableBody>
					</Table>

					{totalPages > 1 && (
						<div className="mt-4">
							<Pagination
								currentPage={currentPage}
								totalPages={totalPages}
								onPageChange={setCurrentPage}
							/>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Fraud Management Dialog */}
			{selectedJob && (
				<FraudManagementDialog
					isOpen={isDialogOpen}
					onClose={() => {
						setIsDialogOpen(false);
						setSelectedJob(null);
					}}
					job={selectedJob}
					onAction={handleFraudAction}
				/>
			)}
		</div>
	);
}
