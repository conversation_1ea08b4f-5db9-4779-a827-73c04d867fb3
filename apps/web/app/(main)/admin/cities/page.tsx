"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    <PERSON><PERSON>,
    DialogContent,
    <PERSON><PERSON>Footer,
    <PERSON><PERSON>Header,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Edit, RefreshCw, Save, Search, Upload } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface CategoryContent {
    title?: string;
    content?: string;
}

interface CityContent {
    id: string;
    city: string;
    state: string;
    category_content: {
        'rv-repair'?: CategoryContent;
        'rv-inspection'?: CategoryContent;
    };
    created_at: string;
    updated_at: string;
}

interface EditModalData {
    cityId: string;
    city: string;
    state: string;
    category: 'rv-repair' | 'rv-inspection';
    title: string;
    content: string;
}

export default function CitiesPage() {
    const [cities, setCities] = useState<CityContent[]>([]);
    const [filteredCities, setFilteredCities] = useState<CityContent[]>([]);
    const [searchQuery, setSearchQuery] = useState("");
    const [loading, setLoading] = useState(true);
    const [uploading, setUploading] = useState(false);
    const [syncingReviews, setSyncingReviews] = useState(false);

    // Modal state
    const [editModalOpen, setEditModalOpen] = useState(false);
    const [editModalData, setEditModalData] = useState<EditModalData | null>(null);
    const [saving, setSaving] = useState(false);

    useEffect(() => {
        fetchCities();
    }, []);

    useEffect(() => {
        // Filter cities based on search query
        if (!searchQuery.trim()) {
            setFilteredCities(cities);
        } else {
            const query = searchQuery.toLowerCase();
            const filtered = cities.filter(city => {
                const cityMatch = city.city.toLowerCase().includes(query);
                const stateMatch = city.state.toLowerCase().includes(query);

                // Check category content
                const rvRepairTitle = city.category_content?.['rv-repair']?.title?.toLowerCase() || '';
                const rvRepairContent = city.category_content?.['rv-repair']?.content?.toLowerCase() || '';
                const rvInspectionTitle = city.category_content?.['rv-inspection']?.title?.toLowerCase() || '';
                const rvInspectionContent = city.category_content?.['rv-inspection']?.content?.toLowerCase() || '';

                const contentMatch = rvRepairTitle.includes(query) ||
                    rvRepairContent.includes(query) ||
                    rvInspectionTitle.includes(query) ||
                    rvInspectionContent.includes(query);

                return cityMatch || stateMatch || contentMatch;
            });
            setFilteredCities(filtered);
        }
    }, [searchQuery, cities]);

    const fetchCities = async () => {
        try {
            const response = await fetch("/api/admin/city-content");
            if (!response.ok) throw new Error("Failed to fetch cities");
            const data = await response.json();
            setCities(data);
            setFilteredCities(data);
        } catch (error) {
            toast.error("Failed to load cities");
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const openEditModal = (city: CityContent, category: 'rv-repair' | 'rv-inspection') => {
        const categoryData = city.category_content?.[category] || {};
        setEditModalData({
            cityId: city.id,
            city: city.city,
            state: city.state,
            category,
            title: categoryData.title || '',
            content: categoryData.content || ''
        });
        setEditModalOpen(true);
    };

    const handleSaveModal = async () => {
        if (!editModalData) return;

        setSaving(true);
        try {
            // Update the category content for this specific city
            const response = await fetch(`/api/admin/city-content/${editModalData.cityId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    category: editModalData.category,
                    title: editModalData.title,
                    content: editModalData.content
                })
            });

            if (!response.ok) throw new Error('Failed to save content');

            toast.success(`${editModalData.category} content saved for ${editModalData.city}`);
            setEditModalOpen(false);
            setEditModalData(null);
            fetchCities(); // Refresh data
        } catch (error) {
            toast.error('Failed to save content');
            console.error(error);
        } finally {
            setSaving(false);
        }
    };

    const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        const formData = new FormData();
        formData.append("file", file);

        setUploading(true);
        try {
            const response = await fetch("/api/admin/city-content/upload", {
                method: "POST",
                body: formData
            });

            if (!response.ok) throw new Error("Failed to upload CSV");

            const result = await response.json();
            toast.success(`Uploaded ${result.results.length} cities`);
            fetchCities(); // Refresh the list
        } catch (error) {
            toast.error("Failed to upload CSV");
            console.error(error);
        } finally {
            setUploading(false);
            if (e.target) e.target.value = "";
        }
    };

    const handleSyncReviews = async () => {
        setSyncingReviews(true);
        try {
            const response = await fetch("/api/admin/city-content/sync-reviews", {
                method: "POST"
            });

            if (!response.ok) throw new Error("Failed to sync reviews");

            const result = await response.json();
            toast.success("Reviews synced successfully");

            // Show detailed results
            const successCount = result.results.filter(r => r.message === 'Success').length;
            const totalReviews = result.results.reduce((sum, r) => sum + (r.reviewsUpdated || 0), 0);

            if (successCount > 0) {
                toast.success(`Updated ${successCount} cities with ${totalReviews} total reviews`);
            }

            fetchCities(); // Refresh the list
        } catch (error) {
            toast.error("Failed to sync reviews");
            console.error(error);
        } finally {
            setSyncingReviews(false);
        }
    };

    const getCategoryBadgeVariant = (city: CityContent, category: 'rv-repair' | 'rv-inspection') => {
        const hasContent = city.category_content?.[category]?.title || city.category_content?.[category]?.content;
        return hasContent ? "default" : "secondary";
    };

    if (loading) {
        return (
            <div className="container mx-auto p-6">
                <div className="animate-pulse space-y-4">
                    <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-64 bg-gray-200 rounded"></div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto p-6">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-2xl font-bold">City Content Management</h1>
                    <p className="text-muted-foreground">Manage category-specific content for city directory pages</p>
                </div>
                <div className="flex gap-4">
                    <div>
                        <Input
                            type="file"
                            accept=".csv"
                            onChange={handleFileUpload}
                            disabled={uploading}
                            className="hidden"
                            id="csv-upload"
                        />
                        <Label htmlFor="csv-upload">
                            <Button asChild variant="outline" disabled={uploading}>
                                <span>
                                    <Upload className="w-4 h-4 mr-2" />
                                    {uploading ? "Uploading..." : "Upload CSV"}
                                </span>
                            </Button>
                        </Label>
                    </div>
                    <Button
                        onClick={handleSyncReviews}
                        variant="outline"
                        disabled={syncingReviews}
                    >
                        <RefreshCw className={`w-4 h-4 mr-2 ${syncingReviews ? 'animate-spin' : ''}`} />
                        {syncingReviews ? "Syncing..." : "Sync Reviews"}
                    </Button>
                </div>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Cities</CardTitle>
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                            placeholder="Search cities, states, or content..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {filteredCities.length === 0 ? (
                            <p className="text-center py-8 text-muted-foreground">
                                {searchQuery ?
                                    "No cities found matching your search." :
                                    "No cities found. Upload a CSV file to get started."
                                }
                            </p>
                        ) : (
                            filteredCities.map((city) => (
                                <div key={city.id} className="border rounded-lg p-4">
                                    <div className="flex justify-between items-start mb-4">
                                        <div>
                                            <h3 className="font-medium text-lg">{city.city}, {city.state}</h3>
                                            <p className="text-sm text-muted-foreground">
                                                Last updated: {new Date(city.updated_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {/* RV Repair Category */}
                                        <div className="border rounded p-3">
                                            <div className="flex justify-between items-center mb-2">
                                                <div className="flex items-center gap-2">
                                                    <h4 className="font-medium">RV Repair</h4>
                                                    <Badge variant={getCategoryBadgeVariant(city, 'rv-repair')}>
                                                        {city.category_content?.['rv-repair']?.title ? 'Has Content' : 'No Content'}
                                                    </Badge>
                                                </div>
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    onClick={() => openEditModal(city, 'rv-repair')}
                                                >
                                                    <Edit className="w-3 h-3 mr-1" />
                                                    Edit
                                                </Button>
                                            </div>
                                            {city.category_content?.['rv-repair']?.title && (
                                                <p className="text-sm text-muted-foreground truncate">
                                                    {city.category_content['rv-repair'].title}
                                                </p>
                                            )}
                                        </div>

                                        {/* RV Inspection Category */}
                                        <div className="border rounded p-3">
                                            <div className="flex justify-between items-center mb-2">
                                                <div className="flex items-center gap-2">
                                                    <h4 className="font-medium">RV Inspection</h4>
                                                    <Badge variant={getCategoryBadgeVariant(city, 'rv-inspection')}>
                                                        {city.category_content?.['rv-inspection']?.title ? 'Has Content' : 'No Content'}
                                                    </Badge>
                                                </div>
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    onClick={() => openEditModal(city, 'rv-inspection')}
                                                >
                                                    <Edit className="w-3 h-3 mr-1" />
                                                    Edit
                                                </Button>
                                            </div>
                                            {city.category_content?.['rv-inspection']?.title && (
                                                <p className="text-sm text-muted-foreground truncate">
                                                    {city.category_content['rv-inspection'].title}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Edit Modal */}
            <Dialog open={editModalOpen} onOpenChange={setEditModalOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>
                            Edit {editModalData?.category === 'rv-repair' ? 'RV Repair' : 'RV Inspection'} Content
                        </DialogTitle>
                        <p className="text-sm text-muted-foreground">
                            {editModalData?.city}, {editModalData?.state}
                        </p>
                    </DialogHeader>

                    {editModalData && (
                        <div className="space-y-4">
                            <div>
                                <Label>Title</Label>
                                <Input
                                    value={editModalData.title}
                                    onChange={(e) => setEditModalData({
                                        ...editModalData,
                                        title: e.target.value
                                    })}
                                    placeholder={`e.g., ${editModalData.category === 'rv-repair'
                                        ? 'RV Mobile Repair Services Across'
                                        : 'NRVIA Certified RV Inspectors in'} ${editModalData.city}`}
                                />
                            </div>
                            <div>
                                <Label>Content</Label>
                                <Textarea
                                    value={editModalData.content}
                                    onChange={(e) => setEditModalData({
                                        ...editModalData,
                                        content: e.target.value
                                    })}
                                    placeholder={`Enter ${editModalData.category === 'rv-repair' ? 'repair' : 'inspection'}-specific content for ${editModalData.city}...`}
                                    rows={8}
                                />
                            </div>
                        </div>
                    )}

                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setEditModalOpen(false)}
                            disabled={saving}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSaveModal}
                            disabled={saving}
                        >
                            <Save className="w-4 h-4 mr-2" />
                            {saving ? "Saving..." : "Save"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
