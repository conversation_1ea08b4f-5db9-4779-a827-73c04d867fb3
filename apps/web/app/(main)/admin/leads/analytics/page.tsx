"use client";

import Loader from "@/components/Loader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import {
	Bar,
	BarChart,
	CartesianGrid,
	Cell,
	Pie,
	<PERSON>hart,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis
} from "recharts";

interface LocationData {
	location: string;
	count: number;
	percentage: number;
}

interface CategoryData {
	category: string;
	count: number;
	percentage: number;
}

interface AnalyticsData {
	totalJobs: number;
	totalQuotes: number;
	responseRate: number;
	acceptanceRate: number;
	completionRate: number;
	topCities: LocationData[];
	topStates: LocationData[];
	categoriesBreakdown: CategoryData[];
	statusBreakdown: { status: string; count: number }[];
}

const COLORS = [
	"#0088FE",
	"#00C49F",
	"#FFBB28",
	"#FF8042",
	"#8884D8",
	"#82CA9D",
	"#FFC658"
];

export default function LeadsAnalyticsPage() {
	const [data, setData] = useState<AnalyticsData | null>(null);
	const [loading, setLoading] = useState(true);
	const [viewType, setViewType] = useState<"city" | "state">("state");
	const [timeframe, setTimeframe] = useState<"30d" | "3m" | "all">("all");

	useEffect(() => {
		const fetchAnalytics = async () => {
			try {
				setLoading(true);
				const params = new URLSearchParams({ timeframe });
				const response = await fetch(`/api/admin/leads/analytics?${params}`);
				if (!response.ok) throw new Error("Failed to fetch analytics");

				const analyticsData = await response.json();
				setData(analyticsData);
			} catch (error) {
				console.error("Error fetching analytics:", error);
			} finally {
				setLoading(false);
			}
		};

		fetchAnalytics();
	}, [timeframe]);

	if (loading) {
		return (
			<div className="container mx-auto py-10">
				<Loader />
			</div>
		);
	}

	if (!data) {
		return (
			<div className="container mx-auto py-10">
				<div className="text-center">Failed to load analytics data</div>
			</div>
		);
	}

	const locationData = viewType === "city" ? data.topCities : data.topStates;

	return (
		<div className="container mx-auto py-10 space-y-6">
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-4">
					<Link href="/admin/leads">
						<Button variant="outline" size="sm">
							<ArrowLeft size={16} className="mr-2" />
							Back to Leads
						</Button>
					</Link>
					<h1 className="text-3xl font-bold">Jobs Analytics</h1>
				</div>
				<Select
					value={timeframe}
					onValueChange={(value: "30d" | "3m" | "all") => setTimeframe(value)}
				>
					<SelectTrigger className="w-40">
						<SelectValue />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="30d">Last 30 Days</SelectItem>
						<SelectItem value="3m">Last 3 Months</SelectItem>
						<SelectItem value="all">All Time</SelectItem>
					</SelectContent>
				</Select>
			</div>

			{/* Summary Cards */}
			<div className="grid grid-cols-1 md:grid-cols-6 gap-4">
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-muted-foreground">
							Total Jobs
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{data.totalJobs.toLocaleString()}
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-muted-foreground">
							Total Quotes
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{data.totalQuotes.toLocaleString()}
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-muted-foreground">
							Response Rate
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{data.responseRate.toFixed(1)}%
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-muted-foreground">
							Acceptance Rate
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{data.acceptanceRate.toFixed(1)}%
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-muted-foreground">
							Completion Rate
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{data.completionRate.toFixed(1)}%
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-muted-foreground">
							Top Category
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{data.categoriesBreakdown[0]?.category || "N/A"}
						</div>
						<div className="text-sm text-muted-foreground">
							{data.categoriesBreakdown[0]?.count || 0} jobs
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Location Analysis */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<Card>
					<CardHeader>
						<div className="flex items-center justify-between">
							<CardTitle>Jobs by Location</CardTitle>
							<Select
								value={viewType}
								onValueChange={(value: "city" | "state") => setViewType(value)}
							>
								<SelectTrigger className="w-32">
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="state">By State</SelectItem>
									<SelectItem value="city">By City</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</CardHeader>
					<CardContent>
						<ResponsiveContainer width="100%" height={400}>
							<BarChart data={locationData.slice(0, 10)}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis
									dataKey="location"
									angle={-45}
									textAnchor="end"
									height={80}
								/>
								<YAxis />
								<Tooltip />
								<Bar dataKey="count" fill="#0088FE" />
							</BarChart>
						</ResponsiveContainer>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>
							Top {viewType === "city" ? "Cities" : "States"}
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{locationData.slice(0, 10).map((item, index) => (
								<div
									key={item.location}
									className="flex items-center justify-between"
								>
									<div className="flex items-center gap-3">
										<div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs font-bold">
											{index + 1}
										</div>
										<span className="font-medium">{item.location}</span>
									</div>
									<div className="text-right">
										<div className="font-bold">{item.count}</div>
										<div className="text-sm text-muted-foreground">
											{item.percentage.toFixed(1)}%
										</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Categories and Status */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<Card>
					<CardHeader>
						<CardTitle>Jobs by Category</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
							<ResponsiveContainer width="100%" height={250}>
								<PieChart>
									<Pie
										data={data.categoriesBreakdown}
										cx="50%"
										cy="50%"
										outerRadius={80}
										fill="#8884d8"
										dataKey="count"
									>
										{data.categoriesBreakdown.map((entry, index) => (
											<Cell
												key={`cell-${index}`}
												fill={COLORS[index % COLORS.length]}
											/>
										))}
									</Pie>
									<Tooltip formatter={(value, name) => [value, "Jobs"]} />
								</PieChart>
							</ResponsiveContainer>
							<div className="space-y-3">
								{data.categoriesBreakdown.map((item, index) => (
									<div
										key={item.category}
										className="flex items-center justify-between"
									>
										<div className="flex items-center gap-2">
											<div
												className="w-4 h-4 rounded-full"
												style={{
													backgroundColor: COLORS[index % COLORS.length]
												}}
											/>
											<span className="text-sm font-medium">
												{item.category}
											</span>
										</div>
										<div className="text-right">
											<div className="text-sm font-bold">{item.count}</div>
											<div className="text-xs text-muted-foreground">
												{item.percentage.toFixed(1)}%
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Jobs by Status</CardTitle>
					</CardHeader>
					<CardContent>
						<ResponsiveContainer width="100%" height={300}>
							<BarChart data={data.statusBreakdown}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis dataKey="status" />
								<YAxis />
								<Tooltip />
								<Bar dataKey="count" fill="#00C49F" />
							</BarChart>
						</ResponsiveContainer>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
