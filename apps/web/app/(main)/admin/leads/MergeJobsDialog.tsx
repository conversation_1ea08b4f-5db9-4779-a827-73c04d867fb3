"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { getCategoryName } from "@/lib/categories";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { HelpCircle } from "lucide-react";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { Lead } from "./columns";

interface MergeJobsDialogProps {
	isOpen: boolean;
	onClose: () => void;
	selectedJobs: Lead[];
	onSuccess: () => void;
}

export default function MergeJobsDialog({
	isOpen,
	onClose,
	selectedJobs,
	onSuccess
}: MergeJobsDialogProps) {
	const [primaryJobId, setPrimaryJobId] = useState<string>("");
	const [isMerging, setIsMerging] = useState(false);

	const handleMerge = async () => {
		if (!primaryJobId) {
			toast.error("Please select a primary job");
			return;
		}

		setIsMerging(true);
		try {
			const response = await fetch("/api/admin/jobs/merge", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					primaryJobId,
					jobsToMerge: selectedJobs
						.filter((job) => job.id !== primaryJobId)
						.map((job) => job.id)
				})
			});

			if (!response.ok) {
				throw new Error("Failed to merge jobs");
			}

			toast.success("Jobs merged successfully");
			onSuccess();
			onClose();
		} catch (error) {
			console.error("Error merging jobs:", error);
			toast.error("Failed to merge jobs");
		} finally {
			setIsMerging(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl">
				<DialogHeader className="relative">
					<DialogTitle className="flex items-center gap-2 pr-8">
						Merge Jobs
					</DialogTitle>
					<a
						href="/docs/merge-service-requests"
						target="_blank"
						rel="noopener noreferrer"
						className="text-gray-400 hover:text-gray-600 transition-colors"
						title="View documentation"
					>
						<HelpCircle className="h-4 w-4" />
					</a>
					<DialogDescription>
						Select the primary job that will remain after merging. All quotes,
						messages, and timeline updates from other jobs will be transferred
						to the primary job.
						<p className="mt-2 text-sm">
							<span className="inline-block w-3 h-3 bg-yellow-100 rounded-sm mr-2" />{" "}
							Matching fields
						</p>
					</DialogDescription>
				</DialogHeader>

				<div className="mt-4 grid grid-cols-2 gap-4">
					{selectedJobs.map((job) => {
						const otherJob = selectedJobs.find((j) => j.id !== job.id);
						const matches = {
							email:
								otherJob?.email?.toLowerCase() === job.email?.toLowerCase(),
							phone: otherJob?.phone === job.phone,
							category: otherJob?.category === job.category,
							location:
								otherJob?.location?.city === job.location?.city &&
								otherJob?.location?.state === job.location?.state
						};

						return (
							<div key={job.id} className="border rounded-lg p-4">
								<div className="flex justify-between items-start mb-4">
									<div>
										<h3 className="font-semibold">{job.user_name}</h3>
										<p className="text-sm text-muted-foreground">
											{job.location?.city}, {job.location?.state}
										</p>
									</div>
									<input
										type="radio"
										name="primaryJob"
										value={job.id}
										checked={primaryJobId === job.id}
										onChange={(e) => setPrimaryJobId(e.target.value)}
									/>
								</div>

								<div className="space-y-2 text-sm">
									<div className="flex justify-between">
										<span>Category</span>
										<span
											className={cn(
												"font-medium",
												matches.category && "bg-yellow-100 px-1 rounded"
											)}
										>
											{getCategoryName(job.category)}
										</span>
									</div>
									<div className="flex justify-between">
										<span>Status</span>
										<span className="font-medium">{job.status}</span>
									</div>
									<div className="flex justify-between">
										<span>Quotes</span>
										<span className="font-medium">
											{job.quotes_count} ({job.quotes_responded} responded)
										</span>
									</div>
									<div className="flex justify-between">
										<span>Created</span>
										<span className="font-medium">
											{format(new Date(job.created_at), "MMM d, yyyy")}
										</span>
									</div>
									<div className="flex justify-between">
										<span>Age</span>
										<span className="font-medium">
											{job.days_since_created} days
										</span>
									</div>

									{job.is_premium && (
										<div className="mt-2">
											<Badge
												variant="outline"
												className="text-yellow-600 border-yellow-600"
											>
												Premium
											</Badge>
										</div>
									)}

									<div className="mt-4 p-3 bg-muted rounded-md">
										<h4 className="font-medium mb-2">Contact Info</h4>
										<p
											className={cn(
												"text-muted-foreground",
												matches.email && "bg-yellow-100 px-1 rounded"
											)}
										>
											{job.email}
										</p>
										{job.phone && (
											<p
												className={cn(
													"text-muted-foreground",
													matches.phone && "bg-yellow-100 px-1 rounded"
												)}
											>
												{job.phone}
											</p>
										)}
									</div>

									<div className="mt-2 p-3 bg-muted rounded-md">
										<h4 className="font-medium mb-2">Message Preview</h4>
										<p className="text-sm text-muted-foreground line-clamp-3">
											{job.message}
										</p>
									</div>
								</div>
							</div>
						);
					})}
				</div>

				<DialogFooter className="mt-6">
					<Button variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button onClick={handleMerge} disabled={!primaryJobId || isMerging}>
						{isMerging ? "Merging..." : "Merge Jobs"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
