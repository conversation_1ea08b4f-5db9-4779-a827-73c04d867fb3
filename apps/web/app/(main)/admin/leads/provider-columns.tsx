import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { SecurityIndicator } from "@/components/ui/security-indicator";
import { Textarea } from "@/components/ui/textarea";
import { getCategoryName } from "@/lib/categories";
import { JobStatus, QuoteStatus } from "@rvhelp/database";
import { ArrowUpDown, Building2, Crown, Eye, User } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { toast } from "react-hot-toast";

// Premium Upgrade Button Component
const PremiumUpgradeButton = ({
	lead,
	onSuccess
}: {
	lead: ProviderLead;
	onSuccess?: () => void;
}) => {
	const [isUpgrading, setIsUpgrading] = useState(false);
	const [adminMessage, setAdminMessage] = useState("");

	const handleUpgrade = async () => {
		try {
			setIsUpgrading(true);
			const response = await fetch(`/api/admin/leads/${lead.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					is_premium: !lead.is_premium,
					...(adminMessage.trim() && { admin_message: adminMessage.trim() })
				})
			});

			if (!response.ok) {
				throw new Error("Failed to update job");
			}

			const data = await response.json();
			toast.success(data.message || "Job updated successfully");
			setAdminMessage(""); // Reset message after success
			onSuccess?.();
		} catch (error) {
			console.error("Error updating job:", error);
			toast.error("Failed to update job");
		} finally {
			setIsUpgrading(false);
		}
	};

	return (
		<AlertDialog>
			<AlertDialogTrigger asChild>
				<Button
					variant="ghost"
					size="icon"
					className={lead.is_premium ? "text-yellow-600" : "text-gray-400"}
					title={
						lead.is_premium ? "Downgrade from Premium" : "Upgrade to Premium"
					}
				>
					<Crown className="h-4 w-4" />
				</Button>
			</AlertDialogTrigger>
			<AlertDialogContent className="max-w-md">
				<AlertDialogHeader>
					<AlertDialogTitle>
						{lead.is_premium
							? "Downgrade Job from Premium?"
							: "Upgrade Job to Premium?"}
					</AlertDialogTitle>
					<AlertDialogDescription>
						{lead.is_premium
							? "This will remove premium status from this job. Premium features will no longer apply."
							: "This will upgrade this job to premium status, giving the customer access to enhanced features for this job."}
					</AlertDialogDescription>
				</AlertDialogHeader>

				{!lead.is_premium && (
					<div className="space-y-2">
						<Label htmlFor="admin-message">Optional Message to Customer</Label>
						<Textarea
							id="admin-message"
							placeholder="Add a personal message to include in the upgrade notification email..."
							value={adminMessage}
							onChange={(e) => setAdminMessage(e.target.value)}
							rows={3}
							className="resize-none"
						/>
						<p className="text-xs text-muted-foreground">
							This message will be included in the email notification sent to
							the customer.
						</p>
					</div>
				)}

				<AlertDialogFooter>
					<AlertDialogCancel onClick={() => setAdminMessage("")}>
						Cancel
					</AlertDialogCancel>
					<AlertDialogAction onClick={handleUpgrade} disabled={isUpgrading}>
						{isUpgrading
							? "Processing..."
							: lead.is_premium
								? "Downgrade"
								: "Upgrade"}
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
};

export type ProviderLead = {
	id: string;
	user_name: string;
	email: string;
	phone: string | null;
	category: string;
	status: JobStatus;
	created_at: string;
	message: string;
	is_premium?: boolean;
	location?: {
		address?: string;
		city?: string;
		state?: string;
		zip?: string;
	};
	// Quote-related fields
	quotes_count: number;
	quotes_invited: number;
	quotes_responded: number;
	quotes_declined: number;
	quotes_pending: number;
	quotes_accepted: number;
	has_responses: boolean;
	days_since_created: number;
	latest_quote_response?: string;
	quote_statuses: QuoteStatus[];
	// Fraud and blacklist information
	flagged_for_fraud?: boolean;
	user_blacklisted?: boolean;
	email_blacklisted?: boolean;
	blacklist_message?: string;
	// Provider-specific fields
	quote_id: string;
	provider_name: string;
	provider_email: string;
	provider_phone?: string;
	provider_location?: {
		city?: string;
		state?: string;
	};
	quote_status: QuoteStatus;
	quote_invited_at: string;
	quote_responded_at?: string;
	quote_provider_notes?: string;
	quote_distance_miles?: number;
	quote_listing_slug: string;
	// Individual provider information for modal display
	providers?: Array<{
		id: string;
		status: QuoteStatus;
		invited_at: string;
		responded_at?: string;
		provider_notes?: string;
		distance_miles?: number;
		listing: {
			id: string;
			slug: string;
			business_name?: string;
			first_name: string;
			last_name: string;
			email: string;
			phone?: string;
			location?: {
				city?: string;
				state?: string;
			};
		};
	}>;
};

export type ProviderColumn = {
	accessorKey: keyof ProviderLead | "actions" | "fraud_status";
	header:
		| string
		| ((props: {
				column: {
					toggleSorting: (asc: boolean) => void;
					getIsSorted: () => string | false;
				};
		  }) => React.ReactNode);
	cell?: (props: {
		row: { original: ProviderLead; getValue: (key: string) => any };
	}) => React.ReactNode;
};

export const providerColumns: ProviderColumn[] = [
	{
		accessorKey: "provider_name",
		header: ({ column }) => {
			return (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
				>
					Provider
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			);
		},
		cell: ({ row }) => {
			const lead = row.original;
			const isBusiness =
				lead.provider_name !==
				`${lead.providers?.[0]?.listing.first_name} ${lead.providers?.[0]?.listing.last_name}`;

			return (
				<div className="flex items-center gap-2">
					{isBusiness ? (
						<Building2 className="w-4 h-4 text-blue-600" />
					) : (
						<User className="w-4 h-4 text-green-600" />
					)}
					<div className="flex flex-col">
						<Link
							href={`/providers/${lead.quote_listing_slug}`}
							className="font-medium text-blue-600 hover:text-blue-800 transition-colors hover:underline"
							target="_blank"
							rel="noopener noreferrer"
						>
							{lead.provider_name}
						</Link>
						<div className="text-xs text-muted-foreground">
							{lead.provider_email}
						</div>
					</div>
				</div>
			);
		}
	},
	{
		accessorKey: "user_name",
		header: ({ column }) => {
			return (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
				>
					Customer
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			);
		},
		cell: ({ row }) => {
			const lead = row.original;
			return (
				<div className="flex flex-col">
					<div className="font-medium">{lead.user_name}</div>
					<div className="text-xs text-muted-foreground">{lead.email}</div>
				</div>
			);
		}
	},
	{
		accessorKey: "category",
		header: "Category",
		cell: ({ row }) => {
			const category = row.getValue("category") as string;
			return <div className="capitalize">{getCategoryName(category)}</div>;
		}
	},
	{
		accessorKey: "quote_status",
		header: "Quote Status",
		cell: ({ row }) => {
			const status = row.getValue("quote_status") as QuoteStatus;
			const isPremium = row.original.is_premium;

			let variant:
				| "secondary"
				| "success"
				| "destructive"
				| "warning"
				| "default" = "secondary";

			if (status === "ACCEPTED") {
				variant = "success";
			} else if (status === "WITHDRAWN") {
				variant = "destructive";
			} else if (status === "PENDING") {
				variant = "secondary";
			} else if (status === "COMPLETED") {
				variant = "default";
			}

			return (
				<div className="flex flex-col">
					<div className="flex items-center gap-2">
						<Badge variant={variant} className="capitalize">
							{status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
						</Badge>
						{isPremium && (
							<Badge
								variant="outline"
								className="text-yellow-600 border-yellow-600 text-xs"
							>
								<Crown className="h-3 w-3 mr-1" />
								Premium
							</Badge>
						)}
					</div>
				</div>
			);
		}
	},
	{
		accessorKey: "fraud_status",
		header: "Security",
		cell: ({ row }) => {
			const lead = row.original;
			return (
				<div className="flex justify-center">
					<SecurityIndicator
						flaggedForFraud={lead.flagged_for_fraud}
						userBlacklisted={lead.user_blacklisted}
						emailBlacklisted={lead.email_blacklisted}
						blacklistMessage={lead.blacklist_message}
					/>
				</div>
			);
		}
	},
	{
		accessorKey: "provider_location",
		header: "Provider Location",
		cell: ({ row }) => {
			const location = row.getValue(
				"provider_location"
			) as ProviderLead["provider_location"];
			if (!location) return "N/A";

			if (location.city && location.state) {
				return `${location.city}, ${location.state}`;
			}

			return "N/A";
		}
	},
	{
		accessorKey: "quote_distance_miles",
		header: "Distance",
		cell: ({ row }) => {
			const distance = row.getValue("quote_distance_miles") as number;
			if (!distance) return "N/A";
			return `${distance.toFixed(1)} mi`;
		}
	},
	{
		accessorKey: "days_since_created",
		header: ({ column }) => {
			return (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
				>
					Age
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			);
		},
		cell: ({ row }) => {
			const days = row.getValue("days_since_created") as number;
			return (
				<div className="text-sm">{days === 0 ? "Today" : `${days}d ago`}</div>
			);
		}
	},
	{
		accessorKey: "actions",
		header: "",
		cell: ({ row }) => {
			const lead = row.original;
			return (
				<div className="flex items-center gap-1">
					<Button
						variant="ghost"
						size="icon"
						onClick={() =>
							window.dispatchEvent(
								new CustomEvent("OPEN_LEAD_DETAILS", { detail: lead })
							)
						}
						title="View Details"
					>
						<Eye className="h-4 w-4" />
					</Button>
					<PremiumUpgradeButton
						lead={lead}
						onSuccess={() => {
							// Optionally trigger a refresh of the table data
							window.location.reload();
						}}
					/>
				</div>
			);
		}
	}
];
