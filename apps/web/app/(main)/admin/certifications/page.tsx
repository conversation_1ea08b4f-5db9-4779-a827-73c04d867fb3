"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { Edit, Eye, Pause, Play, Plus, Trash2 } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface Certification {
	id: string;
	name: string;
	display_name: string;
	description?: string;
	is_active: boolean;
	created_at: string;
	updated_at: string;
	_count?: {
		provider_certifications: number;
	};
}

export default function AdminCertificationsPage() {
	const [certifications, setCertifications] = useState<Certification[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchCertifications();
	}, []);

	const fetchCertifications = async () => {
		try {
			const response = await fetch("/api/admin/certifications");
			if (response.ok) {
				const data = await response.json();
				setCertifications(data);
			} else {
				toast.error("Failed to fetch certifications");
			}
		} catch (error) {
			console.error("Error fetching certifications:", error);
			toast.error("Failed to fetch certifications");
		} finally {
			setLoading(false);
		}
	};

	const toggleCertificationStatus = async (
		certId: string,
		isActive: boolean
	) => {
		try {
			const response = await fetch(`/api/admin/certifications/${certId}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					is_active: !isActive
				})
			});

			if (response.ok) {
				toast.success(
					`Certification ${isActive ? "deactivated" : "activated"} successfully`
				);
				fetchCertifications(); // Refresh the list
			} else {
				toast.error("Failed to update certification status");
			}
		} catch (error) {
			console.error("Error updating certification:", error);
			toast.error("Failed to update certification status");
		}
	};

	const deleteCertification = async (certId: string) => {
		if (
			!confirm(
				"Are you sure you want to delete this certification? This action cannot be undone."
			)
		) {
			return;
		}

		try {
			const response = await fetch(`/api/admin/certifications/${certId}`, {
				method: "DELETE"
			});

			if (response.ok) {
				toast.success("Certification deleted successfully");
				fetchCertifications(); // Refresh the list
			} else {
				toast.error("Failed to delete certification");
			}
		} catch (error) {
			console.error("Error deleting certification:", error);
			toast.error("Failed to delete certification");
		}
	};

	if (loading) {
		return (
			<div className="container mx-auto py-6">
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
						<p className="text-gray-600">Loading certifications...</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-6">
			{/* Header */}
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-semibold text-gray-900">
						Certification Management
					</h1>
					<p className="text-gray-600 mt-2">
						Create and manage provider certification programs
					</p>
				</div>
				<Link href="/admin/certifications/new">
					<Button className="flex items-center gap-2">
						<Plus className="w-4 h-4" />
						Create Certification
					</Button>
				</Link>
			</div>

			{/* Stats Cards */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Total Certifications
						</CardTitle>
						<Badge variant="secondary">{certifications.length}</Badge>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{certifications.length}</div>
						<p className="text-xs text-muted-foreground">
							Active and inactive programs
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Active Programs
						</CardTitle>
						<Badge variant="default" className="bg-green-100 text-green-800">
							{certifications.filter((c) => c.is_active).length}
						</Badge>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">
							{certifications.filter((c) => c.is_active).length}
						</div>
						<p className="text-xs text-muted-foreground">
							Currently available to providers
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Total Enrollments
						</CardTitle>
						<Badge variant="outline">
							{certifications.reduce(
								(sum, cert) =>
									sum + (cert._count?.provider_certifications || 0),
								0
							)}
						</Badge>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{certifications.reduce(
								(sum, cert) =>
									sum + (cert._count?.provider_certifications || 0),
								0
							)}
						</div>
						<p className="text-xs text-muted-foreground">Across all programs</p>
					</CardContent>
				</Card>
			</div>

			{/* Certifications List */}
			<div className="grid gap-6">
				{certifications.map((certification) => (
					<Card
						key={certification.id}
						className="hover:shadow-md transition-shadow"
					>
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-4">
									<div>
										<CardTitle className="text-xl">
											{certification.display_name}
										</CardTitle>
										<CardDescription className="mt-1">
											{certification.description || "No description provided"}
										</CardDescription>
									</div>
									<Badge
										variant={certification.is_active ? "default" : "secondary"}
									>
										{certification.is_active ? "Active" : "Inactive"}
									</Badge>
								</div>
								<div className="flex items-center gap-2">
									<Badge variant="outline">
										{certification._count?.provider_certifications || 0}{" "}
										enrollments
									</Badge>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="flex items-center justify-between">
								<div className="text-sm text-gray-500">
									<p>ID: {certification.name}</p>
									<p>
										Created:{" "}
										{new Date(certification.created_at).toLocaleDateString()}
									</p>
									<p>
										Updated:{" "}
										{new Date(certification.updated_at).toLocaleDateString()}
									</p>
								</div>
								<div className="flex items-center gap-2">
									<Link href={`/admin/certifications/${certification.id}`}>
										<Button variant="outline" size="sm">
											<Eye className="w-4 h-4 mr-1" />
											View
										</Button>
									</Link>
									<Link href={`/admin/certifications/${certification.id}/edit`}>
										<Button variant="outline" size="sm">
											<Edit className="w-4 h-4 mr-1" />
											Edit
										</Button>
									</Link>
									<Button
										variant="outline"
										size="sm"
										onClick={() =>
											toggleCertificationStatus(
												certification.id,
												certification.is_active
											)
										}
									>
										{certification.is_active ? (
											<>
												<Pause className="w-4 h-4 mr-1" />
												Deactivate
											</>
										) : (
											<>
												<Play className="w-4 h-4 mr-1" />
												Activate
											</>
										)}
									</Button>
									<Button
										variant="outline"
										size="sm"
										onClick={() => deleteCertification(certification.id)}
										className="text-red-600 hover:text-red-700"
									>
										<Trash2 className="w-4 h-4 mr-1" />
										Delete
									</Button>
								</div>
							</div>
						</CardContent>
					</Card>
				))}

				{certifications.length === 0 && (
					<Card>
						<CardContent className="text-center py-12">
							<div className="text-gray-500 mb-4">
								<Plus className="w-12 h-12 mx-auto mb-4 opacity-50" />
								<h3 className="text-lg font-medium mb-2">
									No certifications yet
								</h3>
								<p className="text-sm">
									Create your first certification program to get started.
								</p>
							</div>
							<Link href="/admin/certifications/new">
								<Button>Create First Certification</Button>
							</Link>
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
