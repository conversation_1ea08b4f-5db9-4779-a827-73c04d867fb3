"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
	ArrowLeft,
	CheckCircle,
	Clock,
	Edit,
	FileText,
	Users,
	Video,
	XCircle
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface Certification {
	id: string;
	name: string;
	display_name: string;
	description?: string;
	terms_conditions?: string;
	training_content?: any;
	is_active: boolean;
	created_at: string;
	updated_at: string;
	_count?: {
		provider_certifications: number;
	};
	provider_certifications?: Array<{
		id: string;
		status: string;
		opted_out: boolean;
		training_completed_at?: string;
		terms_accepted_at?: string;
		created_at: string;
		user: {
			id: string;
			first_name: string;
			last_name: string;
			email: string;
		};
		listing: {
			id: string;
			business_name: string;
			slug: string;
		};
	}>;
}

export default function ViewCertificationPage({
	params
}: {
	params: { id: string };
}) {
	const router = useRouter();
	const [loading, setLoading] = useState(true);
	const [certification, setCertification] = useState<Certification | null>(
		null
	);

	useEffect(() => {
		fetchCertification();
	}, [params.id]);

	const fetchCertification = async () => {
		try {
			const response = await fetch(`/api/admin/certifications/${params.id}`);
			if (response.ok) {
				const data = await response.json();
				setCertification(data);
			} else {
				toast.error("Failed to fetch certification");
				router.push("/admin/certifications");
			}
		} catch (error) {
			console.error("Error fetching certification:", error);
			toast.error("Failed to fetch certification");
			router.push("/admin/certifications");
		} finally {
			setLoading(false);
		}
	};

	const getStatusBadge = (status: string, optedOut: boolean) => {
		if (optedOut) {
			return <Badge variant="destructive">Opted Out</Badge>;
		}
		switch (status) {
			case "COMPLETED":
				return (
					<Badge variant="default" className="bg-green-100 text-green-800">
						Completed
					</Badge>
				);
			case "IN_PROGRESS":
				return (
					<Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
						In Progress
					</Badge>
				);
			case "NOT_STARTED":
				return <Badge variant="outline">Not Started</Badge>;
			default:
				return <Badge variant="outline">Unknown</Badge>;
		}
	};

	const getStatusIcon = (status: string, optedOut: boolean) => {
		if (optedOut) return <XCircle className="w-4 h-4 text-red-500" />;
		switch (status) {
			case "COMPLETED":
				return <CheckCircle className="w-4 h-4 text-green-500" />;
			case "IN_PROGRESS":
				return <Clock className="w-4 h-4 text-yellow-500" />;
			default:
				return <Clock className="w-4 h-4 text-gray-400" />;
		}
	};

	if (loading) {
		return (
			<div className="container mx-auto py-6">
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
						<p className="text-gray-600">Loading certification...</p>
					</div>
				</div>
			</div>
		);
	}

	if (!certification) {
		return (
			<div className="container mx-auto py-6">
				<div className="text-center">
					<h1 className="text-2xl font-bold text-gray-900 mb-4">
						Certification Not Found
					</h1>
					<p className="text-gray-600 mb-6">
						The requested certification could not be found.
					</p>
					<Link href="/admin/certifications">
						<Button>Return to Certifications</Button>
					</Link>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-6">
			{/* Header */}
			<div className="mb-8">
				{/* Breadcrumbs */}
				<div className="flex items-center justify-between mb-4">
					<Link href="/admin/certifications">
						<Button variant="outline" size="sm">
							<ArrowLeft className="w-4 h-4 mr-2" />
							Back to Certifications
						</Button>
					</Link>
					<Link href={`/admin/certifications/${certification.id}/edit`}>
						<Button>
							<Edit className="w-4 h-4 mr-2" />
							Edit Certification
						</Button>
					</Link>
				</div>

				{/* Title */}
				<div>
					<h1 className="text-3xl font-semibold text-gray-900">
						{certification.display_name}
					</h1>
					<p className="text-gray-600 mt-2">
						Certification Details & Enrollments
					</p>
				</div>
			</div>

			{/* Stats Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Total Enrollments
						</CardTitle>
						<Users className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{certification._count?.provider_certifications || 0}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Completed</CardTitle>
						<CheckCircle className="h-4 w-4 text-green-500" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">
							{certification.provider_certifications?.filter(
								(c) => c.status === "COMPLETED"
							).length || 0}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">In Progress</CardTitle>
						<Clock className="h-4 w-4 text-yellow-500" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-yellow-600">
							{certification.provider_certifications?.filter(
								(c) => c.status === "IN_PROGRESS"
							).length || 0}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Opted Out</CardTitle>
						<XCircle className="h-4 w-4 text-red-500" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-red-600">
							{certification.provider_certifications?.filter((c) => c.opted_out)
								.length || 0}
						</div>
					</CardContent>
				</Card>
			</div>

			<Tabs defaultValue="overview" className="space-y-6">
				<TabsList>
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="enrollments">Enrollments</TabsTrigger>
					<TabsTrigger value="content">Training Content</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle>Certification Details</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<Label className="text-sm font-medium text-gray-500">
										Certification ID
									</Label>
									<p className="text-sm">{certification.name}</p>
								</div>
								<div>
									<label className="text-sm font-medium text-gray-500">
										Status
									</label>
									<div className="mt-1">
										<Badge
											variant={
												certification.is_active ? "default" : "secondary"
											}
										>
											{certification.is_active ? "Active" : "Inactive"}
										</Badge>
									</div>
								</div>
								<div>
									<Label className="text-sm font-medium text-gray-500">
										Created
									</Label>
									<p className="text-sm">
										{new Date(certification.created_at).toLocaleDateString()}
									</p>
								</div>
								<div>
									<Label className="text-sm font-medium text-gray-500">
										Last Updated
									</Label>
									<p className="text-sm">
										{new Date(certification.updated_at).toLocaleDateString()}
									</p>
								</div>
							</div>
							{certification.description && (
								<div>
									<Label className="text-sm font-medium text-gray-500">
										Description
									</Label>
									<p className="text-sm mt-1">{certification.description}</p>
								</div>
							)}
							{certification.terms_conditions && (
								<div>
									<Label className="text-sm font-medium text-gray-500">
										Terms & Conditions
									</Label>
									<p className="text-sm mt-1 whitespace-pre-wrap">
										{certification.terms_conditions}
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="enrollments" className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle>Provider Enrollments</CardTitle>
							<CardDescription>
								{certification.provider_certifications?.length || 0} total
								enrollments
							</CardDescription>
						</CardHeader>
						<CardContent>
							{certification.provider_certifications &&
							certification.provider_certifications.length > 0 ? (
								<div className="space-y-4">
									{certification.provider_certifications.map((enrollment) => (
										<div
											key={enrollment.id}
											className="flex items-center justify-between p-4 border rounded-lg"
										>
											<div className="flex items-center gap-4">
												{getStatusIcon(enrollment.status, enrollment.opted_out)}
												<div>
													<p className="font-medium">
														{enrollment.user.first_name}{" "}
														{enrollment.user.last_name}
													</p>
													<p className="text-sm text-gray-500">
														{enrollment.user.email}
													</p>
													<p className="text-sm text-gray-500">
														{enrollment.listing.business_name}
													</p>
												</div>
											</div>
											<div className="flex items-center gap-4">
												{getStatusBadge(
													enrollment.status,
													enrollment.opted_out
												)}
												<div className="text-right text-sm text-gray-500">
													<p>
														Enrolled:{" "}
														{new Date(
															enrollment.created_at
														).toLocaleDateString()}
													</p>
													{enrollment.training_completed_at && (
														<p>
															Completed:{" "}
															{new Date(
																enrollment.training_completed_at
															).toLocaleDateString()}
														</p>
													)}
												</div>
											</div>
										</div>
									))}
								</div>
							) : (
								<div className="text-center py-8 text-gray-500">
									<Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
									<p>No enrollments yet</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="content" className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle>Training Modules</CardTitle>
							<CardDescription>
								{certification.training_content?.modules?.length || 0} training
								modules
							</CardDescription>
						</CardHeader>
						<CardContent>
							{certification.training_content?.modules ? (
								<div className="space-y-4">
									{certification.training_content.modules.map(
										(module: any, index: number) => (
											<div key={module.id} className="border rounded-lg p-4">
												<div className="flex items-center justify-between mb-4">
													<h3 className="font-medium">
														Module {index + 1}: {module.title}
													</h3>
													<div className="flex gap-2">
														{module.video_url && (
															<Badge
																variant="outline"
																className="bg-blue-50 text-blue-700"
															>
																<Video className="w-3 h-3 mr-1" />
																Video
															</Badge>
														)}
														{module.content && (
															<Badge
																variant="outline"
																className="bg-green-50 text-green-700"
															>
																<FileText className="w-3 h-3 mr-1" />
																Content
															</Badge>
														)}
													</div>
												</div>

												{module.video_url && (
													<div className="mb-4">
														<h4 className="text-sm font-medium text-gray-700 mb-2">
															Video
														</h4>
														<div className="bg-gray-50 p-3 rounded border">
															<a
																href={module.video_url}
																target="_blank"
																rel="noopener noreferrer"
																className="text-blue-600 hover:underline break-all"
															>
																{module.video_url}
															</a>
														</div>
													</div>
												)}

												{module.content && (
													<div>
														<h4 className="text-sm font-medium text-gray-700 mb-2">
															Content
														</h4>
														<div
															className="prose prose-sm max-w-none bg-gray-50 p-4 rounded border"
															dangerouslySetInnerHTML={{
																__html: module.content
															}}
														/>
													</div>
												)}
											</div>
										)
									)}
								</div>
							) : (
								<div className="text-center py-8 text-gray-500">
									<p>No training content configured</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
