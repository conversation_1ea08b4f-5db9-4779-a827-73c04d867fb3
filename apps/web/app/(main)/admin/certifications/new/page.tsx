"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import Wysiwyg from "@/components/Wysiwyg";
import { ArrowLeft, FileText, Plus, Save, Trash2, Video } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface TrainingModule {
	id: string;
	title: string;
	video_url?: string;
	content?: string;
}

export default function CreateCertificationPage() {
	const router = useRouter();
	const [loading, setLoading] = useState(false);
	const [formData, setFormData] = useState({
		name: "",
		display_name: "",
		description: "",
		terms_conditions: "",
		is_active: true
	});
	const [trainingModules, setTrainingModules] = useState<TrainingModule[]>([
		{
			id: "1",
			title: "Welcome",
			content: "Welcome to this certification program!"
		}
	]);

	const addModule = () => {
		const newModule: TrainingModule = {
			id: Date.now().toString(),
			title: `Module ${trainingModules.length + 1}`
		};
		setTrainingModules([...trainingModules, newModule]);
	};

	const removeModule = (id: string) => {
		if (trainingModules.length > 1) {
			setTrainingModules(trainingModules.filter((module) => module.id !== id));
		}
	};

	const updateModule = (
		id: string,
		field: keyof TrainingModule,
		value: string
	) => {
		setTrainingModules(
			trainingModules.map((module) =>
				module.id === id ? { ...module, [field]: value } : module
			)
		);
	};

	const validateModules = () => {
		for (const module of trainingModules) {
			if (!module.video_url && !module.content) {
				toast.error(
					`Module "${module.title}" must have either video or content`
				);
				return false;
			}
		}
		return true;
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!validateModules()) {
			return;
		}

		setLoading(true);

		try {
			const trainingContent = {
				name: formData.name,
				display_name: formData.display_name,
				description: formData.description,
				terms_conditions: formData.terms_conditions,
				training_content: {
					modules: trainingModules
				}
			};

			const response = await fetch("/api/admin/certifications", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(trainingContent)
			});

			if (response.ok) {
				toast.success("Certification created successfully!");
				router.push("/admin/certifications");
			} else {
				const error = await response.json();
				toast.error(error.error || "Failed to create certification");
			}
		} catch (error) {
			console.error("Error creating certification:", error);
			toast.error("Failed to create certification");
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="container mx-auto py-6">
			{/* Header */}
			<div className="mb-8">
				{/* Breadcrumbs */}
				<div className="flex items-center gap-2 mb-4">
					<Link href="/admin/certifications">
						<Button variant="outline" size="sm">
							<ArrowLeft className="w-4 h-4 mr-2" />
							Back to Certifications
						</Button>
					</Link>
				</div>

				{/* Title */}
				<div>
					<h1 className="text-3xl font-semibold text-gray-900">
						Create New Certification
					</h1>
					<p className="text-gray-600 mt-2">
						Set up a new provider certification program
					</p>
				</div>
			</div>

			<form onSubmit={handleSubmit} className="space-y-8">
				{/* Basic Information */}
				<Card>
					<CardHeader>
						<CardTitle>Basic Information</CardTitle>
						<CardDescription>
							Define the core details of your certification program
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label htmlFor="name">Certification ID *</Label>
								<Input
									id="name"
									value={formData.name}
									onChange={(e) =>
										setFormData({ ...formData, name: e.target.value })
									}
									placeholder="e.g., keystone-warranty"
									required
								/>
								<p className="text-sm text-gray-500 mt-1">
									Unique identifier (lowercase, no spaces)
								</p>
							</div>
							<div>
								<Label htmlFor="display_name">Display Name *</Label>
								<Input
									id="display_name"
									value={formData.display_name}
									onChange={(e) =>
										setFormData({ ...formData, display_name: e.target.value })
									}
									placeholder="e.g., Keystone Warranty Certification"
									required
								/>
							</div>
						</div>
						<div>
							<Label htmlFor="description">Description</Label>
							<Textarea
								id="description"
								value={formData.description}
								onChange={(e) =>
									setFormData({ ...formData, description: e.target.value })
								}
								placeholder="Describe what this certification covers..."
								rows={3}
							/>
						</div>
						<div>
							<Label htmlFor="terms_conditions">Terms & Conditions</Label>
							<Textarea
								id="terms_conditions"
								value={formData.terms_conditions}
								onChange={(e) =>
									setFormData({ ...formData, terms_conditions: e.target.value })
								}
								placeholder="Terms and conditions that providers must agree to..."
								rows={4}
							/>
						</div>
						<div className="flex items-center space-x-2">
							<Switch
								id="is_active"
								checked={formData.is_active}
								onCheckedChange={(checked) =>
									setFormData({ ...formData, is_active: checked })
								}
							/>
							<Label htmlFor="is_active">Active (available to providers)</Label>
						</div>
					</CardContent>
				</Card>

				{/* Training Modules */}
				<Card>
					<CardHeader>
						<div className="flex items-center justify-between">
							<div>
								<CardTitle>Training Modules</CardTitle>
								<CardDescription>
									Create the training content that providers will complete. Each
									module must have either video or content.
								</CardDescription>
							</div>
							<Button
								type="button"
								onClick={addModule}
								variant="outline"
								size="sm"
							>
								<Plus className="w-4 h-4 mr-2" />
								Add Module
							</Button>
						</div>
					</CardHeader>
					<CardContent className="space-y-6">
						{trainingModules.map((module, index) => (
							<div key={module.id} className="border rounded-lg p-4">
								<div className="flex items-center justify-between mb-4">
									<h3 className="font-medium">Module {index + 1}</h3>
									{trainingModules.length > 1 && (
										<Button
											type="button"
											onClick={() => removeModule(module.id)}
											variant="outline"
											size="sm"
											className="text-red-600 hover:text-red-700"
										>
											<Trash2 className="w-4 h-4 mr-1" />
											Remove
										</Button>
									)}
								</div>

								<div className="space-y-4">
									<div>
										<Label htmlFor={`title-${module.id}`}>Title</Label>
										<Input
											id={`title-${module.id}`}
											value={module.title}
											onChange={(e) =>
												updateModule(module.id, "title", e.target.value)
											}
											placeholder="Module title"
										/>
									</div>

									<div className="space-y-4">
										<div className="space-y-2">
											<label
												htmlFor={`video-${module.id}`}
												className="flex items-center gap-2 mb-2"
											>
												<Video className="w-4 h-4" />
												Video URL (Optional)
											</label>
											<Input
												id={`video-${module.id}`}
												value={module.video_url || ""}
												onChange={(e) =>
													updateModule(module.id, "video_url", e.target.value)
												}
												placeholder="https://www.youtube.com/watch?v=..."
											/>
											<p className="text-sm text-gray-500 mt-1">
												YouTube, Vimeo, or direct video URL
											</p>
										</div>

										<div className="flex items-center justify-center py-2">
											<div className="text-sm text-gray-500 font-medium">
												OR
											</div>
										</div>

										<div className="space-y-2">
											<label
												htmlFor={`content-${module.id}`}
												className="flex items-center gap-2 mb-2"
											>
												<FileText className="w-4 h-4" />
												Content (Optional)
											</label>
											<Wysiwyg
												name={`content-${module.id}`}
												value={module.content || ""}
												onChange={(value) =>
													updateModule(module.id, "content", value)
												}
												placeholder="Module content (supports rich text formatting)"
												className="min-h-[200px]"
												variant="full"
												enableLinks={true}
												enableImages={true}
											/>
										</div>
									</div>

									{!module.video_url && !module.content && (
										<div className="text-sm text-red-500 bg-red-50 p-2 rounded">
											⚠️ This module must have either video or content
										</div>
									)}
								</div>
							</div>
						))}
					</CardContent>
				</Card>

				{/* Submit */}
				<div className="flex justify-end gap-4">
					<Link href="/admin/certifications">
						<Button type="button" variant="outline">
							Cancel
						</Button>
					</Link>
					<Button type="submit" disabled={loading}>
						<Save className="w-4 h-4 mr-2" />
						{loading ? "Creating..." : "Create Certification"}
					</Button>
				</div>
			</form>
		</div>
	);
}
