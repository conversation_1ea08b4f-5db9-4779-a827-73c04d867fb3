"use client";

import { PlacesAutocomplete } from "@/components/places/PlacesAutocomplete";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { categories, getCategoryName } from "@/lib/categories";
import {
	Briefcase,
	Download,
	MapPin,
	Target,
	TrendingUp,
	Users
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
	CartesianGrid,
	Cell,
	Line,
	LineChart,
	Pie,
	<PERSON><PERSON>,
	ResponsiveContainer,
	<PERSON>lt<PERSON>,
	<PERSON>A<PERSON><PERSON>,
	<PERSON><PERSON>xis
} from "recharts";
import TermsModal from "./terms-modal";

interface Region {
	name: string;
	latitude: number;
	longitude: number;
	radius: number;
}

interface MarketData {
	timeframe: string;
	generatedAt: string;
	regions: Array<{
		region: {
			name: string;
			center: { latitude: number; longitude: number };
			radius: number;
		};
		metrics: {
			totalJobs: number;
			totalQuotes: number;
			completionRate: number;
			jobDensity: number;
			activeProviders: number;
			verifiedProviders: number;
			certifiedProProviders: number;
			verificationRate: number;
		};
		breakdown: {
			categories: Array<{
				category: string;
				count: number;
				percentage: number;
			}>;
			statuses: Array<{
				status: string;
				count: number;
				percentage: number;
			}>;
			providersByCategory: Array<{
				category: string;
				categoryId: string;
				totalProviders: number;
				verifiedProviders: number;
				certifiedProProviders: number;
				verificationRate: number;
				verificationBreakdown: Record<string, number>;
				rvtaaRegistered: number;
				rvtaaCertified: number;
				nrviaInspectors: number;
				rvtaaTotal: number;
			}>;
		};
		trends: {
			monthly: Array<{
				month: string;
				jobs: number;
				quotes: number;
			}>;
			growth: {
				jobsGrowth: number;
				quotesGrowth: number;
			} | null;
		};
	}>;
	comparison: {
		highestDemand: string;
		highestDensity: string;
		mostVerifiedProviders: string;
		highestVerificationRate: string;
		summary: string;
	} | null;
}

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

export default function MarketAnalyticsDashboard() {
	const router = useRouter();
	const [regions, setRegions] = useState<Region[]>([]);
	const [timeframe, setTimeframe] = useState<string>("3m");
	const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
	const [compareMode, setCompareMode] = useState<boolean>(false);
	const [data, setData] = useState<MarketData | null>(null);
	const [loading, setLoading] = useState<boolean>(false);
	const [error, setError] = useState<string | null>(null);

	// Terms and conditions state
	const [showTermsModal, setShowTermsModal] = useState<boolean>(true);
	const [hasAcceptedTerms, setHasAcceptedTerms] = useState<boolean>(true);

	// Get available categories for the dropdown
	const availableCategories = Object.keys(categories);

	// Check if user has accepted terms on component mount
	useEffect(() => {
		const checkTermsAcceptance = () => {
			try {
				const acceptedTerms = localStorage.getItem(
					"rvhelp-market-analytics-terms"
				);
				const acceptedDate = localStorage.getItem(
					"rvhelp-market-analytics-terms-date"
				);

				if (acceptedTerms === "true" && acceptedDate) {
					// Check if acceptance is within the last 30 days
					const thirtyDaysAgo = new Date();
					thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
					const acceptanceDate = new Date(acceptedDate);

					if (acceptanceDate >= thirtyDaysAgo) {
						setHasAcceptedTerms(true);
					} else {
						// Terms acceptance has expired
						localStorage.removeItem("rvhelp-market-analytics-terms");
						localStorage.removeItem("rvhelp-market-analytics-terms-date");
						setShowTermsModal(true);
					}
				} else {
					setShowTermsModal(true);
				}
			} catch (error) {
				// If localStorage is not available, show terms modal
				setShowTermsModal(true);
			}
		};

		checkTermsAcceptance();
	}, []);

	const handleAcceptTerms = () => {
		try {
			localStorage.setItem("rvhelp-market-analytics-terms", "true");
			localStorage.setItem(
				"rvhelp-market-analytics-terms-date",
				new Date().toISOString()
			);
		} catch (error) {
			// localStorage not available, continue anyway
			console.warn("Could not save terms acceptance to localStorage");
		}

		setHasAcceptedTerms(true);
		setShowTermsModal(false);
	};

	const handleDeclineTerms = () => {
		router.push("/provider/dashboard");
	};

	// Predefined popular regions for quick selection
	const popularRegions = [
		{
			name: "Anaheim, CA",
			latitude: 33.8366,
			longitude: -117.9143,
			radius: 50
		},
		{
			name: "Salem, OR",
			latitude: 44.9429,
			longitude: -123.0351,
			radius: 50
		},
		{
			name: "Portland, OR",
			latitude: 45.5152,
			longitude: -122.6784,
			radius: 50
		},
		{
			name: "Phoenix, AZ",
			latitude: 33.4484,
			longitude: -112.074,
			radius: 50
		},
		{
			name: "Denver, CO",
			latitude: 39.7392,
			longitude: -104.9903,
			radius: 50
		},
		{
			name: "Austin, TX",
			latitude: 30.2672,
			longitude: -97.7431,
			radius: 50
		}
	];

	const addRegion = (region: Region) => {
		if (regions.length >= 3) {
			setError("Maximum 3 regions allowed for comparison");
			return;
		}

		// Check if region already exists
		const exists = regions.some(
			(r) =>
				Math.abs(r.latitude - region.latitude) < 0.01 &&
				Math.abs(r.longitude - region.longitude) < 0.01
		);

		if (exists) {
			setError("Region already added");
			return;
		}

		setRegions([...regions, region]);
		setError(null);

		// Enable compare mode if multiple regions
		if (regions.length >= 1) {
			setCompareMode(true);
		}
	};

	const removeRegion = (index: number) => {
		const newRegions = regions.filter((_, i) => i !== index);
		setRegions(newRegions);

		// Disable compare mode if only one region left
		if (newRegions.length <= 1) {
			setCompareMode(false);
		}
	};

	const addPopularRegion = (region: Region) => {
		addRegion(region);
	};

	const handleLocationSelect = (
		address: string,
		details: google.maps.places.PlaceResult | null
	) => {
		if (details?.geometry?.location) {
			const lat = details.geometry.location.lat();
			const lng = details.geometry.location.lng();
			addRegion({
				name: address || "Custom Location",
				latitude: lat,
				longitude: lng,
				radius: 50 // Default radius
			});
		}
	};

	const updateRegionRadius = (index: number, radius: number) => {
		const updatedRegions = [...regions];
		updatedRegions[index].radius = radius;
		setRegions(updatedRegions);
	};

	const handleCategoryToggle = (categoryId: string) => {
		setSelectedCategories((prev) =>
			prev.includes(categoryId)
				? prev.filter((id) => id !== categoryId)
				: [...prev, categoryId]
		);
	};

	const fetchData = async () => {
		if (regions.length === 0) {
			setError("Please add at least one region to analyze");
			return;
		}

		setLoading(true);
		setError(null);

		try {
			const params = new URLSearchParams({
				regions: JSON.stringify(regions),
				timeframe,
				compareMode: compareMode.toString()
			});

			if (selectedCategories.length > 0) {
				params.append("categories", selectedCategories.join(","));
			}

			const response = await fetch(`/api/providers/market-analytics?${params}`);

			if (!response.ok) {
				throw new Error("Failed to fetch market data");
			}

			const result = await response.json();
			setData(result);
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
		} finally {
			setLoading(false);
		}
	};

	const exportData = () => {
		if (!data) return;

		const dataStr = JSON.stringify(data, null, 2);
		const dataBlob = new Blob([dataStr], { type: "application/json" });
		const url = URL.createObjectURL(dataBlob);
		const link = document.createElement("a");
		link.href = url;
		link.download = `market-analytics-${
			data.timeframe
		}-${new Date().toISOString().split("T")[0]}.json`;
		link.click();
		URL.revokeObjectURL(url);
	};

	// Show terms modal if not accepted
	if (!hasAcceptedTerms) {
		return (
			<TermsModal
				isOpen={showTermsModal}
				onAccept={handleAcceptTerms}
				onDecline={handleDeclineTerms}
			/>
		);
	}

	return (
		<div className="space-y-6">
			{/* Data Protection Notice */}
			<Card className="border-red-200 bg-red-50">
				<CardContent className="pt-6">
					<div className="flex items-center gap-2 text-red-700">
						<Users className="h-5 w-5" />
						<span className="font-medium">
							Confidential Market Data - For RVHelp Network Providers Only
						</span>
					</div>
					<p className="text-sm text-red-600 mt-1">
						This proprietary data is protected by your agreement. Sharing with
						unauthorized parties may result in legal action.
					</p>
				</CardContent>
			</Card>

			{/* Region Selection */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<MapPin className="h-5 w-5" />
						Select Regions to Analyze
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{/* Add Custom Location */}
					<div>
						<Label>Add Custom Location</Label>
						<PlacesAutocomplete
							onPlaceSelect={handleLocationSelect}
							placeholder="Search for a city and state..."
							locationType="city"
						/>
					</div>

					{/* Popular Regions */}
					<div>
						<Label>Quick Add Popular Regions</Label>
						<div className="flex flex-wrap gap-2 mt-2">
							{popularRegions.map((region) => (
								<Button
									key={`${region.latitude}-${region.longitude}`}
									variant="outline"
									size="sm"
									onClick={() => addPopularRegion(region)}
									disabled={regions.length >= 3}
								>
									{region.name}
								</Button>
							))}
						</div>
					</div>

					{/* Selected Regions */}
					{regions.length > 0 && (
						<div>
							<Label>Selected Regions ({regions.length}/3)</Label>
							<div className="space-y-2 mt-2">
								{regions.map((region, index) => (
									<div
										key={index}
										className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg"
									>
										<div className="flex-1">
											<div className="font-medium">{region.name}</div>
											<div className="text-sm text-gray-500">
												{region.latitude.toFixed(4)},{" "}
												{region.longitude.toFixed(4)}
											</div>
										</div>
										<div className="flex items-center gap-2">
											<Label className="text-sm">Radius:</Label>
											<Select
												value={region.radius.toString()}
												onValueChange={(value) =>
													updateRegionRadius(index, parseInt(value))
												}
											>
												<SelectTrigger className="w-20">
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="25">25mi</SelectItem>
													<SelectItem value="50">50mi</SelectItem>
													<SelectItem value="75">75mi</SelectItem>
													<SelectItem value="100">100mi</SelectItem>
												</SelectContent>
											</Select>
										</div>
										<Button
											variant="outline"
											size="sm"
											onClick={() => removeRegion(index)}
										>
											Remove
										</Button>
									</div>
								))}
							</div>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Filters */}
			<Card>
				<CardHeader>
					<CardTitle>Filters & Options</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<Label>Time Period</Label>
							<Select value={timeframe} onValueChange={setTimeframe}>
								<SelectTrigger>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="30d">Last 30 Days</SelectItem>
									<SelectItem value="3m">Last 3 Months</SelectItem>
									<SelectItem value="6m">Last 6 Months</SelectItem>
									<SelectItem value="1y">Last Year</SelectItem>
									<SelectItem value="all">All Time</SelectItem>
								</SelectContent>
							</Select>
						</div>

						<div>
							<Label>Service Categories (Optional)</Label>
							<Select
								value={
									selectedCategories.length === 1
										? selectedCategories[0]
										: selectedCategories.length > 1
											? "multiple"
											: ""
								}
								onValueChange={(value) => {
									if (value === "all") {
										setSelectedCategories([]);
									} else if (value && value !== "multiple") {
										handleCategoryToggle(value);
									}
								}}
							>
								<SelectTrigger>
									<SelectValue
										placeholder={
											selectedCategories.length === 0
												? "All Categories"
												: selectedCategories.length === 1
													? getCategoryName(selectedCategories[0])
													: `${selectedCategories.length} categories selected`
										}
									/>
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Categories</SelectItem>
									{availableCategories.map((categoryId) => (
										<SelectItem key={categoryId} value={categoryId}>
											{getCategoryName(categoryId)}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>

					{/* Selected Categories Display */}
					{selectedCategories.length > 0 && (
						<div>
							<Label>Selected Categories:</Label>
							<div className="flex flex-wrap gap-2 mt-2">
								{selectedCategories.map((categoryId) => (
									<Badge
										key={categoryId}
										variant="secondary"
										className="cursor-pointer"
										onClick={() => handleCategoryToggle(categoryId)}
									>
										{getCategoryName(categoryId)} ✕
									</Badge>
								))}
							</div>
						</div>
					)}

					<div className="pt-4">
						<Button
							onClick={fetchData}
							disabled={loading || regions.length === 0}
							className="w-full"
						>
							{loading ? "Analyzing..." : "Generate Report"}
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Error Display */}
			{error && (
				<Card className="border-red-200 bg-red-50">
					<CardContent className="pt-6">
						<p className="text-red-600">{error}</p>
					</CardContent>
				</Card>
			)}

			{/* Results */}
			{data && (
				<div className="space-y-6">
					{/* Summary Cards */}
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
						{data.regions.map((region, index) => (
							<Card key={index}>
								<CardHeader className="pb-2">
									<CardTitle className="text-sm font-medium text-gray-600">
										{region.region.name}
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="space-y-2">
										<div className="flex items-center gap-2">
											<Briefcase className="h-4 w-4 text-blue-500" />
											<span className="text-2xl font-bold">
												{region.metrics.totalJobs}
											</span>
											<span className="text-sm text-gray-500">jobs</span>
										</div>
										<div className="flex items-center gap-2">
											<Users className="h-4 w-4 text-green-500" />
											<span className="text-lg font-semibold">
												{region.metrics.verifiedProviders}
											</span>
											<span className="text-sm text-gray-500">verified</span>
										</div>
										<div className="flex items-center gap-2">
											<Target className="h-4 w-4 text-orange-500" />
											<span className="text-lg font-semibold">
												{region.metrics.verificationRate.toFixed(1)}%
											</span>
											<span className="text-sm text-gray-500">
												verified rate
											</span>
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>

					{/* Comparison Summary */}
					{data.comparison && (
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<TrendingUp className="h-5 w-5" />
									Regional Comparison
								</CardTitle>
							</CardHeader>
							<CardContent>
								<p className="text-gray-700 mb-4">{data.comparison.summary}</p>
								<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
									<div className="text-center">
										<Badge variant="secondary" className="mb-2">
											Highest Demand
										</Badge>
										<p className="font-semibold">
											{data.comparison.highestDemand}
										</p>
									</div>
									<div className="text-center">
										<Badge variant="secondary" className="mb-2">
											Highest Density
										</Badge>
										<p className="font-semibold">
											{data.comparison.highestDensity}
										</p>
									</div>
									<div className="text-center">
										<Badge variant="secondary" className="mb-2">
											Most Verified Providers
										</Badge>
										<p className="font-semibold">
											{data.comparison.mostVerifiedProviders}
										</p>
									</div>
									<div className="text-center">
										<Badge variant="secondary" className="mb-2">
											Highest Verification Rate
										</Badge>
										<p className="font-semibold">
											{data.comparison.highestVerificationRate}
										</p>
									</div>
								</div>

								{/* RVTAA Certification Insights */}
								<div className="mt-6 p-4 bg-blue-50 rounded-lg">
									<h4 className="font-semibold text-blue-900 mb-2">
										RVTAA Certification Analysis
									</h4>
									<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
										{data.regions.map((region, index) => (
											<div key={index} className="bg-white p-3 rounded border">
												<div className="font-medium text-gray-900 mb-2">
													{region.region.name}
												</div>
												{region.breakdown.providersByCategory
													?.filter(
														(cat) =>
															cat.categoryId !== "rv-inspection" &&
															cat.rvtaaTotal > 0
													)
													.map((cat, catIndex) => (
														<div key={catIndex} className="mb-2">
															<div className="text-xs text-gray-600">
																{cat.category}:
															</div>
															<div className="text-xs">
																<span className="text-amber-600">
																	{cat.rvtaaRegistered} Registered
																</span>
																{" | "}
																<span className="text-emerald-600">
																	{cat.rvtaaCertified} Certified
																</span>
															</div>
														</div>
													))}
											</div>
										))}
									</div>
								</div>
							</CardContent>
						</Card>
					)}

					{/* Detailed Analytics */}
					<Tabs defaultValue="trends" className="space-y-4">
						<div className="flex justify-between items-center">
							<TabsList>
								<TabsTrigger value="trends">Trends</TabsTrigger>
								<TabsTrigger value="categories">Categories</TabsTrigger>
								<TabsTrigger value="providers">
									Provider Competition
								</TabsTrigger>
								<TabsTrigger value="metrics">Metrics</TabsTrigger>
							</TabsList>

							<Button onClick={exportData} variant="outline" size="sm">
								<Download className="h-4 w-4 mr-2" />
								Export Data
							</Button>
						</div>

						<TabsContent value="trends">
							<Card>
								<CardHeader>
									<CardTitle>Monthly Trends</CardTitle>
								</CardHeader>
								<CardContent>
									<ResponsiveContainer width="100%" height={300}>
										<LineChart data={data.regions[0]?.trends.monthly || []}>
											<CartesianGrid strokeDasharray="3 3" />
											<XAxis dataKey="month" />
											<YAxis />
											<Tooltip />
											<Line
												type="monotone"
												dataKey="jobs"
												stroke="#8884d8"
												strokeWidth={2}
											/>
											<Line
												type="monotone"
												dataKey="quotes"
												stroke="#82ca9d"
												strokeWidth={2}
											/>
										</LineChart>
									</ResponsiveContainer>
								</CardContent>
							</Card>
						</TabsContent>

						<TabsContent value="categories">
							<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
								{data.regions.map((region, index) => (
									<Card key={index}>
										<CardHeader>
											<CardTitle>
												{region.region.name} - Service Categories
											</CardTitle>
										</CardHeader>
										<CardContent>
											<ResponsiveContainer width="100%" height={250}>
												<PieChart>
													<Pie
														data={region.breakdown.categories}
														cx="50%"
														cy="50%"
														labelLine={false}
														label={({ category, percentage }) =>
															`${category} ${percentage.toFixed(1)}%`
														}
														outerRadius={80}
														fill="#8884d8"
														dataKey="count"
													>
														{region.breakdown.categories.map((entry, idx) => (
															<Cell
																key={`cell-${idx}`}
																fill={COLORS[idx % COLORS.length]}
															/>
														))}
													</Pie>
													<Tooltip />
												</PieChart>
											</ResponsiveContainer>
										</CardContent>
									</Card>
								))}
							</div>
						</TabsContent>

						<TabsContent value="providers">
							<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
								{data.regions.map((region, index) => (
									<Card key={index}>
										<CardHeader>
											<CardTitle>
												{region.region.name} - Provider Competition
											</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-4">
												{region.breakdown.providersByCategory?.length > 0 ? (
													region.breakdown.providersByCategory.map(
														(categoryData, idx) => (
															<div key={idx} className="border rounded-lg p-4">
																<div className="flex justify-between items-center mb-2">
																	<h4 className="font-semibold">
																		{categoryData.category}
																	</h4>
																	<Badge variant="outline">
																		{categoryData.totalProviders} providers
																	</Badge>
																</div>
																<div className="grid grid-cols-2 gap-4 text-sm">
																	<div>
																		<span className="text-gray-600">
																			Verified:
																		</span>
																		<span className="ml-2 font-medium text-green-600">
																			{categoryData.verifiedProviders}
																		</span>
																	</div>
																	<div>
																		<span className="text-gray-600">
																			Certified Pro:
																		</span>
																		<span className="ml-2 font-medium text-blue-600">
																			{categoryData.certifiedProProviders}
																		</span>
																	</div>
																	<div>
																		<span className="text-gray-600">
																			Verification Rate:
																		</span>
																		<span className="ml-2 font-medium">
																			{categoryData.verificationRate.toFixed(1)}
																			%
																		</span>
																	</div>
																	<div>
																		<span className="text-gray-600">
																			Unverified:
																		</span>
																		<span className="ml-2 font-medium text-gray-500">
																			{categoryData.totalProviders -
																				categoryData.verifiedProviders}
																		</span>
																	</div>

																	{/* Show RVTAA certifications for non-inspection categories */}
																	{categoryData.categoryId !==
																		"rv-inspection" &&
																		categoryData.rvtaaTotal > 0 && (
																			<>
																				<div className="col-span-2 border-t pt-2 mt-2">
																					<span className="text-gray-800 font-medium text-sm">
																						RVTAA Certifications:
																					</span>
																				</div>
																				<div>
																					<span className="text-gray-600">
																						Registered (Level 1):
																					</span>
																					<span className="ml-2 font-medium text-amber-600">
																						{categoryData.rvtaaRegistered}
																					</span>
																				</div>
																				<div>
																					<span className="text-gray-600">
																						Certified (Level 2):
																					</span>
																					<span className="ml-2 font-medium text-emerald-600">
																						{categoryData.rvtaaCertified}
																					</span>
																				</div>
																			</>
																		)}

																	{/* Show NRVIA inspectors for inspection category */}
																	{categoryData.categoryId ===
																		"rv-inspection" &&
																		categoryData.nrviaInspectors > 0 && (
																			<>
																				<div className="col-span-2 border-t pt-2 mt-2">
																					<span className="text-gray-800 font-medium text-sm">
																						NRVIA Certifications:
																					</span>
																				</div>
																				<div className="col-span-2">
																					<span className="text-gray-600">
																						NRVIA Inspectors:
																					</span>
																					<span className="ml-2 font-medium text-purple-600">
																						{categoryData.nrviaInspectors}
																					</span>
																				</div>
																			</>
																		)}
																</div>
															</div>
														)
													)
												) : (
													<p className="text-gray-500 text-center py-8">
														No provider data available for this region
													</p>
												)}
											</div>
										</CardContent>
									</Card>
								))}
							</div>
						</TabsContent>

						<TabsContent value="metrics">
							<Card>
								<CardHeader>
									<CardTitle>Performance Metrics</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="overflow-x-auto">
										<table className="w-full">
											<thead>
												<tr className="border-b">
													<th className="text-left p-2">Region</th>
													<th className="text-left p-2">Total Jobs</th>
													<th className="text-left p-2">Verified Providers</th>
													<th className="text-left p-2">Verification Rate</th>
													<th className="text-left p-2">Completion Rate</th>
													<th className="text-left p-2">Job Density</th>
												</tr>
											</thead>
											<tbody>
												{data.regions.map((region, index) => (
													<tr key={index} className="border-b">
														<td className="p-2 font-medium">
															{region.region.name}
														</td>
														<td className="p-2">{region.metrics.totalJobs}</td>
														<td className="p-2">
															{region.metrics.verifiedProviders}
														</td>
														<td className="p-2">
															{region.metrics.verificationRate.toFixed(1)}%
														</td>
														<td className="p-2">
															{region.metrics.completionRate.toFixed(1)}%
														</td>
														<td className="p-2">
															{region.metrics.jobDensity.toFixed(2)}
														</td>
													</tr>
												))}
											</tbody>
										</table>
									</div>
								</CardContent>
							</Card>
						</TabsContent>
					</Tabs>

					{/* Report Info */}
					<Card>
						<CardContent className="pt-6">
							<div className="flex justify-between items-center text-sm text-gray-500">
								<span>
									Report generated:{" "}
									{new Date(data.generatedAt).toLocaleString()}
								</span>
								<span>Timeframe: {data.timeframe}</span>
							</div>
						</CardContent>
					</Card>
				</div>
			)}
		</div>
	);
}
