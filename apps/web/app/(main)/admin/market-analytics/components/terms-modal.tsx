"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Al<PERSON><PERSON>riangle, FileText, Shield } from "lucide-react";
import { useState } from "react";

interface TermsModalProps {
	isOpen: boolean;
	onAccept: () => void;
	onDecline: () => void;
}

export default function TermsModal({
	isOpen,
	onAccept,
	onDecline
}: TermsModalProps) {
	const [hasReadTerms, setHasReadTerms] = useState(false);
	const [agreedToTerms, setAgreedToTerms] = useState(false);

	if (!isOpen) return null;

	const handleAccept = () => {
		if (hasReadTerms && agreedToTerms) {
			onAccept();
		}
	};

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
			<Card className="w-full max-w-4xl max-h-[90vh] flex flex-col">
				<CardHeader className="border-b">
					<CardTitle className="flex items-center gap-2 text-xl">
						<Shield className="h-6 w-6 text-red-600" />
						Proprietary Data Access Agreement
					</CardTitle>
					<div className="flex items-center gap-2 text-orange-600 bg-orange-50 p-3 rounded-lg">
						<AlertTriangle className="h-5 w-5" />
						<span className="font-medium">
							This page contains confidential and proprietary market
							intelligence data
						</span>
					</div>
				</CardHeader>

				<CardContent className="flex-1 overflow-hidden">
					<ScrollArea className="h-96 pr-4">
						<div className="space-y-6 text-sm leading-6">
							<div>
								<h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
									<FileText className="h-5 w-5" />
									Terms and Conditions for Market Analytics Access
								</h3>
								<p className="text-gray-600 mb-4">
									By accessing this Market Analytics system, you acknowledge and
									agree to the following terms:
								</p>
							</div>

							<div className="space-y-4">
								<div className="border-l-4 border-red-500 pl-4">
									<h4 className="font-semibold text-red-700 mb-2">
										1. PROPRIETARY AND CONFIDENTIAL DATA
									</h4>
									<p>
										The market analytics data, insights, reports, statistics,
										and all related information displayed on this platform
										(collectively, "Proprietary Data") are the exclusive
										property of RVHelp and constitute confidential and
										proprietary business intelligence.
									</p>
								</div>

								<div className="border-l-4 border-red-500 pl-4">
									<h4 className="font-semibold text-red-700 mb-2">
										2. STRICT NON-DISCLOSURE OBLIGATION
									</h4>
									<p>
										You explicitly agree that you will NOT, under any
										circumstances:
									</p>
									<ul className="list-disc list-inside mt-2 space-y-1 text-gray-700">
										<li>
											Share, distribute, or disclose any Proprietary Data to any
											third party
										</li>
										<li>
											Reproduce, copy, or transmit any portion of this data in
											any form
										</li>
										<li>
											Use this data to benefit competitors or non-RVHelp network
											participants
										</li>
										<li>
											Discuss specific metrics, statistics, or insights with
											unauthorized individuals
										</li>
										<li>
											Screenshot, print, or otherwise capture this data for
											external use
										</li>
									</ul>
								</div>

								<div className="border-l-4 border-blue-500 pl-4">
									<h4 className="font-semibold text-blue-700 mb-2">
										3. AUTHORIZED USE
									</h4>
									<p>
										This data is provided exclusively for your internal business
										planning and decision-making as an active provider in the
										RVHelp network. You may only discuss this information with:
									</p>
									<ul className="list-disc list-inside mt-2 space-y-1 text-gray-700">
										<li>
											Your direct business partners who are also active RVHelp
											providers
										</li>
										<li>
											Your immediate family members involved in your RVHelp
											business operations
										</li>
										<li>
											Your legal or financial advisors bound by professional
											confidentiality
										</li>
									</ul>
								</div>

								<div className="border-l-4 border-amber-500 pl-4">
									<h4 className="font-semibold text-amber-700 mb-2">
										4. DATA ACCURACY AND USAGE
									</h4>
									<p>
										While we strive for accuracy, this data is provided "as-is"
										for general guidance only. You agree to use this information
										responsibly and understand that market conditions may change
										rapidly.
									</p>
								</div>

								<div className="border-l-4 border-red-600 pl-4 bg-red-50 p-3 rounded">
									<h4 className="font-semibold text-red-800 mb-2">
										5. LEGAL CONSEQUENCES OF BREACH
									</h4>
									<p className="text-red-700">
										<strong>VIOLATION OF THIS AGREEMENT WILL RESULT IN:</strong>
									</p>
									<ul className="list-disc list-inside mt-2 space-y-1 text-red-700">
										<li>
											<strong>Immediate termination</strong> of your RVHelp
											network access
										</li>
										<li>
											<strong>Legal action</strong> including but not limited to
											injunctive relief
										</li>
										<li>
											<strong>Monetary damages</strong> including attorney fees
											and lost profits
										</li>
										<li>
											<strong>Prohibition</strong> from re-joining the RVHelp
											network
										</li>
									</ul>
								</div>

								<div className="border-l-4 border-green-500 pl-4">
									<h4 className="font-semibold text-green-700 mb-2">
										6. ACKNOWLEDGMENT
									</h4>
									<p>
										By clicking "I Agree and Accept" below, you acknowledge
										that:
									</p>
									<ul className="list-disc list-inside mt-2 space-y-1 text-gray-700">
										<li>You have read and understand these terms completely</li>
										<li>You agree to be legally bound by these restrictions</li>
										<li>
											You understand the serious consequences of any violation
										</li>
										<li>
											You will use this data solely for your authorized business
											purposes
										</li>
									</ul>
								</div>

								<div className="bg-gray-100 p-4 rounded-lg">
									<p className="text-sm text-gray-600">
										<strong>Questions about these terms?</strong> Contact our
										legal team before proceeding. This agreement is governed by
										applicable laws and any disputes will be resolved through
										binding arbitration.
									</p>
								</div>
							</div>
						</div>
					</ScrollArea>

					<div className="mt-6 space-y-4 border-t pt-4">
						<div className="flex items-center space-x-2">
							<Checkbox
								id="read-terms"
								checked={hasReadTerms}
								onCheckedChange={(checked) =>
									setHasReadTerms(checked as boolean)
								}
							/>
							<label htmlFor="read-terms" className="text-sm font-medium">
								I have read and understand the complete terms above
							</label>
						</div>

						<div className="flex items-center space-x-2">
							<Checkbox
								id="agree-terms"
								checked={agreedToTerms}
								onCheckedChange={(checked) =>
									setAgreedToTerms(checked as boolean)
								}
								disabled={!hasReadTerms}
							/>
							<label htmlFor="agree-terms" className="text-sm font-medium">
								I explicitly agree to these terms and understand the legal
								consequences of any violation
							</label>
						</div>
					</div>

					<div className="flex gap-4 mt-6">
						<Button onClick={onDecline} variant="outline" className="flex-1">
							Decline - Return to Dashboard
						</Button>
						<Button
							onClick={handleAccept}
							disabled={!hasReadTerms || !agreedToTerms}
							className="flex-1 bg-green-600 hover:bg-green-700"
						>
							I Agree and Accept Terms
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
