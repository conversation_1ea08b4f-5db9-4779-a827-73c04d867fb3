"use client";
import React, { useState } from 'react';
import { Play, RotateCcw, AlertCircle, Mail } from 'lucide-react';

const EmailClassifierTester = () => {
const OPENAI_API_KEY = '********************************************************************************************************************************************************************';
  const [testBody, setTestBody] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [promptVersion, setPromptVersion] = useState(1);

  const EMAIL_CATEGORIES = {
    'Friendly Hello': {
      description: 'A casual greeting or personal story that doesn\'t require assistance from the support team.',
      keywords: ['hi', 'hello', 'just saying hi', 'checking in', 'greetings', 'hope you\'re doing well']
    },
    "What is RV Help?": {
      description: 'A user asking what RV Help is and how it works.',
      keywords: ['what is rv help', 'how does it work', 'what is this', 'what is this app', 'what is this service']
    },
    'RV Technical Help Request': {
      description: 'A user asking RV Help directly for technical assistance with their specific RV, motorhome, trailer, or RV-related vehicle, not understanding we are a directory service.',
      keywords: ['question about my', 'help with my', 'could you help', 'could you possibly help', 'can you help', 'motorhome', 'trailer', 'chevrolet p30', 'winnebago', 'fleetwood', 'my rig', 'some question about my']
    },
    'Support Request': {
      description: 'A user (who does not seem to be a provider on the platform) requesting assistance or expressing dissatisfaction with a service experience',
      keywords: ['help', 'can\'t find', 'having trouble', 'need support', 'how do I', 'problem with', 'issue with']
    },
    'Pro Member Support Request': {
      description: 'A pro-member requesting assistance.',
      keywords: ['i\'m a pro member', 'paying user', 'as a member', 'my membership', 'subscription issue', 'pro support']
    },
    'Provider MIA': {
      description: 'No Provider Response / No Response After Provider Inquiry',
      keywords: ['haven\'t heard back', 'no reply yet', 'still waiting', 'follow up on', 'no response from provider']
    },
    'Pitch Received': {
      description: 'Users offering to provide services for RV Help',
      keywords: ['I\'d love to collaborate', 'offering my services', 'we do X', 'partner with you', 'help you grow RV Help']
    },
    'Promo Materials Request': {
      description: 'Users or providers requesting marketing material.',
      keywords: ['help you grow RV Help', 'spreading the word', 'do you have a flyer', 'I\'d like to promote', 'share with my audience']
    },
    'Uncategorized Buzz': {
      description: 'A question or message that doesn\'t fall under a specific category and may require further clarification.',
      keywords: ['I was wondering', 'quick question', 'not sure who to ask', 'just curious', 'random question']
    },
    'Bug Report': {
      description: 'Support request for a technical glitch or error.',
      keywords: ['bug report', 'not working', 'error message', 'can\'t log in', 'crashed', 'technical issue', 'app issue']
    },
    'Opt-Out': {
      description: 'User requesting to opt-out or stop receiving messages.',
      keywords: ['unsubscribe me', 'remove me from list', 'stop emailing me', 'opt-out', 'no more emails']
    },
    'Provider Support': {
      description: 'Mobile tech with a general question or feedback.',
      keywords: ['I\'m a technician', 'I\'m an RV tech', 'Mobile tech here', 'Service provider inquiry', 'My dashboard']
    },
    'Payment Issues': {
      description: 'Customer needs help with issues related to charges, payments, refunds, invoices etc.',
      keywords: ['invoice', 'charge on my card', 'payment', 'bill', 'payment failed', 'refund request', 'billing issue']
    },
    'Talent Inbox': {
      description: 'Someone\'s interested in joining the team—resume attached!',
      keywords: ['resume', 'job application', 'interested in joining', 'hiring', 'work for RV Help', 'employment']
    }
  };

  const SAMPLE_EMAILS = [
    {
      body: "I am not sure what you're doing or trying to \"sell\".",
      expected: "What is RV Help?"
    },
    {
      body: "Excuse me I have some question about my 1985 Chevrolet P30 could you possibly help with that.",
      expected: "RV Technical Help Request"
    },
    {
      body: "Hi, I'm having trouble logging into my account. The password reset isn't working. Can you help?",
      expected: "Support Request"
    },
    {
      body: "Hi there! Just wanted to say hi and let you know we're loving our RV adventures. Thanks for everything!",
      expected: "Friendly Hello"
    },
    {
      body: "Hi, I'm a pro member and I need immediate help with my RV breakdown. The tech never showed up.",
      expected: "Pro Member Support Request"
    },
    {
      body: "Hi! I run an RV repair business and would love to partner with RV Help. We can provide mobile services in the Phoenix area.",
      expected: "Pitch Received"
    },
    {
      body: "I see a charge on my credit card for $49.99 but I don't remember signing up for anything. Can you help clarify this charge?",
      expected: "Payment Issues"
    },
    {
      body: "The RV Help app keeps crashing when I try to search for techs. I'm on iOS 17. This is really frustrating.",
      expected: "Bug Report"
    },
    {
      body: "Please remove me from your mailing list. I no longer own an RV.",
      expected: "Opt-Out"
    },
    {
      body: "Hi, I'm a mobile RV tech and I can't access my dashboard. My bookings aren't showing up properly.",
      expected: "Provider Support"
    }
  ];

  const buildClassificationPrompt = (categories, version) => {
    switch(version) {
      case 1:
        return `You are an email classifier for RV Help customer support. 

TASK: Classify the email into ONE of these categories. Only classify if you are 80%+ confident.

CATEGORIES:
${Object.keys(categories).map(tag => `${tag}: ${categories[tag].description}`).join('\n')}

RULES:
- Respond with ONLY the exact category name (e.g., "Support Request")
- If confidence is below 80%, respond with "UNCERTAIN"
- Look for clear indicators in the email content
- Consider the sender's tone and intent

EMAIL TO CLASSIFY:
`;

      case 2:
        return `You are an expert email classifier for RV Help customer support.

Your job: Read the email and determine the sender's primary intent.

AVAILABLE CATEGORIES:
${Object.keys(categories).map(tag => {
  const keywords = categories[tag].keywords || [];
  return `\n"${tag}"\n  Description: ${categories[tag].description}\n  Common keywords: ${keywords.slice(0, 5).join(', ')}`;
}).join('')}

CLASSIFICATION INSTRUCTIONS:
1. What is the sender's PRIMARY need or intent?
2. Match to the most appropriate category above
3. Only classify if you're 80%+ confident
4. If unsure, respond "UNCERTAIN"

Response format: Just the category name in quotes, like "Support Request"

EMAIL CONTENT:
`;

      case 3:
        return `Classify this RV Help customer email. Choose the ONE best category.

CATEGORIES:
${Object.keys(categories).map((tag, index) => `${index + 1}. ${tag} - ${categories[tag].description}`).join('\n')}

Rules:
- Respond with exact category name only
- Must be 80%+ confident
- If uncertain, say "UNCERTAIN"

Email:
`;

      default:
        return buildClassificationPrompt(categories, 1);
    }
  };

  const buildReplyPrompt = (classification, originalEmail) => {
    const replyGuidelines = {
      'Friendly Hello': 'Respond warmly and briefly. Thank them for reaching out and wish them well on their RV adventures.',
      'What is RV Help?': 'Explain that RV Help is a free marketplace for RV owners to find pre-vetted mobile techs and inspectors to work on their RV. Mention they received this email because they either searched for a tech on the site or signed up for the newsletter. End with "Happy camping!"',
      'RV Technical Help Request': 'Clarify that RV Help doesn\'t offer service ourselves, but is a directory/marketplace that connects RV owners with mobile techs. Direct them to search for qualified techs in their area at rvhelp.com, including a link, who can help with their specific RV issue.',
      'Support Request': 'Provide helpful guidance and offer to connect them with appropriate resources. Be empathetic and solution-focused.',
      'Pro Member Support Request': 'Acknowledge their pro member status and prioritize their request. Offer immediate assistance and escalation if needed.',
      'Provider MIA': 'Apologize for the poor experience and offer to help resolve the provider issue. Provide alternative solutions.',
      'Pitch Received': 'Thank them for their interest but politely decline or redirect to appropriate channels for partnerships.',
      'Promo Materials Request': 'Provide helpful marketing materials and thank them for wanting to spread the word about RV Help.',
      'Uncategorized Buzz': 'Ask clarifying questions to better understand their needs and offer to help once you understand more.',
      'Bug Report': 'Thank them for the report, acknowledge the issue, and provide workarounds or timeline for fixes.',
      'Opt-Out': 'Process their request immediately and confirm they\'ve been removed from communications.',
      'Provider Support': 'Help them with their technical account issues and provide resources for mobile techs.',
      'Payment Issues': 'Address their billing concern promptly, offer to investigate, and provide contact information for billing support.',
      'Talent Inbox': 'Thank them for their interest and direct them to appropriate hiring channels or job applications.'
    };

    const guideline = replyGuidelines[classification] || 'Provide a helpful and professional response.';

    return `You are a customer support representative for RV Help, a platform that connects RV owners with mobile technicians.

TASK: Write a professional, helpful email reply based on the classification and original customer email.

CLASSIFICATION: ${classification}
REPLY GUIDELINES: ${guideline}

TONE AND STYLE:
- Start with "Hey {firstName}," (use placeholder since we don't have the actual name)
- Be direct and terse - get straight to the point
- Avoid unnecessary pleasantries like "Thank you for reaching out" or "We understand your concern"
- Skip filler language - every sentence should provide value
- Professional but conversational
- Action-oriented when possible
- Sign off as "Best regards, Josiah"

ORIGINAL CUSTOMER EMAIL:
"${originalEmail}"

IMPORTANT: Write a direct, terse reply. Do NOT use phrases like:
- "Thank you for reaching out"
- "We understand your concern" 
- "We are here to help"
- "I hope this helps"

Instead, get straight to the point with useful information.

Write a complete email reply:`;
  };

  const callOpenAI = async (prompt, maxTokens = 50) => {
    const payload = {
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a helpful assistant."
        },
        {
          role: "user", 
          content: prompt
        }
      ],
      max_tokens: maxTokens,
      temperature: 0.1
    };
    
    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENAI_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      
      const data = await response.json();
      
      if (data.choices && data.choices[0]) {
        return data.choices[0].message.content.trim();
      }
      
      return null;
      
    } catch (error) {
      console.error('Error calling OpenAI:', error);
      throw error;
    }
  };

  const classifyEmail = async (emailContent, categories, version) => {
    const classificationPrompt = buildClassificationPrompt(categories, version);
    return await callOpenAI(classificationPrompt + emailContent, 50);
  };

  const generateReply = async (classification, originalEmail) => {
    if (classification === 'ERROR' || classification === 'UNCERTAIN') {
      return 'Unable to generate reply due to classification uncertainty.';
    }
    
    const replyPrompt = buildReplyPrompt(classification, originalEmail);
    return await callOpenAI(replyPrompt, 300);
  };

  const processEmail = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      // Step 1: Classify the email
      const classification = await classifyEmail(testBody, EMAIL_CATEGORIES, promptVersion);
      
      // Step 2: Generate reply based on classification
      const reply = await generateReply(classification, testBody);
      
      setResult({
        body: testBody,
        classification: classification || 'UNCERTAIN',
        reply: reply || 'Unable to generate reply.',
        timestamp: new Date().toLocaleTimeString()
      });
    } catch (error) {
      setResult({
        body: testBody,
        classification: 'ERROR',
        reply: 'Error generating reply.',
        error: error.message,
        timestamp: new Date().toLocaleTimeString()
      });
    }

    setLoading(false);
  };

  const clearResults = () => {
    setResult(null);
    setTestBody('');
  };

  const loadSampleEmail = (email) => {
    setTestBody(email.body);
    setResult(null);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-6">Email Classification & Reply Generator</h1>
        
        {/* Prompt Version Selector */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Prompt Version
          </label>
          <select
            value={promptVersion}
            onChange={(e) => setPromptVersion(parseInt(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={1}>Version 1 - Basic Categories</option>
            <option value={2}>Version 2 - With Keywords</option>
            <option value={3}>Version 3 - Numbered List</option>
          </select>
        </div>

        {/* Email Input Form */}
        <div className="space-y-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Reply Content
            </label>
            <textarea
              value={testBody}
              onChange={(e) => setTestBody(e.target.value)}
              placeholder="Enter the customer's email reply content here..."
              rows={10}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="flex gap-3">
            <button
              onClick={processEmail}
              disabled={loading || !testBody.trim()}
              className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Play size={16} />
              {loading ? 'Processing...' : 'Classify & Generate Reply'}
            </button>
            
            <button
              onClick={clearResults}
              className="flex items-center gap-2 px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              <RotateCcw size={16} />
              Clear All
            </button>
          </div>
        </div>

        {/* Results */}
        {result && (
          <div className="mb-6 space-y-4">
            {/* Classification Result */}
            <div className="p-6 bg-gray-50 rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <AlertCircle size={20} />
                Classification Result
              </h3>
              <div className="space-y-3">
                <div>
                  <span className="font-medium text-gray-700">Classification:</span>
                  <span className={`ml-2 px-3 py-1 rounded-full text-sm font-medium ${
                    result.classification === 'ERROR' ? 'bg-red-100 text-red-800' :
                    result.classification === 'UNCERTAIN' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {result.classification}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Time:</span>
                  <span className="ml-2 text-gray-600">{result.timestamp}</span>
                </div>
                {result.error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <span className="font-medium text-red-800">Error:</span>
                    <span className="ml-2 text-red-700">{result.error}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Generated Reply */}
            <div className="p-6 bg-blue-50 rounded-lg border border-blue-200">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <Mail size={20} />
                Generated Reply
              </h3>
              <div className="bg-white p-4 rounded-md border">
                <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans">{result.reply}</pre>
              </div>
              <button
                onClick={() => navigator.clipboard.writeText(result.reply)}
                className="mt-3 px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                Copy Reply
              </button>
            </div>
          </div>
        )}

        {/* Sample Emails */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Sample Email Replies (Click to Load)</h3>
          <div className="grid gap-3 max-h-64 overflow-y-auto">
            {SAMPLE_EMAILS.map((email, index) => (
              <button
                key={index}
                onClick={() => loadSampleEmail(email)}
                className="text-left p-4 bg-gray-100 hover:bg-gray-200 rounded-lg border transition-colors"
              >
                <div className="text-gray-700 text-sm mb-2 line-clamp-3">{email.body}</div>
                <div className="text-xs text-blue-600 font-medium">Expected: {email.expected}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Categories Reference */}
        <div>
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Email Categories Reference</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(EMAIL_CATEGORIES).map(([tag, info]) => (
              <div key={tag} className="p-4 bg-gray-50 rounded-lg border">
                <h3 className="font-medium text-gray-800 mb-2">{tag}</h3>
                <p className="text-sm text-gray-600 mb-3">{info.description}</p>
                <div className="text-xs text-gray-500">
                  <span className="font-medium">Keywords:</span> {info.keywords.slice(0, 3).join(', ')}...
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
  };
  
  export default EmailClassifierTester;