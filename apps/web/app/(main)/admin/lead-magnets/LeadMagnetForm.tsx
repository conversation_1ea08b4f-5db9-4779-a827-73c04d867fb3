"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { upload } from "@/lib/storage";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Plus, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { z } from "zod";

const formSchema = z.object({
    title: z.string().min(1, "Title is required"),
    description: z.string().min(1, "Description is required"),
    image: z.string().optional(),
    newsletter_tags: z.array(z.string()).default([]),
    status: z.enum(["active", "inactive"], {
        required_error: "Status is required"
    })
});

type FormData = z.infer<typeof formSchema>;

interface LeadMagnetFormProps {
    mode?: "create" | "edit";
    leadMagnet?: {
        id: string;
        title: string;
        description: string;
        image?: string;
        newsletter_tags: string[];
        status: string;
    };
}

export function LeadMagnetForm({ mode = "create", leadMagnet }: LeadMagnetFormProps) {
    const router = useRouter();
    const [saving, setSaving] = useState(false);
    const [newTag, setNewTag] = useState("");

    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            title: leadMagnet?.title || "",
            description: leadMagnet?.description || "",
            image: leadMagnet?.image || "",
            newsletter_tags: leadMagnet?.newsletter_tags || [],
            status: (leadMagnet?.status as "active" | "inactive") || "active"
        }
    });

    const watchedTags = form.watch("newsletter_tags");

    const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        try {
            const fileName = `leadmagnet-${Date.now()}-${file.name}`;
            const result = await upload(file, fileName, "lead-magnets");

            if (result.error) {
                throw new Error(result.error);
            }

            if (result.url) {
                form.setValue("image", result.url);
                toast.success("Image uploaded successfully");
            }
        } catch (error) {
            console.error("Image upload failed:", error);
            toast.error("Failed to upload image");
        }
    };

    const addTag = () => {
        if (newTag.trim() && !watchedTags.includes(newTag.trim())) {
            form.setValue("newsletter_tags", [...watchedTags, newTag.trim()]);
            setNewTag("");
        }
    };

    const removeTag = (tagToRemove: string) => {
        form.setValue(
            "newsletter_tags",
            watchedTags.filter(tag => tag !== tagToRemove)
        );
    };

    const onSubmit = async (data: FormData) => {
        try {
            setSaving(true);

            const url = mode === "create"
                ? "/api/admin/lead-magnets"
                : `/api/admin/lead-magnets/${leadMagnet?.id}`;

            const method = mode === "create" ? "POST" : "PUT";

            const response = await fetch(url, {
                method,
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                toast.success(`Lead magnet ${mode === "create" ? "created" : "updated"} successfully`);
                router.push("/admin/lead-magnets");
            } else {
                const errorData = await response.json();
                toast.error(errorData.error || "Failed to save lead magnet");
            }
        } catch (error) {
            console.error("Failed to save lead magnet:", error);
            toast.error("Failed to save lead magnet");
        } finally {
            setSaving(false);
        }
    };

    return (
        <div className="max-w-2xl mx-auto">
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Basic Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <FormField
                                control={form.control}
                                name="title"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Title</FormLabel>
                                        <FormControl>
                                            <Input placeholder="e.g., Ultimate RV Maintenance Guide" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Description</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                placeholder="Describe what this lead magnet offers..."
                                                className="min-h-[100px]"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormDescription>
                                            This will be shown to users when they see the lead magnet form.
                                        </FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />



                            <FormField
                                control={form.control}
                                name="status"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Status</FormLabel>
                                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select status" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="inactive">Inactive</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormDescription>
                                            Only active lead magnets can be used in blog posts.
                                        </FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Image</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <FormField
                                control={form.control}
                                name="image"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Lead Magnet Image</FormLabel>
                                        <FormControl>
                                            <div className="space-y-4">
                                                <Input
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={handleImageUpload}
                                                    className="cursor-pointer"
                                                />
                                                {field.value && (
                                                    <div className="relative w-48 h-32">
                                                        <img
                                                            src={field.value}
                                                            alt="Lead magnet preview"
                                                            className="w-full h-full object-cover rounded-lg border"
                                                        />
                                                    </div>
                                                )}
                                            </div>
                                        </FormControl>
                                        <FormDescription>
                                            Optional image to display with the lead magnet form.
                                        </FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Newsletter Tags</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <FormField
                                control={form.control}
                                name="newsletter_tags"
                                render={() => (
                                    <FormItem>
                                        <FormLabel>Tags</FormLabel>
                                        <FormDescription>
                                            These tags will be added to subscribers who download this lead magnet.
                                        </FormDescription>
                                        <div className="space-y-3">
                                            <div className="flex gap-2">
                                                <Input
                                                    value={newTag}
                                                    onChange={(e) => setNewTag(e.target.value)}
                                                    placeholder="Enter a tag"
                                                    onKeyPress={(e) => {
                                                        if (e.key === "Enter") {
                                                            e.preventDefault();
                                                            addTag();
                                                        }
                                                    }}
                                                />
                                                <Button
                                                    type="button"
                                                    onClick={addTag}
                                                    variant="outline"
                                                    size="sm"
                                                >
                                                    <Plus className="w-4 h-4" />
                                                </Button>
                                            </div>
                                            {watchedTags.length > 0 && (
                                                <div className="flex flex-wrap gap-2">
                                                    {watchedTags.map((tag) => (
                                                        <Badge key={tag} variant="secondary" className="gap-1">
                                                            {tag}
                                                            <Button
                                                                type="button"
                                                                variant="ghost"
                                                                size="sm"
                                                                className="h-auto p-0 hover:bg-transparent"
                                                                onClick={() => removeTag(tag)}
                                                            >
                                                                <X className="w-3 h-3" />
                                                            </Button>
                                                        </Badge>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </CardContent>
                    </Card>

                    <div className="flex gap-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => router.back()}
                            disabled={saving}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" disabled={saving}>
                            {saving ? (
                                <>
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    {mode === "create" ? "Creating..." : "Updating..."}
                                </>
                            ) : (
                                mode === "create" ? "Create Lead Magnet" : "Update Lead Magnet"
                            )}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
} 