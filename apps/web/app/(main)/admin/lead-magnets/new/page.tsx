"use client";

import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import Link from "next/link";
import { LeadMagnetForm } from "../LeadMagnetForm";

export default function NewLeadMagnetPage() {
    return (
        <div className="container">
            <div className="flex flex-col gap-2 mb-6">
                <Link
                    href="/admin/lead-magnets"
                    className="inline-flex items-center text-primary hover:text-primary/80"
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Lead Magnets
                </Link>
                <h1 className="text-2xl font-bold">Create New Lead Magnet</h1>
                <p className="text-gray-600">
                    Create a new lead magnet to capture leads from your blog posts.
                </p>
            </div>

            <LeadMagnetForm mode="create" />
        </div>
    );
}