"use client";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Edit, FileText, Loader2, Plus, Trash2 } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

interface LeadMagnet {
    id: string;
    title: string;
    description: string;
    image?: string;
    newsletter_tags: string[];
    status: string;
    created_at: string;
    _count: {
        articles: number;
    };
}

const EmptyState = () => (
    <Card className="p-8 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
            No lead magnets found
        </h3>
        <p className="text-gray-500 mb-4">
            Create your first lead magnet to start capturing leads from your blog posts.
        </p>
        <Button asChild>
            <Link href="/admin/lead-magnets/new">
                <Plus className="w-4 h-4 mr-2" />
                Create Lead Magnet
            </Link>
        </Button>
    </Card>
);

const LoadingState = () => (
    <div className="flex items-center justify-center h-64">
        <div className="text-center">
            <Loader2 className="animate-spin h-8 w-8 mx-auto mb-4" />
            <p>Loading lead magnets...</p>
        </div>
    </div>
);

export default function LeadMagnetsPage() {
    const [leadMagnets, setLeadMagnets] = useState<LeadMagnet[]>([]);
    const [loading, setLoading] = useState(true);
    const [deletingId, setDeletingId] = useState<string | null>(null);

    useEffect(() => {
        fetchLeadMagnets();
    }, []);

    const fetchLeadMagnets = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/admin/lead-magnets');
            if (response.ok) {
                const data = await response.json();
                setLeadMagnets(data);
            } else {
                toast.error('Failed to fetch lead magnets');
            }
        } catch (error) {
            console.error('Failed to fetch lead magnets:', error);
            toast.error('Failed to fetch lead magnets');
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async (id: string) => {
        try {
            setDeletingId(id);
            const response = await fetch(`/api/admin/lead-magnets/${id}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                toast.success('Lead magnet deleted successfully');
                fetchLeadMagnets();
            } else {
                const data = await response.json();
                toast.error(data.error || 'Failed to delete lead magnet');
            }
        } catch (error) {
            console.error('Failed to delete lead magnet:', error);
            toast.error('Failed to delete lead magnet');
        } finally {
            setDeletingId(null);
        }
    };

    return (
        <div className="container">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-2xl font-bold">Lead Magnets</h1>
                    <p className="text-gray-600">
                        Manage your lead magnets to capture leads from blog posts
                    </p>
                </div>
                <Button asChild>
                    <Link href="/admin/lead-magnets/new">
                        <Plus className="w-4 h-4 mr-2" />
                        New Lead Magnet
                    </Link>
                </Button>
            </div>

            {loading ? (
                <LoadingState />
            ) : leadMagnets.length === 0 ? (
                <EmptyState />
            ) : (
                <div className="grid gap-4">
                    {leadMagnets.map((leadMagnet) => (
                        <Card key={leadMagnet.id}>
                            <CardHeader>
                                <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-2">
                                            <CardTitle className="text-lg">
                                                {leadMagnet.title}
                                            </CardTitle>
                                            <Badge variant={leadMagnet.status === 'active' ? 'default' : 'secondary'}>
                                                {leadMagnet.status}
                                            </Badge>
                                        </div>
                                        <p className="text-gray-600 mb-2">
                                            {leadMagnet.description}
                                        </p>
                                        <div className="flex items-center gap-4 text-sm text-gray-500">
                                            <span>Used in {leadMagnet._count.articles} article(s)</span>
                                            <span>Created: {new Date(leadMagnet.created_at).toLocaleDateString()}</span>
                                        </div>
                                    </div>
                                    <div className="flex gap-2 ml-4">

                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/admin/lead-magnets/${leadMagnet.id}/edit`}>
                                                <Edit className="w-4 h-4" />
                                            </Link>
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    disabled={deletingId === leadMagnet.id}
                                                >
                                                    {deletingId === leadMagnet.id ? (
                                                        <Loader2 className="w-4 h-4 animate-spin" />
                                                    ) : (
                                                        <Trash2 className="w-4 h-4" />
                                                    )}
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Delete Lead Magnet</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        Are you sure you want to delete &quot;{leadMagnet.title}&quot;?
                                                        {leadMagnet._count.articles > 0 && (
                                                            <span className="block mt-2 text-red-600">
                                                                This lead magnet is currently used in {leadMagnet._count.articles} article(s) and cannot be deleted.
                                                            </span>
                                                        )}
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction
                                                        onClick={() => handleDelete(leadMagnet.id)}
                                                        disabled={leadMagnet._count.articles > 0}
                                                        className="bg-red-600 hover:bg-red-700"
                                                    >
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                {leadMagnet.newsletter_tags.length > 0 && (
                                    <div>
                                        <p className="text-sm font-medium mb-2">Newsletter Tags:</p>
                                        <div className="flex flex-wrap gap-1">
                                            {leadMagnet.newsletter_tags.map((tag) => (
                                                <Badge key={tag} variant="outline" className="text-xs">
                                                    {tag}
                                                </Badge>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    );
} 