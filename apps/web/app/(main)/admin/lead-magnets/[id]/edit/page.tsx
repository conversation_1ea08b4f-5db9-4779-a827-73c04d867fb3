"use client";

import { Arrow<PERSON>ef<PERSON>, Loader2 } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { LeadMagnetForm } from "../../LeadMagnetForm";

interface LeadMagnet {
    id: string;
    title: string;
    description: string;
    url: string;
    image?: string;
    newsletter_tags: string[];
    status: string;
}

const LoadingState = () => (
    <div className="flex items-center justify-center h-64">
        <div className="text-center">
            <Loader2 className="animate-spin h-8 w-8 mx-auto mb-4" />
            <p>Loading lead magnet...</p>
        </div>
    </div>
);

export default function EditLeadMagnetPage() {
    const params = useParams();
    const id = params.id as string;
    const [leadMagnet, setLeadMagnet] = useState<LeadMagnet | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchLeadMagnet = async () => {
            try {
                setLoading(true);
                const response = await fetch(`/api/admin/lead-magnets/${id}`);
                if (response.ok) {
                    const data = await response.json();
                    setLeadMagnet(data);
                } else {
                    toast.error('Failed to fetch lead magnet');
                }
            } catch (error) {
                console.error('Failed to fetch lead magnet:', error);
                toast.error('Failed to fetch lead magnet');
            } finally {
                setLoading(false);
            }
        };

        if (id) {
            fetchLeadMagnet();
        }
    }, [id]);

    if (loading) {
        return (
            <div className="container">
                <LoadingState />
            </div>
        );
    }

    if (!leadMagnet) {
        return (
            <div className="container">
                <div className="text-center py-8">
                    <p className="text-red-600">Lead magnet not found</p>
                    <Link
                        href="/admin/lead-magnets"
                        className="inline-flex items-center text-primary hover:text-primary/80 mt-4"
                    >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Lead Magnets
                    </Link>
                </div>
            </div>
        );
    }

    return (
        <div className="container">
            <div className="flex flex-col gap-2 mb-6">
                <Link
                    href="/admin/lead-magnets"
                    className="inline-flex items-center text-primary hover:text-primary/80"
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Lead Magnets
                </Link>
                <h1 className="text-2xl font-bold">Edit Lead Magnet</h1>
                <p className="text-gray-600">
                    Update the details of &quot;{leadMagnet.title}&quot;.
                </p>
            </div>

            <LeadMagnetForm mode="edit" leadMagnet={leadMagnet} />
        </div>
    );
}