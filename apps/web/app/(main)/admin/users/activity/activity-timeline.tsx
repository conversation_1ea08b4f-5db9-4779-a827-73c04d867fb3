"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { format, formatDistanceToNow } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { Briefcase, Clock, Mail, UserCheck } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

interface Activity {
	id: string;
	type: "job" | "membership" | "transactional_email";
	title: string;
	description: string | null;
	timestamp: string;
	metadata: any;
}

interface UserActivity {
	id: string;
	email: string;
	name: string;
	created_at: string;
}

interface ActivityResponse {
	activities: Activity[];
	total: number;
	user: UserActivity;
}

const activityTypeIcons = {
	job: Briefcase,
	membership: UserCheck,
	transactional_email: Mail
};

const activityTypeColors = {
	job: "bg-green-100 text-green-800",
	membership: "bg-purple-100 text-purple-800",
	transactional_email: "bg-orange-100 text-orange-800"
};

const activityTypeLabels = {
	job: "Job",
	membership: "Membership",
	transactional_email: "Transactional Email"
};

export function ActivityTimeline({ userId }: { userId: string }) {
	const [data, setData] = useState<ActivityResponse | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [activityTypeFilter, setActivityTypeFilter] = useState<string>("all");
	const [selectedActivity, setSelectedActivity] = useState<Activity | null>(
		null
	);

	const fetchActivity = useCallback(async () => {
		setLoading(true);
		setError(null);
		try {
			const params = new URLSearchParams();
			if (activityTypeFilter !== "all") {
				params.set("action_type", activityTypeFilter);
			}

			const response = await fetch(
				`/api/admin/users/${userId}/activity?${params}`
			);
			if (!response.ok) {
				throw new Error("Failed to fetch activity data");
			}
			const result = await response.json();
			setData(result);
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
		} finally {
			setLoading(false);
		}
	}, [userId, activityTypeFilter]);

	useEffect(() => {
		fetchActivity();
	}, [fetchActivity]);

	const getActivityIcon = (type: string) => {
		const IconComponent =
			activityTypeIcons[type as keyof typeof activityTypeIcons] || Clock;
		return <IconComponent className="h-4 w-4" />;
	};

	const getActivityColor = (type: string) => {
		return (
			activityTypeColors[type as keyof typeof activityTypeColors] ||
			"bg-gray-100 text-gray-800"
		);
	};

	const getActivityLabel = (type: string) => {
		return (
			activityTypeLabels[type as keyof typeof activityTypeLabels] || "Activity"
		);
	};

	const renderActivityDetails = (activity: Activity) => {
		switch (activity.type) {
			case "job":
				return (
					<div className="space-y-2">
						<div className="flex items-center gap-2">
							<Badge variant="outline">{activity.metadata.status}</Badge>
							<Badge variant="secondary">
								{activity.metadata.quotes_count} quotes
							</Badge>
						</div>
						{activity.metadata.rv_details && (
							<div className="text-sm text-gray-600">
								RV: {activity.metadata.rv_details.year}{" "}
								{activity.metadata.rv_details.make}{" "}
								{activity.metadata.rv_details.model}
							</div>
						)}
						{activity.metadata.quotes &&
							activity.metadata.quotes.length > 0 && (
								<div className="text-sm">
									<strong>Quotes:</strong>
									<ul className="mt-1 space-y-1">
										{activity.metadata.quotes.map((quote: any) => (
											<li key={quote.id} className="flex items-center gap-2">
												<Badge variant="outline" className="text-xs">
													{quote.status}
												</Badge>
												<span>{quote.provider}</span>
											</li>
										))}
									</ul>
								</div>
							)}
					</div>
				);

			case "membership":
				return (
					<div className="space-y-2">
						<div className="flex items-center gap-2">
							<Badge
								variant={activity.metadata.is_active ? "default" : "secondary"}
							>
								{activity.metadata.is_active ? "Active" : "Cancelled"}
							</Badge>
							<Badge variant="outline">
								#{activity.metadata.member_number}
							</Badge>
						</div>
						{activity.metadata.amount_paid && (
							<div className="text-sm text-gray-600">
								Amount: ${(activity.metadata.amount_paid / 100).toFixed(2)}
							</div>
						)}
					</div>
				);

			case "transactional_email":
				return (
					<div className="space-y-2">
						<div className="flex items-center gap-2">
							<Badge variant="outline">{activity.metadata.status}</Badge>
						</div>
						{activity.metadata.html && (
							<Button
								variant="outline"
								size="sm"
								onClick={() => setSelectedActivity(activity)}
							>
								View Email Content
							</Button>
						)}
					</div>
				);

			default:
				return (
					<div className="text-sm text-gray-600">{activity.description}</div>
				);
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center py-8">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
			</div>
		);
	}

	if (!data) {
		return (
			<div className="text-center py-8">
				<p className="text-gray-600">Failed to load activity data</p>
			</div>
		);
	}

	return (
		<div className="p-6 space-y-6">
			{/* User Info */}
			<div className="bg-gray-50 p-4 rounded-lg">
				<h3 className="font-semibold text-lg">{data.user.name}</h3>
				<p className="text-gray-600">{data.user.email}</p>
				<p className="text-sm text-gray-500">
					Member since {new Date(data.user.created_at).toLocaleDateString()}
				</p>
				<div className="mt-3">
					<a
						href={`https://rv-help.mailcoach.app/email-lists/1ec1df62-5e76-43d7-9840-3a897d097e5d/subscribers?tableSearch=${encodeURIComponent(data.user.email)}`}
						target="_blank"
						rel="noopener noreferrer"
						className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 underline"
					>
						<Mail className="h-4 w-4" />
						View Marketing Email History in Mailcoach
					</a>
				</div>
			</div>

			{/* Filters */}
			<div className="flex justify-between items-center">
				<div className="flex items-center gap-4">
					<div className="w-48">
						<Select
							value={activityTypeFilter}
							onValueChange={(value) => {
								setActivityTypeFilter(value);
								// setCurrentPage(1); // Removed pagination state, so this line is removed
							}}
						>
							<SelectTrigger>
								<SelectValue placeholder="All Activity Types" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">All Activity Types</SelectItem>
								{Object.entries(activityTypeLabels).map(([value, label]) => (
									<SelectItem key={value} value={value}>
										{label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				</div>
				<div className="text-sm text-gray-600">
					{data.total} total activities
				</div>
			</div>

			{/* Timeline */}
			<div className="space-y-4">
				{data.activities.length === 0 ? (
					<div className="text-center py-8">
						<Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<p className="text-gray-600">No activities found</p>
					</div>
				) : (
					data.activities.map((activity) => (
						<div
							key={activity.id}
							className="flex gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors"
						>
							{/* Icon */}
							<div className="flex-shrink-0">
								<div
									className={`w-8 h-8 rounded-full flex items-center justify-center ${getActivityColor(activity.type)}`}
								>
									{getActivityIcon(activity.type)}
								</div>
							</div>

							{/* Content */}
							<div className="flex-1 min-w-0">
								<div className="flex items-start justify-between">
									<div className="flex-1">
										<div className="flex items-center gap-2 mb-1">
											<h4 className="font-medium">{activity.title}</h4>
											<Badge variant="outline" className="text-xs">
												{getActivityLabel(activity.type)}
											</Badge>
										</div>
										{renderActivityDetails(activity)}
									</div>
									<div className="text-sm text-gray-500 ml-4">
										{format(
											toZonedTime(
												new Date(activity.timestamp),
												"America/Chicago"
											),
											"MMM dd, yyyy 'at' h:mm a 'CDT'"
										)}
									</div>
								</div>
							</div>
						</div>
					))
				)}
			</div>

			{/* Email Content Dialog */}
			<Dialog
				open={!!selectedActivity}
				onOpenChange={() => setSelectedActivity(null)}
			>
				<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
					<DialogHeader>
						<DialogTitle>
							{selectedActivity?.metadata.subject || "Email Content"}
						</DialogTitle>
					</DialogHeader>
					{selectedActivity && (
						<div className="space-y-4">
							<div className="bg-gray-50 p-4 rounded">
								<div className="text-sm text-gray-600 mb-2">
									<strong>Subject:</strong> {selectedActivity.metadata.subject}
								</div>
								<div className="text-sm text-gray-600 mb-2">
									<strong>Status:</strong> {selectedActivity.metadata.status}
								</div>
								<div className="text-sm text-gray-600">
									<strong>Sent:</strong>{" "}
									{formatDistanceToNow(new Date(selectedActivity.timestamp), {
										addSuffix: true
									})}
								</div>
							</div>
							{selectedActivity.metadata.html && (
								<div className="border rounded p-4">
									<div className="text-sm font-medium mb-2">Email Content:</div>
									<div
										className="prose max-w-none"
										dangerouslySetInnerHTML={{
											__html: selectedActivity.metadata.html
										}}
									/>
								</div>
							)}
						</div>
					)}
				</DialogContent>
			</Dialog>
		</div>
	);
}
