"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { User } from "@rvhelp/database";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { z } from "zod";

const grantMembershipSchema = z.object({
	level: z.enum(["STANDARD", "PREMIUM"]),
	durationInMonths: z.number().min(1).max(24)
});

type FormData = z.infer<typeof grantMembershipSchema>;

interface GrantMembershipDialogProps {
	isOpen: boolean;
	onClose: () => void;
	user: User | null;
	onMembershipGranted: () => void;
}

export default function GrantMembershipDialog({
	isOpen,
	onClose,
	user,
	onMembershipGranted
}: GrantMembershipDialogProps) {
	const {
		handleSubmit,
		control,
		formState: { errors, isSubmitting },
		reset
	} = useForm<FormData>({
		resolver: zodResolver(grantMembershipSchema),
		defaultValues: {
			level: "STANDARD",
			durationInMonths: 12
		}
	});

	const onSubmit = async (data: FormData) => {
		if (!user) return;

		try {
			const response = await fetch(
				`/api/admin/users/${user.id}/grant-membership`,
				{
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify(data)
				}
			);

			if (response.ok) {
				const result = await response.json();
				toast.success(result.message);
				onMembershipGranted();
				handleClose();
			} else {
				const error = await response.json();
				throw new Error(error.error || "Failed to grant membership");
			}
		} catch (error) {
			console.error("Error granting membership:", error);
			toast.error(error.message || "Failed to grant membership");
		}
	};

	const handleClose = () => {
		reset();
		onClose();
	};

	if (!user) return null;

	return (
		<Dialog open={isOpen} onOpenChange={handleClose}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Grant Membership</DialogTitle>
					<DialogDescription>
						Grant a membership to {user.first_name} {user.last_name} (
						{user.email})
					</DialogDescription>
				</DialogHeader>
				<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
					<div className="space-y-2">
						<Label>Membership Level</Label>
						<Controller
							name="level"
							control={control}
							render={({ field }) => (
								<Select onValueChange={field.onChange} value={field.value}>
									<SelectTrigger>
										<SelectValue placeholder="Select membership level" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="STANDARD">Standard</SelectItem>
										<SelectItem value="PREMIUM">Premium</SelectItem>
									</SelectContent>
								</Select>
							)}
						/>
						{errors.level && (
							<p className="text-sm text-destructive">{errors.level.message}</p>
						)}
					</div>

					<div className="space-y-2">
						<Label>Duration</Label>
						<Controller
							name="durationInMonths"
							control={control}
							render={({ field }) => (
								<Select
									onValueChange={(value) => field.onChange(parseInt(value))}
									value={field.value.toString()}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select duration" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="1">1 Month</SelectItem>
										<SelectItem value="3">3 Months</SelectItem>
										<SelectItem value="6">6 Months</SelectItem>
										<SelectItem value="12">1 Year</SelectItem>
										<SelectItem value="24">2 Years</SelectItem>
									</SelectContent>
								</Select>
							)}
						/>
						{errors.durationInMonths && (
							<p className="text-sm text-destructive">
								{errors.durationInMonths.message}
							</p>
						)}
					</div>

					<div className="bg-amber-50 border border-amber-200 rounded-md p-3">
						<p className="text-sm text-amber-800">
							<strong>Note:</strong> This will grant the user a{" "}
							<span className="font-semibold">
								{control._formValues.level?.toLowerCase() || "standard"}
							</span>{" "}
							membership for {control._formValues.durationInMonths || 12}{" "}
							month(s). A welcome email will be sent to the user.
						</p>
					</div>

					<DialogFooter>
						<Button type="button" variant="outline" onClick={handleClose}>
							Cancel
						</Button>
						<Button type="submit" disabled={isSubmitting}>
							{isSubmitting ? "Granting..." : "Grant Membership"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
