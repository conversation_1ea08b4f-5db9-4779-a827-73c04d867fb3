import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON>alogContent,
	<PERSON>alogDes<PERSON>,
	<PERSON><PERSON><PERSON>ooter,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle
} from "@/components/ui/dialog";
import { Al<PERSON><PERSON>riangle, HelpCircle, Loader2 } from "lucide-react";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface MergeServiceRequestsDialogProps {
	isOpen: boolean;
	onClose: () => void;
	user: {
		id: string;
		email: string;
		name?: string | null;
	};
	onSuccess: () => void;
}

export default function MergeServiceRequestsDialog({
	isOpen,
	onClose,
	user,
	onSuccess
}: MergeServiceRequestsDialogProps) {
	const [isMerging, setIsMerging] = useState(false);

	const handleMerge = async () => {
		try {
			setIsMerging(true);
			const response = await fetch("/api/admin/users/merge-service-requests", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({ userId: user.id })
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Failed to merge service requests");
			}

			toast.success(data.message || "Service requests merged successfully");
			onSuccess();
			onClose();
		} catch (error) {
			console.error("Error merging service requests:", error);
			toast.error(error.message || "Failed to merge service requests");
		} finally {
			setIsMerging(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-md">
				<DialogHeader className="relative">
					<DialogTitle className="flex items-center gap-2 pr-8">
						<AlertTriangle className="h-5 w-5 text-orange-500" />
						<span>Merge Service Requests</span>
						<a
							href="/docs/merge-service-requests"
							target="_blank"
							rel="noopener noreferrer"
							className="text-gray-400 hover:text-gray-600 transition-colors"
							title="View documentation"
						>
							<HelpCircle className="h-4 w-4" />
						</a>
					</DialogTitle>
					<DialogDescription>
						This will automatically merge all service requests for{" "}
						<strong>{user.name || user.email}</strong> that were created on the
						same day, have the same category, and are for the same RV make and
						model.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					<div className="rounded-lg bg-orange-50 p-4">
						<h4 className="font-medium text-orange-800 mb-2">
							What will be merged:
						</h4>
						<ul className="text-sm text-orange-700 space-y-1">
							<li>• Service requests from the same day</li>
							<li>• Same category (e.g., RV_REPAIR, INSPECTION)</li>
							<li>• Same RV make and model</li>
						</ul>
					</div>

					<div className="rounded-lg bg-blue-50 p-4">
						<h4 className="font-medium text-blue-800 mb-2">
							What will be preserved:
						</h4>
						<ul className="text-sm text-blue-700 space-y-1">
							<li>• All quotes and quote messages</li>
							<li>• Timeline updates</li>
							<li>• Warranty request IDs</li>
							<li>• Accepted quotes (highest priority)</li>
							<li>• Most recent activity timestamps</li>
						</ul>
					</div>

					<div className="rounded-lg bg-red-50 p-4">
						<h4 className="font-medium text-red-800 mb-2">Important:</h4>
						<p className="text-sm text-red-700">
							This action cannot be undone. Duplicate service requests will be
							permanently merged into the oldest request in each group.
						</p>
					</div>
				</div>

				<DialogFooter className="flex gap-2">
					<Button variant="outline" onClick={onClose} disabled={isMerging}>
						Cancel
					</Button>
					<Button
						onClick={handleMerge}
						disabled={isMerging}
						className="bg-orange-600 hover:bg-orange-700"
					>
						{isMerging ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Merging...
							</>
						) : (
							"Merge Service Requests"
						)}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
