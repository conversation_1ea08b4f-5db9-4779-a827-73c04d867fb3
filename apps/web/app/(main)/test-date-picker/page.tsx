"use client";

import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useIsMobile } from "@/hooks/useIsMobile";
import { DateRange } from "react-day-picker";
import { useState } from "react";

export default function TestDatePickerPage() {
	const [date, setDate] = useState<DateRange | undefined>();
	const isMobile = useIsMobile();

	return (
		<div className="container mx-auto py-8 px-4 max-w-2xl">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						Mobile-Friendly Date Range Picker Test
						<Badge variant={isMobile ? "default" : "secondary"}>
							{isMobile ? "Mobile" : "Desktop"}
						</Badge>
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-6">
					<div>
						<h3 className="text-lg font-medium mb-4">Test the Date Picker</h3>
						<DatePickerWithRange
							date={date}
							setDate={setDate}
							className="w-full"
						/>
					</div>

					{date && (
						<div className="p-4 bg-muted rounded-lg">
							<h4 className="font-medium mb-2">Selected Range:</h4>
							<p>
								<strong>From:</strong> {date.from?.toLocaleDateString()}
							</p>
							{date.to && (
								<p>
									<strong>To:</strong> {date.to?.toLocaleDateString()}
								</p>
							)}
						</div>
					)}

					<div className="space-y-4">
						<h4 className="font-medium">Mobile Improvements:</h4>
						<ul className="list-disc pl-6 space-y-2 text-sm text-muted-foreground">
							<li>
								<strong>Mobile:</strong> Uses a bottom sheet modal for better touch interaction
							</li>
							<li>
								<strong>Mobile:</strong> Shows single month calendar to fit screen width
							</li>
							<li>
								<strong>Mobile:</strong> Larger touch targets (40px vs 36px)
							</li>
							<li>
								<strong>Mobile:</strong> Auto-closes when both dates are selected
							</li>
							<li>
								<strong>Desktop:</strong> Uses popover with two-month calendar view
							</li>
							<li>
								<strong>Both:</strong> Touch-optimized with touch-manipulation CSS
							</li>
						</ul>
					</div>

					<div className="p-4 bg-blue-50 rounded-lg">
						<h4 className="font-medium mb-2">Testing Instructions:</h4>
						<ol className="list-decimal pl-6 space-y-1 text-sm">
							<li>Try this page on both desktop and mobile browsers</li>
							<li>On mobile, the date picker should open as a bottom sheet</li>
							<li>On desktop, it should open as a popover</li>
							<li>Touch targets should be larger and easier to tap on mobile</li>
							<li>The calendar should auto-close on mobile after selecting both dates</li>
						</ol>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
