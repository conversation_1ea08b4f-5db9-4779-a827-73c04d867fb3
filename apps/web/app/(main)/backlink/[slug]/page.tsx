import { stateAbbreviationToUrlParam } from "@/lib/seo-urls";
import { ListingService } from "@/lib/services/listing.service";
import { redirect } from "next/navigation";

export default async function BacklinkPage({
    params
}: {
    params: { slug: string };
}) {
    const listing = await ListingService.getBySlug(params.slug);

    if (!listing) {
        redirect("/404");
    }

    // Find the primary category first
    let primaryCategory: string | null = null;
    let backupCategory: string | null = null;

    if (listing.categories) {
        // Type assertion for the categories object
        const categories = listing.categories as Record<string, {
            selected?: boolean;
            isPrimary?: boolean;
            subcategories?: Record<string, boolean>;
        }>;

        // Look for explicitly marked primary category
        for (const [categoryKey, category] of Object.entries(categories)) {
            if (category.selected && category.isPrimary) {
                primaryCategory = categoryKey;
                break;
            }
        }

        // If no primary is set, use fallback logic (rv-repair > rv-inspection > others)
        if (!primaryCategory) {
            if (categories["rv-repair"]?.selected) {
                backupCategory = "rv-repair";
            } else if (categories["rv-inspection"]?.selected) {
                backupCategory = "rv-inspection";
            }
        }
    }

    const targetCategory = primaryCategory || backupCategory;

    // Only redirect for categories that have city pages
    if (!targetCategory || !["rv-repair", "rv-inspection"].includes(targetCategory)) {
        redirect("/");
    }

    // Check if we have location data
    if (!listing.location?.city || !listing.location?.state) {
        redirect("/");
    }

    // Convert state abbreviation to full name for URL
    const stateParam = stateAbbreviationToUrlParam(listing.location.state);
    if (!stateParam) {
        redirect("/");
    }

    const cityParam = listing.location.city.toLowerCase()
        .replace(/\n+/g, ' '); // Convert newlines to spaces

    // Generate the appropriate city page URL
    const cityPageUrl = targetCategory === "rv-repair"
        ? `/mobile-rv-repair/${stateParam}/${cityParam}`
        : `/rv-inspection/${stateParam}/${cityParam}`;

    // Redirect to the city page
    redirect(cityPageUrl);
} 