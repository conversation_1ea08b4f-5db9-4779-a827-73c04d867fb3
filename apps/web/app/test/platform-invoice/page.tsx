"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { useState } from "react";

export default function PlatformInvoiceTestPage() {
    const [warrantyRequestId, setWarrantyRequestId] = useState("meepafr5uejucp70d7dbyg0v");
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState<any>(null);
    const [error, setError] = useState<string | null>(null);

    const handleGeneratePlatformInvoice = async () => {
        if (!warrantyRequestId) {
            setError("Please provide a warranty request ID");
            return;
        }

        setIsLoading(true);
        setError(null);
        setResult(null);

        try {
            const response = await fetch("/api/test/platform-invoice", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    warrantyRequestId,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || "Failed to generate platform invoice");
            }

            setResult(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : "An error occurred");
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="container mx-auto py-8 space-y-6">
            <div className="text-center">
                <h1 className="text-3xl font-bold">Platform Invoice Test</h1>
                <p className="text-muted-foreground mt-2">
                    Test platform invoice generation with custom formatting
                </p>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Generate Platform Invoice</CardTitle>
                    <CardDescription>
                        Enter the warranty request ID to generate a platform invoice. The system will automatically find the associated provider invoice.
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="warrantyRequestId">Warranty Request ID</Label>
                        <Input
                            id="warrantyRequestId"
                            value={warrantyRequestId}
                            onChange={(e) => setWarrantyRequestId(e.target.value)}
                            placeholder="Enter warranty request ID"
                        />
                        <p className="text-xs text-muted-foreground">
                            The system will automatically find the associated provider invoice
                        </p>
                    </div>

                    <div className="space-y-2">
                        <Button
                            onClick={handleGeneratePlatformInvoice}
                            disabled={isLoading}
                            className="w-full"
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Generating Platform Invoice...
                                </>
                            ) : (
                                "Generate Platform Invoice"
                            )}
                        </Button>

                        {result && (
                            <Button
                                onClick={async () => {
                                    try {
                                        const response = await fetch("/api/test/platform-invoice/pdf", {
                                            method: "POST",
                                            headers: {
                                                "Content-Type": "application/json",
                                            },
                                            body: JSON.stringify({
                                                warrantyRequestId,
                                            }),
                                        });

                                        if (response.ok) {
                                            const data = await response.json();
                                            console.log("PDF Data:", data);
                                            alert("PDF data generated! Check console for details.");
                                        } else {
                                            const error = await response.json();
                                            alert(`Error: ${error.error}`);
                                        }
                                    } catch (err) {
                                        alert(`Error generating PDF: ${err}`);
                                    }
                                }}
                                variant="outline"
                                className="w-full"
                            >
                                Generate PDF Preview
                            </Button>
                        )}
                    </div>
                </CardContent>
            </Card>

            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {result && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <CheckCircle className="h-5 w-5 text-green-500" />
                            Platform Invoice Generated Successfully
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div>
                                <Label className="font-semibold">Platform Invoice ID:</Label>
                                <p className="text-sm text-muted-foreground">{result.platformInvoice.id}</p>
                            </div>
                            <div>
                                <Label className="font-semibold">Invoice Number:</Label>
                                <p className="text-sm text-muted-foreground">{result.platformInvoice.invoice_number}</p>
                            </div>
                            <div>
                                <Label className="font-semibold">Amount:</Label>
                                <p className="text-sm text-muted-foreground">
                                    ${(result.platformInvoice.amount / 100).toFixed(2)}
                                </p>
                            </div>
                            <div>
                                <Label className="font-semibold">Status:</Label>
                                <p className="text-sm text-muted-foreground">{result.platformInvoice.status}</p>
                            </div>
                            <div>
                                <Label className="font-semibold">Customer:</Label>
                                <p className="text-sm text-muted-foreground">{result.platformInvoice.customer_name}</p>
                            </div>
                            <div>
                                <Label className="font-semibold">Notes:</Label>
                                <p className="text-sm text-muted-foreground">{result.platformInvoice.notes}</p>
                            </div>

                            {result.platformInvoice.items && result.platformInvoice.items.length > 0 && (
                                <div>
                                    <Label className="font-semibold">Line Items:</Label>
                                    <div className="mt-2 space-y-1">
                                        {result.platformInvoice.items.map((item: any, index: number) => (
                                            <div key={index} className="text-sm text-muted-foreground border-l-2 border-gray-200 pl-2">
                                                <div className="font-medium">{item.description}</div>
                                                <div className="text-xs">
                                                    Qty: {item.quantity} × ${(item.unit_price / 100).toFixed(2)} = ${(item.amount / 100).toFixed(2)}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            )}

            <Card>
                <CardHeader>
                    <CardTitle>Platform Invoice Requirements</CardTitle>
                </CardHeader>
                <CardContent>
                    <ul className="list-disc list-inside space-y-2 text-sm">
                        <li><strong>RV Help logo</strong> at the top (instead of provider logo)</li>
                        <li><strong>Customer Name</strong> and <strong>Provider Name</strong> sections (instead of Bill To/From)</li>
                        <li><strong>All original line items</strong> with provider business name in parentheses</li>
                        <li><strong>Platform fee</strong> as an additional line item</li>
                        <li><strong>RV Help branding</strong> throughout</li>
                    </ul>
                </CardContent>
            </Card>
        </div>
    );
}
