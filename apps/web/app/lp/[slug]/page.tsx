"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CheckCircle, Gift, Mail, Star } from "lucide-react";
import Image from "next/image";
import { useParams, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface Campaign {
	id: string;
	title: string;
	description: string;
	discount_type: "PERCENTAGE" | "FIXED_AMOUNT";
	discount_value: number;
	expires_at: string | null;
	page_title?: string;
	page_subtitle?: string;
	page_description?: string;
	button_text?: string;
	success_message?: string;
	background_image?: string;
	logo?: string;
}

interface FormData {
	email: string;
	first_name: string;
	last_name: string;
}

export default function MarketingCampaignLandingPage() {
	const params = useParams();
	const searchParams = useSearchParams();
	const slug = params.slug as string;

	const [campaign, setCampaign] = useState<Campaign | null>(null);
	const [loading, setLoading] = useState(true);
	const [submitting, setSubmitting] = useState(false);
	const [submitted, setSubmitted] = useState(false);
	const [isExpiredFromAPI, setIsExpiredFromAPI] = useState(false);
	const [formData, setFormData] = useState<FormData>({
		email: "",
		first_name: "",
		last_name: ""
	});

	useEffect(() => {
		fetchCampaign();
	}, [slug]);

	const fetchCampaign = async () => {
		try {
			const response = await fetch(`/api/marketing-campaigns/${slug}`);
			if (response.ok) {
				const data = await response.json();
				setCampaign(data.campaign);
			} else {
				const error = await response.json();
				// Handle expired campaign specifically
				if (error.error === "Campaign has expired") {
					setIsExpiredFromAPI(true);
					setCampaign({
						id: "expired",
						title: "Expired Campaign",
						description: "",
						discount_type: "PERCENTAGE",
						discount_value: 0,
						expires_at: new Date().toISOString() // Set to past date to trigger expired view
					});
				} else {
					toast.error(error.error || "Campaign not found");
				}
			}
		} catch (error) {
			console.error("Error fetching campaign:", error);
			toast.error("Failed to load campaign");
		} finally {
			setLoading(false);
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setSubmitting(true);

		try {
			const submitData = {
				...formData,
				utm_source: searchParams.get("utm_source") || undefined,
				utm_medium: searchParams.get("utm_medium") || undefined,
				utm_campaign: searchParams.get("utm_campaign") || undefined
			};

			const response = await fetch(`/api/marketing-campaigns/${slug}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(submitData)
			});

			if (response.ok) {
				const data = await response.json();
				setSubmitted(true);
				toast.success(data.message);
			} else {
				const error = await response.json();
				toast.error(error.error || "Failed to submit");
			}
		} catch (error) {
			console.error("Error submitting form:", error);
			toast.error("Failed to submit form");
		} finally {
			setSubmitting(false);
		}
	};

	const handleInputChange = (field: keyof FormData, value: string) => {
		setFormData((prev) => ({
			...prev,
			[field]: value
		}));
	};

	const formatDiscount = (campaign: Campaign) => {
		if (campaign.discount_type === "PERCENTAGE") {
			return `${campaign.discount_value}% off`;
		}
		return `$${campaign.discount_value} off`;
	};

	const getDiscountDescription = (campaign: Campaign) => {
		if (campaign.discount_type === "PERCENTAGE") {
			return `Save ${campaign.discount_value}% on your RV Help Pro membership`;
		}
		return `Save $${campaign.discount_value} on your RV Help Pro membership`;
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
					<p className="text-gray-600">Loading campaign...</p>
				</div>
			</div>
		);
	}

	if (!campaign) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<h1 className="text-2xl font-bold text-gray-900 mb-4">
						Campaign Not Found
					</h1>
					<p className="text-gray-600">
						The campaign you're looking for is not available.
					</p>
				</div>
			</div>
		);
	}

	const isExpired =
		isExpiredFromAPI ||
		(campaign.expires_at && new Date() > new Date(campaign.expires_at));

	if (isExpired) {
		return (
			<div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center px-6">
				<div className="text-center max-w-md">
					<div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-6">
						<svg
							className="w-8 h-8 text-red-600"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
							/>
						</svg>
					</div>
					<h1 className="text-3xl font-bold text-gray-900 mb-4">
						Campaign Expired
					</h1>
					<p className="text-gray-600 mb-6">
						This promotional campaign has ended.
						{campaign.expires_at && (
							<>
								{" "}
								The offer expired on{" "}
								<span className="font-semibold">
									{new Date(campaign.expires_at).toLocaleDateString("en-US", {
										year: "numeric",
										month: "long",
										day: "numeric"
									})}
								</span>
							</>
						)}
					</p>
					<div className="bg-white rounded-lg p-6 shadow-sm border">
						<h2 className="text-lg font-semibold text-gray-900 mb-3">
							Don't miss out on future deals!
						</h2>
						<p className="text-gray-600 mb-4">
							Sign up for our newsletter to be the first to know about new
							promotions and exclusive offers.
						</p>
						<Button
							onClick={() => (window.location.href = "/")}
							className="w-full"
						>
							Visit RV Help Home
						</Button>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div
			className="min-h-screen relative"
			style={{
				backgroundImage: campaign.background_image
					? `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url(${campaign.background_image})`
					: "linear-gradient(to bottom right, var(--primary)/10, white, var(--secondary)/10)",
				backgroundSize: "cover",
				backgroundPosition: "center",
				backgroundRepeat: "no-repeat"
			}}
		>
			{/* Background overlay for better text readability */}
			{campaign.background_image && (
				<div className="absolute inset-0 bg-black/40" />
			)}

			<div className="relative z-10 max-w-4xl mx-auto px-6 py-12">
				{/* Header */}
				<div className="text-center mb-12">
					{/* Logo display */}
					<div className="mb-20 flex items-center justify-center space-x-6">
						{/* RV Help Logo */}
						<Image
							src="/logos/logo-white.png"
							alt="RV Help"
							width={120}
							height={40}
							className="h-10 w-auto object-contain"
						/>

						{/* Partner Logo (if exists) */}
						{campaign.logo && (
							<>
								<div className="w-px h-12 bg-gray-300/50"></div>
								<Image
									src={campaign.logo}
									alt="Partner Logo"
									width={200}
									height={150}
									className="h-[70px] w-auto object-contain"
								/>
							</>
						)}
					</div>

					{/* Fallback icon if no logos */}
					{!campaign.logo && (
						<div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-full mb-6">
							<Gift className="w-8 h-8 text-white" />
						</div>
					)}

					<h1
						className={`text-4xl md:text-5xl font-bold mb-4 ${
							campaign.background_image ? "text-white" : "text-gray-900"
						}`}
					>
						{campaign.page_title || campaign.title}
					</h1>

					{campaign.page_subtitle && (
						<p
							className={`text-xl font-semibold mb-4 ${
								campaign.background_image ? "text-yellow-300" : "text-primary"
							}`}
						>
							{campaign.page_subtitle}
						</p>
					)}

					<div className="inline-flex items-center justify-center bg-green-100 text-green-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
						<Star className="w-5 h-5 mr-2" fill="currentColor" />
						{formatDiscount(campaign)}
					</div>

					<p
						className={`text-lg max-w-2xl mx-auto ${
							campaign.background_image ? "text-gray-100" : "text-gray-600"
						}`}
					>
						{campaign.page_description || getDiscountDescription(campaign)}
					</p>

					{campaign.expires_at && (
						<p
							className={`text-sm mt-4 ${
								campaign.background_image ? "text-yellow-200" : "text-red-600"
							}`}
						>
							⏰ Offer expires{" "}
							{new Date(campaign.expires_at).toLocaleDateString()}
						</p>
					)}
				</div>

				{/* Main Content */}
				<div className="grid md:grid-cols-2 gap-12 items-center">
					{/* Benefits Section */}
					<div className="space-y-6">
						<h2
							className={`text-2xl font-bold mb-6 ${
								campaign.background_image ? "text-white" : "text-gray-900"
							}`}
						>
							What you'll get with RV Help Pro:
						</h2>

						<div className="space-y-4">
							{[
								"Unlimited pre-service diagnostic calls",
								"Nationwide provider discounts",
								"Priority support access",
								"Maintenance tracking tools",
								"Premium guides and resources",
								"Virtual diagnostic sessions"
							].map((benefit, index) => (
								<div key={index} className="flex items-start space-x-3">
									<CheckCircle className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
									<span
										className={
											campaign.background_image
												? "text-gray-100"
												: "text-gray-700"
										}
									>
										{benefit}
									</span>
								</div>
							))}
						</div>

						<div
							className={`border rounded-lg p-6 ${
								campaign.background_image
									? "bg-white/90 border-white/20 backdrop-blur-sm"
									: "bg-blue-50 border-blue-200"
							}`}
						>
							<h3
								className={`font-semibold mb-2 ${
									campaign.background_image ? "text-blue-900" : "text-blue-900"
								}`}
							>
								Regular Price: $99-$199/year
							</h3>
							<p
								className={`text-sm ${
									campaign.background_image ? "text-blue-800" : "text-blue-800"
								}`}
							>
								Join thousands of RV owners who trust RV Help Pro for their
								service needs.
							</p>
						</div>
					</div>

					{/* Form Section */}
					<div>
						<Card className="shadow-xl bg-white/95 backdrop-blur-sm">
							<CardHeader className="text-center">
								<CardTitle className="text-2xl text-gray-900">
									{submitted ? "Check Your Email!" : "Get Your Discount Code"}
								</CardTitle>
								<CardDescription>
									{submitted
										? "We've sent your discount code to your email address."
										: "Enter your details below to receive your exclusive discount code"}
								</CardDescription>
							</CardHeader>
							<CardContent>
								{submitted ? (
									<div className="text-center py-8">
										<div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
											<Mail className="w-8 h-8 text-green-600" />
										</div>
										<p className="text-gray-600 mb-6">
											{campaign.success_message ||
												"Check your email for your discount code and instructions on how to use it!"}
										</p>
									</div>
								) : (
									<form onSubmit={handleSubmit} className="space-y-4">
										<div className="grid md:grid-cols-2 gap-4">
											<div>
												<Label htmlFor="first_name">First Name</Label>
												<Input
													id="first_name"
													type="text"
													value={formData.first_name}
													onChange={(e) =>
														handleInputChange("first_name", e.target.value)
													}
													placeholder="John"
												/>
											</div>
											<div>
												<Label htmlFor="last_name">Last Name</Label>
												<Input
													id="last_name"
													type="text"
													value={formData.last_name}
													onChange={(e) =>
														handleInputChange("last_name", e.target.value)
													}
													placeholder="Doe"
												/>
											</div>
										</div>

										<div>
											<Label htmlFor="email">Email Address *</Label>
											<Input
												id="email"
												type="email"
												value={formData.email}
												onChange={(e) =>
													handleInputChange("email", e.target.value)
												}
												placeholder="<EMAIL>"
												required
											/>
										</div>

										<Button
											type="submit"
											className="w-full"
											disabled={submitting || !formData.email}
										>
											{submitting
												? "Sending..."
												: campaign.button_text || "Get My Discount Code"}
										</Button>

										<p className="text-xs text-gray-500 text-center">
											By submitting this form, you agree to receive marketing
											emails from RV Help. You can unsubscribe at any time.
										</p>
									</form>
								)}
							</CardContent>
						</Card>
					</div>
				</div>

				{/* Footer */}
				<div className="text-center mt-12 pt-8 border-t border-gray-200/50">
					<p
						className={
							campaign.background_image ? "text-gray-200" : "text-gray-600"
						}
					>
						Questions? Contact us at{" "}
						<a
							href="mailto:<EMAIL>"
							className={`hover:underline ${
								campaign.background_image
									? "text-gray-300 hover:text-white"
									: "text-gray-500 hover:text-gray-700"
							}`}
						>
							<EMAIL>
						</a>
					</p>
				</div>
			</div>
		</div>
	);
}
