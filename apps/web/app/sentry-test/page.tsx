"use client";

import * as Sen<PERSON> from "@sentry/nextjs";
import { useState } from "react";

export default function SentryTestPage() {
	const [error, setError] = useState<string | null>(null);

	const testError = () => {
		try {
			throw new Error("This is a test error for Sentry");
		} catch (err) {
			Sentry.captureException(err);
			setError("Error captured and sent to Sentry!");
		}
	};

	const testMessage = () => {
		Sentry.captureMessage("This is a test message for Sentry", "info");
		setError("Message sent to Sentry!");
	};

	const testPerformance = () => {
		// Performance tracking is automatically enabled with tracesSampleRate: 1
		// This will capture page loads and navigation automatically
		setError(
			"Performance tracking is enabled! Check your Sentry dashboard for automatic traces."
		);
	};

	return (
		<div className="container mx-auto p-8">
			<h1 className="text-3xl font-bold mb-6">Sentry Integration Test</h1>

			<div className="space-y-4">
				<button
					onClick={testError}
					className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
				>
					Test Error Capture
				</button>

				<button
					onClick={testMessage}
					className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 ml-4"
				>
					Test Message Capture
				</button>

				<button
					onClick={testPerformance}
					className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 ml-4"
				>
					Test Performance Tracking
				</button>
			</div>

			{error && (
				<div className="mt-4 p-4 bg-yellow-100 border border-yellow-400 rounded">
					{error}
				</div>
			)}

			<div className="mt-8">
				<h2 className="text-xl font-semibold mb-4">What to check:</h2>
				<ul className="list-disc list-inside space-y-2">
					<li>Check your Sentry dashboard for the captured error/message</li>
					<li>Verify that performance data is being tracked</li>
					<li>Ensure source maps are working for proper stack traces</li>
				</ul>
			</div>
		</div>
	);
}
