import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, AlertTriangle, CheckCircle, Info, HelpCircle } from "lucide-react";
import Link from "next/link";

export default function MergeServiceRequestsDocs() {
	return (
		<div className="container mx-auto px-4 py-8 max-w-4xl">
			<div className="mb-6">
				<Link href="/admin/users">
					<Button variant="outline" className="mb-4">
						<ArrowLeft className="h-4 w-4 mr-2" />
						Back to Admin
					</Button>
				</Link>
				<h1 className="text-3xl font-bold mb-2">Merge Service Requests Documentation</h1>
				<p className="text-gray-600">
					Complete guide to merging service requests in the RVHelp admin system
				</p>
			</div>

			<div className="space-y-6">
				{/* Overview */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Info className="h-5 w-5 text-blue-500" />
							Overview
						</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="mb-4">
							The merge service requests functionality allows administrators to consolidate duplicate or related service requests to maintain data integrity and improve user experience.
						</p>
						<div className="grid md:grid-cols-2 gap-4">
							<div className="border rounded-lg p-4">
								<h3 className="font-semibold mb-2">Manual Merging</h3>
								<p className="text-sm text-gray-600">From Admin Leads page ("Group by Job" view)</p>
							</div>
							<div className="border rounded-lg p-4">
								<h3 className="font-semibold mb-2">Automatic Merging</h3>
								<p className="text-sm text-gray-600">From Admin Users page (bulk merge by user)</p>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Manual Merging */}
				<Card>
					<CardHeader>
						<CardTitle>Manual Merging (Admin Leads Page)</CardTitle>
						<CardDescription>
							Select specific jobs to merge from the leads page
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<h4 className="font-semibold mb-2">How to Access:</h4>
							<ol className="list-decimal list-inside space-y-1 text-sm">
								<li>Navigate to <strong>Admin → Leads</strong></li>
								<li>Switch to <strong>"Group by Job"</strong> view</li>
								<li>Select multiple jobs using checkboxes</li>
								<li>Click <strong>"Merge Selected"</strong> button</li>
							</ol>
						</div>
						
						<div>
							<h4 className="font-semibold mb-2">What Gets Merged:</h4>
							<ul className="space-y-1 text-sm">
								<li>• <strong>Quotes:</strong> All quotes from merged jobs are transferred to the primary job</li>
								<li>• <strong>Quote Messages:</strong> All associated quote messages are preserved</li>
								<li>• <strong>Timeline Updates:</strong> All timeline updates are moved to the primary job</li>
								<li>• <strong>Warranty Information:</strong> Warranty request IDs are preserved</li>
								<li>• <strong>Accepted Quotes:</strong> Accepted quotes take priority over declined ones</li>
								<li>• <strong>Transaction Data:</strong> Payment information is transferred if primary job doesn't have it</li>
							</ul>
						</div>
					</CardContent>
				</Card>

				{/* Automatic Merging */}
				<Card>
					<CardHeader>
						<CardTitle>Automatic Merging (Admin Users Page)</CardTitle>
						<CardDescription>
							Automatically merge all duplicate service requests for a user
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<h4 className="font-semibold mb-2">How to Access:</h4>
							<ol className="list-decimal list-inside space-y-1 text-sm">
								<li>Navigate to <strong>Admin → Users</strong></li>
								<li>Find the user with duplicate service requests</li>
								<li>Click <strong>"Additional Actions"</strong> dropdown</li>
								<li>Select <strong>"Merge Service Requests"</strong></li>
							</ol>
						</div>
						
						<div>
							<h4 className="font-semibold mb-2">Merge Criteria:</h4>
							<p className="text-sm mb-2">Service requests are automatically grouped and merged if they meet ALL criteria:</p>
							<div className="grid md:grid-cols-2 gap-2">
								<Badge variant="outline">Same user</Badge>
								<Badge variant="outline">Same creation date (YYYY-MM-DD)</Badge>
								<Badge variant="outline">Same service category</Badge>
								<Badge variant="outline">Same RV make and model</Badge>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Data Preservation */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<CheckCircle className="h-5 w-5 text-green-500" />
							Data Preservation Rules
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid md:grid-cols-2 gap-6">
							<div>
								<h4 className="font-semibold mb-3">Priority System:</h4>
								<ol className="space-y-2 text-sm">
									<li><strong>1. Warranty Requests:</strong> Always preserved if any job has one</li>
									<li><strong>2. Accepted Quotes:</strong> Highest priority in quote conflicts</li>
									<li><strong>3. Status Priority:</strong> COMPLETED {'>'} IN_PROGRESS {'>'} ASSIGNED {'>'} OPEN {'>'} CANCELLED {'>'} EXPIRED</li>
									<li><strong>4. Timestamps:</strong> Most recent activity dates are preserved</li>
								</ol>
							</div>
							<div>
								<h4 className="font-semibold mb-3">Conflict Resolution:</h4>
								<ul className="space-y-2 text-sm">
									<li>• <strong>Same Provider Quotes:</strong> Higher status priority wins, timestamp as tiebreaker</li>
									<li>• <strong>Warranty Requests:</strong> First one found is preserved</li>
									<li>• <strong>Accepted Quotes:</strong> First one found is preserved</li>
									<li>• <strong>Transaction IDs:</strong> First one found is preserved</li>
								</ul>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Safety Features */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<AlertTriangle className="h-5 w-5 text-orange-500" />
							Safety Features
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid md:grid-cols-3 gap-4">
							<div className="border rounded-lg p-3">
								<h4 className="font-semibold mb-2">Confirmation Required</h4>
								<ul className="text-sm space-y-1">
									<li>• Explicit confirmation for all merges</li>
									<li>• Clear warnings about irreversible actions</li>
									<li>• Detailed explanation of what will be merged</li>
								</ul>
							</div>
							<div className="border rounded-lg p-3">
								<h4 className="font-semibold mb-2">Data Integrity</h4>
								<ul className="text-sm space-y-1">
									<li>• Database transactions for atomicity</li>
									<li>• Proper foreign key constraint handling</li>
									<li>• Quote messages deleted before quotes</li>
								</ul>
							</div>
							<div className="border rounded-lg p-3">
								<h4 className="font-semibold mb-2">Error Handling</h4>
								<ul className="text-sm space-y-1">
									<li>• Comprehensive error messages</li>
									<li>• Transaction rollback on failure</li>
									<li>• User-friendly notifications</li>
								</ul>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Best Practices */}
				<Card>
					<CardHeader>
						<CardTitle>Best Practices</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid md:grid-cols-2 gap-6">
							<div>
								<h4 className="font-semibold mb-3">Before Merging:</h4>
								<ol className="space-y-2 text-sm">
									<li><strong>Review the data:</strong> Ensure jobs are actually duplicates</li>
									<li><strong>Check quotes:</strong> Verify no important quote information will be lost</li>
									<li><strong>Consider timing:</strong> Avoid merging during active quote negotiations</li>
								</ol>
							</div>
							<div>
								<h4 className="font-semibold mb-3">After Merging:</h4>
								<ol className="space-y-2 text-sm">
									<li><strong>Verify results:</strong> Check that all important data was preserved</li>
									<li><strong>Notify users:</strong> If necessary, inform users about consolidated requests</li>
									<li><strong>Monitor:</strong> Watch for any issues with the merged service request</li>
								</ol>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Troubleshooting */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<HelpCircle className="h-5 w-5 text-blue-500" />
							Troubleshooting
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div>
								<h4 className="font-semibold mb-2">Common Issues:</h4>
								<ul className="space-y-2 text-sm">
									<li>• <strong>"No service requests found":</strong> User has no jobs to merge</li>
									<li>• <strong>"Failed to merge":</strong> Database constraint violation or network error</li>
									<li>• <strong>Missing data:</strong> Check if quotes were properly transferred</li>
								</ul>
							</div>
							<div>
								<h4 className="font-semibold mb-2">Recovery Options:</h4>
								<ul className="space-y-2 text-sm">
									<li>• <strong>No automatic recovery:</strong> Merged data cannot be automatically restored</li>
									<li>• <strong>Database backup:</strong> Restore from backup if critical data was lost</li>
									<li>• <strong>Manual recreation:</strong> Recreate lost data if necessary</li>
								</ul>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
} 