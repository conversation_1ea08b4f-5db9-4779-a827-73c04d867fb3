import { EmergencyDispatchConfirmationEmail } from "@/components/email-templates/EmergencyDispatchConfirmationEmail";
import { EmergencyDispatchEmail } from "@/components/email-templates/EmergencyDispatchEmail";
import { createHandler } from "@/lib/api/baseHandler";
import { emailService } from "@/lib/services";
import { createElement } from "react";
import { z } from "zod";

const emergencyDispatchSchema = z.object({
	firstName: z.string().min(1, "First name is required"),
	lastName: z.string().min(1, "Last name is required"),
	email: z.string().email("Invalid email address"),
	phone: z.string().min(1, "Phone number is required"),
	location: z.object({
		address: z.string().min(1, "Location is required"),
		latitude: z.number(),
		longitude: z.number()
	}),
	currentLocation: z.string().optional(),
	emergencyDescription: z.string().min(10, "Please provide more detail about the emergency"),
	uploadedFiles: z.array(z.object({
		name: z.string(),
		size: z.string(),
		type: z.string(),
		url: z.string()
	})).optional()
});

export const POST = createHandler(
	async function () {
		const data = this.validatedData;

		try {
			// Generate a request ID for tracking
			const requestId = `EMR-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

			// Send notification email to support team
			await emailService.send({
				from: "RVHelp Emergency Dispatch <<EMAIL>>",
				to: "<EMAIL>",
				subject: `EMERGENCY DISPATCH: ${data.firstName} ${data.lastName} - ${data.location.address}`,
				react: createElement(EmergencyDispatchEmail, {
					requestId,
					firstName: data.firstName,
					lastName: data.lastName,
					email: data.email,
					phone: data.phone,
					location: data.location,
					currentLocation: data.currentLocation,
					emergencyDescription: data.emergencyDescription,
					uploadedFiles: data.uploadedFiles
				})
			});

			// Send confirmation email to customer
			await emailService.send({
				to: data.email,
				subject: 'Emergency Dispatch Request Submitted - RV Help',
				react: createElement(EmergencyDispatchConfirmationEmail, {
					requestId,
					firstName: data.firstName,
					location: data.location,
					emergencyDescription: data.emergencyDescription,
					phone: data.phone
				})
			});

			return this.respond({
				success: true,
				requestId: requestId,
				message: "Emergency dispatch request submitted successfully"
			});
		} catch (error) {
			console.error('Error processing emergency dispatch request:', error);
			return this.respond({
				success: false,
				error: 'Failed to process emergency dispatch request'
			}, 500);
		}
	},
	{
		validateBody: emergencyDispatchSchema,
		requireAuth: true
	}
); 