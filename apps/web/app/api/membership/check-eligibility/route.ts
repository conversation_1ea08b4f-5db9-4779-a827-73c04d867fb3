import { createHandler } from "@/lib/api/baseHandler";
import { OfferService } from "@/lib/services/offer.service";
import { z } from "zod";

const schema = z.object({
    email: z.string().email("Please enter a valid email address")
});

export const POST = createHandler(
    async function () {
        const { email } = this.validatedData;

        // Check eligibility using the simplified OfferService
        const eligibility = await OfferService.isEmailEligibleForAnnualOffer(email);

        if (!eligibility.eligible) {
            return this.respond({
                eligible: false,
                reason: eligibility.reason || "Not eligible for discount",
                userExists: !!eligibility.userId,
                userId: eligibility.userId
            });
        }

        // User is eligible for the discount
        return this.respond({
            eligible: true,
            reason: "You are eligible for a 50% discount on your first year of Pro membership!",
            userExists: true,
            userId: eligibility.userId,
            offerId: eligibility.offer?.id,
            discountInfo: {
                code: "ONETIME50",
                type: "PERCENTAGE",
                value: eligibility.offer?.discount_percentage || 50,
                description: eligibility.offer?.description || "Limited-time 50% off first year",
                source: "MEMBERSHIP_OFFER"
            }
        });
    },
    {
        validateBody: schema
    }
);