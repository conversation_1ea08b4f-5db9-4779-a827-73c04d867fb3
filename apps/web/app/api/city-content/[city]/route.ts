import prisma from "@/lib/prisma"
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
    request: NextRequest,
    { params }: { params: { city: string } }
) {
    try {
        const city = params.city;
        const { searchParams } = new URL(request.url);
        const category = searchParams.get('category'); // e.g., 'rv-repair' or 'rv-inspection'

        // Find city with case-insensitive search
        const cityContent = await prisma.cityContent.findFirst({
            where: {
                city: {
                    equals: city,
                    mode: 'insensitive'
                }
            },
            select: {
                city: true,
                state: true,
                category_content: true,
                reviews: true,
            },
        });

        if (!cityContent) {
            return NextResponse.json(null, { status: 404 });
        }

        // If a specific category is requested, return only that category's content
        if (category && cityContent.category_content) {
            const categoryData = (cityContent.category_content as any)?.[category];

            return NextResponse.json({
                city: cityContent.city,
                state: cityContent.state,
                title: categoryData?.title || null,
                content: categoryData?.content || null,
                reviews: cityContent.reviews,
                category: category
            });
        }

        // Return the full structure with category_content
        return NextResponse.json({
            city: cityContent.city,
            state: cityContent.state,
            category_content: cityContent.category_content,
            reviews: cityContent.reviews
        });
    } catch (error) {
        console.error('Error fetching city content:', error);
        return NextResponse.json({ error: 'Failed to fetch city content' }, { status: 500 });
    }
} 