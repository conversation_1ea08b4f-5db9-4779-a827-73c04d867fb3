import { createHandler } from "@/lib/api/baseHandler";
import { UserDeviceService } from "@/lib/services/user-device.service";
import { NextRequest } from "next/server";
import { z } from "zod";

const schema = z.object({
	deviceToken: z.string().min(1, "Device token is required"),
	platform: z.enum(["ios", "android"])
});

export const POST = createHandler(
	async function (request: NextRequest) {
		try {
			const device = await UserDeviceService.registerAnonymousDevice({
				deviceToken: this.validatedData.deviceToken,
				platform: this.validatedData.platform
			});

			return Response.json({
				success: true,
				device: {
					id: device.id,
					deviceToken: device.device_token,
					platform: device.platform,
					createdAt: device.created_at,
					lastUsedAt: device.last_used_at
				}
			});
		} catch (error) {
			console.error("Error registering anonymous device:", error);
			return Response.json(
				{ success: false, error: error.message },
				{ status: 500 }
			);
		}
	},
	{
		validateBody: schema
	}
);
