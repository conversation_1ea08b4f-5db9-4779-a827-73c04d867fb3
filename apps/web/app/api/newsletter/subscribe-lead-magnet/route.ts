import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";
import { FirstPromoterService } from "@/lib/services/first-promoter.service";
import { NextRequest } from "next/server";
import { z } from "zod";

const schema = z.object({
	email: z.string().email("Please enter a valid email"),
	first_name: z.string().min(1, "First name is required").optional(),
	last_name: z.string().min(1, "Last name is required").optional(),
	lead_magnet_id: z.string().min(1, "Lead magnet ID is required"),
	page_url: z.string().optional(),
	page_title: z.string().optional()
});

export const POST = createHandler(
	async function (request: NextRequest) {
		const { email, first_name, last_name, lead_magnet_id } = this.validatedData;

		try {
			// Get the lead magnet to retrieve the tags
			const leadMagnet = await prisma.leadMagnet.findUnique({
				where: { id: lead_magnet_id }
			});

			if (!leadMagnet) {
				return this.respond({ error: "Lead magnet not found" }, 404);
			}

			if (leadMagnet.status !== "active") {
				return this.respond({ error: "Lead magnet is not active" }, 400);
			}

			if (this.user) {
				// Update existing user
				await prisma.user.update({
					where: { id: this.user.id },
					data: {
						newsletter_subscribed: true
					}
				});
			}

			// Sync with Mailcoach
			try {
				const tags = [
					"source: lead magnet",
					"consumer action: downloaded lead magnet",
					...leadMagnet.newsletter_tags
				];

				await EmailNewsletterService.syncNewsletterSubscriber({
					email,
					first_name,
					last_name,
					user: this.user,
					tags
				});

				// Track lead magnet download with FirstPromoter
				FirstPromoterService.trackLeadMagnet({
					email,
					userId: this.user?.id,
					leadMagnetTitle: leadMagnet.title,
					req: request
				}).catch((error) => {
					console.error("Failed to track FirstPromoter lead magnet:", error);
					// Don't throw error as this is not critical for lead magnet subscription
				});
			} catch (error) {
				console.error("Newsletter sync error:", error);
				return this.respond(
					{ error: "Failed to sync with newsletter service" },
					500
				);
			}

			return this.respond({
				success: true,
				lead_magnet: {
					title: leadMagnet.title
				}
			});
		} catch (error) {
			console.error("Lead magnet subscription error:", error);
			return this.respond({ error: "Failed to process subscription" }, 500);
		}
	},
	{
		validateBody: schema
	}
);
