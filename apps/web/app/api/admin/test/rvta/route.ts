import prisma from "@/lib/prisma";
import { RVSGService } from "@/lib/services/rvsg.service";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    try {
        const { listingId } = await request.json();

        if (!listingId) {
            return NextResponse.json(
                { error: "Listing ID is required" },
                { status: 400 }
            );
        }

        // Find the listing by ID
        const listing = await prisma.listing.findUnique({
            where: { id: listingId },
            select: {
                id: true,
                business_name: true,
                email: true,
                rvtaa_member_id: true,
                rvtaa_technician_level: true,
                rvtaa_technician_id: true,
                nrvia_inspector_id: true,
                nrvia_inspector_level: true,
            },
        });

        if (!listing) {
            return NextResponse.json(
                { error: "Listing not found" },
                { status: 404 }
            );
        }

        if (!listing.rvtaa_member_id) {
            return NextResponse.json(
                {
                    error: "Listing does not have an RVTAA member ID",
                    listing
                },
                { status: 400 }
            );
        }

        // Call RVSG service to get member data
        const member = await RVSGService.getMember(listing.rvtaa_member_id);

        return NextResponse.json({
            success: true,
            listing,
            member,
        });

    } catch (error) {
        console.error("Error in RVSG test route:", error);

        return NextResponse.json(
            {
                error: error instanceof Error ? error.message : "An error occurred",
                details: error instanceof Error ? error.stack : undefined
            },
            { status: 500 }
        );
    }
} 