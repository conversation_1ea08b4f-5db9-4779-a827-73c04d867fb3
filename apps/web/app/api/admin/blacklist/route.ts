import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import { BlacklistService } from "@/lib/services/blacklist.service";
import { z } from "zod";

const addEntrySchema = z.object({
    type: z.enum(["EMAIL", "DOMAIN", "USER_ID", "IP_ADDRESS"]),
    value: z.string().min(1),
    reason: z.string().min(1),
    message: z.string().optional(),
    expiresAt: z.union([z.string().datetime(), z.string().min(0)]).optional()
});

async function handleGet() {
    const entries = await BlacklistService.listEntries();
    return Response.json(entries);
}

async function handlePost() {
    const data = this.validatedData;

    try {
        const entry = await BlacklistService.addEntry({
            ...data,
            createdBy: this.user.id
        });
        return Response.json(entry);
    } catch (error) {
        return Response.json(
            { error: "Failed to add entry", message: error.message },
            { status: 500 }
        );
    }
}

async function handleDelete() {
    const { id } = this.query;

    try {
        await BlacklistService.removeEntry(id);
    } catch (error) {
        return this.respond(
            { error: "Failed to remove entry", message: error.message },
            { status: 500 }
        );
    }
    return this.respond({ success: true });
}

export const GET = createHandler(handleGet, {
    requireAuth: true,
    requiredRole: "ADMIN"
});

export const POST = createHandler(handlePost, {
    requireAuth: true,
    requiredRole: "ADMIN",
    validateBody: addEntrySchema
});

export const DELETE = createHandler(handleDelete, {
    requireAuth: true,
    requiredRole: "ADMIN",
    validateQuery: z.object({ id: z.string() })
}); 