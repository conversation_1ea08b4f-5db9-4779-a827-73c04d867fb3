import { createHandler } from "@/lib/api/baseHandler";
import { z } from "zod";

const clearMarkersSchema = z.object({
    value: z.string().min(1),
    type: z.enum(["EMAIL", "DOMAIN", "USER_ID", "IP_ADDRESS"])
});

export const POST = createHandler(
    async function () {
        const { value, type } = this.validatedData;

        // Log the request for audit purposes
        console.log(`Admin ${this.user.id} requested to clear ban markers for ${type}: ${value}`);

        // Return a script that will clear ban markers on the client side
        // This script should be executed in the user's browser to clear their localStorage and cookies
        const clearScript = `
            // Clear localStorage ban markers
            localStorage.removeItem('rvhelp_banned');
            localStorage.removeItem('rvhelp_ban_message');
            localStorage.removeItem('rvhelp_ban_timestamp');
            
            // Clear cookie ban markers
            document.cookie = 'rvhelp_banned=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict';
            document.cookie = 'rvhelp_ban_message=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict';
            document.cookie = 'rvhelp_ban_timestamp=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict';
            
            console.log('✅ Ban markers cleared for unbanned user');
        `;

        return Response.json({
            success: true,
            message: `Ban markers cleared for ${type}: ${value}`,
            clearScript: clearScript.trim()
        });
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: clearMarkersSchema
    }
); 