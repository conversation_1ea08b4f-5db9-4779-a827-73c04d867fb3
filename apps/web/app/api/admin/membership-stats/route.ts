import { createHandler } from "@/lib/api/baseHandler";
import { membershipService } from "@/lib/services/membership.service";

export const GET = createHandler({
    requireAuth: true,
    requiredRole: "ADMIN",
    handler: async function (req) {
        const searchParams = new URL(req.url).searchParams;
        const startDateParam = searchParams.get('startDate');
        const endDateParam = searchParams.get('endDate');

        // Default to last 30 days if no dates provided
        const endDate = endDateParam ? new Date(endDateParam) : new Date();
        const startDate = startDateParam ? new Date(startDateParam) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        try {
            const stats = await membershipService.getMembershipStats(startDate, endDate);

            return this.respond(stats);
        } catch (error) {
            console.error('Error fetching membership stats:', error);
            return this.respond(
                { error: 'Failed to fetch membership stats' },
                500
            );
        }
    }
}); 