import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { smsService } from "@/lib/services/sms.service";
import { z } from "zod";

const querySchema = z.object({
    page: z.string().transform(Number).default("1"),
    limit: z.string().transform(Number).default("50"),
    type: z.enum(["all", "email", "sms"]).default("all")
});

async function handler(req: Request) {
    const { id } = this.params;
    const { page, limit, type } = this.validatedData;

    try {
        // Get the listing to find associated phone numbers and emails
        const listing = await prisma.listing.findUnique({
            where: { id },
            select: {
                id: true,
                business_name: true,
                first_name: true,
                last_name: true,
                phone: true,
                notification_sms: true,
                email: true,
                notification_email: true
            }
        });

        if (!listing) {
            return this.respond({ error: "Listing not found" }, 404);
        }

        // Collect all phone numbers and emails to search for
        const phoneNumbers = [
            listing.phone,
            listing.notification_sms
        ].filter(Boolean);

        const emailAddresses = [
            listing.email,
            listing.notification_email
        ].filter(Boolean);



        const notifications = [];

        // Fetch email notifications if requested
        if (type === "all" || type === "email") {
            if (emailAddresses.length > 0) {
                const emails = await prisma.emailOutbox.findMany({
                    where: {
                        to_email: {
                            in: emailAddresses
                        }
                    },
                    orderBy: {
                        send_date: "desc"
                    },
                    skip: (page - 1) * limit,
                    take: limit
                });

                // Format email notifications
                const formattedEmails = emails.map(email => {
                    let status = email.status;
                    try {
                        const response = JSON.parse(email.response);
                        if (response.accepted && Array.isArray(response.accepted)) {
                            status = response.accepted.includes(email.to_email) ? "delivered" : "failed";
                        } else if (response.data?.id) {
                            status = response.data.status || status;
                        }
                    } catch (error) {
                        // Keep original status if parsing fails
                    }

                    return {
                        id: email.id,
                        type: "email" as const,
                        to: email.to_email,
                        subject: email.subject,
                        content: email.text || email.html.substring(0, 200) + "...",
                        status,
                        timestamp: email.send_date,
                        response: email.response
                    };
                });

                notifications.push(...formattedEmails);
            }
        }

        // Fetch SMS notifications if requested
        if (type === "all" || type === "sms") {
            if (smsService.client && phoneNumbers.length > 0) {
                try {
                    // Fetch SMS messages for each phone number
                    const allSmsMessages = [];
                    for (const phoneNumber of phoneNumbers) {
                        const messages = await smsService.client.messages.list({
                            to: phoneNumber,
                            limit: Math.ceil(limit / phoneNumbers.length)
                        });
                        allSmsMessages.push(...messages);
                    }

                    const formattedSms = allSmsMessages.map(message => ({
                        id: message.sid,
                        type: "sms" as const,
                        to: message.to,
                        subject: "SMS Message",
                        content: message.body,
                        status: message.status,
                        timestamp: message.dateSent,
                        response: JSON.stringify(message)
                    }));

                    notifications.push(...formattedSms);
                } catch (error) {
                    console.error("Error fetching SMS messages:", error);
                    // Continue without SMS data if there's an error
                }
            }
        }

        // Sort all notifications by timestamp (newest first)
        notifications.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

        // Apply pagination to the combined results
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedNotifications = notifications.slice(startIndex, endIndex);

        // Get total counts for each type
        const emailCount = type === "all" || type === "email"
            ? await prisma.emailOutbox.count({
                where: {
                    to_email: {
                        in: emailAddresses
                    }
                }
            })
            : 0;

        // Get actual SMS count by fetching messages
        let smsCount = 0;
        if (type === "all" || type === "sms") {
            if (smsService.client && phoneNumbers.length > 0) {
                try {
                    // Count SMS messages for each phone number
                    for (const phoneNumber of phoneNumbers) {
                        const messages = await smsService.client.messages.list({
                            to: phoneNumber,
                            limit: 1000 // Get a large number to count all
                        });
                        smsCount += messages.length;
                    }
                } catch (error) {
                    console.error("Error counting SMS messages:", error);
                    smsCount = 0;
                }
            }
        }

        return this.respond({
            notifications: paginatedNotifications,
            total: notifications.length,
            emailCount,
            smsCount,
            listing: {
                id: listing.id,
                business_name: listing.business_name,
                first_name: listing.first_name,
                last_name: listing.last_name,
                phone: listing.phone,
                notification_sms: listing.notification_sms,
                email: listing.email,
                notification_email: listing.notification_email
            }
        });
    } catch (error) {
        console.error("Error fetching notifications:", error);
        return this.respond(
            { error: "Failed to fetch notifications" },
            500
        );
    }
}

export const GET = createHandler(handler, {
    requireAuth: true,
    requiredRole: "ADMIN",
    validateQuery: querySchema
}); 