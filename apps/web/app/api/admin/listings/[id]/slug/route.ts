import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const updateSlugSchema = z.object({
    slug: z.string()
        .min(1, "Slug is required")
        .max(255, "Slug is too long")
        .regex(/^[a-z0-9-]+$/, "Slug can only contain lowercase letters, numbers, and hyphens")
        .refine((slug) => !slug.startsWith("-") && !slug.endsWith("-"), "Slug cannot start or end with hyphens")
        .refine((slug) => !slug.includes("--"), "Slug cannot contain consecutive hyphens"),
});

export const PUT = createHandler(
    async function (req, { params, validatedData }) {
        const { id } = params;
        const { slug } = validatedData;

        try {
            // Check if the slug is already taken by another listing
            const existingListing = await prisma.listing.findFirst({
                where: {
                    slug,
                    id: { not: id } // Exclude current listing
                }
            });

            if (existingListing) {
                return this.respond({ error: "Slug is already taken by another listing" }, 400);
            }

            // Update the listing slug
            const updatedListing = await prisma.listing.update({
                where: { id },
                data: {
                    slug,
                    updated_at: new Date()
                },
                select: {
                    id: true,
                    slug: true,
                    business_name: true,
                    first_name: true,
                    last_name: true
                }
            });

            return this.respond({
                success: true,
                listing: updatedListing,
                message: "Listing slug updated successfully"
            });
        } catch (error) {
            console.error("Error updating listing slug:", error);
            return this.respond(
                { error: "Failed to update listing slug" },
                500
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: updateSlugSchema
    }
); 