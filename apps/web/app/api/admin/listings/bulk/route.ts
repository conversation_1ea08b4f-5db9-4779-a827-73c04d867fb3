import { createHandler } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { z } from "zod";

export const POST = createHandler(async function (req, { validatedData }) {
    try {
        const { listingIds } = validatedData;

        const listings = await prisma.listing.findMany({
            where: {
                id: {
                    in: listingIds
                }
            },
            select: {
                id: true,
                first_name: true,
                last_name: true,
                business_name: true
            }
        });

        return NextResponse.json(listings);
    } catch (error) {
        console.error("Error fetching listings:", error);
        return NextResponse.json(
            { error: "Failed to fetch listings" },
            { status: 500 }
        );
    }
}, {
    requireAuth: true,
    requiredRole: "ADMIN",
    validateBody: z.object({
        listingIds: z.array(z.string())
    })
}); 