import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";
import { z } from "zod";

export const GET = createHandler(async function (req, { params }) {
    try {
        const email = await prisma.dispatchEmail.findUnique({
            where: { id: params.id },
        });

        if (!email) {
            return NextResponse.json({ error: "Email not found" }, { status: 404 });
        }

        return NextResponse.json(email);
    } catch (error) {
        console.error("Error fetching dispatch email:", error);
        return NextResponse.json(
            { error: "Failed to fetch dispatch email" },
            { status: 500 }
        );
    }
}, {
    requireAuth: true,
    requiredRole: "ADMIN"
});

export const PUT = createHandler(async function (req, { validatedData, params }) {
    try {
        const { title, subject, body: emailBody } = validatedData;

        const email = await prisma.dispatchEmail.findUnique({
            where: { id: params.id },
        });

        if (!email) {
            return NextResponse.json({ error: "Email not found" }, { status: 404 });
        }

        if (email.status !== "DRAFT") {
            return NextResponse.json(
                { error: "Only draft emails can be edited" },
                { status: 400 }
            );
        }

        const updatedEmail = await prisma.dispatchEmail.update({
            where: { id: params.id },
            data: {
                title,
                subject,
                body: emailBody,
            },
        });

        return NextResponse.json(updatedEmail);
    } catch (error) {
        console.error("Error updating dispatch email:", error);
        return NextResponse.json(
            { error: "Failed to update dispatch email" },
            { status: 500 }
        );
    }
}, {
    requireAuth: true,
    requiredRole: "ADMIN",
    validateBody: z.object({
        title: z.string(),
        subject: z.string(),
        body: z.string(),
    })
});