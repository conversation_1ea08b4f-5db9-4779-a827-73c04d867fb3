import { DispatchEmail } from "@/components/email-templates/DispatchEmail";
import { createHandler } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { SearchService } from "@/lib/services/search.service";
import { NextResponse } from "next/server";
import React from "react";

export const POST = createHandler(async function (req, { params }) {
    try {
        const { id } = params;

        console.log("🚀 Starting dispatch email send for ID:", id);

        // Find the dispatch email
        const dispatchEmail = await prisma.dispatchEmail.findUnique({
            where: { id }
        });

        if (!dispatchEmail) {
            return NextResponse.json({ error: "Email not found" }, { status: 404 });
        }

        console.log("📧 Dispatch email found:", {
            title: dispatchEmail.title,
            category: dispatchEmail.category,
            campaign_url: dispatchEmail.campaign_url
        });

        // Check if email can be sent (only DRAFT emails can be sent)
        if (dispatchEmail.status !== "DRAFT") {
            return NextResponse.json(
                { error: "Only draft emails can be sent" },
                { status: 400 }
            );
        }

        // Parse targeting information from campaign_url field
        let targeting = null;
        if (dispatchEmail.campaign_url) {
            try {
                const parsed = JSON.parse(dispatchEmail.campaign_url);
                console.log("📍 Parsed campaign URL:", parsed);
                if (parsed.type === "nearest_verified_techs") {
                    targeting = {
                        address: parsed.address,
                        latitude: parsed.latitude,
                        longitude: parsed.longitude,
                        count: parsed.count || 20
                    };
                    console.log("🎯 Targeting config:", targeting);
                }
            } catch (error) {
                console.error("❌ Error parsing targeting info:", error);
            }
        }

        if (!targeting) {
            console.error("❌ No targeting information found");
            return NextResponse.json(
                { error: "No location targeting information found" },
                { status: 400 }
            );
        }

        // Use specialized SearchService method for finding nearest verified providers
        // Ensure we have a valid category - this should be enforced by the form validation
        if (!dispatchEmail.category) {
            console.error("❌ No category specified for dispatch email");
            return NextResponse.json(
                { error: "No category specified for dispatch email" },
                { status: 400 }
            );
        }

        const searchResult = await SearchService.findNearestVerifiedProviders(
            targeting.latitude,
            targeting.longitude,
            dispatchEmail.category,
            targeting.count, // count of providers to find
            500 // max radius in miles (will search progressively)
        );

        console.log("📊 SearchService specialized result:", {
            totalListings: searchResult.listings.length,
            total: searchResult.total,
            searchRadius: searchResult.searchRadius
        });

        const providers = searchResult.listings;

        console.log(
            `🔍 Found ${providers.length} providers near ${targeting.address} using SearchService`
        );

        if (providers.length === 0) {
            console.error("❌ No providers found by SearchService");
            return NextResponse.json(
                { error: `No verified providers found near ${targeting.address}` },
                { status: 400 }
            );
        }

        // Filter out providers who have opted out of dispatch emails
        const eligibleProviders = [];
        console.log("🔎 Checking eligibility for each provider...");

        for (const provider of providers) {
            console.log(`🔹 Provider ${provider.id} details:`, {
                name: `${provider.first_name} ${provider.last_name}`,
                business: provider.business_name,
                email: provider.email ? "✅ Has email" : "❌ No email",
                is_active: provider.is_active,
                status: provider.status,
                verification: provider.rv_help_verification_level,
                opted_out: provider.settings_dispatch_emails_opt_out,
                distance: provider.distance,
                eligible: provider.is_active &&
                    provider.status === "ACTIVE" &&
                    provider.email &&
                    !provider.settings_dispatch_emails_opt_out
            });

            if (provider.is_active &&
                provider.status === "ACTIVE" &&
                provider.email &&
                !provider.settings_dispatch_emails_opt_out) {
                eligibleProviders.push(provider);
                console.log(`✅ Provider ${provider.id} is ELIGIBLE`);
            } else {
                console.log(`❌ Provider ${provider.id} is NOT eligible`);
            }
        }

        console.log(
            `📈 Eligibility summary: ${eligibleProviders.length} of ${providers.length} providers are eligible for dispatch emails`
        );

        if (eligibleProviders.length === 0) {
            console.error("❌ No eligible providers after filtering");

            // Let's also do a manual check to see what verified providers exist
            console.log("🔍 Manual check: Looking for ANY verified providers in the area...");

            const manualCheck = await prisma.listing.findMany({
                where: {
                    is_active: true,
                    status: "ACTIVE",
                    rv_help_verification_level: "VERIFIED",
                    location: {
                        isNot: null
                    }
                },
                select: {
                    id: true,
                    first_name: true,
                    last_name: true,
                    business_name: true,
                    rv_help_verification_level: true,
                    email: true,
                    settings_dispatch_emails_opt_out: true,
                    location: {
                        select: {
                            latitude: true,
                            longitude: true
                        }
                    }
                },
                take: 10 // Just get first 10 for debugging
            });

            console.log(`🔍 Manual check found ${manualCheck.length} verified providers total:`,
                manualCheck.map(p => ({
                    id: p.id,
                    name: `${p.first_name} ${p.last_name}`,
                    verification: p.rv_help_verification_level,
                    hasEmail: !!p.email,
                    optedOut: p.settings_dispatch_emails_opt_out,
                    location: p.location
                }))
            );

            return NextResponse.json(
                { error: `No eligible verified providers found near ${targeting.address}` },
                { status: 400 }
            );
        }

        const recipients: any[] = [];
        let totalSent = 0;
        let totalFailed = 0;

        console.log(`📤 Starting to send emails to ${eligibleProviders.length} providers using batch send...`);

        // Prepare email options for batch send
        const emailOptions = eligibleProviders.map(provider => ({
            to: provider.email!,
            subject: dispatchEmail.subject,
            react: React.createElement(DispatchEmail, {
                title: dispatchEmail.title,
                subject: dispatchEmail.subject,
                body: dispatchEmail.body,
                providerName: `${provider.first_name}`,
                businessName: provider.business_name
            }),
            emailType: "dispatch_email"
        }));

        console.log(`📋 Prepared ${emailOptions.length} email options for batch send`);

        // Send all emails in batch
        const batchResult = await emailService.batchSend(emailOptions);

        console.log(`📊 Batch send result:`, {
            success: batchResult.success,
            resultsCount: batchResult.results.length,
            errorsCount: batchResult.errors.length,
            errors: batchResult.errors
        });

        // Process batch results and create recipient records
        for (let i = 0; i < eligibleProviders.length; i++) {
            const provider = eligibleProviders[i];
            const isSuccess = i < batchResult.results.length && !batchResult.errors.some(error =>
                error.includes(provider.email!)
            );

            recipients.push({
                listingId: provider.id,
                email: provider.email,
                sentAt: new Date().toISOString(),
                status: isSuccess ? "sent" : "failed",
                errorMessage: isSuccess ? undefined : batchResult.errors.find(error =>
                    error.includes(provider.email!)
                ) || "Unknown error",
                distance: Math.round(provider.distance)
            });

            if (isSuccess) {
                totalSent++;
                console.log(`✅ Successfully sent to ${provider.email}`);
            } else {
                totalFailed++;
                console.log(`❌ Failed to send to ${provider.email}`);
            }
        }

        // Update the dispatch email status
        await prisma.dispatchEmail.update({
            where: { id },
            data: {
                status: totalFailed === 0 ? "SENT" : "FAILED",
                sent_at: new Date(),
                recipients: recipients
            }
        });

        const furthestDistance = recipients.length > 0
            ? Math.max(...recipients.map(r => r.distance || 0))
            : 0;

        const successMessage = `Email sent to ${totalSent} verified technicians near ${targeting.address} (up to ${furthestDistance} miles away)${totalFailed > 0 ? ` (${totalFailed} failed)` : ""}`;

        console.log(`🎉 Successfully sent dispatch email "${dispatchEmail.title}" to ${totalSent} verified providers`);
        console.log(`📊 Final stats: ${totalSent} sent, ${totalFailed} failed, furthest distance: ${furthestDistance} miles`);

        return NextResponse.json({
            success: true,
            totalSent,
            totalFailed,
            totalProviders: eligibleProviders.length,
            message: successMessage,
            targeting: targeting,
            furthestDistance
        });

    } catch (error) {
        console.error("💥 Error sending dispatch email:", error);
        return NextResponse.json(
            { error: "Failed to send dispatch email" },
            { status: 500 }
        );
    }
}, {
    requireAuth: true,
    requiredRole: "ADMIN"
}); 