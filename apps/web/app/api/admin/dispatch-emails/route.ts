import { createH<PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";
import { z } from "zod";

export const GET = createHandler(async function () {
    try {
        const emails = await prisma.dispatchEmail.findMany({
            orderBy: {
                created_at: "desc",
            },
        });

        return NextResponse.json(emails);
    } catch (error) {
        console.error("Error fetching dispatch emails:", error);
        return NextResponse.json(
            { error: "Failed to fetch dispatch emails" },
            { status: 500 }
        );
    }
});

export const POST = createHandler(async function (req, { validatedData }) {
    try {
        const { title, body: emailBody, category, location } = validatedData;

        // All new emails are created as drafts
        const email = await prisma.dispatchEmail.create({
            data: {
                title,
                subject: title, // Use title as subject
                body: emailBody,
                status: "DRAFT",
                scheduled_for: null, // No more scheduling
                category: category,
                // Store location targeting info in campaign_url field
                campaign_url: location ? JSON.stringify({
                    type: "nearest_verified_techs",
                    address: location.address,
                    latitude: location.latitude,
                    longitude: location.longitude,
                    count: 20 // Target nearest 20 verified techs
                }) : null,
            },
        });

        return NextResponse.json(email);
    } catch (error) {
        console.error("Error creating dispatch email:", error);
        return NextResponse.json(
            { error: "Failed to create dispatch email" },
            { status: 500 }
        );
    }
}, {
    validateBody: z.object({
        title: z.string().min(1, "Title is required"),
        body: z.string().min(1, "Body is required"),
        category: z.string().min(1, "Category is required"),
        location: z.object({
            address: z.string(),
            latitude: z.number(),
            longitude: z.number(),
        }).optional(),
    })
});