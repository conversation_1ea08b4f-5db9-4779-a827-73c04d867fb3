import { DispatchEmail } from "@/components/email-templates/DispatchEmail";
import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import React from "react";
import { z } from "zod";

export const POST = createHandler(async function (req, { validatedData }) {
    try {
        const { title, subject, body, listingId, category } = validatedData;

        // If a specific listing ID is provided, send to that listing only
        if (listingId) {
            const listing = await prisma.listing.findUnique({
                where: { id: listingId },
                include: { locations: true }
            });

            if (!listing) {
                return Response.json(
                    { error: "Listing not found" },
                    { status: 404 }
                );
            }

            if (!listing.notification_email) {
                return Response.json(
                    { error: "Listing has no notification email" },
                    { status: 400 }
                );
            }

            if (listing.settings_dispatch_emails_opt_out) {
                return Response.json(
                    { error: "This provider has opted out of dispatch emails" },
                    { status: 400 }
                );
            }

            // Check if the listing has the selected category
            if (category && category !== "all" && listing.categories) {
                const listingCategories = listing.categories as any;
                if (!listingCategories[category]?.selected) {
                    return Response.json(
                        { error: "This provider does not offer the selected service category" },
                        { status: 400 }
                    );
                }
            }

            await emailService.send({
                to: listing.notification_email,
                subject,
                react: React.createElement(DispatchEmail, {
                    title,
                    subject,
                    body,
                    providerName: `${listing.first_name} ${listing.last_name}`,
                    businessName: listing.business_name
                }),
                emailType: "dispatch_email_test"
            });

            // Create a record of the test email for single listing
            await prisma.dispatchEmail.create({
                data: {
                    title,
                    subject,
                    body,
                    status: "SENT",
                    sent_at: new Date(),
                    scheduled_for: new Date(),
                    recipients: [{
                        listingId: listing.id,
                        email: listing.notification_email,
                        sentAt: new Date().toISOString(),
                        status: "sent"
                    }]
                }
            });

            return Response.json({
                success: true,
                message: "Test dispatch email sent successfully",
                sentTo: listing.notification_email
            });
        }

        // Otherwise, send to a sample of providers (limit to 5 for testing)
        const whereClause: any = {
            is_active: true,
            status: "ACTIVE",
            notification_email: {
                not: null
            },
            settings_dispatch_emails_opt_out: false
        };

        // Add category filtering if category is provided and not "all"
        if (category && category !== "all") {
            whereClause.categories = {
                path: [category, "selected"],
                equals: true
            };
        }

        const providers = await prisma.listing.findMany({
            where: whereClause,
            take: 5,
            include: {
                locations: true
            }
        });

        if (providers.length === 0) {
            return Response.json(
                { error: "No active providers found" },
                { status: 404 }
            );
        }

        let sentCount = 0;
        const sentTo: string[] = [];

        for (const provider of providers) {
            try {
                await emailService.send({
                    to: provider.notification_email!,
                    subject,
                    react: React.createElement(DispatchEmail, {
                        title,
                        subject,
                        body,
                        providerName: `${provider.first_name} ${provider.last_name}`,
                        businessName: provider.business_name
                    }),
                    emailType: "dispatch_email_test"
                });
                sentCount++;
                sentTo.push(provider.notification_email!);
            } catch (error) {
                console.error(`Failed to send test email to ${provider.notification_email}:`, error);
            }
        }

        // Create a record of the test email
        const testRecipients = sentTo.map(email => {
            const provider = providers.find(p => p.notification_email === email);
            return {
                listingId: provider?.id || "unknown",
                email: email,
                sentAt: new Date().toISOString(),
                status: "sent"
            };
        });

        await prisma.dispatchEmail.create({
            data: {
                title,
                subject,
                body,
                status: "SENT",
                sent_at: new Date(),
                scheduled_for: new Date(),
                recipients: testRecipients
            }
        });

        return Response.json({
            success: true,
            message: `Test dispatch email sent to ${sentCount} providers`,
            sentTo,
            totalProviders: providers.length
        });
    } catch (error) {
        console.error("Error sending test dispatch email:", error);
        return Response.json(
            { error: "Failed to send test dispatch email" },
            { status: 500 }
        );
    }
}, {
    requireAuth: true,
    requiredRole: "ADMIN",
    validateBody: z.object({
        title: z.string(),
        subject: z.string(),
        body: z.string(),
        listingId: z.string().optional(),
        category: z.string()
    })
}); 