import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { MembershipService } from "@/lib/services/membership.service";
import { z } from "zod";

const membershipService = new MembershipService();

const grantMembershipSchema = z.object({
    level: z.enum(["STANDARD", "PREMIUM"]),
    durationInMonths: z.number().min(1).max(24).default(12) // Default to 12 months, max 24 months
});

export const POST = createHandler(
    async function () {
        const { id: userId } = this.params;
        const { level, durationInMonths } = this.validatedData;

        try {
            // Verify user exists
            const user = await prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, email: true, first_name: true, last_name: true }
            });

            if (!user) {
                return this.respond({ error: "User not found" }, 404);
            }

            // Grant the membership using the membership service
            // Note: We don't pass payment details since this is an admin grant
            const result = await membershipService.createOrUpdateMembership({
                userId,
                level,
                sendWelcomeEmail: false,
                sendSlackNotification: false,
                req: this.req
            });

            // Log this action for audit purposes
            console.log(`Admin ${this.session.user.id} granted ${level} membership to user ${userId} for ${durationInMonths} months`);

            return this.respond({
                message: `Successfully granted ${level} membership to ${user.first_name} ${user.last_name}`,
                membership: result.membership,
                user: result.user
            });
        } catch (error) {
            console.error("Error granting membership:", error);
            return this.respond(
                {
                    error: "Failed to grant membership",
                    details: error.message
                },
                500
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: grantMembershipSchema
    }
);