import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

export const PUT = createHandler(
	async function () {
		const userId = this.params.id;
		const { admin_notes } = this.validatedData;

		// First check if user exists
		const user = await prisma.user.findUnique({
			where: { id: userId }
		});

		if (!user) {
			return this.respond({ error: "User not found" }, 404);
		}

		// Update only the admin_notes field
		const updatedUser = await prisma.user.update({
			where: { id: userId },
			data: { admin_notes }
		});

		return this.respond({ 
			success: true, 
			admin_notes: updatedUser.admin_notes 
		});
	},
	{
		requireAuth: true,
		requiredRole: "ADMIN",
		validateBody: z.object({
			admin_notes: z.string().nullable()
		})
	}
); 