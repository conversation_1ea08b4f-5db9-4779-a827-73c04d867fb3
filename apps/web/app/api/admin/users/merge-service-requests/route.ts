import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const mergeServiceRequestsSchema = z.object({
    userId: z.string().min(1, "User ID is required"),
});

export const POST = createHandler(
    async function () {
        try {
            const { userId } = this.validatedData;

            // Get all jobs for the user
            const userJobs = await prisma.job.findMany({
                where: { user_id: userId },
                include: {
                    quotes: true,
                    timeline_updates: true,
                    warranty_request: true,
                    accepted_quote: true,
                },
                orderBy: { created_at: "asc" }
            });

            if (userJobs.length === 0) {
                return this.respond(
                    { error: "No service requests found for this user" },
                    404
                );
            }

            // Group jobs by the merge criteria: same day, category, RV make and model
            const jobGroups = new Map<string, typeof userJobs>();

            userJobs.forEach(job => {
                // Create a key based on the merge criteria
                const dateKey = job.created_at.toISOString().split('T')[0]; // YYYY-MM-DD
                const rvKey = `${job.rv_make || 'unknown'}-${job.rv_model || 'unknown'}`;
                const groupKey = `${dateKey}-${job.category}-${rvKey}`;

                if (!jobGroups.has(groupKey)) {
                    jobGroups.set(groupKey, []);
                }
                jobGroups.get(groupKey)!.push(job);
            });

            let totalMerged = 0;

            // Process each group that has more than one job
            const groupEntries = Array.from(jobGroups.entries());
            for (const [groupKey, jobs] of groupEntries) {
                if (jobs.length <= 1) continue;

                console.log(`Merging ${jobs.length} jobs for group: ${groupKey}`);

                // Sort jobs by creation date to use the oldest as primary
                jobs.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
                const primaryJob = jobs[0];
                const jobsToMerge = jobs.slice(1);

                await prisma.$transaction(async (tx) => {
                    // Process each job to be merged
                    for (const mergingJob of jobsToMerge) {
                        // Transfer quotes with conflict resolution
                        for (const mergingQuote of mergingJob.quotes) {
                            // Check if there's already a quote from the same provider in the primary job
                            const existingQuote = await tx.quote.findFirst({
                                where: {
                                    job_id: primaryJob.id,
                                    listing_id: mergingQuote.listing_id
                                }
                            });

                            if (existingQuote) {
                                // Resolve conflict based on status priority
                                const statusPriority = {
                                    ACCEPTED: 4,
                                    IN_PROGRESS: 3,
                                    COMPLETED: 2,
                                    PENDING: 1,
                                    REJECTED: 0,
                                    CUSTOMER_REJECTED: 0,
                                    WITHDRAWN: 0,
                                    EXPIRED: 0
                                };

                                const existingPriority = statusPriority[existingQuote.status] || 0;
                                const mergingPriority = statusPriority[mergingQuote.status] || 0;

                                if (mergingPriority > existingPriority) {
                                    // The merging quote has higher priority, replace the existing one
                                    // Delete quote messages first, then the quote
                                    await tx.quoteMessage.deleteMany({
                                        where: { quote_id: existingQuote.id }
                                    });
                                    await tx.quote.delete({
                                        where: { id: existingQuote.id }
                                    });
                                    await tx.quote.update({
                                        where: { id: mergingQuote.id },
                                        data: { job_id: primaryJob.id }
                                    });
                                } else if (mergingPriority === existingPriority) {
                                    // Same priority, use activity timestamp as tiebreaker
                                    const existingQuoteActivity = existingQuote.responded_at || existingQuote.invited_at;
                                    const mergingQuoteActivity = mergingQuote.responded_at || mergingQuote.invited_at;

                                    if (mergingQuoteActivity && (!existingQuoteActivity || mergingQuoteActivity > existingQuoteActivity)) {
                                        // The merging quote is more recent, replace the existing one
                                        // Delete quote messages first, then the quote
                                        await tx.quoteMessage.deleteMany({
                                            where: { quote_id: existingQuote.id }
                                        });
                                        await tx.quote.delete({
                                            where: { id: existingQuote.id }
                                        });
                                        await tx.quote.update({
                                            where: { id: mergingQuote.id },
                                            data: { job_id: primaryJob.id }
                                        });
                                    } else {
                                        // Keep the existing quote, delete the merging one
                                        // Delete quote messages first, then the quote
                                        await tx.quoteMessage.deleteMany({
                                            where: { quote_id: mergingQuote.id }
                                        });
                                        await tx.quote.delete({
                                            where: { id: mergingQuote.id }
                                        });
                                    }
                                } else {
                                    // Keep the existing quote, delete the merging one
                                    // Delete quote messages first, then the quote
                                    await tx.quoteMessage.deleteMany({
                                        where: { quote_id: mergingQuote.id }
                                    });
                                    await tx.quote.delete({
                                        where: { id: mergingQuote.id }
                                    });
                                }
                            } else {
                                // No conflict, simply move the quote to the primary job
                                await tx.quote.update({
                                    where: { id: mergingQuote.id },
                                    data: { job_id: primaryJob.id }
                                });
                            }
                        }

                        // Transfer timeline updates
                        await tx.timelineUpdate.updateMany({
                            where: { job_id: mergingJob.id },
                            data: { job_id: primaryJob.id }
                        });

                        // Transfer warranty request if primary doesn't have one
                        if (mergingJob.warranty_request_id && !primaryJob.warranty_request_id) {
                            await tx.job.update({
                                where: { id: primaryJob.id },
                                data: { warranty_request_id: mergingJob.warranty_request_id }
                            });
                        }

                        // Transfer accepted quote if primary doesn't have one
                        if (mergingJob.accepted_quote_id && !primaryJob.accepted_quote_id) {
                            await tx.job.update({
                                where: { id: primaryJob.id },
                                data: { accepted_quote_id: mergingJob.accepted_quote_id }
                            });
                        }

                        // Transfer transaction if primary doesn't have one
                        if (mergingJob.transaction_id && !primaryJob.transaction_id) {
                            await tx.job.update({
                                where: { id: primaryJob.id },
                                data: { transaction_id: mergingJob.transaction_id }
                            });
                        }

                        // Update primary job status to highest priority
                        const statusPriority = {
                            COMPLETED: 5,
                            IN_PROGRESS: 4,
                            ASSIGNED: 3,
                            OPEN: 2,
                            CANCELLED: 1,
                            EXPIRED: 0
                        };

                        const primaryStatusPriority = statusPriority[primaryJob.status] || 0;
                        const mergingStatusPriority = statusPriority[mergingJob.status] || 0;

                        if (mergingStatusPriority > primaryStatusPriority) {
                            await tx.job.update({
                                where: { id: primaryJob.id },
                                data: { status: mergingJob.status }
                            });
                        }

                        // Preserve premium and fraud flags
                        if (mergingJob.is_premium && !primaryJob.is_premium) {
                            await tx.job.update({
                                where: { id: primaryJob.id },
                                data: { is_premium: true }
                            });
                        }

                        if (mergingJob.flagged_for_fraud && !primaryJob.flagged_for_fraud) {
                            await tx.job.update({
                                where: { id: primaryJob.id },
                                data: { flagged_for_fraud: true }
                            });
                        }

                        // Preserve the most recent timestamps
                        const updates: any = {};

                        if (mergingJob.viewed_at && (!primaryJob.viewed_at || mergingJob.viewed_at > primaryJob.viewed_at)) {
                            updates.viewed_at = mergingJob.viewed_at;
                        }

                        if (mergingJob.sent_at && (!primaryJob.sent_at || mergingJob.sent_at > primaryJob.sent_at)) {
                            updates.sent_at = mergingJob.sent_at;
                        }

                        // Preserve the most recent reminder timestamps
                        if (mergingJob.offer_reminder_sent_at && (!primaryJob.offer_reminder_sent_at || mergingJob.offer_reminder_sent_at > primaryJob.offer_reminder_sent_at)) {
                            updates.offer_reminder_sent_at = mergingJob.offer_reminder_sent_at;
                        }

                        if (mergingJob.offer_reminder_48h_sent_at && (!primaryJob.offer_reminder_48h_sent_at || mergingJob.offer_reminder_48h_sent_at > primaryJob.offer_reminder_48h_sent_at)) {
                            updates.offer_reminder_48h_sent_at = mergingJob.offer_reminder_48h_sent_at;
                        }

                        if (mergingJob.follow_up_sent_at && (!primaryJob.follow_up_sent_at || mergingJob.follow_up_sent_at > primaryJob.follow_up_sent_at)) {
                            updates.follow_up_sent_at = mergingJob.follow_up_sent_at;
                        }

                        if (mergingJob.reminder_48h_sent_at && (!primaryJob.reminder_48h_sent_at || mergingJob.reminder_48h_sent_at > primaryJob.reminder_48h_sent_at)) {
                            updates.reminder_48h_sent_at = mergingJob.reminder_48h_sent_at;
                        }

                        if (mergingJob.reminder_1w_sent_at && (!primaryJob.reminder_1w_sent_at || mergingJob.reminder_1w_sent_at > primaryJob.reminder_1w_sent_at)) {
                            updates.reminder_1w_sent_at = mergingJob.reminder_1w_sent_at;
                        }

                        if (mergingJob.expired_at && (!primaryJob.expired_at || mergingJob.expired_at > primaryJob.expired_at)) {
                            updates.expired_at = mergingJob.expired_at;
                        }

                        if (Object.keys(updates).length > 0) {
                            await tx.job.update({
                                where: { id: primaryJob.id },
                                data: updates
                            });
                        }

                        // Delete the merged job
                        await tx.job.delete({
                            where: { id: mergingJob.id }
                        });

                        totalMerged++;
                    }
                });
            }

            return this.respond({
                success: true,
                message: `Successfully merged ${totalMerged} service requests`,
                totalMerged,
                totalGroups: jobGroups.size
            });

        } catch (error) {
            console.error("Error in merge service requests:", error);
            return this.respond(
                { error: "Failed to merge service requests", details: error.message },
                500
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: mergeServiceRequestsSchema
    }
); 