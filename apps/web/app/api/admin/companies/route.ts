import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";

export const GET = createHandler(
	async function () {
		try {
			const companies = await prisma.company.findMany({
				select: {
					id: true,
					name: true,
					abbreviation: true,
					website: true,
					created_at: true,
					updated_at: true
				},
				orderBy: {
					name: "asc"
				}
			});

			return this.respond({ companies });
		} catch (error) {
			console.error("Error fetching companies:", error);
			return this.respond(
				{ error: "Failed to fetch companies", details: error.message },
				500
			);
		}
	},
	{
		requireAuth: true,
		requiredRole: "ADMIN"
	}
);
