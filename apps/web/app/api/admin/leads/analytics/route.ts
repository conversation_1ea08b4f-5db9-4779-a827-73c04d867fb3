import { createHandler } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

const querySchema = z.object({
    timeframe: z.enum(["30d", "3m", "all"]).optional().default("all")
});

export const GET = createHandler(
    async function (req, { validatedData }) {
        const { timeframe } = validatedData;

        // Calculate date filter based on timeframe
        let dateFilter = {};
        const now = new Date();

        if (timeframe === "30d") {
            const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            dateFilter = { created_at: { gte: thirtyDaysAgo } };
        } else if (timeframe === "3m") {
            const threeMonthsAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            dateFilter = { created_at: { gte: threeMonthsAgo } };
        }

        // Get total jobs count with date filter
        const totalJobs = await prisma.job.count({ where: dateFilter });

        // Get jobs with parsed location data and date filtering
        let query = `
			SELECT 
				TRIM(SPLIT_PART(location->>'address', ', ', -1)) as country,
				TRIM(SPLIT_PART(SPLIT_PART(location->>'address', ', ', -2), ' ', 1)) as state,
				TRIM(SPLIT_PART(location->>'address', ', ', -3)) as city,
				category,
				status as job_status
			FROM jobs
			WHERE location->>'address' IS NOT NULL 
			AND location->>'address' != ''
		`;

        if (timeframe === "30d") {
            query += " AND created_at >= NOW() - INTERVAL '30 days'";
        } else if (timeframe === "3m") {
            query += " AND created_at >= NOW() - INTERVAL '3 months'";
        }

        const jobsWithLocation = await prisma.$queryRawUnsafe(query) as any[];

        // Get quote response statistics
        let quotesQuery = `
            SELECT 
                j.id as job_id,
                j.category,
                COUNT(q.id) as total_quotes,
                COUNT(CASE WHEN q.status != 'PENDING' THEN 1 END) as responded_quotes,
                COUNT(CASE WHEN q.status = 'ACCEPTED' THEN 1 END) as accepted_quotes,
                COUNT(CASE WHEN q.status = 'COMPLETED' THEN 1 END) as completed_quotes
            FROM jobs j
            LEFT JOIN quotes q ON j.id = q.job_id
        `;

        if (timeframe === "30d") {
            quotesQuery += " WHERE j.created_at >= NOW() - INTERVAL '30 days'";
        } else if (timeframe === "3m") {
            quotesQuery += " WHERE j.created_at >= NOW() - INTERVAL '3 months'";
        }

        quotesQuery += " GROUP BY j.id, j.category";

        const quotesStats = await prisma.$queryRawUnsafe(quotesQuery) as any[];

        // Process cities data
        const cityCount: { [key: string]: number } = {};
        const cityStateMap: { [key: string]: string } = {};
        const stateCount: { [key: string]: number } = {};
        const categoryCount: { [key: string]: number } = {};
        const statusCount: { [key: string]: number } = {};

        jobsWithLocation.forEach((job) => {
            // Count cities with state information
            if (job.city && job.city.trim() !== '' && job.state && job.state.trim() !== '') {
                const city = job.city.trim();
                const state = job.state.trim();
                const cityKey = `${city}, ${state}`;

                cityCount[cityKey] = (cityCount[cityKey] || 0) + 1;
                cityStateMap[cityKey] = state;
            }

            // Count states
            if (job.state && job.state.trim() !== '') {
                const state = job.state.trim();
                stateCount[state] = (stateCount[state] || 0) + 1;
            }

            // Count categories
            if (job.category) {
                categoryCount[job.category] = (categoryCount[job.category] || 0) + 1;
            }

            // Count job statuses
            if (job.job_status) {
                statusCount[job.job_status] = (statusCount[job.job_status] || 0) + 1;
            }
        });

        // Calculate response metrics
        const totalQuotes = quotesStats.reduce((sum, stat) => sum + parseInt(stat.total_quotes), 0);
        const totalResponded = quotesStats.reduce((sum, stat) => sum + parseInt(stat.responded_quotes), 0);
        const totalAccepted = quotesStats.reduce((sum, stat) => sum + parseInt(stat.accepted_quotes), 0);
        const totalCompleted = quotesStats.reduce((sum, stat) => sum + parseInt(stat.completed_quotes), 0);

        const responseRate = totalQuotes > 0 ? (totalResponded / totalQuotes) * 100 : 0;
        const acceptanceRate = totalResponded > 0 ? (totalAccepted / totalResponded) * 100 : 0;
        const completionRate = totalAccepted > 0 ? (totalCompleted / totalAccepted) * 100 : 0;

        // Convert to sorted arrays with percentages
        const topCities = Object.entries(cityCount)
            .map(([city, count]) => ({
                location: city,
                count,
                percentage: (count / totalJobs) * 100
            }))
            .sort((a, b) => b.count - a.count);

        const topStates = Object.entries(stateCount)
            .map(([state, count]) => ({
                location: state,
                count,
                percentage: (count / totalJobs) * 100
            }))
            .sort((a, b) => b.count - a.count);

        const categoriesBreakdown = Object.entries(categoryCount)
            .map(([category, count]) => {
                // Clean up category names for better readability
                let cleanCategory = category;
                if (category.includes('rv-')) {
                    cleanCategory = category.replace('rv-', '').replace(/-/g, ' ');
                }
                cleanCategory = cleanCategory.charAt(0).toUpperCase() + cleanCategory.slice(1);

                return {
                    category: cleanCategory,
                    count,
                    percentage: (count / totalJobs) * 100
                };
            })
            .sort((a, b) => b.count - a.count);

        const statusBreakdown = Object.entries(statusCount)
            .map(([status, count]) => ({
                status: status.charAt(0).toUpperCase() + status.slice(1),
                count
            }))
            .sort((a, b) => b.count - a.count);

        return Response.json({
            totalJobs,
            totalQuotes,
            responseRate: Math.round(responseRate * 100) / 100,
            acceptanceRate: Math.round(acceptanceRate * 100) / 100,
            completionRate: Math.round(completionRate * 100) / 100,
            topCities,
            topStates,
            categoriesBreakdown,
            statusBreakdown
        });
    },
    {
        validateQuery: querySchema,
        requireAuth: true,
        requiredRole: "ADMIN"
    }
); 