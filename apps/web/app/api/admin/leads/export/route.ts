import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { QuoteStatus } from "@rvhelp/database";

export const GET = createHandler(
    async function (req, { session }) {
        // Execute the query to get all jobs with quotes and provider information
        const jobs = await prisma.job.findMany({
            include: {
                user: {
                    select: {
                        first_name: true,
                        last_name: true,
                        email: true,
                        phone: true
                    }
                },
                quotes: {
                    include: {
                        listing: {
                            select: {
                                business_name: true,
                                first_name: true,
                                last_name: true,
                                location: {
                                    select: {
                                        formatted_address: true,
                                        city: true,
                                        state: true,
                                        postcode: true,
                                        latitude: true,
                                        longitude: true
                                    }
                                }
                            }
                        }
                    }
                }
            },
            orderBy: {
                created_at: 'desc'
            }
        });

        // Convert to CSV format with aggregated quote data
        const csvHeaders = [
            'Job ID',
            'Customer Name',
            'Customer Email',
            'Customer Phone',
            'Category',
            'Job Status',
            'Location',
            'Message',
            'Created At',
            'Days Since Created',
            'Source',
            'RV Year',
            'RV Make',
            'RV Model',
            'RV Type',
            'Is Premium',
            'Quotes Total',
            'Quotes Invited',
            'Quotes Responded',
            'Quotes Pending',
            'Quotes Accepted',
            'Quotes Declined',
            'Has Responses',
            'Response Rate %',
            'Latest Quote Response',
            'Quote Statuses',
            'Provider Names',
            'Provider Locations'
        ];

        const csvRows: string[][] = [];

        jobs.forEach((job: any) => {
            // Parse location JSON if it exists
            let locationData = null;
            if (job.location) {
                try {
                    locationData = typeof job.location === 'string' ? JSON.parse(job.location) : job.location;
                } catch (e) {
                    locationData = null;
                }
            }

            // Calculate quote statistics
            const quotesTotal = job.quotes.length;
            const quotesInvited = job.quotes.length; // All quotes are invited
            const quotesResponded = job.quotes.filter((q: any) => q.responded_at).length;
            const quotesPending = job.quotes.filter((q: any) => q.status === QuoteStatus.PENDING).length;
            const quotesAccepted = job.quotes.filter((q: any) => q.status === QuoteStatus.ACCEPTED).length;
            const quotesDeclined = job.quotes.filter((q: any) =>
                q.status === QuoteStatus.REJECTED || q.status === QuoteStatus.WITHDRAWN
            ).length;
            const hasResponses = quotesResponded > 0;
            const responseRate = quotesTotal > 0 ? (quotesResponded / quotesTotal) * 100 : 0;

            // Get latest quote response date
            const latestQuoteResponse = job.quotes
                .filter((q: any) => q.responded_at)
                .sort((a: any, b: any) => new Date(b.responded_at).getTime() - new Date(a.responded_at).getTime())[0]?.responded_at || null;

            // Get all quote statuses
            const quoteStatuses = job.quotes.map((q: any) => q.status).join(', ');

            // Get provider names
            const providerNames = job.quotes.map((q: any) =>
                q.listing ? `${q.listing.business_name || `${q.listing.first_name} ${q.listing.last_name}`}` : 'N/A'
            ).join('; ');

            // Get provider locations
            const providerLocations = job.quotes.map((q: any) => {
                const location = q.listing?.location;
                return location ? `${location.city || ''}, ${location.state || ''}` : 'N/A';
            }).join('; ');

            // Calculate days since created
            const daysSinceCreated = Math.floor((new Date().getTime() - new Date(job.created_at).getTime()) / (1000 * 60 * 60 * 24));

            csvRows.push([
                job.id,
                `${job.first_name} ${job.last_name}`,
                job.email,
                job.phone || '',
                job.category || '',
                job.status || '',
                locationData ? `"${locationData.address || ''}"` : '',
                `"${(job.message || '').replace(/"/g, '""')}"`, // Escape quotes in message
                job.created_at?.toISOString() || '',
                daysSinceCreated.toString(),
                job.source || '',
                job.rv_year || '',
                job.rv_make || '',
                job.rv_model || '',
                job.rv_type || '',
                job.is_premium ? 'Yes' : 'No',
                quotesTotal.toString(),
                quotesInvited.toString(),
                quotesResponded.toString(),
                quotesPending.toString(),
                quotesAccepted.toString(),
                quotesDeclined.toString(),
                hasResponses ? 'Yes' : 'No',
                responseRate.toFixed(1),
                latestQuoteResponse ? new Date(latestQuoteResponse).toISOString() : '',
                `"${quoteStatuses}"`,
                `"${providerNames}"`,
                `"${providerLocations}"`
            ]);
        });

        // Create CSV content
        const csvContent = [
            csvHeaders.join(","),
            ...csvRows.map((row) => row.join(","))
        ].join("\n");

        // Return CSV file
        return new Response(csvContent, {
            headers: {
                'Content-Type': 'text/csv',
                'Content-Disposition': `attachment; filename="jobs-export-${new Date().toISOString().split('T')[0]}.csv"`
            }
        });
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN"
    }
); 
