import { PremiumUpgradeEmail } from "@/components/email-templates/jobs/customer/PremiumUpgradeEmail";
import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { z } from "zod";

const updateJobSchema = z.object({
  is_premium: z.boolean().optional(),
  admin_message: z.string().optional(),
});

export const PATCH = createHandler(
  async function (req, { params, validatedData }) {
    const { id } = params;
    const { is_premium, admin_message } = validatedData;

    // Find the job first to get customer details
    const job = await prisma.job.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            first_name: true,
            last_name: true,
            email: true
          }
        },
        quotes: {
          include: {
            listing: {
              select: {
                business_name: true,
                first_name: true,
                last_name: true,
                email: true
              }
            }
          }
        }
      }
    });

    if (!job) {
      return Response.json(
        { error: "Job not found" },
        { status: 404 }
      );
    }

    // Update the job
    const updatedJob = await prisma.job.update({
      where: { id },
      data: {
        ...(is_premium !== undefined && { is_premium })
      }
    });

    // Send email notification to customer if upgraded to premium
    if (is_premium === true) {
      try {
        await emailService.send({
          to: job.user.email,
          subject: "Your RV Service Request Has Been Upgraded to Premium",
          react: PremiumUpgradeEmail({
            customerName: job.user.first_name,
            adminMessage: admin_message || undefined,
            jobId: job.id,
            workroomUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/jobs/${job.id}`
          })
        });
      } catch (error) {
        console.error('Failed to send premium upgrade email:', error);
        // Don't fail the request if email fails
      }
    }

    return Response.json({
      success: true,
      message: is_premium ? "Job upgraded to premium successfully and customer notified" : "Job premium status updated",
      job: updatedJob
    });
  },
  {
    validateBody: updateJobSchema,
    requireAuth: true,
    requiredRole: "ADMIN"
  }
); 