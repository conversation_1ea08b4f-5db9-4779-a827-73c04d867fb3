import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const createCampaignSchema = z.object({
    title: z.string().min(1, "Title is required"),
    description: z.string().min(1, "Description is required"),
    slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
    discount_type: z.enum(["PERCENTAGE", "FIXED_AMOUNT"]),
    discount_value: z.number().min(0, "Discount value must be positive"),
    coupon_code: z.string().optional(),
    expires_at: z.string().optional(),
    page_title: z.string().optional(),
    page_subtitle: z.string().optional(),
    page_description: z.string().optional(),
    button_text: z.string().optional(),
    success_message: z.string().optional(),
    background_image: z.string().optional(),
    logo: z.string().optional(),
});

export const GET = createHandler(
    async function () {
        const campaigns = await prisma.marketingCampaign.findMany({
            orderBy: {
                created_at: "desc",
            },
        });

        return this.respond({ campaigns });
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
    }
);

export const POST = createHandler(
    async function () {
        const validatedData = this.validatedData;

        // Check if slug already exists
        const existingCampaign = await prisma.marketingCampaign.findUnique({
            where: { slug: validatedData.slug },
        });

        if (existingCampaign) {
            return this.respond(
                { error: "Campaign with this slug already exists" },
                400
            );
        }

        const campaign = await prisma.marketingCampaign.create({
            data: {
                ...validatedData,
                expires_at: validatedData.expires_at ? new Date(validatedData.expires_at) : null,
            },
        });

        return this.respond({ campaign }, 201);
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: createCampaignSchema,
    }
); 