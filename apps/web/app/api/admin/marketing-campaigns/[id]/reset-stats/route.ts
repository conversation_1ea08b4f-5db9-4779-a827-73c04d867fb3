import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const resetStatsSchema = z.object({
    confirm: z.boolean().refine(val => val === true, {
        message: "You must confirm this action"
    })
});

export const POST = createHandler(
    async function (req, { params }) {
        const { id } = params;
        const { confirm } = this.validatedData;

        if (!confirm) {
            return this.respond(
                { error: "Confirmation required to reset stats" },
                400
            );
        }

        const campaign = await prisma.marketingCampaign.findUnique({
            where: { id },
        });

        if (!campaign) {
            return this.respond({ error: "Campaign not found" }, 404);
        }

        // Reset all tracking stats
        const updatedCampaign = await prisma.marketingCampaign.update({
            where: { id },
            data: {
                views_count: 0,
                leads_count: 0,
                conversions_count: 0,
            },
        });

        return this.respond({
            campaign: updatedCampaign,
            message: "Campaign stats reset successfully"
        });
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: resetStatsSchema,
    }
); 