import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const updateCampaignSchema = z.object({
    title: z.string().min(1, "Title is required"),
    description: z.string().min(1, "Description is required"),
    slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
    discount_type: z.enum(["PERCENTAGE", "FIXED_AMOUNT"]),
    discount_value: z.number().min(0, "Discount value must be positive"),
    coupon_code: z.string().optional(),
    expires_at: z.string().optional(),
    page_title: z.string().optional(),
    page_subtitle: z.string().optional(),
    page_description: z.string().optional(),
    button_text: z.string().optional(),
    success_message: z.string().optional(),
    background_image: z.string().optional(),
    logo: z.string().optional(),
});

export const GET = createHandler(
    async function (req, { params }) {
        const { id } = params;

        const campaign = await prisma.marketingCampaign.findUnique({
            where: { id },
            include: {
                leads: {
                    orderBy: {
                        created_at: "desc",
                    },
                },
            },
        });

        if (!campaign) {
            return this.respond({ error: "Campaign not found" }, 404);
        }

        return this.respond({ campaign });
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
    }
);

export const PUT = createHandler(
    async function (req, { params }) {
        const { id } = params;
        const validatedData = this.validatedData;

        // Check if campaign exists
        const existingCampaign = await prisma.marketingCampaign.findUnique({
            where: { id },
        });

        if (!existingCampaign) {
            return this.respond({ error: "Campaign not found" }, 404);
        }

        // Check if slug already exists (excluding current campaign)
        if (validatedData.slug !== existingCampaign.slug) {
            const slugExists = await prisma.marketingCampaign.findFirst({
                where: {
                    slug: validatedData.slug,
                    NOT: { id },
                },
            });

            if (slugExists) {
                return this.respond(
                    { error: "Campaign with this slug already exists" },
                    400
                );
            }
        }

        const campaign = await prisma.marketingCampaign.update({
            where: { id },
            data: {
                ...validatedData,
                expires_at: validatedData.expires_at ? new Date(validatedData.expires_at) : null,
            },
        });

        return this.respond({ campaign });
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: updateCampaignSchema,
    }
);

export const DELETE = createHandler(
    async function (req, { params }) {
        const { id } = params;

        const campaign = await prisma.marketingCampaign.findUnique({
            where: { id },
            include: {
                leads: true,
            },
        });

        if (!campaign) {
            return this.respond({ error: "Campaign not found" }, 404);
        }

        // Check if campaign has leads
        if (campaign.leads.length > 0) {
            return this.respond(
                { error: "Cannot delete campaign with existing leads" },
                400
            );
        }

        await prisma.marketingCampaign.delete({
            where: { id },
        });

        return this.respond({ message: "Campaign deleted successfully" });
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
    }
); 