import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const toggleStatusSchema = z.object({
    status: z.enum(["DRAFT", "ACTIVE", "PAUSED", "EXPIRED"]),
});

export const POST = createHandler(
    async function (req, { params }) {
        const { id } = params;
        const { status } = this.validatedData;

        const campaign = await prisma.marketingCampaign.findUnique({
            where: { id },
        });

        if (!campaign) {
            return this.respond({ error: "Campaign not found" }, 404);
        }

        // Check if campaign is expired
        if (campaign.expires_at && new Date() > campaign.expires_at) {
            return this.respond(
                { error: "Cannot change status of expired campaign" },
                400
            );
        }

        const updatedCampaign = await prisma.marketingCampaign.update({
            where: { id },
            data: { status },
        });

        return this.respond({ campaign: updatedCampaign });
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: toggleStatusSchema,
    }
); 