import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";

export const GET = createHandler({
    requireAuth: true,
    requiredRole: "ADMIN",
    handler: async function () {
        // Get all users with paid membership levels
        const existingMembers = await prisma.user.findMany({
            where: {
                membership_level: {
                    in: ['STANDARD', 'PREMIUM']
                }
            },
            select: {
                id: true,
                email: true,
                first_name: true,
                last_name: true,
                membership_level: true,
                member_number: true,
                created_at: true,
                membership: {
                    select: {
                        id: true
                    }
                }
            },
            orderBy: {
                created_at: 'desc'
            }
        });

        // Transform data for frontend
        const membersWithMigrationStatus = existingMembers.map(member => ({
            id: member.id,
            email: member.email,
            first_name: member.first_name,
            last_name: member.last_name,
            membership_level: member.membership_level,
            member_number: member.member_number,
            created_at: member.created_at,
            hasExistingMembership: !!member.membership
        }));

        return this.respond({
            members: membersWithMigrationStatus,
            total: existingMembers.length,
            eligible: membersWithMigrationStatus.filter(m => !m.hasExistingMembership).length,
            alreadyMigrated: membersWithMigrationStatus.filter(m => m.hasExistingMembership).length
        });
    }
}); 