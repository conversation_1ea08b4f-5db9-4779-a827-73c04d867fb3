import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { membershipService } from "@/lib/services/membership.service";
import { stripe } from "@/lib/stripe";

interface MigrationResult {
    success: boolean;
    userId: string;
    email: string;
    stripeData?: {
        customerId: string;
        subscriptionId?: string;
        amountPaid?: number;
    };
    error?: string;
}

export const POST = createHandler({
    requireAuth: true,
    requiredRole: "ADMIN",
    handler: async function () {
        const results: MigrationResult[] = [];

        // Get all users with paid membership levels who don't have Membership records
        const eligibleUsers = await prisma.user.findMany({
            where: {
                membership_level: {
                    in: ['STANDARD', 'PREMIUM']
                },
                membership: null // Users without existing membership records
            },
            select: {
                id: true,
                email: true,
                first_name: true,
                last_name: true,
                membership_level: true,
                member_number: true,
                created_at: true
            }
        });

        // Process each user
        for (const user of eligibleUsers) {
            try {
                // Look up customer in Stripe by email
                const stripeCustomers = await stripe.customers.list({
                    email: user.email,
                    limit: 1
                });

                let stripeData: MigrationResult['stripeData'];
                let amountPaid = user.membership_level === 'PREMIUM' ? 9900 : 4900; // Default fallback
                let stripeSessionId: string | undefined;
                let stripeSubscriptionId: string | undefined;

                if (stripeCustomers.data.length > 0) {
                    const customer = stripeCustomers.data[0];

                    // Get active subscriptions for this customer
                    const subscriptions = await stripe.subscriptions.list({
                        customer: customer.id,
                        status: 'active',
                        limit: 1
                    });

                    if (subscriptions.data.length > 0) {
                        const subscription = subscriptions.data[0];
                        stripeSubscriptionId = subscription.id;

                        // Get amount from subscription (first price item)
                        if (subscription.items.data.length > 0) {
                            const priceItem = subscription.items.data[0];
                            amountPaid = priceItem.price.unit_amount || amountPaid;
                        }

                        // Try to find the original checkout session (optional)
                        try {
                            const sessions = await stripe.checkout.sessions.list({
                                customer: customer.id,
                                limit: 10
                            });

                            const relevantSession = sessions.data.find(session =>
                                session.subscription === subscription.id
                            );

                            if (relevantSession) {
                                stripeSessionId = relevantSession.id;
                            }
                        } catch (sessionError) {
                            // Sessions might be old or not found, continue without
                            console.log(`Could not find checkout session for ${user.email}`);
                        }
                    }

                    stripeData = {
                        customerId: customer.id,
                        subscriptionId: stripeSubscriptionId,
                        amountPaid
                    };
                } else {
                    console.log(`No Stripe customer found for ${user.email}, using defaults`);
                }

                // Create membership record
                await membershipService.createOrUpdateMembership({
                    userId: user.id,
                    level: user.membership_level as 'STANDARD' | 'PREMIUM',
                    stripeSessionId,
                    stripeSubscriptionId,
                    amountPaid,
                    currency: 'usd',
                    // Since we're migrating existing users, we won't send welcome emails or notifications
                    sendWelcomeEmail: false,
                    sendSlackNotification: false
                });

                results.push({
                    success: true,
                    userId: user.id,
                    email: user.email,
                    stripeData
                });

            } catch (error) {
                console.error(`Failed to migrate user ${user.id}:`, error);
                results.push({
                    success: false,
                    userId: user.id,
                    email: user.email,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }

        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;
        const stripeFoundCount = results.filter(r => r.success && r.stripeData?.customerId).length;

        return this.respond({
            results,
            summary: {
                total: results.length,
                successful: successCount,
                failed: failureCount,
                stripeDataFound: stripeFoundCount,
                stripeDataMissing: successCount - stripeFoundCount
            },
            message: `Migration completed: ${successCount} successful (${stripeFoundCount} with Stripe data), ${failureCount} failed`
        });
    }
}); 