import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from 'next/server';
import <PERSON> from 'papaparse';



export async function POST(request: NextRequest) {
    try {
        const formData = await request.formData();
        const file = formData.get('file') as File;

        if (!file) {
            return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
        }

        const text = await file.text();

        // Parse CSV using papaparse
        const parsed = Papa.parse(text, { header: true, skipEmptyLines: true });

        if (!parsed.data || !Array.isArray(parsed.data)) {
            return NextResponse.json({ error: 'Invalid CSV format' }, { status: 400 });
        }

        const results = [];

        for (const row of parsed.data) {
            // Check if required fields exist (accepting either Content or Description)
            const content = row.Content || row.Description;
            if (!row.City || !row.State || !content) {
                continue; // Skip rows with missing required fields
            }

            // Use provided title or generate default
            const title = row.Title || `RV Mobile Repair Services Across ${row.City}`;

            try {
                const upserted = await prisma.cityContent.upsert({
                    where: { city: row.City },
                    update: {
                        state: row.State,
                        category_content: {
                            "rv-repair": {
                                title: title,
                                content: content,
                            },
                        },
                    },
                    create: {
                        city: row.City,
                        state: row.State,
                        category_content: {
                            "rv-repair": {
                                title: title,
                                content: content,
                            },
                        },
                    },
                });

                results.push(upserted);
            } catch (error) {
                console.error(`Error upserting city ${row.City}:`, error);
                // Continue with other rows even if one fails
            }
        }

        return NextResponse.json({
            results,
            message: `Successfully processed ${results.length} cities`
        });
    } catch (error) {
        console.error('Error processing CSV upload:', error);
        return NextResponse.json({ error: 'Failed to process CSV upload' }, { status: 500 });
    }
} 