import prisma from "@/lib/prisma"
import { NextRequest, NextResponse } from 'next/server';


export async function GET() {
    try {
        const cities = await prisma.cityContent.findMany({
            select: {
                id: true,
                city: true,
                state: true,
                category_content: true,
                created_at: true,
                updated_at: true,
            },
            orderBy: { city: 'asc' },
        });
        return NextResponse.json(cities);
    } catch (error) {
        console.error('Error fetching cities:', error);
        return NextResponse.json({ error: 'Failed to fetch cities' }, { status: 500 });
    }
}

export async function POST(request: NextRequest) {
    try {
        const { cities } = await request.json();

        if (!Array.isArray(cities)) {
            return NextResponse.json({ error: 'cities must be an array' }, { status: 400 });
        }

        const results = await Promise.all(
            cities.map(async (city) => {
                // Handle new cities (those with temporary IDs)
                if (city.id.startsWith('new-')) {
                    return prisma.cityContent.create({
                        data: {
                            city: city.city,
                            state: city.state,
                            category_content: city.category_content || {},
                        },
                    });
                }

                // Handle existing cities
                return prisma.cityContent.update({
                    where: { id: city.id },
                    data: {
                        city: city.city,
                        state: city.state,
                        category_content: city.category_content || {},
                    },
                });
            })
        );

        return NextResponse.json({ updated: results });
    } catch (error) {
        console.error('Error updating cities:', error);
        return NextResponse.json({ error: 'Failed to update cities' }, { status: 500 });
    }
} 