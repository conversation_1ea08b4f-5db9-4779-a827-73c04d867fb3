import { SearchService } from '@/lib/services/search.service';
import prisma from "@/lib/prisma"
import { NextRequest, NextResponse } from 'next/server';


export async function POST(request: NextRequest) {
    try {
        // Get all cities from CityContent
        const cities = await prisma.cityContent.findMany({
            select: {
                id: true,
                city: true,
                state: true
            },
        });


        const results = [];

        for (const cityContent of cities) {
            try {
                // Use SearchService to find listings in this city, sorted by reviews
                const searchResults = await SearchService.getListingsByCityState(
                    {
                        city: cityContent.city,
                        state: cityContent.state,
                        category: 'rv-repair', // Default to rv-repair, could make this configurable
                        sortBy: 'reviews',
                        radius: '250', // 250 mile radius to ensure we get results
                        filters: {} // Add empty filters object as required by SearchParams
                    },
                    1, // page
                    20 // Get more listings to have better review selection
                );


                if (searchResults.listings.length === 0) {
                    results.push({
                        city: cityContent.city,
                        message: 'No listings found',
                        reviewsUpdated: 0
                    });
                    continue;
                }

                // Get the listing IDs from search results
                const listingIds = searchResults.listings.map((l: any) => l.id);

                // First, let's check what statuses exist
                const statusCheck = await prisma.review.findMany({
                    where: {
                        listing_id: {
                            in: listingIds // Just check first 3 listings
                        }
                    },
                    select: {
                        status: true
                    },
                    take: 10
                });

                // Get reviews for these top-rated listings
                const reviews = await prisma.review.findMany({
                    where: {
                        listing_id: {
                            in: listingIds
                        },
                        // Try without status filter first, or use 'active' instead of 'published'
                        status: 'active', // Changed from 'published' to 'active'
                        overall: {
                            gte: 4 // Only get 4+ star reviews
                        },
                        content: {
                            not: ''
                        }
                    },
                    select: {
                        id: true,
                        title: true,
                        content: true,
                        overall: true,
                        listing: {
                            select: {
                                business_name: true,
                                first_name: true,
                                last_name: true,
                                categories: true,
                                location: {
                                    select: {
                                        city: true,
                                        state: true
                                    }
                                }
                            }
                        }
                    },
                    orderBy: [
                        { overall: 'desc' },
                        { created_at: 'desc' }
                    ],
                    take: 6 // Get up to 6 reviews per city
                });



                // Format reviews for storage
                const formattedReviews = reviews.map(review => {
                    // Determine the category type from the listing
                    let categoryType = 'Repair';
                    if (review.listing.categories && typeof review.listing.categories === 'object') {
                        const categories = review.listing.categories as any;
                        if (categories['rv-inspection']?.selected) {
                            categoryType = 'Inspection';
                        } else if (categories['rv-solar']?.selected) {
                            categoryType = 'Solar';
                        }
                    }

                    // Get city and state from location
                    const city = review.listing.location?.city || cityContent.city;
                    const state = review.listing.location?.state || cityContent.state;

                    return {
                        text: review.content || review.title || '',
                        provider: review.listing.business_name || `${review.listing.first_name} ${review.listing.last_name}`,
                        title: `Mobile RV ${categoryType} Tech in ${city}, ${state}`
                    };
                });

                // Update the city content with reviews
                const updateData = {
                    reviews: formattedReviews.length > 0 ? formattedReviews : null
                };

                await prisma.cityContent.update({
                    where: { id: cityContent.id },
                    data: updateData
                });

                results.push({
                    city: cityContent.city,
                    state: cityContent.state,
                    message: 'Success',
                    reviewsUpdated: formattedReviews.length
                });

            } catch (error) {
                console.error(`Error processing city ${cityContent.city}:`, error);
                results.push({
                    city: cityContent.city,
                    message: 'Error',
                    error: error.message
                });
            }
        }

        return NextResponse.json({
            message: 'Review sync completed',
            results,
            summary: {
                total: results.length,
                successful: results.filter(r => r.message === 'Success').length,
                totalReviews: results.reduce((sum, r) => sum + (r.reviewsUpdated || 0), 0)
            }
        });

    } catch (error) {
        console.error('Error syncing reviews:', error);
        return NextResponse.json(
            { error: 'Failed to sync reviews' },
            { status: 500 }
        );
    }
} 