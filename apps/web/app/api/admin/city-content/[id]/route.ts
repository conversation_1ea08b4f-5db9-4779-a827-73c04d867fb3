import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from 'next/server';


export async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const body = await request.json();
        const { category, title, content } = body;

        if (!category || !['rv-repair', 'rv-inspection'].includes(category)) {
            return NextResponse.json(
                { error: 'Invalid category. Must be rv-repair or rv-inspection' },
                { status: 400 }
            );
        }

        // Get current city content
        const currentCity = await prisma.cityContent.findUnique({
            where: { id: params.id },
            select: { category_content: true }
        });

        if (!currentCity) {
            return NextResponse.json(
                { error: 'City not found' },
                { status: 404 }
            );
        }

        // Parse current category_content or initialize as empty object
        const currentCategoryContent = (currentCity.category_content as any) || {};

        // Update the specific category content
        const updatedCategoryContent = {
            ...currentCategoryContent,
            [category]: {
                title: title || '',
                content: content || ''
            }
        };

        // Update the database
        const updatedCity = await prisma.cityContent.update({
            where: { id: params.id },
            data: {
                category_content: updatedCategoryContent,
                updated_at: new Date()
            }
        });

        return NextResponse.json({
            success: true,
            city: updatedCity
        });

    } catch (error) {
        console.error('Error updating city content:', error);
        return NextResponse.json(
            { error: 'Failed to update city content' },
            { status: 500 }
        );
    }
} 