import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";

export const POST = createHandler(
    async function () {
        // Check if user is currently impersonating
        if (!this.user?.isImpersonating || !this.user?.originalAdminId) {
            return NextResponse.json(
                { error: "Not currently impersonating" },
                { status: 400 }
            );
        }

        try {
            // Get the original admin user
            const adminUser = await prisma.user.findUnique({
                where: { id: this.user.originalAdminId }
            });

            if (!adminUser) {
                return NextResponse.json(
                    { error: "Original admin user not found" },
                    { status: 404 }
                );
            }

            // Delete any existing impersonation tokens for this session
            await prisma.verificationToken.deleteMany({
                where: {
                    admin_id: this.user.originalAdminId,
                    type: "impersonation"
                }
            });

            // Create a special token for admin to sign back in
            const returnToken = await prisma.verificationToken.create({
                data: {
                    token: `admin_return_${Math.random().toString(36).substring(2)}`,
                    expires: new Date(Date.now() + 1000 * 60 * 5), // 5 minutes
                    user_id: adminUser.id,
                    type: "admin_return"
                }
            });

            // Return the admin user info and return token
            return NextResponse.json({
                success: true,
                adminUser: {
                    id: adminUser.id,
                    email: adminUser.email,
                    role: adminUser.role
                },
                returnToken: returnToken.token
            });
        } catch (error) {
            console.error("Error stopping impersonation:", error);
            return NextResponse.json(
                { error: "Failed to stop impersonation" },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true
    }
); 