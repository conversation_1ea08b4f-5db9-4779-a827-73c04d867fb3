import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const mergeSchema = z.object({
	primaryJobId: z.string(),
	jobsToMerge: z.array(z.string())
});

export const POST = createHandler(
	async function () {
		const { primaryJobId, jobsToMerge } = this.validatedData;

		try {
			await prisma.$transaction(async (tx) => {
				// Get the jobs being merged
				const mergingJobs = await tx.job.findMany({
					where: { id: { in: jobsToMerge } },
					include: {
						quotes: {
							include: {
								messages: true
							}
						},
						timeline_updates: true
					}
				});

				// Get the primary job
				const primaryJob = await tx.job.findUnique({
					where: { id: primaryJobId },
					include: {
						quotes: {
							include: {
								messages: true
							}
						},
						timeline_updates: true
					}
				});

				if (!primaryJob) {
					throw new Error("Primary job not found");
				}

				// For each job being merged, move its data to the primary job
				for (const job of mergingJobs) {
					// Get existing quotes for the primary job to check for conflicts
					const primaryJobQuotes = await tx.quote.findMany({
						where: { job_id: primaryJobId }
					});

					// Get quotes from the job being merged
					const mergingJobQuotes = await tx.quote.findMany({
						where: { job_id: job.id }
					});

					// Handle quotes with potential conflicts
					for (const mergingQuote of mergingJobQuotes) {
						// Check if there's already a quote from this listing in the primary job
						const existingQuote = primaryJobQuotes.find(
							q => q.listing_id === mergingQuote.listing_id
						);

						if (existingQuote) {
							// If there's a conflict, we need to decide which quote to keep
							// Priority order: ACCEPTED > PENDING > REJECTED/WITHDRAWN/EXPIRED
							const statusPriority = {
								'ACCEPTED': 4,
								'IN_PROGRESS': 3,
								'COMPLETED': 2,
								'PENDING': 1,
								'REJECTED': 0,
								'CUSTOMER_REJECTED': 0,
								'WITHDRAWN': 0,
								'EXPIRED': 0
							};

							const existingPriority = statusPriority[existingQuote.status] || 0;
							const mergingPriority = statusPriority[mergingQuote.status] || 0;

							if (mergingPriority > existingPriority) {
								// The merging quote has higher priority, replace the existing one
								// Delete quote messages first, then the quote
								await tx.quoteMessage.deleteMany({
									where: { quote_id: existingQuote.id }
								});
								await tx.quote.delete({
									where: { id: existingQuote.id }
								});
								await tx.quote.update({
									where: { id: mergingQuote.id },
									data: { job_id: primaryJobId }
								});
							} else if (mergingPriority === existingPriority) {
								// Same priority, use activity timestamp as tiebreaker
								const existingQuoteActivity = existingQuote.responded_at || existingQuote.invited_at;
								const mergingQuoteActivity = mergingQuote.responded_at || mergingQuote.invited_at;

								if (mergingQuoteActivity && (!existingQuoteActivity || mergingQuoteActivity > existingQuoteActivity)) {
									// The merging quote is more recent, replace the existing one
									// Delete quote messages first, then the quote
									await tx.quoteMessage.deleteMany({
										where: { quote_id: existingQuote.id }
									});
									await tx.quote.delete({
										where: { id: existingQuote.id }
									});
									await tx.quote.update({
										where: { id: mergingQuote.id },
										data: { job_id: primaryJobId }
									});
								} else {
									// Keep the existing quote, delete the merging one
									// Delete quote messages first, then the quote
									await tx.quoteMessage.deleteMany({
										where: { quote_id: mergingQuote.id }
									});
									await tx.quote.delete({
										where: { id: mergingQuote.id }
									});
								}
							} else {
								// Keep the existing quote, delete the merging one
								// Delete quote messages first, then the quote
								await tx.quoteMessage.deleteMany({
									where: { quote_id: mergingQuote.id }
								});
								await tx.quote.delete({
									where: { id: mergingQuote.id }
								});
							}
						} else {
							// No conflict, just move the quote
							await tx.quote.update({
								where: { id: mergingQuote.id },
								data: { job_id: primaryJobId }
							});
						}
					}

					// Move timeline updates to primary job
					await tx.timelineUpdate.updateMany({
						where: { job_id: job.id },
						data: { job_id: primaryJobId }
					});

					// If the merged job has a warranty request, move it to primary job
					if (job.warranty_request_id && !primaryJob.warranty_request_id) {
						await tx.job.update({
							where: { id: primaryJobId },
							data: { warranty_request_id: job.warranty_request_id }
						});
					}

					// If the merged job has an accepted quote, move it to primary job
					if (job.accepted_quote_id && !primaryJob.accepted_quote_id) {
						await tx.job.update({
							where: { id: primaryJobId },
							data: { accepted_quote_id: job.accepted_quote_id }
						});
					}

					// If the merged job has a transaction, move it to primary job
					if (job.transaction_id && !primaryJob.transaction_id) {
						await tx.job.update({
							where: { id: primaryJobId },
							data: { transaction_id: job.transaction_id }
						});
					}

					// Update the primary job's status if the merged job has a more advanced status
					const statusPriority = {
						'OPEN': 1,
						'ASSIGNED': 2,
						'IN_PROGRESS': 3,
						'COMPLETED': 4,
						'CANCELLED': 5,
						'EXPIRED': 6
					};

					const primaryStatusPriority = statusPriority[primaryJob.status] || 0;
					const mergedStatusPriority = statusPriority[job.status] || 0;

					if (mergedStatusPriority > primaryStatusPriority) {
						await tx.job.update({
							where: { id: primaryJobId },
							data: { status: job.status }
						});
					}

					// Preserve premium status if any job has it
					if (job.is_premium && !primaryJob.is_premium) {
						await tx.job.update({
							where: { id: primaryJobId },
							data: { is_premium: true }
						});
					}

					// Preserve fraud flag if any job has it
					if (job.flagged_for_fraud && !primaryJob.flagged_for_fraud) {
						await tx.job.update({
							where: { id: primaryJobId },
							data: { flagged_for_fraud: true }
						});
					}

					// Preserve viewed_at if the merged job was viewed more recently
					if (job.viewed_at && (!primaryJob.viewed_at || job.viewed_at > primaryJob.viewed_at)) {
						await tx.job.update({
							where: { id: primaryJobId },
							data: { viewed_at: job.viewed_at }
						});
					}

					// Preserve sent_at if the merged job was sent more recently
					if (job.sent_at && (!primaryJob.sent_at || job.sent_at > primaryJob.sent_at)) {
						await tx.job.update({
							where: { id: primaryJobId },
							data: { sent_at: job.sent_at }
						});
					}

					// Preserve reminder timestamps if they exist
					const reminderFields = [
						'offer_reminder_sent_at',
						'offer_reminder_48h_sent_at',
						'follow_up_sent_at',
						'reminder_48h_sent_at',
						'reminder_1w_sent_at',
						'expired_at'
					];

					for (const field of reminderFields) {
						if (job[field] && !primaryJob[field]) {
							await tx.job.update({
								where: { id: primaryJobId },
								data: { [field]: job[field] }
							});
						}
					}
				}

				// Finally delete the merged jobs
				await tx.job.deleteMany({
					where: { id: { in: jobsToMerge } }
				});
			});

			return this.respond({ success: true });
		} catch (error) {
			console.error("Error in merge process:", error);
			return this.respond({ error: "Failed to merge jobs" }, 500);
		}
	},
	{
		requireAuth: true,
		requiredRole: "ADMIN",
		validateBody: mergeSchema
	}
); 