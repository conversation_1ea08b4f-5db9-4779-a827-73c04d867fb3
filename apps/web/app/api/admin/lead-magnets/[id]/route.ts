import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const updateLeadMagnetSchema = z.object({
    title: z.string().min(1, "Title is required"),
    description: z.string().min(1, "Description is required"),
    image: z.string().optional(),
    newsletter_tags: z.array(z.string()).default([]),
    status: z.enum(["active", "inactive"]).default("active")
});

export const GET = createHandler(
    async function () {
        const { id } = this.params;

        const leadMagnet = await prisma.leadMagnet.findUnique({
            where: { id },
            include: {
                articles: {
                    select: {
                        id: true,
                        title: true,
                        slug: true,
                        type: true
                    }
                }
            }
        });

        if (!leadMagnet) {
            return Response.json({ error: "Lead magnet not found" }, { status: 404 });
        }

        return Response.json(leadMagnet);
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN"
    }
);

export const PUT = createHandler(
    async function () {
        const { id } = this.params;
        const data = this.validatedData;

        try {
            const leadMagnet = await prisma.leadMagnet.update({
                where: { id },
                data
            });

            return Response.json(leadMagnet);
        } catch (error) {
            console.error("Failed to update lead magnet:", error);
            return Response.json(
                { error: "Failed to update lead magnet" },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: updateLeadMagnetSchema
    }
);

export const DELETE = createHandler(
    async function () {
        const { id } = this.params;

        try {
            // First, check if any articles are using this lead magnet
            const articlesCount = await prisma.article.count({
                where: { lead_magnet_id: id }
            });

            if (articlesCount > 0) {
                return Response.json(
                    { error: "Cannot delete lead magnet that is being used by articles" },
                    { status: 400 }
                );
            }

            await prisma.leadMagnet.delete({
                where: { id }
            });

            return Response.json({ success: true });
        } catch (error) {
            console.error("Failed to delete lead magnet:", error);
            return Response.json(
                { error: "Failed to delete lead magnet" },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN"
    }
); 