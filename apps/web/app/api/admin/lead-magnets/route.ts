import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const createLeadMagnetSchema = z.object({
    title: z.string().min(1, "Title is required"),
    description: z.string().min(1, "Description is required"),
    image: z.string().optional(),
    newsletter_tags: z.array(z.string()).default([]),
    status: z.enum(["active", "inactive"]).default("active")
});

export const GET = createHandler(
    async function () {
        const leadMagnets = await prisma.leadMagnet.findMany({
            orderBy: {
                created_at: "desc"
            },
            include: {
                _count: {
                    select: {
                        articles: true
                    }
                }
            }
        });

        return Response.json(leadMagnets);
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN"
    }
);

export const POST = createHandler(
    async function () {
        const data = this.validatedData;
        console.log("POST request data:", data);

        try {
            const leadMagnet = await prisma.leadMagnet.create({
                data
            });

            return Response.json(leadMagnet);
        } catch (error) {
            console.error("Failed to create lead magnet:", error);
            return Response.json(
                { error: "Failed to create lead magnet" },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: createLeadMagnetSchema
    }
);

