import { createHandler } from "@/lib/api/baseHandler";
import { membershipService } from "@/lib/services/membership.service";

export const POST = createHandler(
    async function () {
        const { userId } = this.params;

        if (!userId) {
            return this.respond({ error: "User ID is required" }, 400);
        }

        try {
            // Cancel the membership
            const result = await membershipService.cancelMembership(userId);

            return this.respond({
                success: true,
                message: `Membership cancelled for user ${result.user.email}`,
                user: {
                    id: result.user.id,
                    email: result.user.email,
                    membership_level: result.user.membership_level
                }
            });
        } catch (error) {
            console.error('Error cancelling membership:', error);

            if (error instanceof Error) {
                return this.respond({ error: error.message }, 400);
            }

            return this.respond({ error: 'Failed to cancel membership' }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN"
    }
); 