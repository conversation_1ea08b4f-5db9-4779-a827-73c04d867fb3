import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { z } from "zod";

export const GET = createHandler(
    async function (req, { params }) {
        try {
            const { id } = params;

            const certification = await prisma.providerCertification.findUnique({
                where: { id },
                include: {
                    _count: {
                        select: {
                            provider_certifications: true,
                        },
                    },
                    provider_certifications: {
                        include: {
                            user: {
                                select: {
                                    id: true,
                                    first_name: true,
                                    last_name: true,
                                    email: true,
                                },
                            },
                            listing: {
                                select: {
                                    id: true,
                                    business_name: true,
                                    slug: true,
                                },
                            },
                        },
                        orderBy: {
                            created_at: "desc",
                        },
                    },
                },
            });

            if (!certification) {
                return this.respond({ error: "Certification not found" }, 404);
            }

            return this.respond(certification);
        } catch (error) {
            console.error("Error fetching certification:", error);
            return this.respond({ error: "Failed to fetch certification" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
    }
);

export const PATCH = createHandler(
    async function (req, { params }) {
        try {
            const { id } = params;
            const { display_name, description, training_content, terms_conditions, is_active } = this.validatedData;

            try {
                const certification = await prisma.providerCertification.update({
                    where: { id },
                    data: {
                        display_name,
                        description,
                        training_content,
                        terms_conditions,
                        is_active,
                    },
                    include: {
                        _count: {
                            select: {
                                provider_certifications: true,
                            },
                        },
                    },
                });

                return this.respond(certification);
            } catch (error) {
                console.error("Error updating certification:", error);
                return this.respond({ error: "Failed to update certification" }, 500);
            }
        } catch (error) {
            console.error("Error updating certification:", error);
            return this.respond({ error: "Failed to update certification" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: z.object({
            display_name: z.string().min(1, "Display name is required").optional(),
            description: z.string().optional(),
            training_content: z.any().optional(),
            terms_conditions: z.string().optional(),
            is_active: z.boolean().optional(),
        }),
    }
);

export const DELETE = createHandler(
    async function (req, { params }) {
        try {
            const { id } = params;

            // Check if there are any enrollments
            const enrollmentCount = await prisma.providerCertificationRecord.count({
                where: { certification_id: id },
            });

            if (enrollmentCount > 0) {
                return this.respond(
                    {
                        error: "Cannot delete certification with existing enrollments. Deactivate it instead."
                    },
                    400
                );
            }

            await prisma.providerCertification.delete({
                where: { id },
            });

            return this.respond({ message: "Certification deleted successfully" });
        } catch (error) {
            console.error("Error deleting certification:", error);
            return this.respond({ error: "Failed to delete certification" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
    }
); 