import { createHandler } from "@/lib/api/baseHandler";
import { z } from "zod";

export const GET = createHandler(
    async function () {
        try {
            const certifications = await prisma.providerCertification.findMany({
                include: {
                    _count: {
                        select: {
                            provider_certifications: true,
                        },
                    },
                },
                orderBy: {
                    created_at: "desc",
                },
            });

            return this.respond(certifications);
        } catch (error) {
            console.error("Error fetching certifications:", error);
            return this.respond({ error: "Failed to fetch certifications" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
    }
);

export const POST = createHandler(
    async function () {
        try {
            const { name, display_name, description, training_content, terms_conditions } = this.validatedData;

            // Check if certification with this name already exists
            const existingCert = await prisma.providerCertification.findUnique({
                where: { name },
            });

            if (existingCert) {
                return this.respond({ error: "Certification with this name already exists" }, 400);
            }

            const certification = await prisma.providerCertification.create({
                data: {
                    name,
                    display_name,
                    description,
                    training_content,
                    terms_conditions,
                    is_active: true,
                },
                include: {
                    _count: {
                        select: {
                            provider_certifications: true,
                        },
                    },
                },
            });

            return this.respond(certification, 201);
        } catch (error) {
            console.error("Error creating certification:", error);
            return this.respond({ error: "Failed to create certification" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: z.object({
            name: z.string().min(1, "Name is required"),
            display_name: z.string().min(1, "Display name is required"),
            description: z.string().optional(),
            training_content: z.any().optional(),
            terms_conditions: z.string().optional(),
        }),
    }
); 