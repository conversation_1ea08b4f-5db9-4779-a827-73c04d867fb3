import ReviewVerificationEmail from "@/components/email-templates/ReviewVerificationEmail";
import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { generateToken } from "@/lib/utils";
import { z } from "zod";

const resendVerificationSchema = z.object({});

export const POST = createHandler(
    async function resendReviewVerification(req, { params }) {
        const { id: reviewId } = params;

        try {
            // Find the review
            const review = await prisma.review.findUnique({
                where: { id: reviewId },
                include: {
                    listing: true,
                    user: true
                }
            });

            if (!review) {
                return this.respond({ error: "Review not found" }, 404);
            }

            // Check if review is in a state that can be verified
            if (review.status !== "draft" && review.status !== "pending_verification") {
                return this.respond(
                    { error: "Review is not in a state that requires verification" },
                    400
                );
            }

            // Check if user exists
            if (!review.user_id || !review.user) {
                return this.respond(
                    { error: "Review does not have an associated user" },
                    400
                );
            }

            // Delete any existing verification tokens for this review
            await prisma.verificationToken.deleteMany({
                where: {
                    user_id: review.user_id,
                    type: "review_verification",
                    redirect_url: {
                        contains: `reviewId=${reviewId}`
                    }
                }
            });

            // Create new verification token with 7-day expiration
            const token = generateToken();
            const tokenExpiry = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

            const verificationToken = await prisma.verificationToken.create({
                data: {
                    token,
                    expires: tokenExpiry,
                    user_id: review.user_id,
                    type: "review_verification",
                    redirect_url: `/verify-review?token=${token}&reviewId=${reviewId}`
                }
            });

            // Update review status to pending_verification if it was draft
            if (review.status === "draft") {
                await prisma.review.update({
                    where: { id: reviewId },
                    data: {
                        status: "pending_verification"
                    }
                });
            }

            // Send verification email
            await emailService.send({
                to: review.email!,
                subject: "Verify your review - Action Required",
                react: ReviewVerificationEmail({
                    reviewerName: `${review.first_name} ${review.last_name}`,
                    verificationLink: `${process.env.NEXT_PUBLIC_APP_URL}/verify-review?token=${token}&reviewId=${reviewId}`
                })
            });

            return this.respond({
                success: true,
                message: "Verification email sent successfully",
                expiresAt: tokenExpiry
            });
        } catch (error) {
            console.error("Error resending review verification:", error);
            return this.respond(
                { error: "Failed to resend verification email. Please try again." },
                500
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: resendVerificationSchema
    }
);
