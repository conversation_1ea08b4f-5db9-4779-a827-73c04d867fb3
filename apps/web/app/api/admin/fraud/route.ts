import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { FraudDetectionService } from "@/lib/services/fraud-detection.service";
import { z } from "zod";

const flagJobSchema = z.object({
    jobId: z.string(),
    reason: z.string().min(1, "Reason is required")
});

const unflagJobSchema = z.object({
    jobId: z.string()
});

const banUserSchema = z.object({
    userId: z.string(),
    reason: z.string().min(1, "Reason is required"),
    sendNotifications: z.boolean().default(true)
});

const getFraudStatsSchema = z.object({
    // No additional parameters needed for stats
});

export const GET = createHandler(
    async function () {
        try {
            const stats = await FraudDetectionService.getFraudStats();
            return this.respond(stats);
        } catch (error) {
            console.error("Error fetching fraud stats:", error);
            return this.respond(
                { error: "Failed to fetch fraud statistics" },
                500
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN"
    }
);

export const POST = createHandler(
    async function () {
        const { action, ...data } = this.validatedData;

        try {
            switch (action) {
                case "flag_job":
                    const { jobId, reason } = flagJobSchema.parse(data);
                    const flaggedJob = await FraudDetectionService.flagJobAsFraudulent(
                        jobId,
                        reason,
                        this.user.id
                    );
                    return this.respond({
                        success: true,
                        message: "Job flagged as fraudulent",
                        job: flaggedJob
                    });

                case "unflag_job":
                    const { jobId: unflagJobId } = unflagJobSchema.parse(data);
                    const unflaggedJob = await FraudDetectionService.unflagJob(
                        unflagJobId,
                        this.user.id
                    );
                    return this.respond({
                        success: true,
                        message: "Job unflagged",
                        job: unflaggedJob
                    });

                case "ban_user":
                    const { userId, reason: banReason, sendNotifications } = banUserSchema.parse(data);
                    const banResult = await FraudDetectionService.banUserAndNotifyProviders(
                        userId,
                        banReason,
                        this.user.id,
                        sendNotifications
                    );
                    return this.respond({
                        success: true,
                        message: "User banned successfully",
                        result: banResult
                    });

                default:
                    return this.respond(
                        { error: "Invalid action" },
                        400
                    );
            }
        } catch (error) {
            console.error("Error in fraud management:", error);
            return this.respond(
                { error: "Failed to process request", details: error.message },
                500
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: z.discriminatedUnion("action", [
            z.object({
                action: z.literal("flag_job"),
                jobId: z.string(),
                reason: z.string().min(1, "Reason is required")
            }),
            z.object({
                action: z.literal("unflag_job"),
                jobId: z.string()
            }),
            z.object({
                action: z.literal("ban_user"),
                userId: z.string(),
                reason: z.string().min(1, "Reason is required"),
                sendNotifications: z.boolean().default(true)
            })
        ])
    }
); 