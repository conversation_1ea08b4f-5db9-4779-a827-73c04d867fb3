import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const querySchema = z.object({
    page: z.coerce.number().optional().default(1),
    per_page: z.coerce.number().optional().default(10),
    search: z.string().optional()
});

export const GET = createHandler(
    async function () {
        try {
            const { page = 1, per_page = 10, search = "" } = this.query;

            // Ensure page and per_page are numbers
            const pageNum = Number(page);
            const perPageNum = Number(per_page);

            const where: any = {
                flagged_for_fraud: true
            };

            if (search) {
                where.OR = [
                    { first_name: { contains: search, mode: "insensitive" } },
                    { last_name: { contains: search, mode: "insensitive" } },
                    { email: { contains: search, mode: "insensitive" } },
                    { message: { contains: search, mode: "insensitive" } }
                ];
            }

            const [jobs, total] = await Promise.all([
                prisma.job.findMany({
                    where,
                    include: {
                        user: {
                            select: {
                                id: true,
                                first_name: true,
                                last_name: true,
                                email: true,
                                phone: true,
                                created_at: true
                            }
                        },
                        quotes: {
                            include: {
                                listing: {
                                    select: {
                                        id: true,
                                        business_name: true,
                                        first_name: true,
                                        last_name: true,
                                        email: true
                                    }
                                }
                            }
                        }
                    },
                    orderBy: { created_at: "desc" },
                    take: perPageNum,
                    skip: (pageNum - 1) * perPageNum
                }),
                prisma.job.count({ where })
            ]);

            return this.respond({
                jobs,
                total,
                page: pageNum,
                per_page: perPageNum,
                totalPages: Math.ceil(total / perPageNum)
            });
        } catch (error) {
            console.error("Error fetching flagged jobs:", error);
            return this.respond(
                { error: "Failed to fetch flagged jobs" },
                500
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateQuery: querySchema
    }
); 