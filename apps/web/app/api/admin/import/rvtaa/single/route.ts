// @ts-nocheck
import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { z } from "zod";
// myqsl connection - we have a separate database for the rvta data
import { RVSGService } from "@/lib/services/rvsg.service";

// const mrrConnection = await mysql.createConnection({
// 	host: "localhost",
// 	user: "root",
// 	password: "password",
// 	database: "mrr"
// });

// async function processRecord(
// 	email: string,
// 	rvtaConnection: mysql.Connection,
// 	mrrConnection: mysql.Connection,
// 	intersectSqlConnection: mysql.Connection
// ) {
// 	// ONLY get where LIsting type === Company
// 	const [mrrUsers] = await mrrConnection.query(
// 		"SELECT * FROM users_data WHERE listing_type = 'Company' AND email = ?",
// 		[email]
// 	);

// 	// STEP 2: See if there is a user in mrr with the same email
// 	const mrrUser = mrrUsers.find((user) => {
// 		const matchedOn = {
// 			user_name: user.email === membershipPlan.user_name,
// 			member_email: user.email === membershipPlan.member_email,
// 			directory_email: user.email === directoryEmail,
// 			company:
// 				user.company !== null &&
// 				user.company !== "" &&
// 				user.company === directoryCompany
// 		};

// 		const isMatch = Object.values(matchedOn).some(Boolean);

// 		return isMatch;
// 	});

// 	// STEP 3: Get the best values from the meta data or the mrr user
// 	const description = metaDescription
// 		? metaDescription
// 		: mrrUser?.listing_description;
// }
const schema = z.object({
	userId: z.string().min(1, "Please enter a valid user ID")
});

async function handler() {
	const { userId } = this.validatedData;

	let listing;
	try {
		listing = await RVSGService.importMember(userId, false);
	} catch (error) {
		return this.respond(
			{
				success: false,
				message: "Failed to import member. Failure reason: " + error
			},
			400
		);
	}

	return this.respond({
		success: true,
		listing
	});
}

// Protected route handler - requires admin role
export const POST = createHandler(handler, {
	requireAuth: true,
	requiredRole: "ADMIN",
	validateBody: schema
});
