import { RVSGService } from "@/lib/services/rvsg.service";
import { NextRequest, NextResponse } from "next/server";

// force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
    try {
        // Get recent listings (all listings from the last week, regardless of eligibility)
        const listings = await RVSGService.getRecentlyCreatedListings();



        const response = NextResponse.json({
            success: true,
            listings: listings.map(listing => ({
                id: listing.id,
                email: listing.email,
                first_name: listing.first_name,
                last_name: listing.last_name,
                business_name: listing.business_name,
                slug: listing.slug,
                created_at: listing.created_at,
                invite_sent_at: listing.invite_sent_at,
                rvtaa_member_id: listing.rvtaa_member_id,
                rvtaa_technician_level: listing.rvtaa_technician_level,
                nrvia_inspector_id: listing.nrvia_inspector_id,
                nrvia_inspector_level: listing.nrvia_inspector_level
            }))
        });

        // Add cache-busting headers
        response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
        response.headers.set('Pragma', 'no-cache');
        response.headers.set('Expires', '0');

        return response;
    } catch (error) {
        console.error("Error fetching listings:", error);
        return NextResponse.json(
            { success: false, error: "Failed to fetch listings" },
            { status: 500 }
        );
    }
} 