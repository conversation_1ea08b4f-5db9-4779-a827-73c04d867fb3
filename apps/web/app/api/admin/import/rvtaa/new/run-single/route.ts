import { RVSGService } from "@/lib/services/rvsg.service";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    try {
        const { listingId } = await request.json();

        if (!listingId) {
            return NextResponse.json(
                { success: false, error: "listingId is required" },
                { status: 400 }
            );
        }

        // Get the listing to verify it exists and get the member ID
        const listing = await RVSGService.getEligibleListings();
        const targetListing = listing.find(l => l.id === listingId);

        if (!targetListing) {
            return NextResponse.json(
                { success: false, error: "Listing not found or not eligible" },
                { status: 404 }
            );
        }

        if (!targetListing.rvtaa_member_id) {
            return NextResponse.json(
                { success: false, error: "Listing has no RVTAA member ID" },
                { status: 400 }
            );
        }

        // Queue the single listing for processing
        await RVSGService.queueRVSGImport(targetListing.id, targetListing.rvtaa_member_id);

        return NextResponse.json({
            success: true,
            message: `Successfully queued listing ${listingId} for RVSG import processing`,
            listingId,
            rvtaaMemberId: targetListing.rvtaa_member_id
        });
    } catch (error) {
        console.error("Error queuing single RVSG import job:", error);
        return NextResponse.json(
            { success: false, error: "Failed to queue RVSG import job" },
            { status: 500 }
        );
    }
} 