import { RVSGService } from "@/lib/services/rvsg.service";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    try {
        // Get eligible listings
        const eligibleListings = await RVSGService.getEligibleListings();

        if (eligibleListings.length === 0) {
            return NextResponse.json({
                success: true,
                message: "No eligible listings found",
                queued: 0
            });
        }

        // Queue all eligible listings for processing
        await RVSGService.queueBulkRVSGImports(eligibleListings);

        return NextResponse.json({
            success: true,
            queued: eligibleListings.length,
            total: eligibleListings.length,
            message: `Successfully queued ${eligibleListings.length} listings for RVSG import processing. Jobs will be processed one at a time with automatic retries.`
        });
    } catch (error) {
        console.error("Error queuing RVSG import jobs:", error);
        return NextResponse.json(
            { success: false, error: "Failed to queue RVSG import jobs" },
            { status: 500 }
        );
    }
} 