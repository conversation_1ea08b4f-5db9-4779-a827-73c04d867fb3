// @ts-nocheck
import { createHand<PERSON> } from "@/lib/api/baseHandler";
// myqsl connection - we have a separate database for the rvta data
import prisma from "@/lib/prisma";
import {
	generateRandomPassword,
	generateSlug,
	sanitizeShortDescription
} from "@/lib/utils";
import { standardizePhoneNumber } from "@/lib/utils/phoneNumberStandardization";
import { sanitizeDescription } from "@/lib/utils/sanitize-descriptions";

import mysql from "mysql2/promise";
const intersectSqlConnection = await mysql.createConnection({
	host: "localhost",
	user: "root",
	password: "password",
	database: "intersect"
});

const mrrConnection = await mysql.createConnection({
	host: "localhost",
	user: "root",
	password: "password",
	database: "mrr"
});

const rvtaConnection = await mysql.createConnection({
	host: "localhost",
	user: "root",
	password: "password",
	database: "rvta_12_23"
});

interface FailedRecord {
	id?: string;
	record: Record<string, any>;
	userData: {
		first_name: string;
		last_name: string;
		email: string;
		phone?: string;
	};
	listingData: {
		first_name: string;
		last_name: string;
		business_name: string;
		slug: string;
	};
	error?: string;
}

async function getLocation(e4Locations: any, cities: any) {
	if (!e4Locations.length) {
		return null;
	}

	const location = e4Locations[0];
	return {
		latitude: location.geo_lat,
		longitude: location.geo_lon
	};

	if (!location.geo_lat || !location.geo_lon) {
		return null;
	}

	// One degree of latitude is approximately 111 km
	// For a 50km radius, we can look within roughly 0.45 degrees
	const LAT_RANGE = 0.45;
	// Longitude range needs to be adjusted based on latitude due to Earth's curvature
	const LON_RANGE = LAT_RANGE / Math.cos((location.geo_lat * Math.PI) / 180);

	// Pre-filter cities within rough lat/lon range
	const nearbyCities = cities.filter((city) => {
		const latDiff = Math.abs(Number(city.latitude) - Number(location.geo_lat));
		const lonDiff = Math.abs(Number(city.longitude) - Number(location.geo_lon));
		return latDiff <= LAT_RANGE && lonDiff <= LON_RANGE;
	});

	if (nearbyCities.length === 0) {
		console.log("No cities found in initial range check");
		return null;
	}

	function getDistance(lat1: number, lon1: number, lat2: number, lon2: number) {
		const R = 6371;
		const dLat = ((lat2 - lat1) * Math.PI) / 180;
		const dLon = ((lon2 - lon1) * Math.PI) / 180;
		const a =
			Math.sin(dLat / 2) * Math.sin(dLat / 2) +
			Math.cos((lat1 * Math.PI) / 180) *
				Math.cos((lat2 * Math.PI) / 180) *
				Math.sin(dLon / 2) *
				Math.sin(dLon / 2);
		const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
		return R * c;
	}
}

async function processRecords(
	rvtaConnection: mysql.Connection,
	mrrConnection: mysql.Connection,
	intersectSqlConnection: mysql.Connection
) {
	const stats = {
		total: 0,
		processed: 0,
		users: { success: 0, failed: 0, errors: [] },
		listings: { success: 0, failed: 0, errors: [] },
		locations: { success: 0, failed: 0, errors: [] }
	};

	const failedRecords: FailedRecord[] = [];

	const [membershipPlans] = await intersectSqlConnection.query(
		"SELECT * FROM member_exports WHERE subscription_id IS NOT NULL"
	);
	const [cities] = await intersectSqlConnection.query(
		"SELECT * FROM us_cities"
	);
	// ONLY get where LIsting type === Company
	const [mrrUsers] = await mrrConnection.query(
		"SELECT * FROM users_data WHERE listing_type = 'Company'"
	);
	// loop through membership plans and determine if the user exists in the records
	for (const membershipPlan of membershipPlans.slice(10)) {
		const secondMembershipPlan = membershipPlans.find(
			(plan) =>
				plan.user_id === membershipPlan.user_id &&
				plan.membership_plan !== membershipPlan.membership_plan
		);

		// STEP 1: Look up the user's shit
		const [e4Locations] = await rvtaConnection.query(
			"SELECT * FROM wp_e4_locations WHERE member_id = ?",
			[membershipPlan.user_id]
		);
		const [userMeta] = await rvtaConnection.query(
			"SELECT * FROM wp_usermeta WHERE user_id = ?",
			[membershipPlan.user_id]
		);
		const [activeMemberships] = await rvtaConnection.query(
			"SELECT * FROM wp_posts WHERE post_type = 'wc_user_membership' AND post_status = 'wcm-active'",
			[membershipPlan.user_id]
		);

		const activeMembership = activeMemberships.find(
			(membership) => membership.post_author === membershipPlan.user_id
		);

		if (!activeMembership) {
			continue;
		}

		const location = await getLocation(e4Locations, cities);

		const directoryPhone = userMeta?.find(
			(meta) => meta.meta_key === "directory_phone"
		)?.meta_value;

		const directoryEmail = userMeta?.find(
			(meta) => meta.meta_key === "directory_email"
		)?.meta_value;

		const directoryCompany = userMeta?.find(
			(meta) => meta.meta_key === "directory_company"
		)?.meta_value;

		const metaDescription = userMeta?.find(
			(meta) => meta.meta_key === "description"
		)?.meta_value;

		// STEP 2: See if there is a user in mrr with the same email
		const mrrUser = mrrUsers.find((user) => {
			const matchedOn = {
				user_name: user.email === membershipPlan.user_name,
				member_email: user.email === membershipPlan.member_email,
				directory_email: user.email === directoryEmail,
				company:
					user.company !== null &&
					user.company !== "" &&
					user.company === directoryCompany
			};

			const isMatch = Object.values(matchedOn).some(Boolean);

			return isMatch;
		});

		// STEP 3: Get the best values from the meta data or the mrr user
		const description = metaDescription
			? metaDescription
			: mrrUser?.listing_description;

		const longDescription = sanitizeDescription(description || "");
		const shortDescription = sanitizeShortDescription(
			description ? description.slice(0, 170) : ""
		);
		const phone = directoryPhone
			? standardizePhoneNumber(directoryPhone)
			: mrrUser?.phone
			? standardizePhoneNumber(mrrUser.phone)
			: null;
		const isInspector =
			membershipPlan.membership_plan === "Inspector" ||
			secondMembershipPlan?.membership_plan === "Inspector";
		const isTechnician =
			membershipPlan.membership_plan === "Technician" ||
			secondMembershipPlan?.membership_plan === "Technician";

		// get the technician level and id from meta data
		const technicianLevel = userMeta?.find(
			(meta) => meta.meta_key === "technician_level"
		)?.meta_value;
		const technicianId = userMeta?.find(
			(meta) => meta.meta_key === "_rvsg_technician_id"
		)?.meta_value;

		// get the inspector level and id from meta data
		const inspectorLevel = userMeta?.find(
			(meta) => meta.meta_key === "_rvsg_inspector_id"
		)?.meta_value;
		const inspectorId = userMeta?.find(
			(meta) => meta.meta_key === "_rvsg_inspector_id"
		)?.meta_value;

		const isActive = activeMembership ? true : false;

		// Generate unique slug with counter if needed
		const baseSlug = await generateSlug(
			`${membershipPlan.member_first_name} ${membershipPlan.member_last_name}`
		);

		// STEP 4: Create a user in our prisma users if they don't exist, no need to update
		// if user exists, dont do this
		const rvhelpUser = await prisma.user.upsert({
			create: {
				first_name: membershipPlan.member_first_name,
				last_name: membershipPlan.member_last_name,
				email: directoryEmail ? directoryEmail : membershipPlan.member_email,
				phone: phone || "",
				avatar: membershipPlan.profile_image,
				password: generateRandomPassword(),
				role: "PROVIDER",
				mrr_user_id: mrrUser ? String(mrrUser.user_id) : null
			},
			where: {
				email: directoryEmail ? directoryEmail : membershipPlan.member_email
			},
			update: {
				first_name: membershipPlan.member_first_name,
				last_name: membershipPlan.member_last_name,
				avatar: membershipPlan.profile_image
			}
		});

		stats.users.success++;

		// check if the listing exists
		const existingListing = await prisma.listing.findFirst({
			where: {
				rvtaa_member_id: String(membershipPlan.user_id)
			}
		});

		if (existingListing) {
			continue;
		}

		// STEP 5: Create a listing in our prisma listings

		const rvhelpListing = await prisma.listing.upsert({
			create: {
				first_name: membershipPlan.member_first_name,
				last_name: membershipPlan.member_last_name,
				email: directoryEmail ? directoryEmail : membershipPlan.member_email,
				phone: phone || "",
				profile_image:
					membershipPlan.profile_image || membershipPlan.profile_logo,
				logo: membershipPlan.profile_logo,
				business_name: directoryCompany || "",
				slug: baseSlug,
				long_description: longDescription,
				short_description: shortDescription,
				pricing_settings: {},
				categories: {
					...(isInspector && { "rv-inspection": { selected: true } }),
					...(isTechnician && { "rv-repair": { selected: true } })
				},
				rvtaa_technician_level: Number(technicianLevel) || undefined,
				rvtaa_technician_id: technicianId || undefined,
				nrvia_inspector_level: Number(inspectorLevel) || undefined,
				nrvia_inspector_id: inspectorId || undefined,
				rvtaa_member_id: membershipPlan.user_id
					? String(membershipPlan.user_id)
					: undefined,
				is_active: isActive,
				mrr_user_id: mrrUser ? String(mrrUser.user_id) : null,
				owner: {
					connect: {
						id: rvhelpUser.id
					}
				}
			},
			where: {
				slug: baseSlug
			},
			update: {}
		});

		if (location) {
			await prisma.location.create({
				data: {
					latitude: Number(location.latitude),
					longitude: Number(location.longitude),
					radius: 50,
					listing_id: rvhelpListing.id,
					listing: {
						connect: {
							id: rvhelpListing.id
						}
					}
				}
			});
		}

		stats.listings.success++;
	}

	return {
		success: stats.users.success,
		failed: stats.users.failed,
		total: stats.total,
		stats,
		failedRecords
	};
}

async function handler() {
	console.log("Starting import process...");

	console.log("Clearing existing data...");
	await prisma.$executeRaw`DELETE FROM "users" WHERE email != '<EMAIL>'`;
	await prisma.$executeRaw`DELETE FROM "listings" CASCADE`;
	await prisma.$executeRaw`DELETE FROM "locations" CASCADE`;
	await prisma.$executeRaw`DELETE FROM "import_failures"`;

	const { success, failed, failedRecords, total, stats } = await processRecords(
		rvtaConnection,
		mrrConnection,
		intersectSqlConnection
	);

	console.log("\n=== Final Import Results ===");
	console.log({
		users: {
			success: stats.users.success,
			failed: stats.users.failed,
			errors: stats.users.errors
		},
		listings: {
			success: stats.listings.success,
			failed: stats.listings.failed,
			errors: stats.listings.errors
		},
		locations: {
			success: stats.locations.success,
			failed: stats.locations.failed,
			errors: stats.locations.errors
		}
	});

	return Response.json({
		success,
		failed,
		total,
		failedRecords,
		stats
	});
}

// Protected route handler - requires admin role
export const POST = createHandler(handler, {
	requireAuth: true,
	requiredRole: "ADMIN"
});
