import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";
import { z } from "zod";

const schema = z.object({
    token: z.string().min(1, "Token is required"),
    password: z.string().min(8, "Password must be at least 8 characters")
});

export const POST = createHandler(
    async function () {
        const { token, password } = this.validatedData;

        // Find the verification token
        const verificationToken = await prisma.verificationToken.findFirst({
            where: {
                token,
                type: "password_setup",
                expires: {
                    gt: new Date()
                }
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        first_name: true,
                        last_name: true
                    }
                }
            }
        });

        if (!verificationToken) {
            return this.respond(
                { error: "Invalid or expired token" },
                400
            );
        }

        // Hash the password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Update the user's password
        await prisma.user.update({
            where: { id: verificationToken.user_id },
            data: {
                password: hashedPassword
            }
        });

        // Delete the used token
        await prisma.verificationToken.delete({
            where: { id: verificationToken.id }
        });

        return this.respond({
            success: true,
            email: verificationToken.user.email
        });
    },
    {
        validateBody: schema
    }
);