import { BlacklistService } from "@/lib/services/blacklist.service";
import { z } from "zod";

const checkSchema = z.object({
    types: z.array(z.enum(["EMAIL", "DOMAIN", "USER_ID", "IP_ADDRESS"])),
    values: z.array(z.string().min(1))
});

export async function POST(request: Request) {
    try {
        const body = await request.json();
        const { types, values } = checkSchema.parse(body);

        // Check each type/value pair and return the first blacklisted result
        for (let i = 0; i < types.length; i++) {
            const result = await BlacklistService.isBlacklisted(types[i], values[i]);
            if (result.isBlacklisted) {
                return Response.json(result);
            }
        }

        // If none are blacklisted, return false
        return Response.json({ isBlacklisted: false });
    } catch (error) {
        return Response.json(
            { error: "Invalid request", message: error.message },
            { status: 400 }
        );
    }
} 