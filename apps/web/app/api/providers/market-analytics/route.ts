import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

const querySchema = z.object({
    regions: z.string().optional(), // JSON string of regions: [{ name, lat, lng, radius }]
    timeframe: z.enum(["30d", "3m", "6m", "1y", "all"]).optional().default("3m"),
    categories: z.string().optional(), // Comma-separated category filters
    compareMode: z.string().optional().default("false").transform((val) => val === 'true') // Enable multi-region comparison
});

interface Region {
    name: string;
    latitude: number;
    longitude: number;
    radius: number; // in miles
}

// Haversine distance formula to calculate distance between two points
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 3959; // Radius of the Earth in miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
}

export const GET = createHandler(
    async function (req, { validatedData, session }) {
        try {
            const { regions: regionsParam, timeframe, categories, compareMode } = validatedData;

            // Parse regions if provided, otherwise use default regions
            let regions: Region[] = [];
            if (regionsParam) {
                try {
                    regions = JSON.parse(regionsParam);
                } catch (e) {
                    return Response.json({ error: "Invalid regions format" }, { status: 400 });
                }
            } else {
                // Default to some common regions if none specified
                regions = [{
                    name: "National Overview",
                    latitude: 39.8283, // Center of US
                    longitude: -98.5795,
                    radius: 2000 // Very large radius for national view
                }];
            }

            // Calculate date filter based on timeframe
            let dateFilter = {};
            const now = new Date();

            if (timeframe === "30d") {
                const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                dateFilter = { created_at: { gte: thirtyDaysAgo } };
            } else if (timeframe === "3m") {
                const threeMonthsAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                dateFilter = { created_at: { gte: threeMonthsAgo } };
            } else if (timeframe === "6m") {
                const sixMonthsAgo = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
                dateFilter = { created_at: { gte: sixMonthsAgo } };
            } else if (timeframe === "1y") {
                const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                dateFilter = { created_at: { gte: oneYearAgo } };
            }

            // Get all jobs with location data and associated listings for verification info
            const jobs = await prisma.job.findMany({
                where: {
                    location: { not: null },
                    ...dateFilter,
                    ...(categories && { category: { in: categories.split(',') } })
                },
                select: {
                    id: true,
                    location: true,
                    category: true,
                    status: true,
                    created_at: true,
                    quotes: {
                        select: {
                            id: true,
                            status: true,
                            listing_id: true,
                            listing: {
                                select: {
                                    id: true,
                                    rv_help_verification_level: true,
                                    categories: true,
                                    rvtaa_technician_level: true,
                                    nrvia_inspector_level: true
                                }
                            }
                        }
                    }
                }
            });

            // Process each region
            const regionAnalytics = await Promise.all(regions.map(async (region) => {
                const regionalJobs = jobs.filter(job => {
                    if (!job.location) return false;

                    const location = job.location as any;
                    if (!location.latitude || !location.longitude) return false;

                    const distance = calculateDistance(
                        region.latitude,
                        region.longitude,
                        location.latitude,
                        location.longitude
                    );

                    return distance <= region.radius;
                });

                // Calculate metrics for this region
                const totalJobs = regionalJobs.length;
                const totalQuotes = regionalJobs.reduce((sum, job) => sum + job.quotes.length, 0);
                const completedQuotes = regionalJobs.reduce((sum, job) =>
                    sum + job.quotes.filter(q => q.status === 'COMPLETED').length, 0);

                // Calculate completion rate (still useful for providers)
                const completionRate = totalQuotes > 0 ? (completedQuotes / totalQuotes) * 100 : 0;

                // Count verified providers by category for this region
                const providersByCategory: Record<string, {
                    total: number;
                    verified: number;
                    certifiedPro: number;
                    verificationBreakdown: Record<string, number>;
                    rvtaaRegistered: number;
                    rvtaaCertified: number;
                    nrviaInspectors: number;
                }> = {};

                // Get unique providers operating in this region
                const regionProviders = new Map();
                regionalJobs.forEach(job => {
                    job.quotes.forEach(quote => {
                        if (quote.listing_id && quote.listing && quote.listing.rv_help_verification_level) {
                            const key = `${quote.listing_id}-${job.category}`;
                            if (!regionProviders.has(key)) {
                                regionProviders.set(key, {
                                    listingId: quote.listing_id,
                                    category: job.category,
                                    verificationLevel: quote.listing.rv_help_verification_level,
                                    listingCategories: quote.listing.categories,
                                    rvtaaTechnicianLevel: quote.listing.rvtaa_technician_level,
                                    nrviaInspectorLevel: quote.listing.nrvia_inspector_level
                                });
                            }
                        }
                    });
                });

                // Process provider counts by category
                regionProviders.forEach((provider) => {
                    const category = provider.category;

                    if (!providersByCategory[category]) {
                        providersByCategory[category] = {
                            total: 0,
                            verified: 0,
                            certifiedPro: 0,
                            verificationBreakdown: {
                                NONE: 0,
                                PROFILE_COMPLETE: 0,
                                VERIFIED: 0,
                                CERTIFIED_PRO: 0
                            },
                            rvtaaRegistered: 0,
                            rvtaaCertified: 0,
                            nrviaInspectors: 0
                        };
                    }

                    providersByCategory[category].total++;
                    providersByCategory[category].verificationBreakdown[provider.verificationLevel]++;

                    if (provider.verificationLevel === 'VERIFIED' || provider.verificationLevel === 'CERTIFIED_PRO') {
                        providersByCategory[category].verified++;
                    }
                    if (provider.verificationLevel === 'CERTIFIED_PRO') {
                        providersByCategory[category].certifiedPro++;
                    }

                    // Count RVTAA certifications for non-inspection categories
                    if (category !== 'rv-inspection') {
                        if (provider.rvtaaTechnicianLevel === 1) {
                            providersByCategory[category].rvtaaRegistered++;
                        } else if (provider.rvtaaTechnicianLevel === 2) {
                            providersByCategory[category].rvtaaCertified++;
                        }
                    }

                    // Count NRVIA inspectors for inspection categories
                    if (category === 'rv-inspection' && provider.nrviaInspectorLevel) {
                        providersByCategory[category].nrviaInspectors++;
                    }
                });

                // Calculate overall verified provider stats
                const totalActiveProviders = regionProviders.size;
                const totalVerifiedProviders = Array.from(regionProviders.values()).filter(
                    p => p.verificationLevel === 'VERIFIED' || p.verificationLevel === 'CERTIFIED_PRO'
                ).length;
                const totalCertifiedProProviders = Array.from(regionProviders.values()).filter(
                    p => p.verificationLevel === 'CERTIFIED_PRO'
                ).length;

                // Category breakdown
                const categoryBreakdown = regionalJobs.reduce((acc, job) => {
                    acc[job.category] = (acc[job.category] || 0) + 1;
                    return acc;
                }, {} as Record<string, number>);

                // Status breakdown
                const statusBreakdown = regionalJobs.reduce((acc, job) => {
                    acc[job.status] = (acc[job.status] || 0) + 1;
                    return acc;
                }, {} as Record<string, number>);

                // Monthly trend data for the past year
                const monthlyTrends = [];
                for (let i = 11; i >= 0; i--) {
                    const monthStart = new Date(now);
                    monthStart.setMonth(monthStart.getMonth() - i);
                    monthStart.setDate(1);
                    monthStart.setHours(0, 0, 0, 0);

                    const monthEnd = new Date(monthStart);
                    monthEnd.setMonth(monthEnd.getMonth() + 1);

                    const monthlyJobs = regionalJobs.filter(job =>
                        job.created_at >= monthStart && job.created_at < monthEnd
                    );

                    monthlyTrends.push({
                        month: monthStart.toLocaleDateString('en-US', { year: 'numeric', month: 'short' }),
                        jobs: monthlyJobs.length,
                        quotes: monthlyJobs.reduce((sum, job) => sum + job.quotes.length, 0)
                    });
                }

                // Calculate job density (jobs per 1000 sq miles for the search area)
                const searchAreaSqMiles = Math.PI * region.radius * region.radius;
                const jobDensity = totalJobs / (searchAreaSqMiles / 1000);

                // Get unique providers (by listing_id) operating in this region
                const uniqueProviders = new Set();
                regionalJobs.forEach(job => {
                    job.quotes.forEach(quote => {
                        if (quote.listing_id) {
                            uniqueProviders.add(quote.listing_id);
                        }
                    });
                });

                return {
                    region: {
                        name: region.name,
                        center: { latitude: region.latitude, longitude: region.longitude },
                        radius: region.radius
                    },
                    metrics: {
                        totalJobs,
                        totalQuotes,
                        completionRate: Math.round(completionRate * 100) / 100,
                        jobDensity: Math.round(jobDensity * 100) / 100,
                        activeProviders: totalActiveProviders,
                        verifiedProviders: totalVerifiedProviders,
                        certifiedProProviders: totalCertifiedProProviders,
                        verificationRate: totalActiveProviders > 0 ? Math.round((totalVerifiedProviders / totalActiveProviders) * 10000) / 100 : 0
                    },
                    breakdown: {
                        categories: Object.entries(categoryBreakdown)
                            .map(([category, count]) => ({
                                category: category.replace('rv-', '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                                count,
                                percentage: Math.round((count / totalJobs) * 10000) / 100
                            }))
                            .sort((a, b) => b.count - a.count),

                        statuses: Object.entries(statusBreakdown)
                            .map(([status, count]) => ({
                                status: status.charAt(0).toUpperCase() + status.slice(1).toLowerCase(),
                                count,
                                percentage: Math.round((count / totalJobs) * 10000) / 100
                            }))
                            .sort((a, b) => b.count - a.count),

                        providersByCategory: Object.entries(providersByCategory)
                            .map(([category, data]) => ({
                                category: category.replace('rv-', '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                                categoryId: category,
                                totalProviders: data.total,
                                verifiedProviders: data.verified,
                                certifiedProProviders: data.certifiedPro,
                                verificationRate: data.total > 0 ? Math.round((data.verified / data.total) * 10000) / 100 : 0,
                                verificationBreakdown: data.verificationBreakdown,
                                rvtaaRegistered: data.rvtaaRegistered,
                                rvtaaCertified: data.rvtaaCertified,
                                nrviaInspectors: data.nrviaInspectors,
                                rvtaaTotal: data.rvtaaRegistered + data.rvtaaCertified
                            }))
                            .sort((a, b) => b.totalProviders - a.totalProviders)
                    },
                    trends: {
                        monthly: monthlyTrends,
                        growth: monthlyTrends.length >= 2 ? {
                            jobsGrowth: monthlyTrends[monthlyTrends.length - 1].jobs - monthlyTrends[monthlyTrends.length - 2].jobs,
                            quotesGrowth: monthlyTrends[monthlyTrends.length - 1].quotes - monthlyTrends[monthlyTrends.length - 2].quotes
                        } : null
                    }
                };
            }));

            // If compare mode is enabled and multiple regions, add comparison insights
            let comparison = null;
            if (compareMode && regionAnalytics.length > 1) {
                const sortedByJobs = [...regionAnalytics].sort((a, b) => b.metrics.totalJobs - a.metrics.totalJobs);
                const sortedByDensity = [...regionAnalytics].sort((a, b) => b.metrics.jobDensity - a.metrics.jobDensity);
                const sortedByVerified = [...regionAnalytics].sort((a, b) => b.metrics.verifiedProviders - a.metrics.verifiedProviders);
                const sortedByVerificationRate = [...regionAnalytics].sort((a, b) => b.metrics.verificationRate - a.metrics.verificationRate);

                comparison = {
                    highestDemand: sortedByJobs[0].region.name,
                    highestDensity: sortedByDensity[0].region.name,
                    mostVerifiedProviders: sortedByVerified[0].region.name,
                    highestVerificationRate: sortedByVerificationRate[0].region.name,
                    summary: `${sortedByJobs[0].region.name} has the highest total demand with ${sortedByJobs[0].metrics.totalJobs} jobs. ${sortedByVerified[0].region.name} has the most verified providers (${sortedByVerified[0].metrics.verifiedProviders}), while ${sortedByVerificationRate[0].region.name} has the highest verification rate at ${sortedByVerificationRate[0].metrics.verificationRate}%.`
                };
            }

            return Response.json({
                timeframe,
                generatedAt: new Date().toISOString(),
                regions: regionAnalytics,
                comparison,
                metadata: {
                    totalRegions: regions.length,
                    compareMode,
                    categoriesFilter: categories?.split(',') || null
                }
            });
        } catch (error) {
            console.error('Market analytics API error:', error);
            return Response.json(
                { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
                { status: 500 }
            );
        }
    },
    {
        validateQuery: querySchema,
        requireAuth: true
        // Note: No role requirement - any authenticated user can access
    }
); 