import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { SearchService } from "@/lib/services/search.service";
import { z } from "zod";

const searchParamsSchema = z.object({
    lat: z.string(),
    lng: z.string(),
    category: z.string(),
    subcategory: z.string().optional(),
    page: z.string().default("1"),
    limit: z.string().default("10")
});

export const GET = createHandler(
    async function (req: Request) {
        const url = new URL(req.url);
        const params = Object.fromEntries(url.searchParams);

        const {
            lat,
            lng,
            category,
            subcategory,
            page,
            limit
        } = params;

        const searchParams = {
            lat,
            lng,
            category,
            subcategory,
            filters: {}
        };

        try {
            const results = await SearchService.getInviteProviders(
                searchParams,
                parseInt(page),
                parseInt(limit)
            );

            return Response.json(results);
        } catch (error) {
            console.error("Invite providers search error:", error);
            return Response.json({ error: "Search failed" }, { status: 500 });
        }
    }, {
    validateQuery: searchParamsSchema
}); 