import { createHandler } from "@/lib/api/baseHandler";
import { SearchService } from "@/lib/services/search.service";
import { ComingSoonApiResponse } from "@/types/coming-soon";
import { z } from "zod";

const searchParamsSchema = z.object({
	lat: z.string(),
	lng: z.string(),
	category: z.string(),
	subcategory: z.string().optional(),
	radius: z.string().default("100")
});

export const GET = createHandler(
	async function (req: Request) {
		const url = new URL(req.url);
		const params = Object.fromEntries(url.searchParams);

		const {
			lat,
			lng,
			category,
			subcategory,
			radius
		} = params;

		const searchLat = parseFloat(lat);
		const searchLng = parseFloat(lng);
		const searchRadius = parseFloat(radius);

		try {
			const listings = await SearchService.getComingSoonProviders(
				searchLat,
				searchLng,
				category,
				subcategory,
				searchRadius
			);

			const response: ComingSoonApiResponse = {
				success: true,
				listings,
				total: listings.length
			};

			return Response.json(response);

		} catch (error) {
			console.error("Coming soon search error:", error);
			const errorResponse: ComingSoonApiResponse = {
				error: "Failed to fetch coming soon providers",
				success: false,
				listings: [],
				total: 0
			};
			return Response.json(errorResponse, { status: 500 });
		}
	},
	{
		validateQuery: searchParamsSchema
	}
);
