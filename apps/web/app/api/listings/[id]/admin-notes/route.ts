import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

export const PUT = createHandler(
	async function () {
		const listingId = this.params.id;
		const { admin_notes } = this.validatedData;

		// First check if listing exists
		const listing = await prisma.listing.findUnique({
			where: { id: listingId }
		});

		if (!listing) {
			return this.respond({ error: "Listing not found" }, 404);
		}

		// Only admins can update admin notes
		if (!this.isAdmin) {
			return this.respond({ error: "Unauthorized" }, 401);
		}

		// Update only the admin_notes field
		const updatedListing = await prisma.listing.update({
			where: { id: listingId },
			data: { admin_notes }
		});

		return this.respond({ 
			success: true, 
			admin_notes: updatedListing.admin_notes 
		});
	},
	{
		requireAuth: true,
		requiredRole: "ADMIN",
		validateBody: z.object({
			admin_notes: z.string().nullable()
		})
	}
); 