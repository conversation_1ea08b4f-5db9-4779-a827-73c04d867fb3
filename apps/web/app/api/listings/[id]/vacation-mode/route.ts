import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { hasListingAccess } from "@/lib/services/listing-access.service";
import { z } from "zod";

export const PUT = createHandler(
    async function () {
        const listingId = this.params.id;
        const data = this.validatedData;

        // Verify user owns this listing
        const listing = await prisma.listing.findUnique({
            where: { id: listingId }
        });

        if (!listing) {
            return this.respond({ error: "Listing not found" }, 404);
        }

        const hasAccess =
            (await hasListingAccess({
                user: this.user,
                listingId,
                requiredRole: "MANAGER"
            })) || this.isAdmin;

        if (!hasAccess) {
            return this.respond({ error: "Unauthorized" }, 401);
        }

        // Update certifications
        await prisma.listing.update({
            where: { id: listingId },
            data: {
                vacation_mode: data
            }
        });

        console.log(`Updated vacation mode for listing ID: ${listingId}`);

        return this.respond({ success: true }, 200);
    },
    {
        requireAuth: true,
        validateBody: z.object({
            enabled: z.boolean(),
            message: z.string().optional().nullable(),
        })
    }
);
