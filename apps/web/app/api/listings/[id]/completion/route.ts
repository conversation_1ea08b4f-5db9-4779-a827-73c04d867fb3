import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { hasListingAccess } from "@/lib/services/listing-access.service";

// Import the same validation logic used by the client
import { validateAllSections } from "@/app/(main)/provider/business/(components)/forms/validationRules";

export const GET = createHandler(
    async function getCompletionStatus() {
        const listingId = this.params.id;

        const hasAccess =
            (await hasListingAccess({
                user: this.user,
                listingId,
                requiredRole: "MANAGER"
            })) || this.isAdmin;

        if (!hasAccess) {
            return this.respond({ error: "Unauthorized" }, 401);
        }

        const listing = await prisma.listing.findUnique({
            where: { id: listingId },
            include: {
                locations: true
            }
        });

        if (!listing) {
            return this.respond({ error: "Listing not found" }, 404);
        }

        // Use the same validation logic as the client
        const validation = validateAllSections(listing as any);

        // Calculate completion percentage using the same logic as the client
        // Only consider sections that are actually required for this user's case
        const hasRVRepair = listing.categories?.["rv-repair"]?.selected === true;

        // Define which sections are required based on user's selections
        const requiredSections = [
            'profile',
            'business',
            'location',
            'categories'
        ];

        // Add pricing only if RV repair is selected
        if (hasRVRepair) {
            requiredSections.push('pricing');
        }

        // Filter validation to only include required sections
        const relevantValidation = Object.entries(validation).filter(([sectionId]) =>
            requiredSections.includes(sectionId)
        );

        const sectionPercentages = relevantValidation.map(
            ([_, v]: [string, any]) => v.completionPercentage || 0
        );

        const completionPercentage = Math.round(
            sectionPercentages.reduce((sum: number, value: number) => sum + value, 0) /
            sectionPercentages.length
        );

        return this.respond({
            completionPercentage,
            validation,
            profile_completed: listing.profile_completed
        });
    },
    {
        requireAuth: true
    }
); 