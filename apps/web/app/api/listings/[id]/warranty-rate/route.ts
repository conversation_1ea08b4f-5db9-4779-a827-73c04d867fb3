import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { hasListingAccess } from "@/lib/services/listing-access.service";
import { z } from "zod";

const warrantyRateSchema = z.object({
    warranty_rate: z.number().min(0.01, "Warranty rate must be greater than $0")
});

export const PUT = createHandler(
    async function () {
        const listingId = this.params.id;
        const { warranty_rate } = this.validatedData;

        // First check if listing exists
        const listing = await prisma.listing.findUnique({
            where: { id: listingId },
            select: {
                id: true,
                pricing_settings: true
            }
        });

        if (!listing) {
            return this.respond({ error: "Listing not found" }, 404);
        }

        const hasAccess =
            (await hasListingAccess({
                user: this.user,
                listingId,
                requiredRole: "MANAGER"
            })) || this.isAdmin;

        if (!hasAccess) {
            return this.respond({ error: "Unauthorized" }, 401);
        }

        // Update only the warranty_rate in pricing_settings
        const currentPricingSettings = (listing.pricing_settings as Record<string, any>) || {};
        const updatedPricingSettings = {
            ...currentPricingSettings,
            warranty_rate
        };

        // Update the listing
        const updatedListing = await prisma.listing.update({
            where: { id: listingId },
            data: {
                pricing_settings: updatedPricingSettings
            },
            select: {
                id: true,
                pricing_settings: true
            }
        });

        return this.respond({
            data: updatedListing,
            message: "Warranty rate updated successfully"
        });
    },
    {
        requireAuth: true,
        validateBody: warrantyRateSchema
    }
);