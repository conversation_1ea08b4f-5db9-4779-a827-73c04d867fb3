import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { hasListingAccess } from "@/lib/services/listing-access.service";
import { z } from "zod";

const locationSchema = z.object({
    formatted_address: z.string().min(1, "Address is required"),
    address_line_1: z.string().min(1, "Address line 1 is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    postcode: z.string().min(1, "Postal code is required"),
    country: z.string(),
    latitude: z.number(),
    longitude: z.number(),
    radius: z.number().min(1, "Service radius is required"),
    inspection_radius: z
        .number()
        .min(1, "Inspection radius is required")
        .optional()
        .nullable(),
    emergency_radius: z
        .number()
        .min(1, "Emergency radius is required")
        .optional()
        .nullable(),
    start_date: z.string().optional().nullable(),
    end_date: z.string().optional().nullable(),
    description: z.string().optional().nullable(),
    default: z.boolean().optional().nullable()
});

// PUT /api/listings/[id]/locations/[locationId] - Update specific location
export const PUT = createHandler(
    async function () {
        const { id: listingId, locationId } = this.params;
        const locationData = this.validatedData;

        if (!this.user) {
            return this.respond({ error: "Unauthorized" }, 401);
        }

        const listing = await prisma.listing.findUnique({
            where: { id: listingId },
            include: { locations: true }
        });

        if (!listing) {
            return this.respond({ error: "Listing not found" }, 404);
        }

        const hasAccess = await hasListingAccess({
            user: this.user,
            listingId: listing.id,
            requiredRole: "MANAGER"
        });

        if (!hasAccess) {
            return this.respond({ error: "Unauthorized" }, 401);
        }

        // Check if location exists
        const existingLocation = listing.locations.find(loc => loc.id === locationId);
        if (!existingLocation) {
            return this.respond({ error: "Location not found" }, 404);
        }

        try {
            const updatedLocation = await prisma.location.update({
                where: { id: locationId },
                data: locationData
            });

            return this.respond(updatedLocation, 200);
        } catch (error) {
            console.error("Error updating location:", error);
            return this.respond({ error: "Internal Server Error" }, 500);
        }
    },
    {
        validateBody: locationSchema
    }
);

// DELETE /api/listings/[id]/locations/[locationId] - Delete specific location
export const DELETE = createHandler(
    async function () {
        const { id: listingId, locationId } = this.params;

        if (!this.user) {
            return this.respond({ error: "Unauthorized" }, 401);
        }

        const listing = await prisma.listing.findUnique({
            where: { id: listingId },
            include: { locations: true }
        });

        if (!listing) {
            return this.respond({ error: "Listing not found" }, 404);
        }

        const hasAccess = await hasListingAccess({
            user: this.user,
            listingId: listing.id,
            requiredRole: "MANAGER"
        });

        if (!hasAccess) {
            return this.respond({ error: "Unauthorized" }, 401);
        }

        // Check if location exists
        const existingLocation = listing.locations.find(loc => loc.id === locationId);
        if (!existingLocation) {
            return this.respond({ error: "Location not found" }, 404);
        }

        // Prevent deletion of default location if it's the only one
        if (existingLocation.default && listing.locations.length === 1) {
            return this.respond({ error: "Cannot delete the only location" }, 400);
        }

        try {
            await prisma.location.delete({
                where: { id: locationId }
            });

            return this.respond({ success: true }, 200);
        } catch (error) {
            console.error("Error deleting location:", error);
            return this.respond({ error: "Internal Server Error" }, 500);
        }
    }
);
