import ReviewVerificationEmail from "@/components/email-templates/ReviewVerificationEmail";
import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { generateToken } from "@/lib/utils";
import { z } from "zod";

const resendVerificationSchema = z.object({
    email: z.string().email("Invalid email address")
});

export const POST = createHandler(
    async function resendReviewVerification(req, { validatedData }) {
        const { email } = validatedData;

        try {
            // Find user by email
            const user = await prisma.user.findUnique({
                where: { email }
            });

            if (!user) {
                return this.respond({ error: "User not found" }, 404);
            }

            // Check if user's email is already verified
            if (user.email_verified_at) {
                return this.respond({
                    success: true,
                    message: "Email already verified",
                    redirect: `/review-submitted?email=${encodeURIComponent(email)}&verified=true`
                });
            }

            // Find pending verification reviews for this user
            const pendingReviews = await prisma.review.findMany({
                where: {
                    user_id: user.id,
                    status: "pending_verification"
                }
            });

            if (pendingReviews.length === 0) {
                return this.respond({
                    error: "No pending reviews found for verification"
                }, 404);
            }

            // Check for existing verification tokens for this user
            const existingTokens = await prisma.verificationToken.findMany({
                where: {
                    user_id: user.id,
                    type: "review_verification",
                    expires: {
                        gt: new Date() // Only active tokens
                    }
                }
            });

            // If there are active tokens, don't send duplicate emails
            if (existingTokens.length > 0) {
                return this.respond({
                    success: true,
                    message: "Verification email already sent. Please check your inbox or spam folder.",
                    expiresAt: existingTokens[0].expires
                });
            }

            // Delete any expired tokens
            await prisma.verificationToken.deleteMany({
                where: {
                    user_id: user.id,
                    type: "review_verification",
                    expires: {
                        lt: new Date()
                    }
                }
            });

            // Create new verification token with 3-day expiration
            const token = generateToken();
            const tokenExpiry = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000); // 3 days

            // Create verification token for the first pending review
            const firstReview = pendingReviews[0];
            const verificationToken = await prisma.verificationToken.create({
                data: {
                    token,
                    expires: tokenExpiry,
                    user_id: user.id,
                    type: "review_verification",
                    redirect_url: `/verify-review?token=${token}&reviewId=${firstReview.id}`
                }
            });

            // Send verification email
            await emailService.send({
                to: email,
                subject: "Verify your review",
                react: ReviewVerificationEmail({
                    reviewerName: `${user.first_name} ${user.last_name}`,
                    verificationLink: `${process.env.NEXT_PUBLIC_APP_URL}/verify-review?token=${token}&reviewId=${firstReview.id}`
                })
            });

            return this.respond({
                success: true,
                message: "Verification email sent successfully",
                expiresAt: tokenExpiry
            });
        } catch (error) {
            console.error("Error resending review verification:", error);
            return this.respond(
                { error: "Failed to send verification email. Please try again." },
                500
            );
        }
    },
    {
        validateBody: resendVerificationSchema
    }
);
