import { createH<PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const deleteReviewSchema = z.object({
    reason: z.string().min(1, "Deletion reason is required"),
    admin_notes: z.string().optional()
});

export const GET = createHandler(
    async function getReview(req, { params }) {
        const { id } = params;

        try {
            const review = await prisma.review.findUnique({
                where: { id },
                select: {
                    id: true,
                    email: true,
                    user_id: true,
                    overall: true,
                    status: true,
                    verified: true
                }
            });

            if (!review) {
                return this.respond({ error: "Review not found" }, 404);
            }

            return this.respond({
                success: true,
                review
            });
        } catch (error) {
            console.error("Error fetching review:", error);
            return this.respond(
                { error: "Failed to fetch review" },
                500
            );
        }
    }
);

export const DELETE = createHandler(
    async function deleteReview(req, { params }) {
        const { id } = params;

        try {
            // Check if review exists
            const review = await prisma.review.findUnique({
                where: { id },
                include: {
                    listing: {
                        select: {
                            business_name: true
                        }
                    }
                }
            });

            if (!review) {
                return this.respond({ error: "Review not found" }, 404);
            }

            // Build admin notes
            const adminNoteParts = [`Deleted by admin: ${this.user.email}`];
            if (this.validatedData.admin_notes) {
                adminNoteParts.push(this.validatedData.admin_notes);
            }
            const adminNotes = adminNoteParts.join("\n\n");

            // Delete the review
            await prisma.review.delete({
                where: { id }
            });

            // Log the deletion for audit purposes
            await prisma.auditLog.create({
                data: {
                    action: "REVIEW_DELETED",
                    entity_type: "REVIEW",
                    entity_id: id,
                    user_id: this.user.id,
                    changes: {
                        review_id: id,
                        business_name: review.listing.business_name,
                        reason: this.validatedData.reason,
                        admin_notes: adminNotes
                    }
                }
            });

            return this.respond({
                success: true,
                message: "Review deleted successfully"
            });
        } catch (error) {
            console.error("Error deleting review:", error);
            return this.respond(
                { error: "Failed to delete review" },
                500
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN",
        validateBody: deleteReviewSchema
    }
); 