
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { createHandler } from "../../../../lib/api/baseHandler";

const submitReviewSchema = z.object({
    title: z.string().min(1, "Title is required"),
    content: z.string().min(10, "Review content must be at least 10 characters"),
    ratings: z.object({
        overall: z.number().min(1).max(5),
        service: z.number().min(1).max(5),
        responsiveness: z.number().min(1).max(5),
        expertise: z.number().min(1).max(5),
        results: z.number().min(1).max(5),
        communication: z.number().min(1).max(5)
    }),
    service_category: z.string().min(1, "Service category is required"),
    listingId: z.string().min(1, "Listing ID is required")
});

export const POST = createHandler(async function (req, { validatedData }) {
    // Ensure user is logged in
    if (!this.user) {
        return this.respond(
            { error: "Authentication required" },
            401
        );
    }

    // Check if user has already reviewed this listing
    const existingReview = await prisma.review.findFirst({
        where: {
            listing_id: validatedData.listingId,
            user_id: this.user.id
        }
    });

    if (existingReview) {
        return this.respond(
            { error: "You have already reviewed this listing" },
            400
        );
    }

    // Create the complete review directly
    const review = await prisma.review.create({
        data: {
            listing_id: validatedData.listingId,
            user_id: this.user.id,
            title: validatedData.title,
            content: validatedData.content,
            overall: validatedData.ratings.overall,
            service: validatedData.ratings.service,
            responsiveness: validatedData.ratings.responsiveness,
            expertise: validatedData.ratings.expertise,
            results: validatedData.ratings.results,
            communication: validatedData.ratings.communication,
            service_category: validatedData.service_category,
            first_name: this.user.first_name,
            last_name: this.user.last_name,
            email: this.user.email,
            // Set status based on email verification
            status: this.user.email_verified_at ? "in_moderation" : "pending_verification"
        }
    });

    // If user is verified, create membership offer and redirect to upsell page
    if (this.user.email_verified_at) {
        // Create membership offer for verified users (same logic as in verify route)
        console.log('🎁 SUBMIT DEBUG: Creating membership offer for verified user:', this.user.id);
        if (this.user.id && this.user.membership_level === "FREE") {
            try {
                // Check if user already has an active review-based offer
                const existingOffer = await prisma.membershipOffer.findFirst({
                    where: {
                        user_id: this.user.id,
                        offer_type: "REVIEW_50_OFF",
                        is_active: true,
                        used_at: null,
                        expires_at: {
                            gt: new Date()
                        }
                    }
                });
                console.log('🔍 SUBMIT DEBUG: Existing offer found:', existingOffer);

                // Only create if no active offer exists
                if (!existingOffer) {
                    console.log('🆕 SUBMIT DEBUG: Creating new membership offer');
                    const newOffer = await prisma.membershipOffer.create({
                        data: {
                            user_id: this.user.id,
                            email: this.user.email,
                            offer_type: "REVIEW_50_OFF",
                            discount_percentage: 50,
                            description: "50% off for reviewers",
                            is_active: true,
                            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
                        }
                    });
                    console.log('✅ SUBMIT DEBUG: Created membership offer:', newOffer);
                } else {
                    console.log('⚠️ SUBMIT DEBUG: User already has active review offer');
                }
            } catch (error) {
                console.error("Error creating membership offer for review submission:", error);
                // Don't fail the review submission if offer creation fails
            }
        } else {
            console.log('❌ SUBMIT DEBUG: User is not FREE tier or missing user info');
        }

        const url = this.user.membership_level === "FREE" ? `/verify-review?reviewId=${review.id}&rating=${validatedData.ratings.overall}` : `/review-submitted?email=${encodeURIComponent(this.user.email)}&verified=true`;
        return this.respond({
            success: true,
            redirect: url,
            message: "Review submitted successfully and is now under moderation."
        });
    }

    // If user is not verified, they need to verify their email
    return this.respond({
        success: true,
        redirect: `/review-submitted?email=${encodeURIComponent(this.user.email)}&verified=false`,
        message: "Review submitted successfully. Please check your email to verify your review."
    });
}, {
    validateBody: submitReviewSchema
});