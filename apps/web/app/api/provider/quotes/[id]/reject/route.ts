import { createHandler } from "@/lib/api/baseHandler";
import { ListingService } from "@/lib/services/listing.service";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { NextResponse } from "next/server";
import { z } from "zod";

const rejectJobSchema = z.object({
	rejection_reason: z.enum([
		"TOO_BUSY",
		"SCHEDULE_CONFLICT",
		"OUTSIDE_TRAVEL_AREA",
		"NOT_A_GOOD_FIT",
		"OTHER"
	]),
	rejection_reason_details: z.string().optional().nullable()
});

export const POST = createHandler({
	requireAuth: true,
	requiredRole: "PROVIDER",
	validateBody: rejectJobSchema,
	handler: async (req, { validatedData, session, params }) => {
		const { rejection_reason, rejection_reason_details } = validatedData;

		const listing = await ListingService.getListingByUserId(session.user.id);

		if (!listing) {
			throw new Error("Provider listing not found");
		}

		// Single service call handles all the logic
		const updatedQuote = await QuoteStatusService.providerDeclineJob({
			quoteId: params.id,
			listingId: listing.id,
			userId: session.user.id,
			rejectionReason: rejection_reason,
			rejectionReasonDetails: rejection_reason_details,
		});

		return NextResponse.json(updatedQuote, { status: 200 });
	}
}); 