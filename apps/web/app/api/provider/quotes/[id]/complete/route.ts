import { createHandler } from "@/lib/api/baseHandler";
import { ListingService } from "@/lib/services/listing.service";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { ResolutionStatus } from "@rvhelp/database";
import { z } from "zod";

const requestSchema = z.object({
	sendInvoice: z.boolean().default(false),
	requestReview: z.boolean().default(false),
	reviewDelayHours: z.number().optional(),
	// Additional resolution fields
	resolutionStatus: z.string().optional(),
	resolutionNotes: z.string().optional()
});

export const POST = createHandler(
	async function (req, { params, validatedData }) {
		const listing = await ListingService.getListingByUserId(this.user.id);
		if (!listing) {
			return this.respond({ error: "Listing not found" }, 404);
		}

		try {
			// Single service call handles all the logic
			const updatedQuote = await QuoteStatusService.providerCompleteJob({
				quoteId: params.id,
				listingId: listing.id,
				userId: this.user.id,
				resolutionStatus: validatedData.resolutionStatus?.toUpperCase() as ResolutionStatus,
				resolutionNotes: validatedData.resolutionNotes,
				sendInvoice: validatedData.sendInvoice,
				requestReview: validatedData.requestReview,
				reviewDelayHours: validatedData.reviewDelayHours,
			});
			return this.respond({ success: true, quote: updatedQuote });
		} catch (error) {
			console.error(error);
			return this.respond({ error: "Failed to complete job" }, 500);
		}
	},
	{
		requireAuth: true,
		requiredRole: "PROVIDER",
		validateBody: requestSchema
	}
); 