import { createH<PERSON><PERSON> } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { ListingService } from "@/lib/services/listing.service";
import { z } from "zod";

const warrantyTermsSchema = z.object({
    accepted: z.boolean().refine(val => val === true, {
        message: "Terms must be accepted"
    })
});

export const POST = createHandler(
    async function () {
        try {
            const { accepted } = this.validatedData;
            const quoteId = this.params.id;

            if (!accepted) {
                return this.respond({ error: "Terms must be accepted" }, 400);
            }

            // Get the provider's listing
            const listing = await ListingService.getListingByUserId(this.user.id);
            if (!listing) {
                return this.respond({ error: "No listing found" }, 404);
            }

            // Get the quote and verify ownership + warranty request
            const quote = await prisma.quote.findUnique({
                where: { id: quoteId },
                include: {
                    job: {
                        include: {
                            warranty_request: {
                                include: {
                                    company: true
                                }
                            }
                        }
                    }
                }
            });

            if (!quote) {
                return this.respond({ error: "Quote not found" }, 404);
            }

            if (quote.listing_id !== listing.id) {
                return this.respond({ error: "Unauthorized" }, 403);
            }

            if (!quote.job.warranty_request) {
                return this.respond({ error: "Not a warranty request" }, 400);
            }

            // Check if terms are already accepted
            if (quote.warranty_terms_accepted_at) {
                return this.respond({
                    message: "Terms already accepted",
                    acceptedAt: quote.warranty_terms_accepted_at
                });
            }

            // Update the quote to mark warranty terms as accepted
            const updatedQuote = await prisma.quote.update({
                where: { id: quoteId },
                data: {
                    warranty_terms_accepted_at: new Date()
                }
            });

            return this.respond({
                message: "Warranty terms accepted successfully",
                acceptedAt: updatedQuote.warranty_terms_accepted_at
            });

        } catch (error) {
            console.error("Error accepting warranty terms:", error);
            return this.respond({ error: "Failed to accept warranty terms" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "PROVIDER",
        validateBody: warrantyTermsSchema
    }
); 