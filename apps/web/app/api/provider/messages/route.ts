import { createHandler } from "@/lib/api/baseHandler";
import { ListingService } from "@/lib/services/listing.service";
import { MessageService } from "@/lib/services/messaging.service";
import { NextResponse } from "next/server";

export const GET = createHandler({
	requireAuth: true,
	requiredRole: "PROVIDER",
	handler: async (req, { session }) => {
		const { searchParams } = new URL(req.url);
		const limit = parseInt(searchParams.get("limit") || "50");
		const offset = parseInt(searchParams.get("offset") || "0");

		// Get the provider's listing
		const listing = await ListingService.getListingByUserId(session.user.id);
		if (!listing) {
			console.error("Provider listing not found", session.user.id);
			throw new Error("Provider listing not found");
		}



		// Get conversations using the service
		const result = await MessageService.getConversations({
			providerId: session.user.id,
			limit,
			offset
		});

		console.log("result", result.conversations[0].messages);

		return NextResponse.json(result);
	}
});
