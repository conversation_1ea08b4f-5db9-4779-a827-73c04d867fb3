import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { ListingService } from "@/lib/services/listing.service";
import { z } from "zod";

const querySchema = z.object({
	status: z
		.enum(["pending", "accepted", "rejected", "completed", "all"])
		.optional()
});

export const GET = createHandler(
	async function (req, { query }) {
		// get the listing for this user
		const listing = await ListingService.getListingByUserId(this.user.id);

		if (!listing) {
			return this.respond({ error: "Listing not found" }, 404);
		}

		const status = query.status;

		const troubleshootingRequests =
			await prisma.troubleshootingRequest.findMany({
				where: {
					listing_id: listing.id,
					...(status && status !== "all" ? { status: status } : {})
				},
				include: {
					user: {
						select: {
							first_name: true,
							last_name: true,
							email: true,
							phone: true
						}
					},
					listing: {
						select: {
							id: true,
							business_name: true,
							phone: true,
							email: true,
							settings_virtual_diagnosis: true,
							settings_virtual_diagnosis_notifications: true
						}
					}
				},
				orderBy: {
					created_at: "desc"
				}
			});

		return this.respond(troubleshootingRequests, 200);
	},
	{
		validateQuery: querySchema,
		requireAuth: true,
		requiredRole: "PROVIDER"
	}
);
