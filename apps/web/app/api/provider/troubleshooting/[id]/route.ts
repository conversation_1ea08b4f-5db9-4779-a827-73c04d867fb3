import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { ListingService } from "@/lib/services/listing.service";
import { z } from "zod";

const updateSchema = z.object({
    notes: z.string().optional(),
    requestReview: z.boolean().optional(),
    reviewDelayHours: z.number().optional()
});

export const GET = createHandler(
    async function (req, { params }) {
        // Get the provider's listing to verify ownership
        const listing = await ListingService.getListingByUserId(this.user.id);

        if (!listing) {
            return this.respond({ error: "Listing not found" }, 404);
        }

        // Get the troubleshooting request
        const request = await prisma.troubleshootingRequest.findUnique({
            where: { id: params.id },
            include: {
                listing: true,
                user: true
            }
        });

        if (!request) {
            return this.respond({ error: "Troubleshooting request not found" }, 404);
        }

        // Verify the request belongs to this provider's listing
        if (request.listing_id !== listing.id) {
            return this.respond({ error: "Unauthorized" }, 403);
        }

        return this.respond(request, 200);
    },
    {
        requireAuth: true,
        requiredRole: "PROVIDER"
    }
);

export const PUT = createHandler(
    async function (req, { params, query, validatedData }) {
        // Get the provider's listing to verify ownership
        const listing = await ListingService.getListingByUserId(this.user.id);

        if (!listing) {
            return this.respond({ error: "Listing not found" }, 404);
        }

        // Get the troubleshooting request
        const request = await prisma.troubleshootingRequest.findUnique({
            where: { id: params.id },
            include: {
                listing: true,
                user: true
            }
        });

        if (!request) {
            return this.respond({ error: "Troubleshooting request not found" }, 404);
        }

        // Verify the request belongs to this provider's listing
        if (request.listing_id !== listing.id) {
            return this.respond({ error: "Unauthorized" }, 403);
        }

        const action = query.action as string;
        const { notes, requestReview, reviewDelayHours } = validatedData;

        try {
            let updateData: any = {};
            let providerNotes = request.provider_notes ? [...(request.provider_notes as any[])] : [];

            switch (action) {
                case "accepted":
                    updateData = {
                        status: "accepted",
                        provider_response: notes
                    };
                    if (notes) {
                        providerNotes.push({
                            type: "accepted",
                            content: notes,
                            created_at: new Date().toISOString()
                        });
                    }
                    break;

                case "rejected":
                    updateData = {
                        status: "rejected"
                    };
                    if (notes) {
                        providerNotes.push({
                            type: "rejected",
                            content: notes,
                            created_at: new Date().toISOString()
                        });
                    }
                    break;

                case "completed":
                    updateData = {
                        status: "completed",
                        completed_at: new Date()
                    };
                    if (notes) {
                        providerNotes.push({
                            type: "completed",
                            content: notes,
                            created_at: new Date().toISOString()
                        });
                    }

                    // Handle review request
                    if (requestReview) {
                        updateData.review_requested = true;
                        updateData.review_requested_at = new Date();
                        updateData.review_delay_hours = reviewDelayHours || 0;
                    }
                    break;

                case "send-review":
                    updateData = {
                        review_sent_at: new Date()
                    };
                    break;

                default:
                    return this.respond({ error: "Invalid action" }, 400);
            }

            if (providerNotes.length > 0) {
                updateData.provider_notes = providerNotes;
            }

            // Update the request
            const updatedRequest = await prisma.troubleshootingRequest.update({
                where: { id: params.id },
                data: updateData,
                include: {
                    listing: true,
                    user: true
                }
            });

            return this.respond(updatedRequest, 200);
        } catch (error) {
            console.error("Error updating troubleshooting request:", error);
            return this.respond({ error: "Failed to update request" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "PROVIDER",
        validateBody: updateSchema
    }
); 