import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { ListingService } from "@/lib/services/listing.service";
import { ProviderStatsService } from "@/lib/services/provider-stats.service";

export const POST = createHandler(
    async function () {
        // Get the listing for this user
        const listing = await ListingService.getListingByUserId(this.user.id);

        if (!listing) {
            return this.respond({ error: "Listing not found" }, 404);
        }

        try {
            // Refresh the provider stats
            await ProviderStatsService.updateProviderStats(listing.id);

            // Get the updated stats
            const stats = await ProviderStatsService.getProviderStats(listing.id);

            if (!stats) {
                return this.respond({ error: "Failed to refresh stats" }, 500);
            }

            console.log("Stats:", stats);
            // Calculate real-time lead counts
            const now = new Date();
            const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

            const [totalLeads, totalLeads30d] = await Promise.all([
                // Total leads all time
                prisma.quote.count({
                    where: { listing_id: listing.id }
                }),
                // Total leads in last 30 days
                prisma.quote.count({
                    where: {
                        listing_id: listing.id,
                        invited_at: { gte: thirtyDaysAgo }
                    }
                })
            ]);

            // Return enhanced stats with real-time lead counts and pre-calculated complex metrics
            return this.respond(
                {
                    // Response metrics (real-time counts, pre-calculated rates)
                    totalLeads,
                    totalLeads30d,
                    responseRate: Math.round(stats.response_rate_30d),
                    avgResponseTime: Math.round(stats.avg_response_time_30d),
                    recentResponseRate: Math.round(stats.response_rate_30d),

                    // Completion metrics
                    acceptedJobs: stats.accepted_jobs_30d,
                    completedJobs: stats.completed_jobs_30d,
                    completionRate: Math.round(stats.completion_rate_30d),
                    avgCompletionTime: Math.round(stats.avg_completion_time_30d),

                    // Review metrics
                    reviewedJobs: stats.reviewed_jobs_30d,
                    reviewCompletionRate: Math.round(stats.review_completion_rate_30d),

                    // Failure metrics
                    nonResponseCount: stats.non_response_count_30d,
                    nonResponseRate: Math.round(stats.non_response_rate_30d),
                    abandonmentCount: stats.abandonment_count_30d,
                    abandonmentRate: Math.round(stats.abandonment_rate_30d),

                    // Historical data (90-day)
                    historical90d: {
                        totalLeads: stats.total_leads_90d,
                        responseRate: Math.round(stats.response_rate_90d),
                        avgResponseTime: Math.round(stats.avg_response_time_90d),
                        completionRate: Math.round(stats.completion_rate_90d),
                        avgCompletionTime: Math.round(stats.avg_completion_time_90d)
                    },

                    // All-time data
                    allTime: {
                        totalLeads: stats.total_leads_all_time,
                        responseRate: Math.round(stats.response_rate_all_time),
                        avgResponseTime: Math.round(stats.avg_response_time_all_time),
                        completionRate: Math.round(stats.completion_rate_all_time),
                        avgCompletionTime: Math.round(stats.avg_completion_time_all_time)
                    }
                },
                200
            );
        } catch (error) {
            console.error("Error refreshing provider stats:", error);
            return this.respond({ error: "Failed to refresh provider stats" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "PROVIDER"
    }
); 