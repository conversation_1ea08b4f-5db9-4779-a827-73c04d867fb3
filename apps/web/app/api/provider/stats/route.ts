import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { ListingService } from "@/lib/services/listing.service";
import { ProviderStatsService } from "@/lib/services/provider-stats.service";

export const GET = createHandler(
	async function (req, { query }) {
		// get the listing for this user
		const listing = await ListingService.getListingByUserId(this.user.id);

		if (!listing) {
			return this.respond({ error: "Listing not found" }, 404);
		}

		try {
			const stats = await ProviderStatsService.getProviderStats(listing.id);

			if (!stats) {
				return this.respond({ error: "Stats not found" }, 404);
			}

			// Calculate real-time lead counts
			const now = new Date();
			const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

			const [totalLeads, totalLeads30d] = await Promise.all([
				// Total leads all time
				prisma.quote.count({
					where: { listing_id: listing.id }
				}),
				// Total leads in last 30 days
				prisma.quote.count({
					where: {
						listing_id: listing.id,
						invited_at: { gte: thirtyDaysAgo }
					}
				})
			]);

			// Return enhanced stats with real-time lead counts and pre-calculated complex metrics
			return this.respond(
				{
					// Response metrics (use 90-day stats for main display since 30-day might be too small)
					totalLeads,
					totalLeads30d,
					responseRate: Math.round(stats.response_rate_90d),
					avgResponseTime: Math.round(stats.avg_response_time_90d * 60), // Convert to minutes
					recentResponseRate: Math.round(stats.completion_rate_90d), // Show completion rate instead

					// Completion metrics (use 90-day stats for main display)
					acceptedJobs: stats.accepted_jobs_90d,
					completedJobs: stats.completed_jobs_90d,
					completionRate: Math.round(stats.completion_rate_90d),
					avgCompletionTime: Math.round(stats.avg_completion_time_90d),

					// Review metrics (use 90-day stats for main display)
					reviewedJobs: stats.reviewed_jobs_90d,
					reviewCompletionRate: Math.round(stats.review_completion_rate_90d),

					// Failure metrics (use 90-day stats for main display)
					nonResponseCount: stats.non_response_count_90d,
					nonResponseRate: Math.round(stats.non_response_rate_90d),
					abandonmentCount: stats.abandonment_count_90d,
					abandonmentRate: Math.round(stats.abandonment_rate_90d),

					// Historical data (90-day)
					historical90d: {
						totalLeads: stats.total_leads_90d,
						responseRate: Math.round(stats.response_rate_90d),
						avgResponseTime: Math.round(stats.avg_response_time_90d),
						completionRate: Math.round(stats.completion_rate_90d),
						avgCompletionTime: Math.round(stats.avg_completion_time_90d)
					},

					// All-time data
					allTime: {
						totalLeads: stats.total_leads_all_time,
						responseRate: Math.round(stats.response_rate_all_time),
						avgResponseTime: Math.round(stats.avg_response_time_all_time),
						completionRate: Math.round(stats.completion_rate_all_time),
						avgCompletionTime: Math.round(stats.avg_completion_time_all_time)
					}
				},
				200
			);
		} catch (error) {
			console.error("Error fetching provider stats:", error);
			return this.respond({ error: "Failed to fetch provider stats" }, 500);
		}
	},
	{
		requireAuth: true,
		requiredRole: "PROVIDER"
	}
);
