import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest,
    { params }: { params: { id: string } }) {
    try {
        const providerId = params.id;

        if (!providerId) {
            return NextResponse.json(
                { error: "Provider ID is required" },
                { status: 400 }
            );
        }

        const stripeConnect = await prisma.stripeConnection.findFirst({
            where: {
                user_id: providerId
            }
        });
        return NextResponse.json(stripeConnect);
    } catch (error) {
        console.error("Error fetching user:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    }
}