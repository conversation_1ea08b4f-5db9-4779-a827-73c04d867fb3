import { createHandler } from "@/lib/api/baseHandler";
import { ListingService } from "@/lib/services/listing.service";
import { QuoteStatus } from "@rvhelp/database";
import { z } from "zod";
import { JobService } from "../../../../lib/services/job.service";

const querySchema = z.object({
	status: z.enum([
		"PENDING",
		"ACCEPTED",
		"REJECTED",
		"CUSTOMER_REJECTED",
		"IN_PROGRESS",
		"COMPLETED",
		"WITHDRAWN",
		"EXPIRED",
		"all"
	]).optional()
});

export const GET = createHandler(
	async function (req, { query }) {
		// get the listing for this user
		const listing = await ListingService.getListingByUserId(this.user.id);

		if (!listing) {
			return this.respond({ error: "Listing not found" }, 404);
		}

		// Parse status from query parameter
		const status = query.status && query.status !== "all"
			? (query.status as QuoteStatus)
			: undefined;

		const quotes = await JobService.getJobsForProvider(listing.id, status);

		return this.respond(quotes, 200);
	},
	{
		validateQuery: querySchema,
		requireAuth: true,
		requiredRole: "PROVIDER"
	}
); 