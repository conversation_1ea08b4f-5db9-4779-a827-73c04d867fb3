import { createHandler } from "@/lib/api/baseHandler";
import { CertificationService } from "@/lib/services/certification.service";
import { UserService } from "@/lib/services/user.service";

export const POST = createHandler(
    async function (req, { params }) {
        try {
            const { name } = params;
            if (!name) {
                return this.respond({ error: "Certification name is required" }, 400);
            }

            // Get user's listing
            const listing = await UserService.getUserListing(this.user.id);
            if (!listing) {
                return this.respond({ error: "No listing found" }, 404);
            }

            // Complete the certification
            const certificationRecord = await CertificationService.completeCertification(
                listing.id,
                name,
                this.user.id
            );

            return this.respond(certificationRecord);
        } catch (error) {
            console.error("Error completing certification:", error);
            return this.respond({ error: "Failed to complete certification" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "PROVIDER",
    }
); 