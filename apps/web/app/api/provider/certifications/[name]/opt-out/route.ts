import { createHandler } from "@/lib/api/baseHandler";
import { CertificationService } from "@/lib/services/certification.service";
import { UserService } from "@/lib/services/user.service";
import { z } from "zod";

const optOutSchema = z.object({
    reason: z.string().optional(),
});

export const POST = createHandler(
    async function (req, { params }) {
        try {
            const { name } = params;
            if (!name) {
                return this.respond({ error: "Certification name is required" }, 400);
            }

            const { reason } = this.validatedData || {};

            // Get user's listing
            const listing = await UserService.getUserListing(this.user.id);
            if (!listing) {
                return this.respond({ error: "No listing found" }, 404);
            }

            // Opt out of the certification
            const certificationRecord = await CertificationService.optOutOfCertification(
                listing.id,
                name,
                reason
            );

            return this.respond(certificationRecord);
        } catch (error) {
            console.error("Error opting out of certification:", error);
            return this.respond({ error: "Failed to opt out of certification" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "PROVIDER",
        validateBody: optOutSchema,
    }
); 