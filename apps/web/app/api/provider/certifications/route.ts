import { createHandler } from "@/lib/api/baseHandler";
import { CertificationService } from "@/lib/services/certification.service";
import { UserService } from "@/lib/services/user.service";
import { z } from "zod";

const startCertificationSchema = z.object({
    certificationName: z.string().min(1, "Certification name is required"),
});

export const GET = createHandler(
    async function () {
        try {
            // Get user's listing
            const listing = await UserService.getUserListing(this.user.id);
            if (!listing) {
                return this.respond({ error: "No listing found" }, 404);
            }

            // Get all active certifications
            const certifications = await CertificationService.getActiveCertifications();

            // Get provider's certification status for each certification
            const providerCertifications = await Promise.all(
                certifications.map(async (cert) => {
                    const status = await CertificationService.getProviderCertificationStatus(
                        listing.id,
                        cert.name
                    );
                    return {
                        certification: cert,
                        status: status,
                    };
                })
            );

            return this.respond(providerCertifications);
        } catch (error) {
            console.error("Error fetching certifications:", error);
            return this.respond({ error: "Failed to fetch certifications" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "PROVIDER",
    }
);

export const POST = createHandler(
    async function () {
        try {
            const { certificationName } = this.validatedData;

            // Get user's listing
            const listing = await UserService.getUserListing(this.user.id);
            if (!listing) {
                return this.respond({ error: "No listing found" }, 404);
            }

            // Start the certification process
            const certificationRecord = await CertificationService.startCertification(
                listing.id,
                certificationName,
                this.user.id
            );

            return this.respond(certificationRecord);
        } catch (error) {
            console.error("Error starting certification:", error);
            return this.respond({ error: "Failed to start certification" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "PROVIDER",
        validateBody: startCertificationSchema,
    }
); 