import { createHandler } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { CertificationService } from "@/lib/services/certification.service";
import { ListingService } from "@/lib/services/listing.service";
import { z } from "zod";

const warrantyAccessSchema = z.object({
    companyId: z.string().min(1, "Company ID is required"),
    quoteId: z.string().min(1, "Quote ID is required")
});

export const POST = createHandler(
    async function () {
        try {
            const { companyId, quoteId } = this.validatedData;

            // Get the provider's listing
            const listing = await ListingService.getListingByUserId(this.user.id);
            if (!listing) {
                return this.respond({ error: "No listing found" }, 404);
            }

            // Get the warranty request to check company
            const quote = await prisma.quote.findUnique({
                where: { id: quoteId },
                include: {
                    job: {
                        include: {
                            warranty_request: {
                                include: {
                                    company: true
                                }
                            }
                        }
                    }
                }
            });

            if (!quote || !quote.job.warranty_request) {
                return this.respond({ error: "Warranty request not found" }, 404);
            }

            // Map company ID to certification name
            const companyCertificationMap: Record<string, string> = {
                // Add your company IDs and their corresponding certification names here
                // Example: "company-id-1": "keystone-warranty"
            };

            const certificationName = companyCertificationMap[companyId];

            // If no certification is required for this company, provider has access
            if (!certificationName) {
                return this.respond({
                    hasAccess: true,
                    certificationName: null,
                    companyName: quote.job.warranty_request.company.name
                });
            }

            // Check if provider has completed the required certification
            const certificationStatus = await CertificationService.getProviderCertificationStatus(
                listing.id,
                certificationName
            );

            const hasAccess = certificationStatus?.status === "COMPLETED";

            return this.respond({
                hasAccess,
                certificationName,
                certificationStatus: certificationStatus?.status,
                companyName: quote.job.warranty_request.company.name
            });
        } catch (error) {
            console.error("Error checking warranty access:", error);
            return this.respond({ error: "Failed to check warranty access" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "PROVIDER",
        validateBody: warrantyAccessSchema
    }
); 