import { createHandler } from "@/lib/api/baseHandler";
import { RVSGService } from "@/lib/services/rvsg.service";
import { UserService } from "@/lib/services/user.service";

export const POST = createHandler(
    async function () {
        try {
            // Get user's listing
            const listing = await UserService.getUserListing(this.user.id);
            if (!listing) {
                return this.respond({ error: "No listing found" }, 404);
            }

            if (!listing.rvtaa_member_id) {
                return this.respond(
                    { error: "No RVTA member ID associated with this listing" },
                    400
                );
            }

            // Sync with RVSG
            await RVSGService.updateListing(listing);

            // Fetch updated listing
            const updatedListing = await UserService.getUserListing(this.user.id);

            return this.respond(updatedListing);
        } catch (error) {
            console.error("Error syncing RVSG data:", error);
            return this.respond({ error: "Failed to sync with <PERSON>VS<PERSON>" }, 500);
        }
    },
    {
        requireAuth: true,
        requiredRole: "PROVIDER",
    }
); 