import { createHandler } from '@/lib/api/baseHandler';
import prisma from "@/lib/prisma";

export const GET = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract ID from URL: /api/companies/[id]
        const urlParts = this.req.url.split('/');
        const id = urlParts[urlParts.length - 1];

        try {
            // Get the company with its components
            const company = await prisma.company.findUnique({
                where: { id },
                include: {
                    components: true,
                }
            });

            if (!company) {
                return Response.json({ error: 'Company not found' }, { status: 404 });
            }

            // Format the response to match the ExtendedCompany type
            const response = {
                ...company,
                components: company.components.map(component => ({
                    ...component,
                    // Ensure attachments is always an array
                    attachments: component.attachments ? (Array.isArray(component.attachments) ? component.attachments : [component.attachments]) : []
                }))
            };

            return Response.json(response);
        } catch (error) {
            console.error("Error fetching company:", error);
            return Response.json({ error: "Failed to fetch company" }, { status: 500 });
        }
    },
    { 
        requireAuth: true,
        requiredRole: 'PROVIDER'
    }
);
