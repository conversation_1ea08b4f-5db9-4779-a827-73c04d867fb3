import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { invoiceService } from "@/lib/services";
import { ListingService } from "@/lib/services/listing.service";
import { z } from "zod";

const createWarrantyInvoiceSchema = z.object({
    customer_name: z.string().min(1, "Customer name is required"),
    customer_email: z.string().email("Invalid email address"),
    customer_phone: z.string().optional(),
    amount: z.number().min(0, "Amount must be positive"),
    currency: z.string().default("usd"),
    due_date: z
        .string()
        .optional()
        .transform((str) => (str ? new Date(str) : undefined)),
    notes: z.string().optional(),
    items: z
        .array(
            z.object({
                description: z.string().min(1, "Description is required"),
                unit_price: z.number().min(0, "Unit price must be positive"),
                quantity: z.number().optional(),
                amount: z.number().min(0, "Amount must be positive")
            })
        )
        .optional(),
    status: z.string().optional()
});

export const GET = createHandler(
    async function (req, { params }) {
        const { id: warrantyRequestId } = params;

        try {
            // Verify the warranty request exists and belongs to the user's company
            const warrantyRequest = await prisma.warrantyRequest.findFirst({
                where: {
                    id: warrantyRequestId,
                    company: {
                        users: {
                            some: {
                                id: this.user.id
                            }
                        }
                    }
                },
                include: {
                    provider_invoice: true
                }
            });

            if (!warrantyRequest) {
                return this.respond({ error: "Warranty request not found" }, 404);
            }

            return this.respond({ invoice: warrantyRequest.provider_invoice || null });
        } catch (error) {
            console.error("Error fetching warranty invoice:", error);
            return this.respond({ error: "Failed to fetch invoice" }, 500);
        }
    },
    {
        requireAuth: true
    }
);

export const POST = createHandler(
    async function (req, { params }) {
        const { id: warrantyRequestId } = params;

        try {
            // Verify the warranty request exists and belongs to the user's company
            const warrantyRequest = await prisma.warrantyRequest.findFirst({
                where: {
                    id: warrantyRequestId
                }
            });


            if (!warrantyRequest) {

                return this.respond({ error: "Warranty request not found" }, 404);
            }

            // Check if an invoice already exists for this warranty request
            if (warrantyRequest.provider_invoice_id) {
                return this.respond({ error: "Invoice already exists for this warranty request" }, 400);
            }

            // Get the provider's listing
            const listing = await ListingService.getListingByUserId(this.user.id);
            if (!listing) {
                return this.respond({ error: "No listing found" }, 404);
            }

            // Create the invoice
            const invoice = await invoiceService.createInvoice({
                ...this.validatedData,
                provider_id: listing.id,
                warranty_request_id: warrantyRequestId
            });

            // Update the warranty request to link it to the invoice (but don't change status for draft)
            await prisma.warrantyRequest.update({
                where: { id: warrantyRequestId },
                data: {
                    provider_invoice_id: invoice.id
                    // Don't change status - that happens when invoice is actually sent/completed
                }
            });



            return this.respond({ invoice });
        } catch (error) {
            console.error("Error creating warranty invoice:", error);
            if (error instanceof Error) {
                return this.respond({ error: error.message }, 500);
            }
            return this.respond({ error: "Failed to create invoice" }, 500);
        }
    },
    {
        requireAuth: true,
        validateBody: createWarrantyInvoiceSchema
    }
);

export const PATCH = createHandler(
    async function (req, { params }) {
        const { id: warrantyRequestId } = params;

        try {
            // Verify the warranty request exists and belongs to the user's company
            const warrantyRequest = await prisma.warrantyRequest.findFirst({
                where: {
                    id: warrantyRequestId,
                    company: {
                        users: {
                            some: {
                                id: this.user.id
                            }
                        }
                    }
                },
                include: {
                    provider_invoice: true
                }
            });

            if (!warrantyRequest) {
                return this.respond({ error: "Warranty request not found" }, 404);
            }

            if (!warrantyRequest.provider_invoice_id) {
                return this.respond({ error: "No invoice found for this warranty request" }, 404);
            }

            // Update the existing invoice
            const invoice = await invoiceService.updateInvoice(
                warrantyRequest.provider_invoice_id,
                this.validatedData
            );

            return this.respond({ invoice });
        } catch (error) {
            console.error("Error updating warranty invoice:", error);
            if (error instanceof Error) {
                return this.respond({ error: error.message }, 500);
            }
            return this.respond({ error: "Failed to update invoice" }, 500);
        }
    },
    {
        requireAuth: true,
        validateBody: createWarrantyInvoiceSchema
    }
);
