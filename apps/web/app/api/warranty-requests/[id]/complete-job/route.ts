import { create<PERSON><PERSON><PERSON> } from '@/lib/api/baseHandler';
import prisma from "@/lib/prisma";
import { TimelineEventType } from "@rvhelp/database";
import { z } from 'zod';

const completeJobSchema = z.object({
    cause: z.string().min(1, "Cause is required"),
    correction: z.string().min(1, "Correction is required"),
    actual_hours: z.number().min(0.1, "Estimated hours must be greater than 0"),
    provider_invoice_id: z.string().optional(),
    attachments: z.array(z.object({
        id: z.string().optional().nullable(),
        component_id: z.string().optional().nullable(),
        type: z.enum(['image', 'document', 'form']),
        title: z.string(),
        url: z.string(),
        required: z.boolean().optional(),
        completed: z.boolean().optional()
    })).optional(),
    return_details: z
        .object({
            height: z
                .number({
                    required_error: "Height is required",
                    invalid_type_error: "Please enter a valid number"
                })
                .min(1, "Height must have a non-zero value"),
            width: z
                .number({
                    required_error: "Width is required",
                    invalid_type_error: "Please enter a valid number"
                })
                .min(1, "Width must have a non-zero value"),
            depth: z
                .number({
                    required_error: "Depth is required",
                    invalid_type_error: "Please enter a valid number"
                })
                .min(1, "Depth must have a non-zero value"),
            weight: z
                .number({
                    required_error: "Weight is required",
                    invalid_type_error: "Please enter a valid number"
                })
                .min(1, "Weight must have a non-zero value")
        })
        .optional()
        .nullable(),
    update_notes: z.string().optional()
});

export const POST = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract ID from pathname: /api/warranty-requests/[id]/request-authorization
        const url = new URL(this.req.url);
        const pathParts = url.pathname.split('/');
        const id = pathParts[pathParts.length - 2]; // Get the ID from the second-to-last part

        try {
            // Verify the warranty request exists and belongs to the user's company
            const warrantyRequest = await prisma.warrantyRequest.findFirst({
                where: {
                    id,
                },
                include: {
                    company: true
                }
            });

            if (!warrantyRequest) {
                return Response.json({ error: 'Warranty request not found' }, { status: 404 });
            }

            // Update the warranty request with authorization request details
            const updatedRequest = await prisma.warrantyRequest.update({
                where: { id },
                data: {
                    cause: this.validatedData.cause,
                    correction: this.validatedData.correction,
                    actual_hours: this.validatedData.actual_hours,
                    provider_invoice_id: this.validatedData.provider_invoice_id,
                    status: 'INVOICE_CREATED'
                }
            });

            // Create a status update record with attachments in the details
            await prisma.timelineUpdate.create({
                data: {
                    job_id: updatedRequest.job_id,
                    warranty_request_id: id,
                    updated_by_id: this.user.id,
                    event_type: 'INVOICE_CREATED' as TimelineEventType,
                    details: {
                        notes: this.validatedData.update_notes || `Payment requested with ${this.validatedData.actual_hours} actual hours`,
                        attachments: this.validatedData.attachments || []
                    },
                    date: new Date()
                }
            });

            return Response.json(updatedRequest);
        } catch (error) {
            console.error('Error completing warranty request:', error);
            return Response.json(
                { error: 'Failed to request authorization' },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: 'PROVIDER',
        validateBody: completeJobSchema
    }
); 