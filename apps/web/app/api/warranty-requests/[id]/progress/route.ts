import { createHand<PERSON> } from '@/lib/api/baseHandler';
import prisma from "@/lib/prisma";
import { TimelineEventType } from "@rvhelp/database";
import { z } from 'zod';

const updateProgressSchema = z.object({
    attachments: z.array(z.object({
        id: z.string(),
        component_id: z.string().optional(),
        type: z.enum(['image', 'document', 'form']),
        title: z.string(),
        url: z.string(),
        required: z.boolean().optional(),
        completed: z.boolean().optional()
    })).optional(),
    update_notes: z.string().optional()
});

export const POST = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract ID from pathname: /api/warranty-requests/[id]/request-authorization
        const url = new URL(this.req.url);
        const pathParts = url.pathname.split('/');
        const id = pathParts[pathParts.length - 2]; // Get the ID from the second-to-last part

        try {
            // Verify the warranty request exists and belongs to the user's company
            const warrantyRequest = await prisma.warrantyRequest.findFirst({
                where: {
                    id,
                },
                include: {
                    company: true
                }
            });

            if (!warrantyRequest) {
                return Response.json({ error: 'Warranty request not found' }, { status: 404 });
            }

            // Create a status update record with attachments in the details
            const update = await prisma.timelineUpdate.create({
                data: {
                    job_id: warrantyRequest.job_id,
                    warranty_request_id: id,
                    updated_by_id: this.user.id,
                    event_type: 'TECHNICIAN_UPDATED' as TimelineEventType,
                    details: {
                        notes: this.validatedData.update_notes || "Technician updated progress",
                        attachments: this.validatedData.attachments || []
                    },
                    date: new Date()
                }
            });

            return Response.json(update);
        } catch (error) {
            console.error('Error updating warranty request:', error);
            return Response.json(
                { error: 'Failed to request authorization' },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: 'PROVIDER',
        validateBody: updateProgressSchema
    }
); 