import { create<PERSON><PERSON><PERSON> } from '@/lib/api/baseHandler';
import prisma from "@/lib/prisma";
import { emailService, slackService } from '@/lib/services';
import { TimelineEventType, WarrantyRequestStatus } from "@rvhelp/database";
import { z } from 'zod';

const requestAuthorizationSchema = z.object({
    cause: z.string().min(1, "Cause is required"),
    correction: z.string().min(1, "Correction is required"),
    estimated_hours: z.number().min(0.1, "Estimated hours must be greater than 0"),
    attachments: z.array(z.object({
        id: z.string().optional().nullable(),
        component_id: z.string().optional().nullable(),
        type: z.string(),
        title: z.string(),
        url: z.string(),
        required: z.boolean().optional(),
        completed: z.boolean().optional(),
        status_update: z.boolean().optional()
    })).optional(),
    skip_attachments: z.boolean().optional().default(false),
    return_details: z
        .object({
            height: z
                .number({
                    required_error: "Height is required",
                    invalid_type_error: "Please enter a valid number"
                })
                .min(1, "Height must have a non-zero value"),
            width: z
                .number({
                    required_error: "Width is required",
                    invalid_type_error: "Please enter a valid number"
                })
                .min(1, "Width must have a non-zero value"),
            depth: z
                .number({
                    required_error: "Depth is required",
                    invalid_type_error: "Please enter a valid number"
                })
                .min(1, "Depth must have a non-zero value"),
            weight: z
                .number({
                    required_error: "Weight is required",
                    invalid_type_error: "Please enter a valid number"
                })
                .min(1, "Weight must have a non-zero value")
        })
        .optional()
        .nullable(),
    update_notes: z.string().optional()
});

export const POST = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract ID from pathname: /api/warranty-requests/[id]/request-authorization
        const url = new URL(this.req.url);
        const pathParts = url.pathname.split('/');
        const id = pathParts[pathParts.length - 2]; // Get the ID from the second-to-last part

        try {
            // Verify the warranty request exists and belongs to the user's company
            const warrantyRequest = await prisma.warrantyRequest.findFirst({
                where: {
                    id,
                },
            });

            if (!warrantyRequest) {
                return Response.json({ error: 'Warranty request not found' }, { status: 404 });
            }

            // Check if documentation is required, but skip if skip_attachments is true
            const skipAttachments = this.validatedData.skip_attachments || false;
            const requiresDocumentation = skipAttachments ? false : this.validatedData.attachments?.some(attachment => attachment.required && !attachment.completed);

            const newStatus = this.validatedData.estimated_hours <= warrantyRequest.approved_hours && !requiresDocumentation
                ? 'AUTHORIZATION_APPROVED'
                : 'AUTHORIZATION_REQUESTED';

            if (warrantyRequest.requires_return) {
                if (!this.validatedData.return_details) {
                    return Response.json({ error: 'Return details are required' }, { status: 400 });
                }

                // Find the latest TECHNICIAN_UPDATED event with "Return details updated" message
                const latestReturnDetailsUpdate = await prisma.timelineUpdate.findFirst({
                    where: {
                        warranty_request_id: id,
                        event_type: 'TECHNICIAN_UPDATED',
                        details: {
                            path: ['notes'],
                            equals: 'Return details updated'
                        }
                    },
                    orderBy: {
                        date: 'desc'
                    }
                });

                // Only create a new update if return_details are different
                let shouldCreateUpdate = true;
                if (latestReturnDetailsUpdate && latestReturnDetailsUpdate.details) {
                    const existingDetails = latestReturnDetailsUpdate.details as any;
                    const existingReturnDetails = existingDetails.return_details;
                    const newReturnDetails = this.validatedData.return_details;

                    // Compare return_details fields
                    if (existingReturnDetails && newReturnDetails) {
                        const fieldsToCompare = ['height', 'width', 'depth', 'weight'];
                        const hasChanges = fieldsToCompare.some(field =>
                            existingReturnDetails[field] !== newReturnDetails[field]
                        );

                        if (!hasChanges) {
                            shouldCreateUpdate = false;
                        }
                    }
                }

                if (shouldCreateUpdate) {
                    // Create a status update record with attachments in the details
                    await prisma.timelineUpdate.create({
                        data: {
                            job_id: warrantyRequest.job_id,
                            warranty_request_id: id,
                            updated_by_id: this.user.id,
                            event_type: 'TECHNICIAN_UPDATED' as TimelineEventType,
                            details: {
                                notes: `Return details updated`,
                                return_details: this.validatedData.return_details,
                                attachments: this.validatedData.attachments?.filter(a => a.status_update) || []
                            },
                            date: new Date()
                        }
                    });
                }
            }

            // Update the warranty request with authorization request details
            const updatedRequest = await prisma.warrantyRequest.update({
                where: { id },
                data: {
                    cause: this.validatedData.cause,
                    correction: this.validatedData.correction,
                    estimated_hours: this.validatedData.estimated_hours,
                    status: newStatus as WarrantyRequestStatus,
                    attachments: this.validatedData.attachments?.filter(a => !a.status_update) || []
                }
            });

            // Create a status update record with attachments in the details
            await prisma.timelineUpdate.create({
                data: {
                    job_id: warrantyRequest.job_id,
                    warranty_request_id: id,
                    updated_by_id: this.user.id,
                    event_type: 'AUTHORIZATION_REQUESTED' as TimelineEventType,
                    details: {
                        notes: this.validatedData.update_notes || `Authorization requested with ${this.validatedData.estimated_hours} estimated hours`,
                        attachments: this.validatedData.attachments?.filter(a => a.status_update) || []
                    },
                    date: new Date()
                }
            });

            // Send email notification to customer about authorization request
            if (newStatus === "AUTHORIZATION_REQUESTED") {
                try {
                    // Get the full warranty request with company info for email
                    const fullWarrantyRequest = await prisma.warrantyRequest.findUnique({
                        where: { id },
                        include: {
                            company: true,
                            component: true,
                        },
                    });

                    if (fullWarrantyRequest && fullWarrantyRequest.email) {
                        await emailService.sendWarrantyAuthorizationRequestedEmail({
                            to: fullWarrantyRequest.email,
                            customerName: fullWarrantyRequest.first_name || 'Customer',
                            companyName: fullWarrantyRequest.company?.name || 'Unknown Company',
                            rvYear: fullWarrantyRequest.rv_year,
                            rvMake: fullWarrantyRequest.rv_make,
                            rvModel: fullWarrantyRequest.rv_model,
                            rvVin: fullWarrantyRequest.rv_vin,
                            estimatedHours: this.validatedData.estimated_hours,
                            cause: this.validatedData.cause,
                            correction: this.validatedData.correction,
                            updateNotes: this.validatedData.update_notes,
                            componentName: fullWarrantyRequest.component?.type,
                            warrantyRequestId: fullWarrantyRequest.id,
                        });
                    }

                    // Send Slack notification to Keystone reps
                    try {
                        await slackService.notifyWarrantyAuthorizationRequest(
                            fullWarrantyRequest,
                            fullWarrantyRequest?.company?.name || 'Unknown Company',
                        );
                    } catch (slackError) {
                        console.error('Failed to send Slack notification for authorization request:', slackError);
                        // Don't fail the entire request if Slack notification fails
                    }
                } catch (emailError) {
                    console.error('Failed to send authorization request email:', emailError);
                    // Don't fail the entire request if email fails
                }
            }

            if (newStatus === "AUTHORIZATION_APPROVED") {
                // Create a status update record with attachments in the details
                await prisma.timelineUpdate.create({
                    data: {
                        job_id: warrantyRequest.job_id,
                        warranty_request_id: id,
                        updated_by_id: this.user.id,
                        event_type: 'AUTHORIZATION_APPROVED' as TimelineEventType,
                        details: {
                            notes: this.validatedData.update_notes || `Authorization approved with ${this.validatedData.estimated_hours} estimated hours`,
                        },
                        date: new Date()
                    }
                });

            }

            return Response.json(updatedRequest);
        } catch (error) {
            console.error('Error requesting authorization:', error);
            return Response.json(
                { error: 'Failed to request authorization' },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: 'PROVIDER',
        validateBody: requestAuthorizationSchema
    }
); 