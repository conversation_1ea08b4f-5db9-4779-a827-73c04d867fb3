import { createH<PERSON><PERSON> } from '@/lib/api/baseHandler';
import prisma from "@/lib/prisma";
import { z } from 'zod';

const acknowledgeAttachmentsSchema = z.object({
    attachments_acknowledged: z.boolean()
});

export const PATCH = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract ID from pathname: /api/warranty-requests/[id]/acknowledge-attachments
        const url = new URL(this.req.url);
        const pathParts = url.pathname.split('/');
        const id = pathParts[pathParts.length - 2]; // Get the ID from the second-to-last part

        try {
            // Verify the warranty request exists and belongs to the user's company
            const warrantyRequest = await prisma.warrantyRequest.findFirst({
                where: {
                    id,
                },
            });

            if (!warrantyRequest) {
                return Response.json({ error: 'Warranty request not found' }, { status: 404 });
            }

            // Update the warranty request to mark attachments as acknowledged
            const updatedWarrantyRequest = await prisma.warrantyRequest.update({
                where: { id },
                data: {
                    attachments_acknowledged: this.validatedData.attachments_acknowledged
                },
                include: {
                    company: true,
                    job: true
                }
            });

            return Response.json(updatedWarrantyRequest);
        } catch (error) {
            console.error('Error acknowledging warranty request attachments:', error);
            return Response.json(
                { error: 'Failed to acknowledge attachments' },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: 'PROVIDER',
        validateBody: acknowledgeAttachmentsSchema
    }
); 