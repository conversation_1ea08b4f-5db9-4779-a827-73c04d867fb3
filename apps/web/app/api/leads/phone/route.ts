import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { BlacklistService } from "@/lib/services/blacklist.service";
import { z } from "zod";

const phoneLeadSchema = z.object({
	listing_id: z.string(),
	first_name: z.string().min(1, "First name is required"),
	last_name: z.string().min(1, "Last name is required"),
	email: z.string().email("Invalid email address"),
	phone: z.string().min(1, "Phone number is required"),
	contact_preference: z.enum(["sms", "phone", "email"]),
	category: z.string().min(1, "Category is required"),
	source: z.enum(["web", "mobile"]).default("web"),
	type: z.enum(["message", "phone"]).default("phone"),
	location: z
		.object({
			address: z.string().optional(),
			city: z.string().optional(),
			state: z.string().optional(),
			zip: z.string().optional(),
			country: z.string().optional(),
			latitude: z.number().optional(),
			longitude: z.number().optional()
		})
		.optional(),
	message: z.string().min(1, "Message is required"),
	// Add RV details fields
	rv_type: z.string().optional(),
	rv_make: z.string().optional(),
	rv_model: z.string().optional(),
	rv_year: z.number()
		.min(1950, "RV year must be 1950 or later")
		.max(new Date().getFullYear(), `RV year cannot be later than ${new Date().getFullYear()}`)
		.optional()
});

export const POST = createHandler(
	async function (req, { validatedData }) {
		// Check if email is blacklisted before processing
		const emailCheck = await BlacklistService.checkEmailAccess(validatedData.email);
		if (emailCheck.isBlacklisted) {
			return Response.json(
				{
					success: false,
					error: "Lead submission blocked",
					message: emailCheck.message || "Email address is not allowed to submit leads"
				},
				{ status: 403 }
			);
		}

		// Check if user ID is blacklisted
		const userCheck = await BlacklistService.checkUserAccess(this.user.id);
		if (userCheck.isBlacklisted) {
			return Response.json(
				{
					success: false,
					error: "Lead submission blocked",
					message: userCheck.message || "User account is not allowed to submit leads"
				},
				{ status: 403 }
			);
		}

		// Get the listing to verify it exists
		const listing = await prisma.listing.findUnique({
			where: { id: validatedData.listing_id }
		});

		if (!listing) {
			return Response.json({ error: "Listing not found" }, { status: 404 });
		}

		// Get user's RV details
		const user = await prisma.user.findUnique({
			where: { id: this.user.id },
			select: {
				rv_details: true
			}
		});

		// Format RV details if available
		let messageWithRvDetails = validatedData.message;
		if (user?.rv_details) {
			const rvDetails = user.rv_details as {
				year?: string | number;
				make?: string;
				model?: string;
				type?: string;
			};
			if (rvDetails.year || rvDetails.make || rvDetails.model) {
				const rvInfo = [
					rvDetails.year && `Year: ${rvDetails.year}`,
					rvDetails.make && `Make: ${rvDetails.make}`,
					rvDetails.model && `Model: ${rvDetails.model}`,
					rvDetails.type && `Type: ${rvDetails.type}`
				]
					.filter(Boolean)
					.join("\n");

				messageWithRvDetails = `${messageWithRvDetails}\n\nRV Details:\n${rvInfo}`;
			}
		}

		// Create the lead record
		const lead = await prisma.lead.create({
			data: {
				...validatedData,
				message: messageWithRvDetails,
				user_id: this.user.id,
				contact_preference: "phone", // Always phone for this route
				status: "sent", // Mark as sent since no email needs to be sent
				sent_at: new Date() // Set sent time to now
			}
		});

		return Response.json({
			success: true,
			lead_id: lead.id
		});
	},
	{
		validateBody: phoneLeadSchema,
		requireAuth: true
	}
);
