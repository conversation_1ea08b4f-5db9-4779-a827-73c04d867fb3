import config from "@/config";
import { createH<PERSON><PERSON> } from "@/lib/api/baseHandler";
import { z } from "zod";

const distanceSchema = z.object({
    origin: z.object({
        lat: z.number(),
        lng: z.number()
    }),
    destination: z.object({
        lat: z.number(),
        lng: z.number()
    })
});

export const POST = createHandler(
    async function (req, { validatedData }) {
        const { origin, destination } = validatedData;

        try {
            const response = await fetch(
                `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${origin.lat},${origin.lng}&destinations=${destination.lat},${destination.lng}&mode=driving&key=${config.google.serverApiKey}`
            );

            if (!response.ok) {
                throw new Error(`Google Maps API responded with status: ${response.status}`);
            }

            const data = await response.json();

            // Check if the API returned an error
            if (data.status !== "OK") {
                throw new Error(`Google Maps API error: ${data.status} - ${data.error_message || "Unknown error"}`);
            }

            // Check if we have valid results
            if (!data.rows?.[0]?.elements?.[0]) {
                throw new Error("No distance data returned from Google Maps API");
            }

            const element = data.rows[0].elements[0];

            // Check if the specific element has an error
            if (element.status !== "OK") {
                throw new Error(`Distance calculation failed: ${element.status}`);
            }

            const distanceInMeters = element.distance.value;
            const distanceInMiles = distanceInMeters * 0.000621371;

            return this.respond({
                distance: distanceInMiles,
                duration: element.duration?.text || null,
                status: "success"
            });
        } catch (error) {
            console.error("Error calculating driving distance:", error);
            return this.respond(
                {
                    error: "Failed to calculate distance",
                    message: error instanceof Error ? error.message : "Unknown error"
                },
                500
            );
        }
    },
    {
        validateBody: distanceSchema
    }
); 