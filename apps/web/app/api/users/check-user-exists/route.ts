import { createHandler } from "@/lib/api/baseHandler";
import { WarrantyRequestService } from "@/lib/services/warranty-request.service";
import { z } from "zod";

const schema = z.object({
    email: z.string().email("Please enter a valid email address")
});

export const POST = createHandler(
    async function () {
        const { email } = this.validatedData;

        const userState = await WarrantyRequestService.checkUserState(email);

        return this.respond(userState);
    },
    {
        validateBody: schema
    }
);