import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { WarrantyRequestService } from "@/lib/services/warranty-request.service";
import { z } from "zod";

const schema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    email: z.string().email("Please enter a valid email address"),
    password: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z.string(),
    newsletterSourceTag: z.string().optional(),
    companyName: z.string().optional(),
    sms_opt_in: z.boolean().default(true)
}).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"]
});

export const POST = createHandler(
    async function () {
        const { firstName, lastName, email, password, newsletterSourceTag, companyName, sms_opt_in } = this.validatedData;

        try {
            const result = await WarrantyRequestService.setWarrantyPassword({
                firstName,
                lastName,
                email,
                password,
                newsletterSourceTag,
                companyName,
                sms_opt_in
            });

            return this.respond(result);
        } catch (error: any) {
            if (error.message === "User not found") {
                return this.respond(
                    { error: error.message },
                    404
                );
            }
            if (error.message === "User already has a password set. Please use login instead.") {
                return this.respond(
                    { error: error.message },
                    400
                );
            }

            console.error("Error setting warranty password:", error);
            return this.respond(
                { error: "Failed to set password" },
                500
            );
        }
    },
    {
        validateBody: schema
    }
);