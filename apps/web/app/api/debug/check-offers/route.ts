import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";

export const GET = createHandler(async (request: Request) => {
    const url = new URL(request.url);
    const email = url.searchParams.get('email');
    const userId = url.searchParams.get('userId');

    if (!email && !userId) {
        return this.respond({ error: 'Need either email or userId' }, 400);
    }

    let offers = [];
    let user = null;

    if (email) {
        // Find offers by email
        offers = await prisma.membershipOffer.findMany({
            where: {
                email: {
                    equals: email,
                    mode: "insensitive"
                }
            },
            orderBy: {
                created_at: "desc"
            }
        });

        // Also find user by email
        user = await prisma.user.findFirst({
            where: {
                email: {
                    equals: email,
                    mode: "insensitive"
                }
            },
            select: {
                id: true,
                email: true,
                membership_level: true,
                created_at: true
            }
        });
    }

    if (userId) {
        // Find offers by user ID
        const userOffers = await prisma.membershipOffer.findMany({
            where: {
                user_id: userId
            },
            orderBy: {
                created_at: "desc"
            }
        });
        offers = [...offers, ...userOffers];

        if (!user) {
            user = await prisma.user.findUnique({
                where: { id: userId },
                select: {
                    id: true,
                    email: true,
                    membership_level: true,
                    created_at: true
                }
            });
        }
    }

    // Also check recent reviews for this user/email
    let reviews = [];
    if (email) {
        reviews = await prisma.review.findMany({
            where: {
                email: {
                    equals: email,
                    mode: "insensitive"
                }
            },
            orderBy: {
                created_at: "desc"
            },
            take: 5,
            select: {
                id: true,
                email: true,
                user_id: true,
                status: true,
                overall: true,
                created_at: true
            }
        });
    }

    return this.respond({
        user,
        offers,
        reviews,
        debug: {
            searchedEmail: email,
            searchedUserId: userId,
            offerCount: offers.length,
            reviewCount: reviews.length
        }
    });
});