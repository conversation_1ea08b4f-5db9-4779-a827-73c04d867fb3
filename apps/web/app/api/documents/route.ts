import config from '@/config';
import { prisma } from '@/lib/prisma';
import { s3 } from '@/lib/s3';
import { WarrantyAttachment } from '@/types/warranty';
import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { JsonValue } from '@prisma/client/runtime/library';
import { NextRequest, NextResponse } from 'next/server';
import { PDFCheckBox, PDFDocument, PDFDropdown, PDFRadioGroup, PDFTextField } from 'pdf-lib';
import { z } from 'zod';


// Helper function to convert stream to buffer
async function streamToBuffer(stream: any): Promise<Buffer> {
  const chunks: Uint8Array[] = [];
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks);
}

const fillPdfSchema = z.object({
  warrantyRequestId: z.string(),
  documentId: z.string(),
  formFields: z.array(z.object({
    name: z.string(),
    type: z.string(),
    index: z.number(),
    value: z.union([z.string(), z.boolean()]).optional()
  }))
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { documentId, formFields, warrantyRequestId } = fillPdfSchema.parse(body);

    // Get the original document
    const originalDocument = await prisma.document.findUnique({
      where: { id: documentId }
    });

    if (!originalDocument) {
      return NextResponse.json({ error: 'Original document not found' }, { status: 404 });
    }

    // Extract S3 key from URL
    const url = new URL(originalDocument.url);
    const s3Key = url.pathname.substring(1); // Remove leading slash

    // Download the original PDF from S3
    const getCommand = new GetObjectCommand({
      Bucket: config.aws.bucket,
      Key: s3Key
    });

    const response = await s3.send(getCommand);

    if (!response.Body) {
      throw new Error('No file content received from S3');
    }

    // Convert stream to buffer
    const pdfBytes = await streamToBuffer(response.Body);

    // Load the PDF document
    const pdfDoc = await PDFDocument.load(pdfBytes);

    // Get the form from the document
    const form = pdfDoc.getForm();

    if (!form) {
      return NextResponse.json({ error: 'No form fields found in this PDF' }, { status: 400 });
    }

    let appliedCount = 0;
    let errorCount = 0;

    // Apply values to form fields
    for (const fieldData of formFields) {
      // Skip fields without values
      if (!fieldData.value || fieldData.value === null) {
        continue;
      }

      try {
        const field = form.getField(fieldData.name);

        if (fieldData.type === 'PDFTextField') {
          (field as PDFTextField).setText(String(fieldData.value));
        } else if (fieldData.type === 'PDFCheckBox') {
          const checkBox = field as PDFCheckBox;
          const lowerValue = String(fieldData.value).toLowerCase();
          if (lowerValue === 'true' || lowerValue === 'checked') {
            checkBox.check();
          } else if (lowerValue === 'false' || lowerValue === 'unchecked') {
            checkBox.uncheck();
          } else {
            console.log(`Warning: Invalid checkbox value "${fieldData.value}" for "${fieldData.name}"`);
            errorCount++;
            continue;
          }
        } else if (fieldData.type === 'PDFDropdown') {
          (field as PDFDropdown).select(String(fieldData.value));
        } else if (fieldData.type === 'PDFRadioGroup') {
          (field as PDFRadioGroup).select(String(fieldData.value));
        } else {
          console.log(`Warning: Unsupported field type "${fieldData.type}" for "${fieldData.name}"`);
          errorCount++;
          continue;
        }

        appliedCount++;

      } catch (error) {
        console.log(`Error setting "${fieldData.name}": ${error.message}`);
        errorCount++;
      }
    }

    // Save the filled PDF
    const filledPdfBytes = await pdfDoc.save();

    // Generate new filename for the filled PDF
    const originalBaseName = s3Key.split('/').pop()?.split('.')[0] || 'document';
    const timestamp = new Date().getTime();
    const newS3Key = `filled-documents/${timestamp}-${originalBaseName}_filled.pdf`;

    // Upload the filled PDF to S3
    const putCommand = new PutObjectCommand({
      Bucket: config.aws.bucket,
      Key: newS3Key,
      Body: filledPdfBytes,
      ContentType: 'application/pdf',
      ACL: 'public-read'
    });

    await s3.send(putCommand);

    // Create new document record
    const newDocument = await prisma.document.create({
      data: {
        url: `https://${config.aws.bucket}.s3.${config.aws.region}.amazonaws.com/${newS3Key}`,
        type: "form",
        title: originalDocument.title,
        form_fields: JSON.stringify(formFields),
        original_id: documentId
      }
    });

    // Update the warranty request with the new document id
    const warrantyRequest = await prisma.warrantyRequest.findUnique({
      where: { id: warrantyRequestId }
    });
    if (!warrantyRequest) {
      return NextResponse.json({ error: 'Warranty request not found' }, { status: 404 });
    }
    const warrantyAttachments = warrantyRequest.attachments as unknown as WarrantyAttachment[];
    const originalAttachmentIndex = warrantyAttachments.findIndex((attachment) => attachment.id === documentId);
    if (originalAttachmentIndex === -1) {
      return NextResponse.json({ error: 'Original attachment not found' }, { status: 404 });
    }
    const originalAttachment = warrantyAttachments[originalAttachmentIndex];
    // Replace the original attachment with the new document info
    warrantyAttachments[originalAttachmentIndex] = {
      id: newDocument.id,
      type: "form",
      title: originalDocument.title,
      url: `https://${config.aws.bucket}.s3.${config.aws.region}.amazonaws.com/${newS3Key}`,
      required: originalAttachment.required,
      component_id: originalAttachment.component_id,
      completed: true
    };
    await prisma.warrantyRequest.update({
      where: { id: warrantyRequestId },
      data: {
        attachments: warrantyAttachments as unknown as JsonValue
      }
    });

    return NextResponse.json({
      success: true,
      document: newDocument,
      stats: {
        appliedCount,
        errorCount,
        totalFields: formFields.length
      }
    });

  } catch (error) {
    console.error('Error filling PDF:', error);
    return NextResponse.json(
      { error: 'Failed to fill PDF', details: String(error) },
      { status: 500 }
    );
  }
} 