import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { PDFDocument, PDFTextField, PDFCheckBox, PDFDropdown, PDFRadioGroup } from 'pdf-lib';
import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { s3 } from '@/lib/s3';
import config from '@/config';
import { FormField } from '@/types/warranty';

// Helper function to convert stream to buffer
async function streamToBuffer(stream: any): Promise<Buffer> {
    const chunks: Uint8Array[] = [];
    for await (const chunk of stream) {
        chunks.push(chunk);
    }
    return Buffer.concat(chunks);
}

const fillPdfSchema = z.object({
    formFields: z.array(z.object({
        name: z.string(),
        type: z.string(),
        index: z.number(),
        value: z.union([z.string(), z.boolean()]).optional()
    }))
});

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
    const { id } = params;
    if (!id) {
        return NextResponse.json({ error: 'Missing document id' }, { status: 400 });
    }

    try {
        const document = await prisma.document.findUnique({
            where: { id },
        });
        if (!document) {
            return NextResponse.json({ error: 'Document not found' }, { status: 404 });
        }
        return NextResponse.json(document);
    } catch (error) {
        return NextResponse.json({ error: 'Failed to fetch document', details: String(error) }, { status: 500 });
    }
}

export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
    const { id } = params;
    if (!id) {
        return NextResponse.json({ error: 'Missing document id' }, { status: 400 });
    }
    try {
        const body = await req.json();
        const { formFields } = fillPdfSchema.parse(body);

        // Get the original document
        const originalDocument = await prisma.document.findUnique({
            where: { id }
        });
        if (!originalDocument) {
            return NextResponse.json({ error: 'Document not found' }, { status: 404 });
        }

        // Extract S3 key from URL
        const url = new URL(originalDocument.url);
        const s3Key = url.pathname.substring(1); // Remove leading slash

        // Download the original PDF from S3
        const getCommand = new GetObjectCommand({
            Bucket: config.aws.bucket,
            Key: s3Key
        });
        const response = await s3.send(getCommand);
        if (!response.Body) {
            throw new Error('No file content received from S3');
        }
        // Convert stream to buffer
        const pdfBytes = await streamToBuffer(response.Body);
        // Load the PDF document
        const pdfDoc = await PDFDocument.load(pdfBytes);
        // Get the form from the document
        const form = pdfDoc.getForm();
        if (!form) {
            return NextResponse.json({ error: 'No form fields found in this PDF' }, { status: 400 });
        }
        let appliedCount = 0;
        let errorCount = 0;
        // Apply values to form fields
        for (const fieldData of formFields) {
            if (!fieldData.value || fieldData.value === null) {
                continue;
            }
            try {
                const field = form.getField(fieldData.name);
                if (fieldData.type === 'PDFTextField') {
                    (field as PDFTextField).setText(String(fieldData.value));
                } else if (fieldData.type === 'PDFCheckBox') {
                    const checkBox = field as PDFCheckBox;
                    const lowerValue = String(fieldData.value).toLowerCase();
                    if (lowerValue === 'true' || lowerValue === 'checked') {
                        checkBox.check();
                    } else if (lowerValue === 'false' || lowerValue === 'unchecked') {
                        checkBox.uncheck();
                    } else {
                        console.log(`Warning: Invalid checkbox value "${fieldData.value}" for "${fieldData.name}"`);
                        errorCount++;
                        continue;
                    }
                } else if (fieldData.type === 'PDFDropdown') {
                    (field as PDFDropdown).select(String(fieldData.value));
                } else if (fieldData.type === 'PDFRadioGroup') {
                    (field as PDFRadioGroup).select(String(fieldData.value));
                } else {
                    console.log(`Warning: Unsupported field type "${fieldData.type}" for "${fieldData.name}"`);
                    errorCount++;
                    continue;
                }
                appliedCount++;
            } catch (error) {
                console.log(`Error setting "${fieldData.name}": ${error.message}`);
                errorCount++;
            }
        }
        // Save the filled PDF
        const filledPdfBytes = await pdfDoc.save();
        // Generate new filename for the filled PDF
        const originalBaseName = s3Key.split('/').pop()?.split('.')[0] || 'document';
        const timestamp = new Date().getTime();
        const newS3Key = `filled-documents/${timestamp}-${originalBaseName}_filled.pdf`;
        // Upload the filled PDF to S3
        const putCommand = new PutObjectCommand({
            Bucket: config.aws.bucket,
            Key: newS3Key,
            Body: filledPdfBytes,
            ContentType: 'application/pdf',
            ACL: 'public-read'
        });
        await s3.send(putCommand);
        // Update the existing document record
        const updatedDocument = await prisma.document.update({
            where: { id },
            data: {
                url: `https://${config.aws.bucket}.s3.${config.aws.region}.amazonaws.com/${newS3Key}`,
                form_fields: JSON.stringify(formFields)
            }
        });
        return NextResponse.json({
            success: true,
            document: updatedDocument,
            stats: {
                appliedCount,
                errorCount,
                totalFields: formFields.length
            }
        });
    } catch (error) {
        console.error('Error filling PDF:', error);
        return NextResponse.json(
            { error: 'Failed to fill PDF', details: String(error) },
            { status: 500 }
        );
    }
} 