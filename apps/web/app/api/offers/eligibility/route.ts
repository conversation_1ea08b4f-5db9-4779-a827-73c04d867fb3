import { createHandler } from "@/lib/api/baseHandler";
import { OfferService } from "@/lib/services/offer.service";

export const GET = createHandler(
  async function () {
    if (!this.user) {
      return this.respond({ error: "Unauthorized" }, { status: 401 });
    }

    const eligibility = await OfferService.isUserEligibleForAnnualOffer(this.user.id);

    return this.respond({
      eligible: eligibility.eligible,
      reason: eligibility.reason,
      offer: eligibility.offer,
    });
  }
); 