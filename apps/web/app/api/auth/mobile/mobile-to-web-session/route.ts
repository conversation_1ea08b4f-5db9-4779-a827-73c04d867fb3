import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { MobileAuthService } from "@/lib/services/mobile-auth.service";
import { NextResponse } from "next/server";
import { z } from "zod";

const schema = z.object({
	token: z.string()
});

export const POST = createHandler(
	async function () {
		try {
			const { token } = this.validatedData;

			const result =
				await MobileAuthService.createWebSessionFromMobileToken(token);

			return NextResponse.json({
				success: true,
				message: "Web session created successfully",
				email: result.user.email,
				signInToken: result.signInToken,
				redirectUrl: result.redirectUrl
			});
		} catch (error) {
			console.error("Mobile to web session error:", error);

			if (
				error.name === "JsonWebTokenError" ||
				error.name === "TokenExpiredError"
			) {
				return NextResponse.json(
					{ error: "Invalid or expired token" },
					{ status: 401 }
				);
			}

			if (error.message === "User not found") {
				return NextResponse.json({ error: "User not found" }, { status: 404 });
			}

			return NextResponse.json(
				{ error: "Internal server error" },
				{ status: 500 }
			);
		}
	},
	{ validateBody: schema }
);
