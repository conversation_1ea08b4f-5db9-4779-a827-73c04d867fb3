import { createH<PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { generateToken } from "@/lib/utils/token";
import { z } from "zod";

const schema = z.object({
    serviceRequestId: z.string(),
    email: z.string().email()
});

export const POST = createHandler(
    async function () {
        const { serviceRequestId, email } = this.validatedData;

        // Get the service request with details
        const serviceRequest = await prisma.job.findUnique({
            where: { id: serviceRequestId },
            select: {
                id: true,
                email: true,
                user_id: true,
                first_name: true,
                last_name: true,
                category: true,
                message: true,
                rv_make: true,
                rv_model: true,
                rv_year: true,
                rv_type: true
            }
        });

        console.log("serviceRequest", serviceRequest);
        if (!serviceRequest) {
            return this.respond(
                { error: "Service request not found" },
                404
            );
        }

        if (serviceRequest.email.toLowerCase() !== email.toLowerCase()) {
            return this.respond(
                { error: "Email does not match service request" },
                403
            );
        }

        // Check if user exists with case-insensitive email search
        const user = await prisma.user.findFirst({
            where: {
                email: {
                    equals: email,
                    mode: "insensitive"
                }
            }
        });

        if (!user) {
            return this.respond({
                error: "User not found"
            }, 404);
        }

        // Check if user has already logged in (has set up password)
        if (user.last_login) {
            return this.respond({
                error: "User has already logged in before. Please use the login page."
            }, 400);
        }

        // Check if a password setup email was sent recently (within last 5 minutes)
        const recentToken = await prisma.verificationToken.findFirst({
            where: {
                user_id: user.id,
                type: "password_setup",
                created_at: {
                    gte: new Date(Date.now() - 5 * 60 * 1000) // 5 minutes ago
                }
            }
        });

        if (recentToken) {
            return this.respond({
                success: true,
                message: "Password setup instructions were recently sent to your email"
            });
        }

        // Delete existing password reset tokens
        await prisma.verificationToken.deleteMany({
            where: {
                user_id: user.id,
                type: "password_setup"
            }
        });

        // Create new password setup token
        const setupToken = generateToken();
        const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

        await prisma.verificationToken.create({
            data: {
                token: setupToken,
                expires: tokenExpiry,
                user_id: user.id,
                type: "password_setup"
            }
        });

        // Send custom service request password setup email
        await emailService.sendServiceRequestPasswordSetupEmail({
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            serviceRequestId: serviceRequest.id,
            token: setupToken,
            rvDetails: {
                year: serviceRequest.rv_year,
                make: serviceRequest.rv_make,
                model: serviceRequest.rv_model,
                type: serviceRequest.rv_type
            },
            category: serviceRequest.category,
            message: serviceRequest.message
        });

        return this.respond({
            success: true,
            message: "Password setup instructions sent to your email"
        });
    },
    {
        validateBody: schema
    }
); 