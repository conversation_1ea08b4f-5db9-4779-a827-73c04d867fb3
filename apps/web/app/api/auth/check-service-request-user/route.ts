import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const schema = z.object({
  serviceRequestId: z.string()
});

export const POST = createHandler(
  async function () {
    const { serviceRequestId } = this.validatedData;

    // Get the service request to find the email
    const serviceRequest = await prisma.job.findUnique({
      where: { id: serviceRequestId },
      select: { email: true }
    });

    if (!serviceRequest) {
      return this.respond(
        { error: "Service request not found" },
        404
      );
    }

    // Check if user exists with case-insensitive email search
    const user = await prisma.user.findFirst({
      where: {
        email: {
          equals: serviceRequest.email,
          mode: "insensitive"
        }
      },
      select: {
        id: true,
        email: true,
        password: true,
        last_login: true, // Add last_login to check if user has ever logged in
        first_name: true,
        last_name: true
      }
    });

    if (!user) {
      return this.respond({
        userExists: false,
        hasPassword: false,
        needsPasswordSetup: true,
        email: serviceRequest.email
      });
    }

    // User exists but has never logged in (auto-created account)
    const hasNeverLoggedIn = !user.last_login;

    return this.respond({
      userExists: true,
      hasPassword: !!user.password,
      needsPasswordSetup: hasNeverLoggedIn, // True if user has never logged in
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name
    });
  },
  {
    validateBody: schema
  }
); 