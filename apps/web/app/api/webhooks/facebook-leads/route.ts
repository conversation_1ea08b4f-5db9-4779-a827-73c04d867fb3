import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import { adminLogger } from "@/lib/services/admin-log.service";
import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";
import { z } from "zod";

// Zapier webhook schema for Facebook Lead Ads
const zapierFacebookLeadSchema = z.object({
    // Zapier typically sends the data in a flattened structure
    email: z.string().email().optional(),
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    form_name: z.string().optional(),

    // Facebook Lead Ads specific fields that Zapier might send
    form_id: z.string().optional(),
}).catchall(z.any()); // Allow for any additional fields that Zapier might send

// Zapier webhook authentication (optional - can use API key or basic auth)
function verifyZapierWebhook(req: Request): boolean {
    const apiKey = req.headers.get("x-api-key");
    const expectedApiKey = process.env.ZAPIER_WEBHOOK_API_KEY;

    // In development or test mode, allow access if no API key is configured
    if (
        (process.env.NODE_ENV === "development" || process.env.NODE_ENV === "test") &&
        !expectedApiKey
    ) {
        return true;
    }

    // Check for API key in header
    if (apiKey && expectedApiKey) {
        return apiKey === expectedApiKey;
    }

    return false;
}

async function handler(req: Request) {
    try {
        const protocol = req.headers.get("x-forwarded-proto");
        const userAgent = req.headers.get("user-agent");

        // Check HTTPS first
        if (protocol !== "https") {
            adminLogger.log("[Webhook] Rejected: Non-HTTPS request");
            return this.respond({ error: "HTTPS required" }, 400);
        }

        // Verify Zapier webhook authentication
        if (!verifyZapierWebhook(req)) {
            adminLogger.log("[Webhook] Rejected: Invalid authentication", {
                userAgent,
                hasApiKey: !!req.headers.get("x-api-key"),
            });
            return this.respond({ error: "Unauthorized" }, 401);
        }

        const leadData = this.validatedData;

        // Process the lead data
        await processZapierFacebookLead(leadData);

        return this.respond({ received: true }, 200);
    } catch (error) {
        adminLogger.log("[Webhook] Error processing Zapier Facebook leads webhook:", error);
        return this.respond({ error: "Internal server error" }, 500);
    }
}

async function processZapierFacebookLead(leadData: z.infer<typeof zapierFacebookLeadSchema>) {
    try {
        // Extract lead information from Zapier payload
        const leadInfo = extractZapierLeadInfo(leadData);

        if (!leadInfo.email) {
            adminLogger.log("[Webhook] Lead missing email, skipping", {
            });
            return;
        }

        // Create newsletter subscriber with Zapier/Facebook-specific tags
        const tags = [
            "source: facebook lead ads",
            "consumer action: submitted facebook form"
        ];

        // Add form-specific tags based on form ID
        if (leadData.form_id) {
            tags.push(`facebook form id: ${leadData.form_id}`);
            tags.push(`facebook form name: ${leadData.form_name}`);
        }

        await EmailNewsletterService.syncNewsletterSubscriber({
            email: leadInfo.email,
            first_name: leadInfo.first_name || "",
            last_name: leadInfo.last_name || "",
            user: null, // No user object for Facebook leads
            tags
        });

    } catch (error) {
        adminLogger.log("[Webhook] Error processing Zapier Facebook lead:", error);
        // Don't throw error to prevent webhook failure
    }
}

function extractZapierLeadInfo(leadData: z.infer<typeof zapierFacebookLeadSchema>) {
    const leadInfo: {
        email?: string;
        first_name?: string;
        last_name?: string;
        phone?: string;
        message?: string;
        [key: string]: string | undefined;
    } = {};

    // Map Zapier fields to our standard format
    if (leadData.email) leadInfo.email = leadData.email;
    if (leadData.first_name) leadInfo.first_name = leadData.first_name;
    if (leadData.last_name) leadInfo.last_name = leadData.last_name;
    if (leadData.phone) leadInfo.phone = leadData.phone;

    // Handle message field variations
    if (leadData.message) {
        leadInfo.message = leadData.message;
    } else if (leadData.comments) {
        leadInfo.message = leadData.comments;
    } else if (leadData.additional_information) {
        leadInfo.message = leadData.additional_information;
    }

    // Store any additional fields for potential future use
    Object.entries(leadData).forEach(([key, value]) => {
        if (!leadInfo[key] && typeof value === 'string' && value.trim()) {
            leadInfo[key] = value;
        }
    });

    return leadInfo;
}

export const POST = createHandler(handler, {
    validateBody: zapierFacebookLeadSchema
});

// Force Node.js runtime for better security features
export const runtime = "nodejs"; 