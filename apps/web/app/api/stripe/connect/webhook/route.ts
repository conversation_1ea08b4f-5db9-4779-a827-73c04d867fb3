import prisma from "@/lib/prisma";
import { adminLogger } from "@/lib/services/admin-log.service";
import { stripe } from "@/lib/stripe";
import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";
import { headers } from "next/headers";
import { NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

const webhookSecret = process.env.STRIPE_CONNECT_WEBHOOK_SECRET;

// Helper function to handle Prisma errors
function handlePrismaError(error: unknown): { status: number; message: string } {
    // Check if it's a Prisma error by checking the error code directly
    if (error && typeof error === 'object' && 'code' in error) {
        const prismaError = error as { code: string };
        switch (prismaError.code) {
            case 'P2025':
                // Record not found - return 200 to acknowledge the webhook
                return { status: 200, message: 'Record not found - webhook acknowledged' };
            case 'P2002':
                // Unique constraint violation
                return { status: 409, message: 'Conflict - record already exists' };
            case 'P2003':
                // Foreign key constraint violation
                return { status: 400, message: 'Invalid reference' };
            default:
                // Other Prisma errors
                return { status: 500, message: 'Database error' };
        }
    }

    // Non-Prisma errors
    return { status: 500, message: 'Internal server error' };
}

export async function POST(request: Request) {
    const body = await request.text();
    const headersList = headers();
    const signature = headersList.get("stripe-signature");

    if (!signature || !webhookSecret) {
        adminLogger.log("Missing signature or webhook secret", {
            "signature": signature,
            "webhookSecret": webhookSecret
        }, "error");
        return NextResponse.json(
            { error: "Missing signature or webhook secret" },
            { status: 400 }
        );
    }

    let event;

    try {
        event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
        console.error(`⚠️  Stripe Connect webhook signature verification failed.`, err);
        return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
    }

    try {
        switch (event.type) {
            case "account.updated": {
                const account = event.data.object as Stripe.Account;

                // Check if account has requirements that need attention
                const hasRequirements = account.requirements?.currently_due?.length > 0 ||
                    account.requirements?.eventually_due?.length > 0 ||
                    account.requirements?.past_due?.length > 0;

                // Get current connection state to check if needs_info_update is changing
                const currentConnection = await prisma.stripeConnection.findUnique({
                    where: { stripe_account_id: account.id },
                    include: { user: true }
                });

                await prisma.stripeConnection.update({
                    where: { stripe_account_id: account.id },
                    data: {
                        stripe_account_status: account.details_submitted
                            ? "active"
                            : "pending",
                        payments_enabled: account.charges_enabled,
                        details_submitted: account.details_submitted,
                        payouts_enabled: account.payouts_enabled,
                        is_verified: account.payouts_enabled && account.charges_enabled,
                        needs_info_update: hasRequirements
                    }
                });

                // Send notification if needs_info_update changed from false to true
                if (currentConnection && !currentConnection.needs_info_update && hasRequirements) {
                    try {
                        const { StripeService } = await import("@/lib/services/stripe.service");
                        await StripeService.sendAccountUpdateNotification(currentConnection.user_id);
                        console.log(`Sent account update notification to user ${currentConnection.user_id}`);
                    } catch (error) {
                        console.error("Failed to send account update notification:", error);
                        // Don't fail the webhook if notification fails
                    }
                }

                console.log(`Stripe Connect account ${account.id} updated`);
                break;
            }

            case "account.application.deauthorized": {
                const account = event.data.object as Stripe.Account;

                try {
                    await prisma.stripeConnection.delete({
                        where: { stripe_account_id: account.id }
                    });
                    console.log(`Stripe Connect account ${account.id} deauthorized and removed`);
                } catch (error) {
                    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
                        console.log(`Stripe Connect account ${account.id} not found in database - webhook acknowledged`);
                    } else {
                        throw error; // Re-throw other errors to be handled by the main catch block
                    }
                }
                break;
            }

            case "capability.updated": {
                const capability = event.data.object as Stripe.Capability;
                console.log(
                    `Capability ${capability.id} for account ${capability.account} is now ${capability.status}`
                );
                break;
            }

            case "payout.failed":
            case "payout.paid": {
                const payout = event.data.object as Stripe.Payout;

                // Update payout status in your database
                try {
                    await prisma.payout.update({
                        where: { stripe_payout_id: payout.id },
                        data: {
                            status: payout.status,
                            failure_reason: payout.failure_code || null
                        }
                    });
                } catch (error) {
                    if (error instanceof PrismaClientKnownRequestError && error.code === 'P2025') {
                        console.log(`Payout ${payout.id} not found in database - webhook acknowledged`);
                    } else {
                        throw error; // Re-throw other errors to be handled by the main catch block
                    }
                }

                // If payout failed, mark account as needing info update and send notification
                if (payout.status === "failed") {
                    // Get current connection state to check if needs_info_update is changing
                    const currentConnection = await prisma.stripeConnection.findUnique({
                        where: { stripe_account_id: payout.destination as string },
                        include: { user: true }
                    });

                    await prisma.stripeConnection.update({
                        where: { stripe_account_id: payout.destination as string },
                        data: {
                            needs_info_update: true
                        }
                    });

                    // Send notification if needs_info_update changed from false to true
                    if (currentConnection && !currentConnection.needs_info_update) {
                        try {
                            const { StripeService } = await import("@/lib/services/stripe.service");
                            await StripeService.sendAccountUpdateNotification(currentConnection.user_id);
                            console.log(`Sent payout failure notification to user ${currentConnection.user_id}`);
                        } catch (error) {
                            console.error("Failed to send payout failure notification:", error);
                            // Don't fail the webhook if notification fails
                        }
                    }
                }

                console.log(`Payout ${payout.id} status updated to ${payout.status}`);
                break;
            }

            case "transfer.created": {
                const transfer = event.data.object as Stripe.Transfer;
                console.log(`Transfer ${transfer.id} created for ${transfer.amount / 100} ${transfer.currency.toUpperCase()}`);

                // Send payout notification to the provider
                await handleTransferCreated(transfer);
                break;
            }

            case "transfer.failed": {
                const transfer = event.data.object as Stripe.Transfer;
                console.log(`Transfer ${transfer.id} failed`);
                break;
            }

            case "application_fee.created": {
                const fee = event.data.object as Stripe.ApplicationFee;
                console.log(`Application fee ${fee.id} created: ${fee.amount / 100} ${fee.currency.toUpperCase()}`);
                break;
            }

            default: {
                console.log(`Unhandled Stripe Connect event type: ${event.type}`);
            }
        }

        return NextResponse.json({ received: true });
    } catch (error) {
        const { status, message } = handlePrismaError(error);

        adminLogger.log("Error processing Stripe Connect webhook", {
            "error": error,
            "status": status,
            "message": message
        }, status >= 500 ? "error" : "warn");

        // For P2025 errors (record not found), return success to acknowledge the webhook
        if (status === 200) {
            return NextResponse.json({ received: true });
        }

        return NextResponse.json(
            { error: message },
            { status }
        );
    }
}

// Handle transfer created notification
async function handleTransferCreated(transfer: Stripe.Transfer) {
    try {
        // Find the provider associated with this Stripe account
        const stripeConnection = await prisma.stripeConnection.findUnique({
            where: { stripe_account_id: transfer.destination as string },
            include: {
                user: true
            }
        });

        if (!stripeConnection || !stripeConnection.user) {
            adminLogger.log("No provider found for Stripe account", {
                "stripe_account_id": transfer.destination
            }, "error");
            console.log(`No provider found for Stripe account ${transfer.destination}`);
            return;
        }

        const provider = stripeConnection.user;

        // Get the provider's listing
        const listing = await prisma.listing.findFirst({
            where: { owner_id: provider.id },
            select: {
                id: true,
                business_name: true,
                first_name: true,
                last_name: true,
                email: true
            }
        });

        if (!listing?.email) {
            adminLogger.log("No email found for provider", {
                "provider_id": provider.id
            }, "error");
            return;
        }

        // Get provider name
        const providerName = listing.business_name ||
            `${listing.first_name || ''} ${listing.last_name || ''}`.trim() ||
            provider.email || 'Provider';

        // Try to get invoice details from the transfer's source transaction
        let invoiceNumber = transfer.metadata?.invoice_number;
        let workDescription = transfer.metadata?.description;

        // If we don't have metadata, try to get it from the source transaction (payment intent)
        if (!invoiceNumber && transfer.source_transaction) {
            try {
                const charge = await stripe.charges.retrieve(transfer.source_transaction as string);
                if (charge.payment_intent) {
                    const paymentIntent = await stripe.paymentIntents.retrieve(charge.payment_intent as string);
                    invoiceNumber = paymentIntent.metadata?.invoice_number;
                    workDescription = paymentIntent.metadata?.description;
                }
            } catch (error) {
                adminLogger.log("Could not retrieve payment intent metadata", {
                    "error": error
                }, "error");
            }
        }

        // Import email service and template
        const { emailService } = await import("@/lib/services");
        const { PayoutNotificationEmail } = await import(
            "@/components/email-templates/PayoutNotificationEmail"
        );

        // Send the notification email
        await emailService.send({
            to: listing.email,
            subject: "💰 Your RVHelp payment is on the way!",
            react: PayoutNotificationEmail({
                providerName,
                amount: transfer.amount,
                currency: transfer.currency,
                invoiceNumber,
                workDescription
            }),
            emailType: "payout_notification"
        });

        adminLogger.log("Payout notification sent", {
            "provider_email": listing.email,
            "transfer_id": transfer.id
        });
    } catch (error) {
        adminLogger.log("Failed to send payout notification", {
            "transfer_id": transfer.id,
            "error": error
        }, "error");
        // Don't fail the webhook processing if notification fails
    }
}

export const runtime = "nodejs";
export const dynamic = "force-dynamic"; 