import { SupportRequestEmail } from "@/components/email-templates/SupportRequestEmail";
import { createHandler } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { z } from "zod";

const schema = z.object({
	name: z.string(),
	email: z.string().email(),
	phone: z.string(),
	location: z.object({
		address: z.string(),
		latitude: z.number(),
		longitude: z.number(),
		city: z.string().optional(),
		state: z.string().optional(),
		zip: z.string().optional(),
		country: z.string().optional()
	}),
	subject: z.string(),
	message: z.string()
});

export const POST = createHandler(
	async function ({ }) {
		const { name, email, phone, location, subject, message } =
			this.validatedData;


		try {
			// Update user phone if needed
			if (this.user && !this.user.phone) {
				await prisma.user.update({
					where: { id: this.user.id },
					data: { phone }
				});
			}

			// Determine member type for priority handling
			const membershipLevel = this.user?.membership_level || "FREE";
			const isPaid =
				membershipLevel === "STANDARD" || membershipLevel === "PREMIUM";
			const isPremium = membershipLevel === "PREMIUM";

			// Create enhanced message with location information
			const enhancedMessage = `
${message}

--- Pro Member Location Information ---
Address: ${location.address}
${location.city && location.state ? `City/State: ${location.city}, ${location.state}` : ""}
${location.zip ? `ZIP: ${location.zip}` : ""}
Coordinates: ${location.latitude}, ${location.longitude}

--- Support Priority ---
Membership Level: ${membershipLevel}
Priority Response: ${isPaid ? (isPremium ? "Premium (2-hour)" : "Standard (4-hour)") : "Standard (24-hour)"}
			`.trim();

			// Send email with pro member priority
			await emailService.send({
				from: "RVHelp Website <<EMAIL>>",
				to: "<EMAIL>",
				cc: email,
				subject: `${isPaid ? "[PRO MEMBER] " : ""}Support Request: ${subject}`,
				text: `
New ${isPaid ? "PRO MEMBER " : ""}support request from ${name}
Email: ${email}
Phone: ${phone}
Membership: ${membershipLevel}
Location: ${location.address}
Subject: ${subject}

Message:
${message}

Location Details:
- Address: ${location.address}
- City/State: ${location.city ? `${location.city}, ${location.state}` : "Not provided"}
- ZIP: ${location.zip || "Not provided"}
- Coordinates: ${location.latitude}, ${location.longitude}

Expected Response Time: ${isPaid ? (isPremium ? "2 hours" : "4 hours") : "24 hours"}
				`,
				react: SupportRequestEmail({
					name,
					email,
					phone,
					subject,
					message: enhancedMessage,
					membership: membershipLevel
				})
			});

			// Log pro member support request for analytics
			if (isPaid) {
				console.log(
					`[PRO-SUPPORT] ${membershipLevel} member ${email} submitted support request from ${location.city}, ${location.state}`
				);
			}
		} catch (error) {
			console.error(
				"[pro-contact-route] Error processing pro support request:",
				error
			);
			return this.respond({ success: false, error: error.message }, 500);
		}

		return this.respond({
			success: true,
			message: "Pro support request submitted successfully"
		});
	},
	{
		validateBody: schema,
		requireAuth: true
	}
);
