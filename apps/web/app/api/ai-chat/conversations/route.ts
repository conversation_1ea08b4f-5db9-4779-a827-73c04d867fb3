import { createHandler } from "@/lib/api/baseHandler";
import { AIChatService } from "@/lib/services/ai-chat.service";
import { NextResponse } from "next/server";

export const POST = createHandler({
    requireAuth: true,
    handler: async function () {
        try {
            const result = await AIChatService.createConversation(this.user.id);
            return NextResponse.json(result);
        } catch (error) {
            console.error("Error creating conversation:", error);
            return NextResponse.json(
                { error: "Failed to create conversation" },
                { status: 500 }
            );
        }
    }
});

export const GET = createHandler({
    requireAuth: true,
    handler: async function () {
        try {
            const result = await AIChatService.getUserConversations(this.user.id);
            return NextResponse.json(result);
        } catch (error) {
            console.error("Error fetching conversations:", error);
            return NextResponse.json(
                { error: "Failed to fetch conversations" },
                { status: 500 }
            );
        }
    }
});
