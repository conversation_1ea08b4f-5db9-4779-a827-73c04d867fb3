import { createHandler } from "@/lib/api/baseHandler";
import { AIChatService } from "@/lib/services/ai-chat.service";
import { NextResponse } from "next/server";

export const GET = createHandler({
    requireAuth: true,
    handler: async function () {
        try {
            const { conversationId } = this.params;
            const result = await AIChatService.getConversationHistory(this.user.id, conversationId);
            return NextResponse.json(result);
        } catch (error) {
            console.error("Error fetching conversation history:", error);
            return NextResponse.json(
                { error: "Failed to fetch conversation history" },
                { status: 500 }
            );
        }
    }
});
