import { createHandler } from "@/lib/api/baseHandler";
import { AIChatService } from "@/lib/services/ai-chat.service";
import { NextResponse } from "next/server";

export const GET = createHandler({
    requireAuth: true,
    handler: async function () {
        try {
            const result = await AIChatService.getSuggestedQuestions(this.user.id);
            return NextResponse.json(result);
        } catch (error) {
            console.error("Error fetching suggestions:", error);
            return NextResponse.json(
                { error: "Failed to fetch suggestions" },
                { status: 500 }
            );
        }
    }
});
