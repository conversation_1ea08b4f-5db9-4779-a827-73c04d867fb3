import { createHandler } from "@/lib/api/baseHandler";
import { isAIModelWarmed, warmupAIModel } from "@/lib/services/ai-chat.service";
import { NextResponse } from "next/server";

export const POST = createHandler({
    requireAuth: true,
    handler: async function () {
        try {
            await warmupAIModel();
            return NextResponse.json({ success: true, warmed: isAIModelWarmed() });
        } catch (error) {
            console.error("Warmup failed:", error);
            return NextResponse.json({ success: false, error: "Warmup failed" }, { status: 500 });
        }
    }
});
