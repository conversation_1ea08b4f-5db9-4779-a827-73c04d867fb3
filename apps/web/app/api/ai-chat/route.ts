import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { AI_CHAT_SYSTEM_PROMPT, CHAT_SETTINGS } from "@/lib/chat/config";
import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";
import { z } from "zod";

const chatSchema = z.object({
	messages: z.array(
		z.object({
			role: z.enum(["user", "assistant", "system"]),
			content: z.string()
		})
	),
	formData: z
		.object({
			make: z.string().optional(),
			model: z.string().optional(),
			year: z.string().optional(),
			system: z.string().optional()
		})
		.optional()
});

export const POST = createHandler(
	async function () {
		if (!process.env.OPENAI_API_KEY) {
			throw new Error("OPENAI_API_KEY is not configured");
		}

		const systemPrompt = `${AI_CHAT_SYSTEM_PROMPT}
		

		Form data: ${this.validatedData.formData
				? JSON.stringify(this.validatedData.formData)
				: "Not provided yet"
			}`;

		try {
			const result = streamText({
				model: openai(CHAT_SETTINGS.model),
				messages: [
					{
						role: "system",
						content: systemPrompt
					},
					...this.validatedData.messages
				],
				temperature: CHAT_SETTINGS.temperature,
				maxTokens: CHAT_SETTINGS.max_tokens
			});

			return result.toDataStreamResponse();
		} catch (error) {
			console.error("Chat API Error:", error);
			throw error;
		}
	},
	{
		validateBody: chatSchema
	}
);
