import { create<PERSON>and<PERSON> } from "@/lib/api/baseHandler";
import { AIChatMessageRequest, AIChatService } from "@/lib/services/ai-chat.service";
import { NextResponse } from "next/server";
import { z } from "zod";

const messageSchema = z.object({
    message: z.string().min(1, "Message cannot be empty"),
    conversationId: z.string().min(1, "Conversation ID is required"),
    context: z.object({
        userRvDetails: z.object({
            make: z.string().optional(),
            model: z.string().optional(),
            year: z.number().optional(),
            type: z.string().optional()
        }).optional(),
        currentLocation: z.object({
            lat: z.number(),
            lng: z.number()
        }).optional(),
        previousMessages: z.array(z.object({
            id: z.string(),
            text: z.string(),
            isUser: z.boolean(),
            timestamp: z.string()
        })).optional()
    }).optional()
});

export const POST = createHandler({
    requireAuth: true,
    validateBody: messageSchema,
    handler: async function () {
        try {
            const request: AIChatMessageRequest = {
                message: this.validatedData.message,
                conversationId: this.validatedData.conversationId,
                context: this.validatedData.context
            };

            const result = await AIChatService.sendMessage(this.user.id, request);
            console.log("result", result);
            return NextResponse.json(result);
        } catch (error) {
            console.error("Error sending message:", error);
            return NextResponse.json(
                { error: "Failed to send message" },
                { status: 500 }
            );
        }
    }
});
