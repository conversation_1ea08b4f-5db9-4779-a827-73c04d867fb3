import { createHandler } from "@/lib/api/baseHandler";
import { stripe } from "@/lib/stripe";
import { NextResponse } from "next/server";

export const GET = createHandler(async () => {
    try {
        // Fetch pricing information from Stripe
        const prices = await stripe.prices.list({
            active: true,
            expand: ['data.product']
        });

        // Filter for membership products and format the response
        const membershipPricing = prices.data
            .filter(price => {
                const product = price.product as any;
                return product?.metadata?.type === 'membership' ||
                    product?.name?.toLowerCase().includes('membership') ||
                    product?.name?.toLowerCase().includes('pro');
            })
            .map(price => {
                const product = price.product as any;
                return {
                    id: price.id,
                    productId: product.id,
                    name: product.name,
                    description: product.description,
                    unitAmount: price.unit_amount,
                    currency: price.currency,
                    interval: price.recurring?.interval,
                    intervalCount: price.recurring?.interval_count,
                    metadata: product.metadata
                };
            });

        return NextResponse.json({ pricing: membershipPricing });
    } catch (error) {
        console.error('Error fetching pricing:', error);
        return NextResponse.json(
            { error: 'Failed to fetch pricing information' },
            { status: 500 }
        );
    }
}); 