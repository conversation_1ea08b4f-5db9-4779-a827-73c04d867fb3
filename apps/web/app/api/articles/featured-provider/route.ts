import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";

export const GET = createHandler(
    async function () {
        const featuredArticle = await prisma.article.findFirst({
            where: {
                type: "blog-post",
                status: "published",
                is_featured_provider: true
            },
            orderBy: {
                published_at: "desc"
            }
        });

        return Response.json(featuredArticle);
    }
); 