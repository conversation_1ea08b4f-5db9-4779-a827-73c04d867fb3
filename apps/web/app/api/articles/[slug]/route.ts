import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const paramsSchema = z.object({
    slug: z.string()
});

export const GET = createHandler(
    async function (req, { params }) {
        // Validate params manually since validateParams is not implemented
        const validationResult = paramsSchema.safeParse(params);
        if (!validationResult.success) {
            return Response.json({ error: "Invalid slug parameter" }, { status: 400 });
        }

        const { slug } = validationResult.data;

        const article = await prisma.article.findFirst({
            where: {
                slug,
                type: {
                    in: [
                        "provider-announcement",
                        "provider-training",
                        "provider-office-hours"
                    ]
                },
                status: "published"
            },
            select: {
                id: true,
                slug: true,
                title: true,
                description: true,
                content: true,
                type: true,
                status: true,
                published_at: true,
                meta_image: true,
                zoom_url: true,
                youtube_url: true,
                tags: true
            }
        });

        if (!article) {
            return Response.json({ error: "Article not found" }, { status: 404 });
        }

        return Response.json(article);
    },
    {
        requireAuth: true
    }
); 