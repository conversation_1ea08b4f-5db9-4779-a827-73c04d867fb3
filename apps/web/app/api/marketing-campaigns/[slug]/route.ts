import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";

import MarketingCampaignCouponEmail, { marketingCampaignCouponText } from "@/components/email-templates/MarketingCampaignCouponEmail";
import crypto from "crypto";
import React from "react";
import { z } from "zod";

const submitLeadSchema = z.object({
    email: z.string().email("Please enter a valid email address"),
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    utm_source: z.string().optional(),
    utm_medium: z.string().optional(),
    utm_campaign: z.string().optional(),
});

export const GET = createHandler(
    async function (req, { params }) {
        const { slug } = params;

        const campaign = await prisma.marketingCampaign.findUnique({
            where: { slug },
            select: {
                id: true,
                title: true,
                description: true,
                discount_type: true,
                discount_value: true,
                status: true,
                expires_at: true,
                page_title: true,
                page_subtitle: true,
                page_description: true,
                button_text: true,
                success_message: true,
                background_image: true,
                logo: true,
            },
        });

        if (!campaign) {
            return this.respond({ error: "Campaign not found" }, 404);
        }

        // Check if campaign is active
        if (campaign.status !== "ACTIVE") {
            return this.respond({ error: "Campaign is not active" }, 400);
        }

        // Check if campaign is expired
        if (campaign.expires_at && new Date() > campaign.expires_at) {
            return this.respond({ error: "Campaign has expired" }, 400);
        }

        // Only increment view count if user is not an admin
        // This prevents admin views from skewing the stats
        if (!this.user || this.user.role !== "ADMIN") {
            await prisma.marketingCampaign.update({
                where: { slug },
                data: {
                    views_count: {
                        increment: 1,
                    },
                },
            });
        }

        return this.respond({ campaign });
    }
);

export const POST = createHandler(
    async function (req, { params }) {
        const { slug } = params;
        const validatedData = this.validatedData;

        // Get campaign
        const campaign = await prisma.marketingCampaign.findUnique({
            where: { slug },
        });

        if (!campaign) {
            return this.respond({ error: "Campaign not found" }, 404);
        }

        // Check if campaign is active
        if (campaign.status !== "ACTIVE") {
            return this.respond({ error: "Campaign is not active" }, 400);
        }

        // Check if campaign is expired
        if (campaign.expires_at && new Date() > campaign.expires_at) {
            return this.respond({ error: "Campaign has expired" }, 400);
        }

        // Check if email already exists for this campaign
        const existingLead = await prisma.marketingCampaignLead.findFirst({
            where: {
                campaign_id: campaign.id,
                email: validatedData.email,
            },
        });

        if (existingLead) {
            return this.respond(
                { error: "Email already registered for this campaign" },
                400
            );
        }

        // Generate coupon code - always create unique codes for each lead
        let couponCode;
        if (campaign.coupon_code) {
            // Use the campaign's custom coupon code as a base, but make it unique
            couponCode = `${campaign.coupon_code}-${crypto.randomBytes(4).toString('hex').toUpperCase()}`;
        } else {
            // Generate unique coupon code
            couponCode = `${campaign.slug.toUpperCase()}-${crypto.randomBytes(4).toString('hex').toUpperCase()}`;
        }

        // Get request metadata
        const ip = req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || "unknown";
        const userAgent = req.headers.get("user-agent") || "unknown";
        const referrer = req.headers.get("referer") || undefined;

        // Create lead
        const lead = await prisma.marketingCampaignLead.create({
            data: {
                campaign_id: campaign.id,
                email: validatedData.email,
                first_name: validatedData.first_name,
                last_name: validatedData.last_name,
                coupon_code: couponCode,
                ip_address: ip,
                user_agent: userAgent,
                referrer: referrer,
                utm_source: validatedData.utm_source,
                utm_medium: validatedData.utm_medium,
                utm_campaign: validatedData.utm_campaign,
            },
        });

        // Update campaign leads count
        await prisma.marketingCampaign.update({
            where: { id: campaign.id },
            data: {
                leads_count: {
                    increment: 1,
                },
            },
        });

        // Add to newsletter with tags
        try {
            const tags = [
                "consumer action: requested partner coupon code",
                `source: ${campaign.title}`
            ];

            await EmailNewsletterService.syncNewsletterSubscriber({
                email: validatedData.email,
                first_name: validatedData.first_name || "",
                last_name: validatedData.last_name || "",
                user: null, // No user object for campaign leads
                tags
            });
        } catch (newsletterError) {
            console.error("Failed to sync campaign lead with newsletter service:", newsletterError);
            // Don't fail the lead creation if newsletter sync fails
        }

        // Send email with coupon code
        await sendCouponEmail(campaign, lead);

        return this.respond({
            success: true,
            message: campaign.success_message || "Thank you! Check your email for your discount code.",
            coupon_code: couponCode,
        });
    },
    {
        validateBody: submitLeadSchema,
    }
);

// Helper method to send coupon email
async function sendCouponEmail(campaign: any, lead: any) {
    const emailSubject = campaign.email_subject || `Your ${campaign.discount_type === "PERCENTAGE" ? campaign.discount_value + "%" : "$" + campaign.discount_value} discount code is here!`;

    // Use custom email template if provided, otherwise use the default template
    let customMessage = undefined;
    if (campaign.email_template) {
        customMessage = campaign.email_template
            .replace(/\{\{coupon_code\}\}/g, lead.coupon_code)
            .replace(/\{\{first_name\}\}/g, lead.first_name || "")
            .replace(/\{\{last_name\}\}/g, lead.last_name || "")
            .replace(/\{\{email\}\}/g, lead.email);
    }

    const emailProps = {
        firstName: lead.first_name,
        lastName: lead.last_name,
        email: lead.email,
        couponCode: lead.coupon_code,
        campaignTitle: campaign.title,
        discountType: campaign.discount_type,
        discountValue: campaign.discount_value,
        expiresAt: campaign.expires_at ? new Date(campaign.expires_at) : undefined,
        customMessage,
    };

    try {
        await emailService.send({
            to: lead.email,
            subject: emailSubject,
            react: React.createElement(MarketingCampaignCouponEmail, emailProps),
            text: marketingCampaignCouponText(emailProps),
            emailType: "marketing_campaign_coupon",
        });

        // Mark as sent
        await prisma.marketingCampaignLead.update({
            where: { id: lead.id },
            data: { coupon_sent_at: new Date() },
        });
    } catch (error) {
        console.error("Failed to send coupon email:", error);
        // Don't throw error - we still want to return success to user
    }
} 