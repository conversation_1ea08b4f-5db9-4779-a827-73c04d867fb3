import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { z } from "zod";

const validateCouponSchema = z.object({
    couponCode: z.string().min(1, "Coupon code is required"),
});

export const POST = createHandler(
    async function () {
        const { couponCode } = this.validatedData;

        // Find the marketing campaign lead with this coupon code
        const lead = await prisma.marketingCampaignLead.findFirst({
            where: {
                coupon_code: couponCode,
                coupon_used_at: null, // Only unused coupons
            },
            include: {
                campaign: {
                    select: {
                        id: true,
                        title: true,
                        discount_type: true,
                        discount_value: true,
                        status: true,
                        expires_at: true,
                    },
                },
            },
        });

        if (!lead) {
            return this.respond({ error: "Invalid or expired coupon code" }, 400);
        }

        // Check if campaign is still active
        if (lead.campaign.status !== "ACTIVE") {
            return this.respond({ error: "Campaign is no longer active" }, 400);
        }

        // Check if campaign has expired
        if (lead.campaign.expires_at && new Date() > lead.campaign.expires_at) {
            return this.respond({ error: "Campaign has expired" }, 400);
        }

        return this.respond({
            campaign: lead.campaign,
            couponCode: lead.coupon_code,
        });
    },
    {
        validateBody: validateCouponSchema,
    }
); 