import { createHand<PERSON> } from "@/lib/api/baseHandler";
import { invoiceService } from "@/lib/services";
import { generateInvoicePDF } from "@/lib/utils/pdf";
import { NextResponse } from "next/server";

export const GET = createHandler(
    async function (req, { params }) {
        const { id } = params;

        try {
            // Get the invoice with items and provider information
            const invoice = await invoiceService.getInvoiceById(id);
            if (!invoice) {
                return new NextResponse("Invoice not found", { status: 404 });
            }

            // Check if this is a platform invoice (no provider_id)
            const pdfBuffer = await generateInvoicePDF(
                invoice,
                invoice.items,
                invoice.provider
            );



            // Return the PDF with appropriate headers
            return new NextResponse(pdfBuffer, {
                status: 200,
                headers: {
                    "Content-Type": "application/pdf",
                    "Content-Disposition": `inline; filename="invoice-${invoice.invoice_number}.pdf"`,
                    "Cache-Control": "public, max-age=3600" // Cache for 1 hour
                }
            });
        } catch (error) {
            console.error("Error generating PDF:", error);
            return new NextResponse("Failed to generate PDF", { status: 500 });
        }
    },
    {
        // Allow public access for PDF viewing
        requireAuth: false
    }
); 