import { createH<PERSON><PERSON> } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { hasListingAccess } from "@/lib/services/listing-access.service";
import { MessageService } from "@/lib/services/messaging.service";
import { NextResponse } from "next/server";
import { z } from "zod";

export const GET = createHandler({
	requireAuth: true,
	handler: async (req, { params }) => {
		const { id: quoteId } = params;

		try {
			// Single service call handles all the logic
			const messages = await MessageService.getQuoteMessages({
				quoteId
			});

			const job = await prisma.job.findFirst({
				where: {
					quotes: {
						some: {
							id: quoteId
						}
					}
				},
				include: {
					warranty_request: {
						include: {
							company: true
						}
					},
					accepted_quote: {
						include: {
							listing: true
						}
					}
				}
			});

			// Get the specific quote for this conversation
			const quote = await prisma.quote.findUnique({
				where: { id: quoteId },
				include: {
					listing: true
				}
			});

			return NextResponse.json({
				messages,
				job: {
					id: job?.id,
					category: job?.category,
					message: job?.message,
					rv_year: job?.rv_year,
					rv_make: job?.rv_make,
					rv_model: job?.rv_model,
					rv_type: job?.rv_type,
					status: job?.status,
					warranty_request_id: job?.warranty_request_id,
					accepted_quote_id: job?.accepted_quote_id,
					warranty_request: job?.warranty_request ? {
						id: job.warranty_request.id,
						company: job.warranty_request.company
					} : null,
					accepted_quote: job?.accepted_quote ? {
						id: job.accepted_quote.id,
						listing: job.accepted_quote.listing
					} : null
				},
				quote: quote ? {
					id: quote.id,
					status: quote.status,
					listing: quote.listing
				} : null
			});
		} catch (error) {
			return NextResponse.json(
				{ error: "Internal server error" },
				{ status: 500 }
			);
		}
	}
});

const postValidationSchema = z
	.object({
		content: z.string().min(1, "Message cannot be empty"),
		// Require either providerId OR userId, but not both
		providerId: z.string().optional(),
		userId: z.string().optional()
	})
	.refine(
		(data) => {
			// Exactly one of providerId or userId must be provided
			return (
				(data.providerId && !data.userId) || (!data.providerId && data.userId)
			);
		},
		{
			message: "Either providerId or userId must be provided, but not both"
		}
	);

export const POST = createHandler({
	requireAuth: true,
	validateBody: postValidationSchema,
	handler: async (req, { params, validatedData, session }) => {
		const { id: quoteId } = params;
		const { providerId, userId, content } = validatedData;

		try {
			// Get quote with job and listing details for permission validation
			const quote = await prisma.quote.findUnique({
				where: { id: quoteId },
				include: {
					job: {
						include: {
							user: {
								select: { id: true }
							},
							warranty_request: {
								include: {
									company: true
								}
							}
						}
					},
					listing: {
						select: { id: true }
					}
				}
			});
			if (!quote) {
				return NextResponse.json({ error: "Quote not found" }, { status: 404 });
			}

			let message;

			if (providerId) {
				// Validate provider permissions
				if (quote.listing.id !== providerId) {
					return NextResponse.json(
						{ error: "Provider ID does not match quote listing" },
						{ status: 403 }
					);
				}

				// Check if current user has access to this listing
				const hasAccess = await hasListingAccess({
					user: session.user,
					listingId: providerId
				});
				if (!hasAccess) {
					return NextResponse.json(
						{ error: "You don't have access to this listing" },
						{ status: 403 }
					);
				}

				// Create provider message
				message = await MessageService.createProviderQuoteMessage({
					quoteId,
					listingId: providerId,
					content
				});
			} else if (userId) {
				// Validate user permissions
				if (quote.job.user.id !== userId) {
					return NextResponse.json(
						{ error: "User ID does not match job owner" },
						{ status: 403 }
					);
				}

				// Check if current user is the job owner
				if (session.user.id !== userId) {
					return NextResponse.json(
						{ error: "You can only send messages as yourself" },
						{ status: 403 }
					);
				}

				// Create user message
				message = await MessageService.createQuoteMessage({
					quoteId,
					userId,
					content
				});
			}

			return NextResponse.json({ message });
		} catch (error) {
			return NextResponse.json(
				{ error: "Internal server error" },
				{ status: 500 }
			);
		}
	}
});
