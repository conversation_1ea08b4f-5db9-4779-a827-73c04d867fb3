import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { ListingService } from "@/lib/services/listing.service";
import { shouldAllowMarkAsRead } from "@/lib/utils/impersonation";
import { NextResponse } from 'next/server';

export const POST = createHandler({
  requireAuth: true,
  handler: async (req, { params, session }) => {
    try {
      // Check if mark-as-read is allowed (not impersonating)
      const allowMarkAsRead = await shouldAllowMarkAsRead(session);
      if (!allowMarkAsRead) {
        return NextResponse.json({
          error: 'Mark-as-read not allowed during impersonation'
        }, { status: 403 });
      }

      const quoteId = params.id;

      // Get the quote to verify permissions
      const quote = await prisma.quote.findUnique({
        where: { id: quoteId },
        include: {
          job: {
            include: {
              user: true
            }
          },
          listing: true
        }
      });

      if (!quote) {
        return NextResponse.json({ error: 'Quote not found' }, { status: 404 });
      }

      // Determine if current user is the customer or provider
      const isCustomer = quote.job.user.id === session.user.id;
      let isProvider = false;

      if (!isCustomer) {
        // Check if user is the provider
        const userListing = await ListingService.getListingByUserId(session.user.id);
        isProvider = userListing?.id === quote.listing.id;
      }

      if (!isCustomer && !isProvider) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 });
      }

      // Mark messages as read based on user context
      // If user is customer, mark provider messages as read
      // If user is provider, mark customer messages as read
      const senderTypeToMarkAsRead = isCustomer ? 'PROVIDER' : 'USER';

      const updated = await prisma.quoteMessage.updateMany({
        where: {
          quote_id: quoteId,
          sender_type: senderTypeToMarkAsRead,
          read_at: null, // Only update unread messages
        },
        data: {
          read_at: new Date(),
        },
      });

      return NextResponse.json({
        success: true,
        updated: updated.count,
        context: isCustomer ? 'customer' : 'provider'
      });
    } catch (error) {
      console.error('Error marking messages as read:', error);
      return NextResponse.json({
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error)
      }, { status: 500 });
    }
  }
});
