import { createHandler } from "@/lib/api/baseHandler";
import { prisma } from "@/lib/prisma";
import { setActiveListingLocation } from "../../../../../../lib/location-utils";

export const GET = createHandler(async function GET(req, { params }) {
    const { id, quoteId } = params;

    const quote = await prisma.quote.findUnique({
        where: { id: quoteId },
        include: {
            messages: true,
            listing: {
                include: {
                    locations: true,
                }
            },
            job: {
                include: {
                    user: true,
                }
            },
        }
    });

    if (!quote) {
        return this.respond({ error: "Quote not found" }, 404);
    }

    const location = setActiveListingLocation(quote.listing);

    return this.respond({ ...quote, location });
});
