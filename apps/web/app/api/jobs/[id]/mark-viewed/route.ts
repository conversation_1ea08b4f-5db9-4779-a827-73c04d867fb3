import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";

export const POST = createHandler({
  requireAuth: true,
  handler: async (req, { params, session }) => {
    const { id: jobId } = params;

    const job = await prisma.job.findFirst({
      where: {
        id: jobId,
        user_id: session.user.id,
      },
    });

    if (!job) {
      return NextResponse.json({ error: "Job not found" }, { status: 404 });
    }

    if (job.viewed_at) {
        return NextResponse.json({ success: true, message: "Offer already viewed." });
    }

    await prisma.job.update({
      where: { id: jobId },
      data: { viewed_at: new Date() },
    });

    return NextResponse.json({ success: true });
  }
}); 