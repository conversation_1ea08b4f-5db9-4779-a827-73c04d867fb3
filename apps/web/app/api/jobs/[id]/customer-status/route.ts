import { createHandler } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { JobStatus } from "@rvhelp/database";
import { z } from "zod";

const requestSchema = z.object({
    status: z.enum(["COMPLETED", "CANCELLED"]),
    reason: z.string().min(1, "Reason is required"),
    selectedProviderId: z.string().optional(),
});

export const POST = createHandler(
    async function () {
        const jobId = this.params.id;
        const { status, reason, selectedProviderId } = this.validatedData;


        // Validate required fields
        if (!status) {
            return this.respond(
                { error: "Status is required" },
                400
            );
        }

        if (!reason) {
            return this.respond(
                { error: "Reason is required" },
                400
            );
        }

        // Basic job validation
        const job = await prisma.job.findFirst({
            where: { id: jobId, user_id: this.user.id },
        });

        if (!job) {
            return this.respond(
                { error: "Job not found or access denied" },
                404
            );
        }

        if (job.status === JobStatus.COMPLETED || job.status === JobStatus.CANCELLED) {
            return this.respond(
                { error: "Job is already in a final state" },
                400
            );
        }

        try {
            let updatedJob;

            if (status === "COMPLETED") {
                updatedJob = await QuoteStatusService.customerCompleteJob({
                    jobId,
                    userId: this.user.id,
                    reason,
                    selectedProviderId,
                });
            } else if (status === "CANCELLED") {
                updatedJob = await QuoteStatusService.customerCancelJob({
                    jobId,
                    userId: this.user.id,
                    reason,
                });
            } else {
                return this.respond(
                    { error: "Invalid status. Must be 'COMPLETED' or 'CANCELLED'" },
                    400
                );
            }

            return this.respond({
                success: true,
                job: updatedJob,
                message: `Job successfully marked as ${status.toLowerCase()}`,
            });
        } catch (error) {
            console.error("Error updating job status:", error);
            return this.respond(
                { error: error.message || "Failed to update job status" },
                500
            );
        }
    },
    { requireAuth: true, validateBody: requestSchema }
); 