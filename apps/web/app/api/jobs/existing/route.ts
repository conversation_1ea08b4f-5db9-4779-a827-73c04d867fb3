import { createHandler } from '@/lib/api/baseHandler';
import { JobService } from '@/lib/services/job.service';

export const GET = createHandler(
    async function () {
        if (!this?.user?.id) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        try {
            const existingJobs = await JobService.getExistingJobs(this.user.id);
            const hasExisting = existingJobs.length > 0;
            const mostRecentActiveJob = await JobService.getMostRecentActiveJob(this.user.id);

            return Response.json({
                hasExisting,
                existingJobs,
                mostRecentActiveJob
            });
        } catch (error) {
            console.error('Error fetching existing jobs:', error);
            return Response.json(
                { error: 'Failed to fetch existing jobs' },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true
    }
); 