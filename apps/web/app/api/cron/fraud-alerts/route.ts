import config from "@/config";
import { createHandler } from "@/lib/api/baseHandler";
import { slackService } from "@/lib/services";
import { FraudDetectionService } from "@/lib/services/fraud-detection.service";

export const GET = createHandler(async function () {
    const cronSecret = config.cronSecret;
    const authHeader = this.req.headers.get("authorization");
    const expectedAuth = `Bearer ${cronSecret}`;

    if (!cronSecret) {
        console.error("CRON_SECRET environment variable is not set");
        return new Response("Server configuration error", { status: 500 });
    }

    if (!authHeader || authHeader !== expectedAuth) {
        console.error("Invalid or missing authorization header");
        return new Response("Unauthorized", { status: 401 });
    }

    try {
        const stats = await FraudDetectionService.getFraudStats();

        // Only send alert if there's suspicious activity
        if (stats.flaggedJobsToday > 0 || stats.suspiciousUsersToday > 0) {
            await slackService.sendToJosiahMann({
                blocks: [
                    {
                        type: "header",
                        text: {
                            type: "plain_text",
                            text: "🚨 Daily Fraud Alert",
                            emoji: true
                        }
                    },
                    {
                        type: "section",
                        fields: [
                            {
                                type: "mrkdwn",
                                text: `*Flagged Jobs Today:*\n${stats.flaggedJobsToday}`
                            },
                            {
                                type: "mrkdwn",
                                text: `*Suspicious Users Today:*\n${stats.suspiciousUsersToday}`
                            }
                        ]
                    },
                    {
                        type: "section",
                        fields: [
                            {
                                type: "mrkdwn",
                                text: `*Total Flagged Jobs:*\n${stats.totalFlaggedJobs}`
                            }
                        ]
                    },
                    {
                        type: "section",
                        text: {
                            type: "mrkdwn",
                            text: `<${process.env.NEXT_PUBLIC_APP_URL}/admin/fraud|View Fraud Management Dashboard>`
                        }
                    },
                    {
                        type: "context",
                        elements: [
                            {
                                type: "mrkdwn",
                                text: `Alert for ${new Date().toLocaleDateString()}`
                            }
                        ]
                    }
                ]
            });
        }

        return Response.json({
            success: true,
            stats,
            alertSent: stats.flaggedJobsToday > 0 || stats.suspiciousUsersToday > 0
        });
    } catch (error) {
        console.error("Error in fraud alerts cron:", error);
        return new Response("Internal server error", { status: 500 });
    }
}); 