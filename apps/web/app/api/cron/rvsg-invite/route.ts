import config from "@/config";
import { createHandler } from "@/lib/api/baseHandler";
import { slackService } from "@/lib/services";
import { RVSGService } from "@/lib/services/rvsg.service";

export const GET = createHandler(async function () {
    const cronSecret = config.cronSecret;
    const authHeader = this.req.headers.get("authorization");
    const expectedAuth = `Bearer ${cronSecret}`;

    if (!cronSecret) {
        console.error("CRON_SECRET environment variable is not set");
        return new Response("Server configuration error", { status: 500 });
    }

    if (!authHeader || authHeader !== expectedAuth) {
        console.error("Invalid or missing authorization header");
        return new Response("Unauthorized", { status: 401 });
    }

    try {
        console.log("Starting RVSG invite cron job...");

        const startTime = new Date();
        const result = await RVSGService.processRecentRVSGImports();
        const endTime = new Date();
        const duration = Math.round((endTime.getTime() - startTime.getTime()) / 1000);

        console.log("RVSG invite cron job completed successfully");

        // Send Slack notification to Josiah
        const message = {
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "🔄 RVSG Invite Cron Job - Sunday Report",
                        emoji: true
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: "*📋 Total Processed*\n" + (result.stats?.totalProcessed || 0).toLocaleString()
                        },
                        {
                            type: "mrkdwn",
                            text: "*✅ Invites Sent*\n" + (result.stats?.invited || 0).toLocaleString()
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: "*🔍 Inspection Eligible*\n" + (result.stats?.inspectionEligible || 0).toLocaleString()
                        },
                        {
                            type: "mrkdwn",
                            text: "*🔧 Repair Eligible*\n" + (result.stats?.repairEligible || 0).toLocaleString()
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: "*⏭️ Skipped*\n" + (result.stats?.skipped || 0).toLocaleString()
                        },
                        {
                            type: "mrkdwn",
                            text: "*⏱️ Duration*\n" + duration + " seconds"
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: "*✅ Status*\nCompleted Successfully"
                        },
                        {
                            type: "mrkdwn",
                            text: "*📅 Date*\n" + startTime.toLocaleDateString()
                        }
                    ]
                },
                {
                    type: "context",
                    elements: [
                        {
                            type: "mrkdwn",
                            text: `🕐 Started: ${startTime.toLocaleTimeString()} | 🕐 Completed: ${endTime.toLocaleTimeString()}`
                        }
                    ]
                }
            ]
        };

        await slackService.sendToJosiahMann(message);

        return Response.json({
            success: true,
            message: "RVSG invite processing completed successfully",
            listings: result.listings?.length || 0,
            stats: result.stats,
            duration: duration
        });
    } catch (error) {
        console.error("Error in RVSG invite cron job:", error);

        // Send error notification to Slack
        const errorMessage = {
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "❌ RVSG Invite Cron Job - Error",
                        emoji: true
                    }
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: "*Error Details:*\n" + (error instanceof Error ? error.message : "Unknown error")
                    }
                },
                {
                    type: "context",
                    elements: [
                        {
                            type: "mrkdwn",
                            text: `📅 Date: ${new Date().toLocaleDateString()} | 🕐 Time: ${new Date().toLocaleTimeString()}`
                        }
                    ]
                }
            ]
        };

        await slackService.sendToJosiahMann(errorMessage);

        return new Response("Error processing RVSG invites", { status: 500 });
    }
}); 