import { create<PERSON><PERSON><PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { RVHelpVerificationLevel } from "@rvhelp/database";

// Import the same validation logic used by the client
import { validateAllSections } from "@/app/(main)/provider/business/(components)/forms/validationRules";

async function checkAndTriggerProfileVerification(listing: any) {
    try {
        // Check if profile is now complete using the same validation logic as the client
        const validation = validateAllSections(listing as any);
        const allSectionsComplete = Object.values(validation).every(
            (section: any) => section.isComplete
        );

        // If all sections are complete and profile isn't already marked as completed, trigger celebration
        if (allSectionsComplete) {
            // Update listing to mark as verified
            await prisma.listing.update({
                where: { id: listing.id },
                data: {
                    profile_completed: true,
                    rv_help_verification_level: RVHelpVerificationLevel.VERIFIED
                }
            });

            // Create verification celebration notification
            await prisma.verificationCelebration.create({
                data: {
                    listing_id: listing.id,
                    level: RVHelpVerificationLevel.VERIFIED,
                    message: "Your profile is now verified!"
                }
            });

            console.log(`Profile verification celebration triggered for listing ${listing.id}`);
            return true;
        }
        return false;
    } catch (error) {
        console.error(`Error checking profile completion for listing ${listing.id}:`, error);
        return false;
    }
}

export const POST = createHandler(
    async function verifyCompleteProfiles() {
        // Verify this is a legitimate cron request
        const authHeader = this.request.headers.get("authorization");
        if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
            return this.respond({ error: "Unauthorized" }, 401);
        }

        try {
            console.log("🔄 Starting daily profile completion verification job...");

            // Get all listings that are not marked as PROFILE_COMPLETE
            const listings = await prisma.listing.findMany({
                where: {
                    rv_help_verification_level: {
                        not: RVHelpVerificationLevel.VERIFIED
                    },
                    // Only check active listings
                    is_active: true,
                    // Only check listings that have been created (not drafts)
                    created_at: {
                        not: null
                    }
                },
                include: {
                    locations: true
                }
            });

            console.log(`📋 Found ${listings.length} listings to check`);

            let verifiedCount = 0;
            let errorCount = 0;

            for (const listing of listings) {
                try {
                    const wasVerified = await checkAndTriggerProfileVerification(listing);
                    if (wasVerified) {
                        verifiedCount++;
                    }
                } catch (error) {
                    console.error(`Error processing listing ${listing.id}:`, error);
                    errorCount++;
                }
            }

            console.log(`✅ Daily profile completion verification job completed:`);
            console.log(`   - Total listings checked: ${listings.length}`);
            console.log(`   - Newly verified: ${verifiedCount}`);
            console.log(`   - Errors: ${errorCount}`);

            return this.respond({
                success: true,
                summary: {
                    totalChecked: listings.length,
                    newlyVerified: verifiedCount,
                    errors: errorCount
                }
            });

        } catch (error) {
            console.error("❌ Error in daily profile completion verification job:", error);
            return this.respond(
                { error: "Failed to run profile completion verification job" },
                500
            );
        }
    },
    {
        requireAuth: false // Cron jobs don't use regular auth
    }
); 