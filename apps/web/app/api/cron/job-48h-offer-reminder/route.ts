import config from "@/config";
import { createHandler } from "@/lib/api/baseHandler";
import { jobLifecycleService } from "@/lib/services/job-lifecycle.service";

export const GET = createHandler(async function () {
    const cronSecret = config.cronSecret;
    const authHeader = this.req.headers.get("authorization");
    const expectedAuth = `Bearer ${cronSecret}`;

    if (!cronSecret) {
        console.error("CRON_SECRET environment variable is not set");
        return new Response("Server configuration error", { status: 500 });
    }

    if (!authHeader || authHeader !== expectedAuth) {
        console.error("Invalid or missing authorization header");
        return new Response("Unauthorized", { status: 401 });
    }

    try {
        const result = await jobLifecycleService.send48HourOfferReminder();

        console.log(`48-hour offer reminder cron: Successfully processed ${result.processed} jobs.`);

        return Response.json({
            success: true,
            processed: result.processed,
            total: result.total,
            timestamp: result.timestamp
        });
    } catch (error) {
        console.error("Error in 48-hour offer reminder cron job:", error);
        return new Response("Error processing 48-hour offer reminders", { status: 500 });
    }
}); 