import config from "@/config";
import { createHandler } from "@/lib/api/baseHandler";
import { ProviderStatsService } from "@/lib/services/provider-stats.service";

export const GET = createHandler(async function () {
	const cronSecret = config.cronSecret;
	const authHeader = this.req.headers.get("authorization");
	const expectedAuth = `Bearer ${cronSecret}`;

	if (!cronSecret) {
		console.error("CRON_SECRET environment variable is not set");
		return new Response("Server configuration error", { status: 500 });
	}

	if (!authHeader || authHeader !== expectedAuth) {
		console.error("Invalid or missing authorization header");
		return new Response("Unauthorized", { status: 401 });
	}

	try {
		console.log("Starting provider stats update cron job...");

		await ProviderStatsService.updateAllProviderStats();

		console.log("Provider stats update cron job completed successfully");

		return Response.json({
			success: true,
			message: "Provider stats updated successfully"
		});
	} catch (error) {
		console.error("Error in provider stats update cron job:", error);
		return new Response("Error updating provider stats", { status: 500 });
	}
}); 