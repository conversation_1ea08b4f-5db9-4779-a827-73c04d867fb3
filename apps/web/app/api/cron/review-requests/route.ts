import config from "@/config";
import { createHandler } from "@/lib/api/baseHandler";
import { ReviewRequestService } from "@/lib/services/review-request.service";

export const GET = createHandler(async function () {
    const cronSecret = config.cronSecret;
    const authHeader = this.req.headers.get("authorization");
    const expectedAuth = `Bearer ${cronSecret}`;

    if (!cronSecret) {
        console.error("CRON_SECRET environment variable is not set");
        return new Response("Server configuration error", { status: 500 });
    }

    if (!authHeader || authHeader !== expectedAuth) {
        console.error("Invalid or missing authorization header");
        return new Response("Unauthorized", { status: 401 });
    }

    try {
        const result = await ReviewRequestService.sendPendingReviewRequests();

        console.log(`Review requests cron: Successfully sent ${result.sent} requests.`);

        return Response.json({
            success: true,
            sent: result.sent,
            errors: result.errors
        });
    } catch (error) {
        console.error("Error sending review requests:", error);
        return new Response("Error sending review requests", { status: 500 });
    }
}); 