import { createHand<PERSON> } from '@/lib/api/baseHandler';
import prisma from "@/lib/prisma";
import { z } from 'zod';

const updateTimelineUpdateSchema = z.object({
    notes: z.string().optional(),
    details: z.record(z.any()).optional(),
});

export const PATCH = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const user = await prisma.user.findFirst({
            where: { email: this.user.email },
        });

        if (!user) {
            return Response.json({ error: 'User not found' }, { status: 404 });
        }

        // Extract ID from URL
        const url = new URL(this.req.url);
        const pathParts = url.pathname.split('/');
        const id = pathParts[pathParts.length - 1];

        const { details, notes } = this.validatedData;

        try {
            // Find the timeline update and verify ownership
            const existingUpdate = await prisma.timelineUpdate.findFirst({
                where: { 
                    id,
                    updated_by_id: user.id // Only allow updating own updates
                }
            });

            if (!existingUpdate) {
                return Response.json({ error: 'Timeline update not found or access denied' }, { status: 404 });
            }

            // Combine existing details with new ones
            const existingDetails = (existingUpdate.details as any) || {};
            const combinedDetails = {
                ...existingDetails,
                ...details,
                ...(notes && { notes })
            };

            // Update the timeline update
            const timelineUpdate = await prisma.timelineUpdate.update({
                where: { id },
                data: {
                    details: Object.keys(combinedDetails).length > 0 ? combinedDetails : null,
                },
                include: {
                    updated_by: {
                        select: {
                            first_name: true,
                            last_name: true,
                            email: true
                        }
                    }
                }
            });

            return Response.json(timelineUpdate);
        } catch (error) {
            console.error('Error updating timeline update:', error);
            return Response.json(
                { error: 'Failed to update timeline update' }, 
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
        validateBody: updateTimelineUpdateSchema,
    }
);

export const DELETE = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const user = await prisma.user.findFirst({
            where: { email: this.user.email },
        });

        if (!user) {
            return Response.json({ error: 'User not found' }, { status: 404 });
        }

        // Extract ID from URL
        const url = new URL(this.req.url);
        const pathParts = url.pathname.split('/');
        const id = pathParts[pathParts.length - 1];

        try {
            // Find the timeline update and verify ownership
            const existingUpdate = await prisma.timelineUpdate.findFirst({
                where: { 
                    id,
                    updated_by_id: user.id // Only allow deleting own updates
                }
            });

            if (!existingUpdate) {
                return Response.json({ error: 'Timeline update not found or access denied' }, { status: 404 });
            }

            // Delete the timeline update
            await prisma.timelineUpdate.delete({
                where: { id }
            });

            return Response.json({ message: 'Timeline update deleted successfully' });
        } catch (error) {
            console.error('Error deleting timeline update:', error);
            return Response.json(
                { error: 'Failed to delete timeline update' }, 
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
    }
);

export const GET = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const user = await prisma.user.findFirst({
            where: { email: this.user.email },
        });

        if (!user) {
            return Response.json({ error: 'User not found' }, { status: 404 });
        }

        // Extract ID from URL
        const url = new URL(this.req.url);
        const pathParts = url.pathname.split('/');
        const id = pathParts[pathParts.length - 1];

        try {
            // First, find the timeline update
            const timelineUpdate = await prisma.timelineUpdate.findUnique({
                where: { id },
                include: {
                    updated_by: {
                        select: {
                            first_name: true,
                            last_name: true,
                            email: true
                        }
                    }
                }
            });

            if (!timelineUpdate) {
                return Response.json({ error: 'Timeline update not found' }, { status: 404 });
            }

            // Check access permissions
            let hasAccess = false;

            // Can view own updates
            if (timelineUpdate.updated_by_id === user.id) {
                hasAccess = true;
            }

            // Can view updates for warranty requests they have access to
            if (!hasAccess && timelineUpdate.warranty_request_id) {
                const warrantyRequest = await prisma.warrantyRequest.findFirst({
                    where: { 
                        id: timelineUpdate.warranty_request_id,
                        OR: [
                            { oem_user_id: user.id },
                            { customer_id: user.id },
                            { listing: { owner_id: user.id } }
                        ]
                    }
                });
                if (warrantyRequest) hasAccess = true;
            }

            // Can view updates for jobs they have access to
            if (!hasAccess && timelineUpdate.job_id) {
                const job = await prisma.job.findFirst({
                    where: { 
                        id: timelineUpdate.job_id,
                        OR: [
                            { user_id: user.id }, // Customer
                            { quotes: { some: { listing: { owner_id: user.id } } } } // Provider
                        ]
                    }
                });
                if (job) hasAccess = true;
            }

            if (!hasAccess) {
                return Response.json({ error: 'Access denied' }, { status: 403 });
            }

            return Response.json(timelineUpdate);
        } catch (error) {
            console.error('Error fetching timeline update:', error);
            return Response.json(
                { error: 'Failed to fetch timeline update' }, 
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
    }
); 