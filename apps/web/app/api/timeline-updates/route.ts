import { createHandler } from '@/lib/api/baseHandler';
import prisma from "@/lib/prisma";
import { TimelineEventType } from "@rvhelp/database";
import { z } from 'zod';

const createTimelineUpdateSchema = z.object({
    warranty_request_id: z.string().optional(),
    job_id: z.string().optional(),
    event_type: z.nativeEnum(TimelineEventType),
    details: z.record(z.any()).optional(), // JSON object for storing event details
    notes: z.string().optional(),
}).refine(
    (data) => data.warranty_request_id || data.job_id,
    {
        message: "Either warranty_request_id or job_id must be provided",
        path: ["warranty_request_id", "job_id"],
    }
);



export const GET = createHandler(
    async function () {
        const url = new URL(this.req.url);
        const job_id = url.searchParams.get('job_id');
        const event_types = url.searchParams.getAll('event_types');
        const date_from = url.searchParams.get('date_from');
        const date_to = url.searchParams.get('date_to');
        const limit = url.searchParams.get('limit');
        const offset = url.searchParams.get('offset');

        if (!job_id) {
            return Response.json({ error: 'job_id query parameter is required' }, { status: 400 });
        }

        try {
            const whereClause: any = {};
            whereClause.job_id = job_id;

            // Verify access to job
            const job = await prisma.job.findFirst({
                where: {
                    id: job_id,
                    OR: [
                        { user_id: this.user.id }, // Customer
                        { quotes: { some: { listing: { owner_id: this.user.id } } } } // Provider
                    ]
                }
            });

            if (!job) {
                return Response.json({ error: 'Job not found or access denied' }, { status: 404 });
            }

            // Add filters to where clause
            if (event_types.length > 0) {
                whereClause.event_type = { in: event_types };
            }

            if (date_from || date_to) {
                whereClause.date = {};
                if (date_from) whereClause.date.gte = new Date(date_from);
                if (date_to) whereClause.date.lte = new Date(date_to);
            }

            // Fetch timeline updates
            const timelineUpdates = await prisma.timelineUpdate.findMany({
                where: whereClause,
                orderBy: { date: 'desc' },
                include: {
                    updated_by: {
                        select: {
                            first_name: true,
                            last_name: true,
                            email: true
                        }
                    }
                },
                ...(limit && { take: parseInt(limit) }),
                ...(offset && { skip: parseInt(offset) }),
            });

            return Response.json(timelineUpdates);
        } catch (error) {
            console.error('Error fetching timeline updates:', error);
            return Response.json(
                { error: 'Failed to fetch timeline updates' },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
    }
);

export const POST = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const user = await prisma.user.findFirst({
            where: { email: this.user.email },
        });

        if (!user) {
            return Response.json({ error: 'User not found' }, { status: 404 });
        }

        const { details, notes, ...timelineData } = this.validatedData;

        try {
            // Verify the resource exists and user has access
            if (timelineData.warranty_request_id) {
                const warrantyRequest = await prisma.warrantyRequest.findFirst({
                    where: {
                        id: timelineData.warranty_request_id,
                        // Add access control based on user role
                        OR: [
                            { oem_user_id: user.id },
                            { customer_id: user.id },
                            { listing: { owner_id: user.id } }
                        ]
                    }
                });

                if (!warrantyRequest) {
                    return Response.json({ error: 'Warranty request not found or access denied' }, { status: 404 });
                }
            }

            if (timelineData.job_id) {
                const job = await prisma.job.findFirst({
                    where: {
                        id: timelineData.job_id,
                        OR: [
                            { user_id: user.id }, // Customer
                            { quotes: { some: { listing: { owner_id: user.id } } } } // Provider
                        ]
                    }
                });

                if (!job) {
                    return Response.json({ error: 'Job not found or access denied' }, { status: 404 });
                }
            }

            // Combine details and notes into the details JSON field
            const combinedDetails = {
                ...details,
                ...(notes && { notes })
            };

            // Create the timeline update
            const timelineUpdate = await prisma.timelineUpdate.create({
                data: {
                    ...timelineData,
                    updated_by_id: user.id,
                    event_type: timelineData.event_type,
                    details: Object.keys(combinedDetails).length > 0 ? combinedDetails : null,
                    date: new Date()
                },
                include: {
                    updated_by: {
                        select: {
                            first_name: true,
                            last_name: true,
                            email: true
                        }
                    },
                    warranty_request: {
                        select: {
                            id: true,
                            status: true
                        }
                    }
                }
            });

            return Response.json(timelineUpdate);
        } catch (error) {
            console.error('Error creating timeline update:', error);
            return Response.json(
                { error: 'Failed to create timeline update' },
                { status: 500 }
            );
        }
    },
    {
        requireAuth: true,
        validateBody: createTimelineUpdateSchema,
    }
);
