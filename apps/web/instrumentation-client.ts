// This file configures the initialization of Sentry on the client.
// The added config here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

// Only initialize Sentry in production to avoid build time impact in development
if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    dsn: "https://<EMAIL>/4508320959430656",

    // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
    // Reduced from 1.0 to 0.1 (10%) for better performance
    tracesSampleRate: 0.1,
    // Enable logs to be sent to Sentry
    enableLogs: true,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: false,
  });
}

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;