import { useEffect, useState } from "react";
import { useAuth } from "./useAuth";

export function usePendingReviews() {
    const { user } = useAuth();
    const [pendingCount, setPendingCount] = useState(0);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        if (!user || user.role !== "PROVIDER") {
            setIsLoading(false);
            return;
        }

        const fetchPendingCount = async () => {
            try {
                const response = await fetch("/api/provider/reviews?status=pending");
                if (response.ok) {
                    const reviews = await response.json();
                    setPendingCount(reviews.length);
                }
            } catch (error) {
                console.error("Error fetching pending reviews count:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchPendingCount();
    }, [user]);

    return { pendingCount, isLoading };
} 