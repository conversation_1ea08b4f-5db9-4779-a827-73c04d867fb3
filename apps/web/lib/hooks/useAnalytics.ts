import { useCallback } from 'react';

interface AnalyticsEvent {
    eventName: 'lead_form_submission' | 'provider_contact_click' | 'account_creation';
    listingId?: string;
    listingName?: string;
    category?: string;
    contactType?: 'phone' | 'email';
    contactValue?: string;
    source?: string;
    referrer?: string;
    value?: number;
}

export function useAnalytics() {
    const trackEvent = useCallback(async (event: AnalyticsEvent) => {
        try {
            // For client-side tracking, we'll use the client-side methods
            // These will work in the browser environment
            switch (event.eventName) {
                case 'lead_form_submission':
                    if (typeof window !== 'undefined' && window.gtag) {
                        window.gtag('event', 'lead_form_submission', {
                            event_category: 'conversion',
                            event_label: 'lead_form_submission',
                            listing_id: event.listingId,
                            listing_name: event.listingName,
                            category: event.category,
                            value: 1
                        });
                    } else {
                    }
                    break;
                case 'provider_contact_click':
                    if (typeof window !== 'undefined' && window.gtag) {
                        window.gtag('event', 'provider_contact_click', {
                            event_category: 'conversion',
                            event_label: `provider_contact_${event.contactType}`,
                            listing_id: event.listingId,
                            listing_name: event.listingName,
                            contact_type: event.contactType,
                            contact_value: event.contactValue,
                            value: 1
                        });
                    } else {
                    }
                    break;
                case 'account_creation':
                    if (typeof window !== 'undefined' && window.gtag) {
                        window.gtag('event', 'account_creation', {
                            event_category: 'conversion',
                            event_label: 'account_creation',
                            source: event.source,
                            referrer: event.referrer,
                            value: 1
                        });
                    } else {
                    }
                    break;
                default:
            }
        } catch (error) {
            console.error('Error tracking analytics event:', error);
        }
    }, []);

    const trackLeadFormSubmission = useCallback((data: {
        listingId: string;
        listingName?: string;
        category: string;
    }) => {
        return trackEvent({
            eventName: 'lead_form_submission',
            ...data
        });
    }, [trackEvent]);

    const trackProviderContact = useCallback((data: {
        listingId: string;
        listingName?: string;
        contactType: 'phone' | 'email';
        contactValue: string;
    }) => {
        const result = trackEvent({
            eventName: 'provider_contact_click',
            ...data
        });

        return result;
    }, [trackEvent]);

    const trackAccountCreation = useCallback((data: {
        source?: string;
        referrer?: string;
    }) => {
        return trackEvent({
            eventName: 'account_creation',
            ...data
        });
    }, [trackEvent]);

    return {
        trackEvent,
        trackLeadFormSubmission,
        trackProviderContact,
        trackAccountCreation
    };
} 