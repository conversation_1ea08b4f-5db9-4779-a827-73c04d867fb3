import { NewQuoteMessageEmail } from "@/components/email-templates/jobs/new-quote-message";
import config from "@/config";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { smsService } from "@/lib/services/sms.service";
import { JobWithUserAndLocation, QuoteWithListing } from "@/types/global";
import { QuoteMessage } from "@rvhelp/database";

export interface NewMessagePayload {
    message: QuoteMessage & {
        quote: QuoteWithListing & {
            job: JobWithUserAndLocation;
        };
    };
}

export async function processNewMessage(payload: NewMessagePayload) {
    const { message } = payload;
    const { quote } = message;

    const job = await prisma.job.findUnique({
        where: { id: quote.job_id },
        include: {
            user: true
        }
    });

    // Check if we have all required data, if not refetch
    let completeMessage = message;
    if (!!quote?.listing?.email) {

        const refetchedMessage = await prisma.quoteMessage.findUnique({
            where: { id: message.id },
            include: {
                quote: {
                    include: {
                        job: {
                            include: {
                                user: {
                                    select: {
                                        id: true,
                                        first_name: true,
                                        last_name: true,
                                        email: true,
                                        avatar: true,
                                        phone: true
                                    }
                                }
                            }
                        },
                        listing: {
                            select: {
                                id: true,
                                business_name: true,
                                first_name: true,
                                last_name: true,
                                email: true,
                                phone: true,
                                sms_verified_at: true,
                                profile_image: true
                            }
                        }
                    }
                }
            }
        });

        if (!refetchedMessage) {
            console.log("Could not refetch message, skipping notification");
            return { success: false, reason: "Message not found" };
        }

        completeMessage = refetchedMessage as any;
    }

    const { quote: completeQuote } = completeMessage;

    try {
        // If message is from provider, send to customer
        if (completeMessage.sender_type === "PROVIDER") {
            await emailService.send({
                to: job.user.email,
                replyTo: completeQuote.listing.notification_email || completeQuote.listing.email, // Reply goes to provider
                subject: `New message from ${completeQuote.listing.business_name || `${completeQuote.listing.first_name || ''} ${completeQuote.listing.last_name || ''}`.trim() || 'Provider'}`,
                react: NewQuoteMessageEmail({
                    recipientName: job.user.first_name || "Customer",
                    senderName: completeQuote.listing.business_name || `${completeQuote.listing.first_name || ''} ${completeQuote.listing.last_name || ''}`.trim() || 'Provider',
                    messagePreview: completeMessage.content,
                    jobId: job.id,
                    quoteId: completeQuote.id,
                    isProvider: false
                })
            });

            // Send SMS notification to customer
            try {
                // Only send SMS if user has opted in and has a phone number
                console.log("Sending SMS notification to customer", job);
                if (job.sms_opt_in && job.user.phone) {
                    console.log("customer has opted in and has a phone number");
                    const providerName = completeQuote.listing.business_name || `${completeQuote.listing.first_name || ''} ${completeQuote.listing.last_name || ''}`.trim() || 'Provider';
                    const messageText = `New message from ${providerName} on RV Help.\n\nLog in to view and respond: ${config.appUrl}/service-requests/${job.id}`;

                    await smsService.sendToUser(job.user.phone, messageText);
                    console.log(`SMS notification sent to user ${job.user.email} for message from provider ${completeQuote.listing.id}`);
                } else {
                    console.log("customer has not opted in or does not have a phone number");
                }
            } catch (error) {
                console.error("Failed to send SMS notification to customer:", error);
                // Don't fail the process if SMS fails
            }
        }
        // If message is from customer, send to provider
        else if (completeMessage.sender_type === "USER") {
            await emailService.send({
                to: completeQuote.listing.notification_email || completeQuote.listing.email,
                replyTo: job.user.email, // Reply goes to customer
                subject: `New message from ${job.user.first_name || "Customer"}`,
                react: NewQuoteMessageEmail({
                    recipientName: completeQuote.listing.business_name || `${completeQuote.listing.first_name || ''} ${completeQuote.listing.last_name || ''}`.trim() || 'Provider',
                    senderName: job.user.first_name || "Customer",
                    messagePreview: completeMessage.content,
                    jobId: job.id,
                    quoteId: completeQuote.id,
                    isProvider: true
                })
            });

            // Send SMS notification to provider
            try {
                console.log("Sending SMS notification to provider", completeQuote.listing.phone);
                // Only send SMS if provider has verified SMS and has a phone number
                if (completeQuote.listing.sms_verified_at && completeQuote.listing.phone) {
                    console.log("listing is verified and has a phone number");
                    const customerName = job.user.first_name || "Customer";

                    const messageText = `New message from ${customerName} on RV Help.\n\nLog in to view and respond: ${config.appUrl}/provider/quotes/${completeQuote.id}`;

                    await smsService.sendToProvider(completeQuote.listing.phone, messageText);
                    console.log(`SMS notification sent to provider ${completeQuote.listing.id} for message from user ${job.user.email}`);
                } else {
                    console.log("listing is not verified or does not have a phone number");
                }
            } catch (error) {
                console.error("Failed to send SMS notification to provider:", error);
                // Don't fail the process if SMS fails
            }
        }

        return { success: true };
    } catch (error) {
        console.log("Error processing new message notification:", error);
        throw error;
    }
} 