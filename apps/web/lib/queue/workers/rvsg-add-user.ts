import { prisma } from "@/lib/prisma";
import { adminLogger } from "@/lib/services/admin-log.service";
import { ListingService } from "@/lib/services/listing.service";

export interface RVSGAddUserJob {
    listingId: string;
    userId: string;
}

export async function processRVSGAddUser(job: RVSGAddUserJob) {
    const { listingId, userId } = job;

    adminLogger.log(`[Queue] Processing RVSG add user for listing ${listingId}`, {
        listingId,
        userId
    });

    try {
        // Get the user from the database
        const user = await prisma.user.findUnique({
            where: { id: userId }
        });

        if (!user) {
            throw new Error(`User ${userId} not found`);
        }

        // Add user to listing
        await ListingService.addUser(listingId, user);

        adminLogger.log(`[Queue] Successfully added user to listing ${listingId}`, {
            listingId,
            userId,
            userEmail: user.email
        });

        return {
            success: true,
            listingId,
            userId,
            userEmail: user.email
        };

    } catch (error) {
        adminLogger.log(`[Queue] Error adding user to listing ${listingId}`, {
            listingId,
            userId,
            error: error instanceof Error ? error.message : "Unknown error"
        });

        // Re-throw the error so the queue system can retry
        throw error;
    }
} 