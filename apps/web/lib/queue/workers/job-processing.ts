import { ServiceRequestConfirmationEmail } from "@/components/email-templates/jobs/customer/ServiceRequestConfirmationEmail";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";
import { FraudDetectionService } from "@/lib/services/fraud-detection.service";
import { JobInvitationService } from "@/lib/services/job-invitation.service";
import { membershipService } from "@/lib/services/membership.service";
import { timelineService } from "@/lib/services/timeline.service";
import { JobWithUserAndLocation, ListingWithLocation, User } from "@/types/global";

export interface JobProcessingPayload {
	job: JobWithUserAndLocation;
	listing: ListingWithLocation;
	validatedData: any;
	user: User;
	isWarranty?: boolean;
}

export async function processJob(payload: JobProcessingPayload) {
	try {
		const { job, listing, validatedData, user, isWarranty } = payload;

		// Check for suspicious activity
		const fraudCheck = await FraudDetectionService.checkJobSubmission(
			user.id,
			validatedData.email,
			validatedData.message || ""
		);

		// Flag job if suspicious
		if (fraudCheck.isSuspicious) {
			await FraudDetectionService.flagJobAsFraudulent(
				job.id,
				fraudCheck.reason || "Suspicious activity detected",
				"System"
			);
		}

		// Subscribe user to newsletter with appropriate tags
		try {
			const tags = [
				"consumer action: submitted lead form",
				"consumer has an account"
			];

			await EmailNewsletterService.syncNewsletterSubscriber({
				email: user.email,
				first_name: user.first_name,
				last_name: user.last_name,
				user: user,
				tags
			});
		} catch (newsletterError) {
			console.error(`Failed to sync user ${user.email} with newsletter service:`, newsletterError);
			// Do not fail the whole process for newsletter errors
		}

		// Create membership offer for new job submissions (only for FREE users)
		if (user.membership_level === "FREE") {
			try {
				// Check if user already has an active membership offer
				const existingOffer = await prisma.membershipOffer.findFirst({
					where: {
						user_id: user.id,
						is_active: true,
						used_at: null,
						expires_at: {
							gt: new Date()
						}
					}
				});

				// Only create offer if user doesn't already have one
				if (!existingOffer) {
					// Create a membership offer that expires in 30 days
					const expiresAt = new Date();
					expiresAt.setDate(expiresAt.getDate() + 30);

					await prisma.membershipOffer.create({
						data: {
							user_id: user.id,
							email: user.email,
							offer_type: "JOB_SUBMISSION_50_OFF",
							discount_percentage: 50,
							discount_amount: null,
							description: "50% off Pro membership - Limited time offer for new service requests",
							is_active: true,
							expires_at: expiresAt
						}
					});

					console.log(`Created membership offer for job submission by user ${user.email}`);
				}
			} catch (offerError) {
				console.error('Error creating membership offer for job submission:', offerError);
				// Don't fail the whole process if offer creation fails
			}
		}

		// Send service request confirmation email
		try {
			await emailService.send({
				to: user.email,
				replyTo: listing.notification_email || listing.email,
				subject: `Your Service Request has been Submitted!`,
				react: ServiceRequestConfirmationEmail({
					job: job,
					customerName: user.first_name,
					customerEmail: user.email,
					customerPhone: user.phone,
					contactPreference: job.contact_preference as "sms" | "phone" | "email",
					message: job.message,
					category: job.category,
					membershipLevel: user.membership_level,
				}),
			});
		} catch (error) {
			console.error(`Failed to send service request confirmation email for job ${job.id}:`, error);
			// Do not fail the whole process for email errors
		}

		// Send SMS confirmation to customer
		try {
			// Only send SMS if user has opted in and has a phone number
			if (job.sms_opt_in && user.phone) {
				const { smsService } = await import("@/lib/services/sms.service");

				const message = `Your RV Help service request has been submitted! We'll notify you when providers respond.\n\nLog in to view and respond: ${process.env.NEXT_PUBLIC_APP_URL}/service-requests/${job.id}`;

				await smsService.sendToUser(user.phone, message);
				console.log(`SMS confirmation sent to user ${user.email} for job ${job.id}`);
			}
		} catch (error) {
			console.error(`Failed to send SMS confirmation for job ${job.id}:`, error);
			// Do not fail the whole process for SMS errors
		}

		// Create timeline events
		if (validatedData) {
			try {
				if (validatedData.warranty_request_id) {
					// For warranty requests - customer registered
					await timelineService.createTimelineUpdate({
						job_id: job.id,
						warranty_request_id: validatedData.warranty_request_id,
						event_type: "CUSTOMER_REGISTERED",
						notes: `Service request submitted: ${validatedData.rv_year} ${validatedData.rv_make} ${validatedData.rv_model}`,
						updated_by_id: user.id
					});
				} else {
					// For regular service requests - job started
					await timelineService.createTimelineUpdate({
						job_id: job.id,
						event_type: "JOB_STARTED",
						notes: `Service request submitted: ${validatedData.rv_year || ''} ${validatedData.rv_make || ''} ${validatedData.rv_model || ''} ${validatedData.category}`.trim(),
						updated_by_id: user.id
					});
				}

				// Create timeline event for provider invitation (if provider was invited)
				if (!isWarranty && validatedData.listing_id && job.quotes?.length > 0) {
					// Get listing info for timeline
					const listingInfo = await prisma.listing.findUnique({
						where: { id: validatedData.listing_id },
						select: { business_name: true, first_name: true, last_name: true }
					});

					if (listingInfo) {
						await timelineService.createTimelineUpdate({
							job_id: job.id,
							event_type: "TECHNICIAN_INVITED",
							notes: `Service provider invited: ${listingInfo.business_name || `${listingInfo.first_name} ${listingInfo.last_name}`}`,
							updated_by_id: user.id
						});
					}
				}
			} catch (timelineError) {
				console.error('Error creating timeline events:', timelineError);
				// Don't fail the whole process if timeline fails
			}
		}

		// Update warranty request if applicable
		if (validatedData?.warranty_request_id) {
			try {
				await prisma.warrantyRequest.update({
					where: { id: validatedData.warranty_request_id },
					data: {
						job_id: job.id,
						status: "JOB_ACCEPTED",
					}
				});

				// Grant one year of RV Help Pro (Standard) membership to warranty customers
				try {
					// Check if user already has a membership
					const existingMembership = await prisma.membership.findUnique({
						where: { user_id: user.id }
					});

					// Only grant membership if user doesn't already have one or has a FREE membership
					if (!existingMembership || existingMembership.level === "FREE") {
						// Calculate one year from now
						const oneYearFromNow = new Date();
						oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

						await membershipService.createOrUpdateMembership({
							userId: user.id,
							level: "STANDARD",
							amountPaid: 0, // Free for warranty customers
							currency: "usd",
							sendWelcomeEmail: false, // We'll send a custom warranty membership email
							sendSlackNotification: false, // Don't send Slack notification for warranty grants
							req: undefined
						});

						// Update user's membership level
						await prisma.user.update({
							where: { id: user.id },
							data: {
								membership_level: "STANDARD"
							}
						});

						console.log(`Granted STANDARD membership to warranty customer ${user.email}`);
					}
				} catch (membershipError) {
					console.error('Error granting membership to warranty customer:', membershipError);
					// Don't fail the whole process if membership grant fails
				}
			} catch (error) {
				console.error('Error updating warranty request:', error);
				// Don't fail the whole process if warranty update fails
			}
		}

		// Send provider notifications (only if listing exists)
		if (listing) {
			try {
				await JobInvitationService.sendLeadNotification(job, listing);
			} catch (error) {
				console.error('Error sending provider notification:', error);
				// Don't fail the whole process if notification fails
			}
		}

		return { success: true, job };
	} catch (error) {
		// Update job status to failed
		await prisma.job.update({
			where: { id: payload.job.id },
			data: {
				error_message: error instanceof Error ? error.message : "Unknown error"
			}
		});
		throw error;
	}
}
