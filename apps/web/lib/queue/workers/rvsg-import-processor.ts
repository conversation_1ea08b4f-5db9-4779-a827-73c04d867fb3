import { prisma } from "@/lib/prisma";
import { adminLogger } from "@/lib/services/admin-log.service";
import { RVSGService } from "@/lib/services/rvsg.service";

export interface RVSGImportJob {
    listingId: string;
    rvtaaMemberId: string;
}

export async function processRVSGImport(job: RVSGImportJob) {
    const { listingId, rvtaaMemberId } = job;

    adminLogger.log(`[Queue] Processing RVSG import for listing ${listingId}`, {
        listingId,
        rvtaaMemberId
    });

    try {
        // Get the listing from the database
        const listing = await prisma.listing.findUnique({
            where: { id: listingId, invite_sent_at: null },
            include: {
                users: {
                    include: {
                        user: true
                    }
                }
            }
        });

        if (!listing) {
            throw new Error(`Listing ${listingId} not found`);
        }


        // Update listing with latest RVSG data
        const { isEligibleForInspection, isEligibleForRepair } = await RVSGService.updateListing(listing);

        // Check eligibility
        if (!isEligibleForInspection && !isEligibleForRepair) {
            adminLogger.log(`[Queue] Listing ${listingId} not eligible for inspection or repair`);
            return { success: true, skipped: true, reason: "Not eligible" };
        }

        // Send invite
        await RVSGService.inviteProvider(listing);

        adminLogger.log(`[Queue] Successfully processed RVSG import for listing ${listingId}`, {
            listingId,
            isEligibleForInspection,
            isEligibleForRepair
        });

        return {
            success: true,
            invited: true,
            isEligibleForInspection,
            isEligibleForRepair
        };

    } catch (error) {
        adminLogger.log(`[Queue] Error processing RVSG import for listing ${listingId}`, {
            listingId,
            error: error instanceof Error ? error.message : "Unknown error"
        });

        // Re-throw the error so the queue system can retry
        throw error;
    }
} 