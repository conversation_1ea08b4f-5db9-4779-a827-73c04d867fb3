import { SmsVerificationRemovedEmail } from "@/components/email-templates/SmsVerificationRemovedEmail";
import config from "@/config";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { adminLogger } from "@/lib/services/admin-log.service";
import { smsService } from "@/lib/services/sms.service";

export interface SmsNotificationPayload {
    listingId: string;
    phoneNumber: string;
    message: string;
    jobId: string;
    retryCount?: number;
}

const MAX_RETRIES = 3;

export async function processSmsNotification(payload: SmsNotificationPayload) {
    const { listingId, phoneNumber, message, jobId, retryCount = 0 } = payload;

    try {
        // Send SMS
        await smsService.sendToProvider(phoneNumber, message);

        console.log(`SMS notification sent successfully to ${phoneNumber} for job ${jobId}`);

        return { success: true };
    } catch (error) {
        console.error(`SMS notification failed for job ${jobId}, attempt ${retryCount + 1}:`, error);

        // Check for specific Twilio error codes that indicate permanent failures
        const errorCode = error.code;
        const isPermanentFailure = [
            21606, // Invalid phone number
            21211, // Invalid phone number
            21614, // Invalid phone number
            21615, // Invalid phone number
            21212  // Invalid From Number
        ].includes(errorCode);

        if (isPermanentFailure || retryCount >= MAX_RETRIES - 1) {
            // This is a permanent failure or we've exhausted retries
            await handleSmsFailure(listingId, phoneNumber, errorCode, jobId);
            return { success: false, permanent: true };
        }

        // This is a temporary failure, let the queue retry
        throw error;
    }
}

async function handleSmsFailure(listingId: string, phoneNumber: string, errorCode: number, jobId: string) {
    try {
        // Get listing details
        const listing = await prisma.listing.findUnique({
            where: { id: listingId },
            include: {
                owner: true
            }
        });

        if (!listing) {
            console.error(`Listing ${listingId} not found when handling SMS failure`);
            return;
        }

        // Update listing to remove SMS verification
        await prisma.listing.update({
            where: { id: listingId },
            data: {
                sms_verified_at: null
            }
        });

        console.log(`SMS verification removed for listing ${listingId} due to persistent failures`);

        // Send email notification to provider
        const providerName = listing.owner?.first_name || listing.first_name || "Provider";
        const businessName = listing.business_name;

        await emailService.send({
            to: listing.notification_email || listing.email,
            subject: "SMS Verification Removed - Action Required",
            react: SmsVerificationRemovedEmail({
                providerName,
                businessName,
                phone: phoneNumber,
                listingId
            })
        });

        console.log(`Email notification sent to ${listing.notification_email || listing.email} about SMS verification removal`);

        // Send Slack notification to josiah-mann channel only if not in test environment
        if (!config.isDevelopment) {
            await sendSlackFailureNotification(listing, errorCode);
        }

    } catch (error) {
        console.error("Error handling SMS failure:", error);
        adminLogger.log("Error handling SMS failure", { listingId, phoneNumber, errorCode, jobId, error });
    }
}

async function sendSlackFailureNotification(listing: any, errorCode: number) {
    try {
        const webhookUrl = process.env.SLACK_WEBHOOK_URL;
        if (!webhookUrl) {
            console.warn("SLACK_WEBHOOK_URL not configured, skipping Slack notification");
            return;
        }

        const message = {
            text: `🚨 SMS Verification Removed`,
            blocks: [
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: `*SMS Verification Removed*\n\n*Provider:* ${listing.business_name || `${listing.first_name} ${listing.last_name}`}\n*Phone:* ${listing.phone}\n*Error Code:* ${errorCode}\n*Listing ID:* ${listing.id}`
                    }
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: "SMS verification has been automatically removed due to persistent delivery failures. Provider has been notified via email."
                    }
                }
            ]
        };

        await fetch(webhookUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(message),
        });

        console.log("Slack notification sent for SMS verification removal");
    } catch (error) {
        console.error("Failed to send Slack notification:", error);
    }
}
