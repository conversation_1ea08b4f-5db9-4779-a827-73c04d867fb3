import { GoogleAnalyticsService } from "@/lib/services/google-analytics.service";

export interface AnalyticsTrackingPayload {
    type: 'lead-form-submission' | 'provider-contact' | 'account-creation';
    data: any;
}

export async function processAnalyticsTracking(payload: AnalyticsTrackingPayload) {
    const { type, data } = payload;

    try {
        switch (type) {
            case 'lead-form-submission':
                await GoogleAnalyticsService.trackServerLeadFormSubmission(data);
                break;

            case 'provider-contact':
                await GoogleAnalyticsService.trackServerProviderContact(data);
                break;

            case 'account-creation':
                await GoogleAnalyticsService.trackServerAccountCreation(data);
                break;

            default:
                console.error(`[Analytics] Unknown tracking type: ${type}`);
                break;
        }

        console.log(`[Analytics] Successfully tracked ${type} event`);
    } catch (error) {
        console.error(`[Analytics] Failed to track ${type} event:`, error);
        // Don't throw error to prevent queue retries for analytics failures
    }
} 