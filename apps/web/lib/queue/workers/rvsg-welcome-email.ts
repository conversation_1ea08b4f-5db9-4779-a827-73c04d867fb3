import { prisma } from "@/lib/prisma";
import { adminLogger } from "@/lib/services/admin-log.service";
import { EmailNewsletterService } from "@/lib/services/emailNewsletter.service";

export interface RVSGWelcomeEmailJob {
    listingId: string;
}

export async function processRVSGWelcomeEmail(job: RVSGWelcomeEmailJob) {
    const { listingId } = job;

    adminLogger.log(`[Queue] Processing RVSG welcome email for listing ${listingId}`, {
        listingId
    });

    try {
        // Get the listing from the database
        const listing = await prisma.listing.findUnique({
            where: { id: listingId },
            include: {
                users: {
                    include: {
                        user: true
                    }
                }
            }
        });

        if (!listing) {
            throw new Error(`Listing ${listingId} not found`);
        }

        // Send welcome email
        await EmailNewsletterService.sendProviderWelcomeEmail(listing);

        adminLogger.log(`[Queue] Successfully sent welcome email for listing ${listingId}`, {
            listingId,
            listingEmail: listing.email
        });

        return {
            success: true,
            listingId,
            listingEmail: listing.email
        };

    } catch (error) {
        adminLogger.log(`[Queue] Error sending welcome email for listing ${listingId}`, {
            listingId,
            error: error instanceof Error ? error.message : "Unknown error"
        });

        // Re-throw the error so the queue system can retry
        throw error;
    }
} 