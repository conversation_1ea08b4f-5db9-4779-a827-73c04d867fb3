import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

/**
 * Check if the current user session is being impersonated by an admin
 * @param session - The current session object (optional, will be fetched if not provided)
 * @returns boolean indicating if the user is being impersonated
 */
export async function isUserImpersonated(session?: any): Promise<boolean> {
  try {
    const currentSession = session || await getServerSession(authOptions);
    return !!currentSession?.user?.isImpersonating;
  } catch (error) {
    console.error("Error checking impersonation status:", error);
    return false;
  }
}

/**
 * Utility function to prevent mark-as-read actions when admin is impersonating
 * @param session - The current session object (optional, will be fetched if not provided)
 * @returns boolean indicating if mark-as-read should be allowed
 */
export async function shouldAllowMarkAsRead(session?: any): Promise<boolean> {
  const isImpersonating = await isUserImpersonated(session);
  return !isImpersonating;
}

/**
 * Get impersonation context for logging/debugging
 * @param session - The current session object (optional, will be fetched if not provided)
 * @returns object with impersonation details
 */
export async function getImpersonationContext(session?: any): Promise<{
  isImpersonating: boolean;
  originalAdminId?: string;
  impersonatedUserId?: string;
}> {
  try {
    const currentSession = session || await getServerSession(authOptions);
    return {
      isImpersonating: !!currentSession?.user?.isImpersonating,
      originalAdminId: currentSession?.user?.originalAdminId,
      impersonatedUserId: currentSession?.user?.id
    };
  } catch (error) {
    console.error("Error getting impersonation context:", error);
    return {
      isImpersonating: false
    };
  }
} 