/**
 * Client-safe offer utility functions
 * These functions can be used in both client and server components
 */

export class OfferUtils {
    /**
     * Check if a job's 72-hour offer window is still active
     */
    static isJobOfferActive(jobCreatedAt: Date): boolean {
        const now = new Date();
        const expirationTime = new Date(jobCreatedAt.getTime() + 72 * 60 * 60 * 1000);
        return now < expirationTime;
    }

    /**
     * Client-side check if a user is eligible for the annual 50% off offer
     * Requirements:
     * - User must be FREE tier (not paid)
     * - The current time is within 72 hours of the most recent service request creation
     * @param user The user object (must include membership_level)
     * @param jobCreatedAt The Date the service request was created
     */
    static isUserEligibleForAnnualOfferClientSide(user: any, jobCreatedAt: Date): {
        eligible: boolean;
        reason?: string;
    } {
        // 1. Must be free tier
        if (user?.membership_level !== "FREE") {
            return {
                eligible: false,
                reason: "User already has paid membership",
            };
        }

        const now = new Date();

        // 2. Must be within 72 hours of service request creation
        if (!jobCreatedAt) {
            return {
                eligible: false,
                reason: "Missing job creation date for offer window check",
            };
        }
        const expirationTime = new Date(jobCreatedAt.getTime() + 72 * 60 * 60 * 1000);
        if (now > expirationTime) {
            return {
                eligible: false,
                reason: "Offer window expired (more than 72 hours since service request)",
            };
        }

        // Eligible!
        return {
            eligible: true,
        };
    }
} 