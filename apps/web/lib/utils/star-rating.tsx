import { Star } from "lucide-react";

export const renderStarRating = (rating: number, num_reviews: number) => {
    const roundedRating = Math.round(rating);
    return (
        <div className="flex items-center gap-1">
            {[1, 2, 3, 4, 5].map((star) => (
                <Star
                    key={star}
                    className={`w-4 h-4 ${star <= roundedRating ? "text-yellow-400 fill-yellow-400" : "text-gray-200"}`}
                />
            ))}
            <span className="text-sm text-gray-600 ml-1">
                ({num_reviews || 0})
            </span>
        </div>
    );
};