import { Badge } from "@/components/ui/badge";
import { QuoteStatus, RejectionReasonType } from "@rvhelp/database";
import { UnifiedLead } from "../../types/global";

/**
 * Converts QuoteStatus enum values to human-readable display strings
 */
export const getQuoteStatusDisplay = (status: QuoteStatus): string => {
	switch (status) {
		case QuoteStatus.PENDING:
			return "Pending";
		case QuoteStatus.ACCEPTED:
			return "Accepted";
		case QuoteStatus.IN_PROGRESS:
			return "In Progress";
		case QuoteStatus.REJECTED:
			return "Provider Declined";
		case QuoteStatus.CUSTOMER_REJECTED:
			return "Proposal Declined";
		case QuoteStatus.WITHDRAWN:
			return "Withdrawn";
		case QuoteStatus.EXPIRED:
			return "Expired";
		case QuoteStatus.COMPLETED:
			return "Completed";
		default:
			return "Unknown";
	}
};

/**
 * Renders a styled Badge component for quote status
 */
export const getQuoteStatusBadge = (status: QuoteStatus) => {
	const variants = {
		PENDING: "secondary",
		ACCEPTED: "default",
		REJECTED: "destructive",
		CUSTOMER_REJECTED: "destructive",
		WITHDRAWN: "destructive",
		EXPIRED: "destructive",
		IN_PROGRESS: "default",
		COMPLETED: "default"
	} as const;

	return (
		<Badge
			className="capitalize"
			variant={variants[status as keyof typeof variants] || "default"}
		>
			{getQuoteStatusDisplay(status)}
		</Badge>
	);
};

export const filterLeadsByStatus = (
	allLeads: UnifiedLead[],
	status: string
) => {
	if (status === "all") return allLeads;

	return allLeads.filter((lead) => {
		if (status === "needs_action") {
			// Leads that need provider action (not completed, rejected, or customer rejected)
			return (
				lead.status !== QuoteStatus.COMPLETED &&
				lead.status !== "completed" &&
				lead.status !== QuoteStatus.REJECTED &&
				lead.status !== "rejected" &&
				lead.status !== QuoteStatus.CUSTOMER_REJECTED &&
				lead.status !== "customer_rejected"
			);
		}

		if (status === "in_progress") {
			return lead.status === QuoteStatus.ACCEPTED || lead.status === "accepted";
		}

		if (status === "completed") {
			return (
				lead.status === QuoteStatus.COMPLETED || lead.status === "completed"
			);
		}

		if (status === "rejected") {
			return lead.status === QuoteStatus.REJECTED || lead.status === "rejected";
		}

		if (status === "proposal_declined") {
			return (
				lead.status === QuoteStatus.CUSTOMER_REJECTED ||
				lead.status === "customer_rejected"
			);
		}

		return lead.status === status;
	});
};

export const getRejectionReasonForHumans = (
	rejectionReason: RejectionReasonType
) => {
	switch (rejectionReason) {
		case RejectionReasonType.TOO_BUSY:
			return "📅 Not available";
		case RejectionReasonType.NOT_A_GOOD_FIT:
			return "🤔 Type of work not a good fit";
		case RejectionReasonType.SCHEDULE_CONFLICT:
			return "🚫 Schedule conflict";
		case RejectionReasonType.OUTSIDE_TRAVEL_AREA:
			return "🚫 Outside travel area";
		case RejectionReasonType.OTHER:
			return "🤷‍♂️ Other";
		default:
			return rejectionReason;
	}
};
