/**
 * Business day utilities for cron job scheduling
 * Business days are defined as Monday-Saturday (excluding Sunday)
 */

/**
 * Check if the current day is a business day (Monday-Saturday)
 * @param date Optional date to check, defaults to current date
 * @returns true if it's a business day, false if it's Sunday
 */
export function isBusinessDay(date?: Date): boolean {
    const checkDate = date || new Date();
    const dayOfWeek = checkDate.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

    // Return true for Monday-Saturday (1-6), false for Sunday (0)
    return dayOfWeek !== 0;
}

/**
 * Get the next business day from a given date
 * @param date Starting date
 * @returns Next business day (skips Sunday)
 */
export function getNextBusinessDay(date: Date): Date {
    const nextDay = new Date(date);
    nextDay.setDate(date.getDate() + 1);

    // If next day is Sunday, skip to Monday
    if (nextDay.getDay() === 0) {
        nextDay.setDate(nextDay.getDate() + 1);
    }

    return nextDay;
}

/**
 * Calculate business days between two dates (excludes Sundays)
 * @param startDate Start date
 * @param endDate End date
 * @returns Number of business days between the dates
 */
export function getBusinessDaysBetween(startDate: Date, endDate: Date): number {
    let count = 0;
    const current = new Date(startDate);

    while (current < endDate) {
        if (isBusinessDay(current)) {
            count++;
        }
        current.setDate(current.getDate() + 1);
    }

    return count;
}

/**
 * Get a user-friendly message explaining business day policy
 */
export function getBusinessDayMessage(): string {
    return "Reminders and notifications are sent Monday through Saturday. Sunday processing is paused.";
} 