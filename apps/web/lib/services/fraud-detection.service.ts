import FraudNotificationEmail from "@/components/email-templates/FraudNotificationEmail";
import prisma from "@/lib/prisma";
import { emailService, slackService } from "@/lib/services";
import { JobStatus, QuoteStatus } from "@rvhelp/database";
import React from "react";
import { BlacklistService } from "./blacklist.service";

export interface FraudDetectionResult {
    isSuspicious: boolean;
    reason?: string;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    details: {
        jobsToday: number;
        similarMessages: number;
        suspiciousPatterns: string[];
    };
}

export class FraudDetectionService {
    /**
     * Check if a job submission is suspicious
     */
    static async checkJobSubmission(userId: string, email: string, message: string): Promise<FraudDetectionResult> {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        // Get all jobs submitted by this user today
        const jobsToday = await prisma.job.count({
            where: {
                user_id: userId,
                created_at: {
                    gte: today,
                    lt: tomorrow
                }
            }
        });

        // Check for similar messages from this user today
        const similarMessages = await prisma.job.count({
            where: {
                user_id: userId,
                created_at: {
                    gte: today,
                    lt: tomorrow
                },
                message: {
                    contains: this.extractKeyWords(message)
                }
            }
        });

        const suspiciousPatterns: string[] = [];
        let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';

        // Check for multiple submissions in one day
        if (jobsToday >= 6) {
            suspiciousPatterns.push(`User submitted ${jobsToday} jobs today`);
            riskLevel = 'HIGH';
        } else if (jobsToday >= 4) {
            suspiciousPatterns.push(`User submitted ${jobsToday} jobs today`);
            riskLevel = 'MEDIUM';
        }

        // Check for similar messages
        if (similarMessages >= 3) {
            suspiciousPatterns.push(`User sent ${similarMessages} similar messages today`);
            riskLevel = riskLevel === 'LOW' ? 'MEDIUM' : 'HIGH';
        }

        // Check for very suspicious phrases (high likelihood of fraud)
        const verySuspiciousPhrases = [
            "master rvti"
        ];

        const messageLower = message.toLowerCase();

        const foundVerySuspiciousPhrases = verySuspiciousPhrases.filter(phrase => {
            const phraseLower = phrase.toLowerCase();
            return messageLower.includes(phraseLower);
        });

        if (foundVerySuspiciousPhrases.length > 0) {
            suspiciousPatterns.push(`Message contains very suspicious phrases: ${foundVerySuspiciousPhrases.join(', ')}`);
            riskLevel = 'HIGH';
        }

        // Check for suspicious message content
        const suspiciousKeywords = [
            'urgent', 'emergency', 'asap', 'immediate', 'cash only', 'no insurance',
            'pay cash', 'under the table', 'no receipt', 'discount', 'deal',
            'cheap', 'budget', 'lowest price', 'best deal', 'negotiable'
        ];

        const foundKeywords = suspiciousKeywords.filter(keyword =>
            messageLower.includes(keyword)
        );

        if (foundKeywords.length >= 3) {
            suspiciousPatterns.push(`Message contains suspicious keywords: ${foundKeywords.join(', ')}`);
            riskLevel = riskLevel === 'LOW' ? 'MEDIUM' : 'HIGH';
        }

        // Check for very short or generic messages
        if (message.length < 20) {
            suspiciousPatterns.push('Very short message');
            riskLevel = riskLevel === 'LOW' ? 'MEDIUM' : riskLevel;
        }

        const isSuspicious = suspiciousPatterns.length > 0;

        return {
            isSuspicious,
            reason: suspiciousPatterns.length > 0 ? suspiciousPatterns.join('; ') : undefined,
            riskLevel,
            details: {
                jobsToday,
                similarMessages,
                suspiciousPatterns
            }
        };
    }

    /**
     * Flag a job as potentially fraudulent
     */
    static async flagJobAsFraudulent(jobId: string, reason: string, flaggedBy: string) {
        const job = await prisma.job.update({
            where: { id: jobId },
            data: {
                flagged_for_fraud: true
            },
            include: {
                user: true,
                quotes: {
                    include: {
                        listing: true
                    }
                }
            }
        });

        // Send Slack notification
        await this.sendFraudAlert(job, reason, flaggedBy);

        return job;
    }

    /**
     * Unflag a job (mark as not fraudulent)
     */
    static async unflagJob(jobId: string, unflaggedBy: string) {
        const job = await prisma.job.update({
            where: { id: jobId },
            data: {
                flagged_for_fraud: false
            },
            include: {
                user: true
            }
        });

        // Send Slack notification
        await slackService.sendToJosiahMann({
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "✅ Job Unflagged as Fraudulent",
                        emoji: true
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Job ID:*\n${job.id}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Customer:*\n${job.first_name} ${job.last_name}`
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Email:*\n${job.email}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Unflagged By:*\n${unflaggedBy}`
                        }
                    ]
                },
                {
                    type: "context",
                    elements: [
                        {
                            type: "mrkdwn",
                            text: `Time: ${new Date().toLocaleString()}`
                        }
                    ]
                }
            ]
        });

        return job;
    }

    /**
     * Ban a user and notify all affected providers
     */
    static async banUserAndNotifyProviders(userId: string, reason: string, bannedBy: string, sendNotifications: boolean = true) {
        // Get all jobs and quotes for this user
        const userJobs = await prisma.job.findMany({
            where: { user_id: userId },
            include: {
                quotes: {
                    include: {
                        listing: true
                    }
                }
            }
        });

        // Flag all jobs as fraudulent
        await prisma.job.updateMany({
            where: { user_id: userId },
            data: { flagged_for_fraud: true, status: JobStatus.CANCELLED }
        });

        // Cancel all quotes for this user
        await prisma.quote.updateMany({
            where: { job: { user_id: userId } },
            data: { status: QuoteStatus.REJECTED }
        });

        // Add user to blacklist
        const user = await prisma.user.findUnique({
            where: { id: userId }
        });

        if (user) {
            await BlacklistService.addEntry({
                type: "USER_ID",
                value: userId,
                reason: `Fraudulent activity: Suspicious activity detected`,
                message: "Your account has been suspended due to suspicious activity. Please contact support if you believe this is an error.",
                createdBy: bannedBy
            });

            // Also blacklist the email
            await BlacklistService.addEntry({
                type: "EMAIL",
                value: user.email,
                reason: `Fraudulent activity: Suspicious activity detected`,
                message: "This email address has been blocked due to suspicious activity.",
                createdBy: bannedBy
            });
        }

        // Send notifications to providers if requested
        if (sendNotifications) {
            await this.notifyProvidersOfFraudulentUser(userJobs, reason);
        }

        // Send Slack notification
        await this.sendUserBanAlert(user, userJobs, reason, bannedBy);

        return {
            user,
            jobsAffected: userJobs.length,
            quotesAffected: userJobs.reduce((total, job) => total + job.quotes.length, 0)
        };
    }

    /**
     * Send fraud alert to Slack
     */
    private static async sendFraudAlert(job: any, reason: string, flaggedBy: string) {
        await slackService.sendToJosiahMann({
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "🚨 Potential Fraudulent Job Detected",
                        emoji: true
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Job ID:*\n${job.id}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Customer:*\n${job.first_name} ${job.last_name}`
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Email:*\n${job.email}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Flagged By:*\n${flaggedBy}`
                        }
                    ]
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: `*Reason:*\n${reason}`
                    }
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: `*Message Preview:*\n${job.message.substring(0, 200)}${job.message.length > 200 ? '...' : ''}`
                    }
                },
                {
                    type: "context",
                    elements: [
                        {
                            type: "mrkdwn",
                            text: `Time: ${new Date().toLocaleString()}`
                        }
                    ]
                }
            ]
        });
    }

    /**
     * Send user ban alert to Slack
     */
    private static async sendUserBanAlert(user: any, jobs: any[], reason: string, bannedBy: string) {
        await slackService.sendToJosiahMann({
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "🚫 User Banned for Fraudulent Activity",
                        emoji: true
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*User:*\n${user?.first_name} ${user?.last_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Email:*\n${user?.email}`
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Jobs Affected:*\n${jobs.length}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Banned By:*\n${bannedBy}`
                        }
                    ]
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: `*Reason:*\n${reason}`
                    }
                },
                {
                    type: "context",
                    elements: [
                        {
                            type: "mrkdwn",
                            text: `Time: ${new Date().toLocaleString()}`
                        }
                    ]
                }
            ]
        });
    }

    /**
     * Notify providers about fraudulent user
     */
    private static async notifyProvidersOfFraudulentUser(jobs: any[], reason: string) {
        const providerEmails = new Set<string>();
        const providerData = new Map<string, { name?: string; jobs: any[] }>();

        // Collect all unique provider emails and their data
        jobs.forEach(job => {
            job.quotes.forEach((quote: any) => {
                if (quote.listing?.email) {
                    const email = quote.listing.email;
                    providerEmails.add(email);

                    if (!providerData.has(email)) {
                        providerData.set(email, {
                            name: quote.listing.business_name,
                            jobs: []
                        });
                    }
                    providerData.get(email)!.jobs.push(job);
                }
            });
        });

        // Prepare batch email options
        const emailOptions = Array.from(providerEmails).map(email => {
            const provider = providerData.get(email);
            const job = provider?.jobs[0]; // Use first job for details

            return {
                from: "RVHelp <<EMAIL>>",
                to: email,
                subject: "Important: Lead Flagged as Potentially Fraudulent",
                text: `
                    Dear Provider,

                    We wanted to inform you that a lead you received has been flagged as potentially fraudulent activity.

                    The customer and their associated leads have been suspended from our platform.

                    If you have already started work on this request or would like additional information, please contact our support team immediately.

                    Thank you for your understanding.

                    RV Help Team
                `,
                react: React.createElement(FraudNotificationEmail, {
                    providerName: provider?.name,
                    providerEmail: email,
                    jobId: job?.id,
                    jobMessage: job?.message,
                    customerName: job ? `${job.first_name} ${job.last_name}` : undefined,
                    customerEmail: job?.email
                }),
                emailType: "fraud_notification"
            };
        });

        if (emailOptions.length === 0) {
            console.log("No provider emails to notify about fraudulent user");
            return;
        }

        // Send all notifications in batch
        const batchResult = await emailService.batchSend(emailOptions);

        console.log(`Fraud notification batch result: ${batchResult.results.length} sent, ${batchResult.errors.length} errors`);

        // Log any errors
        if (batchResult.errors.length > 0) {
            console.error("Failed to send some fraud notifications:", batchResult.errors);
        }
    }

    /**
     * Extract key words from message for similarity checking
     */
    private static extractKeyWords(message: string): string {
        // Simple keyword extraction - you could make this more sophisticated
        const words = message.toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter(word => word.length > 3)
            .slice(0, 5); // Take first 5 significant words

        return words.join(' ');
    }

    /**
     * Get fraud statistics
     */
    static async getFraudStats() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const [flaggedJobsToday, totalFlaggedJobs, suspiciousUsersToday] = await Promise.all([
            prisma.job.count({
                where: {
                    flagged_for_fraud: true,
                    created_at: {
                        gte: today,
                        lt: tomorrow
                    }
                }
            }),
            prisma.job.count({
                where: {
                    flagged_for_fraud: true
                }
            }),
            prisma.job.groupBy({
                by: ['user_id'],
                where: {
                    created_at: {
                        gte: today,
                        lt: tomorrow
                    }
                },
                _count: {
                    id: true
                },
                having: {
                    id: {
                        _count: {
                            gte: 3
                        }
                    }
                }
            })
        ]);

        return {
            flaggedJobsToday,
            totalFlaggedJobs,
            suspiciousUsersToday: suspiciousUsersToday.length
        };
    }
} 