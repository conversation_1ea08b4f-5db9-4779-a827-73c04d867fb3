import config from "@/config";

export class GoogleAnalyticsService {
    private static readonly GA_ID = 'G-RJ4TCPN4LS';
    private static readonly ADS_CONVERSION_ID = 'AW-17219812383';
    private static readonly LEAD_FORM_CONVERSION_LABEL = 'pyNgCJyai-EaEJ_4hZNA';
    private static readonly PROVIDER_CONTACT_CONVERSION_LABEL = 'ZAUdCKO-sPEaEJ_4hZNA';

    /**
     * Check if analytics tracking should be enabled
     */
    private static shouldTrack(): boolean {
        // Temporarily allow tracking in development for testing
        // TODO: Re-enable production-only check after testing


        // Only track in production (rvhelp.com) and when not in development mode
        return !config.isDevelopment &&
            (typeof window === 'undefined' ||
                window.location.hostname === 'rvhelp.com');

    }

    /**
     * Initialize Google Analytics if not already initialized
     */
    private static initializeGA() {
        if (typeof window === 'undefined') return;

        // Initialize dataLayer if it doesn't exist
        if (!window.dataLayer) {
            window.dataLayer = [];
        }

        // Initialize gtag if it doesn't exist
        if (!window.gtag) {
            window.gtag = function () {
                window.dataLayer.push(arguments);
            };
            window.gtag('js', new Date());
            window.gtag('config', this.GA_ID);
            window.gtag('config', this.ADS_CONVERSION_ID);
        }
    }

    /**
     * Track a custom event
     */
    static trackEvent(eventName: string, parameters: Record<string, any> = {}) {
        console.log('[DEBUG] GoogleAnalyticsService.trackEvent called with:', {
            eventName,
            parameters,
            shouldTrack: this.shouldTrack(),
            windowExists: typeof window !== 'undefined',
            gtagExists: typeof window !== 'undefined' && window.gtag
        });

        if (!this.shouldTrack()) {
            console.log(`[GA] Skipping event tracking in non-production: ${eventName}`, parameters);
            return;
        }

        if (typeof window === 'undefined') {
            console.log('[DEBUG] trackEvent: window is undefined, returning');
            return;
        }

        this.initializeGA();

        const eventData = {
            event_category: 'conversion',
            event_label: parameters.event_label || eventName,
            ...parameters,
            value: parameters.value || 1
        };

        window.gtag('event', eventName, eventData);
    }

    /**
     * Track Google Ads conversion
     */
    static trackConversion(conversionId: string, conversionLabel: string, value?: number) {
        if (!this.shouldTrack()) {
            return;
        }

        if (typeof window === 'undefined') {
            return;
        }

        this.initializeGA();

        window.gtag('event', 'conversion', {
            'send_to': `${this.ADS_CONVERSION_ID}/${conversionLabel}`,
            'value': value,
            'currency': 'USD'
        });

    }

    /**
     * Track lead form submission
     */
    static trackLeadFormSubmission(data: {
        listingId: string;
        listingName?: string;
        category: string;
        userId?: string;
        isAnonymous?: boolean;
    }) {
        // Track in Google Analytics
        this.trackEvent('lead_form_submission', {
            event_label: 'lead_form_submission',
            listing_id: data.listingId,
            listing_name: data.listingName,
            category: data.category,
            user_id: data.userId,
            is_anonymous: data.isAnonymous,
            value: 1
        });

        // Track Google Ads conversion
        this.trackConversion(this.ADS_CONVERSION_ID, this.LEAD_FORM_CONVERSION_LABEL, 1);
    }

    /**
     * Track provider contact click (phone or email)
     */
    static trackProviderContact(data: {
        listingId: string;
        listingName?: string;
        contactType: 'phone' | 'email';
        contactValue: string;
        userId?: string;
        isAnonymous?: boolean;
    }) {
        console.log('[DEBUG] GoogleAnalyticsService.trackProviderContact called with data:', data);

        // Track in Google Analytics
        console.log('[DEBUG] GoogleAnalyticsService.trackProviderContact: Calling trackEvent...');
        this.trackEvent('provider_contact_click', {
            event_label: `provider_contact_${data.contactType}`,
            listing_id: data.listingId,
            listing_name: data.listingName,
            contact_type: data.contactType,
            contact_value: data.contactValue,
            user_id: data.userId,
            is_anonymous: data.isAnonymous,
            value: 1
        });
        console.log('[DEBUG] GoogleAnalyticsService.trackProviderContact: trackEvent completed');

        // Track Google Ads conversion with PROVIDER_CONTACT label (not LEAD_FORM label)
        console.log('[DEBUG] GoogleAnalyticsService.trackProviderContact: Calling trackConversion...', {
            conversionId: this.ADS_CONVERSION_ID,
            conversionLabel: this.PROVIDER_CONTACT_CONVERSION_LABEL,
            value: 1
        });
        this.trackConversion(this.ADS_CONVERSION_ID, this.PROVIDER_CONTACT_CONVERSION_LABEL, 1);
        console.log('[DEBUG] GoogleAnalyticsService.trackProviderContact: trackConversion completed');
    }

    /**
     * Track account creation
     */
    static trackAccountCreation(data: {
        userId: string;
        email: string;
        source?: string;
        referrer?: string;
    }) {
        // Track in Google Analytics
        this.trackEvent('account_creation', {
            event_label: 'account_creation',
            user_id: data.userId,
            email: data.email,
            source: data.source,
            referrer: data.referrer,
            value: 1
        });
    }

    /**
     * Track page view
     */
    static trackPageView(page: string, title?: string) {
        if (!this.shouldTrack()) {
            return;
        }

        if (typeof window === 'undefined') return;

        this.initializeGA();

        window.gtag('config', this.GA_ID, {
            page_title: title,
            page_location: window.location.href,
            page_path: page
        });
    }

    /**
     * Server-side tracking for API endpoints
     */
    static async trackServerEvent(eventName: string, parameters: Record<string, any> = {}) {
        if (!this.shouldTrack()) {
            console.log(`[GA] Skipping server-side event tracking in non-production: ${eventName}`, parameters);
            return;
        }

        try {
            // Use Google Analytics Measurement Protocol for server-side tracking
            const measurementId = this.GA_ID;
            const apiSecret = process.env.GA_API_SECRET; // You'll need to add this to your env vars

            if (!apiSecret) {
                return;
            }

            const payload = {
                client_id: parameters.userId || 'anonymous',
                events: [{
                    name: eventName,
                    params: {
                        ...parameters,
                        event_category: 'conversion',
                        event_label: parameters.event_label || eventName,
                        value: parameters.value || 1
                    }
                }]
            };

            const response = await fetch(`https://www.google-analytics.com/mp/collect?measurement_id=${measurementId}&api_secret=${apiSecret}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
            }
        } catch (error) {
        }
    }

    /**
     * Server-side lead form submission tracking
     */
    static async trackServerLeadFormSubmission(data: {
        listingId: string;
        listingName?: string;
        category: string;
        userId?: string;
        isAnonymous?: boolean;
    }) {
        await this.trackServerEvent('lead_form_submission', {
            event_label: 'lead_form_submission',
            listing_id: data.listingId,
            listing_name: data.listingName,
            category: data.category,
            user_id: data.userId,
            is_anonymous: data.isAnonymous,
            value: 1
        });
    }

    /**
     * Server-side provider contact tracking
     */
    static async trackServerProviderContact(data: {
        listingId: string;
        listingName?: string;
        contactType: 'phone' | 'email';
        contactValue: string;
        userId?: string;
        isAnonymous?: boolean;
    }) {
        await this.trackServerEvent('provider_contact_click', {
            event_label: `provider_contact_${data.contactType}`,
            listing_id: data.listingId,
            listing_name: data.listingName,
            contact_type: data.contactType,
            contact_value: data.contactValue,
            user_id: data.userId,
            is_anonymous: data.isAnonymous,
            value: 1
        });
    }

    /**
     * Server-side account creation tracking
     */
    static async trackServerAccountCreation(data: {
        userId: string;
        email: string;
        source?: string;
        referrer?: string;
    }) {
        await this.trackServerEvent('account_creation', {
            event_label: 'account_creation',
            user_id: data.userId,
            email: data.email,
            source: data.source,
            referrer: data.referrer,
            value: 1
        });
    }
}

