import prisma from "@/lib/prisma";

export class CertificationService {
    /**
     * Create a new certification
     */
    static async createCertification(data: {
        name: string;
        display_name: string;
        description?: string;
        training_content?: any;
        terms_conditions?: string;
    }) {
        return await prisma.providerCertification.create({
            data: {
                name: data.name,
                display_name: data.display_name,
                description: data.description,
                training_content: data.training_content,
                terms_conditions: data.terms_conditions,
            },
        });
    }

    /**
     * Get all active certifications
     */
    static async getActiveCertifications() {
        return await prisma.providerCertification.findMany({
            where: {
                is_active: true,
            },
            orderBy: {
                created_at: "desc",
            },
        });
    }

    /**
     * Get certification by name
     */
    static async getCertificationByName(name: string) {
        return await prisma.providerCertification.findUnique({
            where: {
                name,
            },
        });
    }

    /**
     * Get certification status for a specific provider
     */
    static async getProviderCertificationStatus(
        listingId: string,
        certificationName: string
    ) {
        const certification = await this.getCertificationByName(certificationName);
        if (!certification) {
            return null;
        }

        return await prisma.providerCertificationRecord.findFirst({
            where: {
                listing_id: listingId,
                certification_id: certification.id,
            },
            include: {
                certification: true,
                user: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
            },
        });
    }

    /**
     * Get all certification records for a provider
     */
    static async getProviderCertifications(listingId: string) {
        return await prisma.providerCertificationRecord.findMany({
            where: {
                listing_id: listingId,
            },
            include: {
                certification: true,
                user: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
            },
            orderBy: {
                created_at: "desc",
            },
        });
    }

    /**
     * Start certification process for a provider
     */
    static async startCertification(
        listingId: string,
        certificationName: string,
        userId: string
    ) {
        const certification = await this.getCertificationByName(certificationName);
        if (!certification) {
            throw new Error("Certification not found");
        }

        // Check if record already exists
        const existingRecord = await this.getProviderCertificationStatus(
            listingId,
            certificationName
        );

        if (existingRecord) {
            // Update status to IN_PROGRESS if it was PENDING
            if (existingRecord.status === "PENDING") {
                return await prisma.providerCertificationRecord.update({
                    where: {
                        id: existingRecord.id,
                    },
                    data: {
                        status: "IN_PROGRESS",
                    },
                    include: {
                        certification: true,
                        user: {
                            select: {
                                id: true,
                                first_name: true,
                                last_name: true,
                                email: true,
                            },
                        },
                    },
                });
            }
            return existingRecord;
        }

        // Create new record
        return await prisma.providerCertificationRecord.create({
            data: {
                listing_id: listingId,
                certification_id: certification.id,
                user_id: userId,
                status: "IN_PROGRESS",
            },
            include: {
                certification: true,
                user: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
            },
        });
    }

    /**
     * Update training progress
     */
    static async updateTrainingProgress(
        listingId: string,
        certificationName: string,
        progress: any
    ) {
        const certification = await this.getCertificationByName(certificationName);
        if (!certification) {
            throw new Error("Certification not found");
        }

        // First, find the existing record
        const existingRecord = await prisma.providerCertificationRecord.findFirst({
            where: {
                listing_id: listingId,
                certification_id: certification.id,
            },
        });

        if (!existingRecord) {
            throw new Error("Certification record not found. Please start the certification first.");
        }

        return await prisma.providerCertificationRecord.update({
            where: {
                id: existingRecord.id,
            },
            data: {
                training_progress: progress,
                status: "IN_PROGRESS",
            },
            include: {
                certification: true,
                user: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
            },
        });
    }

    /**
     * Complete training and accept terms
     */
    static async completeCertification(
        listingId: string,
        certificationName: string,
        userId: string
    ) {
        const certification = await this.getCertificationByName(certificationName);
        if (!certification) {
            throw new Error("Certification not found");
        }

        // First, find the existing record
        const existingRecord = await prisma.providerCertificationRecord.findFirst({
            where: {
                listing_id: listingId,
                certification_id: certification.id,
            },
        });

        if (!existingRecord) {
            throw new Error("Certification record not found. Please start the certification first.");
        }

        // Update the record using its ID
        return await prisma.providerCertificationRecord.update({
            where: {
                id: existingRecord.id,
            },
            data: {
                status: "COMPLETED",
                training_completed_at: new Date(),
                terms_accepted_at: new Date(),
                terms_accepted_by: userId,
            },
            include: {
                certification: true,
                user: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
            },
        });
    }

    /**
     * Opt out of certification
     */
    static async optOutOfCertification(
        listingId: string,
        certificationName: string,
        reason?: string
    ) {
        const certification = await this.getCertificationByName(certificationName);
        if (!certification) {
            throw new Error("Certification not found");
        }

        // First, find the existing record
        const existingRecord = await prisma.providerCertificationRecord.findFirst({
            where: {
                listing_id: listingId,
                certification_id: certification.id,
            },
        });

        if (!existingRecord) {
            throw new Error("Certification record not found. Please start the certification first.");
        }

        return await prisma.providerCertificationRecord.update({
            where: {
                id: existingRecord.id,
            },
            data: {
                opted_out: true,
                opted_out_at: new Date(),
                opted_out_reason: reason,
                status: "PENDING",
            },
            include: {
                certification: true,
                user: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
            },
        });
    }

    /**
     * Get providers who haven't completed a specific certification
     */
    static async getProvidersWithoutCertification(certificationName: string) {
        const certification = await this.getCertificationByName(certificationName);
        if (!certification) {
            throw new Error("Certification not found");
        }

        // Get all listings that don't have a completed certification record
        const listingsWithoutCertification = await prisma.listing.findMany({
            where: {
                AND: [
                    {
                        status: "ACTIVE",
                    },
                    {
                        OR: [
                            {
                                certification_records: {
                                    none: {
                                        certification_id: certification.id,
                                    },
                                },
                            },
                            {
                                certification_records: {
                                    some: {
                                        certification_id: certification.id,
                                        status: {
                                            not: "COMPLETED",
                                        },
                                        opted_out: false,
                                    },
                                },
                            },
                        ],
                    },
                ],
            },
            include: {
                owner: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
                certification_records: {
                    where: {
                        certification_id: certification.id,
                    },
                    include: {
                        certification: true,
                    },
                },
            },
        });

        return listingsWithoutCertification;
    }

    /**
     * Get certification statistics
     */
    static async getCertificationStats(certificationName: string) {
        const certification = await this.getCertificationByName(certificationName);
        if (!certification) {
            throw new Error("Certification not found");
        }

        const [
            totalProviders,
            completedProviders,
            optedOutProviders,
            pendingProviders,
        ] = await Promise.all([
            prisma.providerCertificationRecord.count({
                where: {
                    certification_id: certification.id,
                },
            }),
            prisma.providerCertificationRecord.count({
                where: {
                    certification_id: certification.id,
                    status: "COMPLETED",
                },
            }),
            prisma.providerCertificationRecord.count({
                where: {
                    certification_id: certification.id,
                    opted_out: true,
                },
            }),
            prisma.providerCertificationRecord.count({
                where: {
                    certification_id: certification.id,
                    status: {
                        in: ["PENDING", "IN_PROGRESS"],
                    },
                    opted_out: false,
                },
            }),
        ]);

        return {
            totalProviders,
            completedProviders,
            optedOutProviders,
            pendingProviders,
            completionRate: totalProviders > 0 ? (completedProviders / totalProviders) * 100 : 0,
        };
    }

    /**
     * Check if a provider has completed a specific certification for warranty job access
     */
    static async hasWarrantyJobAccess(
        listingId: string,
        warrantyRequest?: { company_id: string }
    ): Promise<{
        hasAccess: boolean;
        certificationName?: string;
        certificationStatus?: string;
        companyName?: string;
    }> {
        // If no warranty request, provider has full access
        if (!warrantyRequest) {
            return { hasAccess: true };
        }

        // Get company details to determine certification requirements
        const company = await prisma.company.findUnique({
            where: { id: warrantyRequest.company_id },
            select: { id: true, name: true, abbreviation: true }
        });

        if (!company) {
            return { hasAccess: true };
        }

        // Determine certification name based on company
        let certificationName: string | undefined;
        if (company.name.toLowerCase().includes('keystone') || company.abbreviation === 'KEY') {
            certificationName = 'keystone-warranty';
        }

        // If no certification is required for this company, provider has access
        if (!certificationName) {
            return { hasAccess: true };
        }

        // Check if provider has completed the required certification
        const certificationStatus = await this.getProviderCertificationStatus(
            listingId,
            certificationName
        );

        const hasAccess = certificationStatus?.status === "COMPLETED";

        return {
            hasAccess,
            certificationName,
            certificationStatus: certificationStatus?.status,
            companyName: company.name
        };
    }

    /**
     * Get all providers who have completed a specific certification
     */
    static async getProvidersWithCertification(certificationName: string) {
        const certification = await this.getCertificationByName(certificationName);
        if (!certification) {
            throw new Error("Certification not found");
        }

        return await prisma.providerCertificationRecord.findMany({
            where: {
                certification_id: certification.id,
                status: "COMPLETED",
                opted_out: false,
            },
            include: {
                listing: {
                    include: {
                        locations: true,
                        owner: true,
                    },
                },
                user: {
                    select: {
                        id: true,
                        first_name: true,
                        last_name: true,
                        email: true,
                    },
                },
            },
        });
    }
} 