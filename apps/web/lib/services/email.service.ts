// Web email service with local templates and shared infrastructure
import { MembershipUpgradeEmail } from "@/components/email-templates/MembershipUpgradeEmail";
import { MembershipWelcomeEmail } from "@/components/email-templates/MembershipWelcomeEmail";
import { NoResultsEmail } from "@/components/email-templates/NoResultsEmail";
import PasswordResetEmail, {
    passwordResetText
} from "@/components/email-templates/PasswordResetEmail";
import ServiceRequestPasswordSetupEmail, {
    serviceRequestPasswordSetupText
} from "@/components/email-templates/ServiceRequestPasswordSetupEmail";
import { UserWelcomeEmail } from "@/components/email-templates/user-welcome";
import { WarrantyAuthorizationRequestedEmail, warrantyAuthorizationRequestedText } from "@/components/email-templates/WarrantyAuthorizationRequestedEmail";
import config from "@/config";
import prisma from "@/lib/prisma";
import { formatToE164 } from "@/lib/utils";
import { mockEmailService } from "@/tests/mocks/email-service";
import { User, VerificationToken } from "@prisma/client";
import { SharedEmailService } from "@rvhelp/services";
import React from "react";

export interface EmailOptions {
    to: string;
    replyTo?: string;
    cc?: string;
    from?: string;
    subject: string;
    react: React.ReactElement;
    text?: string;
    emailType?: string;
    data?: Record<string, any>;
    attachments?: Array<{
        filename: string;
        content: Buffer;
        contentType?: string;
    }>;
}

// Web-specific email service interface matching the old EmailService
export interface WebEmailService {
    sendVerificationEmail(
        email: string,
        verificationToken: VerificationToken,
        isMobile?: boolean
    ): Promise<{ success: boolean; error?: any }>;
    sendPasswordResetEmail(
        email: string,
        token: string,
        isMobile: boolean
    ): Promise<{ success: boolean; error?: any }>;
    sendServiceRequestPasswordSetupEmail(params: {
        email: string;
        firstName: string;
        lastName: string;
        serviceRequestId: string;
        token: string;
        rvDetails?: {
            year?: string;
            make?: string;
            model?: string;
            type?: string;
        };
        category?: string;
        message?: string;
    }): Promise<{ success: boolean; error?: any }>;
    sendNoResultsEmail(data: Record<string, any>): Promise<{ success: boolean; error?: any }>;
    sendWelcomeEmail(user: User): Promise<{ success: boolean; error?: any }>;
    send(options: EmailOptions): Promise<{ success: boolean; result?: any; error?: any }>;
    batchSend(options: EmailOptions[]): Promise<{ results: any[]; errors: any[] }>;
    getEmail(emailId: string): Promise<any>;
    sendMembershipWelcomeEmail(params: {
        email: string;
        name: string;
        setupPasswordUrl: string;
        membershipLevel: string;
    }): Promise<{ success: boolean; result?: any; error?: any }>;
    sendMembershipUpgradeEmail(params: {
        email: string;
        name: string;
        membershipLevel: string;
    }): Promise<{ success: boolean; result?: any; error?: any }>;
    sendWarrantyAuthorizationRequestedEmail(params: {
        to: string;
        customerName: string;
        companyName: string;
        rvYear?: string;
        rvMake?: string;
        rvModel?: string;
        rvVin: string;
        estimatedHours: number;
        cause: string;
        correction: string;
        updateNotes?: string;
        componentName?: string;
        warrantyRequestId: string;
    }): Promise<{ success: boolean; error?: any }>;
}

export interface WebEmailServiceConfig {
    appUrl: string;
    portalUrl?: string; // For future portal integration
}

export class WebEmailServiceImpl extends SharedEmailService implements WebEmailService {
    constructor(webConfig: WebEmailServiceConfig) {
        const sharedConfig = {
            from: config.email.from,
            appUrl: webConfig.appUrl,
            allowedDomains: config.email.allowedDomains,
            allowedEmails: config.email.allowedEmails,
            resendApiKey: process.env.RESEND_API_KEY,
            isDevelopment: config.isDevelopment,
            formatToE164: formatToE164,
        };

        super(prisma, sharedConfig);

        if (!webConfig.appUrl) {
            throw new Error("appUrl is required in web config");
        }
    }



    // Individual typed methods provide better type safety and clearer APIs

    async sendVerificationEmail(
        email: string,
        verificationToken: VerificationToken,
        isMobile: boolean = false
    ): Promise<{ success: boolean; error?: any }> {
        // Queue the email verification job
        const { processEmailVerification } = await import(
            "../queue/workers/email-verification"
        );
        await processEmailVerification({
            email,
            verificationTokenId: verificationToken.id,
            isMobile
        });

        return { success: true };
    }

    async sendPasswordResetEmail(
        email: string,
        token: string,
        isMobile: boolean
    ): Promise<{ success: boolean; error?: any }> {
        if (!config.appUrl) {
            throw new Error("NEXT_PUBLIC_APP_URL environment variable is not set");
        }

        const resetUrl = `${config.appUrl}${isMobile ? "/mobile" : ""}/reset-password?token=${token}`;

        return await this.send({
            to: email,
            from: config.email.from,
            subject: "Reset your password",
            react: PasswordResetEmail({ resetLink: resetUrl }),
            text: passwordResetText(resetUrl),
            emailType: "password_reset"
        });
    }

    async sendServiceRequestPasswordSetupEmail(params: {
        email: string;
        firstName: string;
        lastName: string;
        serviceRequestId: string;
        token: string;
        rvDetails?: {
            year?: string;
            make?: string;
            model?: string;
            type?: string;
        };
        category?: string;
        message?: string;
    }): Promise<{ success: boolean; error?: any }> {
        if (!config.appUrl) {
            throw new Error("NEXT_PUBLIC_APP_URL environment variable is not set");
        }

        const passwordSetupLink = `${config.appUrl}/service-requests/${params.serviceRequestId}/setup-password?token=${params.token}&email=${encodeURIComponent(params.email)}`;

        return await this.send({
            to: params.email,
            from: config.email.from,
            subject: "Set up your password to access your RV service request",
            react: ServiceRequestPasswordSetupEmail({
                firstName: params.firstName,
                lastName: params.lastName,
                serviceRequestId: params.serviceRequestId,
                passwordSetupLink,
                rvDetails: params.rvDetails,
                category: params.category,
                message: params.message
            }),
            text: serviceRequestPasswordSetupText(
                params.firstName,
                params.lastName,
                params.serviceRequestId,
                passwordSetupLink,
                params.rvDetails,
                params.category
            ),
            emailType: "service_request_password_setup"
        });
    }

    async sendNoResultsEmail(data: Record<string, any>): Promise<{ success: boolean; error?: any }> {
        console.log("sending no results email", data);
        return await this.send({
            to: "<EMAIL>",
            from: config.email.from,
            cc: data.email,
            subject: "New Service Request - No Results Found",
            react: NoResultsEmail({
                name: data?.name ?? "",
                email: data?.email ?? "",
                phone: data?.phone ?? "",
                location: `${data?.city}, ${data?.state}`,
                rvDetails: `${data?.unit_year} ${data?.unit_make} ${data?.unit_model}`,
                description: data?.description ?? "",
                category: data?.category ?? ""
            }),
            emailType: "no_results"
        });
    }

    async sendWelcomeEmail(user: User) {
        if (user.welcome_email_sent) {
            return { success: true };
        }
        await prisma.user.update({
            where: { id: user.id },
            data: { welcome_email_sent: true }
        });
        return await this.send({
            to: user.email,
            subject: "Welcome to RV Help!",
            react: UserWelcomeEmail({
                firstName: user.first_name
            }),
            emailType: "welcome"
        });
    }


    async sendMembershipWelcomeEmail(params: {
        email: string;
        name: string;
        setupPasswordUrl: string;
        membershipLevel: string;
    }): Promise<{ success: boolean; result?: any; error?: any }> {
        return await this.send({
            to: params.email,
            subject: `Welcome to RV Help ${params.membershipLevel} Membership`,
            react: MembershipWelcomeEmail({
                name: params.name,
                setupPasswordUrl: params.setupPasswordUrl,
                membershipLevel: params.membershipLevel
            }),
            text: `Welcome to RV Help ${params.membershipLevel} Membership! Please set up your password at ${params.setupPasswordUrl}`,
            emailType: "membership_welcome"
        });
    }

    async sendMembershipUpgradeEmail(params: {
        email: string;
        name: string;
        membershipLevel: string;
    }): Promise<{ success: boolean; result?: any; error?: any }> {
        return await this.send({
            to: params.email,
            subject: `Your RV Help Membership Has Been Upgraded`,
            react: MembershipUpgradeEmail({
                name: params.name,
                membershipLevel: params.membershipLevel
            }),
            text: `Congratulations! Your RV Help membership has been upgraded to ${params.membershipLevel}`,
            emailType: "membership_upgrade"
        });
    }

    async sendWarrantyAuthorizationRequestedEmail(params: {
        to: string;
        customerName: string;
        companyName: string;
        rvYear?: string;
        rvMake?: string;
        rvModel?: string;
        rvVin: string;
        estimatedHours: number;
        cause: string;
        correction: string;
        updateNotes?: string;
        componentName?: string;
        warrantyRequestId: string;
    }): Promise<{ success: boolean; error?: any }> {
        const {
            to,
            customerName,
            companyName,
            rvYear,
            rvMake,
            rvModel,
            rvVin,
            estimatedHours,
            cause,
            correction,
            updateNotes,
            componentName,
            warrantyRequestId
        } = params;

        return await this.send({
            to,
            subject: `Authorization Request for Your ${companyName} Warranty Service`,
            react: WarrantyAuthorizationRequestedEmail({
                customerName,
                companyName,
                rvYear,
                rvMake,
                rvModel,
                rvVin,
                estimatedHours,
                cause,
                correction,
                updateNotes,
                componentName,
                warrantyRequestId,
                rvhelpUrl: config.appUrl
            }),
            text: warrantyAuthorizationRequestedText({
                customerName,
                companyName,
                rvYear,
                rvMake,
                rvModel,
                rvVin,
                estimatedHours,
                cause,
                correction,
                updateNotes,
                componentName,
                warrantyRequestId,
                rvhelpUrl: config.appUrl
            }),
            emailType: "warranty_authorization_requested"
        });
    }

    // getEmail method is inherited - no need to override
}

export function getEmailService(): WebEmailService | typeof mockEmailService {
    if (
        process.env.NODE_ENV === "test" ||
        process.env.USE_MOCK_EMAIL === "true"
    ) {
        return mockEmailService;
    }
    return new WebEmailServiceImpl({
        appUrl: config.appUrl,
    });
}

export const emailService = getEmailService();