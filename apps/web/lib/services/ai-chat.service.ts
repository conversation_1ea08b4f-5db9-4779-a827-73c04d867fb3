import { AI_CHAT_SYSTEM_PROMPT, CHAT_SETTINGS } from "@/lib/chat/config";
import { prisma } from "@/lib/prisma";
import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";

// Pre-warm the AI model to avoid cold starts
let modelWarmed = false;
const warmupModel = async () => {
    if (!modelWarmed) {
        try {
            // Send a minimal request to warm up the model
            await streamText({
                model: openai(CHAT_SETTINGS.model),
                messages: [
                    {
                        role: "system",
                        content: "You are a helpful assistant."
                    },
                    {
                        role: "user",
                        content: "Hello"
                    }
                ],
                temperature: CHAT_SETTINGS.temperature,
                maxTokens: 10
            });
            modelWarmed = true;
            console.log("AI model warmed up successfully");
        } catch (error) {
            console.warn("Model warmup failed, continuing without warmup:", error);
        }
    }
};

// Public method to warm up the model
export const warmupAIModel = async (): Promise<void> => {
    await warmupModel();
};

// Public method to check if model is warmed
export const isAIModelWarmed = (): boolean => {
    return modelWarmed;
};

// Simple cache for common responses
const responseCache = new Map<string, string>();
const CACHE_TTL = 1000 * 60 * 60; // 1 hour

const getCachedResponse = (message: string): string | null => {
    const cacheKey = message.toLowerCase().trim();
    const cached = responseCache.get(cacheKey);
    if (cached) {
        return cached;
    }
    return null;
};

const setCachedResponse = (message: string, response: string) => {
    const cacheKey = message.toLowerCase().trim();
    responseCache.set(cacheKey, response);

    // Clean up old entries periodically
    if (responseCache.size > 100) {
        const firstKey = responseCache.keys().next().value;
        responseCache.delete(firstKey);
    }
};

export interface AIChatContext {
    userRvDetails?: {
        make?: string;
        model?: string;
        year?: number;
        type?: string;
    };
    currentLocation?: {
        lat: number;
        lng: number;
    };
    previousMessages?: Array<{
        id: string;
        text: string;
        isUser: boolean;
        timestamp: string;
    }>;
}

export interface AIChatMessageRequest {
    message: string;
    conversationId: string;
    context?: AIChatContext;
}

export interface AIChatMessageResponse {
    message: string;
    conversationId: string;
    suggestions?: string[];
    relatedResources?: Array<{
        title: string;
        url: string;
        type: string;
    }>;
}

export interface AIChatConversationHistoryResponse {
    conversationId: string;
    messages: Array<{
        id: string;
        text: string;
        isUser: boolean;
        timestamp: string;
    }>;
}

export interface AIChatConversationSummary {
    id: string;
    title: string;
    status: string;
    createdAt: string;
    lastMessageAt: string;
    messageCount: number;
    rvDetails?: {
        make?: string;
        model?: string;
        year?: number;
        type?: string;
    };
}

export interface AIChatUserConversationsResponse {
    conversations: AIChatConversationSummary[];
    total: number;
}

export class AIChatService {
    /**
     * Create a new chat conversation for a user
     */
    static async createConversation(userId: string): Promise<{ conversationId: string; status: string }> {
        const conversation = await prisma.aIChatConversation.create({
            data: {
                user_id: userId,
                status: "ACTIVE"
            }
        });

        return {
            conversationId: conversation.id,
            status: "created"
        };
    }

    /**
     * Send a message to the AI assistant and get a response
     */
    static async sendMessage(
        userId: string,
        request: AIChatMessageRequest
    ): Promise<AIChatMessageResponse> {
        // Verify conversation exists and belongs to user
        const conversation = await prisma.aIChatConversation.findFirst({
            where: {
                id: request.conversationId,
                user_id: userId,
                status: "ACTIVE"
            },
            include: {
                messages: {
                    orderBy: { created_at: "asc" }
                }
            }
        });

        if (!conversation) {
            throw new Error("Conversation not found or access denied");
        }

        // Save user message
        const userMessage = await prisma.aIChatMessage.create({
            data: {
                conversation_id: request.conversationId,
                content: request.message,
                is_user_message: true
            }
        });

        console.log("userMessage", userMessage);

        // If this is the first message, generate a title for the conversation
        if (conversation.messages.length === 0) {
            const title = this.generateConversationTitle(request.message);
            await prisma.aIChatConversation.update({
                where: { id: request.conversationId },
                data: { title }
            });
        }

        // Prepare system prompt with context
        const systemPrompt = this.buildSystemPrompt(conversation, request.context);

        console.log("systemPrompt", systemPrompt);

        // Prepare conversation history for AI
        const messages = conversation.messages.map(msg => ({
            role: msg.is_user_message ? "user" : "assistant" as "user" | "assistant",
            content: msg.content
        }));

        // Add the new user message
        messages.push({
            role: "user" as "user",
            content: request.message
        });

        try {
            // Check cache first for common questions
            const cachedResponse = getCachedResponse(request.message);
            if (cachedResponse) {
                console.log("Using cached response for:", request.message);

                // Save cached response to database
                const aiMessage = await prisma.aIChatMessage.create({
                    data: {
                        conversation_id: request.conversationId,
                        content: cachedResponse,
                        is_user_message: false,
                        suggestions: this.generateSuggestions(request.message, request.context),
                        related_resources: this.generateRelatedResources(request.message, request.context)
                    }
                });

                // Update conversation last message timestamp
                await prisma.aIChatConversation.update({
                    where: { id: request.conversationId },
                    data: { last_message_at: new Date() }
                });

                return {
                    message: cachedResponse,
                    conversationId: request.conversationId,
                    suggestions: this.generateSuggestions(request.message, request.context),
                    relatedResources: this.generateRelatedResources(request.message, request.context)
                };
            }

            // Warm up the model if needed
            await warmupModel();

            // Get AI response
            const result = await streamText({
                model: openai(CHAT_SETTINGS.model),
                messages: [
                    {
                        role: "system",
                        content: systemPrompt
                    },
                    ...messages
                ],
                temperature: CHAT_SETTINGS.temperature,
                maxTokens: CHAT_SETTINGS.max_tokens
            });

            console.log("result", result);

            // For now, we'll collect the full response
            // In a real implementation, you might want to stream this
            let aiResponse = "";
            for await (const chunk of result.textStream) {
                aiResponse += chunk;
            }

            // Cache the response for future similar questions
            setCachedResponse(request.message, aiResponse);

            // Generate suggestions and related resources
            const suggestions = this.generateSuggestions(request.message, request.context);
            const relatedResources = this.generateRelatedResources(request.message, request.context);

            // Save AI response
            const aiMessage = await prisma.aIChatMessage.create({
                data: {
                    conversation_id: request.conversationId,
                    content: aiResponse,
                    is_user_message: false,
                    suggestions: suggestions,
                    related_resources: relatedResources
                }
            });

            // Update conversation last message timestamp
            await prisma.aIChatConversation.update({
                where: { id: request.conversationId },
                data: { last_message_at: new Date() }
            });

            return {
                message: aiResponse,
                conversationId: request.conversationId,
                suggestions,
                relatedResources
            };
        } catch (error) {
            console.error("Chat API Error:", error);
            throw new Error("Failed to generate AI response");
        }
    }

    /**
     * Get conversation history
     */
    static async getConversationHistory(
        userId: string,
        conversationId: string
    ): Promise<AIChatConversationHistoryResponse> {
        const conversation = await prisma.aIChatConversation.findFirst({
            where: {
                id: conversationId,
                user_id: userId,
                status: "ACTIVE"
            },
            include: {
                messages: {
                    orderBy: { created_at: "asc" }
                }
            }
        });

        if (!conversation) {
            throw new Error("Conversation not found or access denied");
        }

        return {
            conversationId: conversation.id,
            messages: conversation.messages.map(msg => ({
                id: msg.id,
                text: msg.content,
                isUser: msg.is_user_message,
                timestamp: msg.created_at.toISOString()
            }))
        };
    }

    /**
     * Get all conversations for a user
     */
    static async getUserConversations(userId: string): Promise<AIChatUserConversationsResponse> {
        const conversations = await prisma.aIChatConversation.findMany({
            where: {
                user_id: userId,
                status: "ACTIVE"
            },
            include: {
                messages: {
                    orderBy: { created_at: "desc" },
                    take: 1 // Get only the latest message for count
                },
                _count: {
                    select: { messages: true }
                }
            },
            orderBy: { last_message_at: "desc" }
        });

        return {
            conversations: conversations.map(conv => ({
                id: conv.id,
                title: conv.title || `Conversation ${conv.id.slice(-8)}`,
                status: conv.status,
                createdAt: conv.created_at.toISOString(),
                lastMessageAt: conv.last_message_at?.toISOString() || conv.created_at.toISOString(),
                messageCount: conv._count.messages,
                rvDetails: conv.rv_make ? {
                    make: conv.rv_make,
                    model: conv.rv_model,
                    year: conv.rv_year,
                    type: conv.rv_type
                } : undefined
            })),
            total: conversations.length
        };
    }

    /**
     * Get suggested questions based on user's RV details
     */
    static async getSuggestedQuestions(userId: string): Promise<{ questions: string[] }> {
        // Get user's most recent conversation to understand their RV context
        const recentConversation = await prisma.aIChatConversation.findFirst({
            where: {
                user_id: userId,
                status: "ACTIVE"
            },
            orderBy: { last_message_at: "desc" }
        });

        const baseQuestions = [
            "How do I check my tire pressure?",
            "What's the proper way to level my RV?",
            "How often should I service my generator?",
            "What maintenance should I do before a trip?"
        ];

        // If we have RV context, customize questions
        if (recentConversation?.rv_make && recentConversation?.rv_model) {
            const customQuestions = [
                `How do I winterize my ${recentConversation.rv_year} ${recentConversation.rv_make} ${recentConversation.rv_model}?`,
                `What are common issues with ${recentConversation.rv_make} ${recentConversation.rv_model}?`,
                `How do I check the battery system in my ${recentConversation.rv_make}?`,
                `What's the recommended maintenance schedule for my ${recentConversation.rv_make}?`
            ];
            return { questions: [...customQuestions, ...baseQuestions] };
        }

        return { questions: baseQuestions };
    }

    /**
     * Build system prompt with context
     */
    private static buildSystemPrompt(conversation: any, context?: AIChatContext): string {
        let prompt = AI_CHAT_SYSTEM_PROMPT;

        // Add RV context
        if (conversation.rv_make && conversation.rv_model) {
            prompt += `\n\nRV Context: The user has a ${conversation.rv_year || ""} ${conversation.rv_make} ${conversation.rv_model} ${conversation.rv_type || ""}.`;
        } else if (context?.userRvDetails) {
            const { make, model, year, type } = context.userRvDetails;
            prompt += `\n\nRV Context: The user has a ${year || ""} ${make || ""} ${model || ""} ${type || ""}.`;
        }

        return prompt;
    }

    /**
     * Generate suggested follow-up questions
     */
    private static generateSuggestions(message: string, context?: AIChatContext): string[] {
        // Simple keyword-based suggestions
        const suggestions = [];

        if (message.toLowerCase().includes("winterize")) {
            suggestions.push(
                "What supplies do I need for winterizing?",
                "How often should I winterize my RV?",
                "Can I winterize my RV myself?"
            );
        } else if (message.toLowerCase().includes("tire")) {
            suggestions.push(
                "What's the proper tire pressure for my RV?",
                "How often should I check my tires?",
                "When should I replace my RV tires?"
            );
        } else if (message.toLowerCase().includes("battery")) {
            suggestions.push(
                "How do I maintain my RV batteries?",
                "What type of batteries should I use?",
                "How long do RV batteries typically last?"
            );
        } else {
            suggestions.push(
                "What maintenance should I do regularly?",
                "How do I prepare for a long trip?",
                "What safety checks should I perform?"
            );
        }

        return suggestions.slice(0, 3); // Return max 3 suggestions
    }

    /**
     * Generate conversation title from first message
     */
    private static generateConversationTitle(message: string): string {
        // Simple keyword-based title generation
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes("winterize") || lowerMessage.includes("winter")) {
            return "Winterization Help";
        } else if (lowerMessage.includes("tire") || lowerMessage.includes("tires")) {
            return "Tire Maintenance";
        } else if (lowerMessage.includes("battery") || lowerMessage.includes("batteries")) {
            return "Battery Issues";
        } else if (lowerMessage.includes("plumb") || lowerMessage.includes("water") || lowerMessage.includes("leak")) {
            return "Plumbing Help";
        } else if (lowerMessage.includes("electrical") || lowerMessage.includes("power") || lowerMessage.includes("outlet")) {
            return "Electrical Issues";
        } else if (lowerMessage.includes("appliance") || lowerMessage.includes("fridge") || lowerMessage.includes("stove")) {
            return "Appliance Help";
        } else if (lowerMessage.includes("hvac") || lowerMessage.includes("heat") || lowerMessage.includes("ac") || lowerMessage.includes("air")) {
            return "HVAC Issues";
        } else if (lowerMessage.includes("slide") || lowerMessage.includes("slide-out")) {
            return "Slide-out Problems";
        } else if (lowerMessage.includes("awning")) {
            return "Awning Issues";
        } else if (lowerMessage.includes("level") || lowerMessage.includes("jacks")) {
            return "Leveling Help";
        } else if (lowerMessage.includes("generator")) {
            return "Generator Issues";
        } else if (lowerMessage.includes("propane") || lowerMessage.includes("gas")) {
            return "Propane/Gas Issues";
        } else if (lowerMessage.includes("roof") || lowerMessage.includes("ceiling")) {
            return "Roof Issues";
        } else if (lowerMessage.includes("brake") || lowerMessage.includes("brakes")) {
            return "Brake Problems";
        } else if (lowerMessage.includes("maintenance") || lowerMessage.includes("service")) {
            return "General Maintenance";
        } else {
            // Extract first few words as title
            const words = message.split(' ').slice(0, 4).join(' ');
            return words.length > 0 ? words : "New Conversation";
        }
    }

    /**
     * Generate related resources
     */
    private static generateRelatedResources(message: string, context?: AIChatContext): Array<{ title: string; url: string; type: string }> {
        const resources = [];

        if (message.toLowerCase().includes("winterize")) {
            resources.push({
                title: "RV Winterization Guide",
                url: "/resources/winterization-guide",
                type: "manual"
            });
        } else if (message.toLowerCase().includes("tire")) {
            resources.push({
                title: "Tire Maintenance Guide",
                url: "/resources/tire-maintenance",
                type: "manual"
            });
        } else if (message.toLowerCase().includes("battery")) {
            resources.push({
                title: "Battery Care Guide",
                url: "/resources/battery-care",
                type: "manual"
            });
        }

        // Add general resources
        resources.push({
            title: "RV Maintenance Checklist",
            url: "/resources/maintenance-checklist",
            type: "checklist"
        });

        return resources.slice(0, 3); // Return max 3 resources
    }
}
