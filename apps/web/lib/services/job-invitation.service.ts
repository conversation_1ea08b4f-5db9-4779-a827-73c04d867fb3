import { BatchInvitationConfirmationEmail } from "@/components/email-templates/jobs/customer/BatchInvitationConfirmationEmail";
import { LeadRejectionEmail } from "@/components/email-templates/jobs/customer/LeadRejectionEmail";
import { NewLeadEmail } from "@/components/email-templates/jobs/provider/NewLeadEmail";
import { WarrantyLeadEmail } from "@/components/email-templates/jobs/provider/WarrantyLeadEmail";
import prisma from "@/lib/prisma";
import { queueMessage } from "@/lib/queue/qstash";
import { emailService, slackService } from "@/lib/services";
import { getDrivingDistance } from "@/lib/utils/distance";
import { Job, JobStatus, Listing, QuoteStatus, RejectionReasonType, TimelineEventType } from "@rvhelp/database";
import config from "../../config";
import { JobWithUserAndLocation, ListingWithLocation } from "../../types/global";
import { getCategoryName } from "../categories";
import { setActiveListingLocation } from "../location-utils";
import { url } from "../url";
import { adminLogger } from "./admin-log.service";
import { timelineService } from "./timeline.service";

type ContactPreference = "sms" | "phone" | "email";
type Category = "rv-repair" | "rv-inspection";

export interface InviteProvidersParams {
    jobId: string;
    providerIds: string[];
    userId: string;
}

export interface InviteProvidersResult {
    success: boolean;
    quotes?: any[];
    error?: string;
    message?: string;
}

export class JobInvitationService {
    /**
     * Invite providers to a job with all business logic
     */
    static async inviteProviders(params: InviteProvidersParams): Promise<InviteProvidersResult> {
        const { jobId, providerIds, userId } = params;

        try {
            // Verify the job exists and user has access
            const job = await prisma.job.findUnique({
                where: { id: jobId },
                include: {
                    quotes: {
                        include: {
                            listing: true
                        }
                    },
                    user: true
                }
            });

            if (!job) {
                return {
                    success: false,
                    error: "Job not found"
                };
            }

            // Check if user is the customer for this request
            if (job.user_id !== userId) {
                return {
                    success: false,
                    error: "Unauthorized"
                };
            }

            // Check provider invitation limits based on membership
            const activeQuotes = job.quotes.filter(
                (quote) =>
                    quote.status !== QuoteStatus.REJECTED &&
                    quote.status !== QuoteStatus.WITHDRAWN
            );

            // Free users can only invite 1 provider, paid users can invite up to 5
            const isPaidUser =
                job.user.membership_level === "STANDARD" ||
                job.user.membership_level === "PREMIUM";
            const maxInvitations = isPaidUser || job.warranty_request_id ? 5 : 1;
            const currentInvitations = activeQuotes.length;
            const remainingInvitations = maxInvitations - currentInvitations;

            // Get existing provider IDs to avoid duplicates
            const existingProviderIds = job.quotes.map((l) => l.listing_id);
            const newProviderIds = providerIds.filter(
                (id) => !existingProviderIds.includes(id)
            );

            if (newProviderIds.length === 0) {
                return {
                    success: true,
                    message: "All selected providers have already been invited"
                };
            }

            // Check if the request would exceed the user's invitation limit
            if (newProviderIds.length > remainingInvitations) {
                const errorMessage = isPaidUser
                    ? `You can only invite ${remainingInvitations} more provider${remainingInvitations !== 1 ? "s" : ""} (maximum ${maxInvitations} total)`
                    : `Free users can only invite ${maxInvitations} provider total. Upgrade to Pro to invite up to 5 providers`;

                return {
                    success: false,
                    error: errorMessage
                };
            }

            // Create new quotes for each provider
            const quotes = await this.createQuotesForProviders(job, newProviderIds);

            // Update warranty request if applicable
            if (job.warranty_request_id) {
                await this.updateWarrantyRequest(job.warranty_request_id);
                await this.createTimelineUpdates(job, quotes, userId);
            }

            // Add initial message to newly created quotes
            await this.addInitialMessagesToQuotes(job, quotes);

            // Send provider invitations
            await this.sendProviderInvitations(job, quotes);

            return {
                success: true,
                message: `Successfully invited ${quotes.length} provider${quotes.length !== 1 ? "s" : ""}`,
                quotes
            };

        } catch (error) {
            console.error("Error inviting providers:", error);
            return {
                success: false,
                error: "Internal server error"
            };
        }
    }

    /**
     * Create quotes for the specified providers
     */
    private static async createQuotesForProviders(job: any, providerIds: string[]) {
        return await prisma.$transaction(async (tx) => {
            // Get job location for distance calculation
            const jobLocation = job.location as { latitude: number; longitude: number } | null;

            // First create all the quotes
            const createdQuotes = await Promise.all(
                providerIds.map(async (providerId) => {
                    // Get provider location for distance calculation
                    const provider = await tx.listing.findUnique({
                        where: { id: providerId },
                        include: { locations: true }
                    });

                    const location = setActiveListingLocation(provider);
                    // Calculate distance if both locations exist
                    let distance_miles: number | null = null;
                    if (jobLocation && provider?.locations) {
                        distance_miles = await getDrivingDistance({
                            lat: jobLocation.latitude,
                            lng: jobLocation.longitude
                        }, {
                            lat: location.latitude,
                            lng: location.longitude
                        });
                    }

                    return tx.quote.create({
                        data: {
                            job: {
                                connect: { id: job.id }
                            },
                            listing: {
                                connect: { id: providerId }
                            },
                            status: QuoteStatus.PENDING,
                            distance_miles
                        },
                        include: {
                            listing: {
                                include: {
                                    locations: true
                                }
                            }
                        }
                    });
                })
            );

            // Then fetch the quotes with all the listing data
            return Promise.all(
                createdQuotes.map((quote) =>
                    tx.quote.findUnique({
                        where: { id: quote.id },
                        include: {
                            listing: {
                                include: {
                                    users: true,
                                    reviews: true,
                                    locations: true,
                                    _count: {
                                        select: {
                                            reviews: true
                                        }
                                    }
                                }
                            }
                        }
                    })
                )
            );
        });
    }

    /**
     * Update warranty request status
     */
    private static async updateWarrantyRequest(warrantyRequestId: string) {
        await prisma.warrantyRequest.update({
            where: { id: warrantyRequestId },
            data: {
                status: "JOB_REQUESTED"
            }
        });
    }

    /**
     * Create timeline updates for warranty requests
     */
    private static async createTimelineUpdates(job: any, quotes: any[], userId: string) {
        await Promise.all(
            quotes.map(async (quote) => {
                if (job.warranty_request_id) {
                    await timelineService.createTimelineUpdate({
                        warranty_request_id: job.warranty_request_id,
                        event_type: "TECHNICIAN_INVITED" as TimelineEventType,
                        updated_by_id: userId,
                        notes: `Provider invited: ${quote.listing.business_name} - ${quote.listing.first_name} ${quote.listing.last_name}`,
                        listing_id: quote.listing_id
                    });
                }
            })
        );
    }

    /**
     * Add initial messages to newly created quotes
     */
    private static async addInitialMessagesToQuotes(job: any, quotes: any[]) {
        if (job.message?.trim()) {
            try {
                await Promise.all(
                    quotes.map(quote =>
                        prisma.quoteMessage.create({
                            data: {
                                quote_id: quote.id,
                                sender_id: job.user_id,
                                sender_type: 'USER',
                                recipient_id: quote.listing.id,
                                recipient_type: 'PROVIDER',
                                content: job.message,
                                type: 'SYSTEM',
                                attachments: [],
                                metadata: { isInitialMessage: true },
                                read_at: new Date()
                            },
                        })
                    )
                );
            } catch (error) {
                console.error('Failed to add initial messages to invited quotes:', error);
                // Don't fail the invitation process if initial message addition fails
            }
        }
    }

    /**
 * Send provider invitations and customer confirmation
 */
    private static async sendProviderInvitations(job: any, quotes: any[]) {
        // Send provider invitations individually but customer confirmation as batch
        await Promise.all(
            quotes.map(async (quote) => {
                if (quote?.listing) {
                    // Send individual invitation email to each provider
                    await this.sendProviderInvitation(job, quote.listing);
                }
            })
        );

        // Send single batch confirmation email to customer
        await this.sendBatchInvitationConfirmation(job, quotes.map(q => q.listing));
    }

    /**
     * Send invitation email to a single provider
     */
    static async sendProviderInvitation(
        job: Job,
        listing: ListingWithLocation
    ) {
        // define variables
        const rvDetails = `\nRV: ${[job.rv_year, job.rv_make, job.rv_model]
            .filter(Boolean)
            .join(" ")}`

        const locationText = `\nLocation: ${(job.location as { address: string }).address}`;

        const categoryText = getCategoryName(job.category as Category);

        const lead = await prisma.quote.findFirst({
            where: {
                job_id: job.id,
                listing_id: listing.id
            }
        });

        // If no quote found, we can't send notifications
        if (!lead) {
            console.error(`No quote found for job ${job.id} and listing ${listing.id}`);
            return;
        }

        // Check if this is a warranty job and get manufacturer info
        let manufacturerName = null;
        if (job.warranty_request_id) {
            const warrantyRequest = await prisma.warrantyRequest.findUnique({
                where: { id: job.warranty_request_id },
                include: { company: true }
            });
            manufacturerName = warrantyRequest?.company?.name || "Partner";
        }

        const message = manufacturerName
            ? `Pre-Approved ${manufacturerName} Warranty Lead from RV Help\nName: ${job.first_name} ${job.last_name}\nService: ${categoryText}${rvDetails}${locationText}\n\nRespond to lead: ${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${lead.id}`
            : `New lead from RV Help\nName: ${job.first_name} ${job.last_name}\nService: ${categoryText}${rvDetails}${locationText}\n\nRespond to lead: ${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${lead.id}`;

        // Send email notification to provider
        await emailService.send({
            to: listing.notification_email || listing.email,
            subject: manufacturerName
                ? `Pre-Approved ${manufacturerName} Warranty Lead from ${job.first_name} ${job.last_name}`
                : `New Lead from ${job.first_name} ${job.last_name}`,
            replyTo: job.email,
            react: manufacturerName
                ? WarrantyLeadEmail({
                    firstName: job.first_name,
                    lastName: job.last_name,
                    email: job.email,
                    phone: job.phone,
                    contactPreference: job.contact_preference as ContactPreference,
                    location: job.location as {
                        address: string;
                        latitude: number;
                        longitude: number;
                    },
                    message: job.message,
                    category: job.category as Category,
                    leadId: lead.id,
                    manufacturerName
                })
                : NewLeadEmail({
                    firstName: job.first_name,
                    lastName: job.last_name,
                    email: job.email,
                    phone: job.phone,
                    contactPreference: job.contact_preference as ContactPreference,
                    location: job.location as {
                        address: string;
                        latitude: number;
                        longitude: number;
                    },
                    message: job.message,
                    category: job.category as Category,
                    leadId: lead.id
                })
        });

        // If listing has verified SMS, queue SMS notification
        if (
            listing.sms_verified_at &&
            (listing.notification_sms || listing.phone)
        ) {
            try {
                await queueMessage({
                    type: "sms-notification",
                    payload: {
                        listingId: listing.id,
                        phoneNumber: listing.notification_sms || listing.phone,
                        message,
                        jobId: job.id,
                        retryCount: 0
                    }
                });
                console.log(
                    `SMS notification queued for ${listing.notification_sms || listing.phone} for lead ${job.id}`
                );
            } catch (error) {
                console.error("Failed to queue SMS notification:", error);
                // Don't fail the entire process if queueing fails
            }
        }

        // Update lead status to sent
        await prisma.job.update({
            where: { id: job.id },
            data: {
                status: JobStatus.OPEN,
                sent_at: new Date()
            }
        });

        await this.notifySlackLeadSent(job, listing);
    }

    /**
     * Send batch confirmation email to customer about all providers invited
     */
    static async sendBatchInvitationConfirmation(
        job: Job,
        listings: Array<Listing & { location: { city?: string; state?: string } | null }>
    ) {
        // Prepare providers data for the email
        const providers = listings.map(listing => ({
            name: listing.business_name || `${listing.first_name} ${listing.last_name}`,
            location: listing.location
                ? {
                    city: listing.location.city,
                    state: listing.location.state
                }
                : undefined
        }));

        // Send single confirmation email to customer
        await emailService.send({
            to: job.email,
            subject: `Your messages have been sent to ${providers.length} provider${providers.length !== 1 ? 's' : ''}`,
            react: BatchInvitationConfirmationEmail({
                firstName: job.first_name,
                lastName: job.last_name,
                providers,
                message: job.message,
                contactPreference: job.contact_preference as ContactPreference,
                email: job.email,
                phone: job.phone,
                category: job.category as Category,
                jobId: job.id
            })
        });

        if (!job.sent_at) {
            // Update job status to OPEN and set sent_at timestamp
            await prisma.job.update({
                where: { id: job.id },
                data: {
                    status: JobStatus.OPEN,
                    sent_at: new Date()
                }
            });
        }
    }

    static async sendLeadNotification(
        job: JobWithUserAndLocation,
        listing: ListingWithLocation
    ) {
        const lead = await prisma.quote.findFirst({
            where: {
                job_id: job.id,
                listing_id: listing.id
            }
        });

        // If no quote found, we can't send notifications
        if (!lead) {
            console.error(`No quote found for job ${job.id} and listing ${listing.id}`);
            return;
        }

        // Check if this is a warranty job and get manufacturer info
        let manufacturerName = null;
        if (job.warranty_request_id) {
            const warrantyRequest = await prisma.warrantyRequest.findUnique({
                where: { id: job.warranty_request_id },
                include: { company: true }
            });
            manufacturerName = warrantyRequest?.company?.name || "Partner";
        }

        const rvDetails = `\nRV: ${[job.rv_year, job.rv_make, job.rv_model]
            .filter(Boolean)
            .join(" ")}`;
        const locationText = `\nLocation: ${(job.location as { address: string }).address}`;
        const categoryText = getCategoryName(job.category as Category);
        const leadUrl = `${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${lead.id}`;
        const message = manufacturerName
            ? `Pre-Approved ${manufacturerName} Warranty Lead from RV Help\nName: ${job.first_name} ${job.last_name}\nService: ${categoryText}${rvDetails}${locationText}\n\nRespond to lead: ${leadUrl}`
            : `New lead from RV Help\nName: ${job.first_name} ${job.last_name}\nService: ${categoryText}${rvDetails}${locationText}\n\nRespond to lead: ${leadUrl}`;

        // Send email notification to provider
        await emailService.send({
            to: listing.notification_email || listing.email,
            subject: manufacturerName
                ? `Pre-Approved ${manufacturerName} Warranty Lead from ${job.first_name} ${job.last_name}`
                : `New Lead from ${job.first_name} ${job.last_name}`,
            replyTo: job.email,
            react: manufacturerName
                ? WarrantyLeadEmail({
                    firstName: job.first_name,
                    lastName: job.last_name,
                    email: job.email,
                    phone: job.phone,
                    contactPreference: job.contact_preference as ContactPreference,
                    location: job.location as {
                        address: string;
                        latitude: number;
                        longitude: number;
                    },
                    message: job.message,
                    category: job.category as Category,
                    leadId: lead.id,
                    manufacturerName
                })
                : NewLeadEmail({
                    firstName: job.first_name,
                    lastName: job.last_name,
                    email: job.email,
                    phone: job.phone,
                    contactPreference: job.contact_preference as ContactPreference,
                    location: job.location as {
                        address: string;
                        latitude: number;
                        longitude: number;
                    },
                    message: job.message,
                    category: job.category as Category,
                    leadId: lead.id
                })
        });

        // If listing has verified SMS, queue SMS notification
        if (
            listing.sms_verified_at &&
            (listing.notification_sms || listing.phone)
        ) {
            try {
                await queueMessage({
                    type: "sms-notification",
                    payload: {
                        listingId: listing.id,
                        phoneNumber: listing.notification_sms || listing.phone,
                        message,
                        jobId: job.id,
                        retryCount: 0
                    }
                });
                console.log(
                    `SMS notification queued for ${listing.notification_sms || listing.phone} for lead ${job.id}`
                );
            } catch (error) {
                console.error("Failed to queue SMS notification:", error);
                // Don't fail the entire process if queueing fails
            }
        }
    }

    static async slackFailureNotification(listing: Listing, errorCode: string) {
        await slackService.sendToJosiahMann({
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "⚠️ SMS Notification Failed - " + errorCode,
                        emoji: true
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Business:*\n${listing.business_name ||
                                `${listing.first_name} ${listing.last_name}`
                                }`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Phone:*\n${listing.phone}`
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Error:*\n${errorCode}`
                        }
                    ]
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: `*Action Taken:*\nSMS verification has been removed for this listing.`
                    }
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: `*Edit Listing:*\n<${url("edit-listing", {
                            id: listing.id
                        })}|Edit this listing>`
                    }
                },
                {
                    type: "context",
                    elements: [
                        {
                            type: "mrkdwn",
                            text: `Time: ${new Date().toLocaleString()}`
                        }
                    ]
                }
            ]
        });
    }

    static async notifySlackLeadSent(
        lead: Job,
        listing: ListingWithLocation
    ) {
        // Skip Slack notifications in test environment
        if (config.isDevelopment) {
            return;
        }

        // Format lead details for notification
        const rvDetails =
            lead.rv_year || lead.rv_make || lead.rv_model
                ? `\nRV: ${[lead.rv_year, lead.rv_make, lead.rv_model]
                    .filter(Boolean)
                    .join(" ")}`
                : "";

        const locationText = lead.location
            ? `\nLocation: ${(lead.location as { address: string }).address}`
            : "";

        const categoryText =
            lead.category === "rv-repair"
                ? "Mobile RV Repair"
                : lead.category === "rv-inspection"
                    ? "RV Inspection"
                    : lead.category;

        const leadUrl = `${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${lead.id}`;
        const editListingUrl = `${process.env.NEXT_PUBLIC_APP_URL}/provider/business/profile?id=${listing.id}`;
        const listingName =
            listing.business_name || `${listing.first_name} ${listing.last_name}`;
        const listingLocation = listing.location
            ? `${listing.location.city || ""}, ${listing.location.state || ""}`
            : "No location";

        await slackService.sendToProviderLeads({
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "✅ New Lead Sent to Provider",
                        emoji: true
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Provider:*\n${listingName}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Location:*\n${listingLocation}`
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Customer:*\n${lead.first_name} ${lead.last_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Contact:*\n${lead.contact_preference === "email" ? lead.email : lead.phone
                                }`
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Service:*\n${categoryText}${rvDetails}${locationText}`
                        }
                    ]
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: `*Links:*\n• <${leadUrl}|View Lead>\n• <${editListingUrl}|Edit Provider>`
                    }
                },
                {
                    type: "context",
                    elements: [
                        {
                            type: "mrkdwn",
                            text: `Time: ${new Date().toLocaleString()}`
                        }
                    ]
                }
            ]
        });
    }

    static async sendLeadRejectionEmail(
        lead: Job,
        listing: Listing & { location: { city?: string; state?: string } | null },
        rejectionReason: {
            type:
            | RejectionReasonType
            details?: string;
        }
    ) {
        try {
            await emailService.send({
                to: lead.email,
                replyTo: listing.email,
                subject: `Update on your service request to ${listing.business_name || `${listing.first_name} ${listing.last_name}`}`,
                react: LeadRejectionEmail({
                    firstName: lead.first_name,
                    lastName: lead.last_name,
                    providerName:
                        listing.business_name ||
                        `${listing.first_name} ${listing.last_name}`,
                    providerLocation: listing.location
                        ? {
                            city: listing.location.city,
                            state: listing.location.state
                        }
                        : undefined,
                    category: lead.category,
                    rejectionReason
                }),
                emailType: "lead_rejection"
            });
        } catch (error) {
            adminLogger.log("Error sending lead rejection email to customer", {
                leadId: lead.id,
                customerEmail: lead.email,
                error
            });
            console.error("Error sending lead rejection email:", error);
        }
    }
} 