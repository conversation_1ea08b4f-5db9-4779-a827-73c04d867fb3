import prisma from "@/lib/prisma";

export class OfferService {
	/**
	 * Get review-based offer for a user
	 */
	static async getReviewBasedOffer(userId: string): Promise<any | null> {
		try {
			const offer = await prisma.membershipOffer.findFirst({
				where: {
					user_id: userId,
					offer_type: "REVIEW_50_OFF",
					is_active: true,
					used_at: null,
					expires_at: {
						gt: new Date()
					}
				},
				orderBy: {
					created_at: "desc"
				}
			});
			return offer;
		} catch (error) {
			console.error("Error getting review-based offer:", error);
			return null;
		}
	}

	/**
	 * Check if a user is eligible for any active offer
	 */
	static async isUserEligibleForAnnualOffer(userId: string): Promise<{
		eligible: boolean;
		reason?: string;
		offer?: any;
	}> {
		try {
			// Check for review-based offers first (highest priority)
			const reviewOffer = await this.getReviewBasedOffer(userId);
			if (reviewOffer) {
				return {
					eligible: true,
					offer: reviewOffer,
				};
			}

			const offer = await prisma.membershipOffer.findFirst({
				where: {
					user_id: userId,
					is_active: true,
					used_at: null,
					expires_at: {
						gt: new Date()
					}
				},
				orderBy: {
					created_at: "desc"
				}
			});

			if (!offer) {
				return { eligible: false, reason: "No active offers found" };
			}

			return {
				eligible: true,
				offer: offer,
			};
		} catch (error) {
			console.error("Error checking user offer eligibility:", error);
			return { eligible: false, reason: "Database error" };
		}
	}

	/**
	 * Check eligibility by email for non-logged-in users
	 */
	static async isEmailEligibleForAnnualOffer(email: string): Promise<{
		eligible: boolean;
		reason?: string;
		userId?: string;
		offer?: any;
	}> {
		try {
			console.log('🔍 OFFER SERVICE DEBUG: Checking email eligibility for:', email);

			// Check for review-based offers first (highest priority)
			const reviewOffer = await prisma.membershipOffer.findFirst({
				where: {
					email: {
						equals: email,
						mode: "insensitive"
					},
					offer_type: "REVIEW_50_OFF",
					is_active: true,
					used_at: null,
					expires_at: {
						gt: new Date()
					}
				},
				orderBy: {
					created_at: "desc"
				}
			});

			console.log('🎁 REVIEW OFFER DEBUG: Found review offer:', reviewOffer);

			if (reviewOffer) {
				console.log('✅ REVIEW OFFER ELIGIBLE: User is eligible for review-based discount');
				return {
					eligible: true,
					userId: reviewOffer.user_id,
					offer: reviewOffer,
				};
			}

			const offer = await prisma.membershipOffer.findFirst({
				where: {
					email: {
						equals: email,
						mode: "insensitive"
					},
					is_active: true,
					used_at: null,
					expires_at: {
						gt: new Date()
					}
				},
				orderBy: {
					created_at: "desc"
				}
			});

			if (!offer) {
				return { eligible: false, reason: "No active offers found for this email" };
			}

			return {
				eligible: true,
				userId: offer.user_id,
				offer: offer,
			};
		} catch (error) {
			console.error("Error checking email offer eligibility:", error);
			return { eligible: false, reason: "Database error" };
		}
	}

	/**
	 * Mark an offer as used by offer ID
	 */
	static async markOfferUsed(offerId: string): Promise<boolean> {
		try {
			await prisma.membershipOffer.update({
				where: { id: offerId },
				data: {
					used_at: new Date(),
					is_active: false,
				},
			});
			return true;
		} catch (error) {
			console.error("Error marking offer as used:", error);
			return false;
		}
	}

	/**
	 * Mark the most recent active offer as used for a user
	 */
	static async markUserOfferAsUsed(userId: string): Promise<boolean> {
		try {
			// Input validation
			if (!userId || typeof userId !== 'string') {
				console.error("Invalid userId provided to markUserOfferAsUsed:", userId);
				return false;
			}

			// Find the most recent active offer for the user
			const offer = await prisma.membershipOffer.findFirst({
				where: {
					user_id: userId,
					is_active: true,
					used_at: null,
				},
				orderBy: {
					created_at: "desc"
				}
			});

			if (!offer) {
				console.log(`No active offer found for user ${userId}`);
				return false;
			}

			const result = await this.markOfferUsed(offer.id);
			if (result) {
				console.log(`Successfully marked offer ${offer.id} as used for user ${userId}`);
			}
			return result;
		} catch (error) {
			console.error("Error marking user offer as used:", error, { userId });
			return false;
		}
	}

	/**
	 * Mark the most recent active offer as used for an email
	 */
	static async markEmailOfferAsUsed(email: string): Promise<boolean> {
		try {
			// Input validation
			if (!email || typeof email !== 'string' || !email.includes('@')) {
				console.error("Invalid email provided to markEmailOfferAsUsed:", email);
				return false;
			}

			// Find the most recent active offer for the email
			const offer = await prisma.membershipOffer.findFirst({
				where: {
					email: {
						equals: email,
						mode: "insensitive"
					},
					is_active: true,
					used_at: null,
				},
				orderBy: {
					created_at: "desc"
				}
			});

			if (!offer) {
				console.log(`No active offer found for email ${email}`);
				return false;
			}

			const result = await this.markOfferUsed(offer.id);
			if (result) {
				console.log(`Successfully marked offer ${offer.id} as used for email ${email}`);
			}
			return result;
		} catch (error) {
			console.error("Error marking email offer as used:", error, { email });
			return false;
		}
	}

	/**
	 * Validate marketing campaign coupon code
	 */
	static async validateMarketingCampaignCoupon(couponCode: string): Promise<{
		valid: boolean;
		campaign?: any;
		reason?: string;
	}> {
		try {
			const marketingCampaignLead = await prisma.marketingCampaignLead.findFirst({
				where: {
					coupon_code: couponCode,
					coupon_used_at: null,
				},
				include: {
					campaign: true,
				},
			});

			if (!marketingCampaignLead) {
				return { valid: false, reason: "Invalid or expired coupon code" };
			}

			const { campaign } = marketingCampaignLead;

			// Check if campaign is still active and not expired
			if (campaign.status !== 'ACTIVE' || (campaign.expires_at && new Date() > campaign.expires_at)) {
				return { valid: false, reason: "Campaign is no longer active" };
			}

			return {
				valid: true,
				campaign,
			};
		} catch (error) {
			console.error("Error validating marketing campaign coupon:", error);
			return { valid: false, reason: "Database error" };
		}
	}
} 