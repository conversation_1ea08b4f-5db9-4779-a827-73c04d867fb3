import prisma from "@/lib/prisma";
import { User } from "@rvhelp/database";
import bcrypt from "bcryptjs";
import { v4 as uuidv4 } from 'uuid';
import { EmailNewsletterService } from "./emailNewsletter.service";
import { FirstPromoterService } from "./first-promoter.service";
import { smsService } from "./sms.service";

export interface UserState {
    userExists: boolean;
    hasLoggedIn: boolean;
    needsPasswordSetup: boolean;
}

export interface SetWarrantyPasswordParams {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    newsletterSourceTag?: string;
    companyName?: string;
    sms_opt_in?: boolean;
}

export interface SetWarrantyPasswordResult {
    success: boolean;
    message: string;
    user: {
        id: string;
        email: string;
        first_name: string;
        last_name: string;
    };
}

export interface WarrantyRequestFilters {
    representativeId?: string | null;
    status?: string | null;
    component?: string | null;
    search?: string | null;
    rvVin?: string | null;
    rvModel?: string | null;
    page?: number;
    pageSize?: number;
}

export interface WarrantyRequestsResult {
    requests: any[];
    totalCount: number;
}

export interface CreateWarrantyRequestData {
    id: string;
    complaint: string;
    cause?: string;
    correction?: string;
    component_id?: string | null;
    notes_to_provider?: string | null;
    requires_return?: boolean;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    contact_preference?: 'sms' | 'phone';
    location?: {
        address: string;
        longitude?: number;
        latitude?: number;
    };
    attachments?: Array<{
        id?: string | null;
        title: string;
        type: string;
        url: string;
        required: boolean;
        component_id?: string | null;
        completed?: boolean;
    }>;
    rv_vin: string;
    rv_make: string;
    rv_model: string;
    rv_year: string;
    rv_type: string;
    approved_hours?: number;
}

export class WarrantyRequestService {
    /**
     * Check the state of a user for warranty request flow
     */
    static async checkUserState(email: string): Promise<UserState> {
        const user = await prisma.user.findFirst({
            where: {
                email: {
                    equals: email,
                    mode: "insensitive"
                }
            },
            select: {
                id: true,
                last_login: true,
                password: true
            }
        });

        if (!user) {
            return {
                userExists: false,
                hasLoggedIn: false,
                needsPasswordSetup: false
            };
        }

        const hasLoggedIn = !!user.last_login;
        const hasPassword = !!user.password;

        return {
            userExists: true,
            hasLoggedIn: hasLoggedIn,
            needsPasswordSetup: !hasLoggedIn
        };
    }

    /**
     * Set password for existing warranty user and handle newsletter subscription
     */
    static async setWarrantyPassword(params: SetWarrantyPasswordParams): Promise<SetWarrantyPasswordResult> {
        const { firstName, lastName, email, password, newsletterSourceTag, companyName, sms_opt_in } = params;

        // Find the existing user
        const existingUser = await prisma.user.findFirst({
            where: {
                email: {
                    equals: email,
                    mode: "insensitive"
                }
            }
        });

        if (!existingUser) {
            throw new Error("User not found");
        }

        if (existingUser.password) {
            throw new Error("User already has a password set. Please use login instead.");
        }

        // Hash the password
        const hashedPassword = await bcrypt.hash(password, 12);

        // Update the user with password and other details
        const updatedUser = await prisma.user.update({
            where: { id: existingUser.id },
            data: {
                password: hashedPassword,
                first_name: firstName,
                last_name: lastName,
                email_verified_at: new Date(),
                last_login: new Date(), // Set last_login to indicate they've "logged in"
                newsletter_subscribed: true // Subscribe to newsletter
            }
        });

        // Handle newsletter subscription with proper tags - always sync when password is set
        try {
            const tags = [
                "Consumer Action: Logged In",
            ];

            // Add source tag if provided
            if (newsletterSourceTag) {
                tags.push(newsletterSourceTag);
            }

            await EmailNewsletterService.syncNewsletterSubscriber({
                email,
                first_name: firstName,
                last_name: lastName,
                user: updatedUser,
                tags
            });

            // Track with FirstPromoter if available
            try {
                await FirstPromoterService.trackNewsletterSignup({
                    email,
                    userId: updatedUser.id
                });
            } catch (error) {
                console.error("Failed to track FirstPromoter signup:", error);
                // Don't fail the whole request
            }

            console.log(`Successfully subscribed ${email} to newsletter with logged-in tags:`, tags);
        } catch (error) {
            console.error("Failed to sync with newsletter service:", error);
            // Don't fail the password setup if newsletter fails
        }

        return {
            success: true,
            message: "Password set successfully",
            user: {
                id: updatedUser.id,
                email: updatedUser.email,
                first_name: updatedUser.first_name || firstName,
                last_name: updatedUser.last_name || lastName
            }
        };
    }

    /**
     * Create user for warranty request with newsletter subscription
     */
    static async createWarrantyUser(params: {
        email: string;
        firstName: string;
        lastName: string;
        phone?: string;
        companyName?: string;
        source?: string;
    }): Promise<User> {
        const { email, firstName, lastName, phone, companyName, source } = params;

        // Check if user already exists
        const existingUser = await prisma.user.findFirst({
            where: {
                email: {
                    equals: email,
                    mode: "insensitive"
                }
            }
        });

        if (existingUser) {
            return existingUser;
        }

        // Create new user
        const newUser = await prisma.user.create({
            data: {
                email: email.toLowerCase().trim(),
                first_name: firstName,
                last_name: lastName,
                phone,
                newsletter_subscribed: true,
                email_verified_at: new Date() // Auto-verify for warranty users
            }
        });

        return newUser;
    }

    /**
     * Connect existing warranty request to user after password setup
     */
    static async connectWarrantyRequestToUser(warrantyRequestId: string, userId: string): Promise<void> {
        await prisma.warrantyRequest.update({
            where: { id: warrantyRequestId },
            data: { customer_id: userId }
        });
    }

    /**
     * Helper function to get the next member number
     */
    private static async getNextMemberNumber(): Promise<number> {
        const count = await prisma.membership.count({
            where: { is_active: true }
        });
        return count + 1;
    }

    /**
     * Get warranty requests with filtering and pagination
     */
    static async getWarrantyRequests(
        companyId: string,
        userId: string,
        filters: WarrantyRequestFilters = {}
    ): Promise<WarrantyRequestsResult> {
        const {
            representativeId,
            status: statusParam,
            component: componentParam,
            search,
            rvVin: rvVinParam,
            rvModel: rvModelParam,
            page = 1,
            pageSize = 8
        } = filters;

        const skip = (page - 1) * pageSize;

        // Build the where clause for filtering
        const whereClause: any = { company_id: companyId };

        // Filter by representative if specified
        if (representativeId === 'current') {
            whereClause.oem_user_id = userId;
        } else if (representativeId && representativeId !== 'all') {
            whereClause.oem_user_id = representativeId;
        }

        // Filter by status if specified - handle multiple statuses
        if (statusParam) {
            try {
                const statusArray = JSON.parse(statusParam);
                if (Array.isArray(statusArray) && statusArray.length > 0) {
                    whereClause.status = { in: statusArray };
                }
            } catch (error) {
                // Fallback to single status for backward compatibility
                console.log('Error parsing status:', error);
                if (statusParam !== 'all') {
                    whereClause.status = statusParam;
                }
            }
        }

        // Filter by component if specified - handle multiple components
        if (componentParam) {
            try {
                const componentArray = JSON.parse(componentParam);
                if (Array.isArray(componentArray) && componentArray.length > 0) {
                    whereClause.component_id = { in: componentArray };
                }
            } catch (error) {
                console.log('Error parsing component:', error);
                // Fallback to single component for backward compatibility
                if (componentParam !== 'all') {
                    whereClause.component_id = componentParam;
                }
            }
        }

        // Filter by last name if search term is provided
        if (search && search.trim()) {
            whereClause.last_name = {
                contains: search.trim(),
                mode: 'insensitive',
            };
        }

        // Filter by RV VIN if search term is provided
        if (rvVinParam && rvVinParam.trim()) {
            whereClause.rv_vin = {
                contains: rvVinParam.trim(),
                mode: 'insensitive',
            };
        }

        // Filter by RV model if search term is provided
        if (rvModelParam && rvModelParam.trim()) {
            whereClause.rv_model = {
                contains: rvModelParam.trim(),
                mode: 'insensitive',
            };
        }

        const [requests, totalCount] = await prisma.$transaction([
            prisma.warrantyRequest.findMany({
                where: whereClause,
                orderBy: { created_at: 'desc' },
                skip: skip,
                take: pageSize,
                include: {
                    oem_user: true,
                    listing: true,
                    component: true,
                    timeline_updates: {
                        orderBy: { date: 'desc' },
                        include: {
                            updated_by: {
                                select: {
                                    first_name: true,
                                    last_name: true,
                                },
                            },
                        },
                    },
                },
            }),
            prisma.warrantyRequest.count({
                where: whereClause,
            }),
        ]);

        return { requests, totalCount };
    }

    /**
     * Create or upgrade user for warranty request with membership handling
     */
    static async createOrUpgradeUser(email: string, firstName: string, lastName: string): Promise<string> {
        const customerId: string | null = null;
        let existingCustomer = await prisma.user.findUnique({
            where: { email },
            include: { membership: true }
        });

        if (!existingCustomer) {
            // Create user for warranty request
            const newUser = await prisma.user.create({
                data: {
                    email,
                    first_name: firstName,
                    last_name: lastName,
                    role: "USER",
                    membership_level: "STANDARD", // Grant STANDARD membership immediately
                    newsletter_subscribed: true
                }
            });

            // Create membership record
            await prisma.membership.create({
                data: {
                    user_id: newUser.id,
                    level: "STANDARD",
                    member_number: await this.getNextMemberNumber(),
                    amount_paid: 0, // Free for warranty users
                    currency: "usd"
                }
            });

            // Refetch user with membership included
            existingCustomer = await prisma.user.findUnique({
                where: { id: newUser.id },
                include: { membership: true }
            });

            console.log(`Created user and granted STANDARD membership for warranty request: ${existingCustomer!.email}`);
        } else {
            // User exists - check if they need membership upgrade
            if (!existingCustomer.membership || existingCustomer.membership.level === "FREE") {
                // Update user's membership level
                await prisma.user.update({
                    where: { id: existingCustomer.id },
                    data: {
                        membership_level: "STANDARD",
                        member_number: await this.getNextMemberNumber()
                    }
                });

                if (!existingCustomer.membership) {
                    // Create new membership record
                    await prisma.membership.create({
                        data: {
                            user_id: existingCustomer.id,
                            level: "STANDARD",
                            member_number: await this.getNextMemberNumber(),
                            amount_paid: 0, // Free for warranty users
                            currency: "usd"
                        }
                    });
                } else {
                    // Update existing membership
                    await prisma.membership.update({
                        where: { user_id: existingCustomer.id },
                        data: {
                            level: "STANDARD",
                            is_active: true,
                            cancelled_at: null
                        }
                    });
                }

                console.log(`Upgraded existing user to STANDARD membership for warranty request: ${existingCustomer.email}`);
            }
        }

        return existingCustomer.id;
    }

    /**
     * Create a warranty request with all associated logic
     */
    static async createWarrantyRequest(
        data: CreateWarrantyRequestData,
        companyId: string,
        oemUserId: string,
        companyName?: string
    ): Promise<any> {
        const { id, component_id, attachments, ...otherFields } = data;

        // Create or upgrade user with membership handling
        const customerId = await this.createOrUpgradeUser(
            otherFields.email,
            otherFields.first_name,
            otherFields.last_name
        );

        // Set RV make to company name if not provided
        if (!otherFields.rv_make && companyName) {
            otherFields.rv_make = companyName;
        }

        // Create the warranty request with the standard fields and metadata
        const request = await prisma.warrantyRequest.create({
            data: {
                ...otherFields,
                id: id,
                uuid: uuidv4(),
                status: 'REQUEST_APPROVED',
                company_id: companyId,
                oem_user_id: oemUserId,
                created_at: new Date(),
                customer_id: customerId,
                component_id: component_id,
                attachments: attachments,
            },
            include: {
                component: true,
                company: true,
            },
        });

        return request;
    }

    /**
     * Send warranty SMS notification to provider
     */
    static async sendWarrantySmsNotification(quoteId: string): Promise<void> {
        try {
            const quote = await prisma.quote.findUnique({
                where: { id: quoteId },
                include: {
                    job: {
                        include: {
                            warranty_request: {
                                include: {
                                    company: true
                                }
                            }
                        }
                    },
                    listing: true
                }
            });

            if (!quote || !quote.job.warranty_request) {
                return;
            }

            const warrantyRequest = quote.job.warranty_request;
            const listing = quote.listing;

            // Get provider's phone number (prefer notification_sms, fallback to phone)
            const providerPhone = listing.notification_sms || listing.phone;
            if (!providerPhone) {
                console.log(`No phone number available for provider ${listing.id}`);
                return;
            }

            const providerName = listing.business_name || `${listing.first_name} ${listing.last_name}`;
            const companyName = warrantyRequest.company.name;
            const approvedHours = warrantyRequest.approved_hours;
            const hoursText = approvedHours
                ? `${approvedHours} hours`
                : "diagnostic/repair time";
            const workroomUrl = `${process.env.NEXT_PUBLIC_APP_URL}/provider/jobs/${quote.id}`;

            const message = `Congratulations! You've accepted a ${companyName} warranty lead!

This pre-approval covers your dispatch fee and ${hoursText} for diagnostic/repair time on your initial visit. If more time is needed, you can just request additional hours via the workroom and expect to hear back within a few hours.

Make sure to utilize your RV Help workroom, which takes you through the step by step by step process, ensuring access to partnership perks like expedited part shipping and quick, direct payouts!

If you have any questions you can find contact information for your ${companyName} support rep and the RV Help team here as well.

${workroomUrl}`;

            await smsService.sendToProvider(providerPhone, message);

            console.log(`Warranty SMS notification sent to ${providerName} (${providerPhone})`);
        } catch (error) {
            console.error("Failed to send warranty SMS notification:", error);
            // Don't fail the main operation if SMS fails
        }
    }
}