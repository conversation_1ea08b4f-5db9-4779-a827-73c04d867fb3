import { LeadFollowUpEmail } from "@/components/email-templates/jobs/customer/LeadFollowUpEmail";
import { NoResponseEmail } from "@/components/email-templates/jobs/customer/NoResponseEmail";
import { ProviderReminderEmail } from "@/components/email-templates/jobs/provider/LeadReminderEmail";
import prisma from "@/lib/prisma";
import { queueMessage } from "@/lib/queue/qstash";
import { emailService } from "@/lib/services";
import { isBusinessDay } from "@/lib/utils/business-days";
import { JobWithUserAndLocation, QuoteWithListing } from "@/types/global";
import { JobStatus, QuoteMessage, QuoteStatus } from "@rvhelp/database";
import { getCategoryName } from "../categories";
import { setActiveListingLocation } from "../location-utils";
import { adminLogger } from "./admin-log.service";
import { smsService } from "./sms.service";

export interface JobLifecycleServiceInterface {
    sendNewMessageNotification(message: QuoteMessage & { quote: QuoteWithListing & { job: JobWithUserAndLocation } }): Promise<void>;
    send24HourProviderReminders(): Promise<void>;
    send24HourCustomerFollowUps(): Promise<void>;
    send48HourOfferReminder(): Promise<{ processed: number; total: number; timestamp: Date }>;
    send48HourJobReminders(): Promise<{ processed: number; total: number; timestamp: Date }>;
    send1WeekJobReminders(): Promise<{ processed: number; total: number; timestamp: Date }>;
    send10DayJobWarning(): Promise<{ processed: number; total: number; timestamp: Date }>;
    processJobExpiration(): Promise<{ processed: number; total: number; timestamp: Date }>;
    send72HourQuoteReminders(): Promise<{ processed: number; total: number; timestamp: Date }>;
    send1WeekQuoteReminders(): Promise<{ processed: number; total: number; timestamp: Date }>;
    sendRandomProviderReminder(): Promise<any>;
}

export class JobLifecycleService implements JobLifecycleServiceInterface {
    constructor(private readonly emailService: any, private readonly smsService: any) {
        this.emailService = emailService;
        this.smsService = smsService;
    }


    async sendNewMessageNotification(message: QuoteMessage & { quote: QuoteWithListing & { job: JobWithUserAndLocation } }) {
        // Queue the new message notification job
        await queueMessage({
            type: "new-message",
            payload: {
                message
            }
        });
    }

    /**
     * Send 24-hour reminders to providers who haven't responded to job invitations
     * Previously: LeadReminderService.sendLeadReminders()
     */
    async send24HourProviderReminders(): Promise<void> {
        return;
        // Check if today is a business day (Monday-Saturday)
        if (!isBusinessDay()) {
            console.log("Skipping provider reminders - not a business day (Sunday)");
            adminLogger.log("Skipping provider reminders - not a business day (Sunday)");
            return;
        }

        // Find jobs that were created between 24-48 hours ago and have quotes still pending
        const fortyEightHoursAgo = new Date(Date.now() - 48 * 60 * 60 * 1000);
        const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

        // Get all eligible jobs with quotes that are still pending, and haven't had reminders sent, and have no messages
        const quotes = await prisma.quote.findMany({
            where: {
                status: QuoteStatus.PENDING,
                reminder_sent_at: null,
                invited_at: {
                    gte: fortyEightHoursAgo,
                    lte: twentyFourHoursAgo
                },
                messages: {
                    none: {}
                }
            },
            include: {
                listing: true,
                job: {
                    include: {
                        user: true
                    }
                }
            }
        });

        adminLogger.log(
            `Found ${quotes.length} eligible quotes for provider reminders`
        );

        if (quotes.length === 0) {
            return;
        }

        // Prepare batch email options
        const emailOptions = quotes.map(quote => {
            const job = quote.job;
            const providerName =
                quote.listing.business_name ||
                `${quote.listing.first_name} ${quote.listing.last_name}`;

            return {
                to: quote.listing.email,
                subject: `Reminder: Pending Lead from ${job.first_name} ${job.last_name}`,
                react: ProviderReminderEmail({
                    firstName: job.first_name,
                    lastName: job.last_name,
                    email: job.email,
                    phone: job.phone,
                    contactPreference: job.contact_preference as
                        | "sms"
                        | "phone"
                        | "email",
                    message: job.message,
                    category: job.category,
                    providerName,
                    location: job.location as {
                        address: string;
                        latitude: number;
                        longitude: number;
                    },
                    leadId: job.id
                }),
                emailType: "provider_reminder"
            };
        });

        let batchResult;
        let successfulQuotes: typeof quotes = [];

        try {
            // Send all emails in batch with timeout
            const batchPromise = this.emailService.batchSend(emailOptions);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error("Batch email timeout")), 25000); // 25 second timeout
            });

            batchResult = await Promise.race([batchPromise, timeoutPromise]) as any;

            adminLogger.log(
                `Batch email result: ${batchResult.results.length} sent, ${batchResult.errors.length} errors`
            );

            // Process successful emails from batch
            successfulQuotes = quotes.filter((quote, index) => {
                return index < batchResult.results.length && !batchResult.errors.some(error =>
                    error.includes(quote.listing.email)
                );
            });

        } catch (error) {
            console.error("Batch email failed or timed out, falling back to individual emails:", error);
            adminLogger.log("Batch email failed or timed out, falling back to individual emails:", error);

            // Fallback to individual emails
            successfulQuotes = [];

            for (let i = 0; i < quotes.length; i++) {
                const quote = quotes[i];
                const emailOption = emailOptions[i];

                try {
                    // Send individual email with timeout
                    const individualPromise = this.emailService.send(emailOption);
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => reject(new Error("Individual email timeout")), 10000); // 10 second timeout
                    });

                    await Promise.race([individualPromise, timeoutPromise]);
                    successfulQuotes.push(quote);

                    console.log(`Individual email sent successfully to ${quote.listing.email}`);
                } catch (individualError) {
                    console.error(`Failed to send individual email to ${quote.listing.email}:`, individualError);
                    adminLogger.log(`Failed to send individual email to ${quote.listing.email}:`, individualError);
                }
            }
        }

        // Process successful quotes (from either batch or individual sends)
        for (const quote of successfulQuotes) {
            try {
                // Send SMS reminder if phone is available
                if (quote.listing.phone) {
                    try {
                        await this.smsService.sendToProvider(
                            quote.listing.phone,
                            `RV Help Reminder: You have a pending lead from ${quote.job.first_name} ${quote.job.last_name} that needs your attention. Respond at ${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${quote.job.id}/respond`
                        );
                    } catch (smsError) {
                        console.error(`Failed to send SMS to ${quote.listing.phone}:`, smsError);
                        // Don't fail the entire operation if SMS fails
                    }
                }

                // Update the quote to mark that a provider reminder was sent
                await prisma.quote.update({
                    where: { id: quote.id },
                    data: {
                        reminder_sent_at: new Date()
                    }
                });

                console.log(`Successfully processed reminder for quote ${quote.id}`);
            } catch (error) {
                console.error(
                    `Failed to send SMS or update quote ${quote.id}:`,
                    error
                );
                adminLogger.log(
                    `Failed to send SMS or update quote ${quote.id}:`,
                    error
                );
            }
        }

        // Log summary
        const failedCount = quotes.length - successfulQuotes.length;
        adminLogger.log(
            `Provider reminder summary: ${successfulQuotes.length} successful, ${failedCount} failed out of ${quotes.length} total`
        );

        // Log any batch errors if they occurred
        if (batchResult && batchResult.errors && batchResult.errors.length > 0) {
            console.error("Batch email errors:", batchResult.errors);
            adminLogger.log("Provider reminder batch email errors:", batchResult.errors);
        }
    }

    /**
     * Send 24-hour follow-up emails to customers with smart status updates
     * Previously: LeadFollowUpService.sendFollowUpEmails()
     */
    async send24HourCustomerFollowUps(): Promise<void> {
        return;
        // Check if today is a business day (Monday-Saturday)
        if (!isBusinessDay()) {
            console.log("Skipping customer follow-ups - not a business day");
            return;
        }

        // Find jobs that were created 24-25 hours ago and haven't received a follow-up
        const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        // Create a 1-day window: jobs created 24-25 hours ago
        const twentyFiveHoursAgo = new Date(Date.now() - 25 * 60 * 60 * 1000);

        // Get all eligible jobs with their quotes
        const allJobs = await prisma.job.findMany({
            where: {
                created_at: {
                    lte: twentyFourHoursAgo,
                    gte: twentyFiveHoursAgo // Create 1-day window
                },
                follow_up_sent_at: null,
                status: JobStatus.OPEN, // Only open jobs need follow-up
                accepted_quote_id: null // Job hasn't been assigned to a provider yet
            },
            include: {
                quotes: {
                    include: {
                        listing: {
                            include: {
                                locations: true
                            }
                        }
                    }
                },
                user: true
            }
        });

        adminLogger.log(`Found ${allJobs.length} eligible jobs for follow-up`);

        // Group jobs by user_id to send one email per user
        const jobsByUser = allJobs.reduce(
            (acc, job) => {
                if (!acc[job.user_id]) {
                    acc[job.user_id] = [];
                }
                acc[job.user_id].push(job);
                return acc;
            },
            {} as Record<string, typeof allJobs>
        );

        // Prepare batch email options for all users
        const emailOptions = [];
        const jobsToUpdate = [];

        // Process one job per user, prioritizing by urgency
        for (const userId in jobsByUser) {
            const userJobs = jobsByUser[userId];

            // Sort jobs by priority: jobs needing customer action > jobs with mixed responses > jobs waiting for responses
            userJobs.sort((a, b) => {
                const getPriority = (job: (typeof userJobs)[0]) => {
                    const acceptedQuotes = job.quotes.filter(q => q.status === QuoteStatus.ACCEPTED);
                    const declinedQuotes = job.quotes.filter(q => q.status === QuoteStatus.REJECTED);
                    const pendingQuotes = job.quotes.filter(q => q.status === QuoteStatus.PENDING);

                    if (acceptedQuotes.length > 0) return 1; // Highest priority - customer needs to select
                    if (declinedQuotes.length > 0 && pendingQuotes.length === 0) return 2; // All declined - needs new providers
                    return 3; // Still waiting for responses
                };

                return getPriority(a) - getPriority(b);
            });

            // Take the highest priority job
            const job = userJobs[0];

            // Analyze job status to determine what message to send
            const acceptedQuotes = job.quotes.filter(q => q.status === QuoteStatus.ACCEPTED);
            const declinedQuotes = job.quotes.filter(q => q.status === QuoteStatus.REJECTED);
            const pendingQuotes = job.quotes.filter(q => q.status === QuoteStatus.PENDING);

            let followUpType: "select_provider" | "all_declined" | "awaiting_responses" | "mixed_responses";
            let actionMessage: string;

            if (acceptedQuotes.length > 0) {
                // Customer has quotes to review - they need to select a provider
                followUpType = "select_provider";
                actionMessage = `You have ${acceptedQuotes.length} response${acceptedQuotes.length > 1 ? 's' : ''} from provider${acceptedQuotes.length > 1 ? 's' : ''} waiting for your review. Please select a provider to move forward with your service request.`;
            } else if (declinedQuotes.length > 0 && pendingQuotes.length === 0) {
                // All providers declined - customer needs to find new providers
                followUpType = "all_declined";
                actionMessage = `Unfortunately, the provider${declinedQuotes.length > 1 ? 's' : ''} we contacted ${declinedQuotes.length > 1 ? 'were' : 'was'} unable to help with your request. Let's find you ${declinedQuotes.length > 1 ? 'other' : 'another'} provider${declinedQuotes.length > 1 ? 's' : ''}.`;
            } else if (pendingQuotes.length > 0 && acceptedQuotes.length === 0) {
                // Still waiting for responses
                followUpType = "awaiting_responses";
                actionMessage = `We're still waiting to hear back from ${pendingQuotes.length} provider${pendingQuotes.length > 1 ? 's' : ''} about your service request. We'll notify you as soon as ${pendingQuotes.length > 1 ? 'they respond' : 'they respond'}.`;
            } else {
                // Mixed responses - some declined, some pending
                followUpType = "mixed_responses";
                actionMessage = `We have partial responses to your service request. ${declinedQuotes.length} provider${declinedQuotes.length > 1 ? 's' : ''} ${declinedQuotes.length > 1 ? 'were' : 'was'} unable to help, and we're still waiting to hear from ${pendingQuotes.length} ${pendingQuotes.length > 1 ? 'others' : 'other'}.`;
            }

            // Prepare email options for this user
            emailOptions.push({
                to: job.user.email,
                subject: `Update: Your ${getCategoryName(job.category)} Service Request`,
                react: LeadFollowUpEmail({
                    firstName: job.user.first_name,
                    lastName: job.user.last_name,
                    email: job.user.email,
                    phone: job.user.phone,
                    contactPreference: "email" as const,
                    message: job.message,
                    category: job.category,
                    followUpType,
                    actionMessage,
                    jobId: job.id,
                    acceptedQuotesCount: acceptedQuotes.length,
                    declinedQuotesCount: declinedQuotes.length,
                    pendingQuotesCount: pendingQuotes.length,
                    membershipLevel: job.user.membership_level as "FREE" | "STANDARD" | "PREMIUM",
                    // Include provider details for accepted quotes
                    acceptedProviders: acceptedQuotes.map(quote => ({
                        name: quote.listing.business_name || `${quote.listing.first_name} ${quote.listing.last_name}`,
                        location: setActiveListingLocation(quote.listing) || null
                    }))
                }),
                emailType: "customer_followup"
            });

            // Track jobs to update for this user
            jobsToUpdate.push(userId);
        }

        if (emailOptions.length === 0) {
            return;
        }

        // Send all emails in batch
        const batchResult = await this.emailService.batchSend(emailOptions);

        adminLogger.log(
            `Customer follow-up batch email result: ${batchResult.results.length} sent, ${batchResult.errors.length} errors`
        );

        // Update jobs for successful emails
        const successfulUserIds = jobsToUpdate.filter((userId, index) => {
            return index < batchResult.results.length && !batchResult.errors.some(error =>
                error.includes(emailOptions[index].to)
            );
        });

        for (const userId of successfulUserIds) {
            try {
                // Update all jobs for this user to mark that follow-up was sent
                await prisma.job.updateMany({
                    where: {
                        user_id: userId,
                        follow_up_sent_at: null
                    },
                    data: { follow_up_sent_at: new Date() }
                });
            } catch (error) {
                console.error(
                    `Failed to update follow-up timestamp for user ${userId}:`,
                    error
                );
                adminLogger.log(
                    `Failed to update follow-up timestamp for user ${userId}:`,
                    error
                );
            }
        }

        // Log any errors
        if (batchResult.errors.length > 0) {
            console.error("Customer follow-up batch email errors:", batchResult.errors);
            adminLogger.log("Customer follow-up batch email errors:", batchResult.errors);
        }
    }

    /**
     * Send 48-hour offer expiration reminder to customers
     * This is sent exactly 48 hours after job creation (not business days)
     */
    async send48HourOfferReminder(): Promise<{ processed: number; total: number; timestamp: Date }> {
        // TEMPORARILY DISABLED: Non-transactional emails disabled until unsubscribe functionality is implemented
        // OfferReminderEmail is marketing-related and needs proper unsubscribe handling
        console.log("48-hour offer reminder emails temporarily disabled - non-transactional emails require unsubscribe functionality");
        return {
            processed: 0,
            total: 0,
            timestamp: new Date()
        };

        const fortyEightHoursAgo = new Date(Date.now() - 48 * 60 * 60 * 1000);

        try {
            // Find jobs that are:
            // 1. Still OPEN
            // 2. Created exactly 48 hours ago
            // 3. User is on FREE plan
            // 4. Haven't received a 48-hour offer reminder yet
            const eligibleJobs = await prisma.job.findMany({
                where: {
                    status: JobStatus.OPEN,
                    created_at: {
                        gte: new Date(fortyEightHoursAgo.getTime() - 60 * 60 * 1000), // Within 1 hour of 48 hours
                        lte: fortyEightHoursAgo
                    },
                    offer_reminder_48h_sent_at: null,
                    user: {
                        membership_level: "FREE"
                    }
                },
                include: {
                    user: true
                }
            });

            console.log(
                `Found ${eligibleJobs.length} jobs eligible for 48-hour offer reminders`
            );

            let processedCount = 0;

            for (const job of eligibleJobs) {
                try {
                    // Send offer expiration reminder email
                    // DISABLED: OfferReminderEmail temporarily disabled
                    /* await this.emailService.send({
                        to: job.user.email,
                        subject: "⏰ Your 50% off Pro offer expires in 24 hours!",
                        react: OfferReminderEmail({
                            job: job,
                            customerName: job.user.first_name,
                            customerEmail: job.user.email,
                            customerPhone: job.user.phone,
                            contactPreference: job.contact_preference as "sms" | "phone" | "email",
                            message: job.message,
                            category: job.category,
                        })
                    }); */

                    // Mark that 48-hour offer reminder was sent
                    // DISABLED: Not marking as sent since we're not sending the email
                    /* await prisma.job.update({
                        where: { id: job.id },
                        data: { offer_reminder_48h_sent_at: new Date() }
                    }); */

                    processedCount++;
                    console.log(`Sent 48-hour offer reminder for job ${job.id}`);
                } catch (error) {
                    console.error(
                        `Error sending 48-hour offer reminder for job ${job.id}:`,
                        error
                    );
                }
            }

            return {
                processed: processedCount,
                total: eligibleJobs.length,
                timestamp: new Date()
            };
        } catch (error) {
            console.error("Error in 48-hour offer reminder processing:", error);
            throw error;
        }
    }

    /**
     * Send 48-hour reminders to customers about jobs with no responses
     * This is the first escalation after the 24-hour follow-up
     */
    async send48HourJobReminders(): Promise<{ processed: number; total: number; timestamp: Date }> {
        // Check if today is a business day (Monday-Saturday)
        if (!isBusinessDay()) {
            console.log("Skipping 48-hour job reminders - not a business day (Sunday)");
            adminLogger.log("Skipping 48-hour job reminders - not a business day (Sunday)");
            return { processed: 0, total: 0, timestamp: new Date() };
        }

        const fortyEightHoursAgo = new Date(Date.now() - 48 * 60 * 60 * 1000);
        // Create a 1-day window: jobs created 48-49 hours ago
        const fortyNineHoursAgo = new Date(Date.now() - 49 * 60 * 60 * 1000);

        try {
            // Find jobs that are:
            // 1. Still OPEN
            // 2. Created 48-49 hours ago (1-day window)
            // 3. Have no ACCEPTED responses
            // 4. Haven't received a 48-hour reminder yet
            const eligibleJobs = await prisma.job.findMany({
                where: {
                    status: JobStatus.OPEN,
                    created_at: {
                        lte: fortyEightHoursAgo,
                        gte: fortyNineHoursAgo // Create 1-day window
                    },
                    reminder_48h_sent_at: null,
                    quotes: {
                        none: {
                            status: QuoteStatus.ACCEPTED
                        }
                    }
                },
                include: {
                    quotes: true,
                    user: true
                }
            });

            console.log(
                `Found ${eligibleJobs.length} jobs eligible for 48-hour reminders`
            );

            if (eligibleJobs.length === 0) {
                return { processed: 0, total: 0, timestamp: new Date() };
            }

            // Prepare batch email options
            const emailOptions = eligibleJobs.map(job => ({
                to: job.user.email,
                subject: "Your service request - let's get you help!",
                text: "Your service request has been waiting for responses",
                react: NoResponseEmail({
                    customerName: job.user.first_name,
                    job: job as JobWithUserAndLocation,
                    membershipLevel: job.user.membership_level as
                        | "FREE"
                        | "STANDARD"
                        | "PREMIUM"
                }),
                emailType: "48h_job_reminder"
            }));

            // Send all emails in batch
            const batchResult = await this.emailService.batchSend(emailOptions);

            console.log(
                `48-hour job reminder batch result: ${batchResult.results.length} sent, ${batchResult.errors.length} errors`
            );

            // Update jobs for successful emails
            const successfulJobs = eligibleJobs.filter((job, index) => {
                return index < batchResult.results.length && !batchResult.errors.some(error =>
                    error.includes(job.user.email)
                );
            });

            for (const job of successfulJobs) {
                try {
                    // Mark that 48-hour reminder was sent
                    await prisma.job.update({
                        where: { id: job.id },
                        data: { reminder_48h_sent_at: new Date() }
                    });
                } catch (error) {
                    console.error(
                        `Error updating 48-hour reminder timestamp for job ${job.id}:`,
                        error
                    );
                }
            }

            // Log any errors
            if (batchResult.errors.length > 0) {
                console.error("48-hour job reminder batch errors:", batchResult.errors);
            }

            return {
                processed: successfulJobs.length,
                total: eligibleJobs.length,
                timestamp: new Date()
            };
        } catch (error) {
            console.error("Error in 48-hour job reminder processing:", error);
            throw error;
        }
    }

    /**
     * Send 1-week reminders to customers about jobs with no responses
     * This is the second escalation after the 72-hour reminder
     */
    async send1WeekJobReminders(): Promise<{ processed: number; total: number; timestamp: Date }> {
        // Check if today is a business day (Monday-Saturday)
        if (!isBusinessDay()) {
            console.log("Skipping 1-week job reminders - not a business day (Sunday)");
            adminLogger.log("Skipping 1-week job reminders - not a business day (Sunday)");
            return { processed: 0, total: 0, timestamp: new Date() };
        }

        const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        // Create a 1-day window: jobs created 7-8 days ago
        const eightDaysAgo = new Date(Date.now() - 8 * 24 * 60 * 60 * 1000);

        try {
            // Find jobs that are:
            // 1. Still OPEN
            // 2. Created 7-8 days ago (1-day window)
            // 3. Have no ACCEPTED responses
            // 4. Haven't received a 1-week reminder yet
            const eligibleJobs = await prisma.job.findMany({
                where: {
                    status: JobStatus.OPEN,
                    created_at: {
                        lte: oneWeekAgo,
                        gte: eightDaysAgo // Create 1-day window
                    },
                    reminder_1w_sent_at: null,
                    quotes: {
                        none: {
                            status: QuoteStatus.ACCEPTED
                        }
                    }
                },
                include: {
                    quotes: true,
                    user: true
                }
            });

            console.log(
                `Found ${eligibleJobs.length} jobs eligible for 1-week reminders`
            );

            if (eligibleJobs.length === 0) {
                return { processed: 0, total: 0, timestamp: new Date() };
            }

            // Prepare batch email options
            const emailOptions = eligibleJobs.map(job => ({
                to: job.user.email,
                subject: "Last chance - Your service request needs attention",
                text: "Your service request will expire soon without provider responses",
                react: NoResponseEmail({
                    customerName: job.user.first_name,
                    job: job as JobWithUserAndLocation,
                    membershipLevel: job.user.membership_level as
                        | "FREE"
                        | "STANDARD"
                        | "PREMIUM"
                }),
                emailType: "1w_job_reminder"
            }));

            // Send all emails in batch
            const batchResult = await this.emailService.batchSend(emailOptions);

            console.log(
                `1-week job reminder batch result: ${batchResult.results.length} sent, ${batchResult.errors.length} errors`
            );

            // Update jobs for successful emails
            const successfulJobs = eligibleJobs.filter((job, index) => {
                return index < batchResult.results.length && !batchResult.errors.some(error =>
                    error.includes(job.user.email)
                );
            });

            for (const job of successfulJobs) {
                try {
                    // Mark that 1-week reminder was sent
                    await prisma.job.update({
                        where: { id: job.id },
                        data: { reminder_1w_sent_at: new Date() }
                    });
                } catch (error) {
                    console.error(
                        `Error updating 1-week reminder timestamp for job ${job.id}:`,
                        error
                    );
                }
            }

            // Log any errors
            if (batchResult.errors.length > 0) {
                console.error("1-week job reminder batch errors:", batchResult.errors);
            }

            return {
                processed: successfulJobs.length,
                total: eligibleJobs.length,
                timestamp: new Date()
            };
        } catch (error) {
            console.error("Error in 1-week job reminder processing:", error);
            throw error;
        }
    }



    /**
     * Send 10-day warning to customers about jobs that will expire soon
     * This is the final warning before job expiration
     */
    async send10DayJobWarning(): Promise<{ processed: number; total: number; timestamp: Date }> {
        // Check if today is a business day (Monday-Saturday)
        if (!isBusinessDay()) {
            console.log("Skipping 10-day job warnings - not a business day (Sunday)");
            adminLogger.log("Skipping 10-day job warnings - not a business day (Sunday)");
            return { processed: 0, total: 0, timestamp: new Date() };
        }

        const tenDaysAgo = new Date(Date.now() - 10 * 24 * 60 * 60 * 1000);
        // Create a 1-day window: jobs created 10-11 days ago
        const elevenDaysAgo = new Date(Date.now() - 11 * 24 * 60 * 60 * 1000);

        try {
            // Find jobs that are:
            // 1. Still OPEN
            // 2. Created 10-11 days ago (1-day window)
            // 3. Have no ACCEPTED responses
            // 4. Haven't received a 10-day warning yet
            const eligibleJobs = await prisma.job.findMany({
                where: {
                    status: JobStatus.OPEN,
                    created_at: {
                        lte: tenDaysAgo,
                        gte: elevenDaysAgo // Create 1-day window
                    },
                    reminder_1w_sent_at: null, // Use existing field name
                    quotes: {
                        none: {
                            status: QuoteStatus.ACCEPTED
                        }
                    }
                },
                include: {
                    quotes: true,
                    user: true
                }
            });

            console.log(
                `Found ${eligibleJobs.length} jobs eligible for 10-day warnings`
            );

            if (eligibleJobs.length === 0) {
                return { processed: 0, total: 0, timestamp: new Date() };
            }

            // Prepare batch email options
            const emailOptions = eligibleJobs.map(job => ({
                to: job.user.email,
                subject: "Your Service Request - no responses, but we're here to help!",
                text: "Your service request will expire soon without provider responses",
                react: NoResponseEmail({
                    customerName: job.user.first_name,
                    job: job as JobWithUserAndLocation,
                    membershipLevel: job.user.membership_level as
                        | "FREE"
                        | "STANDARD"
                        | "PREMIUM"
                }),
                emailType: "10d_job_warning"
            }));

            // Send all emails in batch
            const batchResult = await this.emailService.batchSend(emailOptions);

            console.log(
                `10-day job warning batch result: ${batchResult.results.length} sent, ${batchResult.errors.length} errors`
            );

            // Update jobs for successful emails
            const successfulJobs = eligibleJobs.filter((job, index) => {
                return index < batchResult.results.length && !batchResult.errors.some(error =>
                    error.includes(job.user.email)
                );
            });

            for (const job of successfulJobs) {
                try {
                    // Mark that 10-day warning was sent
                    await prisma.job.update({
                        where: { id: job.id },
                        data: { reminder_1w_sent_at: new Date() } // Use existing field name
                    });
                } catch (error) {
                    console.error(
                        `Error updating 10-day warning timestamp for job ${job.id}:`,
                        error
                    );
                }
            }

            // Log any errors
            if (batchResult.errors.length > 0) {
                console.error("10-day job warning batch errors:", batchResult.errors);
            }

            return {
                processed: successfulJobs.length,
                total: eligibleJobs.length,
                timestamp: new Date()
            };
        } catch (error) {
            console.error("Error in 10-day job warning processing:", error);
            throw error;
        }
    }

    /**
     * Process job expiration - mark jobs as expired if they haven't received responses
     * This runs after the 1-week reminder has been sent
     */
    async processJobExpiration(): Promise<{ processed: number; total: number; timestamp: Date }> {
        // Check if today is a business day (Monday-Saturday)
        if (!isBusinessDay()) {
            console.log("Skipping job expiration - not a business day (Sunday)");
            adminLogger.log("Skipping job expiration - not a business day (Sunday)");
            return { processed: 0, total: 0, timestamp: new Date() };
        }

        const fourteenDaysAgo = new Date(Date.now() - 14 * 24 * 60 * 60 * 1000);
        // Create a 7-day window: jobs created 14-21 days ago (wider window for expiration)
        const twentyOneDaysAgo = new Date(Date.now() - 21 * 24 * 60 * 60 * 1000);

        try {
            // Find jobs that are:
            // 1. Still OPEN
            // 2. Created 14-21 days ago (7-day window)
            // 3. Have no ACCEPTED responses
            // 4. Have received their 10-day warning
            // 5. Haven't been expired yet
            const eligibleJobs = await prisma.job.findMany({
                where: {
                    status: JobStatus.OPEN,
                    created_at: {
                        lte: fourteenDaysAgo,
                        gte: twentyOneDaysAgo // Create 7-day window
                    },
                    reminder_1w_sent_at: {
                        not: null
                    },
                    expired_at: null,
                    quotes: {
                        none: {
                            status: QuoteStatus.ACCEPTED
                        }
                    }
                },
                include: {
                    quotes: true,
                    user: true
                }
            });

            console.log(
                `Found ${eligibleJobs.length} jobs eligible for expiration`
            );

            let processedCount = 0;

            for (const job of eligibleJobs) {
                try {
                    // Mark job as expired
                    await prisma.job.update({
                        where: { id: job.id },
                        data: {
                            status: JobStatus.EXPIRED,
                            expired_at: new Date()
                        }
                    });

                    processedCount++;
                    console.log(`Expired job ${job.id}`);
                } catch (error) {
                    console.error(
                        `Error expiring job ${job.id}:`,
                        error
                    );
                }
            }

            return {
                processed: processedCount,
                total: eligibleJobs.length,
                timestamp: new Date()
            };
        } catch (error) {
            console.error("Error in job expiration processing:", error);
            throw error;
        }
    }

    /**
     * Send 72-hour reminders to providers about pending quotes
     * This is in addition to the 24-hour provider reminders
     */
    async send72HourQuoteReminders(): Promise<{ processed: number; total: number; timestamp: Date }> {
        // Check if today is a business day (Monday-Saturday)
        if (!isBusinessDay()) {
            console.log("Skipping 72-hour quote reminders - not a business day (Sunday)");
            adminLogger.log("Skipping 72-hour quote reminders - not a business day (Sunday)");
            return { processed: 0, total: 0, timestamp: new Date() };
        }

        const seventyTwoHoursAgo = new Date(Date.now() - 72 * 60 * 60 * 1000);
        // Create a 1-day window: quotes created 72-73 hours ago
        const seventyThreeHoursAgo = new Date(Date.now() - 73 * 60 * 60 * 1000);

        try {
            // Find quotes that are:
            // 1. Still PENDING
            // 2. Created 72-73 hours ago (1-day window)
            // 3. Haven't received a 72-hour reminder yet
            // 4. Have no messages (haven't been responded to)
            const eligibleQuotes = await prisma.quote.findMany({
                where: {
                    status: QuoteStatus.PENDING,
                    invited_at: {
                        lte: seventyTwoHoursAgo,
                        gte: seventyThreeHoursAgo // Create 1-day window
                    },
                    reminder_72h_sent_at: null,
                    messages: {
                        none: {}
                    }
                },
                include: {
                    listing: true,
                    job: {
                        include: {
                            user: true
                        }
                    }
                }
            });

            console.log(
                `Found ${eligibleQuotes.length} quotes eligible for 72-hour reminders`
            );

            if (eligibleQuotes.length === 0) {
                return { processed: 0, total: 0, timestamp: new Date() };
            }

            // Prepare batch email options
            const emailOptions = eligibleQuotes.map(quote => {
                const job = quote.job;
                const providerName =
                    quote.listing.business_name ||
                    `${quote.listing.first_name} ${quote.listing.last_name}`;

                return {
                    to: quote.listing.email,
                    subject: `Urgent: Pending Lead from ${job.first_name} ${job.last_name}`,
                    react: ProviderReminderEmail({
                        firstName: job.first_name,
                        lastName: job.last_name,
                        email: job.email,
                        phone: job.phone,
                        contactPreference: job.contact_preference as
                            | "sms"
                            | "phone"
                            | "email",
                        message: job.message,
                        category: job.category,
                        providerName,
                        location: job.location as {
                            address: string;
                            latitude: number;
                            longitude: number;
                        },
                        leadId: job.id
                    }),
                    emailType: "72h_quote_reminder"
                };
            });

            // Send all emails in batch
            const batchResult = await this.emailService.batchSend(emailOptions);

            console.log(
                `72-hour quote reminder batch result: ${batchResult.results.length} sent, ${batchResult.errors.length} errors`
            );

            // Send SMS reminders and update quotes for successful emails
            const successfulQuotes = eligibleQuotes.filter((quote, index) => {
                return index < batchResult.results.length && !batchResult.errors.some(error =>
                    error.includes(quote.listing.email)
                );
            });

            for (const quote of successfulQuotes) {
                try {
                    // Send SMS reminder if phone is available
                    if (quote.listing.phone) {
                        await this.smsService.sendToProvider(
                            quote.listing.phone,
                            `URGENT: RV Help lead from ${quote.job.first_name} ${quote.job.last_name} still needs your response. This lead will expire soon. Respond now at ${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${quote.job.id}/respond`
                        );
                    }

                    // Mark that 72-hour reminder was sent
                    await prisma.quote.update({
                        where: { id: quote.id },
                        data: { reminder_72h_sent_at: new Date() }
                    });
                } catch (error) {
                    console.error(
                        `Error sending SMS or updating quote ${quote.id}:`,
                        error
                    );
                }
            }

            // Log any errors
            if (batchResult.errors.length > 0) {
                console.error("72-hour quote reminder batch errors:", batchResult.errors);
            }

            return {
                processed: successfulQuotes.length,
                total: eligibleQuotes.length,
                timestamp: new Date()
            };
        } catch (error) {
            console.error("Error in 72-hour quote reminder processing:", error);
            throw error;
        }
    }

    /**
     * Send 1-week reminders to providers about pending quotes
     * This is the final reminder before the job expires
     */
    async send1WeekQuoteReminders(): Promise<{ processed: number; total: number; timestamp: Date }> {
        // Check if today is a business day (Monday-Saturday)
        if (!isBusinessDay()) {
            console.log("Skipping 1-week quote reminders - not a business day (Sunday)");
            adminLogger.log("Skipping 1-week quote reminders - not a business day (Sunday)");
            return { processed: 0, total: 0, timestamp: new Date() };
        }

        const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        // Create a 1-day window: quotes created 7-8 days ago
        const eightDaysAgo = new Date(Date.now() - 8 * 24 * 60 * 60 * 1000);

        try {
            // Find quotes that are:
            // 1. Still PENDING
            // 2. Created 7-8 days ago (1-day window)
            // 3. Haven't received a 1-week reminder yet
            // 4. Have no messages (haven't been responded to)
            const eligibleQuotes = await prisma.quote.findMany({
                where: {
                    status: QuoteStatus.PENDING,
                    invited_at: {
                        lte: oneWeekAgo,
                        gte: eightDaysAgo // Create 1-day window
                    },
                    reminder_1w_sent_at: null,
                    messages: {
                        none: {}
                    }
                },
                include: {
                    listing: true,
                    job: {
                        include: {
                            user: true
                        }
                    }
                }
            });

            console.log(
                `Found ${eligibleQuotes.length} quotes eligible for 1-week reminders`
            );

            if (eligibleQuotes.length === 0) {
                return { processed: 0, total: 0, timestamp: new Date() };
            }

            // Prepare batch email options
            const emailOptions = eligibleQuotes.map(quote => {
                const job = quote.job;
                const providerName =
                    quote.listing.business_name ||
                    `${quote.listing.first_name} ${quote.listing.last_name}`;

                return {
                    to: quote.listing.email,
                    subject: `FINAL NOTICE: Lead from ${job.first_name} ${job.last_name} expires soon`,
                    react: ProviderReminderEmail({
                        firstName: job.first_name,
                        lastName: job.last_name,
                        email: job.email,
                        phone: job.phone,
                        contactPreference: job.contact_preference as
                            | "sms"
                            | "phone"
                            | "email",
                        message: job.message,
                        category: job.category,
                        providerName,
                        location: job.location as {
                            address: string;
                            latitude: number;
                            longitude: number;
                        },
                        leadId: job.id
                    }),
                    emailType: "1w_quote_reminder"
                };
            });

            // Send all emails in batch
            const batchResult = await this.emailService.batchSend(emailOptions);

            console.log(
                `1-week quote reminder batch result: ${batchResult.results.length} sent, ${batchResult.errors.length} errors`
            );

            // Send SMS reminders and update quotes for successful emails
            const successfulQuotes = eligibleQuotes.filter((quote, index) => {
                return index < batchResult.results.length && !batchResult.errors.some(error =>
                    error.includes(quote.listing.email)
                );
            });

            for (const quote of successfulQuotes) {
                try {
                    // Send SMS reminder if phone is available
                    if (quote.listing.phone) {
                        await this.smsService.sendToProvider(
                            quote.listing.phone,
                            `FINAL NOTICE: RV Help lead from ${quote.job.first_name} ${quote.job.last_name} expires in 3 days. This is your last chance to respond. Act now: ${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${quote.job.id}/respond`
                        );
                    }

                    // Mark that 1-week reminder was sent
                    await prisma.quote.update({
                        where: { id: quote.id },
                        data: { reminder_1w_sent_at: new Date() }
                    });
                } catch (error) {
                    console.error(
                        `Error sending SMS or updating quote ${quote.id}:`,
                        error
                    );
                }
            }

            // Log any errors
            if (batchResult.errors.length > 0) {
                console.error("1-week quote reminder batch errors:", batchResult.errors);
            }

            return {
                processed: successfulQuotes.length,
                total: eligibleQuotes.length,
                timestamp: new Date()
            };
        } catch (error) {
            console.error("Error in 1-week quote reminder processing:", error);
            throw error;
        }
    }

    /**
     * Send a random provider reminder for testing purposes
     * Finds a random job with pending quotes and sends a reminder
     */
    async sendRandomProviderReminder(): Promise<any> {
        // Find a random job with pending quotes that haven't had reminders sent
        const job = await prisma.job.findFirst({
            where: {
                status: {
                    not: JobStatus.CANCELLED
                },
                quotes: {
                    some: {
                        status: QuoteStatus.PENDING,
                        reminder_sent_at: null
                    }
                }
            },
            include: {
                quotes: {
                    where: {
                        status: QuoteStatus.PENDING,
                        reminder_sent_at: null
                    },
                    include: {
                        listing: {
                            include: {
                                locations: true
                            }
                        }
                    }
                }
            },
            orderBy: {
                created_at: "desc"
            }
        });

        if (!job || !job.quotes.length) {
            throw new Error("No eligible jobs found");
        }

        // Send reminder to the first pending quote
        const quote = job.quotes[0];
        const providerName =
            quote.listing.business_name ||
            `${quote.listing.first_name} ${quote.listing.last_name}`;

        // Send email reminder
        await this.emailService.send({
            to: quote.listing.email,
            subject: `Reminder: Pending Lead from ${job.first_name} ${job.last_name}`,
            react: ProviderReminderEmail({
                firstName: job.first_name,
                lastName: job.last_name,
                email: job.email,
                phone: job.phone,
                contactPreference: job.contact_preference as "sms" | "phone" | "email",
                message: job.message,
                category: job.category,
                providerName,
                location: job.location as {
                    address: string;
                    latitude: number;
                    longitude: number;
                },
                leadId: job.id
            })
        });

        // Send SMS reminder if phone is available
        if (quote.listing.phone) {
            await this.smsService.sendToProvider(
                quote.listing.phone,
                `RV Help Reminder: You have a pending lead from ${job.first_name} ${job.last_name} that needs your attention. Respond at ${process.env.NEXT_PUBLIC_APP_URL}/provider/leads/${job.id}/respond`
            );
        }

        // Update the quote to mark that a provider reminder was sent
        await prisma.quote.update({
            where: { id: quote.id },
            data: {
                reminder_sent_at: new Date()
            }
        });

        return job;
    }
}

export const jobLifecycleService = new JobLifecycleService(emailService as any, smsService as any);