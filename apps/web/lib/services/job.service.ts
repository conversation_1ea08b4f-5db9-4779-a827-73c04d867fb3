import prisma from "@/lib/prisma";
import { queueMessage } from "@/lib/queue/qstash";
import { JobStatus, Quote<PERSON>tatus, User, WarrantyRequestStatus } from "@rvhelp/database";
import { setActiveListingLocation } from "../location-utils";
import { getDrivingDistance } from "../utils/distance";
import { BlacklistService } from "./blacklist.service";

export interface CreateJobParams {
    first_name: string;
    last_name: string;
    email: string;
    contact_phone?: string;
    contact_preference?: string;
    category: string;
    source?: string;
    location?: string;
    message?: string;
    rv_type?: string;
    rv_make?: string;
    rv_model?: string;
    rv_year?: number;
    listing_id?: string; // Required for non-warranty jobs, optional for warranty jobs
    warranty_request_id?: string;
    isWarranty?: boolean;
    sms_opt_in?: boolean;
}

export interface JobCreationResult {
    success: boolean;
    job?: any;
    error?: string;
    message?: string;
}

export interface ExistingJob {
    id: string;
    type: 'job' | 'warranty_request';
    uuid?: string;
    status: JobStatus | WarrantyRequestStatus;
    rv_make?: string;
    rv_model?: string;
    rv_year?: string;
    created_at: Date;
    job_id?: string;
    warranty_request_id?: string;
    message?: string;
    category?: string;
    // Warranty-specific fields
    company_name?: string;
    onboarding_completed_at?: Date | null;
}

export class JobService {
    /**
     * Create a new job with all related business logic
     */
    static async createJob(params: CreateJobParams): Promise<JobCreationResult> {
        const { isWarranty = false, ...validatedData } = params;

        try {
            // Validate business rules
            if (!isWarranty && !validatedData.listing_id) {
                return {
                    success: false,
                    error: "Validation error",
                    message: "listing_id is required for non-warranty jobs"
                };
            }

            // Check email blacklist
            const emailCheck = await BlacklistService.checkEmailAccess(validatedData.email);
            if (emailCheck.isBlacklisted) {
                return {
                    success: false,
                    error: "Lead submission blocked",
                    message: emailCheck.message || "Email address is not allowed to submit leads"
                };
            }

            // Create or find user
            const user = await this.upsertUser(validatedData);

            // Create the job
            const job = await this.createJobRecord(validatedData, user, isWarranty);

            // Queue all async operations
            await this.queueJobProcessing(job, validatedData, user, isWarranty);

            // Get listing details for tracking
            const listing = await prisma.listing.findUnique({
                where: { id: validatedData.listing_id },
                select: { business_name: true, first_name: true, last_name: true }
            });

            await queueMessage({
                type: "analytics-tracking",
                payload: {
                    type: "lead-form-submission",
                    data: {
                        listingId: validatedData.listing_id,
                        listingName: listing?.business_name || listing?.first_name,
                        category: validatedData.category,
                        userId: user.id,
                        isAnonymous: false
                    }
                }
            });

            return {
                success: true,
                message: "Lead submission received and will be processed shortly",
                job
            };
        } catch (error) {
            console.error("Error creating job:", error);
            return {
                success: false,
                error: "Internal server error",
                message: "Failed to create job"
            };
        }
    }

    /**
     * Get jobs for a user
     */
    static async getJobsForUser(userId: string) {
        const jobs = await prisma.job.findMany({
            where: {
                user_id: userId
            },
            include: {
                quotes: {
                    include: {
                        listing: {
                            include: {
                                locations: true
                            }
                        },
                        messages: {
                            where: {
                                read_at: null,
                                sender_type: 'PROVIDER',
                            }
                        }
                    }
                },
                user: true
            }
        });

        // Transform quotes to include unread messages count
        return jobs.map(job => ({
            ...job,
            quotes: job.quotes.map(quote => ({
                ...quote,
                unread_messages_count: quote.messages.length || 0
            }))
        }));
    }

    /**
     * Get jobs for a provider
     * 
     * only get warranty requests for now
     */
    static async getJobsForProvider(listingId: string, status?: QuoteStatus) {
        const statuses = status ? [status] : [QuoteStatus.ACCEPTED, QuoteStatus.IN_PROGRESS, QuoteStatus.WITHDRAWN, QuoteStatus.COMPLETED]
        const quotes = await prisma.quote.findMany({
            where: {
                listing_id: listingId,
                status: {
                    in: statuses
                },
                job: {
                    warranty_request: {
                        isNot: null
                    }
                }
            },
            include: {
                job: {
                    select: {
                        id: true,
                        flagged_for_fraud: true,
                        user: {
                            select: {
                                first_name: true,
                                last_name: true,
                                email: true,
                                phone: true
                            }
                        },
                        message: true,
                        category: true,
                        location: true,
                        status: true,
                        rv_type: true,
                        rv_make: true,
                        rv_model: true,
                        rv_year: true,
                        warranty_request: true,
                        // add other fields as needed
                    }
                },
                messages: {
                    orderBy: {
                        created_at: "desc"
                    }
                }
            },
            orderBy: {
                created_at: "desc"
            }
        });

        return quotes;
    }

    /**
     * Create or update user for job submission
     */
    private static async upsertUser(data: CreateJobParams): Promise<User> {
        return await prisma.user.upsert({
            where: { email: data.email },
            update: {
                phone: data.contact_phone || null,
                rv_details: {
                    type: data.rv_type,
                    make: data.rv_make,
                    model: data.rv_model,
                    year: data.rv_year?.toString() || null
                }
            },
            create: {
                email: data.email,
                first_name: data.first_name,
                last_name: data.last_name,
                phone: data.contact_phone || null,
                role: "USER",
                password: null // No password for unauthenticated users
            }
        });
    }

    /**
     * Create the job record with quotes if needed
     */
    private static async createJobRecord(data: CreateJobParams, user: User, isWarranty: boolean) {
        const jobData: any = {
            first_name: data.first_name,
            last_name: data.last_name,
            email: data.email,
            phone: data.contact_phone,
            contact_preference: data.contact_preference,
            sms_opt_in: data.sms_opt_in ?? true, // Default to true if not provided
            category: data.category,
            source: data.source,
            location: data.location,
            message: data.message,
            status: JobStatus.OPEN,
            rv_type: data.rv_type,
            rv_make: data.rv_make,
            rv_model: data.rv_model,
            rv_year: data.rv_year?.toString() || null,
            ...(data.warranty_request_id && {
                warranty_request: {
                    connect: {
                        id: data.warranty_request_id,
                    }
                }
            }),
            user: {
                connect: {
                    id: user.id
                }
            }
        };

        // Add quotes connection only for non-warranty requests with a listing
        if (!isWarranty && data.listing_id) {
            const listing = await prisma.listing.findUnique({
                where: { id: data.listing_id },
                include: {
                    locations: true
                }
            });
            const activeLocation = setActiveListingLocation(listing);

            const distance_miles = await getDrivingDistance({
                lat: jobData.location.latitude,
                lng: jobData.location.longitude
            }, {
                lat: activeLocation.latitude,
                lng: activeLocation.longitude
            });
            jobData.quotes = {
                create: {
                    listing: {
                        connect: {
                            id: data.listing_id,
                        }
                    },
                    status: QuoteStatus.PENDING,
                    distance_miles: distance_miles
                }
            };
        }

        const job = await prisma.job.create({
            data: jobData,
            include: {
                quotes: {
                    include: {
                        listing: {
                            select: {
                                id: true,
                                email: true,
                                notification_email: true,
                                first_name: true,
                                last_name: true,
                                business_name: true,
                                phone: true,
                                sms_verified_at: true
                            }
                        }
                    }
                }
            }
        });

        // Add initial message to quotes if they were created
        if (job.quotes && job.quotes.length > 0 && data.message?.trim()) {
            try {
                await Promise.all(
                    job.quotes.map(quote =>
                        prisma.quoteMessage.create({
                            data: {
                                quote_id: quote.id,
                                sender_id: user.id,
                                sender_type: 'USER',
                                recipient_id: quote.listing.id,
                                recipient_type: 'PROVIDER',
                                content: data.message,
                                type: 'SYSTEM',
                                attachments: [],
                                read_at: new Date(),
                                metadata: { isInitialMessage: true },
                            },
                        })
                    )
                );
            } catch (error) {
                console.error('Failed to add initial messages to quotes:', error);
                // Don't fail job creation if initial message addition fails
            }
        }

        return job;
    }

    /**
     * Queue all async job processing operations
     */
    private static async queueJobProcessing(job: any, validatedData: CreateJobParams, user: User, isWarranty: boolean) {
        // Extract listing from the job's quotes if available
        const listing_id = job.quotes && job.quotes.length > 0 ? job.quotes[0].listing_id : null;

        let listing = null;
        let activeLocation = null;
        if (listing_id) {
            listing = await prisma.listing.findUnique({
                where: { id: listing_id },
                include: {
                    locations: true
                }
            });
            activeLocation = setActiveListingLocation(listing);
        }

        await queueMessage({
            type: "job-processing",
            payload: {
                job,
                listing: {
                    ...listing,
                    location: activeLocation
                },
                validatedData,
                user,
                isWarranty
            }
        });
    }

    /**
     * Get all existing jobs for a user
     * This includes:
     * 1. All jobs that aren't completed/cancelled/expired (regardless of warranty status)
     * 2. Warranty requests that don't have a job_id yet (these will become jobs)
     */
    static async getExistingJobs(userId: string): Promise<ExistingJob[]> {
        const existingJobs: ExistingJob[] = [];

        // 1. Get OPEN jobs only (looking for jobs that can still accept providers)
        const outstandingJobs = await prisma.job.findMany({
            where: {
                user_id: userId,
                status: JobStatus.OPEN
            },
            include: {
                warranty_request: {
                    include: {
                        company: true
                    }
                }
            },
            orderBy: {
                created_at: 'desc'
            }
        });

        // Add all outstanding jobs to the list
        for (const job of outstandingJobs) {
            existingJobs.push({
                id: job.id,
                type: 'job',
                status: job.status,
                rv_make: job.rv_make || undefined,
                rv_model: job.rv_model || undefined,
                rv_year: job.rv_year || undefined,
                created_at: job.created_at,
                job_id: job.id,
                warranty_request_id: job.warranty_request_id || undefined,
                message: job.message,
                category: job.category,
                company_name: job.warranty_request?.company?.name,
                onboarding_completed_at: job.warranty_request?.onboarding_completed_at
            });
        }

        // 2. Get warranty requests without jobs that aren't completed
        const finalWarrantyStates: WarrantyRequestStatus[] = [
            WarrantyRequestStatus.JOB_COMPLETED,
            WarrantyRequestStatus.JOB_CANCELLED,
            WarrantyRequestStatus.INVOICE_PAID,
            WarrantyRequestStatus.REQUEST_REJECTED,
            WarrantyRequestStatus.AUTHORIZATION_REJECTED
        ];

        const warrantyRequestsWithoutJobs = await prisma.warrantyRequest.findMany({
            where: {
                customer_id: userId,
                job_id: null, // No job created yet
                status: {
                    notIn: finalWarrantyStates
                }
            },
            include: {
                company: true
            },
            orderBy: {
                created_at: 'desc'
            }
        });

        // Add warranty requests without jobs to the list
        for (const warrantyRequest of warrantyRequestsWithoutJobs) {
            existingJobs.push({
                id: warrantyRequest.id,
                type: 'warranty_request',
                uuid: warrantyRequest.uuid || undefined,
                status: warrantyRequest.status,
                rv_make: warrantyRequest.rv_make || undefined,
                rv_model: warrantyRequest.rv_model || undefined,
                rv_year: warrantyRequest.rv_year || undefined,
                created_at: warrantyRequest.created_at,
                warranty_request_id: warrantyRequest.id,
                category: 'rv-repair', // Warranty requests are always rv-repair category
                company_name: warrantyRequest.company?.name,
                onboarding_completed_at: warrantyRequest.onboarding_completed_at
            });
        }

        // Sort all by created_at desc
        return existingJobs.sort((a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
    }

    /**
     * Check if a user has any existing jobs (boolean check)
     */
    static async hasExistingJobs(userId: string): Promise<boolean> {
        const existingJobs = await this.getExistingJobs(userId);
        return existingJobs.length > 0;
    }

    /**
 * Get the most recent existing job that can accept provider invitations
 * Prioritizes OPEN jobs over warranty requests since jobs have workrooms
 */
    static async getMostRecentActiveJob(userId: string): Promise<ExistingJob | null> {
        const existingJobs = await this.getExistingJobs(userId);

        if (existingJobs.length === 0) {
            return null;
        }

        // First, try to find an OPEN job (since jobs have workrooms where we can invite providers)
        const openJob = existingJobs.find(job =>
            job.type === 'job' && job.job_id && job.status === JobStatus.OPEN
        );

        if (openJob) {
            return openJob;
        }

        // If no open job, return the most recent warranty request
        return existingJobs[0];
    }

    /**
     * Get existing jobs for a user by their email (for non-logged-in users)
     * This finds the user by email and then gets their existing jobs
     */
    static async getExistingJobsByEmail(email: string): Promise<{ existingJobs: ExistingJob[]; userStatus: any } | null> {
        // Find user by email (case-insensitive)
        const user = await prisma.user.findFirst({
            where: {
                email: {
                    equals: email,
                    mode: "insensitive"
                }
            }
        });


        if (!user) {
            return {
                existingJobs: [],
                userStatus: {
                    userExists: false,
                    hasPassword: false,
                    needsPasswordSetup: true,
                    email: email,
                    firstName: null,
                    lastName: null
                }
            };
        }


        let existingJobs: ExistingJob[] = [];

        if (user?.membership_level !== "FREE") {
            // If user exists, get their existing jobs (but only warranty-related ones)
            existingJobs = await this.getExistingJobs(user.id);
        }

        // Also check for warranty requests by email (even if no user account exists)
        const finalWarrantyStates: WarrantyRequestStatus[] = [
            WarrantyRequestStatus.JOB_COMPLETED,
            WarrantyRequestStatus.JOB_CANCELLED,
            WarrantyRequestStatus.INVOICE_PAID,
            WarrantyRequestStatus.REQUEST_REJECTED,
            WarrantyRequestStatus.AUTHORIZATION_REJECTED
        ];

        const warrantyRequestsByEmail = await prisma.warrantyRequest.findMany({
            where: {
                email: {
                    equals: email,
                    mode: "insensitive"
                },
                status: {
                    notIn: finalWarrantyStates
                }
            },
            include: {
                company: true
            },
            orderBy: {
                created_at: 'desc'
            }
        });

        console.log('🔍 Warranty requests found by email:', warrantyRequestsByEmail.length, warrantyRequestsByEmail.map(w => ({ id: w.id, status: w.status, companyName: w.company?.name })));

        // Add warranty requests found by email to the list (avoid duplicates if user exists)
        for (const warrantyRequest of warrantyRequestsByEmail) {
            // Skip if we already added this warranty request via user lookup
            const alreadyExists = existingJobs.some(job =>
                job.type === 'warranty_request' && job.id === warrantyRequest.id
            );

            if (!alreadyExists) {
                existingJobs.push({
                    id: warrantyRequest.id,
                    type: 'warranty_request',
                    uuid: warrantyRequest.uuid || undefined,
                    status: warrantyRequest.status,
                    rv_make: warrantyRequest.rv_make || undefined,
                    rv_model: warrantyRequest.rv_model || undefined,
                    rv_year: warrantyRequest.rv_year || undefined,
                    created_at: warrantyRequest.created_at,
                    warranty_request_id: warrantyRequest.id,
                    category: 'rv-repair',
                    company_name: warrantyRequest.company?.name,
                    onboarding_completed_at: warrantyRequest.onboarding_completed_at
                });
            }
        }

        // If no user and no warranty requests, return null
        if (!user && existingJobs.length === 0) {
            return null;
        }

        // Sort all by created_at desc
        existingJobs.sort((a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

        // Determine user status for password/login flow
        const userStatus = user ? {
            userExists: true,
            hasPassword: !!user.password,
            needsPasswordSetup: !user.last_login,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name
        } : {
            userExists: false,
            hasPassword: false,
            needsPasswordSetup: true,
            email: email,
            firstName: null,
            lastName: null
        };

        return {
            existingJobs,
            userStatus
        };
    }

    /**
     * Get the most recent active job for a user by email (for non-logged-in users)
     */
    static async getMostRecentActiveJobByEmail(email: string): Promise<ExistingJob | null> {
        const result = await this.getExistingJobsByEmail(email);
        if (!result || result.existingJobs.length === 0) {
            return null;
        }

        // Find the most recent OPEN job or warranty request
        const openJob = result.existingJobs.find(job =>
            job.type === 'job' && job.job_id && job.status === JobStatus.OPEN
        );

        if (openJob) {
            return openJob;
        }

        // If no open job, return the most recent warranty request
        return result.existingJobs[0];
    }
} 