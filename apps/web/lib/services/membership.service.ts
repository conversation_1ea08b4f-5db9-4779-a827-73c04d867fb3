import prisma from "@/lib/prisma";
import { emailService, slackService } from "@/lib/services";
import { RVHelpMembershipLevel } from "@rvhelp/database";
import { NextRequest } from "next/server";
import { FirstPromoterService } from "./first-promoter.service";

export class MembershipService {
	/**
	 * Create or update a user's membership
	 */
	async createOrUpdateMembership({
		userId,
		level,
		stripeSessionId,
		stripeSubscriptionId,
		amountPaid,
		currency = "usd",
		setupPasswordUrl,
		sendWelcomeEmail = true,
		sendSlackNotification = true,
		req
	}: {
		userId: string;
		level: RVHelpMembershipLevel;
		stripeSessionId?: string;
		stripeSubscriptionId?: string;
		amountPaid?: number;
		currency?: string;
		setupPasswordUrl?: string;
		sendWelcomeEmail?: boolean;
		sendSlackNotification?: boolean;
		req?: NextRequest;
	}) {
		return await prisma.$transaction(async (tx) => {
			// Check if user already has a membership
			const existingMembership = await tx.membership.findUnique({
				where: { user_id: userId }
			});

			let membership;
			let memberNumber;
			const isNewMembership = !existingMembership;

			if (existingMembership) {
				// Update existing membership
				membership = await tx.membership.update({
					where: { user_id: userId },
					data: {
						level,
						stripe_session_id: stripeSessionId,
						stripe_subscription_id: stripeSubscriptionId,
						amount_paid: amountPaid,
						currency,
						is_active: true,
						cancelled_at: null
					}
				});
				memberNumber = existingMembership.member_number;
			} else {
				// Create new membership and assign member number
				memberNumber = await this.getNextMemberNumber(tx);
				membership = await tx.membership.create({
					data: {
						user_id: userId,
						level,
						member_number: memberNumber,
						stripe_session_id: stripeSessionId,
						stripe_subscription_id: stripeSubscriptionId,
						amount_paid: amountPaid,
						currency
					}
				});
			}

			// Update user's membership level and member number
			const updatedUser = await tx.user.update({
				where: { id: userId },
				data: {
					membership_level: level,
					member_number: memberNumber
				}
			});

			// Send welcome email for new memberships
			if (sendWelcomeEmail && isNewMembership) {
				try {
					await emailService.sendMembershipWelcomeEmail({
						email: updatedUser.email,
						name: updatedUser.first_name + " " + updatedUser.last_name,
						setupPasswordUrl: setupPasswordUrl || "",
						membershipLevel: level
					});
				} catch (error) {
					console.error("Error sending membership welcome email:", error);
					// Don't fail the transaction if email fails
				}
			}

			// Send Slack notification for new memberships
			if (sendSlackNotification && isNewMembership) {
				try {
					await slackService.notifyNewMemberSignup(
						updatedUser,
						level,
						updatedUser.member_number!
					);
				} catch (error) {
					console.error("Error sending Slack notification:", error);
					// Don't fail the transaction if Slack notification fails
				}
			}

			// Track pro membership with FirstPromoter for new memberships
			if (isNewMembership) {
				FirstPromoterService.trackProMembership({
					email: updatedUser.email,
					userId: updatedUser.id,
					membershipLevel: level,
					amount: amountPaid || 0,
					currency: currency,
					req: req
				}).catch((error) => {
					console.error("Failed to track FirstPromoter pro membership:", error);
					// Don't fail the transaction if FirstPromoter tracking fails
				});
			}

			return { user: updatedUser, membership, isNewMembership };
		});
	}

	/**
	 * Get the next member number for paid members
	 */
	private async getNextMemberNumber(tx: any) {
		const count = await tx.membership.count({
			where: { is_active: true }
		});
		return count + 1;
	}

	/**
	 * Get membership statistics for a date range
	 */
	async getMembershipStats(startDate: Date, endDate: Date) {
		const [totalActiveMembers, newMemberships, totalCancellations] =
			await Promise.all([
				// Total active paid members
				prisma.membership.count({
					where: {
						is_active: true,
						level: {
							in: ["STANDARD", "PREMIUM"]
						}
					}
				}),

				// New memberships in date range (first time getting paid membership)
				prisma.membership.count({
					where: {
						created_at: {
							gte: startDate,
							lte: endDate
						},
						level: {
							in: ["STANDARD", "PREMIUM"]
						}
					}
				}),

				// Cancellations in date range
				prisma.membership.count({
					where: {
						cancelled_at: {
							gte: startDate,
							lte: endDate
						}
					}
				})
			]);

		return {
			totalActiveMembers,
			newMemberships,
			totalCancellations
		};
	}

	/**
	 * Cancel a user's membership
	 */
	async cancelMembership(userId: string) {
		return await prisma.$transaction(async (tx) => {
			const membership = await tx.membership.findUnique({
				where: { user_id: userId }
			});

			if (!membership) {
				throw new Error("User does not have a membership to cancel");
			}

			if (!membership.is_active) {
				throw new Error("Membership is already cancelled");
			}

			// Update membership to cancelled
			const cancelledMembership = await tx.membership.update({
				where: { user_id: userId },
				data: {
					is_active: false,
					cancelled_at: new Date()
				}
			});

			// Update user's membership level back to FREE
			const updatedUser = await tx.user.update({
				where: { id: userId },
				data: { membership_level: "FREE" }
			});

			return { user: updatedUser, membership: cancelledMembership };
		});
	}

	/**
	 * Get a user's membership details
	 */
	async getUserMembership(userId: string) {
		return await prisma.membership.findUnique({
			where: { user_id: userId },
			include: { user: true }
		});
	}
}

export const membershipService = new MembershipService();
