import prisma from "@/lib/prisma";
import { BlacklistType } from "@rvhelp/database";

export class BlacklistService {
	static async addEntry(data: {
		type: BlacklistType;
		value: string;
		reason: string;
		message?: string;
		createdBy?: string;
		expiresAt?: string | Date | null;
	}) {
		const value = data.value.trim().toLowerCase();

		return await prisma.blacklistEntry.create({
			data: {
				type: data.type,
				value,
				reason: data.reason,
				message: data.message || null,
				created_by: data.createdBy,
				expires_at:
					data.expiresAt && data.expiresAt !== ""
						? new Date(data.expiresAt)
						: null
			}
		});
	}

	static async removeEntry(id: string) {
		// Get the entry before deleting to know what was removed
		const entry = await prisma.blacklistEntry.findUnique({
			where: { id }
		});

		const result = await prisma.blacklistEntry.delete({
			where: { id }
		});

		// If we removed a USER_ID or EMAIL entry, call clearBanMarkers
		if (entry && (entry.type === "USER_ID" || entry.type === "EMAIL")) {
			await this.clearBanMarkers(entry.value);
		}

		return result;
	}

	static async clearBanMarkers(value: string) {
		// Log that ban markers should be cleared for this user
		console.log(`🔄 Ban markers should be cleared for: ${value}`);

		// In a production environment, you might want to:
		// 1. Track unbanned users in a database table
		// 2. Send push notifications to clear client-side markers
		// 3. Use a background job to clear markers across sessions

		// For now, we log the action and rely on the admin to manually
		// clear markers using the /api/admin/blacklist/clear-markers endpoint
		// when they unban a user
	}

	static async isBlacklisted(
		type: BlacklistType,
		value: string
	): Promise<{ isBlacklisted: boolean; message?: string }> {
		if (!value) return { isBlacklisted: false };

		const normalizedValue = value.trim().toLowerCase();

		// For emails, also check domain
		if (type === "EMAIL") {
			const domain = normalizedValue.split("@")[1];
			const domainCheck = await this.isBlacklisted("DOMAIN", domain);
			if (domainCheck.isBlacklisted) {
				return domainCheck;
			}
		}

		const entry = await prisma.blacklistEntry.findFirst({
			where: {
				type,
				value: normalizedValue,
				is_active: true,
				OR: [{ expires_at: null }, { expires_at: { gt: new Date() } }]
			}
		});

		return {
			isBlacklisted: !!entry,
			message: entry?.message || undefined
		};
	}

	static async listEntries(type?: BlacklistType) {
		return await prisma.blacklistEntry.findMany({
			where: type ? { type } : undefined,
			orderBy: { created_at: "desc" }
		});
	}

	static async checkUserAccess(
		userId: string
	): Promise<{ isBlacklisted: boolean; message?: string }> {
		return await this.isBlacklisted("USER_ID", userId);
	}

	static async checkEmailAccess(
		email: string
	): Promise<{ isBlacklisted: boolean; message?: string }> {
		return await this.isBlacklisted("EMAIL", email);
	}

	static async checkDomainAccess(
		domain: string
	): Promise<{ isBlacklisted: boolean; message?: string }> {
		return await this.isBlacklisted("DOMAIN", domain);
	}

	static async checkIpAccess(
		ipAddress: string
	): Promise<{ isBlacklisted: boolean; message?: string }> {
		return await this.isBlacklisted("IP_ADDRESS", ipAddress);
	}
}
