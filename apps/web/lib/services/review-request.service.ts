import { ReviewRequestEmail } from "@/components/email-templates/ReviewRequestEmail";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { adminLogger } from "./admin-log.service";

export class ReviewRequestService {
    static async sendPendingReviewRequests() {
        const now = new Date();
        const result = { sent: 0, errors: 0 };

        try {
            // Process both troubleshooting requests and quote review requests
            const troubleshootingResult = await this.sendPendingTroubleshootingReviews(now);
            const quoteResult = await this.sendPendingQuoteReviews(now);

            result.sent = troubleshootingResult.sent + quoteResult.sent;
            result.errors = troubleshootingResult.errors + quoteResult.errors;

            adminLogger.log(`Review request cron completed: ${result.sent} sent, ${result.errors} errors`);
            return result;
        } catch (error) {
            console.error("Error in sendPendingReviewRequests:", error);
            adminLogger.log("Error in sendPendingReviewRequests:", error);
            throw error;
        }
    }

    private static async sendPendingTroubleshootingReviews(now: Date) {
        const result = { sent: 0, errors: 0 };

        // Find troubleshooting requests that:
        // 1. Have review_requested = true
        // 2. Haven't been sent yet (review_sent_at is null)
        // 3. The delay time has passed
        const pendingRequests = await prisma.troubleshootingRequest.findMany({
            where: {
                review_requested: true,
                review_sent_at: null,
                status: 'completed',
                review_requested_at: {
                    not: null
                }
            },
            include: {
                listing: {
                    select: {
                        id: true,
                        business_name: true,
                        first_name: true,
                        last_name: true,
                        slug: true
                    }
                },
                user: {
                    select: {
                        first_name: true,
                        last_name: true,
                        email: true
                    }
                }
            }
        });

        adminLogger.log(`Found ${pendingRequests.length} pending troubleshooting review requests`);

        for (const request of pendingRequests) {
            try {
                // Check if the delay time has passed
                const requestedAt = new Date(request.review_requested_at!);
                const delayHours = request.review_delay_hours || 24;
                const sendTime = new Date(requestedAt.getTime() + (delayHours * 60 * 60 * 1000));

                if (now < sendTime) {
                    // Not time to send yet
                    continue;
                }

                const providerName = request.listing.business_name ||
                    `${request.listing.first_name} ${request.listing.last_name}`;

                // Send the review request email
                await emailService.send({
                    to: request.user.email,
                    subject: `How was your troubleshooting experience with ${providerName}?`,
                    react: ReviewRequestEmail({
                        customerFirstName: request.user.first_name,
                        providerName,
                        providerSlug: request.listing.slug
                    })
                });

                // Mark as sent
                await prisma.troubleshootingRequest.update({
                    where: { id: request.id },
                    data: { review_sent_at: new Date() }
                });

                result.sent++;
                adminLogger.log(`Sent review request for troubleshooting request ${request.id}`);
            } catch (error) {
                console.error(`Failed to send review request for request ${request.id}:`, error);
                adminLogger.log(`Failed to send review request for request ${request.id}:`, error);
                result.errors++;
            }
        }

        return result;
    }

    private static async sendPendingQuoteReviews(now: Date) {
        const result = { sent: 0, errors: 0 };

        // Find quotes that:
        // 1. Have review_requested = true
        // 2. Haven't been sent yet (review_sent_at is null)
        // 3. The delay time has passed
        // 4. Job is completed
        const pendingQuotes = await prisma.quote.findMany({
            where: {
                review_requested: true,
                review_sent_at: null,
                job: {
                    status: 'COMPLETED'
                },
                review_requested_at: {
                    not: null
                }
            },
            include: {
                job: true,
                listing: {
                    select: {
                        id: true,
                        business_name: true,
                        first_name: true,
                        last_name: true,
                        slug: true
                    }
                }
            }
        });

        adminLogger.log(`Found ${pendingQuotes.length} pending quote review requests`);

        for (const quote of pendingQuotes) {
            try {
                // Check if the delay time has passed
                const requestedAt = new Date(quote.review_requested_at!);
                const delayHours = quote.review_delay_hours || 24;
                const sendTime = new Date(requestedAt.getTime() + (delayHours * 60 * 60 * 1000));

                if (now < sendTime) {
                    // Not time to send yet
                    continue;
                }

                const providerName = quote.listing.business_name ||
                    `${quote.listing.first_name} ${quote.listing.last_name}`;

                // Send the review request email
                await emailService.send({
                    to: quote.job.email,
                    subject: `How was your service experience with ${providerName}?`,
                    react: ReviewRequestEmail({
                        customerFirstName: quote.job.first_name,
                        providerName,
                        providerSlug: quote.listing.slug
                    })
                });

                // Mark as sent
                await prisma.quote.update({
                    where: { id: quote.id },
                    data: { review_sent_at: new Date() }
                });

                result.sent++;
                adminLogger.log(`Sent review request for quote ${quote.id}`);
            } catch (error) {
                console.error(`Failed to send review request for quote ${quote.id}:`, error);
                adminLogger.log(`Failed to send review request for quote ${quote.id}:`, error);
                result.errors++;
            }
        }

        return result;
    }
} 