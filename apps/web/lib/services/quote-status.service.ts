import { JobCompletedEmail } from "@/components/email-templates/jobs/provider/JobCompletedEmail";
import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import {
	JobStatus,
	Quote,
	QuoteStatus,
	RejectionReasonType,
	ResolutionStatus,
	WarrantyRequestStatus
} from "@rvhelp/database";
import React from "react";
import { setActiveListingLocation } from "../location-utils";
import { EmailNewsletterService } from "./emailNewsletter.service";
import { ProviderStatsService } from "./provider-stats.service";

export interface QuoteStatusChangeParams {
	quoteId: string;
	listingId: string;
	newStatus: QuoteStatus;
	message?: string;
	schedulingTimeframe?: string;
	rejectionReason?: string;
	rejectionReasonDetails?: string;
	userId?: string; // For tracking who made the change
}

export class QuoteStatusService {
	/**
	 * Change quote status and trigger appropriate side effects
	 */
	static async changeQuoteStatus(params: QuoteStatusChangeParams) {
		const {
			quoteId,
			listingId,
			newStatus,
			message,
			rejectionReason,
			rejectionReasonDetails
		} = params;

		const now = new Date();
		const updateData: Partial<Quote> = {
			status: newStatus
		};

		// Set appropriate timestamps based on status change
		switch (newStatus) {
			case QuoteStatus.ACCEPTED:
			case QuoteStatus.WITHDRAWN:
				updateData.responded_at = now;
				if (message) updateData.provider_notes = message;
				if (rejectionReason) updateData.rejection_reason = rejectionReason as RejectionReasonType;
				if (rejectionReasonDetails)
					updateData.rejection_reason_details = rejectionReasonDetails;
				break;

			case QuoteStatus.ACCEPTED:
				updateData.accepted_at = now;
				break;

			case QuoteStatus.REJECTED:
				// For rejections, don't change responded_at if it's already set
				if (message) updateData.provider_notes = message;
				if (rejectionReason) updateData.rejection_reason = rejectionReason as RejectionReasonType;
				if (rejectionReasonDetails)
					updateData.rejection_reason_details = rejectionReasonDetails;
				break;
		}

		// Update the quote

		const updatedQuote = await prisma.quote.update({
			where: { id: quoteId },
			data: updateData,
			include: {
				job: {
					include: {
						user: true
					}
				},
				listing: {
					include: {
						locations: true
					}
				},
				messages: {
					orderBy: {
						created_at: "desc"
					}
				}
			}
		});

		// Job updates are now only handled by customerAcceptQuote method
		// This ensures job status only changes when customers explicitly accept quotes

		// Trigger appropriate stats updates
		await this.triggerStatsUpdate(newStatus, listingId);

		return updatedQuote;
	}

	/**
	 * Set reviewed_at timestamp when customer submits a review
	 */
	static async markQuoteAsReviewed(quoteId: string, listingId: string) {
		await prisma.quote.update({
			where: { id: quoteId },
			data: {
				reviewed_at: new Date()
			}
		});

		// Update stats for review completion
		await ProviderStatsService.onReviewSubmitted(listingId);
	}

	/**
	 * Provider responds to a quote (accept, decline)
	 */
	static async providerRespond(params: {
		quoteId: string;
		listingId: string;
		responseType: "accepted" | "declined";
		message?: string;
		schedulingTimeframe?: string;
		rejectionReason?: string;
		rejectionReasonDetails?: string;
	}) {
		const statusMap = {
			accepted: QuoteStatus.ACCEPTED,
			declined: QuoteStatus.REJECTED
		};

		return await this.changeQuoteStatus({
			quoteId: params.quoteId,
			listingId: params.listingId,
			newStatus: statusMap[params.responseType],
			message: params.message,
			schedulingTimeframe: params.schedulingTimeframe,
			rejectionReason: params.rejectionReason,
			rejectionReasonDetails: params.rejectionReasonDetails
		});
	}

	/**
	 * Provider completes a job
	 */
	static async providerCompleteJob(params: {
		quoteId: string;
		listingId: string;
		userId: string;
		resolutionStatus?: ResolutionStatus;
		resolutionNotes?: string;
		sendInvoice?: boolean;
		requestReview?: boolean;
		reviewDelayHours?: number;
	}) {
		const {
			quoteId,
			listingId,
			userId,
			resolutionStatus,
			resolutionNotes,
			sendInvoice,
			requestReview,
			reviewDelayHours
		} = params;

		// Check permission - quote must be accepted
		const canComplete = await this.canProviderPerformAction(
			quoteId,
			listingId,
			"complete"
		);
		if (!canComplete.allowed) {
			throw new Error(canComplete.reason || "Cannot complete this quote");
		}


		// Update quote with completion details
		const updatedQuote = await prisma.quote.update({
			where: { id: quoteId },
			data: {
				status: QuoteStatus.COMPLETED,
				completed_at: new Date(),
				resolution_status: resolutionStatus,
				resolution_notes: resolutionNotes,
				review_requested: requestReview,
				review_delay_hours: requestReview ? reviewDelayHours : null,
				review_requested_at: requestReview ? new Date() : null,
			},
			include: {
				job: {
					include: {
						user: true,
						warranty_request: true
					}
				},
				listing: {
					include: {
						owner: true
					}
				}
			}
		});


		// Update job with resolution data and status

		console.log("updating job to completed");
		await prisma.job.update({
			where: { id: updatedQuote.job_id },
			data: {
				status: JobStatus.COMPLETED
			}
		});

		// Create timeline event with resolution notes
		const { timelineService } = await import("./timeline.service");
		await timelineService.createTimelineUpdate({
			...(updatedQuote.job.warranty_request_id
				? { warranty_request_id: updatedQuote.job.warranty_request_id }
				: {}),
			job_id: updatedQuote.job_id,
			event_type: "JOB_COMPLETED",
			updated_by_id: userId,
			notes: resolutionNotes || "Job marked as completed"
		});

		// Send completion email
		try {
			const { JobCompletedEmail } = await import(
				"@/components/email-templates/jobs/provider/JobCompletedEmail"
			);

			await emailService.send({
				to: updatedQuote.listing.email,
				subject: `Service Complete - ${updatedQuote.listing.business_name || `${updatedQuote.listing.first_name} ${updatedQuote.listing.last_name}`}`,
				react: JobCompletedEmail({
					customerName: updatedQuote.job.user.first_name || "Customer",
					resolutionNotes: resolutionNotes
				})
			});
		} catch (error) {
			console.error("Failed to send completion email:", error);
		}

		// Add newsletter tag to customer who received service
		try {
			const tags = ["job complete"];

			await EmailNewsletterService.syncNewsletterSubscriber({
				email: updatedQuote.job.user.email,
				first_name: updatedQuote.job.user.first_name || "",
				last_name: updatedQuote.job.user.last_name || "",
				user: updatedQuote.job.user,
				tags
			});

			console.log(`Added "job complete" newsletter tag to user ${updatedQuote.job.user.email}`);
		} catch (error) {
			console.error("Failed to add newsletter tag for job completion:", error);
			// Don't fail the job completion if newsletter tagging fails
		}

		// Trigger stats update for job completion
		await ProviderStatsService.onJobCompleted(listingId);

		return updatedQuote;
	}

	/**
	 * Provider withdraws from a job (after it was accepted)
	 */
	static async providerWithdraw(params: {
		quoteId: string;
		listingId: string;
		message?: string;
		rejectionReason?: string;
		rejectionReasonDetails?: string;
	}) {
		return await this.changeQuoteStatus({
			quoteId: params.quoteId,
			listingId: params.listingId,
			newStatus: QuoteStatus.WITHDRAWN,
			message: params.message,
			rejectionReason: params.rejectionReason,
			rejectionReasonDetails: params.rejectionReasonDetails
		});
	}

	/**
	 * Trigger appropriate stats updates based on status change
	 */
	private static async triggerStatsUpdate(
		newStatus: QuoteStatus,
		listingId: string
	) {
		switch (newStatus) {
			case QuoteStatus.ACCEPTED:
			case QuoteStatus.WITHDRAWN:
				// Provider responded (either with quote or decline)
				await ProviderStatsService.onProviderResponse(listingId);
				break;

			case QuoteStatus.ACCEPTED:
				// Job was accepted by customer
				await ProviderStatsService.onJobAccepted(listingId);
				break;

			case QuoteStatus.REJECTED:
				// Provider rejected/declined - might affect completion rates
				await ProviderStatsService.updateProviderStats(listingId);
				break;

			case QuoteStatus.CUSTOMER_REJECTED:
				// Customer rejected provider's proposal - might affect completion rates
				await ProviderStatsService.updateProviderStats(listingId);
				break;

			case QuoteStatus.PENDING:
				// No stats update needed for pending (initial invites or info requests)
				break;

			case QuoteStatus.EXPIRED:
				// Expired quotes might affect response rates
				await ProviderStatsService.updateProviderStats(listingId);
				break;
		}
	}

	/**
	 * Get quote with full details
	 */
	static async getQuoteWithDetails(quoteId: string) {
		return await prisma.quote.findUnique({
			where: { id: quoteId },
			include: {
				job: {
					include: {
						user: true,
						warranty_request: {
							include: {
								company: true
							}
						}
					}
				},
				listing: {
					include: {
						locations: true
					}
				},
				messages: {
					orderBy: {
						created_at: "desc"
					}
				}
			}
		});
	}

	/**
	 * Check if provider can perform a specific action on a quote
	 */
	static async canProviderPerformAction(
		quoteId: string,
		listingId: string,
		action: "respond" | "withdraw" | "complete"
	): Promise<{ allowed: boolean; reason?: string }> {
		const quote = await prisma.quote.findUnique({
			where: { id: quoteId },
			select: {
				status: true,
				listing_id: true,
				job: {
					select: {
						warranty_request_id: true,
						warranty_request: {
							select: {
								company: {
									select: {
										id: true,
										name: true
									}
								}
							}
						}
					}
				}
			}
		});

		if (!quote) {
			return { allowed: false, reason: "Quote not found" };
		}

		if (quote.listing_id !== listingId) {
			return { allowed: false, reason: "Unauthorized" };
		}

		// Check warranty access for warranty jobs - only for accepting, not declining
		if (quote.job?.warranty_request_id && action === "respond") {
			// For warranty jobs, we need to check if this is an accept or decline action
			// Since this method is used for both, we'll allow the action to proceed
			// and let the calling method handle the specific validation
			// The UI will disable the accept button, but decline should always be allowed
		}

		switch (action) {
			case "respond":
				if (quote.status !== QuoteStatus.PENDING) {
					return {
						allowed: false,
						reason: "Can only respond to pending quotes"
					};
				}
				break;

			case "withdraw":
				const withdrawableStatuses: QuoteStatus[] = [
					QuoteStatus.ACCEPTED,
					QuoteStatus.PENDING
				];
				if (!withdrawableStatuses.includes(quote.status)) {
					return {
						allowed: false,
						reason: "Can only withdraw from accepted or quoted jobs"
					};
				}
				break;

			case "complete":
				if (quote.status !== QuoteStatus.IN_PROGRESS && quote.status !== QuoteStatus.ACCEPTED) {
					return { allowed: false, reason: "Can only complete accepted jobs" };
				}
				break;
		}

		return { allowed: true };
	}

	/**
	 * Provider accepts/responds to a job - handles all conditional logic
	 */
	static async providerAcceptJob(params: {
		quote: Quote;
		userId: string;
		message: string;
		schedulingTimeframe?: string;
	}) {
		const {
			quote,
			userId,
			message,
			schedulingTimeframe,
		} = params;

		try {
			// Check if provider can respond to this quote
			const canRespond = await this.canProviderPerformAction(
				quote.id,
				quote.listing_id,
				"respond"
			);

			if (!canRespond.allowed) {
				console.error("Provider cannot respond:", canRespond.reason);
				throw new Error(canRespond.reason || "Cannot respond to this quote");
			}


			// Get listing details for timeline and email
			const listing = await prisma.listing.findUnique({
				where: { id: quote.listing_id },
				include: {
					locations: true
				}
			});

			if (!listing) {
				throw new Error("Listing not found");
			}

			const location = setActiveListingLocation(listing);

			if (!location) {
				throw new Error("Listing location not found");
			}

			// Use centralized service to update quote status
			await this.providerRespond({
				quoteId: quote.id,
				listingId: quote.listing_id,
				responseType: "accepted",
				message,
				schedulingTimeframe
			});

			await this.changeQuoteStatus({
				quoteId: quote.id,
				listingId: quote.listing_id,
				newStatus: QuoteStatus.ACCEPTED,
				userId,
				message,
			});


			// Get the final quote with full details
			const finalQuote = await this.getQuoteWithDetails(quote.id);

			if (!finalQuote) {
				console.error("Final quote is null after update");
				throw new Error("Quote not found after update");
			}

			// Create timeline event
			const { timelineService } = await import("./timeline.service");
			await timelineService.createTimelineUpdate({
				...(finalQuote.job.warranty_request_id
					? { warranty_request_id: finalQuote.job.warranty_request_id }
					: {}),
				...(finalQuote.job.id ? { job_id: finalQuote.job.id } : {}),
				event_type: "TECHNICIAN_ACCEPTED",
				updated_by_id: userId,
				notes: `Job accepted by: ${listing.business_name} - ${listing.first_name} ${listing.last_name}`,
				listing_id: quote.listing_id
			});

			// Update warranty request if applicable
			if (finalQuote.job.warranty_request_id) {
				await prisma.warrantyRequest.update({
					where: { id: finalQuote.job.warranty_request_id },
					data: {
						status: WarrantyRequestStatus.JOB_ACCEPTED
					}
				});
			}

			// Send email notification to customer
			if (finalQuote.job.user.email) {
				const providerLocation = location
					? `${location.city}, ${location.state}`
					: undefined;

				const providerName =
					listing.business_name || `${listing.first_name} ${listing.last_name}`;
				const emailSubject = `New Response from ${providerName}`;

				const { QuoteReceivedEmail } = await import(
					"@/components/email-templates/jobs/customer/QuoteReceived"
				);

				await emailService.send({
					to: finalQuote.job.user.email,
					replyTo: finalQuote.listing.email,
					subject: emailSubject,
					react: QuoteReceivedEmail({
						customerName: finalQuote.job.user.first_name || "Customer",
						providerName: providerName,
						providerLocation,
						jobId: finalQuote.job_id,
						schedulingTimeframe,
						message
					}),
					emailType: "provider_quote_submitted"
				});
			}

			// Send SMS notification to customer
			try {
				// Only send SMS if user has opted in and has a phone number
				if (finalQuote.job.sms_opt_in && finalQuote.job.user.phone) {
					const { smsService } = await import("@/lib/services/sms.service");

					const messageText = `A provider has responded to your service request on RV Help.\n\nLog in to view and respond: ${process.env.NEXT_PUBLIC_APP_URL}/service-requests/${finalQuote.job_id}`;

					await smsService.sendToUser(finalQuote.job.user.phone, messageText);
					console.log(`SMS notification sent to user ${finalQuote.job.user.email} for quote from provider ${finalQuote.listing_id}`);
				}
			} catch (error) {
				console.error("Failed to send quote SMS notification:", error);
				// Don't fail the request if SMS fails
			}

			// Create a system message in the quote messages to show the provider's response
			try {
				const { MessageService } = await import("./messaging.service");
				await MessageService.createProviderResponseMessage({
					quoteId: quote.id,
					listingId: quote.listing_id,
					responseType: "accept",
					message,
				});
			} catch (error) {
				console.error("Failed to create system message for accept response:", error);
				// Don't fail the request if message creation fails
			}

			return finalQuote;
		} catch (error) {
			console.error("Error in service method:", error);
			console.error(
				"Error message:",
				error instanceof Error ? error.message : "Unknown error"
			);
			// Re-throw the error to be handled by the API route
			throw error;
		}
	}

	/**
	 * Provider declines a job
	 */
	static async providerDeclineJob(params: {
		quoteId: string;
		listingId: string;
		userId: string;
		rejectionReason?: string;
		rejectionReasonDetails?: string;
	}) {
		const {
			quoteId,
			listingId,
			userId,
			rejectionReason,
			rejectionReasonDetails
		} = params;

		// Check permission
		const canRespond = await this.canProviderPerformAction(
			quoteId,
			listingId,
			"respond"
		);
		if (!canRespond.allowed) {
			throw new Error(canRespond.reason || "Cannot respond to this quote");
		}

		// Get quote details for email
		const quote = await this.getQuoteWithDetails(quoteId);
		if (!quote) {
			throw new Error("Quote not found");
		}

		// Update quote status
		const updatedQuote = await this.providerRespond({
			quoteId,
			listingId,
			responseType: "declined",
			rejectionReason,
			rejectionReasonDetails
		});

		// Send email notification to customer
		try {
			const providerName =
				quote.listing.business_name ||
				`${quote.listing.first_name} ${quote.listing.last_name}`;

			const { LeadRejectionEmail } = await import(
				"@/components/email-templates/jobs/customer/LeadRejectionEmail"
			);

			const location = setActiveListingLocation(quote.listing);

			await emailService.send({
				to: quote.job.user.email,
				replyTo: quote.listing.email,
				subject: `Service Provider Update - ${providerName}`,
				react: LeadRejectionEmail({
					firstName: quote.job.user.first_name || "Customer",
					lastName: quote.job.user.last_name || "",
					providerName,
					providerLocation: location
						? {
							city: location.city,
							state: location.state
						}
						: undefined,
					category: quote.job.category,
					rejectionReason: {
						type: (rejectionReason as any) || "other",
						details: rejectionReasonDetails
					}
				})
			});

			// Send SMS notification to customer
			try {
				// Only send SMS if user has opted in and has a phone number
				if (quote.job.sms_opt_in && quote.job.user.phone) {
					const { smsService } = await import("@/lib/services/sms.service");

					const messageText = `Update from ${providerName} on RV Help.\n\nLog in to view and respond: ${process.env.NEXT_PUBLIC_APP_URL}/service-requests/${quote.job_id}`;

					await smsService.sendToUser(quote.job.user.phone, messageText);
					console.log(`SMS notification sent to user ${quote.job.user.email} for quote rejection from provider ${quote.listing_id}`);
				}
			} catch (error) {
				console.error("Failed to send rejection SMS notification:", error);
				// Don't fail the request if SMS fails
			}
		} catch (error) {
			console.error("Failed to send decline email:", error);
			// Don't fail the request if email fails
		}

		// Create timeline event
		const { timelineService } = await import("./timeline.service");
		await timelineService.createTimelineUpdate({
			...(updatedQuote.job.warranty_request_id
				? { warranty_request_id: updatedQuote.job.warranty_request_id }
				: {}),
			...(updatedQuote.job.id ? { job_id: updatedQuote.job.id } : {}),
			event_type: "TECHNICIAN_REJECTED",
			updated_by_id: userId,
			notes: `Job declined by: ${updatedQuote.listing.business_name} - ${updatedQuote.listing.first_name} ${updatedQuote.listing.last_name}`,
			listing_id: updatedQuote.listing_id
		});

		// Create a system message in the quote messages to show the provider's response
		try {
			const { MessageService } = await import("./messaging.service");
			await MessageService.createProviderResponseMessage({
				quoteId,
				listingId,
				responseType: "reject",
				message: rejectionReasonDetails || undefined,
				rejectionReason
			});
		} catch (error) {
			console.error("Failed to create system message for reject response:", error);
			// Don't fail the request if message creation fails
		}

		return updatedQuote;
	}

	/**
	 * Provider withdraws from a job (after it was accepted)
	 */
	static async providerWithdrawFromJob(params: {
		quoteId: string;
		listingId: string;
		userId: string;
		message?: string;
		rejectionReason?: string;
		rejectionReasonDetails?: string;
	}) {
		const {
			quoteId,
			listingId,
			userId,
			message,
			rejectionReason,
			rejectionReasonDetails
		} = params;

		// Check permission
		const canWithdraw = await this.canProviderPerformAction(
			quoteId,
			listingId,
			"withdraw"
		);
		if (!canWithdraw.allowed) {
			throw new Error(canWithdraw.reason || "Cannot withdraw from this quote");
		}

		// Get quote and listing details
		const quote = await this.getQuoteWithDetails(quoteId);
		if (!quote) {
			throw new Error("Quote not found");
		}

		const listing = await prisma.listing.findUnique({
			where: { id: listingId }
		});
		if (!listing) {
			throw new Error("Listing not found");
		}

		// Update quote status
		const updatedQuote = await this.providerWithdraw({
			quoteId,
			listingId,
			message,
			rejectionReason,
			rejectionReasonDetails
		});

		// Create timeline event
		const { timelineService } = await import("./timeline.service");
		await timelineService.createTimelineUpdate({
			job_id: quote.job_id,
			event_type: "TECHNICIAN_WITHDRAWN",
			notes: `Availability withdrawn by ${listing.business_name || `${listing.first_name} ${listing.last_name}`}: ${message}`,
			updated_by_id: userId,
			listing_id: listingId
		});

		// Send email to customer
		try {
			const { QuoteWithdrawnEmail } = await import(
				"@/components/email-templates/jobs/customer/QuoteWithdrawn"
			);

			await emailService.send({
				to: quote.job.user.email,
				subject: `Proposal Withdrawn - ${listing.business_name || "Service Provider"}`,
				react: QuoteWithdrawnEmail({
					customerName: quote.job.user.first_name,
					providerName:
						listing.business_name ||
						`${listing.first_name} ${listing.last_name}`,
					message: message || "",
					jobId: quote.job_id,
					rejectionReason: rejectionReason as any
				})
			});
		} catch (error) {
			console.error("Failed to send withdrawal email:", error);
		}

		return updatedQuote;
	}

	/**
	 * Customer accepts a quote - handles all related logic
	 */
	static async customerAcceptQuote(params: { quote: Quote; userId: string }) {
		const { quote, userId } = params;

		// Verify the job and quote exist and belong to the user
		const job = await prisma.job.findFirst({
			where: {
				id: quote.job_id,
				user_id: userId
			},
			include: {
				quotes: true,
				user: true,
				warranty_request: {
					include: {
						component: true,
						timeline_updates: true
					}
				}
			}
		});

		if (!job) {
			throw new Error("Job not found");
		}

		// Check if any quote is already accepted
		if (job.accepted_quote_id) {
			throw new Error("Another quote has already been accepted for this job");
		}

		// Find the specific quote
		const quoteWithDetails = await this.getQuoteWithDetails(quote.id);
		if (!quoteWithDetails) {
			throw new Error("Quote not found");
		}

		if (quoteWithDetails.job_id !== job.id) {
			throw new Error("Quote does not belong to this job");
		}

		if (
			quoteWithDetails.status !== QuoteStatus.ACCEPTED
		) {
			throw new Error("Quote cannot be accepted in its current state");
		}

		// Accept the quote using the centralized method
		const acceptedQuote = await this.changeQuoteStatus({
			quoteId: quoteWithDetails.id,
			listingId: quoteWithDetails.listing_id,
			newStatus: QuoteStatus.IN_PROGRESS,
			userId
		});

		// Reject all other quotes for this job
		const otherQuotes = job.quotes.filter((q) => q.id !== quoteWithDetails.id);
		for (const otherQuote of otherQuotes) {
			// Reject ALL other quotes, not just the ones that are currently ACCEPTED
			await this.changeQuoteStatus({
				quoteId: otherQuote.id,
				listingId: otherQuote.listing_id,
				newStatus: QuoteStatus.EXPIRED,
				userId
			});
		}

		// Update job status to ASSIGNED and set accepted_quote_id
		await prisma.job.update({
			where: { id: quoteWithDetails.job_id },
			data: {
				status: JobStatus.IN_PROGRESS,
				accepted_quote_id: quoteWithDetails.id
			}
		});

		// Create timeline event
		const { timelineService } = await import("./timeline.service");
		await timelineService.createTimelineUpdate({
			job_id: quoteWithDetails.job_id,
			event_type: "CUSTOMER_ACCEPTED",
			updated_by_id: userId,
			warranty_request_id: job.warranty_request_id,
			notes: `Proposal accepted by customer: ${job.user.first_name} ${job.user.last_name}`
		});

		// Update warranty request if applicable
		if (job.warranty_request_id) {
			await prisma.warrantyRequest.update({
				where: { id: job.warranty_request_id },
				data: { status: "JOB_STARTED", listing_id: quoteWithDetails.listing_id }
			});
		}

		// Send notifications to the accepted provider and rejected providers
		await this.sendQuoteAcceptanceNotifications(
			job,
			acceptedQuote,
			otherQuotes
		);

		// Send warranty SMS notification if this is a warranty job
		if (job.warranty_request_id) {
			try {
				const { WarrantyRequestService } = await import("./warranty-request.service");
				await WarrantyRequestService.sendWarrantySmsNotification(acceptedQuote.id);
			} catch (error) {
				console.error("Failed to send warranty SMS notification:", error);
				// Don't fail the main operation if SMS fails
			}
		}

		return acceptedQuote;
	}

	/**
	 * Provider accepts a job FOR THE CUSTOMER
	 */
	static async providerStartJob(params: {
		quote: Quote;
		userId: string;
	}) {
		const { quote, userId } = params;

		// Verify the job and quote exist and belong to the user
		const job = await prisma.job.findFirst({
			where: {
				id: quote.job_id,
			},
			include: {
				quotes: true,
				user: true,
				warranty_request: {
					include: {
						component: true,
						timeline_updates: true
					}
				}
			}
		});

		if (!job) {
			throw new Error("Job not found");
		}

		// Check if any quote is already accepted
		if (job.accepted_quote_id) {
			throw new Error("Another quote has already been accepted for this job");
		}

		// Find the specific quote
		const quoteWithDetails = await this.getQuoteWithDetails(quote.id);
		if (!quoteWithDetails) {
			throw new Error("Quote not found");
		}

		if (quoteWithDetails.job_id !== job.id) {
			throw new Error("Quote does not belong to this job");
		}

		if (
			quoteWithDetails.status !== QuoteStatus.ACCEPTED
		) {
			throw new Error("Quote cannot be accepted in its current state");
		}

		// Accept the quote using the centralized method
		const acceptedQuote = await this.changeQuoteStatus({
			quoteId: quoteWithDetails.id,
			listingId: quoteWithDetails.listing_id,
			newStatus: QuoteStatus.IN_PROGRESS,

		});

		// Reject all other quotes for this job
		const otherQuotes = job.quotes.filter((q) => q.id !== quoteWithDetails.id);
		for (const otherQuote of otherQuotes) {
			// Reject ALL other quotes, not just the ones that are currently ACCEPTED
			await this.changeQuoteStatus({
				quoteId: otherQuote.id,
				listingId: otherQuote.listing_id,
				newStatus: QuoteStatus.EXPIRED,
			});
		}

		// Update job status to ASSIGNED and set accepted_quote_id
		await prisma.job.update({
			where: { id: quoteWithDetails.job_id },
			data: {
				status: JobStatus.IN_PROGRESS,
				accepted_quote_id: quoteWithDetails.id
			}
		});

		// Create timeline event
		const { timelineService } = await import("./timeline.service");
		await timelineService.createTimelineUpdate({
			job_id: quoteWithDetails.job_id,
			event_type: "CUSTOMER_ACCEPTED",
			updated_by_id: userId,
			warranty_request_id: job.warranty_request_id,
			notes: `Proposal accepted by customer: ${job.user.first_name} ${job.user.last_name}`
		});

		// Update warranty request if applicable
		if (job.warranty_request_id) {
			await prisma.warrantyRequest.update({
				where: { id: job.warranty_request_id },
				data: { status: "JOB_STARTED", listing_id: quoteWithDetails.listing_id }
			});
		}

		// Send notifications to the accepted provider and rejected providers
		await this.sendQuoteAcceptanceNotifications(
			job,
			acceptedQuote,
			otherQuotes
		);

		// Send warranty SMS notification if this is a warranty job
		if (job.warranty_request_id) {
			try {
				const { WarrantyRequestService } = await import("./warranty-request.service");
				await WarrantyRequestService.sendWarrantySmsNotification(acceptedQuote.id);
			} catch (error) {
				console.error("Failed to send warranty SMS notification:", error);
				// Don't fail the main operation if SMS fails
			}
		}

		return acceptedQuote;

	}

	/**
	 * Customer rejects a quote - handles all related logic
	 */
	static async customerRejectQuote(params: {
		quote: Quote;
		jobId: string;
		userId: string;
	}) {
		const { quote, jobId, userId } = params;

		// Verify the job and quote exist and belong to the user
		const job = await prisma.job.findFirst({
			where: {
				id: jobId,
				user_id: userId
			},
			include: {
				quotes: true,
				user: true
			}
		});

		if (!job) {
			throw new Error("Job not found");
		}

		// Find the specific quote
		const quoteWithDetails = await this.getQuoteWithDetails(quote.id);
		if (!quoteWithDetails) {
			throw new Error("Quote not found");
		}

		if (quoteWithDetails.job_id !== jobId) {
			throw new Error("Quote does not belong to this job");
		}

		if (
			quoteWithDetails.status !== QuoteStatus.ACCEPTED
		) {
			throw new Error("Quote cannot be rejected in its current state");
		}

		// Reject the quote using the centralized method
		const rejectedQuote = await this.changeQuoteStatus({
			quoteId: quoteWithDetails.id,
			listingId: quoteWithDetails.listing_id,
			newStatus: QuoteStatus.CUSTOMER_REJECTED,
			userId
		});

		// Create timeline event
		const { timelineService } = await import("./timeline.service");
		await timelineService.createTimelineUpdate({
			job_id: jobId,
			event_type: "CUSTOMER_REJECTED",
			updated_by_id: userId,
			notes: `Proposal rejected by customer: ${job.user.first_name} ${job.user.last_name}`
		});

		// Send email notification to provider
		try {
			const providerName =
				quoteWithDetails.listing.business_name ||
				`${quoteWithDetails.listing.first_name} ${quoteWithDetails.listing.last_name}`;

			const { ProposalRejectedEmail } = await import(
				"@/components/email-templates/jobs/provider/ProposalRejected"
			);

			await emailService.send({
				to: quoteWithDetails.listing.email,
				subject: "RV Help: Service Request Update",
				react: ProposalRejectedEmail({
					customerName: job.user.first_name || "Customer",
					providerName,
					jobId: jobId
				})
			});
		} catch (error) {
			console.error("Failed to send proposal rejection email:", error);
			// Don't fail the main operation if email fails
		}

		return rejectedQuote;
	}

	/**
	 * Send notifications for quote acceptance
	 */
	private static async sendQuoteAcceptanceNotifications(
		job: any,
		acceptedQuote: any,
		rejectedQuotes: any[]
	) {
		try {
			const { smsService } = await import("./sms.service");

			// Notify the accepted provider
			if (acceptedQuote.listing.phone) {
				await smsService.sendToProvider(
					acceptedQuote.listing.phone,
					`RV Help: Your proposal has been accepted! Click here to view the job and customer details: ${process.env.NEXT_PUBLIC_APP_URL}/provider/jobs/${acceptedQuote.id}`
				);
			}

			if (acceptedQuote.listing.email) {
				const { ProposalAcceptedEmail } = await import(
					"@/components/email-templates/jobs/provider/ProposalAccepted"
				);
				await emailService.send({
					to: acceptedQuote.listing.email,
					replyTo: job.user.email,
					subject: "Your proposal has been accepted!",
					react: ProposalAcceptedEmail({
						customerName: job.user.first_name || "Customer",
						quoteId: acceptedQuote.id,
						reviewRequestScheduled: acceptedQuote.review_requested,
						reviewRequestDelay: acceptedQuote.review_delay_hours
					})
				});
			}

			// Notify other providers who quoted
			// All quotes passed to this method should be notified (they were all rejected by the customer's acceptance)
			for (const provider of rejectedQuotes) {
				// Get the listing details to access the email
				const quote = await this.getQuoteWithDetails(provider.id);
				if (quote?.listing?.email) {
					const { JobFilledEmail } = await import(
						"@/components/email-templates/jobs/provider/JobFilledEmail"
					);
					await emailService.send({
						to: quote.listing.email,
						subject: "RV Help: Service Request Update",
						react: JobFilledEmail({
							customerName: job.user.first_name || "Customer",
							job: job
						})
					});
				}
			}
		} catch (error) {
			console.error("Failed to send quote acceptance notifications:", error);
			// Don't fail the main operation if notifications fail
		}
	}

	/**
	 * Customer completes a job - handles all related quote logic
	 */
	static async customerCompleteJob(params: {
		jobId: string;
		userId: string;
		reason: string;
		selectedProviderId?: string; // If they worked with one of the invited providers
	}) {
		const { jobId, userId, reason, selectedProviderId } = params;

		// Get the job with all quotes
		const job = await prisma.job.findFirst({
			where: {
				id: jobId,
				user_id: userId,
			},
			include: {
				quotes: {
					include: {
						listing: {
							select: {
								id: true,
								business_name: true,
								first_name: true,
								last_name: true,
								email: true,
								notification_email: true,
							},
						},
					},
				},
				user: true,
			},
		});

		if (!job) {
			throw new Error("Job not found or access denied");
		}

		// Handle different scenarios based on job status
		if (job.accepted_quote_id) {
			// Case 1: Provider was already selected, job is in workroom
			throw new Error("Job already has a selected provider - use standard completion flow");
		} else {
			// Case 2: Job is in select-provider stage

			if (selectedProviderId) {
				// Customer worked with one of the invited providers
				const selectedQuote = job.quotes.find(q => q.listing.id === selectedProviderId);
				if (!selectedQuote) {
					throw new Error("Selected provider was not invited to this job");
				}

				// Mark the selected quote as completed
				await this.changeQuoteStatus({
					quoteId: selectedQuote.id,
					listingId: selectedQuote.listing.id,
					newStatus: QuoteStatus.COMPLETED,
					userId,
				});

				// Update job to reference the accepted quote
				await prisma.job.update({
					where: { id: jobId },
					data: {
						accepted_quote_id: selectedQuote.id,
					},
				});

				// Expire all other quotes
				const otherQuotes = job.quotes.filter(q => q.id !== selectedQuote.id);
				await this.expireQuotes(otherQuotes, userId);

				// Send notification to selected provider
				// Attach user data to the quote for the notification
				const selectedQuoteWithUser = { ...selectedQuote, user: job.user };
				await this.sendProviderCompletionNotification(selectedQuoteWithUser, reason);
			} else {
				// Customer didn't work with any of the invited providers
				// Expire all quotes and notify providers
				await this.expireQuotes(job.quotes, userId);
			}

			// Send notifications to all non-selected providers
			const nonSelectedQuotes = selectedProviderId
				? job.quotes.filter(q => q.listing.id !== selectedProviderId)
				: job.quotes;

			await this.notifyProvidersJobNoLongerAvailable(
				nonSelectedQuotes,
				reason,
				"completed",
				job.user.first_name || "The customer"
			);
		}

		// Add newsletter tag to customer who completed the job
		try {
			const tags = ["job complete"];

			await EmailNewsletterService.syncNewsletterSubscriber({
				email: job.user.email,
				first_name: job.user.first_name || "",
				last_name: job.user.last_name || "",
				user: job.user,
				tags
			});

			console.log(`Added "job complete" newsletter tag to user ${job.user.email}`);
		} catch (error) {
			console.error("Failed to add newsletter tag for job completion:", error);
			// Don't fail the job completion if newsletter tagging fails
		}

		return job;
	}

	/**
	 * Customer cancels a job - handles all related quote logic
	 */
	static async customerCancelJob(params: {
		jobId: string;
		userId: string;
		reason: string;
	}) {
		const { jobId, userId, reason } = params;

		// Get the job with all quotes
		const job = await prisma.job.findFirst({
			where: {
				id: jobId,
				user_id: userId,
			},
			include: {
				quotes: {
					include: {
						listing: {
							select: {
								id: true,
								business_name: true,
								first_name: true,
								last_name: true,
								email: true,
							},
						},
					},
				},
				user: true,
			},
		});

		if (!job) {
			throw new Error("Job not found or access denied");
		}

		// Expire all quotes regardless of job stage
		await this.expireQuotes(job.quotes, userId);

		// Notify all providers about cancellation
		await this.notifyProvidersJobNoLongerAvailable(
			job.quotes,
			reason,
			"cancelled",
			job.user.first_name || "The customer"
		);

		// Update job status to CANCELLED
		const updatedJob = await prisma.job.update({
			where: { id: jobId },
			data: {
				status: JobStatus.CANCELLED,
			},
		});

		return updatedJob;
	}

	/**
	 * Helper method to expire multiple quotes
	 */
	private static async expireQuotes(quotes: any[], userId: string) {
		for (const quote of quotes) {
			// Only expire quotes that are still pending or accepted (not already expired/rejected)
			if (quote.status === QuoteStatus.PENDING || quote.status === QuoteStatus.ACCEPTED) {
				await this.changeQuoteStatus({
					quoteId: quote.id,
					listingId: quote.listing_id,
					newStatus: QuoteStatus.EXPIRED,
					userId,
				});
			}
		}
	}

	/**
	 * Send notification to providers that job is no longer available
	 */
	private static async notifyProvidersJobNoLongerAvailable(
		quotes: any[],
		reason: string,
		action: "completed" | "cancelled",
		customerName?: string
	) {

		for (const quote of quotes) {
			// Only notify providers who had pending or accepted quotes
			if (quote.status === QuoteStatus.PENDING || quote.status === QuoteStatus.ACCEPTED) {
				const providerEmail = quote.listing.notification_email || quote.listing.email;
				const providerName = quote.listing.business_name ||
					`${quote.listing.first_name} ${quote.listing.last_name}`;

				try {
					if (action === "completed") {
						// Create a simple React component for completion (existing logic)
						const SimpleNotificationEmail = ({ providerName, message }: { providerName: string; message: string }) => (
							React.createElement('div', { style: { fontFamily: 'Arial, sans-serif', fontSize: '14px', lineHeight: '1.6' } }, [
								React.createElement('p', { key: 'greeting' }, `Hello ${providerName},`),
								React.createElement('p', { key: 'message', style: { whiteSpace: 'pre-line' } }, message),
								React.createElement('p', { key: 'signature' }, 'Best regards,\nRV Help Team')
							])
						);

						const message = `The customer has completed their service request and is no longer looking for providers.\n\nReason: ${reason}\n\nThank you for your interest in this opportunity.`;

						await emailService.send({
							to: providerEmail,
							subject: "Service Request Completed by Customer",
							react: SimpleNotificationEmail({ providerName, message }),
						});
					} else {
						// Use professional email template for cancellations
						const { JobCancelledEmail } = await import(
							"@/components/email-templates/jobs/provider/JobCancelledEmail"
						);

						await emailService.send({
							to: providerEmail,
							subject: "Service Request Cancelled by Customer",
							react: JobCancelledEmail({
								customerName: customerName || "The customer",
								providerName,
								reason,
							}),
						});
					}

				} catch (error) {
					console.error(`❌ Failed to send notification to provider ${providerEmail}:`, error);
					// Don't fail the entire operation if email fails
				}
			} else {
				console.log(`🟡 Skipping notification for provider ${quote.listing.business_name || quote.listing.first_name} (quote status: ${quote.status})`);
			}
		}
	}

	/**
	 * Send notification to the provider who was selected for completion
	 */
	private static async sendProviderCompletionNotification(
		quote: any,
		reason: string
	) {
		const providerEmail = quote.listing.notification_email || quote.listing.email;

		try {

			await emailService.send({
				to: providerEmail,
				subject: "Customer Completed Service Request",
				react: JobCompletedEmail({
					customerName: quote.user?.first_name || "Customer",
					resolutionNotes: reason
				}),
			});

		} catch (error) {
			console.error(`❌ Failed to send completion notification to provider ${providerEmail}:`, error);
			// Don't fail the entire operation if email fails
		}
	}
}
