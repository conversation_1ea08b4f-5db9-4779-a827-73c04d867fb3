import config from "@/config";
import { adminLogger } from "@/lib/services/admin-log.service";
import { NextRequest } from "next/server";

export interface FirstPromoterEvent {
	type: string;
	payload: Record<string, any>;
}

export interface FirstPromoterResponse {
	success: boolean;
	message?: string;
	data?: any;
}

export class FirstPromoterService {
	/**
	 * Check if FirstPromoter is enabled
	 */
	private static get isEnabled(): boolean {
		return !!(config.firstPromoter.companyId && config.firstPromoter.apiKey);
	}

	/**
	 * Get the account ID from config
	 */
	private static get ACCOUNT_ID(): string {
		return config.firstPromoter.companyId;
	}

	/**
	 * Get the API key from config
	 */
	private static get API_KEY(): string {
		return config.firstPromoter.apiKey;
	}

	/**
	 * Extract FirstPromoter tracking ID from cookies
	 */
	private static extractTrackingId(req?: NextRequest): string | null {
		if (!req) return null;

		const cookieHeader = req.headers.get("cookie");
		if (!cookieHeader) return null;

		// Parse cookies manually to find _fprom_tid
		const cookies = cookieHeader
			.split(";")
			.reduce((acc: Record<string, string>, cookie) => {
				const [key, value] = cookie.trim().split("=");
				if (key && value) {
					acc[key] = decodeURIComponent(value);
				}
				return acc;
			}, {});

		return cookies["_fprom_tid"] || null;
	}

	/**
	 * Get the API endpoint for the event type
	 */
	private static getEventEndpoint(eventType: string): string {
		const endpoints = {
			signup: "/track/signup",
			newsletter: "/track/signup", // All leads use signup endpoint
			lead_magnet: "/track/signup", // All leads use signup endpoint
			lead_form: "/track/signup", // All leads use signup endpoint
			pro_membership: "/track/sale" // Sale endpoint for membership purchases
		};

		return endpoints[eventType as keyof typeof endpoints] || "/track/signup";
	}

	/**
	 * Send event to FirstPromoter API
	 */
	private static async sendToFirstPromoter(
		event: FirstPromoterEvent
	): Promise<FirstPromoterResponse> {
		// Skip if not enabled
		if (!this.isEnabled) {
			return { success: false, message: "Service not enabled" };
		}

		// Skip if no tracking data available
		// FirstPromoter requires either tid or ref_id to track an event
		if (!event.payload.tid && !event.payload.ref_id) {
			return { success: true, message: "No referral data to track" };
		}

		// Skip in development mode
		if (process.env.NODE_ENV === "development") {
			return { success: true, message: "Development mode" };
		}

		try {
			const endpoint = this.getEventEndpoint(event.type);
			const url = `https://v2.firstpromoter.com/api/v2${endpoint}`;

			const response = await fetch(url, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${this.API_KEY}`,
					"Account-ID": this.ACCOUNT_ID
				},
				body: JSON.stringify(event.payload)
			});

			if (!response.ok) {
				const errorData = await response.text();
				adminLogger.log(`FirstPromoter API error: ${event.type}`, {
					status: response.status,
					error: errorData,
					payload: event.payload
				});

				return {
					success: false,
					message: `API error: ${response.status}`,
					data: errorData
				};
			}

			const data = await response.json();

			return {
				success: true,
				message: "Event tracked successfully",
				data
			};
		} catch (error) {
			console.error(`Error tracking ${event.type}:`, error);

			return {
				success: false,
				message: error.message || "Unknown error"
			};
		}
	}

	/**
	 * Track user signup/registration
	 */
	public static async trackSignup(data: {
		email: string;
		userId?: string;
		firstName?: string;
		lastName?: string;
		req?: NextRequest;
	}): Promise<FirstPromoterResponse> {
		const payload: Record<string, any> = {
			email: data.email,
			uid: data.userId,
			firstName: data.firstName,
			lastName: data.lastName
		};

		// Add tracking ID if available
		const tid = this.extractTrackingId(data.req);
		if (tid) {
			payload.tid = tid;
		}

		const event: FirstPromoterEvent = {
			type: "signup",
			payload
		};

		return this.sendToFirstPromoter(event);
	}

	/**
	 * Track newsletter signup
	 */
	public static async trackNewsletterSignup(data: {
		email: string;
		userId?: string;
		req?: NextRequest;
	}): Promise<FirstPromoterResponse> {
		const payload: Record<string, any> = {
			email: data.email,
			uid: data.userId
		};


		// Add tracking ID if available
		const tid = this.extractTrackingId(data.req);

		if (tid) {
			payload.tid = tid;
		}

		const event: FirstPromoterEvent = {
			type: "newsletter",
			payload
		};


		return this.sendToFirstPromoter(event);
	}

	/**
	 * Track lead magnet download
	 */
	public static async trackLeadMagnet(data: {
		email: string;
		userId?: string;
		leadMagnetTitle?: string;
		req?: NextRequest;
	}): Promise<FirstPromoterResponse> {
		const payload: Record<string, any> = {
			email: data.email,
			uid: data.userId,
			leadMagnetTitle: data.leadMagnetTitle
		};

		// Add tracking ID if available
		const tid = this.extractTrackingId(data.req);
		if (tid) {
			payload.tid = tid;
		}

		const event: FirstPromoterEvent = {
			type: "lead_magnet",
			payload
		};

		return this.sendToFirstPromoter(event);
	}

	/**
	 * Track lead form submission
	 */
	public static async trackLeadForm(data: {
		email: string;
		userId?: string;
		formType?: string;
		req?: NextRequest;
	}): Promise<FirstPromoterResponse> {
		const payload: Record<string, any> = {
			email: data.email,
			uid: data.userId,
			formType: data.formType
		};

		// Add tracking ID if available
		const tid = this.extractTrackingId(data.req);
		if (tid) {
			payload.tid = tid;
		}

		const event: FirstPromoterEvent = {
			type: "lead_form",
			payload
		};

		return this.sendToFirstPromoter(event);
	}

	/**
	 * Track pro membership purchase
	 */
	public static async trackProMembership(data: {
		email: string;
		userId?: string;
		membershipLevel: string;
		amount: number;
		currency: string;
		req?: NextRequest;
	}): Promise<FirstPromoterResponse> {
		const payload: Record<string, any> = {
			email: data.email,
			uid: data.userId,
			amount: data.amount,
			currency: data.currency,
			plan: data.membershipLevel
		};

		// Add tracking ID if available
		const tid = this.extractTrackingId(data.req);
		if (tid) {
			payload.tid = tid;
		}

		const event: FirstPromoterEvent = {
			type: "pro_membership",
			payload
		};

		return this.sendToFirstPromoter(event);
	}
}
