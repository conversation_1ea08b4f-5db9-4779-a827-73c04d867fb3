import { prisma } from "@/lib/prisma";
import { UserDevice } from "@rvhelp/database";
import { adminLogger } from "./admin-log.service";

export class UserDeviceService {
	/**
	 * Register a device for an anonymous user
	 * This is called when someone opens the mobile app for the first time
	 */
	static async registerAnonymousDevice(data: {
		deviceToken: string;
		platform: "ios" | "android";
	}): Promise<UserDevice> {
		try {
			// Check if device already exists
			const existingDevice = await prisma.userDevice.findUnique({
				where: { device_token: data.deviceToken }
			});

			if (existingDevice) {
				// Update last used timestamp
				return await prisma.userDevice.update({
					where: { id: existingDevice.id },
					data: { last_used_at: new Date() }
				});
			}

			// Create new anonymous device
			const device = await prisma.userDevice.create({
				data: {
					device_token: data.deviceToken,
					platform: data.platform,
					user_id: null, // Anonymous user
					last_used_at: new Date()
				}
			});

			adminLogger.log("Anonymous device registered", {
				deviceId: device.id,
				platform: data.platform
			});

			return device;
		} catch (error) {
			console.error("Error registering anonymous device:", error);
			throw new Error("Failed to register device");
		}
	}

	/**
	 * Connect a device to a user when they register or login
	 * This is called during the registration/login process
	 */
	static async connectDeviceToUser(data: {
		deviceToken: string;
		userId: string;
		platform?: "ios" | "android";
	}): Promise<UserDevice> {
		try {
			const existingDevice = await prisma.userDevice.findUnique({
				where: { device_token: data.deviceToken }
			});

			if (existingDevice) {
				// Update existing device with user info
				const updatedDevice = await prisma.userDevice.update({
					where: { id: existingDevice.id },
					data: {
						user_id: data.userId,
						last_used_at: new Date(),
						...(data.platform && { platform: data.platform })
					}
				});

				adminLogger.log("Device connected to user", {
					deviceId: updatedDevice.id,
					userId: data.userId,
					previouslyAnonymous: !existingDevice.user_id
				});

				return updatedDevice;
			}

			// Create new device directly connected to user
			const device = await prisma.userDevice.create({
				data: {
					device_token: data.deviceToken,
					platform: data.platform || "ios",
					user_id: data.userId,
					last_used_at: new Date()
				}
			});

			adminLogger.log("New device created for user", {
				deviceId: device.id,
				userId: data.userId
			});

			return device;
		} catch (error) {
			console.error("Error connecting device to user:", error);
			throw new Error("Failed to connect device to user");
		}
	}

	/**
	 * Update device token (when app updates or token refreshes)
	 */
	static async updateDeviceToken(data: {
		oldToken: string;
		newToken: string;
		userId?: string;
	}): Promise<UserDevice> {
		try {
			const existingDevice = await prisma.userDevice.findUnique({
				where: { device_token: data.oldToken }
			});

			if (!existingDevice) {
				throw new Error("Device not found");
			}

			const updatedDevice = await prisma.userDevice.update({
				where: { id: existingDevice.id },
				data: {
					device_token: data.newToken,
					last_used_at: new Date(),
					...(data.userId && { user_id: data.userId })
				}
			});

			adminLogger.log("Device token updated", {
				deviceId: updatedDevice.id,
				userId: data.userId
			});

			return updatedDevice;
		} catch (error) {
			console.error("Error updating device token:", error);
			// Re-throw the original error if it's our expected "Device not found" error
			if (error instanceof Error && error.message === "Device not found") {
				throw error;
			}
			throw new Error("Failed to update device token");
		}
	}

	/**
	 * Get all devices for a user
	 */
	static async getUserDevices(userId: string): Promise<UserDevice[]> {
		try {
			return await prisma.userDevice.findMany({
				where: { user_id: userId },
				orderBy: { last_used_at: "desc" }
			});
		} catch (error) {
			console.error("Error getting user devices:", error);
			throw new Error("Failed to get user devices");
		}
	}

	/**
	 * Get device by token
	 */
	static async getDeviceByToken(
		deviceToken: string
	): Promise<UserDevice | null> {
		try {
			return await prisma.userDevice.findUnique({
				where: { device_token: deviceToken },
				include: { user: true }
			});
		} catch (error) {
			console.error("Error getting device by token:", error);
			return null;
		}
	}

	/**
	 * Remove device (when user logs out or uninstalls app)
	 */
	static async removeDevice(deviceToken: string): Promise<void> {
		try {
			const device = await prisma.userDevice.findUnique({
				where: { device_token: deviceToken }
			});

			if (device) {
				await prisma.userDevice.delete({
					where: { device_token: deviceToken }
				});

				adminLogger.log("Device removed", {
					deviceId: device.id,
					userId: device.user_id
				});
			}
		} catch (error) {
			console.error("Error removing device:", error);
			throw new Error("Failed to remove device");
		}
	}

	/**
	 * Get anonymous devices (devices not connected to users)
	 */
	static async getAnonymousDevices(): Promise<UserDevice[]> {
		try {
			return await prisma.userDevice.findMany({
				where: { user_id: null },
				orderBy: { created_at: "desc" }
			});
		} catch (error) {
			console.error("Error getting anonymous devices:", error);
			throw new Error("Failed to get anonymous devices");
		}
	}

	/**
	 * Clean up old anonymous devices (older than 30 days)
	 */
	static async cleanupOldAnonymousDevices(): Promise<number> {
		try {
			const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

			const result = await prisma.userDevice.deleteMany({
				where: {
					user_id: null,
					last_used_at: { lt: thirtyDaysAgo }
				}
			});

			adminLogger.log("Old anonymous devices cleaned up", {
				count: result.count
			});

			return result.count;
		} catch (error) {
			console.error("Error cleaning up old anonymous devices:", error);
			throw new Error("Failed to cleanup old anonymous devices");
		}
	}

	/**
	 * Get device statistics
	 */
	static async getDeviceStats(): Promise<{
		totalDevices: number;
		anonymousDevices: number;
		connectedDevices: number;
		platformBreakdown: { platform: string; count: number }[];
	}> {
		try {
			const [total, anonymous, platformStats] = await Promise.all([
				prisma.userDevice.count(),
				prisma.userDevice.count({ where: { user_id: null } }),
				prisma.userDevice.groupBy({
					by: ["platform"],
					_count: true
				})
			]);

			return {
				totalDevices: total,
				anonymousDevices: anonymous,
				connectedDevices: total - anonymous,
				platformBreakdown: platformStats.map((stat) => ({
					platform: stat.platform,
					count: stat._count
				}))
			};
		} catch (error) {
			console.error("Error getting device stats:", error);
			throw new Error("Failed to get device statistics");
		}
	}

	/**
	 * Connect multiple anonymous devices to a user
	 * Useful when a user registers on mobile but has been browsing anonymously
	 */
	static async connectMultipleDevicesToUser(data: {
		deviceTokens: string[];
		userId: string;
	}): Promise<UserDevice[]> {
		try {
			const devices = await prisma.userDevice.findMany({
				where: {
					device_token: { in: data.deviceTokens },
					user_id: null // Only connect anonymous devices
				}
			});

			if (devices.length === 0) {
				return [];
			}

			const updatedDevices = await prisma.$transaction(
				devices.map((device) =>
					prisma.userDevice.update({
						where: { id: device.id },
						data: {
							user_id: data.userId,
							last_used_at: new Date()
						}
					})
				)
			);

			adminLogger.log("Multiple devices connected to user", {
				userId: data.userId,
				deviceCount: updatedDevices.length,
				deviceIds: updatedDevices.map((d) => d.id)
			});

			return updatedDevices;
		} catch (error) {
			console.error("Error connecting multiple devices to user:", error);
			throw new Error("Failed to connect multiple devices to user");
		}
	}
}
