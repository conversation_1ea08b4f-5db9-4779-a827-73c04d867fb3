import prisma from "@/lib/prisma";
import { QuoteMessageParticipantType, QuoteMessageType, RejectionReasonType } from "@rvhelp/database";
import { getRejectionReasonForHumans } from "../utils/quote";
import { jobLifecycleService } from "./job-lifecycle.service";

export interface CreateQuoteMessageParams {
	quoteId: string;
	userId: string;
	content: string;
	type?: QuoteMessageType;
	attachments?: string[];
	metadata?: Record<string, any>;
}

export interface CreateProviderQuoteMessageParams {
	quoteId: string;
	listingId: string;
	content: string;
	type?: QuoteMessageType;
	attachments?: string[];
	metadata?: Record<string, any>;
}

export interface GetQuoteMessagesParams {
	quoteId: string;
}

export interface GetProviderConversationsParams {
	providerId?: string;
	userId?: string;
	limit?: number;
	offset?: number;
}

// Clean, consistent message response type
export interface MessageResponse {
	id: string;
	quote_id: string;
	content: string;
	status: string;
	type: string;
	attachments: string[];
	metadata: Record<string, any>;
	created_at: Date;
	sent_at: Date | null;
	delivered_at: Date | null;
	read_at: Date | null;

	// Clear sender information
	sender: {
		id: string;
		type: QuoteMessageParticipantType;
		name: string;
		email: string;
		avatar?: string | null;
	};

	// Clear recipient information
	recipient: {
		id: string;
		type: QuoteMessageParticipantType;
		name: string;
		email: string;
		avatar?: string | null;
	};
}

export interface ConversationResponse {
	quote_id: string;
	messages: MessageResponse[];
	last_message_at: Date;
	unread_count: number;
}

export class MessageService {
	/**
	 * Get all messages for a quote with clean, consistent response format
	 */
	static async getQuoteMessages(
		params: GetQuoteMessagesParams
	): Promise<MessageResponse[]> {
		const { quoteId } = params;

		try {
			const messages = await prisma.quoteMessage.findMany({
				where: {
					quote_id: quoteId
				},
				include: {
					quote: {
						include: {
							job: {
								include: {
									user: {
										select: {
											id: true,
											first_name: true,
											last_name: true,
											email: true,
											avatar: true
										}
									}
								}
							},
							listing: true
						}
					}
				},
				orderBy: {
					created_at: "asc"
				}
			});

			// Transform to clean, consistent format
			const transformedMessages = messages.map((message) => this.transformMessageToResponse(message));
			return transformedMessages;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * Create a system message for provider responses (accept/reject)
	 * This creates a message visible in the thread without sending notifications
	 */
	static async createProviderResponseMessage(
		params: {
			quoteId: string;
			listingId: string;
			responseType: "accept" | "reject";
			message?: string;
			rejectionReason?: string;
		}
	): Promise<MessageResponse> {
		const {
			quoteId,
			listingId,
			responseType,
			message,
			rejectionReason
		} = params;

		const rejectionReasonForHumans = getRejectionReasonForHumans(rejectionReason as RejectionReasonType);

		// Get quote details for message content
		const quote = await prisma.quote.findFirst({
			where: {
				id: quoteId,
				listing_id: listingId
			},
			include: {
				listing: true,
				job: {
					include: {
						user: {
							select: {
								id: true,
								email: true,
								first_name: true,
								last_name: true,
								avatar: true
							}
						}
					}
				}
			}
		});

		if (!quote) {
			throw new Error("Quote not found or access denied");
		}

		// Create appropriate message content based on response type
		let content = "";
		if (responseType === "accept") {
			content = "✅ **Provider Accepted**";
			if (message) {
				content += `\n\n${message}`;
			}
		} else {
			content = "❌ **Provider Declined**";
			if (rejectionReason) {
				content += `\n\n**Reason:** ${rejectionReasonForHumans}`;
			}
			if (message) {
				content += `\n\n${message}`;
			}
		}

		// Create the system message
		const systemMessage = await prisma.quoteMessage.create({
			data: {
				quote_id: quoteId,
				sender_id: listingId,
				sender_type: QuoteMessageParticipantType.PROVIDER,
				recipient_id: quote.job.user.id,
				recipient_type: QuoteMessageParticipantType.USER,
				content,
				type: QuoteMessageType.SYSTEM,
				attachments: [],
				metadata: {
					responseType,
					rejectionReason: rejectionReasonForHumans || null
				}
			},
			include: {
				quote: {
					include: {
						listing: true,
						job: {
							include: {
								user: {
									select: {
										id: true,
										first_name: true,
										last_name: true,
										email: true,
										avatar: true
									}
								}
							}
						}
					}
				}
			}
		});

		// Transform to clean response format
		return this.transformMessageToResponse(systemMessage);
	}

	/**
	 * Create a new message from a provider with clean response format
	 */
	static async createProviderQuoteMessage(
		params: CreateProviderQuoteMessageParams
	): Promise<MessageResponse> {
		const {
			quoteId,
			listingId,
			content,
			type = QuoteMessageType.TEXT,
			attachments = [],
			metadata = {}
		} = params;

		try {
			// Get quote details for validation
			const quote = await prisma.quote.findFirst({
				where: {
					id: quoteId,
					listing_id: listingId
				},
				include: {
					job: {
						include: {
							user: {
								select: {
									id: true,
									first_name: true,
									last_name: true,
									email: true,
									avatar: true
								}
							}
						}
					}
				}
			});

			if (!quote) {
				throw new Error("Quote not found or access denied");
			}

			// Check if this is the first provider response to update responded_at
			const existingMessages = await prisma.quoteMessage.count({
				where: {
					quote_id: quoteId,
					sender_type: QuoteMessageParticipantType.PROVIDER
				}
			});

			const shouldUpdateResponseTime = existingMessages === 0;

			// Create the message
			const message = await prisma.quoteMessage.create({
				data: {
					quote_id: quoteId,
					content,
					type,
					attachments,
					metadata,
					sender_id: listingId,
					sender_type: QuoteMessageParticipantType.PROVIDER,
					recipient_id: quote.job.user.id,
					recipient_type: QuoteMessageParticipantType.USER,
					status: "SENT",
					sent_at: new Date()
				},
				include: {
					quote: {
						include: {
							job: {
								include: {
									user: {
										select: {
											id: true,
											first_name: true,
											last_name: true,
											email: true,
											avatar: true
										}
									}
								}
							},
							listing: true
						}
					}
				}
			});


			// Update quote's responded_at if this is the first provider response
			if (shouldUpdateResponseTime) {
				await prisma.quote.update({
					where: { id: quoteId },
					data: { responded_at: new Date() }
				});
			}

			// Send notification
			try {
				await jobLifecycleService.sendNewMessageNotification(message as any);
			} catch (error) {
				console.error("Failed to send message notification:", error);
			}

			return this.transformMessageToResponse(message);
		} catch (error) {
			console.error("MessageService.createProviderQuoteMessage - Error:", error);
			console.error("MessageService.createProviderQuoteMessage - Error stack:", error?.stack);
			throw error;
		}
	}

	/**
	 * Create a new message from a user with clean response format
	 */
	static async createQuoteMessage(
		params: CreateQuoteMessageParams
	): Promise<MessageResponse> {
		const {
			quoteId,
			userId,
			content,
			type = QuoteMessageType.TEXT,
			attachments = [],
			metadata = {}
		} = params;

		try {
			// Get quote details for validation
			const quote = await prisma.quote.findUnique({
				where: { id: quoteId },
				include: {
					job: {
						include: {
							user: {
								select: { id: true }
							}
						}
					}
				}
			});

			if (!quote) {
				throw new Error("Quote not found or access denied");
			}

			// Validate that the user is the job owner
			if (quote.job.user.id !== userId) {
				throw new Error("User is not the job owner");
			}

			// Get the listing details for the recipient
			const listing = await prisma.listing.findUnique({
				where: { id: quote.listing_id },
				select: {
					id: true,
					business_name: true,
					first_name: true,
					last_name: true,
					email: true,
					profile_image: true
				}
			});

			if (!listing) {
				throw new Error("Listing not found");
			}

			// Create the message
			const message = await prisma.quoteMessage.create({
				data: {
					quote_id: quoteId,
					content,
					type,
					attachments,
					metadata,
					sender_id: userId,
					sender_type: QuoteMessageParticipantType.USER,
					recipient_id: listing.id,
					recipient_type: QuoteMessageParticipantType.PROVIDER,
					status: "SENT",
					sent_at: new Date()
				},
				include: {
					quote: {
						include: {
							job: {
								include: {
									user: {
										select: {
											id: true,
											first_name: true,
											last_name: true,
											email: true,
											avatar: true
										}
									}
								}
							},
							listing: true
						}
					}
				}
			});

			// Send notification
			try {
				await jobLifecycleService.sendNewMessageNotification(message as any);
			} catch (error) {
				console.error("Failed to send message notification:", error);
			}

			return this.transformMessageToResponse(message);
		} catch (error) {
			console.error("MessageService.createQuoteMessage - Error:", error);
			console.error("MessageService.createQuoteMessage - Error stack:", error?.stack);
			throw error;
		}
	}

	/**
	 * Transform a database message to clean API response format
	 */
	private static transformMessageToResponse(
		message: any
	): MessageResponse {
		// Get sender info based on sender_type and sender_id
		const sender =
			message.sender_type === QuoteMessageParticipantType.USER
				? {
					id: message.sender_id,
					type: QuoteMessageParticipantType.USER,
					name: `${message.quote.job.user.first_name} ${message.quote.job.user.last_name}`,
					email: message.quote.job.user.email,
					avatar: message.quote.job.user.avatar
				}
				: {
					id: message.sender_id,
					type: QuoteMessageParticipantType.PROVIDER,
					name:
						message.quote.listing.business_name ||
						`${message.quote.listing.first_name} ${message.quote.listing.last_name}`,
					email: message.quote.listing.email,
					avatar: message.quote.listing.profile_image
				};

		// Get recipient info based on recipient_type and recipient_id
		const recipient =
			message.recipient_type === QuoteMessageParticipantType.USER
				? {
					id: message.recipient_id,
					type: QuoteMessageParticipantType.USER,
					name: `${message.quote.job.user.first_name} ${message.quote.job.user.last_name}`,
					email: message.quote.job.user.email,
					avatar: message.quote.job.user.avatar
				}
				: {
					id: message.recipient_id,
					type: QuoteMessageParticipantType.PROVIDER,
					name:
						message.quote.listing.business_name ||
						`${message.quote.listing.first_name} ${message.quote.listing.last_name}`,
					email: message.quote.listing.email,
					avatar: message.quote.listing.profile_image
				};

		return {
			id: message.id,
			quote_id: message.quote_id,
			content: message.content,
			status: message.status,
			type: message.type,
			attachments: message.attachments,
			metadata: message.metadata || {},
			created_at: message.created_at,
			sent_at: message.sent_at,
			delivered_at: message.delivered_at,
			read_at: message.read_at,
			sender,
			recipient
		};
	}

	/**
	 * Get all conversations for a provider with clean, consistent response format
	 */
	static async getConversations(
		params: GetProviderConversationsParams
	): Promise<{
		conversations: ConversationResponse[];
		pagination: {
			total: number;
			offset: number;
			limit: number;
			hasMore: boolean;
		};
	}> {
		const { providerId, userId, limit = 50, offset = 0 } = params;

		let orClause = [];

		if (providerId) {
			orClause = [
				{
					sender_id: providerId
				},
				{
					recipient_id: providerId
				}
			];
		} else {
			orClause = [
				{
					sender_id: userId
				},
				{
					recipient_id: userId
				}
			];
		}
		// Get all messages for the provider's quotes
		const messagesWithContext = await prisma.quoteMessage.findMany({
			where: {
				OR: orClause
			},
			include: {
				quote: {
					include: {
						job: {
							include: {
								user: {
									select: {
										id: true,
										first_name: true,
										last_name: true,
										email: true,
										phone: true,
										avatar: true
									}
								}
							}
						},
						listing: true
					}
				}
			},
			orderBy: {
				created_at: "desc"
			},
			skip: offset ?? 0,
			take: limit ?? 50
		});


		// Transform messages to clean format
		const transformedMessages = messagesWithContext.map((message) =>
			this.transformMessageToResponse(message)
		);

		// Group messages by conversation (quote_id)
		const conversations = transformedMessages.reduce(
			(acc, message) => {
				const quoteId = message.quote_id;

				if (!acc[quoteId]) {
					acc[quoteId] = {
						quote_id: quoteId,
						messages: [],
						last_message_at: message.created_at,
						unread_count: 0
					};
				}

				acc[quoteId].messages.push(message);

				// Update last message time
				if (message.created_at > acc[quoteId].last_message_at) {
					acc[quoteId].last_message_at = message.created_at;
				}

				// Count unread messages based on context
				if (userId) {
					// For users: count unread messages from providers
					if (
						message.sender.type === QuoteMessageParticipantType.PROVIDER &&
						!message.read_at
					) {
						acc[quoteId].unread_count++;
					}
				} else {
					// For providers: count unread messages from users
					if (
						message.sender.type === QuoteMessageParticipantType.USER &&
						!message.read_at
					) {
						acc[quoteId].unread_count++;
					}
				}

				return acc;
			},
			{} as Record<string, ConversationResponse>
		);

		// Convert to array and sort by last message time
		const conversationsArray = Object.values(conversations).sort(
			(a, b) =>
				new Date(b.last_message_at).getTime() -
				new Date(a.last_message_at).getTime()
		);

		// Get total count for pagination - count distinct conversations, not individual messages
		const totalCountResult = await prisma.quoteMessage.findMany({
			where: {
				OR: orClause
			},
			select: {
				quote_id: true
			},
			distinct: ['quote_id']
		});
		const totalCount = totalCountResult.length;

		return {
			conversations: conversationsArray,
			pagination: {
				total: totalCount,
				offset,
				limit,
				hasMore: offset + limit < totalCount
			}
		};
	}
}
