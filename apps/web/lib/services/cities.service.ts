import prisma from "@/lib/prisma";

export interface City {
    id: string;
    city: string | null;
    state_id?: string | null; // For US cities
    state_name?: string | null; // For US cities
    province_id?: string | null; // For Canadian cities
    province_name?: string | null; // For Canadian cities
    lat: string | null;
    lng: string | null;
    alternate_name: string | null;
    // Computed field for unified access
    region_name?: string | null; // Will be state_name or province_name
}

export async function getCitiesByRegion(region: string): Promise<City[]> {
    try {
        // First try US cities
        const usCities = await prisma.cityUnitedStates.findMany({
            where: {
                state_name: {
                    equals: region,
                    mode: "insensitive"
                }
            },
            select: {
                id: true,
                city: true,
                state_id: true,
                state_name: true,
                lat: true,
                lng: true,
                alternate_name: true
            },
            orderBy: {
                city: "asc"
            }
        });

        if (usCities.length > 0) {
            // Add region_name for unified access
            return usCities.map(city => ({
                ...city,
                region_name: city.state_name
            }));
        }

        // If no US cities found, try Canadian cities (provinces)
        const canadianCities = await prisma.cityCanada.findMany({
            where: {
                province_name: {
                    equals: region,
                    mode: "insensitive"
                }
            },
            select: {
                id: true,
                city: true,
                province_id: true,
                province_name: true,
                lat: true,
                lng: true,
                alternate_name: true
            },
            orderBy: {
                city: "asc"
            }
        });

        // Add region_name for unified access
        return canadianCities.map(city => ({
            ...city,
            region_name: city.province_name
        }));
    } catch (error) {
        console.error("Error fetching cities from database:", error);
        // Fallback to empty array if tables don't exist yet
        return [];
    }
}

export async function getCityByNameAndRegion(cityName: string, region: string): Promise<City | null> {
    try {
        // First try US cities
        const usCity = await prisma.cityUnitedStates.findFirst({
            where: {
                city: {
                    equals: cityName,
                    mode: "insensitive"
                },
                state_name: {
                    equals: region,
                    mode: "insensitive"
                }
            },
            select: {
                id: true,
                city: true,
                state_id: true,
                state_name: true,
                lat: true,
                lng: true,
                alternate_name: true
            }
        });

        if (usCity) {
            return {
                ...usCity,
                region_name: usCity.state_name
            };
        }

        // If no US city found, try Canadian cities (provinces)
        const canadianCity = await prisma.cityCanada.findFirst({
            where: {
                city: {
                    equals: cityName,
                    mode: "insensitive"
                },
                province_name: {
                    equals: region,
                    mode: "insensitive"
                }
            },
            select: {
                id: true,
                city: true,
                province_id: true,
                province_name: true,
                lat: true,
                lng: true,
                alternate_name: true
            }
        });

        if (canadianCity) {
            return {
                ...canadianCity,
                region_name: canadianCity.province_name
            };
        }

        return null;
    } catch (error) {
        console.error("Error fetching city from database:", error);
        // Return null if tables don't exist yet
        return null;
    }
}

export async function searchCitiesByName(searchTerm: string, region?: string): Promise<City[]> {
    try {
        // Search US cities
        const usWhereClause = {
            city: {
                contains: searchTerm,
                mode: "insensitive" as const
            },
            ...(region && {
                state_name: {
                    equals: region,
                    mode: "insensitive" as const
                }
            })
        };

        // Search Canadian cities
        const canadaWhereClause = {
            city: {
                contains: searchTerm,
                mode: "insensitive" as const
            },
            ...(region && {
                province_name: {
                    equals: region,
                    mode: "insensitive" as const
                }
            })
        };

        // Search both US and Canadian cities
        const [usCities, canadianCities] = await Promise.all([
            prisma.cityUnitedStates.findMany({
                where: usWhereClause,
                select: {
                    id: true,
                    city: true,
                    state_id: true,
                    state_name: true,
                    lat: true,
                    lng: true,
                    alternate_name: true
                },
                orderBy: {
                    city: "asc"
                },
                take: 50 // Limit results
            }),
            prisma.cityCanada.findMany({
                where: canadaWhereClause,
                select: {
                    id: true,
                    city: true,
                    province_id: true,
                    province_name: true,
                    lat: true,
                    lng: true,
                    alternate_name: true
                },
                orderBy: {
                    city: "asc"
                },
                take: 50 // Limit results
            })
        ]);

        // Combine results with unified region_name
        const combinedResults = [
            ...usCities.map(city => ({
                ...city,
                region_name: city.state_name
            })),
            ...canadianCities.map(city => ({
                ...city,
                region_name: city.province_name
            }))
        ];

        // Sort combined results
        return combinedResults.sort((a, b) => {
            if (a.city && b.city) {
                return a.city.localeCompare(b.city);
            }
            return 0;
        });
    } catch (error) {
        console.error("Error searching cities in database:", error);
        // Fallback to empty array if tables don't exist yet
        return [];
    }
} 