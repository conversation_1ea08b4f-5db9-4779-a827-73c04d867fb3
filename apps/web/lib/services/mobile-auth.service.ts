import prisma from "@/lib/prisma";
import { generateToken } from "@/lib/utils/token";
import { verify } from "jsonwebtoken";

export class MobileAuthService {
	/**
	 * Validates a JWT token from mobile app and creates a web session token
	 */
	static async createWebSessionFromMobileToken(mobileToken: string) {
		if (!process.env.JWT_SECRET) {
			throw new Error("JWT_SECRET environment variable is not set");
		}

		// Verify the JWT token
		const decoded = verify(mobileToken, process.env.JWT_SECRET) as {
			userId: string;
		};

		if (!decoded.userId) {
			throw new Error("Invalid token format");
		}

		// Find the user
		const user = await prisma.user.findUnique({
			where: { id: decoded.userId }
		});

		if (!user) {
			throw new Error("User not found");
		}

		// Create an auto sign-in token for web session
		const signInToken = generateToken();
		const tokenExpiry = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

		// Create the verification token for auto sign-in
		await prisma.verificationToken.create({
			data: {
				token: signInToken,
				expires: tokenExpiry,
				user_id: user.id,
				type: "auto_signin"
			}
		});

		// Update last login
		await prisma.user.update({
			where: { id: user.id },
			data: { last_login: new Date() }
		});

		return {
			user,
			signInToken,
			redirectUrl: "/dashboard"
		};
	}

	/**
	 * Validates a mobile JWT token and returns the user
	 */
	static async validateMobileToken(mobileToken: string) {
		if (!process.env.JWT_SECRET) {
			throw new Error("JWT_SECRET environment variable is not set");
		}

		const decoded = verify(mobileToken, process.env.JWT_SECRET) as {
			userId: string;
		};

		if (!decoded.userId) {
			throw new Error("Invalid token format");
		}

		const user = await prisma.user.findUnique({
			where: { id: decoded.userId }
		});

		if (!user) {
			throw new Error("User not found");
		}

		return user;
	}
}
