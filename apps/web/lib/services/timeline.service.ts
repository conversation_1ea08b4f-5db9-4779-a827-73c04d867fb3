import prisma from "@/lib/prisma";
import { TimelineEventType } from "@rvhelp/database";

export interface TimelineUpdate {
	id: string;
	event_type: TimelineEventType;
	date: string;
	details?: {
		notes?: string;
		[key: string]: any;
	};
	updated_by: {
		first_name: string | null;
		last_name: string | null;
		email: string;
	};
	warranty_request_id?: string;
	job_id?: string;
}

export interface CreateTimelineUpdateData {
	job_id?: string;
	event_type: TimelineEventType;
	updated_by_id: string;
	notes?: string;
	details?: Record<string, any>;
	warranty_request_id?: string;
	date?: Date;
	listing_id?: string;
}

export interface UpdateTimelineUpdateData {
	notes?: string;
	details?: Record<string, any>;
	listing_id?: string;
}

export interface TimelineFilters {
	event_types?: TimelineEventType[];
	date_from?: string;
	date_to?: string;
	limit?: number;
	offset?: number;
}

class TimelineService {
	/**
	 * Fetch timeline updates for a job or warranty request directly from the database
	 */
	async getTimelineUpdates(
		options: {
			jobId?: string;
			warrantyRequestId?: string;
		} & TimelineFilters
	): Promise<TimelineUpdate[]> {
		const {
			jobId,
			warrantyRequestId,
			event_types,
			date_from,
			date_to,
			limit = 50,
			offset = 0
		} = options;

		if (!jobId && !warrantyRequestId) {
			throw new Error("Either jobId or warrantyRequestId must be provided");
		}

		const where = {
			...(jobId && { job_id: jobId }),
			...(warrantyRequestId && { warranty_request_id: warrantyRequestId }),
			...(event_types?.length && { event_type: { in: event_types } }),
			...(date_from && { date: { gte: new Date(date_from) } }),
			...(date_to && { date: { lte: new Date(date_to) } })
		};

		const updates = await prisma.timelineUpdate.findMany({
			where,
			orderBy: { date: "desc" },
			take: limit,
			skip: offset,
			include: {
				updated_by: {
					select: {
						first_name: true,
						last_name: true,
						email: true
					}
				}
			}
		});

		return updates.map((update) => ({
			id: update.id,
			event_type: update.event_type,
			date: update.date.toISOString(),
			details: update.details as
				| { notes?: string;[key: string]: any }
				| undefined,
			updated_by: update.updated_by,
			warranty_request_id: update.warranty_request_id,
			job_id: update.job_id
		}));
	}

	/**
	 * Create a new timeline update in the database
	 */
	async createTimelineUpdate(
		data: CreateTimelineUpdateData
	): Promise<TimelineUpdate> {
		if (!data.job_id && !data.warranty_request_id) {
			throw new Error("Job ID or warranty request ID must be provided");
		}

		const details = {
			...data.details
		}
		if (data.notes) {
			details.notes = data.notes;
		}
		if (data.listing_id) {
			details.listing_id = data.listing_id;
		}

		const update = await prisma.timelineUpdate.create({
			data: {
				event_type: data.event_type,
				date: data.date || new Date(),
				details: details,
				...(data.job_id && { job_id: data.job_id }),
				...(data.warranty_request_id && {
					warranty_request_id: data.warranty_request_id
				}),
				...(data.updated_by_id && { updated_by_id: data.updated_by_id })
			},
			include: {
				updated_by: {
					select: {
						first_name: true,
						last_name: true,
						email: true
					}
				}
			}
		});

		return {
			id: update.id,
			event_type: update.event_type,
			date: update.date.toISOString(),
			details: update.details as
				| { notes?: string;[key: string]: any }
				| undefined,
			updated_by: update.updated_by,
			warranty_request_id: update.warranty_request_id,
			job_id: update.job_id
		};
	}

	/**
	 * Update an existing timeline update in the database
	 */
	async updateTimelineUpdate(
		id: string,
		data: UpdateTimelineUpdateData
	): Promise<TimelineUpdate> {
		const update = await prisma.timelineUpdate.update({
			where: { id },
			data: {
				details: data.notes
					? { notes: data.notes, ...data.details }
					: data.details
			},
			include: {
				updated_by: {
					select: {
						first_name: true,
						last_name: true,
						email: true
					}
				}
			}
		});

		return {
			id: update.id,
			event_type: update.event_type,
			date: update.date.toISOString(),
			details: update.details as
				| { notes?: string;[key: string]: any }
				| undefined,
			updated_by: update.updated_by,
			warranty_request_id: update.warranty_request_id,
			job_id: update.job_id
		};
	}

	/**
	 * Delete a timeline update from the database
	 */
	async deleteTimelineUpdate(id: string): Promise<void> {
		await prisma.timelineUpdate.delete({
			where: { id }
		});
	}

	/**
	 * Add a common timeline update for job started
	 */
	async addJobStartedUpdate(
		jobId: string,
		notes?: string,
		userId?: string
	): Promise<TimelineUpdate> {
		return this.createTimelineUpdate({
			job_id: jobId,
			event_type: "JOB_STARTED",
			notes: notes || "Job has been started by the service provider",
			updated_by_id: userId
		});
	}

	/**
	 * Add a common timeline update for job completed
	 */
	async addJobCompletedUpdate(
		jobId: string,
		notes?: string,
		completionDetails?: Record<string, any>,
		userId?: string
	): Promise<TimelineUpdate> {
		return this.createTimelineUpdate({
			job_id: jobId,
			event_type: "JOB_COMPLETED",
			notes: notes || "Job has been completed successfully",
			details: completionDetails,
			updated_by_id: userId
		});
	}

	/**
	 * Add a common timeline update for job paused
	 */
	async addJobPausedUpdate(
		jobId: string,
		reason?: string,
		userId?: string
	): Promise<TimelineUpdate> {
		return this.createTimelineUpdate({
			job_id: jobId,
			event_type: "JOB_PAUSED",
			notes: reason || "Job has been paused",
			updated_by_id: userId
		});
	}

	/**
	 * Add a common timeline update for invoice created
	 */
	async addInvoiceCreatedUpdate(
		jobId: string,
		userId?: string
	): Promise<TimelineUpdate> {
		return this.createTimelineUpdate({
			job_id: jobId,
			event_type: "INVOICE_CREATED",
			notes: "Invoice has been created for the completed work",
			updated_by_id: userId
		});
	}

	/**
	 * Add a common timeline update for invoice paid
	 */
	async addInvoicePaidUpdate(
		jobId: string,
		amount?: number,
		userId?: string
	): Promise<TimelineUpdate> {
		return this.createTimelineUpdate({
			job_id: jobId,
			event_type: "INVOICE_PAID",
			notes: `Invoice has been paid${amount ? ` for $${amount}` : ""}`,
			details: { amount },
			updated_by_id: userId
		});
	}

	/**
	 * Get timeline updates for multiple jobs
	 */
	async getTimelineUpdatesForJobs(
		jobIds: string[]
	): Promise<Record<string, TimelineUpdate[]>> {
		const updates = await prisma.timelineUpdate.findMany({
			where: {
				job_id: { in: jobIds }
			},
			orderBy: { date: "desc" },
			include: {
				updated_by: {
					select: {
						first_name: true,
						last_name: true,
						email: true
					}
				}
			}
		});

		return jobIds.reduce(
			(acc, jobId) => {
				acc[jobId] = updates
					.filter((update) => update.job_id === jobId)
					.map((update) => ({
						id: update.id,
						event_type: update.event_type,
						date: update.date.toISOString(),
						details: update.details as
							| { notes?: string;[key: string]: any }
							| undefined,
						updated_by: update.updated_by,
						warranty_request_id: update.warranty_request_id,
						job_id: update.job_id
					}));
				return acc;
			},
			{} as Record<string, TimelineUpdate[]>
		);
	}

	/**
	 * Get the latest timeline update for a job or warranty request
	 */
	async getLatestTimelineUpdate(options: {
		jobId?: string;
		warrantyRequestId?: string;
	}): Promise<TimelineUpdate | null> {
		const update = await prisma.timelineUpdate.findFirst({
			where: {
				...(options.jobId && { job_id: options.jobId }),
				...(options.warrantyRequestId && {
					warranty_request_id: options.warrantyRequestId
				})
			},
			orderBy: { date: "desc" },
			include: {
				updated_by: {
					select: {
						first_name: true,
						last_name: true,
						email: true
					}
				}
			}
		});

		if (!update) return null;

		return {
			id: update.id,
			event_type: update.event_type,
			date: update.date.toISOString(),
			details: update.details as
				| { notes?: string;[key: string]: any }
				| undefined,
			updated_by: update.updated_by,
			warranty_request_id: update.warranty_request_id,
			job_id: update.job_id
		};
	}

	/**
	 * Check if a specific event type exists in the timeline
	 */
	async hasEventType(
		eventType: TimelineEventType,
		options: {
			jobId?: string;
			warrantyRequestId?: string;
		}
	): Promise<boolean> {
		const count = await prisma.timelineUpdate.count({
			where: {
				event_type: eventType,
				...(options.jobId && { job_id: options.jobId }),
				...(options.warrantyRequestId && {
					warranty_request_id: options.warrantyRequestId
				})
			}
		});
		return count > 0;
	}
}

// Export a singleton instance
export const timelineService = new TimelineService();
export default timelineService;
