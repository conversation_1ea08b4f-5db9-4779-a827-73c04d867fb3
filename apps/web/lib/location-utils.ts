import { Location } from "@rvhelp/database";
import { ListingWithLocation } from "../types/global";

export function setActiveListingLocation(listing: ListingWithLocation): Location | null {
    const now = new Date();
    if (listing?.locations && listing?.locations.length > 0) {
        if (listing.locations.length === 1) {
            listing.location = listing.locations[0];
        }
        else {
            const result = listing.locations.find(loc =>
                loc.start_date && loc.end_date &&
                loc.start_date <= now && loc.end_date >= now
            );
            if (result) {
                listing.location = result;
            }
            if (!result) {
                listing.location = listing.locations.find(loc => loc.default === true);
            }
            if (!result && listing.locations.length > 0) {
                listing.location = listing.locations[0];
            }
        }
        return listing.location || null;
    }
    return null;
}