/**
 * SEO URL utilities for location formatting
 */

import { states } from "./states";

/**
 * Convert a location string to URL-safe parameter
 * Example: "Dallas, TX" -> "dallas-tx"
 */
export function locationToUrlParam(location: string): string {
    return location
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special chars except spaces and hyphens
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Convert URL parameter back to display location
 * Example: "dallas-tx" -> "Dallas, TX"
 */
export function urlParamToLocation(param: string): string {
    // Split by hyphen and capitalize each word
    const parts = param.split('-');

    // Handle state abbreviations (usually last part if 2 letters)
    const lastPart = parts[parts.length - 1];
    if (lastPart && lastPart.length === 2) {
        // Last part is likely a state abbreviation
        const statePart = lastPart.toUpperCase();
        const cityParts = parts.slice(0, -1).map(capitalize);
        return `${cityParts.join(' ')}, ${statePart}`;
    }

    // Otherwise, just capitalize all parts
    return parts.map(capitalize).join(' ');
}

/**
 * Convert state abbreviation to full name for URL routing
 * Example: "TX" -> "texas", "CA" -> "california"
 */
export function stateAbbreviationToUrlParam(abbreviation: string): string | null {
    const state = states.find(s => s.abbreviation.toUpperCase() === abbreviation.toUpperCase());
    return state ? state.name.toLowerCase()
        .replace(/\n+/g, ' ') : null; // Convert newlines to spaces
}

/**
 * Convert full state name to URL parameter
 * Example: "North Carolina" -> "north-carolina"
 */
export function stateNameToUrlParam(stateName: string): string {
    return stateName.toLowerCase().replace(/\s+/g, '-');
}

/**
 * Validate if a location parameter has valid format
 */
export function isValidLocationParam(param: string): boolean {
    if (!param || typeof param !== 'string') return false;

    // Basic validation: should be lowercase letters, numbers, and hyphens only
    return /^[a-z0-9-]+$/.test(param) &&
        !param.startsWith('-') &&
        !param.endsWith('-') &&
        !param.includes('--');
}

function capitalize(word: string): string {
    return word.charAt(0).toUpperCase() + word.slice(1);
} 