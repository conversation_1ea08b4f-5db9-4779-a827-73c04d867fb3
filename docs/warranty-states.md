# Mobile RV Repair Warranty Process - State Documentation

## Overview

This document outlines the complete state machine for the mobile RV repair warranty process, tracking warranty requests from initial creation through completion and payment.

## State Definitions

### CREATED

**Initial State**

- Customer has contacted OEM support
- Support rep determined work is suitable for mobile repair pilot program
- `WarrantyRequest` created in system
- OEM provides pre-approved hours for diagnostics/repair
- Customer receives email with RVHelp login link

**Allowed Transitions:**

- → `CANCELLED` (warranty no longer viable)
- → `REGISTERED` (customer completes registration)

### REGISTERED

**Customer Onboarded**

- Customer followed email link and logged in/created account
- `Job` created to track the `WarrantyRequest`
- Ready for provider selection

**Allowed Transitions:**

- → `CANCELLED` (warranty cancelled)
- → `REQUESTED` (customer invites providers)

### REQUESTED

**Provider Invitation Phase**

- Customer has invited up to 5 service providers
- Providers receive preliminary manufacturer information
- Providers have customer contact information
- Awaiting provider responses

**Allowed Transitions:**

- → `CANCELLED` (warranty cancelled)
- → `ACCEPTED` (at least one provider accepts)

### ACCEPTED

**Provider Response Received**

- One or more providers expressed willingness to complete work
- Customer has final decision on provider selection
- No commitment yet finalized

**Allowed Transitions:**

- → `CANCELLED` (warranty cancelled)
- → `REQUESTED` (customer rejects all providers, invites new ones)
- → `STARTED` (customer selects a provider)

### STARTED

**Work Initiated**

- Customer agreed to work with selected provider
- Appointment scheduled or in process
- Provider has not yet been on-site

**Allowed Transitions:**

- → `CANCELLED` (only if provider hasn't been on-site)
- → `WITHDRAWN` (mutual decision to discontinue)
- → `PENDING` (provider completes initial work)

### WITHDRAWN

**Voluntary Discontinuation**

- Customer or provider no longer wishes to continue
- No on-site work performed
- No payment obligations

**Allowed Transitions:**

- → `CANCELLED` (warranty cancelled)
- → `REQUESTED` (restart provider selection)

### PENDING

**Authorization Under Review**

- Provider completed on-site diagnostics
- Work either completed within pre-approved hours OR estimate submitted
- Required documentation submitted (cause, correction, diagnostic forms)
- Provider eligible for payment of pre-approved hours
- Awaiting OEM approval for additional work/hours

**Allowed Transitions:**

- → `REJECTED` (OEM rejects authorization)
- → `FEEDBACK` (OEM requests additional information)
- → `APPROVED` (OEM approves estimate)

### FEEDBACK

**Additional Information Required**

- OEM reviewed diagnostics and estimate
- More information needed before approval
- Provider must respond to continue

**Allowed Transitions:**

- → `PENDING` (provider submits required information)
- → `INVOICE` (provider cuts losses, invoices for completed work)

### REJECTED

**Authorization Denied**

- OEM rejected latest authorization request
- Provider can revise and resubmit or proceed to invoice

**Allowed Transitions:**

- → `PENDING` (provider resubmits with changes)
- → `INVOICE` (provider invoices for approved work only)

### APPROVED

**Work Authorization Granted**

- Provider's estimate approved by OEM
- Cleared to complete described work
- Can proceed with repairs

**Allowed Transitions:**

- → `PENDING` (additional work needed, new authorization required)
- → `INVOICE` (approved work completed)

### INVOICE

**Payment Processing**

- Provider submitted detailed invoice
- Covers diagnosis and/or repair work performed
- Awaiting OEM payment

**Allowed Transitions:**

- → `PAID` (OEM processes payment)

### PAID

**Payment Completed**

- OEM has paid provider for work performed
- Financial obligations fulfilled

**Allowed Transitions:**

- → `COMPLETED` (process finalization)

### COMPLETED

**Final State**

- Work completed and paid
- Process closed
- No further transitions allowed

### CANCELLED

**Terminal State**

- Warranty service no longer viable
- Various reasons: customer self-repair, dealer visit, etc.
- No provider payment required
- No further transitions allowed

---

## State Flow Diagrams

### Complete Process Flow

```mermaid
stateDiagram-v2
    [*] --> CREATED: OEM creates warranty request

    CREATED --> CANCELLED: Service no longer viable
    CREATED --> REGISTERED: Customer completes registration

    REGISTERED --> CANCELLED: Service cancelled
    REGISTERED --> REQUESTED: Customer invites providers

    REQUESTED --> CANCELLED: Service cancelled
    REQUESTED --> ACCEPTED: Provider(s) accept work

    ACCEPTED --> CANCELLED: Service cancelled
    ACCEPTED --> REQUESTED: Customer rejects all providers
    ACCEPTED --> STARTED: Customer selects provider

    STARTED --> CANCELLED: Service cancelled (pre on-site)
    STARTED --> WITHDRAWN: Mutual discontinuation
    STARTED --> PENDING: Provider completes initial work

    WITHDRAWN --> CANCELLED: Service cancelled
    WITHDRAWN --> REQUESTED: Restart provider selection

    PENDING --> REJECTED: OEM rejects authorization
    PENDING --> FEEDBACK: OEM requests more info
    PENDING --> APPROVED: OEM approves work

    FEEDBACK --> PENDING: Provider submits info
    FEEDBACK --> INVOICE: Provider cuts losses

    REJECTED --> PENDING: Provider resubmits
    REJECTED --> INVOICE: Provider invoices approved work

    APPROVED --> PENDING: Additional work needed
    APPROVED --> INVOICE: Work completed

    INVOICE --> PAID: OEM processes payment

    PAID --> COMPLETED: Process finalized

    CANCELLED --> [*]
    COMPLETED --> [*]
```

### Authorization Cycle Detail

```mermaid
stateDiagram-v2
    [*] --> PENDING: Provider submits work/estimate

    PENDING --> APPROVED: OEM approves
    PENDING --> REJECTED: OEM rejects
    PENDING --> FEEDBACK: More info needed

    FEEDBACK --> PENDING: Info provided
    FEEDBACK --> INVOICE: Provider exits cycle

    REJECTED --> PENDING: Revised submission
    REJECTED --> INVOICE: Provider exits cycle

    APPROVED --> PENDING: More work needed
    APPROVED --> INVOICE: Work complete

    INVOICE --> [*]: Move to payment flow
```

## Key Decision Points

1. **Provider Selection** (ACCEPTED → STARTED): Customer chooses from accepted providers
2. **Authorization Approval** (PENDING → APPROVED/REJECTED/FEEDBACK): OEM reviews work estimates
3. **Work Continuation** (APPROVED → PENDING/INVOICE): Provider decides if more work needed
4. **Provider Exit Points** (FEEDBACK/REJECTED → INVOICE): Provider can exit authorization cycle

## Business Rules

- Providers are eligible for pre-approved hours payment once on-site work begins
- Cancellation without provider payment only allowed before on-site visit
- Authorization cycle can repeat multiple times for complex repairs
- WITHDRAWN state allows clean restart of provider selection process
- All financial obligations must be resolved before reaching COMPLETED state
