# Mobile-to-Web Authentication

This document explains how to implement mobile-to-web authentication, allowing users logged into the mobile app to access logged-in functionality on the web app from a web view.

## Overview

The mobile-to-web authentication system allows seamless authentication between your mobile app and web app. When a user is logged into the mobile app, they can open a web view and automatically be authenticated on the web app without needing to log in again.

## How It Works

1. **Mobile App**: User is logged in and has a valid JWT token
2. **Web View**: Mobile app opens a web view with the JWT token as a URL parameter
3. **Web App**: Automatically detects the token and creates a web session
4. **Result**: User is now logged into the web app and can access protected functionality

## API Endpoint

### POST `/api/auth/mobile/mobile-to-web-session`

Validates a JWT token from the mobile app and creates a web session for the user.

#### Request Body

```json
{
	"token": "your-jwt-token-from-mobile-app"
}
```

#### Response

```json
{
	"success": true,
	"message": "Web session created successfully",
	"email": "<EMAIL>",
	"signInToken": "auto-sign-in-token",
	"redirectUrl": "/dashboard"
}
```

#### Error Responses

- `401`: Invalid or expired token
- `404`: User not found
- `500`: Internal server error

## Implementation

### 1. Mobile App Integration

In your mobile app, when opening a web view, append the JWT token as a URL parameter:

```javascript
// Example: React Native WebView
const webViewUrl = `https://your-web-app.com/dashboard?mobileToken=${userJwtToken}`;

<WebView source={{ uri: webViewUrl }} />;
```

### 2. Web App Integration

#### Option A: Automatic Authentication (Recommended)

Wrap your app or specific pages with the `MobileAuthHandler` component:

```tsx
import { MobileAuthHandler } from "@/components/MobileAuthHandler";

export default function DashboardPage() {
	return (
		<MobileAuthHandler
			onAuthSuccess={() => console.log("Authenticated from mobile")}
			onAuthFailure={(error) => console.error("Auth failed:", error)}
		>
			<DashboardContent />
		</MobileAuthHandler>
	);
}
```

#### Option B: Manual Authentication

Use the `useMobileAuth` hook for more control:

```tsx
import { useMobileAuth } from "@/hooks/useMobileAuth";

export default function DashboardPage() {
	const { authenticateFromUrl, loading } = useMobileAuth();

	useEffect(() => {
		authenticateFromUrl();
	}, []);

	if (loading) {
		return <div>Authenticating...</div>;
	}

	return <DashboardContent />;
}
```

### 3. Layout Integration

For app-wide mobile authentication, add the handler to your root layout:

```tsx
// app/layout.tsx
import { MobileAuthHandler } from "@/components/MobileAuthHandler";

export default function RootLayout({
	children
}: {
	children: React.ReactNode;
}) {
	return (
		<html lang="en">
			<body>
				<MobileAuthHandler>{children}</MobileAuthHandler>
			</body>
		</html>
	);
}
```

## Security Considerations

1. **Token Expiration**: JWT tokens have a 30-day expiration by default
2. **Auto Sign-in Tokens**: Web session tokens expire after 5 minutes
3. **HTTPS**: Always use HTTPS in production to secure token transmission
4. **Token Validation**: The system validates tokens server-side before creating sessions

## Environment Variables

Ensure these environment variables are set:

```bash
JWT_SECRET=your-jwt-secret-key
NEXTAUTH_SECRET=your-nextauth-secret
```

## Error Handling

The system handles various error scenarios:

- **Invalid Token**: Returns 401 with appropriate error message
- **Expired Token**: Returns 401 with "Invalid or expired token"
- **User Not Found**: Returns 404 if the user doesn't exist
- **Network Errors**: Graceful fallback with user-friendly messages

## Testing

### Test the API Endpoint

```bash
curl -X POST http://localhost:3000/api/auth/mobile/mobile-to-web-session \
  -H "Content-Type: application/json" \
  -d '{"token": "your-test-jwt-token"}'
```

### Test Web View Integration

1. Get a valid JWT token from mobile login
2. Open: `http://localhost:3000/dashboard?mobileToken=your-token`
3. Should automatically authenticate and redirect to dashboard

## Troubleshooting

### Common Issues

1. **Token Not Found**: Ensure the mobile app is passing the token correctly
2. **Authentication Fails**: Check that the JWT token is valid and not expired
3. **User Not Found**: Verify the user exists in the database
4. **CORS Issues**: Ensure proper CORS configuration for cross-origin requests

### Debug Mode

Enable debug logging by checking the browser console and server logs for detailed error messages.

## Mobile App Examples

### React Native

```javascript
import { WebView } from "react-native-webview";

const DashboardWebView = ({ userToken }) => {
	const webViewUrl = `https://your-web-app.com/dashboard?mobileToken=${userToken}`;

	return <WebView source={{ uri: webViewUrl }} style={{ flex: 1 }} />;
};
```

### Flutter

```dart
import 'package:webview_flutter/webview_flutter.dart';

class DashboardWebView extends StatelessWidget {
  final String userToken;

  @override
  Widget build(BuildContext context) {
    final webViewUrl = 'https://your-web-app.com/dashboard?mobileToken=$userToken';

    return WebView(
      initialUrl: webViewUrl,
    );
  }
}
```

### iOS (WKWebView)

```swift
import WebKit

class DashboardWebViewController: UIViewController {
    var webView: WKWebView!
    var userToken: String!

    override func viewDidLoad() {
        super.viewDidLoad()

        let webViewUrl = "https://your-web-app.com/dashboard?mobileToken=\(userToken!)"
        let url = URL(string: webViewUrl)!
        let request = URLRequest(url: url)

        webView.load(request)
    }
}
```

### Android (WebView)

```kotlin
import android.webkit.WebView

class DashboardActivity : AppCompatActivity() {
    private lateinit var webView: WebView
    private var userToken: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        webView = WebView(this)
        setContentView(webView)

        val webViewUrl = "https://your-web-app.com/dashboard?mobileToken=$userToken"
        webView.loadUrl(webViewUrl)
    }
}
```
