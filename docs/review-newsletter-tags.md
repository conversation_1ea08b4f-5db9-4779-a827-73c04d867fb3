# Review Newsletter Tags

This document describes the newsletter tags that are automatically added when users leave reviews.

## Tags Added

When a user leaves a review that gets approved (status becomes "active"), the following tags are automatically added to their newsletter subscription:

1. **`consumer action: reviewed provider`** - Added to all users who leave an approved review
2. **`consumer action: five star review`** - Added only to users who leave a 5-star review

## Implementation

The tags are added in two places:

1. **Review Moderation** (`/api/reviews/[id]/moderate`) - When an admin changes a review status to "active"
2. **Review Approval** (`/api/admin/reviews/[id]/approve`) - When an admin approves a review

## Adding Tags to Existing Subscribers

To add these tags to existing subscribers who have already left reviews, run one of the following commands:

### JavaScript Version
```bash
pnpm add-review-tags
```

### TypeScript Version (Recommended)
```bash
pnpm add-review-tags:ts
```

## Script Details

The script:
- Finds all active reviews with email, first name, and last name
- Processes them in reverse chronological order (newest first)
- Adds appropriate tags based on review rating
- Includes error handling and progress logging
- Adds a 100ms delay between API calls to avoid overwhelming the service

## Example Output

```
Starting to add review tags to existing subscribers...
Found 150 active reviews to process
Processing review abc123 for <EMAIL> with tags: consumer action: reviewed provider, consumer action: five star review
✓ Successfully processed review abc123
Processing review def456 for <EMAIL> with tags: consumer action: reviewed provider
✓ Successfully processed review def456

=== Summary ===
Total reviews processed: 150
Errors: 0
Review tags have been added to existing subscribers.
```

## Error Handling

- If the newsletter sync fails for a specific review, the error is logged but the script continues processing other reviews
- The script will not fail the review approval/moderation process if newsletter sync fails
- All errors are logged to the console for debugging

## Notes

- Tags are added using the `append_tags: true` flag, so existing tags are preserved
- The script only processes reviews with complete user information (email, first name, last name)
- Reviews without user information are skipped
- The script respects the development environment check in `EmailNewsletterService` 