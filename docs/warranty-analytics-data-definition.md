# Warranty Analytics Reports - Data Definition

This document defines the data sources, calculations, and metrics used in the RV Help Portal Analytics Reports page for ADMIN users.

## Overview

The analytics dashboard provides comprehensive insights into warranty job performance, financial metrics, and operational efficiency. The dashboard is only accessible to users with the `ADMIN` role and displays data specific to their company's warranty requests.

## Data Sources

### Primary Data Source

- **Table**: `warranty_requests`
- **Filter**: `company_id = user.company_id`
- **Includes**: Related data from `components`, `listings`, `customers`, and `timeline_updates`

### Secondary Data Sources

- **Components**: `components` table for parts analysis
- **Timeline Updates**: `timeline_updates` table for status change tracking and time calculations
- **Users**: `users` table for customer information

### Timeline Events Used

- **PREAUTHORIZATION_APPROVED**: When a warranty request is created and preauthorization is approved
- **CUSTOMER_REGISTERED**: When a customer completes registration
- **TECHNICIAN_INVITED**: When a technician is first invited to a warranty job
- **TECHNICIAN_ACCEPTED**: When a technician accepts the warranty job
- **INVOICE_PAID**: When the warranty job is completed and paid

## Metrics Definitions

### 1. Performance Metrics

#### Total Jobs This Month

- **Calculation**: Count of warranty requests created in current month
- **Filter**: `created_at >= startOfMonth`
- **Display**: Number with goal percentage (target: 1,000 jobs)

#### Platform Revenue This Month

- **Calculation**: `completedJobsThisMonth × $50`
- **Assumption**: $50 platform fee per completed job
- **Filter**: `status = 'INVOICE_PAID' AND updated_at >= startOfMonth`

#### All-Time Platform Revenue

- **Calculation**: `totalCompletedJobs × $50`
- **Filter**: `status = 'INVOICE_PAID'`

#### Average Time to Acceptance

- **Calculation**: Average time from first TECHNICIAN_INVITED timeline event to TECHNICIAN_ACCEPTED timeline event
- **Formula**: `AVG(accepted_event.date - invited_event.date)`
- **Unit**: Hours
- **Filter**: Requests with both TECHNICIAN_INVITED and TECHNICIAN_ACCEPTED timeline events
- **Logic**: Finds the first TECHNICIAN_INVITED event and the first TECHNICIAN_ACCEPTED event that occurs after it

#### Average Repair Cycle Time (RECT)

- **Calculation**: Average time from PREAUTHORIZATION_APPROVED to INVOICE_PAID timeline events
- **Formula**: `AVG(invoice_paid_event.date - preauth_approved_event.date)`
- **Unit**: Days
- **Filter**: Requests with both PREAUTHORIZATION_APPROVED and INVOICE_PAID timeline events
- **Logic**: Finds the first PREAUTHORIZATION_APPROVED event and the first INVOICE_PAID event that occurs after it
- **Note**: PREAUTHORIZATION_APPROVED events are created when warranty requests are created, using the request's created_at date

#### Match Success Rate

- **Calculation**: `(requestsWith TECHNICIAN_ACCEPTED / totalRequests) × 100`
- **Filter**: Requests with `TECHNICIAN_ACCEPTED` timeline event
- **Goal**: 90%+
- **Logic**: Counts requests that have reached the technician acceptance stage

#### Average Registration Time

- **Calculation**: Average time from PREAUTHORIZATION_APPROVED to CUSTOMER_REGISTERED timeline events
- **Formula**: `AVG(customer_registered_event.date - preauth_approved_event.date)`
- **Unit**: Hours
- **Filter**: Requests with both PREAUTHORIZATION_APPROVED and CUSTOMER_REGISTERED timeline events
- **Logic**: Finds the first PREAUTHORIZATION_APPROVED event and the first CUSTOMER_REGISTERED event that occurs after it

#### No Match Cases This Week

- **Calculation**: Count of failed/cancelled requests in last 7 days
- **Filter**: `status IN ('REQUEST_REJECTED', 'JOB_CANCELLED', 'AUTHORIZATION_REJECTED') AND updated_at >= 7 days ago`

### 2. OEM Health Metrics

> **Note**: These metrics are currently removed from the dashboard as they were using hardcoded example data. Future implementation should calculate actual values from invoice and job data.

#### Average Service Call Fee

- **Status**: Removed from dashboard
- **TODO**: Calculate from actual invoice data

#### Average Hourly Rate

- **Status**: Removed from dashboard
- **TODO**: Calculate from actual invoice data

#### Average Hours Per Job

- **Status**: Removed from dashboard
- **TODO**: Calculate from actual job completion data

#### Average Total Per Job

- **Status**: Removed from dashboard
- **TODO**: Calculate from actual invoice totals

### 3. Financial Summary

#### Tech Payouts This Month

- **Calculation**: `completedJobsThisMonth × $343`
- **Assumption**: Average payout of $343 per job
- **Filter**: `status = 'INVOICE_PAID' AND updated_at >= startOfMonth`

### 4. Pipeline Statistics

#### Request Status Counts

- **Request Created**: `status = 'REQUEST_CREATED'`
- **Customer Registered**: `status = 'CUSTOMER_REGISTERED'`
- **Job Accepted**: `status = 'TECHNICIAN_ACCEPTED'`
- **Parts Ordered**: `status = 'PARTS_ORDERED'`
- **Invoiced**: `status = 'INVOICE_CREATED'`
- **Paid Out**: `status = 'INVOICE_PAID'`

#### Pipeline Times

> **Note**: Pipeline times are currently removed from the dashboard as they were using hardcoded example data. Future implementation should calculate actual values from timeline events.

### 5. No Match Analysis

#### Data Points

- **Job ID**: Last 4 characters of warranty request ID
- **Customer**: `first_name + last_name`
- **Location**: Parsed from `location` JSON field
- **Time in System**: `updated_at - created_at` in days
- **Reason**: Current status (for categorization)
- **Date**: `updated_at` formatted as date
- **Status**: Display badge

#### Reason Categories

- No technicians in 100-mile radius
- All techs declined
- Specialized equipment required
- Customer cancelled
- Parts unavailable

### 6. Top Parts Analysis

#### Data Source

- **Query**: Group by `component_id` where `status = 'PARTS_ORDERED'`
- **Join**: `components` table for type and manufacturer
- **Display**: Component name and count
- **Limit**: Top 10 components

## Access

**Page**: `/reports`
**Access**: ADMIN users only
**Navigation**: Available via dashboard "Reports" quick action (for admins only)

## API Endpoint

### Route

`GET /api/dashboard/analytics`

### Authentication

- Requires valid session
- User must have `ADMIN` role
- User must be associated with a company

### Response Structure

```typescript
{
	performanceMetrics: {
		totalJobsThisMonth: number;
		platformRevenueThisMonth: number;
		allTimePlatformRevenue: number;
		avgTimeToAcceptance: number;
		avgRepairCycleTime: number;
		matchSuccessRate: number;
		avgRegistrationTime: number;
		noMatchCasesThisWeek: number;
	}
	oemHealthMetrics: {
		avgServiceCallFee: number;
		avgHourlyRate: number;
		avgHoursPerJob: number;
		avgTotalPerJob: number;
	}
	financialSummary: {
		techPayoutsThisMonth: number;
	}
	pipeline: {
		stats: {
			requestCreated: number;
			customerRegistered: number;
			jobAccepted: number;
			partsOrdered: number;
			invoiced: number;
			paidOut: number;
		}
		times: {
			requestToRegistration: number;
			registrationToAcceptance: number;
			acceptanceToCompletion: number;
			partsWaitTime: number;
			invoiceToPayment: number;
		}
	}
	noMatchAnalysis: Array<{
		id: string;
		customer: string;
		location: string;
		timeInSystem: string;
		reason: string;
		date: string;
		status: string;
	}>;
	topParts: Array<{
		name: string;
		count: number;
	}>;
	trends: {
		monthlyGrowth: string;
		revenueGrowth: string;
		noMatchTrend: string;
	}
}
```

## Data Assumptions

### Financial Assumptions

- Platform fee: $50 per completed job
- Average tech payout: $343 per job
- Service call fee: $125 (removed from dashboard)
- Hourly rate: $68/hr (removed from dashboard)
- Hours per job: 3.2h (removed from dashboard)

### Time Calculations

- All time calculations use JavaScript Date objects
- Time differences are calculated in milliseconds and converted to appropriate units
- RECT (Repair Event Cycle Time): Converted to days
- Average Registration Time: Converted to hours
- Average Time to Acceptance: Converted to hours
- Pipeline times are currently removed from the dashboard and should be replaced with actual calculations

### Status Mappings

- Completed jobs: `INVOICE_PAID`
- Failed jobs: `REQUEST_REJECTED`, `JOB_CANCELLED`, `AUTHORIZATION_REJECTED`
- Pending jobs: `AUTHORIZATION_REQUESTED`, `INVOICE_CREATED`

## Future Enhancements

### Real-time Data

- Implement real-time updates using WebSocket connections
- Add data refresh intervals for live metrics

### Advanced Analytics

- Add trend analysis with historical data
- Implement predictive analytics for job completion times
- Add geographic analysis for service coverage

### Customization

- Allow admins to customize metric thresholds
- Add configurable date ranges for analysis
- Implement export functionality for reports

### Performance Optimization

- Add database indexing for frequently queried fields
- Implement caching for expensive calculations
- Add pagination for large datasets

### Data Integrity

- Implement proper timeline event creation for all warranty request status changes
- Add validation to ensure timeline events are created consistently
- Create automated data quality checks for analytics accuracy

## Security Considerations

### Data Access

- All data is filtered by `company_id` to ensure data isolation
- Only users with `ADMIN` role can access analytics
- Session validation is required for all requests

### Data Privacy

- Customer information is anonymized in analytics
- Sensitive data is not exposed in API responses
- Audit logging for analytics access

## Error Handling

### API Errors

- 401: Unauthorized (no valid session)
- 403: Forbidden (not admin role)
- 400: Bad Request (no company association)
- 500: Internal Server Error (database/calculation errors)

### Data Validation

- Null checks for optional fields
- Date validation for time calculations
- Currency formatting for financial data
- Status validation for filtering

## Maintenance

### Data Cleanup

- Regular cleanup of old timeline updates
- Archive completed warranty requests
- Optimize database queries for performance

### Monitoring

- Monitor API response times
- Track analytics usage patterns
- Alert on data anomalies
- Regular backup of analytics data

### Migration Scripts

- **SQL Migration**: `packages/database/migrations/add-preauth-timeline-events.sql`
- **Purpose**: Backfill PREAUTHORIZATION_APPROVED timeline events for existing warranty requests
- **Usage**: Run in production to ensure all existing warranty requests have proper timeline events for analytics
- **Safety**: Script only adds events for requests that don't already have them
