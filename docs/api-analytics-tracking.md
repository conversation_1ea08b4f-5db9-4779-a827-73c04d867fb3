# API-Based Google Analytics Tracking Implementation

## Overview

This document outlines the implementation of Google Analytics tracking for RVHelp's Google Ads campaign using direct service integration. Critical business events (lead forms, account creation, provider contacts) are tracked directly in their respective services, while frontend components use client-side tracking. This ensures reliable tracking for both web and mobile applications, capturing actual successful actions rather than just client-side clicks.

## Architecture

### 1. Server-Side Google Analytics Service

**Location**: `lib/services/google-analytics.service.ts`

**Purpose**: Direct server-side tracking for critical business events using Google Analytics Measurement Protocol.

**New Methods**:

- `trackServerEvent()` - Generic server-side event tracking
- `trackServerLeadFormSubmission()` - Lead form submissions
- `trackServerProviderContact()` - Provider contact clicks (phone/email)
- `trackServerAccountCreation()` - Account creation events

**Features**:

- Uses Google Analytics Measurement Protocol for server-side tracking
- Requires `GA_API_SECRET` environment variable
- Handles both authenticated and anonymous users
- Includes comprehensive event parameters

### 2. Client-Side Analytics Hook

**Location**: `lib/hooks/useAnalytics.ts`

**Purpose**: React hook for frontend components to call the analytics API

**Methods**:

- `trackEvent()` - Generic event tracking
- `trackLeadFormSubmission()` - Lead form submissions
- `trackProviderContact()` - Provider contact clicks
- `trackAccountCreation()` - Account creation

## Implementation Details

### Queued Analytics Tracking

All critical business events are now queued for asynchronous processing to avoid impacting response times. The analytics tracking is handled by a dedicated worker that processes events in the background.

#### Queue Worker

- **Location**: `lib/queue/workers/analytics-tracking.ts`
- **Purpose**: Processes analytics tracking events asynchronously
- **Types**: `lead-form-submission`, `provider-contact`, `account-creation`
- **Error Handling**: Graceful error handling without queue retries

#### 1. Lead Form Submissions

- **Location**: `lib/services/job.service.ts`
- **Trigger**: After successful job creation
- **Method**: Queued via `queueMessage({ type: "analytics-tracking", ... })`
- **Data**: Listing ID, listing name, category, user context

#### 2. Provider Contact Clicks

- **Location**: `app/api/service-requests/phone/route.ts`
- **Trigger**: After successful phone lead creation
- **Method**: Queued via `queueMessage({ type: "analytics-tracking", ... })`
- **Data**: Listing ID, listing name, contact type, contact value

#### 3. Account Creation

- **Location**: `lib/services/auth.service.ts`
- **Trigger**: After successful user registration
- **Method**: Queued via `queueMessage({ type: "analytics-tracking", ... })`
- **Data**: User ID, email, source, referrer

### Frontend Component Updates

#### Updated Components:

1. **SendMessageModal** - Lead form tracking removed (now handled server-side)
2. **PhoneNumberDisplay** - Uses `useAnalytics` hook for phone contact tracking
3. **CompanyDetailsSection** - Uses `useAnalytics` hook for email contact tracking

#### Migration from Old System:

- Replaced `useGoogleAnalytics` hook with `useAnalytics`
- Removed direct Google Analytics service calls from frontend components
- Lead form submissions tracked server-side only (more reliable)
- Provider contacts still tracked client-side for immediate feedback

## Environment Variables Required

```env
# Google Analytics API Secret for server-side tracking
GA_API_SECRET=your_ga_api_secret_here
```

## Production-Only Tracking

**Important**: Google Analytics tracking is **only active in production** to prevent development/test data from polluting your analytics.

### When Tracking is Active:

- `config.isDevelopment` is `false` AND
- Hostname is `rvhelp.com`

### When Tracking is Skipped:

- Development environment (`config.isDevelopment` is `true`)
- Non-production hostnames (localhost, staging domains, etc.)
- Test environments

### Benefits:

- **Clean Analytics Data**: No development/test events in production reports
- **Accurate Conversion Tracking**: Only real user interactions are tracked
- **Privacy Compliance**: No tracking on development/staging environments

### Implementation:

- **Client-side**: `GoogleAnalyticsService.shouldTrack()` checks environment and hostname
- **Server-side**: Same checks applied to all server-side tracking methods
- **Component**: `GoogleAnalytics` component only renders in production
- **Logging**: Skipped events are logged for debugging purposes

## Testing

### Test Coverage

- **Location**: `tests/unit/services/google-analytics.service.test.ts`
- **Coverage**: 10 test cases covering all scenarios
- **Features**:
  - Client-side tracking (lead forms, provider contacts, account creation)
  - Server-side tracking (all methods using Google Analytics Measurement Protocol)
  - Error handling (missing API secret, network errors)
  - Google Ads conversion tracking
  - Both client-side and server-side event validation

### Running Tests

```bash
npm run jest tests/unit/services/google-analytics.service.test.ts
```

## Usage Examples

### Frontend Usage

```typescript
import { useAnalytics } from "@/lib/hooks/useAnalytics";

function MyComponent() {
	const { trackProviderContact } = useAnalytics();

	const handlePhoneClick = () => {
		trackProviderContact({
			listingId: "listing123",
			listingName: "Provider Name",
			contactType: "phone",
			contactValue: "+**********"
		});
	};
}
```

### Client-Side Usage

```typescript
// For frontend components (provider contacts only)
import { useAnalytics } from "@/lib/hooks/useAnalytics";

function MyComponent() {
	const { trackProviderContact } = useAnalytics();

	const handlePhoneClick = () => {
		trackProviderContact({
			listingId: "listing123",
			listingName: "Provider Name",
			contactType: "phone",
			contactValue: "+**********"
		});
	};
}
```

## Benefits of Queued Analytics Tracking

### 1. Performance Optimization

- Analytics tracking doesn't impact response times
- Critical business operations complete quickly
- Background processing ensures reliable tracking

### 2. Reliability for Critical Events

- Lead forms, account creation, and provider contacts tracked via queue
- Tracks actual successful actions, not just clicks
- No dependency on client-side JavaScript for critical conversions

### 3. Cross-Platform Compatibility

- Server-side tracking works for web, mobile iOS, and mobile Android
- Client-side tracking available for frontend components
- Consistent data format across all platforms

### 4. Security

- User context handled server-side for critical events
- No exposure of Google Analytics credentials to client
- Centralized error handling

### 5. Maintainability

- Queued service integration for critical business events
- Simple and straightforward architecture
- Graceful error handling without affecting user experience

## Google Ads Integration

### Conversion Tracking

- Lead form submissions trigger Google Ads conversions
- Uses conversion label: `pyNgCJyai-EaEJ_4hZNA`
- Conversion ID: `AW-***********`

### Event Parameters

All events include:

- `event_category`: 'conversion'
- `event_label`: Specific event identifier
- `value`: 1 (for conversion tracking)
- User context (authenticated vs anonymous)
- Relevant business data (listing info, categories, etc.)

## Monitoring and Debugging

### Logging

- All tracking calls are logged to console
- Server-side errors are logged but don't fail the main operation
- Failed tracking doesn't affect user experience

### Error Handling

- Graceful degradation if Google Analytics is unavailable
- Validation errors return appropriate HTTP status codes
- Missing required fields are handled gracefully

## Future Enhancements

### Potential Additions

1. **Batch Tracking**: Support for multiple events in single API call
2. **Custom Dimensions**: Additional business-specific parameters
3. **Real-time Dashboard**: Analytics dashboard for campaign performance
4. **A/B Testing**: Support for tracking different conversion paths

### Performance Optimizations

1. **Caching**: Cache frequently accessed listing data
2. **Queue Processing**: Move tracking to background queue for high-volume events
3. **Rate Limiting**: Prevent abuse of tracking endpoint

## Migration Notes

### From Client-Side Only

- All existing client-side tracking has been migrated
- No breaking changes to existing functionality
- Enhanced reliability and cross-platform support

### Environment Setup

- Ensure `GA_API_SECRET` is configured in production
- Verify `NEXT_PUBLIC_APP_URL` is set correctly
- Test tracking in staging environment before production

## Support

For issues or questions about the analytics implementation:

1. Check the test suite for expected behavior
2. Review server logs for tracking errors
3. Verify environment variables are configured correctly
4. Test API endpoint directly for debugging
