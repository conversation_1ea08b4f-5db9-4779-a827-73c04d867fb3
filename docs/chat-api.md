# AI Chat API Documentation

This document describes the AI Chat API endpoints for the RV Help application, which provides AI-powered chat assistance for RV maintenance and troubleshooting.

## Overview

The AI Chat system provides an expert RV technician AI that helps users with:

- Step-by-step troubleshooting for common RV issues
- Safety-conscious guidance for plumbing, electrical, HVAC, and other systems
- Personalized advice based on RV details and location
- Conversation history and context awareness
- Performance optimizations including model warmup and response caching

## Authentication

All endpoints require Bearer token authentication via the `Authorization` header.

## Endpoints

### 1. Get All Conversations

**GET** `/api/ai-chat/conversations`

Retrieves all AI chat conversations for the authenticated user.

**Response:**

```json
{
	"conversations": [
		{
			"id": "conv_123456789",
			"title": "Winterization Help",
			"status": "ACTIVE",
			"createdAt": "2024-01-15T10:30:00Z",
			"lastMessageAt": "2024-01-15T11:45:00Z",
			"messageCount": 8,
			"rvDetails": {
				"make": "Keystone",
				"model": "Montana",
				"year": 2026,
				"type": "fifth-wheel"
			}
		}
	],
	"total": 1
}
```

### 2. Start New Conversation

**POST** `/api/ai-chat/conversations`

Creates a new chat conversation for the authenticated user. **Authentication required.**

**Request Body:** (empty)

**Response:**

```json
{
	"conversationId": "conv_123456789",
	"status": "created"
}
```

**Note:** The conversation will be automatically titled based on the first message sent to the AI.

### 3. Send AI Chat Message

**POST** `/api/ai-chat/message`

Sends a message to the AI assistant and receives a response.

**Request Body:**

```json
{
	"message": "How do I winterize my RV?",
	"conversationId": "conv_123456789",
	"context": {
		"userRvDetails": {
			"make": "Keystone",
			"model": "Montana",
			"year": 2026,
			"type": "fifth-wheel"
		},
		"currentLocation": {
			"lat": 40.7128,
			"lng": -74.006
		},
		"previousMessages": [
			{
				"id": "msg_1",
				"text": "Hello!",
				"isUser": true,
				"timestamp": "2024-01-15T10:30:00Z"
			}
		]
	}
}
```

**Response:**

```json
{
	"message": "To winterize your 2026 Keystone Montana fifth wheel, you'll need to...",
	"conversationId": "conv_123456789",
	"suggestions": [
		"What supplies do I need for winterizing?",
		"How often should I winterize my RV?",
		"Can I winterize my RV myself?"
	],
	"relatedResources": [
		{
			"title": "Montana Winterization Guide",
			"url": "/resources/montana-winterization",
			"type": "manual"
		}
	]
}
```

### 4. Get AI Conversation History

**GET** `/api/ai-chat/conversations/{conversationId}`

Retrieves the message history for a specific conversation.

**Response:**

```json
{
	"conversationId": "conv_123456789",
	"messages": [
		{
			"id": "msg_1",
			"text": "Hello!",
			"isUser": true,
			"timestamp": "2024-01-15T10:30:00Z"
		},
		{
			"id": "msg_2",
			"text": "Hi! How can I help you today?",
			"isUser": false,
			"timestamp": "2024-01-15T10:30:05Z"
		}
	]
}
```

### 5. Get AI Suggested Questions

**GET** `/api/ai-chat/suggestions`

Returns suggested questions based on the user's RV details and common queries.

**Response:**

```json
{
	"questions": [
		"How do I check my tire pressure?",
		"What's the proper way to level my RV?",
		"How often should I service my generator?",
		"What maintenance should I do before a trip?"
	]
}
```

## Implementation Details

### Database Schema

The AI chat system uses two main tables:

- `ai_chat_conversations`: Stores conversation metadata and RV context
- `ai_chat_messages`: Stores individual messages with AI response metadata

### AI Integration

- Uses OpenAI's GPT-4 API for generating responses
- Includes user context (RV details, location) in the system prompt
- Maintains conversation history for context
- Generates suggestions and related resources based on message content
- Uses a specialized RV technician prompt that provides friendly, step-by-step troubleshooting support

### Security Features

- All endpoints require authentication
- Only logged-in users can create and access AI conversations
- Users can only access their own AI conversations
- Input validation using Zod schemas
- Rate limiting per user (implemented at the service level)

### Error Handling

- Returns appropriate HTTP status codes
- Provides user-friendly error messages
- Handles OpenAI API failures gracefully
- Implements fallback responses for common questions

## Usage Examples

### JavaScript/TypeScript

The AI chat system provides a conversational interface for RV maintenance and troubleshooting assistance.

```typescript
// Get all AI conversations
const conversationsResponse = await fetch("/api/ai-chat/conversations", {
	method: "GET",
	headers: {
		Authorization: `Bearer ${token}`,
		"Content-Type": "application/json"
	}
});
const { conversations, total } = await conversationsResponse.json();

// Create a new AI conversation
const createResponse = await fetch("/api/ai-chat/conversations", {
	method: "POST",
	headers: {
		Authorization: `Bearer ${token}`,
		"Content-Type": "application/json"
	}
});
const { conversationId } = await createResponse.json();

// Send an AI message
const messageResponse = await fetch("/api/ai-chat/message", {
	method: "POST",
	headers: {
		Authorization: `Bearer ${token}`,
		"Content-Type": "application/json"
	},
	body: JSON.stringify({
		message: "How do I winterize my RV?",
		conversationId,
		context: {
			userRvDetails: {
				make: "Keystone",
				model: "Montana",
				year: 2026,
				type: "fifth-wheel"
			}
		}
	})
});
const { message, suggestions, relatedResources } = await messageResponse.json();
```

### cURL

```bash
# Get all AI conversations
curl -X GET http://localhost:3000/api/ai-chat/conversations \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# Create AI conversation
curl -X POST http://localhost:3000/api/ai-chat/conversations \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# Send AI message
curl -X POST http://localhost:3000/api/ai-chat/message \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "How do I winterize my RV?",
    "conversationId": "conv_123456789",
    "context": {
      "userRvDetails": {
        "make": "Keystone",
        "model": "Montana",
        "year": 2026,
        "type": "fifth-wheel"
      }
    }
  }'
```

## Testing

The AI chat functionality includes comprehensive unit tests:

```bash
# Run service tests
npm run jest tests/unit/services/chat.service.test.ts

# Run all tests
npm run jest
```

## Configuration

The AI chat system uses the following environment variables:

- `OPENAI_API_KEY`: Required for AI responses
- `JWT_SECRET`: For authentication

The system prompt and AI settings can be configured in `lib/chat/config.ts`. The AI chat uses a specialized RV technician prompt that provides friendly, step-by-step troubleshooting support for common RV issues including plumbing, electrical systems, appliances, HVAC, slide-outs, and awnings.

### System Prompt

The AI chat uses a carefully crafted system prompt that defines the AI's role as a friendly, knowledgeable RV technician. The prompt includes:

- **Core Role**: Step-by-step troubleshooting support for common RV issues
- **Communication Style**: Casual, friendly, and reassuring tone
- **Safety Guidelines**: Always prioritize safety and recommend professional help when needed
- **Response Structure**: Clear acknowledgment, troubleshooting steps, safety tips, and follow-up support

The prompt is designed to build user confidence while ensuring safety and providing actionable guidance for minor RV problems.

### Conversation Titling

Conversations are automatically titled based on the first message content. The system recognizes common RV topics and generates appropriate titles such as:

- "Winterization Help" for winterization questions
- "Tire Maintenance" for tire-related issues
- "Battery Issues" for battery problems
- "Plumbing Help" for water/plumbing issues
- "Electrical Issues" for power/electrical problems
- "HVAC Issues" for heating/cooling problems
- And many more RV-specific categories

If no specific category is detected, the system uses the first few words of the message as the title.

## Performance Optimizations

The AI Chat system includes several performance optimizations to ensure fast response times:

### Model Warmup

The system automatically warms up the AI model to avoid "cold start" delays:

- **Automatic Warmup**: The model is warmed up when the first chat message is sent
- **Manual Warmup**: You can trigger warmup via the `/api/ai-chat/warmup` endpoint
- **Client-Side Integration**: Use the `useAIChatWarmup` hook or `AIChatWarmup` component to warm up when users enter the chat area

```typescript
// Using the hook
import { useAIChatWarmup } from "@/hooks/useAIChatWarmup";

function ChatComponent() {
    const { isWarmed, isWarming, warmup } = useAIChatWarmup();

    useEffect(() => {
        warmup(); // Warm up when component mounts
    }, []);

    return (
        <div>
            {isWarming && <p>🔄 Warming up AI model...</p>}
            {isWarmed && <p>✅ AI model ready</p>}
        </div>
    );
}
```

### Response Caching

Common questions are cached to provide instant responses:

- **Smart Caching**: Frequently asked questions are cached for 1 hour
- **Automatic Cleanup**: Cache is automatically cleaned when it exceeds 100 entries
- **Transparent**: Caching is handled automatically - no additional configuration needed

### Warmup Endpoint

**POST** `/api/ai-chat/warmup`

Manually triggers AI model warmup. Useful for pre-warming before users start chatting.

**Response:**

```json
{
	"success": true,
	"warmed": true
}
```

**Usage:**

```bash
curl -X POST http://localhost:3000/api/ai-chat/warmup \
  -H "Content-Type: application/json"
```
