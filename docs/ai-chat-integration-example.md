# AI Chat Integration Example

This document shows how to integrate the AI Chat warmup functionality into your React components.

## Basic Integration

```tsx
"use client";

import { useState, useEffect } from "react";
import { useAIChatWarmup } from "@/hooks/useAIChatWarmup";

export function AIChatInterface() {
	const [message, setMessage] = useState("");
	const [conversationId, setConversationId] = useState<string | null>(null);
	const { isWarmed, isWarming, warmup } = useAIChatWarmup();

	// Warm up the AI model when the component mounts
	useEffect(() => {
		warmup();
	}, [warmup]);

	const startConversation = async () => {
		try {
			const response = await fetch("/api/ai-chat/conversations", {
				method: "POST",
				headers: {
					Authorization: `Bearer ${token}`,
					"Content-Type": "application/json"
				}
			});
			const data = await response.json();
			setConversationId(data.conversationId);
		} catch (error) {
			console.error("Failed to start conversation:", error);
		}
	};

	const sendMessage = async () => {
		if (!conversationId || !message.trim()) return;

		try {
			const response = await fetch("/api/ai-chat/message", {
				method: "POST",
				headers: {
					Authorization: `Bearer ${token}`,
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					message: message,
					conversationId: conversationId
				})
			});
			const data = await response.json();
			console.log("AI Response:", data.message);
			setMessage("");
		} catch (error) {
			console.error("Failed to send message:", error);
		}
	};

	return (
		<div className="p-4">
			{/* Warmup Status */}
			<div className="mb-4">
				{isWarming && (
					<div className="text-blue-600">🔄 Warming up AI model...</div>
				)}
				{isWarmed && <div className="text-green-600">✅ AI model ready</div>}
			</div>

			{/* Chat Interface */}
			<div className="space-y-4">
				{!conversationId ? (
					<button
						onClick={startConversation}
						disabled={!isWarmed}
						className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
					>
						Start New Conversation
					</button>
				) : (
					<div className="space-y-2">
						<textarea
							value={message}
							onChange={(e) => setMessage(e.target.value)}
							placeholder="Ask about RV maintenance..."
							className="w-full p-2 border rounded"
							rows={3}
						/>
						<button
							onClick={sendMessage}
							disabled={!message.trim()}
							className="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50"
						>
							Send Message
						</button>
					</div>
				)}
			</div>
		</div>
	);
}
```

## Using the AIChatWarmup Component

Alternatively, you can use the `AIChatWarmup` component directly:

```tsx
import { AIChatWarmup } from "@/components/AIChatWarmup";

export function ChatPage() {
	return (
		<div>
			<h1>RV AI Chat</h1>

			{/* Show warmup status */}
			<AIChatWarmup showStatus={true} />

			{/* Your chat interface */}
			<AIChatInterface />
		</div>
	);
}
```

## Performance Benefits

With this integration:

1. **Fast First Response**: The AI model is warmed up before the user sends their first message
2. **User Feedback**: Users see the warmup status and know when the AI is ready
3. **Automatic Optimization**: Subsequent messages are even faster due to caching
4. **Graceful Degradation**: If warmup fails, the chat still works (just slower first response)

## Best Practices

- **Warm up early**: Trigger warmup when users enter the chat area, not when they start typing
- **Show status**: Let users know the AI is getting ready
- **Handle errors gracefully**: Don't block the UI if warmup fails
- **Cache conversations**: Store conversation IDs locally to avoid recreating them
