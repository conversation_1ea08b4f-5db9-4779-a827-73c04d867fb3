name: Create Neon DB Branch for Vercel Preview

on:  
  push:
    branches:
      - '*'   # Trigger on every branch push
      
jobs:
  deploy-preview:
    runs-on: ubuntu-latest
    # Only run for branches other than main, develop, and staging.
    if: >
      github.ref != 'refs/heads/main'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Dump info
        run: |
          echo "Branch name: ${{ github.ref_name }}"
          echo "GitHub ref: ${{ github.ref }}"
          echo "GitHub event ref: ${{ github.event.ref }}"
          echo "GitHub event name: ${{ github.event_name }}"

      - name: Set up Neon CLI        
        if: ${{ github.ref_name != 'main' && github.ref_name != 'develop' && github.ref_name != 'staging' }}  
  
        run: |
          echo "Installing neonctl..."
          npm install -g neonctl

      - name: Create Neon DB branch
        if: ${{ github.ref_name != 'main' && github.ref_name != 'develop' && github.ref_name != 'staging' }}
        run: |
          echo "Creating Neon DB branch for '${{ github.ref_name }}'"
          # Attempt to create a DB branch matching the Git branch name.
          # Optionally, check if it exists to avoid errors.
          neonctl branches create --name "${{ github.ref_name }}" --parent main --project-id curly-sun-58127739 || echo "Branch already exists"
        env:
          NEON_API_KEY: ${{ secrets.NEON_API_KEY }}

      - name: Retrieve Neon DB Connection String
        if: ${{ github.ref_name != 'main' && github.ref_name != 'develop' && github.ref_name != 'staging' }}
        id: get-connect-string
        run: |
          # Get the connection string as JSON and extract the value.
          conn=$(neonctl connection-string "${{ github.ref_name }}" --database-name dev --project-id curly-sun-58127739)
          echo "Connection string: $conn"
          echo "connection_string=$conn" >> $GITHUB_OUTPUT
        env:
          NEON_API_KEY: ${{ secrets.NEON_API_KEY }}
          
      - name: Set up Vercel CLI
        run: |
          echo "Installing vercel..."
          npm install -g vercel

      - name: Link repository to Vercel
        run: |
          echo "Linking Vercel project and setting environment variables"
          vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} link --repo --yes

      - name: Set DATABASE_URL and DIRECT_URL environment variables for rvhelp-web
        if: ${{ github.ref_name != 'main' && github.ref_name != 'develop' && github.ref_name != 'staging' }}   
        run: |
          echo "Setting environment variables..."
          cd apps/web
          vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env rm DATABASE_URL preview ${{ github.ref_name }} --yes|| echo "ENV VAR does not exist"
          vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env rm DIRECT_URL preview ${{ github.ref_name }} --yes|| echo "ENV VAR does not exist"
          echo "$NEON_DB_URL" | vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env add DATABASE_URL preview ${{ github.ref_name }} || echo "ENV VAR already exists"
          echo "$NEON_DB_URL" | vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env add DIRECT_URL preview ${{ github.ref_name }} || echo "ENV VAR already exists"
        env:
          NEON_DB_URL: ${{ steps.get-connect-string.outputs.connection_string }}          

      - name: Set DATABASE_URL and DIRECT_URL environment variables for oem-portal
        if: ${{ github.ref_name != 'main' && github.ref_name != 'develop' && github.ref_name != 'staging' }}   
        run: |
          echo "Setting environment variables..."
          cd apps/portal
          vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env rm DATABASE_URL preview ${{ github.ref_name }} --yes|| echo "ENV VAR does not exist"
          vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env rm DIRECT_URL preview ${{ github.ref_name }} --yes|| echo "ENV VAR does not exist"
          echo "$NEON_DB_URL" | vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env add DATABASE_URL preview ${{ github.ref_name }} || echo "ENV VAR already exists"
          echo "$NEON_DB_URL" | vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env add DIRECT_URL preview ${{ github.ref_name }} || echo "ENV VAR already exists"
        env:
          NEON_DB_URL: ${{ steps.get-connect-string.outputs.connection_string }}             

      - name: Deploy RVHelp to Vercel
        id: rvhelp-deploy
        run: |
          cd apps/web
          # Deploy and capture the preview URL
          PREVIEW_URL=$(vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} deploy \
            --meta githubDeployment=1 \
            --meta githubCommitRef=${{ github.ref_name }} \
            --meta githubCommitMessage="${{ github.event.head_commit.message }}" \
            --meta githubCommitSha=${{ github.sha }} \
            --meta githubRepo=${{ github.repository }} \
            --yes)
          echo "preview-url=$PREVIEW_URL" >> $GITHUB_OUTPUT

      - name: Remove existing RVHELP_PREVIEW_URL environment variable
        if: ${{ github.ref_name != 'main' && github.ref_name != 'develop' && github.ref_name != 'staging' }}   
        run: |
          echo "Removing existing RVHELP_PREVIEW_URL environment variable..."
          cd apps/portal
          vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env rm RVHELP_PREVIEW_URL preview ${{ github.ref_name }} --yes|| echo "ENV VAR does not exist"

      - name: Set RVHELP_PREVIEW_URL environment variable for OEM Portal
        if: ${{ github.ref_name != 'main' && github.ref_name != 'develop' && github.ref_name != 'staging' }}   
        run: |
          echo "Setting RVHELP_PREVIEW_URL Preview URL environment variable: ${{ steps.rvhelp-deploy.outputs.preview-url }}"
          cd apps/portal
          # Trim whitespace from the preview URL before setting it
          echo "${{ steps.rvhelp-deploy.outputs.preview-url }}" | tr -d '\n\r' | vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env add RVHELP_PREVIEW_URL preview ${{ github.ref_name }} || echo "ENV VAR already exists"

      - name: Deploy OEM Portal to Vercel
        run: |
          cd apps/portal
          vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} deploy \
            --meta githubDeployment=1 \
            --meta githubCommitRef=${{ github.ref_name }} \
            --meta githubCommitMessage="${{ github.event.head_commit.message }}" \
            --meta githubCommitSha=${{ github.sha }} \
            --meta githubRepo=${{ github.repository }} \
            --yes