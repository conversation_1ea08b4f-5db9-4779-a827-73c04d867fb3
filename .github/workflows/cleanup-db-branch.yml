name: Cleanup Neon DB Branch for Vercel Preview

on: delete

jobs:
  cleanup-preview:
    runs-on: ubuntu-latest
    # Only run on branch deletion events
    steps:
      - name: Dump info
        run: |
            echo "Branch name: ${{ github.ref_name }}"
            echo "GitHub ref: ${{ github.ref }}"
            echo "GitHub event ref: ${{ github.event.ref }}"
            echo "GitHub event name: ${{ github.event_name }}"
            
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Neon CLI          
        run: |
          echo "Installing neonctl..."
          npm install -g neonctl

      - name: Delete Neon DB branch
        if: ${{ github.event.ref != 'main' && github.event.ref != 'develop' && github.event.ref != 'staging' }}  
        run: |
          echo "Deleting Neon DB branch for '${{ github.event.ref }}'"
          neonctl branches delete "${{ github.event.ref }}" --project-id curly-sun-58127739 || echo "Branch may not exist"
        env:
          NEON_API_KEY: ${{ secrets.NEON_API_KEY }}

      - name: Set up Vercel CLI
        if: ${{ github.event.ref != 'main' && github.event.ref != 'develop' && github.event.ref != 'staging' }}  
        run: |
          echo "Installing vercel..."
          npm install -g vercel
                        
      - name: Remove environment variables
        if: ${{ github.event.ref != 'main' && github.event.ref != 'develop' && github.event.ref != 'staging' }}  
        run: |
          echo "Linking Vercel project and removing environment variables"
          vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} link --yes --project rvhelp-web
          vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env rm DATABASE_URL preview "${{ github.event.ref }}" -y || echo "Variable may not exist"
          vercel --scope ${{ secrets.VERCEL_ORG_ID }} --token ${{ secrets.VERCEL_TOKEN }} env rm DIRECT_URL preview "${{ github.event.ref }}" -y || echo "Variable may not exist"