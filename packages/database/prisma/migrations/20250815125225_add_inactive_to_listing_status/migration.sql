-- AlterEnum
ALTER TYPE "public"."ListingStatus" ADD VALUE 'INELIGIBLE';

-- Commit the enum change before using it
COMMIT;

-- Start a new transaction for the data update
BEGIN;

-- Update listings to INELIGIBLE status if they don't have appropriate certification levels
-- A listing is INELIGIBLE if BOTH conditions are true:
-- 1. nrvia_inspector_level is NULL, 0, or not greater than 0
-- 2. rvtaa_technician_level is NULL, 0, or not greater than 0
-- Only listings that have at least one certification level of 1, 2, or 3 will remain eligible
UPDATE "public"."listings" 
SET status = 'INELIGIBLE'
WHERE status != 'INELIGIBLE' 
  AND (
    COALESCE(nrvia_inspector_level, 0) <= 0 
    AND COALESCE(rvtaa_technician_level, 0) <= 0
  );

-- Commit the data changes
COMMIT;
