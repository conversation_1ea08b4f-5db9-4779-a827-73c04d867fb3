-- Create a function to format phone numbers to E.164 format
CREATE OR REPLACE FUNCTION format_phone_to_e164(phone TEXT) RETURNS TEXT AS $$
BEGIN
    -- Remove all non-digit characters except + at the start
    phone := regexp_replace(phone, '[^\d+]', '', 'g');
    
    -- If it already starts with +, handle it properly
    IF phone LIKE '+%' THEN
        -- If it's a US number without the 1 after the +, add it
        IF length(phone) = 11 AND phone LIKE '+%' THEN
            -- This is likely a US number missing the 1 after +
            RETURN '+1' || substring(phone from 2);
        END IF;
        -- If it's a US number with the 1 after the +, return as is
        IF length(phone) = 12 AND phone LIKE '+1%' THEN
            RETURN phone;
        END IF;
        -- For other international numbers, return as is
        RETURN phone;
    END IF;
    
    -- Handle numbers without + prefix
    -- If it's exactly 10 digits, it's a US number without country code
    IF length(phone) = 10 THEN
        RETURN '+1' || phone;
    END IF;
    
    -- If it's 11 digits and starts with 1, it's a US number
    IF length(phone) = 11 AND phone LIKE '1%' THEN
        RETURN '+' || phone;
    END IF;
    
    -- If it's 11 digits but doesn't start with 1, assume it's a US number and add +1
    IF length(phone) = 11 AND NOT phone LIKE '1%' THEN
        RETURN '+1' || phone;
    END IF;
    
    -- For any other length, assume it's a US number and add +1
    RETURN '+1' || phone;
END;
$$ LANGUAGE plpgsql;

-- Update phone numbers in users table
UPDATE users 
SET phone = format_phone_to_e164(phone) 
WHERE phone IS NOT NULL 
  AND phone != '' 
  AND phone NOT LIKE '+%';

-- Update phone numbers in listings table
UPDATE listings 
SET phone = format_phone_to_e164(phone) 
WHERE phone IS NOT NULL 
  AND phone != '' 
  AND phone NOT LIKE '+%';

-- Update notification_sms numbers in listings table
UPDATE listings 
SET notification_sms = format_phone_to_e164(notification_sms) 
WHERE notification_sms IS NOT NULL 
  AND notification_sms != '' 
  AND notification_sms NOT LIKE '+%';

-- Update phone numbers in jobs table
UPDATE jobs 
SET phone = format_phone_to_e164(phone) 
WHERE phone IS NOT NULL 
  AND phone != '' 
  AND phone NOT LIKE '+%';

-- Update customer_phone numbers in invoices table
UPDATE invoices 
SET customer_phone = format_phone_to_e164(customer_phone) 
WHERE customer_phone IS NOT NULL 
  AND customer_phone != '' 
  AND customer_phone NOT LIKE '+%';

-- Update phone numbers in troubleshooting_requests table
UPDATE troubleshooting_requests 
SET phone = format_phone_to_e164(phone) 
WHERE phone IS NOT NULL 
  AND phone != '' 
  AND phone NOT LIKE '+%';

-- Update phone numbers in virtual_diagnostic_requests table
UPDATE virtual_diagnostic_requests 
SET phone = format_phone_to_e164(phone) 
WHERE phone IS NOT NULL 
  AND phone != '' 
  AND phone NOT LIKE '+%';

-- Update phone numbers in warranty_requests table
UPDATE warranty_requests 
SET phone = format_phone_to_e164(phone) 
WHERE phone IS NOT NULL 
  AND phone != '' 
  AND phone NOT LIKE '+%';

-- Update support_phone numbers in companies table
UPDATE companies 
SET support_phone = format_phone_to_e164(support_phone) 
WHERE support_phone IS NOT NULL 
  AND support_phone != '' 
  AND support_phone NOT LIKE '+%';

-- Drop the function after use
DROP FUNCTION format_phone_to_e164(TEXT);