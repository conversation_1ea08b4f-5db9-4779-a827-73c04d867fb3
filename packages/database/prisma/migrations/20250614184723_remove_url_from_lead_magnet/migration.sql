-- AlterTable
ALTER TABLE "articles" ADD COLUMN     "lead_magnet_id" TEXT;

-- CreateTable
CREATE TABLE "lead_magnets" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "image" TEXT,
    "newsletter_tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "status" TEXT NOT NULL DEFAULT 'active',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "lead_magnets_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "lead_magnets_status_idx" ON "lead_magnets"("status");

-- CreateIndex
CREATE INDEX "articles_lead_magnet_id_idx" ON "articles"("lead_magnet_id");

-- Ad<PERSON>F<PERSON>ignKey
ALTER TABLE "articles" ADD CONSTRAINT "articles_lead_magnet_id_fkey" FOREIGN KEY ("lead_magnet_id") REFERENCES "lead_magnets"("id") ON DELETE SET NULL ON UPDATE CASCADE;
