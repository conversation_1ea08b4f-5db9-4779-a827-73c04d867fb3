-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "public"."TimelineEventType" ADD VALUE 'CUSTOMER_WITHDRAWN';
ALTER TYPE "public"."TimelineEventType" ADD VALUE 'OEM_UPDATED';
ALTER TYPE "public"."TimelineEventType" ADD VALUE 'CUSTOMER_UPDATED';

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "public"."WarrantyRequestStatus" ADD VALUE 'JOB_WITHDRAWN';
ALTER TYPE "public"."WarrantyRequestStatus" ADD VALUE 'AUTHORIZATION_PARTIAL';
