/*
  Warnings:

  - The enum `WarrantyRequestEventType` will be renamed to `TimelineEventType`

*/

-- Step 1: Create the new TimelineEventType enum with all values
CREATE TYPE "TimelineEventType" AS ENUM (
  'PREAUTHORIZATION_APPROVED',
  'PREAUTHORIZATION_REJECTED', 
  'CUSTOMER_REGISTERED',
  'TECHNICIAN_INVITED',
  'TECHNICIAN_ACCEPTED',
  'TECHNICIAN_REJECTED',
  'TECHNICIAN_REQUESTED_INFO',
  'TECHNICIAN_WITHDRAWN',
  'CUSTOMER_ACCEPTED',
  'CUSTOMER_REJECTED',
  'AU<PERSON>ORIZATION_REQUESTED',
  'AUTHORIZATION_APPROVED',
  'AUTHORIZATION_REJECTED',
  'TECHNICIAN_UPDATED',
  'JOB_STARTED',
  'JOB_COMPLETED',
  'JOB_CANCELLED',
  'JOB_PAUSED',
  'INVOICE_CREATED',
  'INVOICE_PAID',
  'ISSUE_RESOLVED',
  'RE<PERSON><PERSON><PERSON>_REQUESTED',
  'REVIEW_LEFT'
);

-- Step 2: Update the column to use the new enum type
ALTER TABLE "warranty_request_updates" 
  ALTER COLUMN "event_type" TYPE "TimelineEventType" 
  USING "event_type"::text::"TimelineEventType";

-- Step 3: Drop the old enum
DROP TYPE "WarrantyRequestEventType"; 