-- CreateTable
CREATE TABLE "article_reads" (
    "id" TEXT NOT NULL,
    "article_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "read_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "article_reads_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "article_reads_article_id_idx" ON "article_reads"("article_id");

-- CreateIndex
CREATE INDEX "article_reads_user_id_idx" ON "article_reads"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "article_reads_article_id_user_id_key" ON "article_reads"("article_id", "user_id");

-- AddForeignKey
ALTER TABLE "article_reads" ADD CONSTRAINT "article_reads_article_id_fkey" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "article_reads" ADD CONSTRAINT "article_reads_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Mark all existing announcements as read for all users
INSERT INTO "article_reads" ("id", "article_id", "user_id", "read_at")
SELECT 
    gen_random_uuid()::text,
    a.id,
    u.id,
    CURRENT_TIMESTAMP
FROM "articles" a
CROSS JOIN "users" u
WHERE a.status = 'published'
AND a.type IN ('provider-announcement', 'provider-training', 'provider-office-hours')
ON CONFLICT ("article_id", "user_id") DO NOTHING;
