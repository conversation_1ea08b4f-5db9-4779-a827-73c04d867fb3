/*
  Warnings:

  - The values [PENDING,EXPIRED] on the enum `QuoteStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `settings_offers_discount` on the `listings` table. All the data in the column will be lost.
  - You are about to drop the `conversations` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `messages` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `request_notifications` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `service_quotes` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `service_requests` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "JobStatus" AS ENUM ('OPEN', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "QuoteMessageStatus" AS ENUM ('PENDING', 'SENT', 'DELIVERED', 'FAILED', 'READ');

-- CreateEnum
CREATE TYPE "QuoteMessageType" AS ENUM ('TEXT', 'IMAGE', 'DOCUMENT', 'SYSTEM');

-- CreateEnum
CREATE TYPE "ResolutionStatus" AS ENUM ('COMPLETED', 'CANCELLED', 'NO_RESPONSE', 'NOT_VIABLE', 'REFERRED', 'OTHER');

-- CreateEnum
CREATE TYPE "WarrantyRequestStatus" AS ENUM ('REQUEST_CREATED', 'REQUEST_APPROVED', 'REQUEST_REJECTED', 'JOB_REQUESTED', 'JOB_ACCEPTED', 'JOB_STARTED', 'JOB_COMPLETED', 'JOB_CANCELLED', 'AUTHORIZATION_REQUESTED', 'AUTHORIZATION_APPROVED', 'AUTHORIZATION_REJECTED', 'INVOICE_CREATED', 'INVOICE_PAID');

-- CreateEnum
CREATE TYPE "WarrantyRequestEventType" AS ENUM ('PREAUTHORIZATION_APPROVED', 'PREAUTHORIZATION_REJECTED', 'CUSTOMER_REGISTERED', 'TECHNICIAN_INVITED', 'TECHNICIAN_ACCEPTED', 'TECHNICIAN_REJECTED', 'TECHNICIAN_REQUESTED_INFO', 'TECHNICIAN_WITHDRAWN', 'CUSTOMER_ACCEPTED', 'CUSTOMER_REJECTED', 'AUTHORIZATION_REQUESTED', 'AUTHORIZATION_APPROVED', 'AUTHORIZATION_REJECTED', 'JOB_STARTED', 'JOB_COMPLETED', 'JOB_CANCELLED', 'JOB_PAUSED', 'INVOICE_CREATED', 'INVOICE_PAID', 'ISSUE_RESOLVED', 'REVIEW_REQUESTED', 'REVIEW_LEFT');

-- CreateEnum
CREATE TYPE "RejectionReasonType" AS ENUM ('TOO_BUSY', 'NOT_A_GOOD_FIT', 'SCHEDULE_CONFLICT', 'OUTSIDE_TRAVEL_AREA', 'OTHER');

-- AlterEnum - Handle enum change after dropping tables that reference it  
CREATE TYPE "QuoteStatus_new" AS ENUM ('PENDING', 'ACCEPTED', 'REJECTED', 'WITHDRAWN', 'EXPIRED', 'IN_PROGRESS', 'COMPLETED');

-- AlterEnum
ALTER TYPE "Role" ADD VALUE 'OEM';

-- DropForeignKey from tables that will be dropped
ALTER TABLE "conversations" DROP CONSTRAINT IF EXISTS "conversations_customer_id_fkey";
ALTER TABLE "conversations" DROP CONSTRAINT IF EXISTS "conversations_listing_id_fkey";
ALTER TABLE "messages" DROP CONSTRAINT IF EXISTS "messages_conversation_id_fkey";
ALTER TABLE "messages" DROP CONSTRAINT IF EXISTS "messages_listing_id_fkey";
ALTER TABLE "messages" DROP CONSTRAINT IF EXISTS "messages_sender_id_fkey";
ALTER TABLE "request_notifications" DROP CONSTRAINT IF EXISTS "request_notifications_tech_id_fkey";
ALTER TABLE "service_quotes" DROP CONSTRAINT IF EXISTS "service_quotes_listing_id_fkey";

-- AlterTable
ALTER TABLE "listings" DROP COLUMN IF EXISTS "settings_offers_discount";
ALTER TABLE "listings" ADD COLUMN IF NOT EXISTS "settings_enable_instant_booking" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "company_id" TEXT;

-- Drop tables that are no longer needed (before enum changes)
DROP TABLE IF EXISTS "conversations" CASCADE;
DROP TABLE IF EXISTS "messages" CASCADE;
DROP TABLE IF EXISTS "request_notifications" CASCADE;
DROP TABLE IF EXISTS "service_quotes" CASCADE;

-- Drop unused enums
DROP TYPE IF EXISTS "ConversationStatus";
DROP TYPE IF EXISTS "MessageChannel";
DROP TYPE IF EXISTS "MessageStatus";

-- Complete the QuoteStatus enum alteration now that referencing tables are dropped
ALTER TYPE "QuoteStatus" RENAME TO "QuoteStatus_old";
ALTER TYPE "QuoteStatus_new" RENAME TO "QuoteStatus";
DROP TYPE IF EXISTS "QuoteStatus_old";

-- CreateTable - Create Jobs table first
CREATE TABLE "jobs" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "contact_preference" TEXT NOT NULL DEFAULT 'sms',
    "location" JSONB,
    "message" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "source" TEXT NOT NULL DEFAULT 'web',
    "rv_year" TEXT,
    "rv_make" TEXT,
    "rv_model" TEXT,
    "rv_type" TEXT,
    "status" "JobStatus" NOT NULL DEFAULT 'OPEN',
    "sent_at" TIMESTAMP(3),
    "error_message" TEXT,
    "viewed_at" TIMESTAMP(3),
    "is_premium" BOOLEAN NOT NULL DEFAULT false,
    "transaction_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "warranty_request_id" TEXT,
    "accepted_quote_id" TEXT,

    CONSTRAINT "jobs_pkey" PRIMARY KEY ("id")
);

-- CreateTable - Create Quotes table second
CREATE TABLE "quotes" (
    "id" TEXT NOT NULL,
    "job_id" TEXT NOT NULL,
    "listing_id" TEXT NOT NULL,
    "status" "QuoteStatus" NOT NULL DEFAULT 'PENDING',
    "invited_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "responded_at" TIMESTAMP(3),
    "accepted_at" TIMESTAMP(3),
    "reviewed_at" TIMESTAMP(3),
    "display_pricing" BOOLEAN NOT NULL DEFAULT true,
    "discount_hourly_rate" BOOLEAN NOT NULL DEFAULT false,
    "discount_dispatch_fee" BOOLEAN NOT NULL DEFAULT false,
    "hourly_rate" DOUBLE PRECISION,
    "dispatch_fee" DOUBLE PRECISION,
    "first_hour_included" BOOLEAN NOT NULL DEFAULT false,
    "scheduling_timeframe" TEXT,
    "provider_notes" TEXT,
    "reminder_sent_at" TIMESTAMP(3),
    "last_viewed_at" TIMESTAMP(3),
    "provider_viewed_at" TIMESTAMP(3),
    "review_requested" BOOLEAN NOT NULL DEFAULT false,
    "review_delay_hours" INTEGER,
    "review_requested_at" TIMESTAMP(3),
    "review_sent_at" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "resolution_status" "ResolutionStatus",
    "resolution_notes" TEXT,
    "distance_miles" DOUBLE PRECISION,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "rejection_reason" "RejectionReasonType",
    "rejection_reason_details" TEXT,

    CONSTRAINT "quotes_pkey" PRIMARY KEY ("id")
);

-- Remove scheduling_timeframe column that was added but is no longer needed
ALTER TABLE "quotes" DROP COLUMN "scheduling_timeframe";

-- DATA MIGRATION: Group service_requests and create consolidated jobs
-- Step 1: Create a temporary table to identify job groups
-- Multiple service requests for the same RV on the same day become one job
CREATE TEMP TABLE job_groups AS
SELECT 
    MIN("id") as primary_request_id,
    "user_id",
    "first_name", 
    "last_name",
    "email",
    "phone",
    COALESCE("contact_preference", 'sms') as contact_preference,
    MIN("location"::text)::jsonb as location,  -- Convert to text for MIN, then back to JSONB
    -- For consolidated jobs, take the first category or create a combined category
    CASE 
        WHEN COUNT(DISTINCT "category") = 1 THEN MIN("category")
        ELSE 'Multiple: ' || STRING_AGG(DISTINCT "category", ', ' ORDER BY "category")
    END as category,
    -- For consolidated jobs, combine all unique messages
    CASE 
        WHEN COUNT(DISTINCT "message") = 1 THEN MIN("message")
        ELSE STRING_AGG(DISTINCT "message", ' | ' ORDER BY "message")
    END as message,
    COALESCE("source", 'web') as source,
    CASE WHEN "rv_year" IS NOT NULL THEN "rv_year"::TEXT ELSE NULL END as rv_year,
    "rv_make",
    "rv_model",
    "rv_type",
    MIN("created_at") as created_at,
    MAX("updated_at") as updated_at,
    MIN("sent_at") as sent_at,
    MIN("error_message") as error_message,
    -- Calculate job status based on aggregate of service_requests in group
    CASE 
        WHEN COUNT(CASE WHEN "status" = 'completed' THEN 1 END) > 0 THEN 'COMPLETED'
        WHEN COUNT(CASE WHEN "status" = 'cancelled' THEN 1 END) = COUNT(*) THEN 'CANCELLED'
        WHEN COUNT(CASE WHEN "provider_status" = 'resolved' THEN 1 END) > 0 THEN 'COMPLETED'
        WHEN COUNT(CASE WHEN "provider_status" = 'accepted' THEN 1 END) > 0 THEN 'IN_PROGRESS'
        WHEN COUNT(CASE WHEN "provider_status" = 'rejected' THEN 1 END) = COUNT(*) THEN 'CANCELLED'
        WHEN COUNT(CASE WHEN "provider_status" = 'need_info' THEN 1 END) > 0 THEN 'IN_PROGRESS'
        ELSE 'OPEN'
    END as job_status,
    ARRAY_AGG("id") as service_request_ids,
    COUNT(*) as request_count
FROM "service_requests"
GROUP BY 
    "user_id",
    DATE("created_at"), -- Group by same day
    "first_name", 
    "last_name",
    "email",
    "phone",
    COALESCE("contact_preference", 'sms'),
    COALESCE("source", 'web'),
    CASE WHEN "rv_year" IS NOT NULL THEN "rv_year"::TEXT ELSE NULL END,
    "rv_make",
    "rv_model",
    "rv_type";

-- Step 2: Insert consolidated jobs
INSERT INTO "jobs" (
    "id",
    "user_id",
    "first_name", 
    "last_name",
    "email",
    "phone",
    "contact_preference",
    "location",
    "message",
    "category",
    "source",
    "rv_year",
    "rv_make",
    "rv_model", 
    "rv_type",
    "status",
    "sent_at", 
    "error_message",
    "created_at",
    "updated_at"
)
SELECT 
    primary_request_id || '_job',  -- Use primary request ID for job ID
    user_id,
    first_name,
    last_name, 
    email,
    phone,
    contact_preference,
    location,
    message,
    category,
    source,
    rv_year,
    rv_make,
    rv_model,
    rv_type,
    job_status::"JobStatus",
    sent_at,
    error_message, 
    created_at,
    updated_at
FROM job_groups;

-- Step 3: Insert quotes for each service_request, linking to the consolidated job
-- Use DISTINCT ON to ensure only one quote per job+listing combination (taking the latest)
INSERT INTO "quotes" (
    "id",
    "job_id",
    "listing_id", 
    "status",
    "invited_at",
    "responded_at",
    "accepted_at",
    "provider_notes",
    "reminder_sent_at",
    "provider_viewed_at",
    "resolution_status",
    "resolution_notes",
    "distance_miles",
    "created_at",
    "updated_at"
)
SELECT DISTINCT ON (jg.primary_request_id || '_job', sr."listing_id")
    sr."id",  -- Quote ID is the original service_request ID
    jg.primary_request_id || '_job',  -- Link to consolidated job
    sr."listing_id",
    CASE 
        -- Any quote with resolution_status should be marked as COMPLETED (resolved leads)
        WHEN sr."resolution_status" IS NOT NULL THEN 'COMPLETED'::"QuoteStatus"
        -- Provider status 'resolved' means customer accepted the quote and job is complete
        WHEN sr."provider_status" = 'resolved' THEN 'COMPLETED'::"QuoteStatus"
        -- Provider status 'accepted' means provider quoted and customer accepted
        WHEN sr."provider_status" = 'accepted' THEN 'ACCEPTED'::"QuoteStatus"
        -- Provider status 'rejected' means customer rejected the quote
        WHEN sr."provider_status" = 'rejected' THEN 'REJECTED'::"QuoteStatus"
        -- Provider status 'need_info' means provider needs more information
        WHEN sr."provider_status" = 'need_info' THEN 'IN_PROGRESS'::"QuoteStatus"
        -- Provider status 'pending' means provider was invited but hasn't responded
        WHEN sr."provider_status" = 'pending' THEN 'PENDING'::"QuoteStatus"
        -- Job was completed (legacy status)
        WHEN sr."status" = 'completed' THEN 'COMPLETED'::"QuoteStatus"
        -- Request was cancelled or timed out
        WHEN sr."status" = 'cancelled' THEN 'EXPIRED'::"QuoteStatus"
        -- Default: Provider was invited but hasn't responded
        ELSE 'PENDING'::"QuoteStatus"
    END,
    sr."created_at",  -- When they were first invited
    sr."provider_responded_at",  -- When provider responded
    -- Set accepted_at for 'resolved' or 'accepted' status (when customer accepted the quote)
    CASE 
        WHEN sr."provider_status" = 'resolved' THEN sr."provider_responded_at"
        WHEN sr."provider_status" = 'accepted' THEN sr."provider_responded_at"
        WHEN sr."status" = 'completed' THEN sr."provider_responded_at"
        ELSE NULL
    END,
    -- Extract provider response from provider_notes JSON array
    CASE 
        WHEN sr."provider_notes" IS NOT NULL AND jsonb_array_length(sr."provider_notes") > 0
        THEN (sr."provider_notes"->0->>'content')
        ELSE NULL
    END,
    sr."reminder_sent_at",
    NULL,  -- provider_viewed_at - we don't have this data for historical records
    -- Transform lowercase resolution_status to uppercase enum values, or derive from provider_status
    CASE 
        -- First check if there's an explicit resolution_status
        WHEN sr."resolution_status" = 'completed' THEN 'COMPLETED'::"ResolutionStatus"
        WHEN sr."resolution_status" = 'cancelled' THEN 'CANCELLED'::"ResolutionStatus"
        WHEN sr."resolution_status" = 'no_response' THEN 'NO_RESPONSE'::"ResolutionStatus"
        WHEN sr."resolution_status" = 'not_viable' THEN 'NOT_VIABLE'::"ResolutionStatus"
        WHEN sr."resolution_status" = 'referred' THEN 'REFERRED'::"ResolutionStatus"
        WHEN sr."resolution_status" = 'other' THEN 'OTHER'::"ResolutionStatus"
        -- Derive resolution_status from provider_status if no explicit resolution_status
        WHEN sr."provider_status" = 'resolved' THEN 'COMPLETED'::"ResolutionStatus"
        WHEN sr."provider_status" = 'rejected' THEN 'CANCELLED'::"ResolutionStatus"
        WHEN sr."provider_status" = 'need_info' THEN 'OTHER'::"ResolutionStatus"
        WHEN sr."provider_status" = 'pending' THEN 'NO_RESPONSE'::"ResolutionStatus"
        ELSE NULL
    END,  -- Map resolution_status from old leads or derive from provider_status
    sr."resolution_notes",   -- Map resolution_notes from old leads
    sr."distance_miles",
    sr."created_at",
    sr."updated_at"
FROM "service_requests" sr
JOIN job_groups jg ON sr."id" = ANY(jg.service_request_ids)
ORDER BY jg.primary_request_id || '_job', sr."listing_id", sr."updated_at" DESC;

-- Step 3.1: Update completed quotes to set completed_at timestamp
UPDATE "quotes" 
SET "completed_at" = NOW()
WHERE "status" = 'COMPLETED'::"QuoteStatus" AND "completed_at" IS NULL;

-- Step 4: Update job accepted_quote_id and quote status for accepted quotes
-- Use the job_groups temp table to find accepted quotes
UPDATE "jobs" 
SET "accepted_quote_id" = subq.quote_id
FROM (
    SELECT DISTINCT 
        j."id" as job_id,
        q."id" as quote_id
    FROM "jobs" j
    JOIN job_groups jg ON j."id" = jg.primary_request_id || '_job'
    JOIN "quotes" q ON q."job_id" = j."id"
    JOIN "service_requests" sr ON sr."id" = ANY(jg.service_request_ids) 
        AND sr."listing_id" = q."listing_id"
    WHERE (
        -- Quote was accepted if provider status is resolved, accepted, or overall status is completed
        sr."provider_status" = 'resolved' OR sr."provider_status" = 'accepted' OR sr."status" = 'completed'
    )
    AND NOT EXISTS (
        -- Only set if there's exactly one accepted quote per job
        SELECT 1 FROM "quotes" q2 
        JOIN "service_requests" sr2 ON sr2."id" = ANY(jg.service_request_ids) 
            AND sr2."listing_id" = q2."listing_id"
        WHERE q2."job_id" = j."id" 
        AND (sr2."provider_status" = 'resolved' OR sr2."provider_status" = 'accepted' OR sr2."status" = 'completed')
        AND q2."id" != q."id"
    )
) subq
WHERE "jobs"."id" = subq.job_id;

-- Step 5: Ensure accepted quotes have the correct status (but don't override COMPLETED quotes)
UPDATE "quotes" 
SET "status" = 'ACCEPTED'::"QuoteStatus"
FROM "jobs" j
WHERE "quotes"."id" = j."accepted_quote_id"
AND "quotes"."status" NOT IN ('ACCEPTED'::"QuoteStatus", 'COMPLETED'::"QuoteStatus");

-- Step 6: Drop the service_requests table after migration
DROP TABLE IF EXISTS "service_requests" CASCADE;

-- CreateTable - Other tables
CREATE TABLE "quote_messages" (
    "id" TEXT NOT NULL,
    "quote_id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "status" "QuoteMessageStatus" NOT NULL DEFAULT 'PENDING',
    "type" "QuoteMessageType" NOT NULL DEFAULT 'TEXT',
    "attachments" TEXT[],
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sent_at" TIMESTAMP(3),
    "delivered_at" TIMESTAMP(3),
    "read_at" TIMESTAMP(3),
    "sender_type" TEXT NOT NULL,
    "user_id" TEXT,
    "listing_id" TEXT,

    CONSTRAINT "quote_messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invoices" (
    "id" TEXT NOT NULL,
    "invoice_number" SERIAL NOT NULL,
    "provider_id" TEXT NOT NULL,
    "description" TEXT,
    "amount" INTEGER NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'usd',
    "status" TEXT NOT NULL DEFAULT 'draft',
    "due_date" TIMESTAMP(3),
    "customer_name" TEXT NOT NULL,
    "customer_email" TEXT NOT NULL,
    "customer_phone" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "invoices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invoice_items" (
    "id" TEXT NOT NULL,
    "invoice_id" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "unit_price" INTEGER NOT NULL,
    "amount" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "invoice_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "transactions" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "amount_in_cents" INTEGER NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'usd',
    "status" TEXT NOT NULL,
    "stripe_payment_id" TEXT,
    "stripe_customer_id" TEXT,
    "payment_method" TEXT NOT NULL,
    "payment_method_type" TEXT,
    "payment_method_last4" TEXT,
    "payment_method_brand" TEXT,
    "payment_method_expiry" TEXT,
    "description" TEXT,
    "metadata" JSONB,
    "error_message" TEXT,
    "refund_reason" TEXT,
    "refunded_at" TIMESTAMP(3),
    "refund_amount" DOUBLE PRECISION,
    "refund_amount_cents" INTEGER,
    "user_id" TEXT NOT NULL,
    "job_id" TEXT,
    "membership_id" TEXT,
    "provider_id" TEXT,

    CONSTRAINT "transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "components" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "manufacturer" TEXT NOT NULL,
    "notes" TEXT,
    "attachments" JSONB,
    "company_id" TEXT NOT NULL,

    CONSTRAINT "components_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "companies" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "brand_color" TEXT,
    "logo_url" TEXT,
    "logo_url_alt" TEXT,
    "logo_class_name" TEXT,
    "website" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "abbreviation" TEXT,
    "search_params" JSONB,

    CONSTRAINT "companies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "warranty_requests" (
    "id" TEXT NOT NULL,
    "uuid" TEXT NOT NULL,
    "status" "WarrantyRequestStatus" NOT NULL DEFAULT 'REQUEST_CREATED',
    "complaint" TEXT NOT NULL,
    "cause" TEXT,
    "correction" TEXT,
    "estimated_hours" DOUBLE PRECISION,
    "approved_hours" DOUBLE PRECISION,
    "actual_hours" DOUBLE PRECISION,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "contact_preference" TEXT NOT NULL DEFAULT 'sms',
    "location" JSONB,
    "rv_vin" TEXT NOT NULL,
    "rv_year" TEXT,
    "rv_make" TEXT,
    "rv_model" TEXT,
    "rv_type" TEXT,
    "company_id" TEXT NOT NULL,
    "oem_user_id" TEXT NOT NULL,
    "listing_id" TEXT,
    "customer_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "email_sent_at" TIMESTAMP(3),
    "onboarding_completed_at" TIMESTAMP(3),
    "job_id" TEXT,
    "component_id" TEXT,

    CONSTRAINT "warranty_requests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "warranty_request_updates" (
    "id" TEXT NOT NULL,
    "warranty_request_id" TEXT,
    "job_id" TEXT,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by_id" TEXT NOT NULL,
    "event_type" "WarrantyRequestEventType" NOT NULL,
    "details" JSONB,

    CONSTRAINT "warranty_request_updates_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "jobs_warranty_request_id_key" ON "jobs"("warranty_request_id");
CREATE INDEX "jobs_user_id_idx" ON "jobs"("user_id");
CREATE INDEX "jobs_transaction_id_idx" ON "jobs"("transaction_id");
CREATE UNIQUE INDEX "jobs_transaction_id_key" ON "jobs"("transaction_id");
CREATE INDEX "jobs_accepted_quote_id_idx" ON "jobs"("accepted_quote_id");
CREATE UNIQUE INDEX "jobs_accepted_quote_id_key" ON "jobs"("accepted_quote_id");

-- CreateIndex
CREATE INDEX "quotes_job_id_idx" ON "quotes"("job_id");
CREATE INDEX "quotes_listing_id_idx" ON "quotes"("listing_id");
CREATE INDEX "quotes_status_idx" ON "quotes"("status");
CREATE UNIQUE INDEX "quotes_job_id_listing_id_key" ON "quotes"("job_id", "listing_id");

-- CreateIndex
CREATE INDEX "quote_messages_quote_id_idx" ON "quote_messages"("quote_id");
CREATE INDEX "quote_messages_user_id_idx" ON "quote_messages"("user_id");
CREATE INDEX "quote_messages_listing_id_idx" ON "quote_messages"("listing_id");
CREATE INDEX "quote_messages_created_at_idx" ON "quote_messages"("created_at");

-- CreateIndex
CREATE UNIQUE INDEX "invoices_invoice_number_key" ON "invoices"("invoice_number");
CREATE INDEX "invoices_provider_id_idx" ON "invoices"("provider_id");

-- CreateIndex
CREATE INDEX "invoice_items_invoice_id_idx" ON "invoice_items"("invoice_id");

-- CreateIndex
CREATE INDEX "transactions_user_id_idx" ON "transactions"("user_id");
CREATE INDEX "transactions_job_id_idx" ON "transactions"("job_id");
CREATE INDEX "transactions_membership_id_idx" ON "transactions"("membership_id");
CREATE INDEX "transactions_provider_id_idx" ON "transactions"("provider_id");
CREATE INDEX "transactions_stripe_payment_id_idx" ON "transactions"("stripe_payment_id");
CREATE INDEX "transactions_created_at_idx" ON "transactions"("created_at");

-- CreateIndex
CREATE UNIQUE INDEX "warranty_requests_uuid_key" ON "warranty_requests"("uuid");
CREATE UNIQUE INDEX "warranty_requests_job_id_key" ON "warranty_requests"("job_id");
CREATE INDEX "warranty_requests_company_id_idx" ON "warranty_requests"("company_id");
CREATE INDEX "warranty_requests_listing_id_idx" ON "warranty_requests"("listing_id");
CREATE INDEX "warranty_requests_customer_id_idx" ON "warranty_requests"("customer_id");
CREATE INDEX "warranty_requests_oem_user_id_idx" ON "warranty_requests"("oem_user_id");
CREATE INDEX "warranty_requests_component_id_idx" ON "warranty_requests"("component_id");

-- CreateIndex
CREATE INDEX "warranty_request_updates_warranty_request_id_idx" ON "warranty_request_updates"("warranty_request_id");
CREATE INDEX "warranty_request_updates_job_id_idx" ON "warranty_request_updates"("job_id");

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jobs" ADD CONSTRAINT "jobs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jobs" ADD CONSTRAINT "jobs_transaction_id_fkey" FOREIGN KEY ("transaction_id") REFERENCES "transactions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jobs" ADD CONSTRAINT "jobs_warranty_request_id_fkey" FOREIGN KEY ("warranty_request_id") REFERENCES "warranty_requests"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jobs" ADD CONSTRAINT "jobs_accepted_quote_id_fkey" FOREIGN KEY ("accepted_quote_id") REFERENCES "quotes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quotes" ADD CONSTRAINT "quotes_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "jobs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quotes" ADD CONSTRAINT "quotes_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quote_messages" ADD CONSTRAINT "quote_messages_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quote_messages" ADD CONSTRAINT "quote_messages_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quote_messages" ADD CONSTRAINT "quote_messages_quote_id_fkey" FOREIGN KEY ("quote_id") REFERENCES "quotes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "listings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoice_items" ADD CONSTRAINT "invoice_items_invoice_id_fkey" FOREIGN KEY ("invoice_id") REFERENCES "invoices"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_membership_id_fkey" FOREIGN KEY ("membership_id") REFERENCES "memberships"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "listings"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "components" ADD CONSTRAINT "components_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "warranty_requests" ADD CONSTRAINT "warranty_requests_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "warranty_requests" ADD CONSTRAINT "warranty_requests_oem_user_id_fkey" FOREIGN KEY ("oem_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "warranty_requests" ADD CONSTRAINT "warranty_requests_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "warranty_requests" ADD CONSTRAINT "warranty_requests_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "warranty_requests" ADD CONSTRAINT "warranty_requests_component_id_fkey" FOREIGN KEY ("component_id") REFERENCES "components"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "warranty_request_updates" ADD CONSTRAINT "warranty_request_updates_updated_by_id_fkey" FOREIGN KEY ("updated_by_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "warranty_request_updates" ADD CONSTRAINT "warranty_request_updates_warranty_request_id_fkey" FOREIGN KEY ("warranty_request_id") REFERENCES "warranty_requests"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "warranty_request_updates" ADD CONSTRAINT "warranty_request_updates_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "jobs"("id") ON DELETE SET NULL ON UPDATE CASCADE;
