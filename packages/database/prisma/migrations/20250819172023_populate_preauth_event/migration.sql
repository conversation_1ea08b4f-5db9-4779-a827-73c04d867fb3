-- Migration: Add PREAUTHORIZATION_APPROVED timeline events for existing warranty requests
-- This script should be run in production to backfill timeline events for analytics

-- First, let's check how many warranty requests don't have PREAUTHORIZATION_APPROVED events
SELECT 
    COUNT(*) as total_warranty_requests,
    COUNT(tu.id) as requests_with_preauth_events
FROM warranty_requests wr
LEFT JOIN timeline_updates tu ON wr.id = tu.warranty_request_id 
    AND tu.event_type = 'PREAUTHORIZATION_APPROVED'
WHERE wr.company_id IS NOT NULL;

-- Insert PREAUTHORIZATION_APPROVED timeline events for warranty requests that don't have them
-- This uses the warranty request creation date as the timeline event date
INSERT INTO timeline_updates (
    id,
    warranty_request_id,
    updated_by_id,
    event_type,
    date,
    details
)
SELECT 
    gen_random_uuid() as id,
    wr.id as warranty_request_id,
    wr.oem_user_id as updated_by_id,
    'PREAUTHORI<PERSON>ATION_APPROVED'::"TimelineEventType" as event_type,
    wr.created_at as date,
    jsonb_build_object(
        'notes', 'Preauthorization approved for warranty request',
        'approved_by', wr.oem_user_id
    ) as details
FROM warranty_requests wr
WHERE wr.company_id IS NOT NULL
    AND NOT EXISTS (
        SELECT 1 
        FROM timeline_updates tu 
        WHERE tu.warranty_request_id = wr.id 
        AND tu.event_type = 'PREAUTHORIZATION_APPROVED'
    );

-- Verify the migration was successful
SELECT 
    COUNT(*) as total_warranty_requests,
    COUNT(tu.id) as requests_with_preauth_events_after_migration
FROM warranty_requests wr
LEFT JOIN timeline_updates tu ON wr.id = tu.warranty_request_id 
    AND tu.event_type = 'PREAUTHORIZATION_APPROVED'
WHERE wr.company_id IS NOT NULL;

-- Show a sample of the created events
SELECT 
    wr.id as warranty_request_id,
    wr.rv_year,
    wr.rv_make,
    wr.rv_model,
    tu.date as preauth_date,
    wr.created_at as warranty_created_date,
    tu.details
FROM warranty_requests wr
JOIN timeline_updates tu ON wr.id = tu.warranty_request_id 
    AND tu.event_type = 'PREAUTHORIZATION_APPROVED'
LIMIT 10;
