/*
  Warnings:

  - You are about to drop the `warranty_request_updates` table. If the table is not empty, all the data it contains will be lost.

*/

-- DropForeignKey
ALTER TABLE "warranty_request_updates" DROP CONSTRAINT "warranty_request_updates_job_id_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "warranty_request_updates" DROP CONSTRAINT "warranty_request_updates_updated_by_id_fkey";

-- DropForeignKey
ALTER TABLE "warranty_request_updates" DROP CONSTRAINT "warranty_request_updates_warranty_request_id_fkey";

-- AlterTable
ALTER TABLE "provider_stats" ADD COLUMN     "abandonment_count_30d" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "abandonment_count_90d" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "abandonment_count_all_time" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "abandonment_rate_30d" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "abandonment_rate_90d" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "abandonment_rate_all_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "accepted_jobs_30d" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "accepted_jobs_90d" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "accepted_jobs_all_time" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "avg_completion_time_30d" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "avg_completion_time_90d" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "avg_completion_time_all_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "completed_jobs_30d" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "completed_jobs_90d" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "completed_jobs_all_time" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "completion_rate_30d" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "completion_rate_90d" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "completion_rate_all_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "non_response_count_30d" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "non_response_count_90d" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "non_response_count_all_time" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "non_response_rate_30d" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "non_response_rate_90d" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "non_response_rate_all_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "review_completion_rate_30d" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "review_completion_rate_90d" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "review_completion_rate_all_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "reviewed_jobs_30d" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "reviewed_jobs_90d" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "reviewed_jobs_all_time" INTEGER NOT NULL DEFAULT 0;

-- AlterTable - quotes table structure already correct from initial migration
-- ALTER TABLE "quotes" DROP COLUMN "quoted_at",
-- ADD COLUMN     "accepted_at" TIMESTAMP(3),
-- ADD COLUMN     "responded_at" TIMESTAMP(3),
-- ADD COLUMN     "reviewed_at" TIMESTAMP(3);

-- DropTable
DROP TABLE "warranty_request_updates";

-- CreateTable
CREATE TABLE "timeline_updates" (
    "id" TEXT NOT NULL,
    "warranty_request_id" TEXT,
    "job_id" TEXT,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by_id" TEXT NOT NULL,
    "event_type" "TimelineEventType" NOT NULL,
    "details" JSONB,

    CONSTRAINT "timeline_updates_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "timeline_updates_warranty_request_id_idx" ON "timeline_updates"("warranty_request_id");

-- CreateIndex
CREATE INDEX "timeline_updates_job_id_idx" ON "timeline_updates"("job_id");

-- AddForeignKey
ALTER TABLE "timeline_updates" ADD CONSTRAINT "timeline_updates_updated_by_id_fkey" FOREIGN KEY ("updated_by_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "timeline_updates" ADD CONSTRAINT "timeline_updates_warranty_request_id_fkey" FOREIGN KEY ("warranty_request_id") REFERENCES "warranty_requests"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "timeline_updates" ADD CONSTRAINT "timeline_updates_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "jobs"("id") ON DELETE SET NULL ON UPDATE CASCADE;
