-- AlterTable
ALTER TABLE "listings" ADD COLUMN     "is_highly_responsive" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "provider_stats" (
    "id" TEXT NOT NULL,
    "listing_id" TEXT NOT NULL,
    "total_leads_30d" INTEGER NOT NULL DEFAULT 0,
    "responded_leads_30d" INTEGER NOT NULL DEFAULT 0,
    "response_rate_30d" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "avg_response_time_30d" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "total_leads_90d" INTEGER NOT NULL DEFAULT 0,
    "responded_leads_90d" INTEGER NOT NULL DEFAULT 0,
    "response_rate_90d" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "avg_response_time_90d" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "total_leads_all_time" INTEGER NOT NULL DEFAULT 0,
    "responded_leads_all_time" INTEGER NOT NULL DEFAULT 0,
    "response_rate_all_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "avg_response_time_all_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "last_calculated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "provider_stats_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "provider_stats_listing_id_key" ON "provider_stats"("listing_id");

-- CreateIndex
CREATE INDEX "provider_stats_listing_id_idx" ON "provider_stats"("listing_id");

-- AddForeignKey
ALTER TABLE "provider_stats" ADD CONSTRAINT "provider_stats_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE CASCADE ON UPDATE CASCADE;
