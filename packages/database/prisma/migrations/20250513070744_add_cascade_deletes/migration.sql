-- DropFore<PERSON><PERSON>ey
ALTER TABLE "article_reads" DROP CONSTRAINT IF EXISTS "article_reads_article_id_fkey";

-- DropForeignKey
ALTER TABLE "article_reads" DROP CONSTRAINT IF EXISTS "article_reads_user_id_fkey";

-- DropForeignKey
ALTER TABLE "audit_logs" DROP CONSTRAINT IF EXISTS "audit_logs_user_id_fkey";

-- DropForeignKey
ALTER TABLE "listing_interactions" DROP CONSTRAINT IF EXISTS "listing_interactions_user_id_fkey";

-- DropForeignKey
ALTER TABLE "notifications" DROP CONSTRAINT IF EXISTS "notifications_user_id_fkey";

-- DropForeignKey
ALTER TABLE "user_journey_events" DROP CONSTRAINT IF EXISTS "user_journey_events_session_id_fkey";

-- DropForeignKey
ALTER TABLE "user_journey_events" DROP CONSTRAINT IF EXISTS "user_journey_events_user_id_fkey";

-- DropF<PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "user_journey_sessions" DROP CONSTRAINT IF EXISTS "user_journey_sessions_user_id_fkey";

-- DropForeignKey
ALTER TABLE "user_notification_preferences" DROP CONSTRAINT IF EXISTS "user_notification_preferences_user_id_fkey";

-- DropForeignKey
ALTER TABLE "user_terms_acceptances" DROP CONSTRAINT IF EXISTS "user_terms_acceptances_user_id_fkey";

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_notification_preferences" ADD CONSTRAINT "user_notification_preferences_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;


-- AddForeignKey
ALTER TABLE "listing_interactions" ADD CONSTRAINT "listing_interactions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_terms_acceptances" ADD CONSTRAINT "user_terms_acceptances_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_journey_sessions" ADD CONSTRAINT "user_journey_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_journey_events" ADD CONSTRAINT "user_journey_events_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "user_journey_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_journey_events" ADD CONSTRAINT "user_journey_events_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "article_reads" ADD CONSTRAINT "article_reads_article_id_fkey" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "article_reads" ADD CONSTRAINT "article_reads_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
