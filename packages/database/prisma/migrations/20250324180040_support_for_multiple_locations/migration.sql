/*
 Warnings:
 
 - You are about to drop the column `location_id` on the `listings` table. All the data in the column will be lost.
 
 */
-- First, update the locations table to set listing_id for all locations referenced by listings
UPDATE locations
SET listing_id = listings.id
FROM listings
WHERE locations.id = listings.location_id;
-- Cleanup locations with an invalid listing_id
DELETE from locations
WHERE listing_id IS NOT NULL
  AND listing_id NOT IN (
    SELECT id
    FROM listings
  );
-- DropForeignKey
ALTER TABLE "listings" DROP CONSTRAINT "listings_location_id_fkey";
-- DropIndex
DROP INDEX "listings_location_id_key";
-- AlterTable
ALTER TABLE "listings" DROP COLUMN "location_id";
-- AlterTable
ALTER TABLE "locations"
ADD COLUMN "default" BOOLEAN DEFAULT false,
  ADD COLUMN "description" TEXT,
  ADD COLUMN "end_date" TIMESTAMP(3),
  ADD COLUMN "start_date" TIMESTAMP(3);
-- Add<PERSON><PERSON><PERSON><PERSON>ey
ALTER TABLE "locations"
ADD CONSTRAINT "locations_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- Set default to TRUE for all existing locations
UPDATE locations
SET "default" = TRUE;