-- CreateEnum
CREATE TYPE "DispatchEmailStatus" AS ENUM ('DRAFT', 'SCHEDULED', 'SENT', 'FAILED');

-- CreateTable
CREATE TABLE "dispatch_emails" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "body" TEXT NOT NULL,
    "status" "DispatchEmailStatus" NOT NULL DEFAULT 'DRAFT',
    "sent_at" TIMESTAMP(3),
    "scheduled_for" TIMESTAMP(3),
    "campaign_url" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dispatch_emails_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "dispatch_emails_status_idx" ON "dispatch_emails"("status");

-- CreateIndex
CREATE INDEX "dispatch_emails_scheduled_for_idx" ON "dispatch_emails"("scheduled_for");
