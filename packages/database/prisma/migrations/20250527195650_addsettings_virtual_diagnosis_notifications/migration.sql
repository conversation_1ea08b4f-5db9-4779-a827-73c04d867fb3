/*
  Warnings:

  - You are about to drop the column `customer_email` on the `troubleshooting_requests` table. All the data in the column will be lost.
  - You are about to drop the column `customer_first_name` on the `troubleshooting_requests` table. All the data in the column will be lost.
  - You are about to drop the column `customer_last_name` on the `troubleshooting_requests` table. All the data in the column will be lost.
  - You are about to drop the column `customer_phone` on the `troubleshooting_requests` table. All the data in the column will be lost.
  - Added the required column `user_id` to the `troubleshooting_requests` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "troubleshooting_requests" DROP CONSTRAINT "troubleshooting_requests_listing_id_fkey";

-- DropIndex
DROP INDEX "troubleshooting_requests_listing_id_idx";

-- AlterTable
ALTER TABLE "listings" ADD COLUMN     "settings_virtual_diagnosis_notifications" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "troubleshooting_requests" DROP COLUMN "customer_email",
DROP COLUMN "customer_first_name",
DROP COLUMN "customer_last_name",
DROP COLUMN "customer_phone",
ADD COLUMN     "user_id" TEXT NOT NULL,
ALTER COLUMN "listing_id" DROP NOT NULL;

-- CreateIndex
CREATE INDEX "troubleshooting_requests_listing_id_status_idx" ON "troubleshooting_requests"("listing_id", "status");

-- AddForeignKey
ALTER TABLE "troubleshooting_requests" ADD CONSTRAINT "troubleshooting_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "troubleshooting_requests" ADD CONSTRAINT "troubleshooting_requests_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE SET NULL ON UPDATE CASCADE;
