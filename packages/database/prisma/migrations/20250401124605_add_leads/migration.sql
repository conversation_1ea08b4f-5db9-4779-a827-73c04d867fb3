-- CreateTable
CREATE TABLE "leads" (
    "id" TEXT NOT NULL,
    "listing_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "contact_preference" TEXT NOT NULL,
    "location" JSONB,
    "message" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "source" TEXT NOT NULL DEFAULT 'web',
    "rv_year" INTEGER,
    "rv_make" TEXT,
    "rv_model" TEXT,
    "rv_type" TEXT,
    "distance_miles" DOUBLE PRECISION,
    "provider_status" TEXT NOT NULL DEFAULT 'pending',
    "provider_responded_at" TIMESTAMP(3),
    "provider_notes" JSONB,
    "resolution_status" TEXT,
    "resolution_notes" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "sent_at" TIMESTAMP(3),
    "error_message" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "leads_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "leads_listing_id_idx" ON "leads"("listing_id");

-- CreateIndex
CREATE INDEX "leads_status_idx" ON "leads"("status");

-- CreateIndex
CREATE INDEX "leads_provider_status_idx" ON "leads"("provider_status");

-- CreateIndex
CREATE INDEX "leads_user_id_idx" ON "leads"("user_id");

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leads" ADD CONSTRAINT "leads_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
