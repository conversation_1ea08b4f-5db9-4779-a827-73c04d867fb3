-- DropForeignKey
ALTER TABLE "timeline_updates" DROP CONSTRAINT "timeline_updates_updated_by_id_fkey";

-- AlterTable
ALTER TABLE "timeline_updates" ALTER COLUMN "updated_by_id" DROP NOT NULL;

-- AlterTable
ALTER TABLE "warranty_requests" ADD COLUMN     "notes_to_provider" TEXT;

-- AddForeignKey
ALTER TABLE "timeline_updates" ADD CONSTRAINT "timeline_updates_updated_by_id_fkey" FOREIGN KEY ("updated_by_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
