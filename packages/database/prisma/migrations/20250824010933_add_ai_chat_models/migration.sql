-- Create<PERSON><PERSON>
CREATE TYPE "public"."AIChatConversationStatus" AS ENUM ('ACTIVE', 'ARCHIVED', 'DELETED');

-- CreateTable
CREATE TABLE "public"."chat_conversations" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "title" TEXT,
    "status" "public"."AIChatConversationStatus" NOT NULL DEFAULT 'ACTIVE',
    "rv_make" TEXT,
    "rv_model" TEXT,
    "rv_year" INTEGER,
    "rv_type" TEXT,
    "current_latitude" DOUBLE PRECISION,
    "current_longitude" DOUBLE PRECISION,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "last_message_at" TIMESTAMP(3),

    CONSTRAINT "chat_conversations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."chat_messages" (
    "id" TEXT NOT NULL,
    "conversation_id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "is_user_message" BOOLEAN NOT NULL,
    "suggestions" JSONB,
    "related_resources" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "chat_messages_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "chat_conversations_user_id_idx" ON "public"."chat_conversations"("user_id");

-- CreateIndex
CREATE INDEX "chat_conversations_status_idx" ON "public"."chat_conversations"("status");

-- CreateIndex
CREATE INDEX "chat_conversations_created_at_idx" ON "public"."chat_conversations"("created_at");

-- CreateIndex
CREATE INDEX "chat_messages_conversation_id_idx" ON "public"."chat_messages"("conversation_id");

-- CreateIndex
CREATE INDEX "chat_messages_created_at_idx" ON "public"."chat_messages"("created_at");

-- CreateIndex
CREATE INDEX "chat_messages_is_user_message_idx" ON "public"."chat_messages"("is_user_message");

-- AddForeignKey
ALTER TABLE "public"."chat_conversations" ADD CONSTRAINT "chat_conversations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."chat_messages" ADD CONSTRAINT "chat_messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."chat_conversations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
