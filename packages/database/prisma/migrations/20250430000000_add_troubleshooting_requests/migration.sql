-- CreateTable
CREATE TABLE "troubleshooting_requests" (
    "id" TEXT NOT NULL,
    "customer_first_name" TEXT NOT NULL,
    "customer_last_name" TEXT NOT NULL,
    "customer_email" TEXT NOT NULL,
    "customer_phone" TEXT NOT NULL,
    "issue_description" TEXT NOT NULL,
    "has_photo" BOOLEAN NOT NULL DEFAULT false,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "provider_notes" JSONB,
    "scheduled_for" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "listing_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "troubleshooting_requests_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "troubleshooting_requests_listing_id_idx" ON "troubleshooting_requests"("listing_id");

-- CreateIndex
CREATE INDEX "troubleshooting_requests_status_idx" ON "troubleshooting_requests"("status");

-- AddF<PERSON>ign<PERSON><PERSON>
ALTER TABLE "troubleshooting_requests" ADD CONSTRAINT "troubleshooting_requests_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE RESTRICT ON UPDATE CASCADE; 