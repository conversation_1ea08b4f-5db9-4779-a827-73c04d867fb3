-- Create<PERSON><PERSON>
CREATE TYPE "MarketingCampaignStatus" AS ENUM ('DRAFT', 'ACTIVE', 'PAUSED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "DiscountType" AS ENUM ('PERCENTAGE', 'FIXED_AMOUNT');

-- CreateTable
CREATE TABLE "marketing_campaigns" (
    "id" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "discount_type" "DiscountType" NOT NULL,
    "discount_value" DOUBLE PRECISION NOT NULL,
    "status" "MarketingCampaignStatus" NOT NULL DEFAULT 'DRAFT',
    "expires_at" TIMESTAMP(3),
    "page_title" TEXT,
    "page_subtitle" TEXT,
    "page_description" TEXT,
    "button_text" TEXT DEFAULT 'Get My Discount',
    "success_message" TEXT,
    "email_subject" TEXT,
    "email_template" TEXT,
    "views_count" INTEGER NOT NULL DEFAULT 0,
    "leads_count" INTEGER NOT NULL DEFAULT 0,
    "conversions_count" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "marketing_campaigns_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "marketing_campaign_leads" (
    "id" TEXT NOT NULL,
    "campaign_id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "first_name" TEXT,
    "last_name" TEXT,
    "coupon_code" TEXT NOT NULL,
    "coupon_sent_at" TIMESTAMP(3),
    "coupon_used_at" TIMESTAMP(3),
    "stripe_coupon_id" TEXT,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "referrer" TEXT,
    "utm_source" TEXT,
    "utm_medium" TEXT,
    "utm_campaign" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "marketing_campaign_leads_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "marketing_campaigns_slug_key" ON "marketing_campaigns"("slug");

-- CreateIndex
CREATE INDEX "marketing_campaigns_slug_idx" ON "marketing_campaigns"("slug");

-- CreateIndex
CREATE INDEX "marketing_campaigns_status_idx" ON "marketing_campaigns"("status");

-- CreateIndex
CREATE INDEX "marketing_campaigns_expires_at_idx" ON "marketing_campaigns"("expires_at");

-- CreateIndex
CREATE UNIQUE INDEX "marketing_campaign_leads_coupon_code_key" ON "marketing_campaign_leads"("coupon_code");

-- CreateIndex
CREATE INDEX "marketing_campaign_leads_campaign_id_idx" ON "marketing_campaign_leads"("campaign_id");

-- CreateIndex
CREATE INDEX "marketing_campaign_leads_email_idx" ON "marketing_campaign_leads"("email");

-- CreateIndex
CREATE INDEX "marketing_campaign_leads_coupon_code_idx" ON "marketing_campaign_leads"("coupon_code");

-- CreateIndex
CREATE INDEX "marketing_campaign_leads_coupon_used_at_idx" ON "marketing_campaign_leads"("coupon_used_at");

-- AddForeignKey
ALTER TABLE "marketing_campaign_leads" ADD CONSTRAINT "marketing_campaign_leads_campaign_id_fkey" FOREIGN KEY ("campaign_id") REFERENCES "marketing_campaigns"("id") ON DELETE CASCADE ON UPDATE CASCADE;
