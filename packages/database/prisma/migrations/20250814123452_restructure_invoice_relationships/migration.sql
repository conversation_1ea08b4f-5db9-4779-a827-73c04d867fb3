-- Migration: Restructure invoice relationships
-- This migration:
-- 1. Adds new columns to Job and WarrantyRequest tables
-- 2. Migrates existing data from warranty_request_id on invoices to new structure
-- 3. Sets up indexes for the new structure
-- Note: The old warranty_request_id column will be removed in a later migration after testing

-- Step 1: Add new columns to Job table
ALTER TABLE "jobs" ADD COLUMN IF NOT EXISTS "invoice_id" TEXT;

-- Step 2: Add new columns to WarrantyRequest table  
ALTER TABLE "warranty_requests" ADD COLUMN IF NOT EXISTS "provider_invoice_id" TEXT;
ALTER TABLE "warranty_requests" ADD COLUMN IF NOT EXISTS "platform_invoice_id" TEXT;

-- Step 3: Create indexes for new columns (only if they don't exist)
CREATE UNIQUE INDEX IF NOT EXISTS "jobs_invoice_id_key" ON "jobs"("invoice_id");
CREATE INDEX IF NOT EXISTS "jobs_invoice_id_idx" ON "jobs"("invoice_id");
CREATE UNIQUE INDEX IF NOT EXISTS "warranty_requests_provider_invoice_id_key" ON "warranty_requests"("provider_invoice_id");
CREATE UNIQUE INDEX IF NOT EXISTS "warranty_requests_platform_invoice_id_key" ON "warranty_requests"("platform_invoice_id");
CREATE INDEX IF NOT EXISTS "warranty_requests_provider_invoice_id_idx" ON "warranty_requests"("provider_invoice_id");
CREATE INDEX IF NOT EXISTS "warranty_requests_platform_invoice_id_idx" ON "warranty_requests"("platform_invoice_id");

-- Step 4: Migrate existing data
-- Move existing warranty invoices to provider_invoice_id AND update the warranty.job.invoice_id
UPDATE "warranty_requests" 
SET "provider_invoice_id" = (
    SELECT "id" 
    FROM "invoices" 
    WHERE "invoices"."warranty_request_id" = "warranty_requests"."id"
)
WHERE EXISTS (
    SELECT 1 
    FROM "invoices" 
    WHERE "invoices"."warranty_request_id" = "warranty_requests"."id"
);

-- Update the related job's invoice_id to match the warranty request's provider_invoice_id
UPDATE "jobs" 
SET "invoice_id" = (
    SELECT "provider_invoice_id" 
    FROM "warranty_requests" 
    WHERE "warranty_requests"."job_id" = "jobs"."id"
    AND "warranty_requests"."provider_invoice_id" IS NOT NULL
)
WHERE EXISTS (
    SELECT 1 
    FROM "warranty_requests" 
    WHERE "warranty_requests"."job_id" = "jobs"."id"
    AND "warranty_requests"."provider_invoice_id" IS NOT NULL
);

-- Step 5: Add foreign key constraints for new relationships (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'jobs_invoice_id_fkey' 
        AND table_name = 'jobs'
    ) THEN
        ALTER TABLE "jobs" ADD CONSTRAINT "jobs_invoice_id_fkey" 
        FOREIGN KEY ("invoice_id") REFERENCES "invoices"("id") ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'warranty_requests_provider_invoice_id_fkey' 
        AND table_name = 'warranty_requests'
    ) THEN
        ALTER TABLE "warranty_requests" ADD CONSTRAINT "warranty_requests_provider_invoice_id_fkey" 
        FOREIGN KEY ("provider_invoice_id") REFERENCES "invoices"("id") ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'warranty_requests_platform_invoice_id_fkey' 
        AND table_name = 'warranty_requests'
    ) THEN
        ALTER TABLE "warranty_requests" ADD CONSTRAINT "warranty_requests_platform_invoice_id_fkey" 
        FOREIGN KEY ("platform_invoice_id") REFERENCES "invoices"("id") ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
END $$;


-- remove warranty_request_id from invoices
ALTER TABLE "invoices" DROP COLUMN IF EXISTS "warranty_request_id";