/*
  Warnings:

  - You are about to drop the `leads` table. If the table is not empty, all the data it contains will be lost.

*/

-- Drop dependent foreign key constraints first
ALTER TABLE "service_quotes" DROP CONSTRAINT IF EXISTS "service_quotes_request_id_fkey";
ALTER TABLE "request_notifications" DROP CONSTRAINT IF EXISTS "request_notifications_request_id_fkey";

-- Drop the existing service_requests table and its indexes
DROP TABLE IF EXISTS "service_requests" CASCADE;

-- Drop<PERSON>ore<PERSON><PERSON>ey from leads table
ALTER TABLE "leads" DROP CONSTRAINT IF EXISTS "leads_listing_id_fkey";
ALTER TABLE "leads" DROP CONSTRAINT IF EXISTS "leads_user_id_fkey";

-- RenameTable
ALTER TABLE "leads" RENAME TO "service_requests";

-- AddForeignKey
ALTER TABLE "service_requests" ADD CONSTRAINT "service_requests_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "service_requests" ADD CONSTRAINT "service_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- CreateIndex
CREATE INDEX "service_requests_listing_id_idx" ON "service_requests"("listing_id");

-- CreateIndex
CREATE INDEX "service_requests_user_id_idx" ON "service_requests"("user_id");

-- CreateIndex
CREATE INDEX "service_requests_status_idx" ON "service_requests"("status");

-- CreateIndex
CREATE INDEX "service_requests_provider_status_idx" ON "service_requests"("provider_status");

-- AlterTable
ALTER TABLE "service_requests" RENAME CONSTRAINT "leads_pkey" TO "service_requests_pkey";
