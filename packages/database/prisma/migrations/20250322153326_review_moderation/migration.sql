/*
  Warnings:

  - You are about to drop the column `is_soft_deleted` on the `reviews` table. All the data in the column will be lost.
  - You are about to drop the column `rejection_reason` on the `reviews` table. All the data in the column will be lost.

*/
-- DropForeign<PERSON>ey
-- if key exists, drop it
ALTER TABLE "reviews" DROP CONSTRAINT IF EXISTS "reviews_listing_id_fkey";

-- DropIndex
DROP INDEX IF EXISTS "reviews_listing_id_idx";

-- AlterTable
ALTER TABLE "reviews" DROP COLUMN "is_soft_deleted",
DROP COLUMN "rejection_reason",
ADD COLUMN     "moderation_notes" TEXT,
ALTER COLUMN "status" SET DEFAULT 'active';

-- convert all existing reviews from approved to active
UPDATE "reviews" SET "status" = 'active' WHERE "status" = 'approved';

-- AddForeignKey
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
