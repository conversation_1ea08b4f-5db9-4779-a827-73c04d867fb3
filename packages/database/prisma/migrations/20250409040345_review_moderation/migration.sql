/*
  Warnings:

  - The values [SAFETY_AND_COMPLIANCE,MECHANICAL_SERVICES,TIRES_AND_WHEELS,EXTERIOR_SERVICES,INTERIOR_SERVICES,PLUMBING_AND_ELECTRICAL,SEASONAL_SERVICES,ADDITIONAL_SERVICES] on the enum `ServiceCategory` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `verified` on the `reviews` table. All the data in the column will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "ServiceCategory_new" AS ENUM ('RV_REPAIR', 'RV_INSPECTION', 'RV_MAINTENANCE', 'RV_WINTERIZATION', 'RV_DETAILING', 'RV_UPGRADES', 'RV_EMERGENCY', 'RV_OTHER');
ALTER TYPE "ServiceCategory" RENAME TO "ServiceCategory_old";
ALTER TYPE "ServiceCategory_new" RENAME TO "ServiceCategory";
DROP TYPE "ServiceCategory_old";
COMMIT;

-- AlterTable
ALTER TABLE "reviews" DROP COLUMN "verified",
ADD COLUMN     "service_category" TEXT,
ALTER COLUMN "status" SET DEFAULT 'draft';
