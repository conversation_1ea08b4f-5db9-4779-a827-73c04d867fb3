/*
  Warnings:

  - The `status` column on the `invoices` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - A unique constraint covering the columns `[warranty_request_id]` on the table `invoices` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "InvoiceStatus" AS ENUM ('DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED');

-- AlterTable
ALTER TABLE "invoices" ADD COLUMN     "warranty_request_id" TEXT,
DROP COLUMN "status",
ADD COLUMN     "status" "InvoiceStatus" NOT NULL DEFAULT 'DRAFT';

-- CreateIndex
CREATE UNIQUE INDEX "invoices_warranty_request_id_key" ON "invoices"("warranty_request_id");

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_warranty_request_id_fkey" FOREIGN KEY ("warranty_request_id") REFERENCES "warranty_requests"("id") ON DELETE SET NULL ON UPDATE CASCADE;
