{"name": "@rvhelp/database", "version": "1.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "pnpm run db:generate && tsc", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "prisma db seed", "db:push": "prisma db push", "db:reset": "prisma migrate reset", "db:status": "prisma migrate status", "prepare": "pnpm run db:generate"}, "dependencies": {"@prisma/client": "6.13.0"}, "devDependencies": {"@types/node": "^22.7.7", "prisma": "6.13.0", "typescript": "^5.6.3"}, "prisma": {"schema": "./prisma/schema.prisma", "seed": "tsx prisma/seed.ts"}}