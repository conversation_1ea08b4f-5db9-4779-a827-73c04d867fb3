import { PrismaClient, SafelistType } from "@rvhelp/database";

export interface SafelistServiceConfig {
    isDevelopment: boolean;
    formatToE164: (phone: string | undefined) => string;
}

export class SafelistService {
    constructor(
        private prisma: PrismaClient,
        private config: SafelistServiceConfig
    ) { }

    async addEntry(data: {
        type: SafelistType;
        value: string;
        createdBy?: string;
        notes?: string;
        expiresAt?: string | Date | null;
    }) {
        const value =
            data.type === 'PHONE'
                ? this.config.formatToE164(data.value)
                : data.value.toLowerCase();

        return await this.prisma.safelistEntry.create({
            data: {
                type: data.type,
                value,
                created_by: data.createdBy,
                notes: data.notes || null,
                expires_at:
                    data.expiresAt && data.expiresAt !== ''
                        ? new Date(data.expiresAt)
                        : null,
            },
        });
    }

    async removeEntry(id: string) {
        return await this.prisma.safelistEntry.delete({
            where: { id },
        });
    }

    async isAllowed(type: SafelistType, value: string): Promise<boolean> {
        // Always allow in production
        if (!this.config.isDevelopment) {
            return true;
        }

        if (!value) return false;

        const normalizedValue =
            type === 'PHONE'
                ? this.config.formatToE164(value).replace(/^\+1/, '+') // Remove leading +1 for US numbers
                : value.toLowerCase();

        // For emails, also check domain
        if (type === 'EMAIL') {
            const domain = normalizedValue.split('@')[1];
            const domainAllowed = await this.isAllowed('DOMAIN', domain);
            if (domainAllowed) return true;
        }

        const entry = await this.prisma.safelistEntry.findFirst({
            where: {
                type,
                value: normalizedValue,
                is_active: true,
                OR: [{ expires_at: null }, { expires_at: { gt: new Date() } }],
            },
        });

        return !!entry;
    }

    async listEntries(type?: SafelistType) {
        return await this.prisma.safelistEntry.findMany({
            where: type ? { type } : undefined,
            orderBy: { created_at: 'desc' },
        });
    }

    // Static method for backwards compatibility when used without instance
    static async isAllowedStatic(
        prisma: PrismaClient,
        config: SafelistServiceConfig,
        type: SafelistType,
        value: string
    ): Promise<boolean> {
        const service = new SafelistService(prisma, config);
        return service.isAllowed(type, value);
    }
}
