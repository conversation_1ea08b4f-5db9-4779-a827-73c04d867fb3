import { PrismaClient } from "@rvhelp/database";
import { Twi<PERSON> } from "twilio";
import type { MessageInstance } from "twilio/lib/rest/api/v2010/account/message";
import { SafelistService, SafelistServiceConfig } from "./safelist.service";

export interface SmsServiceConfig {
    twilio: {
        accountSid?: string;
        authToken?: string;
        providerFromNumber: string;
        userFromNumber: string;
    };
    isDevelopment: boolean;
    formatToE164: (phone: string | undefined) => string;
}

type DevModeResponse = {
    success: boolean;
    blocked: boolean;
    error: string;
};

export interface SmsService {
    send(to: string, message: string): Promise<MessageInstance | DevModeResponse>;
    sendToProvider(to: string, message: string): Promise<MessageInstance | DevModeResponse>;
    sendToUser(to: string, message: string): Promise<MessageInstance | DevModeResponse>;
}

export class SharedSmsService implements SmsService {
    public client: Twilio | null = null;
    private safelistService: SafelistService;
    private config: SmsServiceConfig;
    private prisma: PrismaClient;

    constructor(prisma: PrismaClient, config: SmsServiceConfig) {
        this.prisma = prisma;
        this.config = config;

        if (config.twilio.accountSid && config.twilio.authToken) {
            this.client = new Twilio(
                config.twilio.accountSid,
                config.twilio.authToken
            );
        }

        // Initialize safelist service
        const safelistConfig: SafelistServiceConfig = {
            isDevelopment: config.isDevelopment,
            formatToE164: config.formatToE164
        };
        this.safelistService = new SafelistService(prisma, safelistConfig);
    }

    // General send method - defaults to provider sends for backward compatibility
    async send(
        to: string,
        message: string
    ): Promise<MessageInstance | DevModeResponse> {
        return this.sendToProvider(to, message);
    }

    // Send SMS to providers (businesses/service providers)
    async sendToProvider(
        to: string,
        message: string
    ): Promise<MessageInstance | DevModeResponse> {
        return this._sendSMS(to, message, this.config.twilio.providerFromNumber);
    }

    // Send SMS to users (customers/end users)
    async sendToUser(
        to: string,
        message: string
    ): Promise<MessageInstance | DevModeResponse> {
        // Use user-specific phone number if configured, otherwise fall back to provider number
        const fromNumber = this.config.twilio.userFromNumber;
        return this._sendSMS(to, message, fromNumber);
    }

    // Private method to handle the actual SMS sending logic
    private async _sendSMS(
        to: string,
        message: string,
        fromNumber: string
    ): Promise<MessageInstance | DevModeResponse> {
        if (!this.client) {
            throw new Error("SMS service not configured");
        }

        const formattedNumber = this.config.formatToE164(to);

        // Check safelist in development
        if (this.config.isDevelopment) {
            const isAllowed = await this.safelistService.isAllowed(
                "PHONE",
                formattedNumber
            );
            if (!isAllowed) {
                console.warn(
                    `SMS sending blocked in development mode. Number not in safelist: ${formattedNumber}`
                );
                return {
                    success: false,
                    blocked: true,
                    error:
                        "SMS blocked - number not in safelist for development environment"
                };
            }
        }

        const twilio = this.client;
        if (!twilio) {
            throw new Error("SMS service not configured");
        }

        return twilio.messages.create({
            to: formattedNumber,
            from: fromNumber,
            body: message
        });
    }
}
