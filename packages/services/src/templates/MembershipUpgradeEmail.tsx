import { Container, Heading, Text } from "@react-email/components";
import React from "react";
import { BaseEmail } from "./BaseEmail";
import { emailStyles } from "./shared-styles";

interface MembershipUpgradeEmailProps {
	name: string;
	membershipLevel: string;
}

export const MembershipUpgradeEmail = ({
	name,
	membershipLevel
}: MembershipUpgradeEmailProps): React.ReactElement => {
	const previewText = `Your RV Help membership has been upgraded to ${membershipLevel}!`;

	return (
		<BaseEmail previewText={previewText}>
			<Container style={emailStyles.container}>
				<Heading style={emailStyles.heading}>Membership Upgraded!</Heading>

				<Text style={emailStyles.text}>Hi {name},</Text>

				<Text style={emailStyles.text}>
					Your RV Help membership has been successfully upgraded to{" "}
					{membershipLevel}!
				</Text>

				<Text style={emailStyles.text}>
					You now have access to all the features and benefits of your new
					membership level. Visit your dashboard to explore your enhanced
					capabilities.
				</Text>

				<Text style={emailStyles.text}>
					If you have any questions about your membership or need assistance,
					our support team is here to help.
				</Text>

				<Text style={emailStyles.footer}>
					Best regards,
					<br />
					The RV Help Team
				</Text>
			</Container>
		</BaseEmail>
	);
};
