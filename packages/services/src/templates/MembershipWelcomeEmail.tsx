import {
	But<PERSON>,
	Container,
	<PERSON><PERSON>,
	Hr,
	<PERSON>,
	Section,
	Text
} from "@react-email/components";
import React from "react";
import { BaseEmail } from "./BaseEmail";

interface MembershipWelcomeEmailProps {
	name: string;
	setupPasswordUrl: string;
	membershipLevel: string;
}

const MEMBERSHIP_BENEFITS = {
	STANDARD: [
		"Unlimited pre-service diagnostic calls",
		"Nationwide provider discounts",
		"Unlimited service requests",
		"Maintenance tracker",
		"Access to traveling techs"
	],
	PREMIUM: [
		"Everything in Standard membership",
		"3 free virtual diagnostic sessions",
		"Priority phone support",
		"Early access to premium guides and resources",
		"Pro maintenance tracker",
		"Emergency dispatch to nearest 20 providers"
	]
};

export const MembershipWelcomeEmail = ({
	name,
	setupPasswordUrl,
	membershipLevel
}: MembershipWelcomeEmailProps): React.ReactElement => {
	const previewText = `Welcome to RV Help ${membershipLevel} Membership!`;
	const benefits =
		MEMBERSHIP_BENEFITS[membershipLevel as keyof typeof MEMBERSHIP_BENEFITS] ||
		MEMBERSHIP_BENEFITS.STANDARD;
	const discount = membershipLevel === "PREMIUM" ? "$100" : "$50";

	return (
		<BaseEmail previewText={previewText}>
			<Container>
				<Heading className="text-2xl font-bold text-center text-[#437F6B] mb-6">
					Welcome to RV Help {membershipLevel} Membership!
				</Heading>

				<Text className="text-gray-700 text-lg mb-4">Hi {name},</Text>

				<Text className="text-gray-700 mb-4">
					Welcome to your new {membershipLevel} membership! We're excited to
					have you as part of the RV Help community.
				</Text>

				<Section className="bg-green-50 p-4 rounded-lg mb-6">
					<Text className="font-semibold text-gray-900 mb-3">
						Your membership includes:
					</Text>
					<ul className="text-gray-700 space-y-1">
						{benefits.map((benefit, index) => (
							<li key={index}>• {benefit}</li>
						))}
					</ul>
				</Section>

				<Text className="text-gray-700 mb-4">
					To get started, please set up your password to access your member
					dashboard:
				</Text>

				<Button
					className="bg-[#437F6B] text-white py-3 px-6 text-base rounded-lg font-medium"
					href={setupPasswordUrl}
				>
					Set Up Your Password
				</Button>

				<Hr className="my-6" />

				<Text className="text-gray-700 mb-4">
					As a {membershipLevel} member, you'll save an average of {discount}{" "}
					per service call!
				</Text>

				<Text className="text-gray-700">
					Questions? Contact our member support team at{" "}
					<Link href="mailto:<EMAIL>" className="text-[#437F6B]">
						<EMAIL>
					</Link>
				</Text>

				<Text className="text-gray-700 mt-6">
					Welcome aboard!
					<br />
					The RV Help Team
				</Text>
			</Container>
		</BaseEmail>
	);
};
