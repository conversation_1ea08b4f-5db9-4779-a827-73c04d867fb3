// Copy from apps/web/components/email-templates/shared-styles.ts
export const emailStyles = {
    container: {
        margin: "0 auto",
        padding: "20px 0 48px",
        width: "580px",
    },
    logo: {
        margin: "0 auto",
    },
    paragraph: {
        fontSize: "16px",
        lineHeight: "26px",
    },
    button: {
        backgroundColor: "#437F6B",
        borderRadius: "8px",
        color: "#fff",
        fontSize: "16px",
        textDecoration: "none",
        textAlign: "center" as const,
        display: "block",
        padding: "12px 20px",
        fontWeight: "600",
    },
    buttonSecondary: {
        backgroundColor: "#F1AC5B",
        borderRadius: "8px",
        color: "#fff",
        fontSize: "16px",
        textDecoration: "none",
        textAlign: "center" as const,
        display: "block",
        padding: "12px 20px",
        fontWeight: "600",
    },
    hr: {
        borderColor: "#cccccc",
        margin: "20px 0",
    },
    footer: {
        color: "#8898aa",
        fontSize: "12px",
        textAlign: "center" as const,
    },
    heading: {
        color: "#333333",
        fontSize: "24px",
        fontWeight: "bold",
        textAlign: "center" as const,
        margin: "30px 0",
    },
    subheading: {
        color: "#333333",
        fontSize: "18px",
        fontWeight: "600",
        margin: "20px 0 10px 0",
    },
    text: {
        color: "#333333",
        fontSize: "16px",
        lineHeight: "24px",
        margin: "16px 0",
    },
    list: {
        color: "#333333",
        fontSize: "16px",
        lineHeight: "24px",
        paddingLeft: "20px",
        margin: "16px 0",
    },
    listItem: {
        margin: "8px 0",
    },
    centered: {
        textAlign: "center" as const,
        margin: "30px 0",
    },
    highlight: {
        backgroundColor: "#f4f4f4",
        padding: "16px",
        borderRadius: "8px",
        margin: "16px 0",
    },
    callout: {
        backgroundColor: "#e8f5e8",
        border: "1px solid #437F6B",
        borderRadius: "8px",
        padding: "16px",
        margin: "16px 0",
    },
};
