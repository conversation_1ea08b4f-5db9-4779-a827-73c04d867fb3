import { Button, Heading, Section, Text } from "@react-email/components";
import { BaseEmail } from "./BaseEmail";
import { emailStyles } from "./shared-styles";

export function UserWelcomeEmail({ firstName }: { firstName?: string }) {
	const greeting = firstName ? `Hi ${firstName}` : "Welcome";
	const previewText =
		"Welcome to RV Help - Your trusted partner for RV services";

	return (
		<BaseEmail previewText={previewText}>
			<Section style={emailStyles.container}>
				<Heading style={emailStyles.heading}>
					{greeting}, your RV journey just got easier!
				</Heading>

				<Text style={emailStyles.text}>
					Thank you for joining RV Help! We&apos;re here to connect you with
					trusted, certified RV technicians and inspectors across North America.
					Whether you need emergency repairs, routine maintenance, or a
					pre-purchase inspection, you&apos;re in the right place.
				</Text>

				<Text style={emailStyles.text}>
					Here&apos;s what you can do with your verified account:
				</Text>

				<ul style={emailStyles.list}>
					<li style={emailStyles.listItem}>
						Find RVTAA-certified mobile technicians who come to your location
					</li>
					<li style={emailStyles.listItem}>
						Book pre-purchase inspections with certified NRVIA inspectors
					</li>
					<li style={emailStyles.listItem}>
						Manage your RV service history and maintenance records (coming soon)
					</li>
					<li style={emailStyles.listItem}>
						Communicate directly with service providers through our platform
						(coming soon)
					</li>
				</ul>

				<Text style={emailStyles.subheading}>So what are you waiting for?</Text>

				<Section style={emailStyles.centered}>
					<Button
						style={emailStyles.button}
						href={"https://rvhelp.com/mobile-rv-repair"}
					>
						Find Mobile Techs
					</Button>

					<Button
						style={{
							...emailStyles.buttonSecondary,
							backgroundColor: "#F1AC5B",
							color: "#ffffff",
							marginLeft: "12px"
						}}
						href={"https://rvhelp.com/rv-inspection"}
					>
						Find Inspectors
					</Button>
				</Section>

				<Text style={emailStyles.text}>
					If you have any questions, our team is here to help. Just reply to
					this email or contact us at{" "}
					<a href="mailto:<EMAIL>" style={{ color: "#437F6B" }}>
						<EMAIL>
					</a>
					.
				</Text>

				<Text style={emailStyles.text}>
					Happy camping,
					<br />
					The RV Help Team
				</Text>
			</Section>
		</BaseEmail>
	);
}
