import { But<PERSON>, Section, Text } from "@react-email/components";
import { BaseEmail } from "./BaseEmail";

interface PasswordResetEmailProps {
	resetLink: string;
}

export const passwordResetText = (resetLink: string) => `Hi there,

Someone requested a password reset for your account. If this wasn't you, please ignore this email.

To reset your password, click the link below:
${resetLink}

This link will expire in 24 hours. If you need to request a new password reset link, visit the forgot password page.`;

export function PasswordResetEmail({ resetLink }: PasswordResetEmailProps) {
	return (
		<BaseEmail previewText="Reset your password">
			<Section>
				<Text>Hi there,</Text>
				<Text>
					Someone requested a password reset for your account. If this
					wasn&apos;t you, please ignore this email.
				</Text>
				<Button
					className="bg-[#437F6B] mt-2 text-white py-2 px-6 text-sm rounded-lg"
					href={resetLink}
				>
					Reset Password
				</Button>
				<Text>
					This link will expire in 24 hours. If you need to request a new
					password reset link, visit the forgot password page.
				</Text>
			</Section>
		</BaseEmail>
	);
}
