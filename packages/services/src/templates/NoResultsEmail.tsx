import { BaseEmail } from "./BaseEmail";

interface NoResultsEmailProps {
	category: string;
	name: string;
	email: string;
	phone: string;
	location: string;
	rvDetails: string;
	description?: string;
}

export function NoResultsEmail({
	category,
	name,
	email,
	phone,
	location,
	rvDetails,
	description
}: NoResultsEmailProps) {
	const providerType = category === "rv-repair" ? "Technicians" : "Inspectors";
	return (
		<BaseEmail previewText="New Service Request - No Results Found">
			<h2 className="text-xl font-semibold mb-6">
				New Service Request - No {providerType} Found
			</h2>
			<div style={{ marginTop: "24px" }}>
				<table className="w-full" cellPadding="8">
					<tbody>
						<tr>
							<td className="text-gray-600 w-1/3">Name:</td>
							<td>{name}</td>
						</tr>
						<tr>
							<td className="text-gray-600">Email:</td>
							<td>{email}</td>
						</tr>
						<tr>
							<td className="text-gray-600">Phone:</td>
							<td>{phone}</td>
						</tr>
						<tr>
							<td className="text-gray-600">Location:</td>
							<td>{location}</td>
						</tr>
						<tr>
							<td className="text-gray-600">RV Details:</td>
							<td>{rvDetails}</td>
						</tr>
						{description && (
							<tr>
								<td className="text-gray-600">Additional Details:</td>
								<td>{description}</td>
							</tr>
						)}
					</tbody>
				</table>
			</div>
		</BaseEmail>
	);
}
