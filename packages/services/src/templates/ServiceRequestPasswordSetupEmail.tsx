import { But<PERSON>, Heading, Hr, Section, Text } from "@react-email/components";
import { BaseEmail } from "./BaseEmail";

interface ServiceRequestPasswordSetupEmailProps {
	firstName: string;
	lastName: string;
	serviceRequestId: string;
	passwordSetupLink: string;
	rvDetails?: {
		year?: string;
		make?: string;
		model?: string;
		type?: string;
	};
	category?: string;
	message?: string;
}

export const serviceRequestPasswordSetupText = (
	firstName: string,
	lastName: string,
	serviceRequestId: string,
	passwordSetupLink: string,
	rvDetails?: any,
	category?: string
) => `Hi ${firstName},

You have a service request for your RV that needs your attention!

${
	rvDetails
		? `RV Details:
${rvDetails.year || ""} ${rvDetails.make || ""} ${rvDetails.model || ""}
Service Category: ${category || "RV Service"}`
		: ""
}

To access your service request workroom and manage your repair request, you'll need to set up your password first.

Set up your password here: ${passwordSetupLink}

From your service request workroom, you'll be able to:
- Message providers directly about your repair needs
- Invite additional providers to quote your job
- View and compare detailed quotes
- Track your repair progress in real-time
- Schedule service appointments
- Access your complete service history

This link will expire in 24 hours. If you need a new password setup link, please contact support.

Need help? Just reply to this email or contact <NAME_EMAIL>.

Thanks for choosing RV Help!

The RV Help Team`;

export default function ServiceRequestPasswordSetupEmail({
	firstName,
	lastName,
	serviceRequestId,
	passwordSetupLink,
	rvDetails,
	category,
	message
}: ServiceRequestPasswordSetupEmailProps) {
	return (
		<BaseEmail previewText="Set up your password to access your RV service request">
			<Section>
				<Heading className="text-xl font-bold text-gray-900 mb-4">
					Complete Your Service Request Setup
				</Heading>

				<Text className="text-gray-700 mb-4">Hi {firstName},</Text>

				<Text className="text-gray-700 mb-4">
					You have a service request for your RV that needs your attention!
				</Text>

				{rvDetails && (
					<Section className="bg-gray-50 p-4 rounded-lg mb-4">
						<Text className="font-semibold text-gray-900 mb-2">
							RV Details:
						</Text>
						<Text className="text-gray-700 mb-1">
							{rvDetails.year && `${rvDetails.year} `}
							{rvDetails.make && `${rvDetails.make} `}
							{rvDetails.model && rvDetails.model}
						</Text>
						{category && (
							<Text className="text-gray-700">
								<strong>Service Category:</strong> {category}
							</Text>
						)}
					</Section>
				)}

				{message && (
					<Section className="bg-blue-50 p-4 rounded-lg mb-4">
						<Text className="font-semibold text-gray-900 mb-2">
							Your Message:
						</Text>
						<Text className="text-gray-700">{message}</Text>
					</Section>
				)}

				<Text className="text-gray-700 mb-4">
					To access your service request workroom and manage your repair
					request, you'll need to set up your password first.
				</Text>

				<Button
					className="bg-[#437F6B] text-white py-3 px-6 text-base rounded-lg font-medium"
					href={passwordSetupLink}
				>
					Set Up Your Password
				</Button>

				<Hr className="my-6" />

				<Text className="text-gray-700 mb-2">
					<strong>
						From your service request workroom, you'll be able to:
					</strong>
				</Text>

				<ul className="text-gray-700 mb-4 space-y-1">
					<li>• Message providers directly about your repair needs</li>
					<li>• Invite additional providers to quote your job</li>
					<li>• View and compare detailed quotes</li>
					<li>• Track your repair progress in real-time</li>
					<li>• Schedule service appointments</li>
					<li>• Access your complete service history</li>
				</ul>

				<Text className="text-sm text-gray-500 mb-4">
					This link will expire in 24 hours. If you need a new password setup
					link, please contact support.
				</Text>

				<Text className="text-gray-700">
					Need help? Just reply to this email or contact us at{" "}
					<a href="mailto:<EMAIL>" className="text-[#437F6B]">
						<EMAIL>
					</a>
				</Text>

				<Hr className="my-6" />

				<Text className="text-gray-700">
					Thanks for choosing RV Help!
					<br />
					The RV Help Team
				</Text>
			</Section>
		</BaseEmail>
	);
}
