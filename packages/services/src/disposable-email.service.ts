export class DisposableEmailService {
    private static readonly DISPOSABLE_EMAIL_DOMAINS = [
        'passmail.net',
        'mailinator.com'
    ];

    private static readonly DISPOSABLE_EMAIL_PATTERN =
        /^[^@]+@(tempmail|temp-mail|10minutemail|guerrillamail|mailinator)\./i;

    static isDisposableEmail(email: string): boolean {
        return (
            this.DISPOSABLE_EMAIL_PATTERN.test(email) ||
            this.DISPOSABLE_EMAIL_DOMAINS.some((domain) => email.endsWith(domain))
        );
    }

    static validateEmail(email: string): { isValid: boolean; error?: string } {
        if (this.isDisposableEmail(email)) {
            return {
                isValid: false,
                error: 'Disposable email addresses are not allowed',
            };
        }
        return { isValid: true };
    }
}
