/**
 * Shared Invoice Service for RVHelp monorepo
 * @module InvoiceService
 */

import { Invoice, InvoiceItem, InvoiceStatus, PrismaClient } from "@rvhelp/database";
import { z } from "zod";

export interface InvoiceServiceInterface {
    createInvoice(data: CreateInvoiceInput): Promise<Invoice>;
    createPlatformInvoice(data: CreatePlatformInvoiceInput): Promise<Invoice>;
    getInvoiceById(id: string): Promise<Invoice | null>;
    getProviderInvoiceByWarrantyRequestId(warrantyRequestId: string): Promise<Invoice | null>;
    updateInvoiceStatus(id: string, status: InvoiceStatus): Promise<Invoice>;
    updateInvoice(id: string, data: any): Promise<Invoice>;
    updateInvoiceItem(itemId: string, data: {
        description?: string;
        quantity?: number;
        unit_price?: number;
    }): Promise<InvoiceItem>;
    generatePlatformInvoice(): Promise<Invoice>;
    getProviderInvoicesForPlatformInvoice(platformInvoiceId: string): Promise<any[]>;
}

export interface InvoiceServiceConfig {
    webAppUrl: string;
    portalAppUrl: string;
}



// Schemas
const createInvoiceSchema = z.object({
    provider_id: z.string().optional().describe("The listing ID of the service provider (optional for platform invoices)"),
    customer_name: z.string(),
    customer_email: z.string().email(),
    customer_phone: z.string().optional(),
    notes: z.string().optional(),
    due_date: z.date().optional().nullable(),
    items: z.array(
        z.object({
            description: z.string(),
            quantity: z.number().positive(),
            unit_price: z.number().int().positive()
        })
    ),
    status: z.nativeEnum(InvoiceStatus).optional()
});

const createPlatformInvoiceSchema = z.object({
    customer_name: z.string(),
    customer_email: z.string().email(),
    notes: z.string(),
    items: z.array(
        z.object({
            description: z.string(),
            quantity: z.number().positive(),
            unit_price: z.number().int().positive()
        })
    ),
    warranty_request_id: z.string(),
    status: z.nativeEnum(InvoiceStatus).optional()
});

export type CreateInvoiceInput = z.infer<typeof createInvoiceSchema>;
export type CreatePlatformInvoiceInput = z.infer<typeof createPlatformInvoiceSchema>;

export class InvoiceService implements InvoiceServiceInterface {
    constructor(
        private prisma: PrismaClient,
        private config?: InvoiceServiceConfig
    ) { }


    /**
     * Creates a new invoice in our database
     */
    async createInvoice(data: CreateInvoiceInput) {
        const validatedData = createInvoiceSchema.parse(data);

        // Verify that the listing exists if provider_id is provided
        if (validatedData.provider_id) {
            const listing = await this.prisma.listing.findUnique({
                where: { id: validatedData.provider_id }
            });

            if (!listing) {
                throw new Error("Listing not found");
            }
        }

        const totalAmount = validatedData.items.reduce(
            (sum, item) => sum + item.quantity * item.unit_price,
            0
        );

        return this.prisma.invoice.create({
            data: {
                provider_id: validatedData.provider_id,
                customer_name: validatedData.customer_name,
                customer_email: validatedData.customer_email,
                customer_phone: validatedData.customer_phone,
                due_date: validatedData.due_date,
                notes: validatedData.notes,
                amount: totalAmount,
                items: {
                    create: validatedData.items.map((item) => ({
                        description: item.description,
                        quantity: item.quantity,
                        unit_price: item.unit_price,
                        amount: item.quantity * item.unit_price
                    }))
                },
                status: validatedData.status || InvoiceStatus.DRAFT
            },
            include: {
                items: true,
                provider: {
                    include: {
                        owner: true
                    }
                }
            }
        });
    }

    /**
     * Create a RV Help platform invoice
     */
    async createPlatformInvoice(data: CreatePlatformInvoiceInput) {
        const validatedData = createPlatformInvoiceSchema.parse(data);

        const totalAmount = validatedData.items.reduce(
            (sum, item) => sum + item.quantity * item.unit_price,
            0
        );

        const invoice = await this.prisma.invoice.create({
            data: {
                provider_id: null, // Platform invoices don't have a provider
                customer_name: validatedData.customer_name,
                customer_email: validatedData.customer_email,
                notes: validatedData.notes,
                due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
                amount: totalAmount,
                items: {
                    create: validatedData.items.map((item) => ({
                        description: item.description,
                        quantity: item.quantity,
                        unit_price: item.unit_price,
                        amount: item.quantity * item.unit_price
                    }))
                },
                status: validatedData.status || InvoiceStatus.DRAFT
            },
            include: {
                items: true,
                provider: {
                    include: {
                        owner: true
                    }
                }
            }
        });

        // Attach the invoice_id to the warranty request
        await this.prisma.warrantyRequest.update({
            where: { id: validatedData.warranty_request_id },
            data: { platform_invoice_id: invoice.id }
        });

        return invoice;
    }

    /**
     * Gets a single invoice by ID
     */
    async getInvoiceById(id: string) {
        return this.prisma.invoice.findUnique({
            where: { id },
            include: {
                items: true,
                provider: {
                    include: {
                        owner: true
                    }
                }
            }
        });
    }

    /**
     * Gets an invoice by warranty request ID (from provider_invoice_id relationship)
     */
    async getProviderInvoiceByWarrantyRequestId(warrantyRequestId: string) {
        // Get the warranty request
        const warrantyRequest = await this.prisma.warrantyRequest.findUnique({
            where: { id: warrantyRequestId }
        });

        if (!warrantyRequest || !warrantyRequest.provider_invoice_id) {
            throw new Error("Warranty request not found or does not have a provider invoice");
        }

        // Get the provider invoice
        const providerInvoice = await this.prisma.invoice.findUnique({
            where: {
                id: warrantyRequest.provider_invoice_id!
            },
            include: {
                items: true,
                provider: {
                    include: {
                        owner: true
                    }
                }
            }
        });

        return providerInvoice;
    }

    /**
     * Updates the status of an invoice
     */
    async updateInvoiceStatus(id: string, status: InvoiceStatus) {
        return this.prisma.invoice.update({
            where: { id },
            data: { status },
            include: {
                items: true
            }
        });
    }

    /**
     * Deletes an invoice
     */
    async deleteInvoice(id: string) {
        return this.prisma.invoice.delete({
            where: { id }
        });
    }

    /**
     * Updates an invoice
     */
    async updateInvoice(id: string, data: any) {
        // Extract items from the data if present
        const { items, ...invoiceData } = data;

        // Prepare the update data
        const updateData: any = { ...invoiceData };

        // If items are provided, handle them properly with Prisma's nested update syntax
        if (items && Array.isArray(items)) {
            // First, delete all existing items
            await this.prisma.invoiceItem.deleteMany({
                where: { invoice_id: id }
            });

            // Then create new items
            updateData.items = {
                create: items.map((item) => ({
                    description: item.description,
                    quantity: item.quantity,
                    unit_price: item.unit_price,
                    amount: item.amount
                }))
            };
        }

        return this.prisma.invoice.update({
            where: { id },
            data: updateData,
            include: {
                items: true
            }
        });
    }

    /**
     * Updates an invoice item
     */
    async updateInvoiceItem(
        itemId: string,
        data: {
            description?: string;
            quantity?: number;
            unit_price?: number;
        }
    ) {
        const item = await this.prisma.invoiceItem.findUnique({
            where: { id: itemId }
        });

        if (!item) {
            throw new Error("Invoice item not found");
        }

        const updatedQuantity = data.quantity ?? item.quantity;
        const updatedUnitPrice = data.unit_price ?? item.unit_price;
        const updatedAmount = updatedQuantity * updatedUnitPrice;

        return this.prisma.invoiceItem.update({
            where: { id: itemId },
            data: {
                ...data,
                amount: updatedAmount
            }
        });
    }



    /**
     * Generates a platform fee invoice for all provider invoices from the last 2 calendar weeks
     */
    async generatePlatformInvoice(): Promise<Invoice> {
        try {
            // Calculate the date range for the last 2 calendar weeks
            const now = new Date();
            const endDate = new Date(now);
            endDate.setHours(23, 59, 59, 999); // End of today

            const startDate = new Date(now);
            startDate.setDate(now.getDate() - 14); // 14 days ago
            startDate.setHours(0, 0, 0, 0); // Start of that day

            console.log(`Generating platform invoice for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

            // Get all provider invoices from the last 2 weeks that are paid
            const providerInvoices = await this.prisma.invoice.findMany({
                where: {
                    status: InvoiceStatus.PAID,
                    created_at: {
                        gte: startDate,
                        lte: endDate
                    },
                    provider_id: {
                        not: null // Only provider invoices, not platform invoices
                    },
                    warranty_provider_request: {
                        isNot: null // Only warranty-related invoices
                    }
                },
                include: {
                    provider: true,
                    warranty_provider_request: {
                        select: {
                            rv_vin: true,
                            rv_year: true,
                            rv_make: true,
                            rv_model: true
                        }
                    }
                },
                orderBy: {
                    created_at: 'asc'
                }
            });

            if (providerInvoices.length === 0) {
                throw new Error("No paid provider invoices found for the last 2 weeks");
            }

            console.log(`Found ${providerInvoices.length} provider invoices for platform invoice generation`);

            // Calculate total platform fee (PLATFORM_FEE per invoice)
            const platformFeePerInvoice = 5000; // $50.00 in cents
            const totalAmount = providerInvoices.length * platformFeePerInvoice;

            // Create line items for each provider invoice
            const lineItems = providerInvoices.map((invoice) => ({
                description: `Platform fee for ${invoice.provider?.business_name || 'Provider'} - RV VIN: ${invoice.warranty_provider_request?.rv_vin || 'N/A'}`,
                quantity: 1,
                unit_price: platformFeePerInvoice,
                amount: platformFeePerInvoice
            }));

            // Create the platform invoice
            const platformInvoice = await this.prisma.invoice.create({
                data: {
                    provider_id: null, // Platform invoices don't have a provider
                    customer_name: "Keystone RV Company",
                    customer_email: "<EMAIL>",
                    notes: `Platform fee invoice for ${providerInvoices.length} warranty provider invoices from ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`,
                    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
                    amount: totalAmount,
                    status: InvoiceStatus.DRAFT,
                    items: {
                        create: lineItems
                    }
                },
                include: {
                    items: true
                }
            });

            console.log(`Created platform invoice ${platformInvoice.id} with ${lineItems.length} line items`);

            return platformInvoice;
        } catch (error) {
            console.error('Error generating platform invoice:', error);
            throw error;
        }
    }

    /**
     * Gets the provider invoices that were used to generate a specific platform invoice
     */
    async getProviderInvoicesForPlatformInvoice(platformInvoiceId: string) {
        try {
            // Get the platform invoice to find its creation date
            const platformInvoice = await this.prisma.invoice.findUnique({
                where: { id: platformInvoiceId }
            });

            if (!platformInvoice) {
                throw new Error("Platform invoice not found");
            }

            // Calculate the date range for the last 2 calendar weeks from the platform invoice creation date
            const endDate = new Date(platformInvoice.created_at);
            endDate.setHours(23, 59, 59, 999);

            const startDate = new Date(platformInvoice.created_at);
            startDate.setDate(endDate.getDate() - 14);
            startDate.setHours(0, 0, 0, 0);

            // Get all provider invoices from that period that are paid
            const providerInvoices = await this.prisma.invoice.findMany({
                where: {
                    status: "PAID",
                    created_at: {
                        gte: startDate,
                        lte: endDate
                    },
                    provider_id: {
                        not: null // Only provider invoices, not platform invoices
                    },
                    warranty_provider_request: {
                        isNot: null // Only warranty-related invoices
                    }
                },
                include: {
                    provider: true,
                    warranty_provider_request: {
                        select: {
                            rv_vin: true,
                            rv_year: true,
                            rv_make: true,
                            rv_model: true
                        }
                    }
                },
                orderBy: {
                    created_at: 'asc'
                }
            });

            // Transform to the expected format
            return providerInvoices.map((inv) => ({
                id: inv.id,
                invoice_number: inv.invoice_number,
                amount: inv.amount,
                provider_business_name: inv.provider?.business_name || `${inv.provider?.first_name} ${inv.provider?.last_name}` || 'Unknown Provider',
                rv_vin: inv.warranty_provider_request?.rv_vin || 'N/A',
                rv_year: inv.warranty_provider_request?.rv_year,
                rv_make: inv.warranty_provider_request?.rv_make,
                rv_model: inv.warranty_provider_request?.rv_model,
                created_at: inv.created_at
            }));
        } catch (error) {
            console.error('Error getting provider invoices for platform invoice:', error);
            throw error;
        }
    }

    /**
     * Attaches the platform fee invoice to the warranty request as an attachment and sets the platform_invoice_id
     */
    private async attachPlatformInvoiceToWarrantyRequest(platformInvoice: any, warrantyRequestId: string) {
        try {
            // Get the current warranty request to access existing attachments
            const warrantyRequest = await this.prisma.warrantyRequest.findUnique({
                where: { id: warrantyRequestId }
            });

            if (!warrantyRequest) {
                console.error(`Warranty request ${warrantyRequestId} not found for platform invoice attachment`);
                return;
            }

            // Create the attachment object for the platform invoice
            const platformInvoiceAttachment = {
                id: `platform-invoice-${platformInvoice.id}`,
                type: "document" as const,
                title: "RV Help Invoice",
                url: `${this.config?.webAppUrl}/api/invoices/${platformInvoice.id}/pdf`,
                required: false,
                completed: true
            };

            // Get existing attachments and add the new platform invoice
            const existingAttachments = (warrantyRequest.attachments as any[]) || [];
            const updatedAttachments = [...existingAttachments, platformInvoiceAttachment];

            // Update the warranty request with the new attachment AND set the platform_invoice_id
            await this.prisma.warrantyRequest.update({
                where: { id: warrantyRequestId },
                data: {
                    attachments: updatedAttachments as any,
                    platform_invoice_id: platformInvoice.id
                }
            });

            console.log(`Attached platform invoice ${platformInvoice.id} to warranty request ${warrantyRequestId} and set platform_invoice_id`);
        } catch (error) {
            console.error(`Error attaching platform invoice to warranty request:`, error);
            // Don't throw here - we don't want to fail invoice generation if attachment fails
        }
    }
}
