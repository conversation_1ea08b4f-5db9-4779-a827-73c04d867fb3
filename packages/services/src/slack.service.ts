/**
 * Shared Slack Service for RVHelp monorepo
 */

import { PrismaClient } from "@rvhelp/database";

interface SlackMessage {
    text?: string;
    blocks?: any[];
}

export interface SlackWebhooks {
    providerGrowth?: string;
    dailyStats?: string;
    josiahMann?: string;
    providerLeads?: string;
    memberSignups?: string;
    oemJobs?: string;
}

export class SharedSlackService {
    private webhooks: SlackWebhooks;
    private prisma: PrismaClient;

    constructor(prisma: PrismaClient, webhooks: SlackWebhooks) {
        this.prisma = prisma;
        this.webhooks = webhooks;
    }

    private async sendToWebhook(message: SlackMessage, webhookUrl: string): Promise<void> {
        if (!webhookUrl) return;

        try {
            const response = await fetch(webhookUrl, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(message),
            });

            if (!response.ok) {
                console.error(`Slack webhook failed: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            console.error("Failed to send Slack message:", error);
        }
    }

    async notifyProviderGrowth(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.providerGrowth || '');
    }

    async notifyDailyStats(membershipService: any): Promise<void> {
        const now = new Date();
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);

        const [
            totalUsers,
            newUsers,
            totalLeads,
            newLeads,
            hourlyRateDiscountCount,
            dispatchFeeDiscountCount,
            virtualDiagnosisCount,
            membershipStats
        ] = await Promise.all([
            this.prisma.user.count({
                where: { role: "USER" }
            }),
            this.prisma.user.count({
                where: {
                    role: "USER",
                    created_at: {
                        gte: yesterday
                    }
                }
            }),
            this.prisma.job.count(),
            this.prisma.job.count({
                where: {
                    created_at: {
                        gte: yesterday
                    }
                }
            }),
            this.prisma.listing.count({
                where: { discount_hourly_rate: true }
            }),
            this.prisma.listing.count({
                where: { discount_dispatch_fee: true }
            }),
            this.prisma.listing.count({
                where: { settings_virtual_diagnosis: true }
            }),
            membershipService.getMembershipStats(yesterday, now)
        ]);

        const message = {
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "📊 Daily RV Help Stats",
                        emoji: true
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: "*🆕 New Users (24h)*\n" + newUsers.toLocaleString()
                        },
                        {
                            type: "mrkdwn",
                            text: "*👥 Total RV Owners*\n" + totalUsers.toLocaleString()
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: "*🎉 New Paid Members (24h)*\n" + membershipStats.newMemberships.toLocaleString()
                        },
                        {
                            type: "mrkdwn",
                            text: "*💳 Total Paid Members*\n" + membershipStats.totalActiveMembers.toLocaleString()
                        }
                    ]
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: "*🎯 New Leads (24h)*\n" + newLeads.toLocaleString()
                        },
                        {
                            type: "mrkdwn",
                            text: "*📝 Total Service Requests*\n" + totalLeads.toLocaleString()
                        }
                    ]
                },
                {
                    type: "divider"
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: "*🏷️ Listing Discount Features*\n" +
                            `• Hourly Rate Discount: ${hourlyRateDiscountCount} listings\n` +
                            `• Dispatch Fee Discount: ${dispatchFeeDiscountCount} listings\n` +
                            `• Virtual Diagnosis: ${virtualDiagnosisCount} listings`
                    }
                },
                {
                    type: "divider"
                },
                {
                    type: "context",
                    elements: [
                        {
                            type: "mrkdwn",
                            text: `Stats for ${yesterday.toLocaleDateString()} - ${now.toLocaleDateString()}`
                        }
                    ]
                }
            ]
        };


        await this.sendToWebhook(message, this.webhooks.dailyStats || '');
    }

    async notifyProviderLeads(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.providerLeads || '');
    }

    async notifyMemberSignups(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.memberSignups || '');
    }

    async notifyOemJobs(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.oemJobs || '');
    }

    async notifyJosiahMann(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.josiahMann || '');
    }

    // Backward compatibility methods that match original API signatures
    async sendToJosiahMann(message: SlackMessage): Promise<void> {
        await this.sendToWebhook(message, this.webhooks.josiahMann || '');
    }

    async sendToProviderLeads(message: SlackMessage): Promise<void> {
        await this.sendToWebhook(message, this.webhooks.providerLeads || '');
    }

    async notifyNewMemberSignup(user: any, level: any, memberNumber: number): Promise<void> {
        const message: SlackMessage = {
            text: `🎉 New ${level} Member Signup`,
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: `🎉 New ${level} Member Signup`
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Member:* ${user.first_name} ${user.last_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Email:* ${user.email}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Member #:* ${memberNumber}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Level:* ${level}`
                        }
                    ]
                }
            ]
        };
        await this.sendToWebhook(message, this.webhooks.memberSignups || '');
    }

    async notifyProviderVerified(user: any, businessName: string, slug: string, memberNumber: number): Promise<void> {
        const message: SlackMessage = {
            text: `✅ Provider Verified`,
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "✅ Provider Verified"
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Business:* ${businessName}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Owner:* ${user.first_name} ${user.last_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Email:* ${user.email}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Member #:* ${memberNumber}`
                        }
                    ]
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: `*Profile:* https://rvhelp.com/providers/${slug}`
                    }
                }
            ]
        };
        await this.sendToWebhook(message, this.webhooks.providerGrowth || '');
    }

    // Advanced notification methods can be added here as needed
    async sendCustomNotification(
        message: SlackMessage,
        webhookType: keyof SlackWebhooks
    ): Promise<void> {
        const webhookUrl = this.webhooks[webhookType];
        if (webhookUrl) {
            await this.sendToWebhook(message, webhookUrl);
        }
    }

    async notifyNewWarrantyRequest(warrantyRequest: any, companyName: string): Promise<void> {
        if (process.env.NODE_ENV === "development") return;
        const message: SlackMessage = {
            text: `🔧 New Warranty Request`,
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: `🔧 New Warranty Request`
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Company:* ${companyName}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Customer:* ${warrantyRequest.first_name} ${warrantyRequest.last_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Email:* ${warrantyRequest.email}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*RV:* ${warrantyRequest.rv_year || 'N/A'} ${warrantyRequest.rv_make || 'N/A'} ${warrantyRequest.rv_model || 'N/A'}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*VIN:* ${warrantyRequest.rv_vin || 'N/A'}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Component:* ${warrantyRequest.component?.name || 'N/A'}`
                        }
                    ]
                }
            ]
        };

        await this.sendToWebhook(message, this.webhooks.oemJobs || '');
    }

    async notifyWarrantyAuthorizationRequest(warrantyRequest: any, companyName: string): Promise<void> {
        const message: SlackMessage = {
            text: `🔍 Warranty Authorization Request`,
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: `🔍 Warranty Authorization Request`
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Company:* ${companyName}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Customer:* ${warrantyRequest.first_name} ${warrantyRequest.last_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Email:* ${warrantyRequest.email}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*RV:* ${warrantyRequest.rv_year || 'N/A'} ${warrantyRequest.rv_make || 'N/A'} ${warrantyRequest.rv_model || 'N/A'}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Component:* ${warrantyRequest.component?.name || 'N/A'}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Status:* ${warrantyRequest.status || 'N/A'}`
                        }
                    ]
                }
            ]
        };

        await this.sendToWebhook(message, this.webhooks.oemJobs || '');
    }
}

// Mock service for testing
export const mockSlackService = {
    notifyProviderGrowth: async () => { },
    notifyDailyStats: async () => { },
    notifyProviderLeads: async () => { },
    notifyMemberSignups: async () => { },
    notifyOemJobs: async () => { },
    notifyJosiahMann: async () => { },
    sendToJosiahMann: async () => { },
    sendToProviderLeads: async () => { },
    notifyNewMemberSignup: async () => { },
    notifyProviderVerified: async () => { },
    sendCustomNotification: async () => { },
    notifyNewWarrantyRequest: async () => { },
    notifyWarrantyAuthorizationRequest: async () => { },
};

export function createSlackService(
    prisma: PrismaClient,
    webhooks: SlackWebhooks,
    useMock: boolean = false
): SharedSlackService | typeof mockSlackService {
    if (useMock || process.env.NODE_ENV === "test") {
        return mockSlackService;
    }
    return new SharedSlackService(prisma, webhooks);
}
