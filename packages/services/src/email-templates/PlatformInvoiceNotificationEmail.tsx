import { Button, Heading, Section, Text } from "@react-email/components";
import { BaseEmail } from "./BaseEmail";
import { emailStyles } from "./shared-styles";

interface PlatformInvoiceNotificationEmailProps {
    platformInvoice: {
        id: string;
        invoice_number: number;
        amount: number;
        currency: string;
    };
    originalInvoice: {
        invoice_number: number;
        amount: number;
        customer_name: string;
        customer_email: string;
    };
    provider: {
        business_name: string;
        first_name: string;
        last_name: string;
        email: string;
    };
    platformInvoiceUrl: string;
}

export function PlatformInvoiceNotificationEmail({
    platformInvoice,
    originalInvoice,
    provider,
    platformInvoiceUrl
}: PlatformInvoiceNotificationEmailProps) {
    const previewText = `RV Help - Warranty Portal Invoice #${platformInvoice.invoice_number}`;
    const providerName =
        provider.business_name || `${provider.first_name} ${provider.last_name}`;

    return (
        <BaseEmail previewText={previewText}>
            <Section style={emailStyles.container}>
                <Heading style={emailStyles.heading}>
                    RV Help - Warranty Portal Invoice
                </Heading>

                <Text style={emailStyles.text}>
                    A new warranty service request has been paid by RV Help and an invoice
                    has been generated.
                </Text>


                <Section style={emailStyles.section}>
                    <Text style={emailStyles.subheading}>Invoice Details</Text>
                    <Text style={emailStyles.text}>
                        <strong>Customer:</strong> {originalInvoice.customer_name}
                        <br />
                        <strong>Provider:</strong> {providerName}
                        <br />
                        <strong>Invoice #:</strong> {platformInvoice.invoice_number}
                    </Text>
                </Section>

                <Section style={emailStyles.centered}>
                    <Button href={platformInvoiceUrl} style={emailStyles.button}>
                        View Invoice
                    </Button>
                </Section>

                <Text style={emailStyles.smallText}>
                    This is an automated notification from the RV Help platform. The platform invoice has been created and is ready for review.
                </Text>
            </Section>
        </BaseEmail>
    );
}
