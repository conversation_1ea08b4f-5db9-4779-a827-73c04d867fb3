import {
    Body,
    Container,
    Font,
    Head,
    Html,
    Img,
    Preview,
    Section,
    Tailwind
} from "@react-email/components";
import * as React from "react";

interface EmailTemplateProps {
    previewText: string;
    children: React.ReactNode;
}

export const BaseEmail: React.FC<Readonly<EmailTemplateProps>> = ({
    children,
    previewText
}) => (
    <Html>
        <Head>
            <Font
                fontFamily="Inter"
                fallbackFontFamily="Verdana"
                webFont={{
                    url: "https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap",
                    format: "woff2"
                }}
                fontWeight={400}
                fontStyle="normal"
            />
        </Head>
        <Preview>{previewText}</Preview>
        <Tailwind>
            <Body>
                <Container className="bg-white rounded-lg shadow-lg p-8">
                    <Section
                        style={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            width: "100%",
                            marginBottom: "1px"
                        }}
                    >
                        <Img
                            src={`https://rvhelp-prod.s3.us-east-1.amazonaws.com/public/rvhelp-logo.png`}
                            height="45"
                            alt="RV Help Logo"
                            width="auto"
                        />
                    </Section>

                    <div
                        className="break-words max-w-full overflow-wrap-anywhere"
                        style={{ wordBreak: "break-word", overflowWrap: "anywhere" }}
                    >
                        {children}
                    </div>
                </Container>
            </Body>
        </Tailwind>
    </Html>
);
