{"name": "@rvhelp/services", "version": "1.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@rvhelp/database": "workspace:*", "@react-email/components": "^0.0.25", "@react-email/render": "^1.0.1", "react-email": "^3.0.1", "nodemailer": "^6.9.0", "resend": "^3.2.0", "zod": "^3.22.4"}, "devDependencies": {"@rvhelp/eslint-config": "1.0.0", "@rvhelp/typescript-config": "1.0.0", "@types/node": "^22.7.7", "@types/nodemailer": "^6.4.0", "@types/react": "^18.2.0", "react": "^18", "typescript": "^5.6.3"}, "peerDependencies": {"@prisma/client": "6.13.0"}}