{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "jsx": "react-jsx", "types": ["react"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}